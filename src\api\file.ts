import useFileStore from '@/store/modules/file';
import axios from 'axios';
import qs from 'query-string';
import i18n from '@/locale/index';

const { t } = i18n.global;

export function fileDownload(file: any) {
  const fileStore = useFileStore();
  const date = Date.now();

  return axios.get('/sys-storage/download', {
    params: {
      f8s: file.fileToken,
    },
    responseType: 'blob',
    timeout: 0,
    onDownloadProgress: (progressEvent) => {
      const value = (progressEvent.loaded / progressEvent.total) * 100;
      fileStore.setDownloadProgress({
        id: date,
        name: file.name,
        percent: value,
      });
    },
  });
}

export function fileZipDownload(filelist: any) {
  const fileStore = useFileStore();
  const date = Date.now();

  return axios.post('/sys-storage/zip', filelist, {
    responseType: 'blob',
    timeout: 0,
    onDownloadProgress: (progressEvent) => {
      const value = (progressEvent.loaded / progressEvent.total) * 100;
      fileStore.setDownloadProgress({
        id: date,
        name: `${t('zip')}.zip`,
        percent: value,
      });
    },
  });
}

// 获取批量下载文件信息
export function batchDownloadInfo(data: object) {
  return axios.post('/cde-collaboration/file/getBatchDownloadInfo', data);
}

/* 大文件上传begin */
// 获取当前文件的上传情况(正常上传、极速秒传、断点续传)
export function queryUploadInfo(data: object) {
  return axios.post('/sys-storage/secondPass', qs.stringify(data));
}

// 获取当前上传文件唯一标识
export function queryUploadId(data: object) {
  return axios.post('/sys-storage/uploadId', data);
}

// 获取当前文件已上传的分片列表
export function queryFileBlockList(params: object) {
  return axios.get('/sys-storage/parts', { params });
}

// 取消上传，删除分片的碎片
export function deleteFileInfo(data: object) {
  return axios.delete('/sys-storage/abort/burst', { data });
}

// 分片上传完成，合并文件
export function mergeFile(data: object) {
  return axios.post('/sys-storage/bigFile/together', data);
}

// 查询文件组信息(groupToken存在则返回一组信息，fileToken存在则返回精确文件信息)
export function queryGroupFileInfo(data: object) {
  return axios.post('/sys-storage/file', { data });
}

// 根据文件token和文件组grouptoken删除文件，token删除单文件，grouptoken删除文件组
export function deleteFile(data: object) {
  return axios.delete('/sys-storage/file', {
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}
/* 大文件上传end */

// 设置文件的元属性
export const setFileMeta = (data: any) => {
  return axios.post('/cde-collaboration/file/setFileMeta', data);
};
