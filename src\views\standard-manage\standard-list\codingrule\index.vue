<template>
  <!-- 搜索区 -->
  <div class="metas">
    <table-title
      class="table-title"
      :title="$t('standard-setting.rule-list')"
    ></table-title>
    <a-row>
      <a-col :span="20">
        <a-form
          :model="formModel"
          label-align="left"
          layout="inline"
          auto-label-width
          class="search-area"
        >
          <a-form-item
            field="name"
            :label="$t('standard-setting.rule-name')"
            label-col-flex="auto"
            content-class="item"
          >
            <a-input
              v-model="formModel.name"
              :placeholder="$t('standard-setting.please-enter')"
              allow-clear
              :max-length="currentLocale === 'en-US' ? 200 : 50"
              @press-enter="search"
              @search="search"
              @clear="search"
            />
          </a-form-item> </a-form
      ></a-col>
      <a-col :span="4" style="text-align: right">
        <a-space :size="8">
          <a-button type="outline" @click="search">
            <template #icon> <icon-search /> </template>
            {{ $t('list.options.btn.search') }}</a-button
          >
          <a-button type="outline" @click="reset"
            ><template #icon><icon-loop /> </template
            >{{ $t('list.options.btn.reset') }}</a-button
          >
        </a-space>
      </a-col>
    </a-row>

    <a-divider margin="10px 0px 24px" />
    <a-card class="general-card">
      <a-row style="margin-bottom: 16px">
        <a-space :size="10">
          <a-button type="primary" @click="addrule"
            ><template #icon><icon-plus /></template>
            {{ $t('list.options.btn.new') }}</a-button
          >
          <a-popconfirm
            :content="$t('standard-setting.whether-delete-checked-rule')"
            type="warning"
            position="left"
            @ok="multipleDelete"
          >
            <a-button
              type="outline"
              status="danger"
              :disabled="!selectedKeys.length"
              ><template #icon> <icon-delete /> </template
              >{{ $t('standard-setting.delete') }}</a-button
            >
          </a-popconfirm>
        </a-space>
      </a-row>
      <a-table
        v-model:selected-keys="selectedKeys"
        v-table-height
        stripe
        row-key="id"
        :loading="loading"
        :scroll="scroll"
        :scrollbar="true"
        :pagination="pagination"
        :columns="columns"
        :data="renderData"
        :bordered="false"
        :row-selection="rowSelection"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #name="{ record }">
          <span class="blue-link" @click="detail(record)">{{
            record.name
          }}</span>
        </template>
        <template #create_by="{ record }">
          <a-avatar
            :size="30"
            :style="{
              cursor: 'pointer',
              backgroundColor: record.color,
              textAlign: 'center',
            }"
          >
            <span style="font-size: 20px">{{
              record.createBy.substring(0, 1)
            }}</span>
          </a-avatar>
          {{ record.createBy }}
        </template>

        <template #operations="{ record }">
          <a-button type="text" size="small" @click="detail(record)">{{
            $t('table.opt.details')
          }}</a-button>
          <a-button type="text" size="small" @click="update(record)">
            {{ $t('table.opt.edit') }}
          </a-button>
          <a-popconfirm
            :content="$t('standard-setting.whether-delete-rule')"
            position="left"
            @ok="remove(record.id)"
          >
            <a-button type="text" size="small" status="danger">
              {{ $t('table.opt.delete') }}
            </a-button>
          </a-popconfirm>
        </template>
      </a-table>
    </a-card>
    <AddRule
      v-model:visible="dialogVisible"
      :form="selectForm"
      :title="dialogTitle"
      :action-type="actionType"
      :disabled="disabled"
      @refresh="search"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue';
  import AddRule from './addrule.vue';
  import useLoading from '@/hooks/loading';
  import {
    getRuleList,
    RuleListParams,
    Pagination,
    AddRuleFormInter,
    RuleList,
    removeRule,
    multipleDeleteRule,
  } from './api';
  import {
    TableColumnData,
    TableRowSelection,
  } from '@arco-design/web-vue/es/table/interface';
  import TableTitle from '@/components/table-title/index.vue';
  import { Message, Notification } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import { useUserStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';

  const { currentLocale } = useLocale();

  const { t } = useI18n();
  const scroll = {
    y: 'calc(100vh - 360px)',
  };

  const selectedKeys = ref([]);

  const rowSelection = reactive<TableRowSelection>({
    type: 'checkbox',
    showCheckedAll: true,
  });

  const dialogVisible = ref(false);
  const dialogTitle = ref('');

  type Column = TableColumnData & { checked?: true };

  // 列表表格展示
  const generateFormModel = () => {
    const initData: AddRuleFormInter = {
      id: '',
      name: '',
      regularExpression: '',
      description: '',
    };
    return initData;
  };

  const { loading, setLoading } = useLoading(true);
  const renderData = ref<RuleList[]>([]);
  const formModel = ref(generateFormModel());
  const columns = computed<Column[]>(() => {
    return [
      {
        title: t('standard-setting.index'),
        dataIndex: 'index',
        slotName: 'index',
        align: 'left',
        width: 80,
      },
      {
        title: t('standard-setting.name'),
        dataIndex: 'name',
        slotName: 'name',
        align: 'left',
        width: 200,
      },
      {
        title: t('standard-setting.regular-expression'),
        dataIndex: 'regularExpression',
        slotName: 'regularExpression',
        align: 'left',
      },
      {
        title: t('standard-setting.instructions'),
        dataIndex: 'description',
        slotName: 'description',
        align: 'left',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: t('standard-setting.creation-time'),
        dataIndex: 'createDate',
        slotName: 'createDate',
        align: 'left',
      },
      // {
      //   title: '创建人',
      //   dataIndex: 'create_by',
      //   slotName: 'create_by',
      //   align: 'left',
      //   width: 160,
      // },
      {
        title: t('standard-setting.operation'),
        dataIndex: 'operations',
        slotName: 'operations',
        align: 'left',
        width: 200,
      },
    ];
  });

  const basePagination: Pagination = {
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
  };
  const pagination = reactive({
    ...basePagination,
  });

  const userStore = useUserStore();
  const userId = getUserId() || '';
  const projectId = getLocalstorage(`last_project_${userId}`) || '';

  // 列表数据相关
  const fetchData = async (
    params: RuleListParams = { pageNo: 1, pageSize: 20, name: '' }
  ) => {
    setLoading(true);
    try {
      const { current, ...rest } = params;
      const apiParams = {
        ...rest,
        pageNo: current || 1,
        source: 'CDE',
        groupId: userStore.admin === 0 ? '0' : projectId,
      };
      const { data } = await getRuleList(apiParams);
      renderData.value = data.list || [];
      pagination.current = apiParams.pageNo;
      pagination.pageSize = apiParams.pageSize;
      pagination.total = data.total;
    } catch (err) {
      // you can report use errorHandler or other
    } finally {
      setLoading(false);
    }
  };
  const search = () => {
    // search时重新回到第一页
    const { pageSize } = pagination;
    fetchData({
      pageSize,
      current: 1,
      ...formModel.value,
    });
  };
  const reset = () => {
    formModel.value.name = '';
    search();
  };

  const onPageChange = (current: number) => {
    const { pageSize } = pagination;
    fetchData({ pageSize, current, ...formModel.value });
  };
  const onPageSizeChange = (pageSize: number) => {
    fetchData({ current: 1, pageSize, ...formModel.value });
  };
  fetchData();

  // 功能按钮相关
  const disabled = ref(false);
  const selectForm = ref<AddRuleFormInter>({
    id: '',
    name: '',
    regularExpression: '',
    description: '',
  });

  const actionType = ref('add');

  const addrule = () => {
    dialogTitle.value = t('standard-setting.new');
    disabled.value = false;
    actionType.value = 'add';
    dialogVisible.value = true;
  };

  const update = (record: RuleList) => {
    actionType.value = 'edit';
    dialogTitle.value = t('standard-setting.edit');
    disabled.value = false;
    dialogVisible.value = true;
    selectForm.value = { ...record };
  };
  const detail = (record: RuleList) => {
    dialogTitle.value = t('standard-setting.view');
    disabled.value = true;
    dialogVisible.value = true;
    selectForm.value = { ...record };
  };

  const multipleDelete = async () => {
    const params = selectedKeys.value;
    if (params === undefined) {
      Message.warning(t('standard-setting.unselected-list'));
    } else {
      const saveRes = await multipleDeleteRule(params);
      if (saveRes) {
        Message.success('删除成功');
        selectedKeys.value = [];
        search();
      } else {
        Message.error('删除失败');
      }
    }
  };

  const remove = async (id: string) => {
    const res = await removeRule(id);
    if (res.status) {
      const { pageSize, current } = pagination;
      fetchData({ pageSize, current, ...formModel.value });
      Message.success('删除成功');
    }
  };
</script>

<style scoped lang="less">
  :deep(.arco-link:hover) {
    background-color: transparent;
  }
  .blue-link {
    color: rgb(var(--primary-6));
    cursor: pointer;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  :deep.arco-btn-size-small {
    padding: 0 15px 0 0;
  }
  .general-card {
    padding: 0;
  }
  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1d2129;
    img {
      position: relative;
      top: 3px;
      height: 20px;
    }
  }
  .metas {
    padding: 0;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
      img {
        position: relative;
        top: 3px;
        height: 20px;
      }
    }
    .search-title {
      height: 22px;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #1d2129;
      line-height: 22px;
    }
  }
  :deep(.arco-card-header) {
    height: auto;
    padding: 0;
    border: none;
    margin-top: 20px;
  }
  :deep(.arco-card-body) {
    padding: 0;
  }
  :deep(.arco-card-bordered) {
    border: none;
  }
  :deep(.arco-table-tr) {
    cursor: pointer;
  }
  .table-title {
    margin-bottom: 16px;
  }
</style>
