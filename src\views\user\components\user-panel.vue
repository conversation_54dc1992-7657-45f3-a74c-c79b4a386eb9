<template>
  <div class="user-card">
    <a-avatar :size="64" class="ava-box">{{ nickname }}</a-avatar>
    <p class="userfullname">{{ userFullname }}</p>
    <a-space :size="34" class="info">
      <span class="info-item">
        <icon-tag />
        <span class="info-text">{{ isAdmin }}</span>
      </span>
      <span class="info-item">
        <icon-phone />
        <span class="info-text">{{ phone }}</span>
      </span>
      <span class="info-item">
        <icon-email />
        <span class="info-text">{{ email }}</span>
      </span>
      <span class="info-item org_view">
        <img
          class="org_img"
          src="@/assets/images/user-setting/Group <EMAIL>"
          alt=""
        />
        <span class="info-text" v-if="orgName"> {{ orgName }}</span>
      </span>
    </a-space>
    <!-- 产权按钮子 -->
    <a-button type="primary" class="btnWithEquity" @click="goRegistration">{{
      $t('userSetting.email.equity')
    }}</a-button>
    <!-- 产权按钮子 -->
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { useUserStore } from '@/store';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const userStore = useUserStore();

  const userFullname = computed(() => {
    return userStore.name || '';
  });

  const nickname = computed(() => {
    return userFullname.value.substring(0, 1);
  });

  const phone = computed(() => {
    return userStore.phone || '';
  });

  const email = computed(() => {
    return userStore.email || '';
  });

  const isAdmin = computed(() => {
    if (userStore.admin === 1) {
      return t('super-administrator');
    }
    if (userStore.admin === 0) {
      return t('administrator');
    }
    return t('ordinary-members');
  });

  // 新增二级单位
  const orgName = userStore.pathName?.split('.')[3];

  // 跳转产品注册
  const goRegistration = () => {
    const baseUrl = window.location.origin;
    const fullUrl = `${baseUrl}/apply-application`;
    window.open(fullUrl);
  };
</script>

<style scoped lang="less">
  .user-card {
    margin-top: 24px;
    background: url('@/assets/images/profile-bg.png');
    display: flex;
    align-items: center;
    flex-direction: column;
    position: relative;
  }
  .ava-box {
    margin-top: 34px;
  }
  .userfullname {
    margin: 8px 0 0;
    font-size: 16px;
    line-height: 24px;
    color: var(--color-text-1);
  }
  .info {
    margin: 8px 0 36px;
    &-text {
      margin-left: 4px;
      font-size: 14px;
      color: var(--color-text-1);
      line-height: 22px;
    }
  }
  .btnWithEquity {
    position: absolute;
    right: 24px;
    bottom: 18px;
  }

  .org_view {
    display: flex;
    align-items: center;

    .org_img {
      width: 14px;
    }
  }
</style>
