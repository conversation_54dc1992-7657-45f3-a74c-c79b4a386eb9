export interface conferenceParams {
  agendaList: any[];
  code: string;
  createBy: string;
  createDate: string;
  deleteFlag: number;
  endTime: string;
  fileList: string;
  id: string;
  illustrate: string;
  link: string;
  mettingDate: string;
  mettingThemeEntryList: any[];
  minutes: string;
  name: string;
  parentId: string;
  participants: string;
  personList: string;
  position: string;
  projectId: string;
  startTime: string;
  status: string;
  updateBy: string;
  updateDate: string;
}

export interface DesignState {
  /** 全部会议数据信息 */
  conferenceData: conferenceParams[];
  /** 条目数据信息 */
  itemList: any[];
  /** 区分分页查询与按钮搜索 */
  searchFlag: boolean;
  /** 创建会议弹窗 */
  createConferenceVisible: boolean;
  /** 当前会议信息 */
  curConferenceData: conferenceParams | null;
  /** 会议卡片议程编辑标志 */
  cardEditFlag: boolean;
  /** 会议列表议程编辑标志 */
  listEditFlag: boolean;
  /** 会议编辑标志 */
  conferenceEditFlag: boolean;
  /** 当前点击的文件id */
  curClickFileId: string;
  /** 判断滚动条是否滑动 */
  scrollFlag: boolean;
}
