namespace Permission {
  namespace Api {
    // 列表展示接口
    interface PageRequestDto {
      id: string;
      module: string;
      name: string;
      description?: string;
      url: string;
      httpMethod: string;
    }

    // 项目按钮回参Dto
    interface ProjectPageBtnDto {
      buttonCodes: string | null;
      projectRoleCode: string;
      sysButtonCodes: string[] | null;
      teamButtons: TeamBtnDto[];
    }
    interface TeamBtnDto {
      teamId: string;
      teamRoleCode: string;
      buttonCodes: string[];
    }

    // 列表展示按钮
    interface PageBtnDto {
      code: string;
      menuId: string;
      name: string;
      id: string;
    }

    // 列表展示菜单
    interface PageMenuDto {
      code: string;
      path: string;
      id: string;
      parentId: string;
      title: string;
      type: number;
      createBy: string;
      deleteFlag: number;
      hasSubMenus: boolean;
      hasButtons: boolean;
      icon: string;
    }

    // 接口实体
    interface RequestDto {
      id?: string;
      module: string;
      name: string;
      description?: string;
      url: string;
      httpMethod: string;
    }
    // 按钮实体
    interface BtnDto {
      code: string;
      menuId: string;
      name: string;
      id?: string;
    }
    // 菜单实体
    interface MenuDto {
      code: string;
      path: string;
      id?: string;
      parentId: string;
      parentName: string;
      title: string;
      icon: string;
    }
  }

  namespace Model {
    interface MenuTree {
      title: string;
      key: string;
      children?: MenuTree[];
      code: string;
      isLeaf: boolean;
      hasButtons: boolean;
      parentId: string;
    }
  }
}
