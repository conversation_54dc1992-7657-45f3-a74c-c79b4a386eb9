<template>
  <a-modal
    :visible="props.visible"
    :title="props.title"
    :width="500"
    :unmount-on-close="true"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-space style="justify-content: center" fill>
      <a-form
        ref="btnRef"
        style="padding-top: 20px"
        :model="formData"
        auto-label-width
        :disabled="isView"
      >
        <a-form-item
          field="name"
          label="按钮名称"
          validate-trigger="input"
          :rules="[
            {
              required: true,
              message: '按钮名称未填写',
            },
          ]"
        >
          <a-input
            v-model="formData.name"
            :placeholder="$t('user-center.please-enter-name')"
          />
        </a-form-item>
        <a-form-item
          field="code"
          label="按钮编码"
          validate-trigger="input"
          :rules="[
            {
              required: true,
              message: '按钮编码未填写',
            },
          ]"
        >
          <a-input
            v-model="formData.code"
            :placeholder="$t('user-center.please-enter-name')"
          />
        </a-form-item>
      </a-form>
    </a-space>
  </a-modal>
</template>

<script lang="ts" setup name="addRole">
  import { ref, computed, toRefs } from 'vue';
  import { FormInstance, Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import { addBtn, getBtnInfo, updateBtnInfo } from '../api';

  const { t } = useI18n();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    selectId: {
      type: String,
      default: '',
    },
    menuId: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
  });

  const formData = ref<Permission.Api.BtnDto>({} as Permission.Api.BtnDto);
  const { selectId, type } = toRefs(props);

  const isView = computed(() => type.value === 'view');

  const emits = defineEmits(['update:visible', 'refresh']);
  // 绑定form的ref
  const btnRef = ref<FormInstance>();

  const handleBeforeOk = async () => {
    const validateRes = await btnRef.value?.validate();
    if (!validateRes) {
      const data = { ...formData.value };

      if (props.type === 'add') {
        await addBtn(data);
        Message.success('按钮新增成功！');
      } else if (props.type === 'edit') {
        await updateBtnInfo(data);
        Message.success('按钮修改成功！');
      }
      emits('refresh');
      emits('update:visible', false);
    }
  };

  const { menuId } = toRefs(props);
  function initialFormData() {
    if (type.value === 'add') {
      formData.value = {
        menuId: menuId.value,
        name: '',
        code: '',
      };
    } else if (type.value === 'edit') {
      getBtnInfo(selectId.value).then((res) => {
        const { data } = res;
        formData.value = data;
      });
    }
  }
  initialFormData();

  const handleCancel = () => {
    emits('update:visible', false);
  };
</script>
<style lang="less" scoped>
  .detailTitle {
    display: flex;
    padding-bottom: 16px;
    font-size: 16px;
    align-items: center;
    color: #1d2129;
    .titleContent {
      margin-left: 6px;
    }
  }
  .tab-pane {
    padding: 0 10px;
  }
  :deep(.arco-tabs-content) {
    padding-top: 20px;
  }
</style>
