<template>
  <div class="project-list">
    <div class="top-box">
      <list-title
        :title-text="$t('dashboard.project-template')"
        :show-button="false"
        class="title"
      />
      <div class="search-bar">
        <a-row :gutter="16">
          <a-col :flex="1">
            <a-form
              ref="formModel"
              :model="searchParams"
              :rules="formRules"
              label-align="left"
            >
              <a-row :gutter="36">
                <a-col flex="215px">
                  <a-form-item
                    field="projectName"
                    :label="$t('dashboard.name')"
                    label-col-flex="35px"
                  >
                    <a-input
                      v-model="searchParams.projectName"
                      :placeholder="$t('dashboard.please-enter')"
                      style="width: 215px"
                      allow-clear
                      @keydown.enter="getList"
                      @clear="getList"
                    ></a-input>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-col>
          <a-col :flex="'128px'" style="text-align: right">
            <a-space :size="8">
              <a-button type="primary" @click="getList">{{
                $t('list.options.btn.search')
              }}</a-button>
              <a-button type="outline" @click="reset">{{
                $t('list.options.btn.clear')
              }}</a-button>
            </a-space>
          </a-col>
        </a-row>
      </div>
    </div>

    <a-row :gutter="16" style="margin-bottom: 16px" justify="start">
      <a-col>
        <a-button
          type="primary"
          class="opt-btn"
          @click="createDialogVisible = true"
          >{{ $t('dashboard.create-project-template') }}</a-button
        >
      </a-col>
    </a-row>

    <div class="content">
      <a-table
        :data="projectList"
        :bordered="false"
        :scroll="{ x: '100%', y: 'calc(100vh - 380px)' }"
        :pagination="pageConfig"
        @page-change="pageChange"
        @page-size-change="pageSizeChange"
        class="project-table"
      >
        <template #columns>
          <a-table-column
            :title="$t('dashboard.index')"
            :width="60"
            align="center"
          >
            <template #cell="{ rowIndex }">
              {{
                (pageConfig.pageSize ? pageConfig.pageSize : 10) *
                  ((pageConfig.current ? pageConfig.current : 1) - 1) +
                (rowIndex + 1)
              }}
            </template>
          </a-table-column>
          <a-table-column :title="$t('dashboard.template-name')">
            <template #cell="{ record }">
              <span class="name" @click="clickRow(record)">{{
                record.name
              }}</span>
            </template>
          </a-table-column>
          <a-table-column
            :title="$t('dashboard.project-type')"
            data-index="type"
          >
            <template #cell="{ record }">
              <span>{{ ProjectTypeMaps[record.type] || record.type }}</span>
            </template>
          </a-table-column>
          <a-table-column
            :title="$t('dashboard.project-code')"
            data-index="code"
          ></a-table-column>
          <a-table-column
            :title="$t('dashboard.create-time')"
            data-index="createDate"
          ></a-table-column>
          <a-table-column
            :title="$t('dashboard.creater')"
            data-index="createName"
          ></a-table-column>
          <a-table-column
            v-if="[0, 1].includes(user.admin)"
            :title="$t('dashboard.operations')"
            align="center"
          >
            <template #cell="{ record }">
              <a-popconfirm
                v-if="deleteControl(record.createBy)"
                :content="$t('dashboard.confirm-delete-template')"
                position="left"
                @ok="remove(record)"
              >
                <a-tooltip :content="$t('dashboard.delete')">
                  <a-button type="text">
                    <img src="@/assets/images/delete.png" style="width: 20px" />
                  </a-button>
                </a-tooltip>
              </a-popconfirm>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <div class="components">
      <CreateTemplate
        v-model:visible="createDialogVisible"
        @submit="createSuccess"
      ></CreateTemplate>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref } from 'vue';
  import { PaginationProps, Notification } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  import { useGlobalModeStore, useUserStore } from '@/store';
  import { getProjectList, ProjectListParams } from '@/api/project';
  import { ProjectTypeMaps } from '@/directionary/project';
  import { setProject } from '@/utils/project-listener';
  import ListTitle from '@/components/list-title/index.vue';
  import CreateTemplate from './create-template.vue';
  import { useI18n } from 'vue-i18n';
  import { deleteProject } from '../project-list/api';
  import { getUserId } from '@/utils/auth';
  import { setLocalstorage } from '@/utils/localstorage';


  const { t } = useI18n();
  const userStore = useUserStore();
  const user = ref(userStore.userInfo);
  const router = useRouter();
  const globalModeStore = useGlobalModeStore();
  const globalMode = computed(() => {
    return globalModeStore.getGlobalMode;
  });
  const searchParams = reactive({
    projectName: '',
  });
  const userId = getUserId() || '';

  const projectList = ref<object[]>([]);
  const pageConfig: PaginationProps = reactive({
    showTotal: true,
    showMore: false,
    showJumper: true,
    showPageSize: true,
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    total: 100,
  });

  const getList = () => {
    const params: ProjectListParams = {
      pageNo: pageConfig.current || 1,
      pageSize: pageConfig.pageSize || 20,
      projectType: 1,
      name: searchParams.projectName || '',
    };
    getProjectList(params).then((res: any) => {
      if (res.code === 8000000) {
        projectList.value = res.data?.list || [];
        pageConfig.total = res.data?.total || projectList.value.length || 10;
      }
    });
  };

  const deleteControl = (creater: string) => {
    const { admin, username } = user.value;
    let result = false;
    // 项目创建员，超级管理员
    if ((admin === 1 && username === creater) || admin === 0) {
      result = true;
    }
    return result;
  };

  const remove = async (record: any) => {
    const res: any = await deleteProject(record.id);
    if (res.code === 8000000) {
      Notification.success({
        id: 'deleteProject',
        title: 'Success',
        content: t('delete-successful'),
      });
      getList();
    } else {
      Notification.error({
        id: 'deleteProject',
        title: 'error',
        content: t('delete-failed'),
      });
    }
  };

  const clickRow = (row: any) => {
    userStore.setProjectTemplate('1');
    const mode: string = globalMode.value;
    // setProject(row);
    const path = `project/${row.id}/project-member`;
    router.push({
      path,
      // name: 'project',
      // params: {
      //   projectId: row.id,
      // },
    });
    globalModeStore.changeGlobalMode('project');
    setLocalstorage(`last_project_${userId}`, row.id);
  };
  const createSuccess = (state: boolean) => {
    if (state) {
      getList();
    }
  };
  const pageSizeChange = (size: number): void => {
    pageConfig.pageSize = size;
    getList();
  };
  const pageChange = (current: number): void => {
    pageConfig.current = current;
    getList();
  };

  const createDialogVisible = ref(false);

  onMounted(() => {
    getList();
  });
</script>

<style scoped lang="less">
  .project-list {
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: var(--color-bg-2);
    border: 1px solid var(--color-neutral-3);
  }

  .top-box {
    border-bottom: 1px solid #e5e6eb;
    margin-bottom: 20px;
    flex-shrink: 0;
  }

  .search-bar {
    position: relative;
    flex-shrink: 0;
    background-color: var(--color-bg-2);
    border-radius: 4px;
    .opt-btn {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: var(--color-bg-2);
    border-radius: 4px;
  }

  .project-table {
    height: 100%;
    :deep(.arco-table-container) {
      height: 100%;
      background-color: var(--color-bg-2);
    }
    :deep(.arco-table-pagination) {
      margin-top: 16px;
      padding: 0;
      background-color: var(--color-bg-2);
    }
    :deep(.arco-table-th) {
      background-color: var(--color-fill-2);
      font-weight: 500;
    }
  }

  .name {
    color: rgb(var(--arcoblue-6));
    cursor: pointer;
  }

  :deep(.arco-table-content .arco-scrollbar:nth-child(2)) {
    height: 100%;
  }
  :deep(.arco-table-header + .arco-scrollbar-track-direction-horizontal) {
    bottom: -510px;
  }
  :deep(.arco-input-wrapper) {
    border: 1px solid #c9cdd4;
      background-color: transparent;
    }
</style>
