<template>
  <a-spin ref="spin" :loading="tableLoading" class="folder-table-panel">
    <a-table
      ref="folderTable"
      :columns="columns"
      :data="tableData"
      :scroll="{ x: '100%', y: 'calc(100% - 30px)' }"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      :bordered="false"
      row-key="id"
      :pagination="{
        showTotal: true,
        showPageSize: true,
        showJumper: true,
        defaultPageSize: 10,
        pageSizeOptions: [10, 20, 50, 100],
      }"
      @selection-change="selectionChange"
    >
      <template #empty>
        <div class="empty-wrapper">
          <img src="@/assets/images/schedule/schedule-bg.png" alt="" />
          <div>{{ $t('standard-attribute.no-data-available') }}</div>
        </div>
      </template>

      <template #name="{ record }">
        <div class="table-name">
          <file-image
            :file-name="record.name"
            :is-sysFile="isSysFolder(record.sysType)"
            :is-file="!!record.folderId"
            style="margin-right: 8px"
          />
          <a-tooltip :content="i18FolderName(record)">
            <span
              style="
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
              @click="clickNameHandle(record)"
              >{{ i18FolderName(record) }}</span
            >
          </a-tooltip>
        </div>
      </template>
      <!-- 转换状态 -->
      <template #status="{ record }">
        <!-- <span>{{
          record.fileToken && !isPicFile(record) && isRequiredConversion(record)
            ? formatStatus(record.status, record.isCombination, record)
            : ''
        }}</span> -->
        <ul class="custom-list">
          <li
            v-show="!isPicFile(record) && isRequiredConversion(record)"
            :style="{ color: getStatusColor(record.status) }"
            >{{
              record.fileToken &&
              !isPicFile(record) &&
              isRequiredConversion(record)
                ? formatStatus(record.status, record.isCombination, record)
                : ''
            }}</li
          >
        </ul>
      </template>
      <template #updateUserName="{ record }">
        <div>{{ record.updateUserName }}</div>
      </template>
      <template #isLocked="{ record }">
        <div v-if="record.abandon === 1" class="locked-box">
          <img src="@/assets/images/file-manager/nullify.png" />{{
            $t('standard-attribute.invalidated')
          }}
        </div>
        <div
          v-else-if="
            (record.type !== 'WIP' || currentFolderRole === 1) &&
            !(record.type === 'SHARED' || record.type === 'PUBLISHED')
          "
          class="locked-box"
        >
          <img
            v-if="!(record.type === 'SHARED' || record.type === 'PUBLISHED')"
            src="@/assets/images/file-manager/read.png"
          />
          {{ $t('standard-attribute.read-only') }}
        </div>
        <a-tooltip
          v-else-if="
            record.type === 'WIP' &&
            record.folderId &&
            record.isLocked &&
            currentFolderRole > 1
          "
          :content="`${record.updateUserName}${$t('file-manage.editing')}`"
        >
          <!--            @click="unlock(record)"-->
          <div class="locked-box" style="cursor: pointer">
            <img src="@/assets/images/file-manager/lock.png" />
            {{ t('file-manage.lock') }}
          </div>
        </a-tooltip>
        <div v-else class="locked-box">
          <img src="@/assets/images/file-manager/edit.png" />
          {{ t('file-manage.writeable') }}
        </div>
      </template>
      <template #size="{ record }">
        {{ record.size ? getFileSize(record.size) : '' }}
      </template>
      <template #version="{ record }">
        <a-tag
          v-if="record.version"
          class="tag"
          bordered
          color="cyan"
          @click="showVersions(record)"
          >V{{ record.version }}</a-tag
        >
      </template>
      <template #updateDate="{ record }">
        {{ record.updateDate || '' }}
      </template>

      <template #classificationName="{ record }">
        <a-tooltip :content="record.classificationName">
          <a-tag
            v-if="record?.classificationName"
            nowrap
            style="
              border-radius: 2px;
              background-color: #e8fffb;
              color: #0fc6c2;
            "
          >
            {{ record.classificationName }}
          </a-tag>
        </a-tooltip>
      </template>

      <template #optional="{ record }">
        <!-- 禁用权限：团队文件夹 || 团队下系统生成文件夹 -->
        <a-button
          :disabled="
            record.parentId === 0 ||
            isSysFolder(record.sysType) ||
            [1, 2, 3, 4].includes(hiddenSlot)
          "
          type="text"
          size="small"
          @click="bindStandard(record)"
        >
          {{ $t('standard-attribute.binding-standard') }}
        </a-button>

        <a-button
          v-show="record.checkStatus === 2"
          type="text"
          size="small"
          style="color: #0fc6c2"
        >
          {{ $t('standard-attribute.in-check') }}
        </a-button>

        <a-popconfirm
          :content="$t('standard-attribute.should-verification-initiated')"
          position="left"
          @ok="initiateVerification(record)"
        >
          <!-- 禁用权限：文件 && 未绑定标准 || 团队文件夹 -->
          <a-button
            v-show="record.checkStatus !== 2"
            :disabled="
              (record.folderId && !record.fileStandardBindId) ||
              record.parentId === 0 ||
              isSysFolder(record.sysType)
            "
            type="text"
            size="small"
          >
            {{ $t('standard-attribute.initiate-verification') }}
          </a-button>
        </a-popconfirm>

        <a-button
          :disabled="!record.folderId || ![3].includes(record.checkStatus)"
          type="text"
          size="small"
          @click="gotoResult(record)"
        >
          {{ $t('standard-attribute.view-results') }}
        </a-button>
      </template>
    </a-table>
  </a-spin>
  <!-- 图片预览 -->
  <ImgViewer v-if="imgViewModel.visible" :view-modal="imgViewModel"></ImgViewer>
  <move-dialog
    v-model:visible="moveModal"
    :title="$t('file-manage.select-target-folder')"
    :ok-function="beforeMoveOkHandler"
    :show-type="[]"
    :dis-check-hierarchy="[1]"
    check-type="single"
    output-type="id"
    is-clear-key
  ></move-dialog>
  <initiate-validation
    v-if="validateVisible"
    v-model:visible="validateVisible"
    :type="bindType"
    :single-data="bindData"
    @refresh="refreshTableData"
  ></initiate-validation>
</template>

<script setup lang="ts">
  import { defineEmits, watch, ref, computed, toRaw } from 'vue';
  import { useI18n } from 'vue-i18n';
  import {
    FileAndFolderNodeMessage,
    FileMessage,
    FolderMessage,
    getFileList,
  } from '@/api/tree-folder';
  import { getFileSize } from '@/utils/file';
  import FileImage from '../image-file.vue';
  import useFileStore from '@/store/modules/file/index';

  import { storeToRefs } from 'pinia';
  import ImgViewer from '@/components/imgView/index.vue';
  import MoveDialog from '@/components/tree-folder/index.vue';

  import {
    isPicFile,
    isRequiredConversion,
    isWpsFile,
    isTopFolder,
    isSysFolder,
  } from '@/views/projectSpace/file/utils';
  import { moveFileAndFolder } from '@/views/projectSpace/file/api';
  import { Message } from '@arco-design/web-vue';

  import { wpsViewHandle } from '@/hooks/wps';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import InitiateValidation from './initiate-validation.vue';

  import {
    getBindStandardFileList,
    startCheckByFilesAndFolders,
    startCheckBySinglleFile,
  } from '../../api';
  import { getUserId } from '@/utils/auth';
  import router from '@/router';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';

  const { t } = useI18n();

  const fileStore = useFileStore();

  const currentFolder = computed(() => fileStore.currentFolder);
  const { hiddenSlot, tableLoading, selectedKeys } = storeToRefs(fileStore);

  const { i18FolderName } = useI18nHandleName();

  const emits = defineEmits(['expendFolder', 'handleDownload']);
  const bindType = ref<string>('singleFile'); // 绑定类型
  const bindData = ref(); // 绑定文件数据

  const prjStore = usePrjPermissionStore();
  const currentFolderRole = computed(() => {
    const currentTeam = (prjStore.teamList as any[]).filter((item) => {
      return item.id === currentFolder.value.teamId;
    })[0];
    return currentTeam ? currentTeam.role : 5;
  });

  const moveModal = ref(false);

  const columns = computed(() => {
    return [
      {
        title: t('standard-attribute.name'),
        dataIndex: 'name',
        slotName: 'name',
        sortable: {
          sortDirections: ['ascend', 'descend'],
        },
        width: 300,
        align: 'left',
        fixed: 'left',
      },
      {
        title: t('standard-attribute.file-size'),
        width: 120,
        dataIndex: 'size',
        slotName: 'size',
        align: 'left',
      },
      {
        title: t('standard-attribute.transition-state'),
        dataIndex: 'status',
        slotName: 'status',
        width: 90,
        align: 'left',
      },
      {
        title: t('standard-attribute.binding-standard'),
        dataIndex: 'classificationName',
        slotName: 'classificationName',
        width: 160,
        align: 'left',
      },
      {
        title: t('standard-attribute.operation'),
        slotName: 'optional',
        titleSlotName: 'optionalTitle',
        width: 175,
        align: 'left',
        fixed: 'right',
      },
    ];
  });

  const fileList = ref([]);
  const folderList = ref<FolderMessage[]>([]);

  const getFiles = async () => {
    if (!isTopFolder(currentFolder.value.id)) {
      const res: any = await getFileList(currentFolder.value.id!);
      if (res.status) {
        const list = res?.data?.list;
        fileList.value = list?.length
          ? list.map((item: any) => {
              item.type = currentFolder.value.type;
              return item;
            })
          : [];
      }
    }
  };

  const tableData = computed(() => fileStore.tableData);

  function selectionChange(rowkeys: string[]) {
    fileStore.setSelectedTableRowkeys(rowkeys);
  }

  const bindStandardData = ref<any>([]); // 绑定标准文件分页数据
  // 绑定标准文件分页
  const bindStandardFilePagination = async () => {
    bindStandardData.value.length = 0;
    const userId = getUserId() || '';
    const projectId = localStorage.getItem(`work_last_project_${userId}`) || '';

    if (!isTopFolder(currentFolder.value.id)) {
      const params = {
        // fileName: folder.name,
        folderId: selectedKeys.value[0],
        pageNo: 1,
        pageSize: 99999,
        projectId,
      };
      try {
        await getBindStandardFileList(params).then((res) => {
          if (res.status) {
            const { list } = res.data;
            bindStandardData.value = list?.length > 0 ? list : [];
          }
        });
      } catch (error) {
        console.log(error);
      }
    }
  };

  // 将分页数据合并到表格数据中
  const mergePaginationData = (initData: any[]) => {
    // 创建一个以 fileId 为 key 的 Map
    const bindMap = new Map(
      bindStandardData.value.map((item) => [item.fileId, item])
    );

    // 遍历 initData，找到匹配项并合并属性
    initData.forEach((item) => {
      const bindInfo = bindMap.get(item.id);
      if (bindInfo) {
        Object.assign(item, bindInfo); // 把 bindInfo 中的字段合并进 item
      }
    });

    console.log('[ initData ] >', initData);
  };

  const folderTable = ref();
  async function refreshTableData(folder?: FolderMessage) {
    tableLoading.value = true;
    folderList.value =
      (folder ? folder.children : currentFolder.value.children) || [];
    fileList.value = [];

    await getFiles();

    const folders = toRaw(folderList.value);
    const files = toRaw(fileList.value);
    await bindStandardFilePagination();
    mergePaginationData([...folders, ...files]);

    fileStore.setTableData([
      ...folders.filter((_folder) => _folder.id !== 'add'),
      ...files,
    ]);

    folderTable?.value.selectAll(false);
    fileStore.setSelectedTableRowkeys([]);
    tableLoading.value = false;
  }

  watch(
    () => currentFolder.value,
    async (folder: any) => {
      // 更新当前文件夹的系统类型
      if (folder && folder.sysType) {
        hiddenSlot.value = Math.min(Math.max(folder.sysType, 1), 4) || 5; // 确保值在 1 到 4 之间，否则默认为 5
      } else {
        hiddenSlot.value = 5;
      }
      if (!folder.id) return;
      await refreshTableData(folder);
    },
    {
      immediate: true,
      deep: true,
    }
  );

  const versionShow = ref(false);
  const currentFile = ref<FileMessage>();
  function showVersions(file: FileAndFolderNodeMessage) {
    currentFile.value = file;
    versionShow.value = true;
  }

  const imgViewModel = computed(() => fileStore.imgViewModal);

  function isEditableFile(record: FileMessage) {
    return (
      currentFolderRole.value > 1 &&
      record.type === 'WIP' &&
      !record.isLocked &&
      isWpsFile(record)
    );
  }

  // 查看校验结果
  const gotoResult = (record: any) => {
    const url = router.resolve({
      path: '/standardResult',
      query: {
        fileId: record?.id || '',
      },
    }).href;
    window.open(url);
  };

  async function clickNameHandle(record: any) {
    // 处理文件夹点击 - 进入下一层
    if (!record.folderId) {
      emits('expendFolder', record);
      return;
    }

    // 处理可编辑文件 - 打开编辑模式
    if (isEditableFile(record)) {
      wpsViewHandle(record, 'edit', 'admin');
      return;
    }

    // 处理普通文件点击 - 根据校验状态决定行为
    if (record.checkStatus === 3) {
      gotoResult(record);
    } else {
      Message.info('请先发起校验！');
    }
  }

  const formatStatus = (status: number, type: number, record: any) => {
    if (type === 2) {
      // 碰撞检查
      if ([-1].includes(status)) return t('file-manage.failed');
      if ([2].includes(status)) return t('file-manage.success');
      if ([0, 1].includes(status)) return t('file-manage.in-conversion');
    } else if (type === 1) {
      return t('file-manage.success');
    } else {
      if (isWpsFile(record)) return t('');
      // 普通模型文件
      if ([-7, -2, -1].includes(status)) return t('file-manage.failed');
      if ([0, 3].includes(status)) return t('file-manage.success');
      if ([-3, 1, 4].includes(status)) return t('file-manage.in-conversion');
      if (status === 2) return t('file-manage.in-queue');
      if (!status) return t('file-manage.not-starts'); // 新增模型状态--未开始
    }
    return '';
  };

  // 获取状态颜色
  const getStatusColor = (status: number) => {
    if ([-7, -2, -1].includes(status)) return '#FF4D4F';
    if ([0, 3].includes(status)) return '#33B737';
    if ([-3, 1, 4].includes(status)) return '#0FC6C2';
    if (status === 2) return '#3366FF';
    if (!status) return '#FCA800';
    return '#000'; // 默认颜色
  };

  let beforeMoveOkHandler = (treeDataPromise: () => Promise<any>) =>
    Promise.resolve(true);

  function batchMoveHandle() {
    beforeMoveOkHandler = async function moveRequest(
      treeDataPromise: () => Promise<any>
    ): Promise<boolean> {
      const parentId: string = await treeDataPromise();
      if (!parentId) {
        Message.error(t('file-manage.select-target-folder'));
        return false;
      }
      const ids = fileStore.moveIds;

      if (ids.folderIds.includes(parentId)) {
        Message.error('无法将文件夹移动到自己目录下');
        return false;
      }
      const res: any = await moveFileAndFolder(
        ids.fileIds,
        ids.folderIds,
        parentId
      );
      const result: any = res;
      emits('expendFolder', currentFolder.value);

      if (result.code === 8000000) {
        Message.success(t('file-manage.success'));
        return true;
      }
      return false;
    };
    moveModal.value = true;
  }

  const validateVisible = ref(false);
  // 绑定标准
  const bindStandard = (record: any) => {
    validateVisible.value = true;
    bindData.value = record;
    if (record.folderId) {
      bindType.value = 'singleFile'; // 单个文件
    } else {
      bindType.value = 'singleFolder'; // 单个文件夹
    }
  };

  // 单个模型文件校验
  const checkSinglleFile = async (record: any) => {
    try {
      const params = {
        fileId: record.id,
      };
      await startCheckBySinglleFile(params).then((res) => {
        if (res.status) {
          Message.success(t('standard-attribute.success'));
        }
      });
    } catch (error) {
      console.log(error);
    }
  };

  // 文件及文件夹校验
  const checkFilesAndFolders = async (record: any) => {
    try {
      const params = {
        folderIds: [record.id],
      };
      await startCheckByFilesAndFolders(params).then((res) => {
        if (res.status) {
          Message.success(t('standard-attribute.success'));
        }
      });
    } catch (error) {
      console.log(error);
    }
  };

  // 发起校验
  const initiateVerification = async (record: any) => {
    if (record.folderId) {
      // 单个文件
      await checkSinglleFile(record);
    } else {
      // 单个文件夹
      await checkFilesAndFolders(record);
    }
    refreshTableData();
  };

  defineExpose({ batchMoveHandle });
</script>

<style scoped lang="less">
  .folder-table-panel {
    width: 100%;
    height: 100%;
    padding: 0 20px;

    .empty-wrapper {
      height: calc(100vh - 365px);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      img {
        display: block;
        width: 140px;
        height: 140px;
      }
      div {
        margin-top: 16px;
        color: #4e5969;
      }
    }
  }
  .table-name {
    display: flex;
    align-items: center;
    color: rgb(22, 93, 255);
    cursor: pointer;
  }
  :deep(.arco-btn-size-small) {
    padding: 0 0;
    margin-right: 8px;
    font-size: 13px;
  }
  :deep(.arco-table-container) {
    height: 100%;
  }

  .custom-list {
    list-style: none; /* 隐藏默认标记 */
    padding-left: 0; /* 移除默认内边距 */
  }

  .custom-list li {
    position: relative;
    padding-left: 12px; /* 为自定义标记留出空间 */
  }

  .custom-list li::before {
    content: '•'; /* 自定义标记符号（可以是文字、emoji或图标） */
    position: absolute;
    left: 0; /* 调整标记与文本的间距 */
  }
</style>
