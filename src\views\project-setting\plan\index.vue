<template>
  <div class="container">
    <list-title
      :title-text="$t('project-setting.schematic-diagram-of-milestone-nodes')"
      :show-button="isPrjAdmin ? true : false"
      :btn-text="$t('project-setting.create-milestones')"
      :diagram-btn-text="$t('project-setting.view-diagram')"
      @add="handleAdd"
      @open="handleViewDiagram"
      class="title"
    />

    <!-- 示意图 -->
    <sketch
      :all-node-rows="allNodeRows"
      :show-node-rows="showNodeRows"
      :show-all="showAll"
      :row-size="ROW_SIZE"
      @expand="handleExpand"
    />

    <!-- 搜索区 -->
    <!-- <a-row justify="space-between" align="center">
      <a-col :span="20">
        <a-form
          :model="searchForm"
          label-align="left"
          layout="inline"
          auto-label-width
          class="search-area"
        >
          <a-form-item
            field="name"
            :label="$t('project-setting.name')"
            label-col-flex="auto"
            content-class="item"
          >
            <a-input
              v-model="searchForm.name"
              placeholder="请输入"
              allow-clear
            />
          </a-form-item>
          <a-form-item
            :label="$t('project-setting.time')"
            label-col-flex="auto"
            content-class="range-item"
          >
            <a-range-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </a-form-item>
        </a-form>
      </a-col>
      <a-col :span="4" class="btn">
        <a-space>
          <a-button type="primary" @click="handleSearch">{{
            $t('project-setting.search')
          }}</a-button>
          <a-button type="outline" @click="handleReset">{{
            $t('project-setting.reset')
          }}</a-button>
        </a-space>
      </a-col>
    </a-row> -->

    <!-- <div class="table-container" style="height: 200px; overflow: auto">
      <a-table
        stripe
        class="table"
        :loading="loading"
        :columns="admin === -1 ? columnsView : columnsEdit"
        :data="milestoneList"
        :scroll="{ x: '100%', y: '100%' }"
        :bordered="false"
        :pagination="pagination"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
        <template #operations="{ record }">
          <a-button type="text" size="small" @click="handleEdit(record)">{{
            $t('project-setting.edit')
          }}</a-button>
          <a-popconfirm
            :content="$t('project-setting.is-delete')"
            position="left"
            @ok="remove(record.id)"
          >
            <a-button type="text" size="small">{{
              $t('project-setting.delete')
            }}</a-button>
          </a-popconfirm>
        </template>
      </a-table>
    </div> -->

    <list-title
      :title-text="$t('project-setting.matrix-of-responsibilities')"
      :show-button="false"
      class="matrTitle title"
    />
    <div class="table-container" style="height: 200px; overflow: auto">
      <div
        v-for="item in milestoneList"
        :key="item.milestoneId"
        style="margin-top: 10px"
      >
        <div>
          <span class="milestone">{{ item.milestoneName }}</span>
          <span class="date">{{ item.milestoneEndTime }}</span>
          <a-button @click="handleEdit(item)" v-if="isPrjAdmin ? true : false">
            <icon-edit />
          </a-button>
          <a-button
            @click="handleDelateMilestone(item)"
            v-if="isPrjAdmin ? true : false"
          >
            <icon-delete style="color: red" />
          </a-button>
        </div>
        <a-table
          v-model:expandedKeys="expandedKeys"
          stripe
          class="table"
          :loading="loading"
          :pagination="false"
          row-key="id"
          :columns="
            isPrjAdmin || prjPermissionStore.teamList?.length > 0
              ? columnsEdit
              : columnsView
          "
          :data="item.taskTreeList"
          show-empty-tree
          style="margin-top: 20px"
        >
          <template v-slot:empty>
            <a-empty :description="$t('project-setting.noData-title')" />
          </template>
          <template #expand-icon="{ expanded }">
            <!-- 或者使用不同的图标元素 -->
            <icon-down v-if="!expanded" />
            <icon-up v-else />
          </template>
          <!-- 状态 -->
          <template #type="{ record }">
            <span>{{
              record.type == 0
                ? $t('project-setting.main-tasks')
                : $t('project-setting.subtasks')
            }}</span>
          </template>

          <!-- 团队名称前加颜色 -->
          <template #content="{ record }">
            <color-select
              :init-color="record.color"
              @color-changed="handleColorChanged($event, record)"
            />
          </template>
          <template #name="{ record }">
            <a-tooltip :content="record.teamName">
              <div class="icon-container">
                <span style="display: flex; align-items: center"
                  ><span
                    class="normal-icon"
                    :style="`background-color: ${record.color || ''}`"
                  ></span
                  >{{ record.teamName }}</span
                >
              </div>
            </a-tooltip>
          </template>

          <template #describe="{ record }">
            <a-tooltip :content="record.description">
              <span>{{ record.description }}</span>
            </a-tooltip>
          </template>
          <template #startEndTime="{ record }">
            <span>{{ record.startTime }}-{{ record.endTime }}</span>
          </template>

          <template #operations="{ record }">
            <a-button
              v-if="
                record.type == 1 &&
                (isPrjAdmin ||
                  ownTeamList.includes(
                    getParentData(record.id, item.taskTreeList).teamId
                  ))
              "
              type="text"
              size="small"
              @click="ismantlingTasks(record, item)"
              >{{ $t('project-setting.edit') }}</a-button
            >
            <a-button
              v-if="
                record.type == 1 &&
                (isPrjAdmin ||
                  ownTeamList.includes(
                    getParentData(record.id, item.taskTreeList).teamId
                  ))
              "
              type="text"
              size="small"
              @click="handleDelate(record)"
              >{{ $t('project-setting.delete') }}</a-button
            >
            <a-button
              v-if="
                record.type == 0 &&
                (isPrjAdmin || ownTeamList.includes(record.teamId))
              "
              type="text"
              size="small"
              @click="dismantlingTasks(record)"
              >{{ $t('project-setting.dismantling-tasks') }}</a-button
            >
          </template>
        </a-table>
      </div>
    </div>
    <milestone-dialog
      v-model:visible="dialogVisible"
      :origin-data="milestoneInfo"
      :team-data="teamMilestoneInfo"
      :handle-type="handleType"
      :plan-start="projectInfo.planStart"
      :plan-end="projectInfo.planEnd"
      :task-end="projectInfo.taskEnd"
      @refresh="handleRefresh"
    />
    <tasks-dialog
      v-model:visible="dialogTasksVisible"
      :origin-data="milestoneTaskInfo"
      :parent-data="milestoneTaskParentInfo"
      :handle-type="handleTaskType"
      :plan-start="projectTaskInfo.planStart"
      :plan-end="projectTaskInfo.planEnd"
      @refresh="handleTaskRefresh"
    />
    <!-- 图示 -->
    <ViewDiagram v-model:visible="dialogWithViewDiagram"></ViewDiagram>
    <!-- 图示 -->
  </div>
</template>

<script lang="ts" setup>
  import { computed, reactive, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import useUserStore from '@/store/modules/user/index';
  import { storeToRefs } from 'pinia';
  import useLoading from '@/hooks/loading';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import {
    MilestoneRecord,
    queryMilestoneList,
    removeMilestone,
    MilestoneRecordTeam,
    queryMilestoneMatrix,
    parentDataeRecord,
    removeTask,
  } from './api';
  import colorSelect from '../team/components/color-select.vue';
  import { ProjectRecord, queryProjectDetail } from '../projectNew/api';
  import MilestoneDialog from './components/milestone-info.vue';
  import TasksDialog from './components/teamTasks-add.vue';
  import ListTitle from '@/components/list-title/index.vue';
  import Sketch from './components/sketch.vue';
  import { Notification, Modal } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import { array } from 'js-md5';

  // 查看图示组件
  import ViewDiagram from './components/ViewDiagram.vue';

  const { t } = useI18n();
  const prjPermissionStore = usePrjPermissionStore();
  const { isPrjAdmin, teamList } = prjPermissionStore;
  const store = useUserStore();
  const { admin } = storeToRefs(store);

  const ownTeamList = [];
  if (teamList && teamList.length > 0) {
    const teamLists = teamList.filter((item) => {
      return item.role === 5;
    });
    if (teamLists.length > 0) {
      teamLists.forEach((element) => {
        ownTeamList.push(element.id);
      });
    }
  }
  // 项目信息
  const route = useRoute();

  const projectId = computed(() => {
    return String(route?.params?.projectId);
  });
  const emptyProject: ProjectRecord = {
    id: projectId.value,
    name: '',
    code: '',
    type: 0,
    protemId: '',
    planStart: '',
    planEnd: '',
    description: '',
  };
  const expandedKeys = ref([]);
  const projectInfo = ref<ProjectRecord>(emptyProject);
  const projectTaskInfo = ref<ProjectRecord>(emptyProject);

  // 图示弹窗
  const dialogWithViewDiagram = ref(false);

  const handleViewDiagram = () => {
    dialogWithViewDiagram.value = true;
  };

  // 新增/编辑弹窗
  const dialogVisible = ref(false);
  const dialogTasksVisible = ref(false);
  const handleType = ref('view');
  const handleTaskType = ref('view');
  const emptyMilestone = {
    projectId: projectId.value,
    name: '',
    endTime: '',
    describe: '',
    createDate: '',
  };
  const milestoneInfo = ref<MilestoneRecord>({ ...emptyMilestone });
  const teamMilestone = {
    teamData: [],
  };
  const parentData = {
    milestoneId: '',
    parentId: '',
    projectId: '',
    parentTeamId: '',
  };
  const teamMilestoneInfo = ref<MilestoneRecordTeam>({ ...teamMilestone });
  const milestoneTaskInfo = ref<MilestoneRecordTeam>({ ...teamMilestone });
  const milestoneTaskParentInfo = ref<parentDataeRecord>({ ...parentData });

  // 列表
  const { loading, setLoading } = useLoading(true);
  const milestoneList = ref<MilestoneRecord[]>([]);

  const columnsView = computed<TableColumnData[]>(() => [
    {
      title: t('project-setting.type'),
      dataIndex: 'type',
      slotName: 'type',
      align: 'center',
      width: 180,
    },
    {
      title: t('project-setting.teamName'),
      dataIndex: 'name',
      slotName: 'name',
      width: 200,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('project-setting.team-task-description'),
      dataIndex: 'describe',
      slotName: 'describe',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('project-setting.team-start-end-time'),
      dataIndex: 'startEndTime',
      slotName: 'startEndTime',
      align: 'center',
      width: 200,
    },
  ]);

  const columnsEdit = computed<TableColumnData[]>(() => [
    ...columnsView.value,
    {
      title: t('project-setting.operations'),
      slotName: 'operations',
      align: 'center',
      width: 170,
    },
  ]);

  const pagination = reactive({
    showTotal: true,
    showPageSize: true,
    showJumper: true,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    current: 1,
    total: 0,
  });

  // 示意图
  const showAll = ref(false); // 是否展示所有节点
  const allMilestoneList = ref<MilestoneRecord[]>([]); // 所有里程碑列表
  const ROW_SIZE = 4; // 一行最多展示几个节点

  // 所有节点二维数组
  const allNodeRows = computed(() => {
    const allNodes = JSON.parse(JSON.stringify(allMilestoneList.value));
    const startNode = {
      id: 'startNode',
      name: t('project-setting.project-start'),
      endTime: projectInfo.value.planStart?.split(' ')[0],
    };
    const endNode = {
      id: 'endNode',
      name: t('project-setting.project-end'),
      endTime: projectInfo.value.planEnd?.split(' ')[0],
    };
    allNodes.unshift(startNode);
    allNodes.push(endNode);

    const rows = [];
    for (let i = 0; i < allNodes.length; i += ROW_SIZE) {
      rows.push(allNodes.slice(i, i + ROW_SIZE));
    }
    return rows;
  });

  // 当前展示节点二维数组
  const showNodeRows = computed(() => {
    if (showAll.value) {
      // 展示所有节点
      return allNodeRows.value;
    }
    // 展示一行节点
    return allNodeRows.value.slice(0, 1);
  });

  // 查询项目详细信息
  const getProjectDetail = () => {
    console.log('2222222222222222222222222222');
    const params = {
      id: projectId.value ? projectId.value : '1721357192716677122',
    };
    queryProjectDetail(params)
      .then((res) => {
        projectInfo.value = res.data;
      })
      .catch((err) => {
        console.log(err);
      });
  };

  // 分页查询里程碑列表
  const getMilestoneList = () => {
    setLoading(true);
    // const params: any = {
    //   pageNo: pagination.current,
    //   pageSize: pagination.pageSize,
    //   projectId: projectId.value,
    // };
    // Object.keys(searchForm).forEach((key) => {
    //   if (searchForm[key]) {
    //     params[key] = searchForm[key];
    //   }
    // });
    // queryMilestoneList(params)
    //   .then((res) => {
    //     milestoneList.value = res.data.list || [];
    //     pagination.total = res.data.total || 10;
    //   })
    //   .catch((err) => {
    //     console.log(err);
    //   })
    //   .finally(() => {
    //     setLoading(false);
    //   });
    const params: any = {
      projectId: projectId.value,
    };
    queryMilestoneMatrix(params)
      .then((res) => {
        milestoneList.value = res.data || [];
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 查询所有里程碑列表
  const getAllMilestoneList = () => {
    const params = {
      pageNo: 1,
      pageSize: 1000,
      projectId: projectId.value,
    };
    queryMilestoneList(params)
      .then((res) => {
        allMilestoneList.value = res.data.list || [];
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // 调用接口修改团队颜色
  const handleColorChanged = async (nValue: string, record) => {
    const params = {
      ...record,
      color: nValue,
    };
    delete params.isShowColor;
    delete params.isShowTooltip;
    const res = await getAllMilestoneList(params);
    if (res.status) {
      // Message.success(t('edit-successful'));
      record.isShowColor = false;
      record.color = nValue;
      // projectStore.setPermission(projectId.value);
    }
  };
  // 示意图展开/折叠
  const handleExpand = () => {
    showAll.value = !showAll.value;
  };

  // 查询
  const handleSearch = () => {
    getMilestoneList();
  };

  // 新增/编辑后刷新页面
  const handleRefresh = () => {
    handleSearch();
    getAllMilestoneList();
  };
  // 新增/编辑后刷新页面
  const handleTaskRefresh = () => {
    handleSearch();
    getAllMilestoneList();
  };

  // 创建里程碑
  const handleAdd = () => {
    handleType.value = 'add';
    milestoneInfo.value = { ...emptyMilestone };
    projectInfo.value.taskEnd = projectInfo.value.planEnd;
    teamMilestoneInfo.value = {
      teamData: [],
    };
    dialogVisible.value = true;
  };
  // 编辑里程碑
  const handleEdit = (record) => {
    const {
      milestoneEndTime,
      taskTreeList,
      milestoneName,
      milestoneId,
      describe,
    } = record;
    const item = {
      projectId: projectId.value,
      name: milestoneName,
      endTime: milestoneEndTime,
      describe,
      id: milestoneId,
    };
    handleType.value = 'edit';

    projectInfo.value.taskEnd = milestoneEndTime;
    milestoneInfo.value = { ...item };
    if (taskTreeList && taskTreeList.length > 0) {
      taskTreeList.forEach((element) => {
        element.date = [element.startTime, element.endTime];
      });
      teamMilestoneInfo.value = {
        teamData: [...taskTreeList],
      };
    } else {
      teamMilestoneInfo.value = {
        teamData: [],
      };
    }
    dialogVisible.value = true;
  };
  // 删除里程碑
  const removeTaskItem = async (id: string) => {
    const res = await removeMilestone(id);
    return res;
  };
  // 删除里程碑
  const handleDelateMilestone = async (record: MilestoneRecord) => {
    if (record.taskTreeList && record.taskTreeList.length > 0) {
      Modal.info({
        title: t('project-setting.confirm-deletion'),
        hideCancel: false,
        content: t('project-setting.milestone-deletion-hint'),
        onOk: async () => {
          const res: any = await removeMilestone(record.milestoneId);
          if (res.status) {
            Notification.success({
              id: 'id',
              title: 'Success',
              content: t('project-setting.edit-success'),
            });
          }
          handleRefresh();
        },
      });
    } else {
      const res1 = await removeTaskItem(record.milestoneId);
      if (res1.status) {
        Notification.success({
          id: 'id',
          title: 'Success',
          content: t('project-setting.edit-success'),
        });
        handleRefresh();
      }
    }
  };
  // 拆解任务
  const dismantlingTasks = (record: MilestoneRecord) => {
    handleTaskType.value = 'add';
    milestoneTaskInfo.value = {
      teamData: [
        {
          teamId: '',
          startTime: '',
          endTime: '',
          date: [],
          description: '',
          type: 1,
          milestoneId: record.milestoneId,
          parentId: record.id,
          projectId: record.projectId,
          id: '',
          name: '',
        },
      ],
    };

    projectTaskInfo.value.planStart = record.startTime;
    projectTaskInfo.value.planEnd = record.endTime;
    milestoneTaskParentInfo.value = {
      milestoneId: record.milestoneId,
      parentId: record.id,
      projectId: record.projectId,
      parentTeamId: record.teamId,
    };
    dialogTasksVisible.value = true;
  };
  // 删除子任务
  const delateTaskItem = async (id: string) => {
    const res = await removeTask(id);
    return res;
  };
  // 删除拆解任务
  const handleDelate = async (record: MilestoneRecord) => {
    const res = await delateTaskItem(record.id);
    if (res.status) {
      Notification.success({
        id: 'id',
        title: 'Success',
        content: t('project-setting.edit-success'),
      });
      handleRefresh();
    }
  };
  // 编辑拆解任务
  const getParentData = (currentKey, data) => {
    // 递归函数查找父节点
    const findParent = (nodes, key) => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === key) {
          return nodes[i];
        }
        if (nodes[i].children && nodes[i].children.length > 0) {
          const parents = nodes[i];
          const parent = findParent(nodes[i].children, key);
          if (parent) return parents;
        }
      }
      return null;
    };
    return findParent(data, currentKey);
  };
  const ismantlingTasks = (record, item: MilestoneRecord) => {
    const a = getParentData(record.id, item.taskTreeList);
    handleTaskType.value = 'edit';
    record.date = [record.startTime, record.endTime];
    milestoneTaskInfo.value = {
      teamData: [
        {
          ...record,
        },
      ],
    };
    projectTaskInfo.value.planStart = a.startTime;
    projectTaskInfo.value.planEnd = a.endTime;
    milestoneTaskParentInfo.value = {
      milestoneId: a.milestoneId,
      parentId: a.id,
      projectId: a.projectId,
      parentTeamId: a.teamId,
    };
    dialogTasksVisible.value = true;
  };

  getProjectDetail();
  getMilestoneList();
  getAllMilestoneList();
</script>

<style scoped lang="less">
  .search-area {
    padding: 24px 0;

    :deep(.item) {
      width: 220px;
    }

    :deep(.range-item) {
      width: 240px;
    }
  }

  .btn {
    text-align: right;

    /* .arco-btn-size-medium {
      border-radius: var(--border-radius-medium);
    } */
  }
  .matrix {
    margin-left: 12px;
    font-size: 18px;
  }
  .milestone {
    font-size: 18x;
  }
  .date {
    margin-left: 16px;
    margin-right: 16px;

    font-size: 18x;
  }
  .icon-container {
    display: flex;
    align-items: center;
    .normal-icon {
      display: inline-block;
      width: 20px;
      height: 20px;
      margin-right: 10px;
      border-radius: 50%;
    }

    .active-icon {
      box-shadow: 0px 2px 5px 0px #e5e6eb;
      border: 2px solid #ffffff;
      width: 24px;
      height: 24px;
    }
  }
  .circle-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: blue;
    color: white;
    text-align: center;
    line-height: 20px;
  }

  .container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 190px);
    // .title {
    //   margin-top: 20px;
    // }
    .matrTitle{
      margin-top: 20px;
    }
    .table-container {
      flex: 1;
      overflow: hidden;
    }
  }

  /* 修复table高度变化后，出现两个滚动条的问题 */
  :deep(.arco-table-container > .arco-scrollbar > .arco-scrollbar-track) {
    display: none;
  }
</style>
