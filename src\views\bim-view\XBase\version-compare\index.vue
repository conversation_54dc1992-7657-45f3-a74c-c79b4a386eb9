<template>
  <div class="navbar">
    <div class="left-side">
      <a-space @click="toDashboard">
        <img class="logo" alt="logo" :src="logoTop" />
      </a-space>
      <a-divider
        direction="vertical"
        :margin="24"
        :style="{ fontSize: '28px' }"
      />
    </div>
    <div class="right-side">
      <ToolsBar v-show="loadOver" type="compare"></ToolsBar>
    </div>
  </div>

  <div class="content">
    <div class="viewer-box">
      <div id="base-viewer" class="viewer"> </div>
      <div id="compare-viewer" class="viewer"> </div>
      <XBaseViewer
        v-if="
          engine === 'XBase' && 'graphicEngineInfo' in baseModelFile && flagA
        "
        :model-file="baseModelFile"
        :element-id="'base-viewer'"
        :viewer-type="'semanticModel'"
        @get-x-base-instance="getBaseViewer"
      ></XBaseViewer>
      <XBaseViewer
        v-if="engine === 'XBase' && 'graphicEngineInfo' in compareModelFile"
        :model-file="compareModelFile"
        :element-id="'compare-viewer'"
        :viewer-type="'semanticModel'"
        @get-x-base-instance="getCompareViewer"
      ></XBaseViewer>
    </div>
    <Compare
      v-show="toolStore.compareInfoShow.value"
      :model-diff-id="modelDiffId"
      :viewers="viewers"
    ></Compare>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import logoTop from '@/assets/images/logo.png';
  import Compare from './components/compare-table.vue';
  import ToolsBar from '@/views/bim-view/components/toolbar/index.vue';
  import { storeToRefs } from 'pinia';
  import useModelToolsStore from '@/store/modules/model-viewer/index';
  import { getCompareList, GetXBaseSemanticInfo } from './api';
  import XBaseViewer from '@/views/bim-view/XBase/viewer.vue';
  import { engineType } from '@/utils/BIM-Engine/dictionary/common';

  const flagA = ref(false);
  const route = useRoute();
  const { query } = route;
  const engine = ref(engineType[query.engine] || 'XBase');

  const toolStore = storeToRefs(useModelToolsStore());
  const router = useRouter();
  const modelDiffId = ref('');
  const viewers = ref<any>({
    baseViewer: {},
    compareViewer: {},
  });
  const baseModelFile = ref({});
  const compareModelFile = ref({});

  const loadOver = ref(false);

  // 获取base大象云实例
  const getBaseViewer = (viewer: any, data: any) => {
    viewers.value.baseViewer = viewer;
  };
  // 获取compare大象云实例
  const getCompareViewer = (viewer: any, data: any) => {
    viewers.value.compareViewer = viewer;
  };

  const init = async () => {
    const params = {
      projectId: route.query.projectId,
      id: route.query.id,
    };
    const res = await getCompareList(params);
    if (res.status) {
      const compareData = res.data[0];
      const [baseView, compareView] = JSON.parse(compareData.viewPair);
      modelDiffId.value = res.data[0].compareResult;
      loadOver.value = true;

      baseModelFile.value = {
        graphicEngineInfo: baseView,
        name: 'baseView.ifc',
      };
      compareModelFile.value = {
        graphicEngineInfo: compareView,
        name: 'compareView.ifc',
      };
      setTimeout(() => {
        flagA.value = true;
      }, 2000);
    }
  };

  onMounted(() => {
    init();
  });

  const toDashboard = () => {
    const url = router.resolve({
      path: '/dashboard',
    });
    // 打开新窗口
    window.open(url.href);
  };
</script>

<style lang="less" scoped>
  .navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    background-color: var(--color-bg-2);
    border-bottom: 1px solid var(--color-border);
  }

  .left-side {
    display: flex;
    align-items: center;
    padding-left: 8px;
    font-size: 16px;
    cursor: pointer;
    .logo {
      width: 200px;
    }
  }

  .right-side {
    padding-right: 32px;
  }

  #modelViewer {
    height: 100%;
    width: 100%;
  }
  .content {
    position: relative;
    width: 100%;
    height: calc(100vh - 64px);
    display: flex;
    .viewer-box {
      width: 100%;
      flex-shrink: 1;
      position: relative;
      overflow: hidden;
    }
  }
  .viewer {
    height: 100%;
    display: inline-block;
    width: 50%;
  }

  .obv-panel {
    right: 0;
  }

  :deep(.arco-modal-body) {
    height: 100%;
    padding: 0;
  }
</style>

<style lang="less">
  .arco-modal-body {
    height: 100%;
    padding: 0;
  }
</style>
