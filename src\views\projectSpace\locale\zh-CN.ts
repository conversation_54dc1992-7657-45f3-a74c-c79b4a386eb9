export default {
  'design.lane-chart': '泳道图',
  'design.current-team-legend': '当前团队图例',
  'design.other-team-legend': '团队颜色对应每个团队图例',
  'design.show-current-team': '展示当前团队',
  'design.show-all-team': '展示所有团队',
  'design.show-more-team': '展示更多团队',
  'design.show-less-team': '展示更少团队',
  'design.close': '关闭',
  'design.information': '提示',
  'design.share': '共享',
  'design.share-name': '共享名称',
  'design.share-name-tip': '共享通过后在SHARED下文件夹的名称',
  'design.sharer': '共享人',
  'design.share-time': '共享时间',
  'design.share-file': '共享文件',
  'design.cache-share': '是否确认缓存当前共享包？',
  'design.cache-share-hint':
    '当前共享包中含有作废文件，作废文件将无法缓存至消费包中',
  'design.no-team': '没有可以展示的团队',
  'design.no-more-team': '没有更多团队',
  'design.confirm': '确定',
  'design.cancel': '取消',
  'design.save': '保存',
  'design.submit': '提交',
  'design.remove': '删除',
  'design.approve-workflow': '批准工作流',
  'design.please-choose': '请选择',
  'design.name': '名称',
  'design.recipients': '收件人',
  'design-recipients-tip': '共享审核全部通过后，会发送共享通知给收件人',
  'design.file-required': '需{name}文件',
  'design.file-counts': '总计：{count} 个文件',
  'design.add-file': '添加文件',
  'design.delivery': '交付',
  'design.milestone': '里程碑',
  'design.to-third-system': '是否交付工作包至第三方系统？',
  'design.yes': '是',
  'design.no': '否',
  'design.system-name': '系统名称',
  'design.data-asset': '数据资产',
  'design.project-information': '是否携带项目信息？',
  'design.please-enter': '请输入名称搜索',
  'design.project-name': '项目名称',
  'design.project-code': '项目编码',
  'design.project-date': '项目日期',
  'design.project-type': '项目类型',
  'design.project-description': '项目描述',
  'design.enter-name': '请输入名称',
  'design.select-process': '请选择流程',
  'design.select-milestone': '请选择里程碑',
  'design.select-recipient': '请选择收件人',
  'design.select-system': '请选择系统',
  'design.attention': '请注意',
  'design.fail-verification_share':
    '您所提交的内容中，部分文件校验标准不通过，请前往共享暂存区修改',
  'design.fail-verification_deliver':
    '您所提交的内容中，部分文件校验标准不通过，请前往交付暂存区修改',
  'design.to-modify': '前往修改',
  'design.select-least': '请至少选择一个文件',
  'design.succeeded': '已成功',
  'design.success-verification': '您所提交的文件已通过，请及时跟进流程状况',
  'design.in-examination-verification':
    '您所提交的文件正在校验中，请及时跟进流程状况',
  'design.highway-engineering': '公路工程',
  'design.urban-road-engineering': '城市道路工程',
  'design.airport-runway-engineering': '机场场道工程',
  'design.traffic-engineering': '城市轨道交通工程',
  'design.bridge-engineering': '桥梁工程',
  'design.culvert-engineering': '涵洞工程',
  'design.port-engineering': '港口工程',
  'design.navigation-engineering': '航道工程',
  'design.tunneling-engineering': '隧道工程',
  'design.building-engineering': '房建工程',
  'design.water-transport-engineering': '水运工程',
  'design.people': '人',
  'design.time': '时间',
  'design.process-state': '流程状态',
  'design.this-diagram-example': '本团队泳道图图例',
  'design.other-diagram-example': '其他团队泳道图图例',
  'design.logo-click': '(数字徽标单击/缩放可展开)',
  'design.dotted-circle': '本团队时间线上的虚线圆',
  'design.dotted-circle-explain': '表示已保存但尚未提交的新共享包',
  'design.light-circle': '本团队时间线上的浅色圆',
  'design.light-circle-explain': '表示已提交但尚未审核完成的新共享包',
  'design.solid-circle': '本团队时间线上的实心圆',
  'design.shared-package': '表示已共享的共享包',
  'design.dashed-square': '本团队时间线上的虚线方块',
  'design.dashed-square-explain': '表示已保存但尚未提交的新交付包',
  'design.light-square': '本团队时间线上的浅色方块',
  'design.light-square-explain': '表示已提交但尚未审核完成的新交付包',
  'design.solid-square': '本团队时间线上的实心方块',
  'design.published-package': '表示已发布的交付包',
  'design.solid-circle-with-number': '带数字符的实心圆',
  'design.group-shared-package': '代表一组已共享的共享包',
  'design.solid-drum': '带数字符的实心鼓形',
  'design.group-completed-package': '代表一组已完成的共享包和发布包',
  'design.hollow-circle': '其他团队时间线上的空心圆',
  'design.hollow-circle-explain': '表示一个共享包已经共享',
  'design.hollow-circle-explain2': '但本团队并未下载使用该资料包',
  'design.other-solid-circle': '其他团队时间线上的实心圆',
  'design.solid-circle-explain': '表示一个本团队已下载使用资料包',
  'design.other-solid-square': '其他团队时间线上的实心方块',
  'design.solid-square-explain': '表示已发布的交付包',
  'design.hollow-circle-with-num': '带数字符的空心圆',
  'design.hollow-circle-with-num-explain': '代表一组已共享的共享包',
  'design.hollow-circle-with-num-explain2': '但本团队并未下载使用',
  'design.solid-circle-with-num': '带数字符的实心圆',
  'design.solid-circle-with-num-explain': '代表一组已共享的共享包',
  'design.solid-circle-with-num-explain2': '且本团队已全部下载使用',
  'design.two-color-circle-with-num': '带数字符的双色圆',
  'design.two-color-circle-with-num-explain': '代表一组已共享的共享包',
  'design.two-color-circle-with-num-explain2': '本团队已部分下载使用',
  'design.solid-square-with-num': '带数字符的实心方块',
  'design.solid-square-with-num-explain': '代表一组已发布的交付包',
  'design.solid-drum-with-num': '带数字符的实心鼓形',
  'design.solid-drum-with-num-explain': '代表一组已完成的共享包和发布包',
  'design.two-color-drum-with-num': '带数字符的双色鼓形',
  'design.two-color-drum-with-num-explain':
    '代表一组包含未使用共享包与发布包组合',
  'design.index': '序号',
  'design.standard-name': '标准名称',
  'design.description': '说明',
  'design.version': '版本',
  'design.size': '大小',
  'design.operation': '操作',
  'design.file-list': '文件列表',
  'design.initiate-delivery': '交付',
  'design.download-source-file': '下载源文件',
  'design.no-permission': '没有权限',
  'design.to-current-team': '设为当前团队',
  'design.unfollow': '取消关注',
  'design.pay-attention': '关注',
  'design.reset': '重置',
  'design.reset-timeline': '重置时间轴',
  'design.shared-package-count': '{count}个共享包',
  'design.delivery-package-count': '{count}个交付包',
  'design.year': '{count}年',
  'design.month': '{count}月',
  'design.week': '{count}周',
  'design.deleteSuccessfully': '删除成功',
  'design.deleteFailed': '删除失败',
  'design-delete-tip': '是否确认删除?',
  'design.no-data-available': '暂无数据',
  'design.sync-push-to-jjt': '同步推送至交建通',
  'design.approver': '审批人',
  'design.recipient': '收件人',
  'file-manage.batch-download': '批量下载',
  'file-manage.wait-downloading': '文件下载中，请稍候',

  'task.share': '共享',
  'task.deliver': '交付',
  'task.share-name': '共享名称',
  'task.please-enter': '请输入名称搜索',
  'task.search': '查询',
  'task.clear': '清空',
  'task.share-list': '共享列表',
  'task.deliver-list': '交付列表',
  'task.review': '审核',
  'task.revocation': '撤销',
  'task.index': '序号',
  'task.task-name': '任务名称',
  'task.task-status': '任务状态',
  'task.process-templates': '流程模板',
  'task.process-status': '流程状态',
  'task.initiator': '发起人',
  'task.creation-time': '创建时间',
  'task.file-count': '文件数',
  'task.operation': '操作',
  'task.whether-revoke-share': '是否撤销该共享？',
  'task.deliver-name': '交付名称',
  'task.milestone': '里程碑',
  'task.whether-revoke-delivery': '是否撤销该交付？',

  'task.process-detail': '流程详情',
  'task.name': '名称',
  'task.file': '文件',
  'task.view-more': '查看更多',
  'task.pack-up': '收起',
  'task.processing-records': '处理记录',
  'task.node-name': '节点名称',
  'task.operator': '操作人',
  'task.approval-result': '审批结果',
  'task.approval-opinion': '审批意见',
  'task.approval-time': '审批时间',
  'task.reject': '拒绝',
  'task.agree': '同意',
  'task.determine': '确定',
  'task.please-enter-approval-opinion': '请输入审批意见！',
  'task.missing-form-configuration': '缺少表单配置！',
  'task.unable-get-form-details': '无法获取表单详情！',
  'task.fileToken-missing': '文件 fileToken 缺失，无法下载！',
  'task.upload-attachment': '上传附件',
  'task.attachment-info': '附件信息',
  'task.click-to-download-the-file': '点击下载文件',
  'task.delete-file-success': '删除文件成功',

  'task.upload': '上传',
  'task.download': '下载',
  'task.not-upload-same-file-twice': '相同文件请勿重复上传',
  'task.uploaded-files-counts-exceeds-limit': '上传文件的数量超出限制',
  'task.not-upload-empty-files': '请勿上传空文件',
  'task.upload-in': '上传中',
  'task.description': '说明',
  'task.disallowed-file-format': '不允许的文件格式',
  'task.uploaded-files-exceeds-limit': '上传文件的大小不能超过',
  'task.uploaded-files-quantity-exceeds-limit': '文件并发上传数量超出限制',
  'task.uploaded-files-types-exceeds-limit': '上传文件的类型超出限制',
  'task.please-check-and-upload again': '文件上传出错，请检查后重新上传',
  'task.quick-pass-successful': '极速秒传成功',
  'task.view': '查看',
  'task.share-audit': '共享审核',
  'task.deliver-audit': '交付审核',
  'task.no-data-available': '暂无数据',
};
