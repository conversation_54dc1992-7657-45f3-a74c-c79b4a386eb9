<template>
  <a-drawer
    v-if="visible"
    :width="460"
    :visible="visible"
    unmount-on-close
    @ok="handleBeforeOk"
    @cancel="handleBeforeCancel"
  >
    <template #title>
      {{ type === 'edit' ? data.panelName : $t('schedule.calendar.add') }}
    </template>
    <div>
      <a-spin style="width: 100%" :loading="loading">
        <a-form ref="formRef" :model="form" auto-label-width layout="vertical">
          <a-form-item
            field="panelName"
            :label="$t('schedule.calendar.name')"
            validate-trigger="input"
            :rules="[
              {
                required: true,
                message: $t('schedule.calendar.name.placeholder'),
              },
            ]"
          >
            <a-input
              v-model="form.panelName"
              :placeholder="$t('schedule.calendar.name.placeholder')"
            />
          </a-form-item>
          <a-form-item
            field="member"
            :label="$t('schedule.calendar.member')"
            validate-trigger="input"
            :rules="[
              {
                required: false,
                message: $t('schedule.calendar.member.placeholder'),
              },
            ]"
          >
            <a-input-tag
              v-model="form.member"
              :placeholder="$t('schedule.calendar.member.placeholder')"
              @focus="getUserFocus"
            />
          </a-form-item>
        </a-form> </a-spin
    ></div>
  </a-drawer>

  <select-members
    v-model:visible="selUserVisible"
    :data="memberData"
    @select-member="selectMember"
  ></select-members>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { ref, watch } from 'vue';
  import selectMembers from '@/components/selectMembers/index.vue';
  import { createSchedule, editSchedule } from '../api';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const emit = defineEmits(['update:visible', 'refresh']);

  const selUserVisible = ref(false);

  const loading: any = ref(false);

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    type: {
      type: String,
      required: false,
    },
  });

  const form = ref<any>({
    name: '',
    member: [],
  });

  const memberData = ref([]);

  // 获取选择成员
  const selectMember = (user: any) => {
    memberData.value = user;
    form.value.member = user?.map((item: any) => item.userFullname);
    memberData.value.forEach((item: any) => {
      item.username = item.userName;
    });
  };

  // 获取焦点时触发选人弹窗
  const getUserFocus = () => {
    selUserVisible.value = true;
  };

  const handleBeforeCancel = () => {
    emit('update:visible', false);
  };

  const formRef = ref(null);

  const handleBeforeOk = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      try {
        const syncUsers =
          props.type === 'edit'
            ? memberData.value.map((item: any) => item.username)
            : memberData.value.map((item: any) => item.userName);
        loading.value = true;
        const param: any = {
          panelName: form.value.panelName,
          syncUsers,
          companyId: '100000',
        };
        if (props.type === 'edit') {
          param.id = props.data.id;
          const creatRes = await editSchedule(param);
          if (creatRes.status) {
            Message.success(t('schedule.calendar.edit'));
            emit('update:visible', false);
            emit('refresh');
          }
        } else {
          const creatRes = await createSchedule(param);
          if (creatRes.status) {
            Message.success(t('schedule.calendar.create'));
            emit('update:visible', false);
            emit('refresh');
          }
        }
      } catch (err: any) {
        console.log(err);
      } finally {
        loading.value = false;
      }
    }
  };

  watch(
    () => props.visible,
    () => {
      form.value.panelName = props.data?.panelName;
      form.value.member = [];
      // 表单回显
      if (props.type === 'edit') {
        props?.data?.panelUsers.forEach((item: any) => {
          form.value.member.push(item.userFullname);
        });
        // 人员数据选人组件回显所需数据
        memberData.value = props?.data?.panelUsers;
        memberData.value.forEach((item: any) => {
          item.userName = item.username;
          // item.id = '1802882952816758786';
        });
      } else {
        form.value = {};
      }
    }
  );
</script>

<style lang="less" scoped>
  :deep(.arco-drawer-title) {
    font-size: 20px;
  }
  :deep(.arco-form-item-label),
  :deep(.arco-form-item-label) {
    font-size: 16px;
  }
  :deep(.arco-form-item-wrapper-col) {
    border: 1px solid #c9cdd4;
    background-color: #fff !important;
    border-radius: 8px;
  }
  :deep(.arco-input-wrapper) {
    background-color: #fff;
    border-radius: 8px;
  }
  :deep(.arco-tag) {
    border-radius: 8px;
  }
  :deep(.arco-input-tag) {
    border-radius: 8px;
    background-color: #fff !important;
  }
</style>
