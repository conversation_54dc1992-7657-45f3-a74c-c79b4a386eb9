<template>
  <!-- 审批申请主页面 -->
  <div class="approve">
    <a-card class="general-card">
      <!-- 搜索区域 -->
      <template #title>
        <a-row>
          <a-col :span="20">
            <div v-if="isAllowedUser">
              <span class="search-title">{{ $t('approve-apply.search') }}</span>
              <a-input
                v-model="searchParams.info"
                :style="{ width: '296px', marginLeft: '16px' }"
                :placeholder="$t('approve-apply.enterInfo')"
                allow-clear
                @search="fetchData"
                @press-enter="fetchData"
                @clear="fetchData"
              />
            </div>
          </a-col>
          <a-col :span="4" style="text-align: right">
            <a-space :size="8">
              <!-- 申请按钮 -->
              <a-button @click="showApplyModal">{{
                $t('approve-apply.apply')
              }}</a-button>
              <template v-if="isAllowedUser">
                <!-- 查询按钮 -->
                <a-button type="primary" @click="fetchData">{{
                  $t('approve-apply.query')
                }}</a-button>
                <!-- 清除按钮 -->
                <a-button type="outline" @click="reset">{{
                  $t('approve-apply.clear')
                }}</a-button>
              </template>
            </a-space>
          </a-col>
        </a-row>
      </template>
      <a-divider />
      <!-- 申请列表标题 -->
      <a-row style="margin-bottom: 16px">
        <a-col :span="17" class="card-title">
          <img src="@/assets/images/dashboard/<EMAIL>" />
          <span>{{ $t('approve-apply.applicationList') }}</span>
        </a-col>
      </a-row>
      <!-- 申请列表表格 -->
      <a-table
        v-table-height
        stripe
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="false"
        :scroll="scroll"
        :scrollbar="true"
        @page-change="onPageChange"
        @page-size-change="pageSizeChange"
      >
        <!-- 序号列 -->
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <!-- 名称列 -->
        <template #name="{ record }">
          <span style="color: rgb(var(--primary-6))">{{ record.name }}</span>
        </template>
        <!-- 申请内容列 -->
        <template #application="{ record }">
          <a-typography-paragraph>
            <li v-for="item in record.application" :key="item">{{ item }}</li>
          </a-typography-paragraph>
        </template>
        <!-- 申请应用通过列 -->
        <template #throughApplication="{ record }">
          <a-typography-paragraph>
            <li v-for="item in record.throughApplication" :key="item">{{
              item
            }}</li>
          </a-typography-paragraph>
        </template>
        <!-- 申请类型列 -->
        <template #applyType="{ record }">
          {{
            $t(
              record.applyType === 0
                ? 'approve-apply.newUser'
                : 'approve-apply.deferredUser'
            )
          }}
        </template>
        <!-- 操作列 -->
        <template #operation="{ record }">
          <div v-if="record.status !== 0">{{
            $t(
              record.status === 1
                ? 'approve-apply.approved'
                : 'approve-apply.rejected'
            )
          }}</div>
          <div v-else>
            <!-- 撤销按钮 -->
            <a-popconfirm
              :content="$t('approve-apply.quashConfirm')"
              position="left"
              @ok="quash(record, 'quash')"
              v-if="record.createBy === userName"
            >
              <a-button type="text" size="small" :status="'warning'">{{
                $t('approve-apply.quash')
              }}</a-button>
            </a-popconfirm>

            <!-- 权限判断包裹同意/拒绝按钮 -->
            <template v-if="isAllowedUser">
              <!-- 同意按钮 -->
              <a-popconfirm
                :content="$t('approve-apply.agreeConfirm')"
                position="left"
                @ok="agreeOrRefuse(record, 'agree')"
              >
                <a-button type="text" size="small">{{
                  $t('approve-apply.approve')
                }}</a-button>
              </a-popconfirm>
              <!-- 拒绝按钮 -->
              <a-popconfirm
                :content="$t('approve-apply.rejectConfirm')"
                position="left"
                @ok="agreeOrRefuse(record, 'reject')"
              >
                <a-button type="text" size="small" :status="'danger'">{{
                  $t('approve-apply.reject')
                }}</a-button>
              </a-popconfirm>
            </template>
          </div>
        </template>
      </a-table>
    </a-card>

    <!-- 申请弹窗组件 -->
    <ApplyApplicationModal
      ref="applyModal"
      :select-products="currentSelectProducts"
      :default-checked="currentAction === 'approve'"
      @ok="handleApplyOk"
      @cancel="handleApplyCancel"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive, watch } from 'vue';
  import useLoading from '@/hooks/loading';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import cloneDeep from 'lodash/cloneDeep';
  import {
    queryApproveList,
    approveApply,
    deleteApply,
    applyApplication,
  } from './api';
  import { useI18n } from 'vue-i18n';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import ApplyApplicationModal from './components/ApplyApplicationModal.vue';
  import { int } from '@braks/revue-draggable/dist/utils';

  const { t } = useI18n();

  // 表格配置
  type Column = TableColumnData & { checked?: true };
  const { loading, setLoading } = useLoading(true);
  const scroll = {
    y: 'calc(100vh - 250px)',
  };
  const userStore = useUserStore();
  const userName = computed(() => userStore.username);
  // 在顶部定义允许的用户列表常量
  const ALLOWED_USERS = [
    '2023000411',
    'L10004895',
    '2022020264',
    '2013168746',
    'wanglei336',
    'jiangjiarui',
    '2023000791',
    'zhoulei',
    'l10004895',
    '15354507512',
    '17856500949',
  ];

  // 添加计算属性
  const isAllowedUser = computed(() => ALLOWED_USERS.includes(userName.value));
  // 搜索参数
  const generateSearch = () => {
    return {
      info: '',
    };
  };
  const renderData = ref([]);
  const searchParams = ref(generateSearch());
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    total: 0,
  });

  // 表格列配置
  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('approve-apply.name'),
      dataIndex: 'name',
      align: 'center',
      width: 100,
    },
    {
      title: t('approve-apply.companyDepartment'),
      dataIndex: 'departent',
      align: 'center',
      width: 150,
    },
    {
      title: t('approve-apply.position'),
      dataIndex: 'post',
      align: 'center',
      width: 150,
    },
    {
      title: t('approve-apply.phoneNumber'),
      dataIndex: 'phone',
      align: 'center',
      width: 150,
    },
    {
      title: t('approve-apply.email'),
      dataIndex: 'email',
      align: 'center',
      width: 180,
    },
    {
      title: t('approve-apply.applyApplication'),
      dataIndex: 'application',
      align: 'left',
      slotName: 'application',
      width: 210,
    },
    {
      title: t('approve-apply.throughApplication'),
      dataIndex: 'throughApplication',
      align: 'left',
      slotName: 'throughApplication',
      width: 210,
    },
    {
      title: t('approve-apply.applyTime'),
      dataIndex: 'updateDate',
      slotName: 'updateDate',
      align: 'center',
      width: 140,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('approve-apply.applyType'),
      dataIndex: 'applyType',
      slotName: 'applyType',
      align: 'center',
      width: 70,
    },
    {
      title: isAllowedUser.value
        ? t('approve-apply.operation')
        : t('approve-apply.status'),
      dataIndex: 'operation',
      align: 'center',
      slotName: 'operation',
      width: 100,
    },
  ]);

  interface ApiResponse<T = any> {
    code: number;
    message: string;
    data: T;
    status: boolean;
  }

  // 产品代码到显示名称的映射
  const PRODUCT_MAP = {
    'cde': 'CDex BIM协同管理系统',
    '6000': '国产化基座系统',
    '6001': '国产化道路设计系统',
    '6003': '国产化桥梁设计系统',
    '6004': '国产化地质勘察建模系统',
    '6005': '国产化隧道设计系统',
    '6007': '国产化涵洞设计系统',
    '6008': '国产化交通工程设计系统',
    '2001': '道路设计系统',
    '2003': '桥梁设计系统',
    '2004': '地质勘察设计系统',
    '2005': '隧道设计系统',
    '2006': '景观设计系统',
    '2007': '涵洞设计系统',
    'design': '公路数字化方案设计系统',
    '公路工程BIM云平台': '公路工程BIM云平台',
    '7001': '高桩码头',
    '7002': '重力式沉箱码头',
    '7003': '内河船闸水工结构及配筋数字化设计系统',
    '7004': '港口陆域结构BIM设计系统'
  };

  /**
   * 将产品代码转换为显示名称
   * @param productCodes 产品代码字符串，以逗号分隔
   * @returns 按产品代码在PRODUCT_MAP中的顺序排序的产品名称数组
   */
  const convertProductCodesToNames = (productCodes: string): string[] => {
    if (!productCodes) return [];
    
    // 获取PRODUCT_MAP的键数组，用于确定顺序
    const productKeys = Object.keys(PRODUCT_MAP);
    
    // 先将产品代码分割为数组，并过滤掉不在PRODUCT_MAP中的key
    const codeArray = productCodes.split(',').filter(code => code in PRODUCT_MAP);
    
    // 按照PRODUCT_MAP中键的顺序进行排序
    codeArray.sort((a, b) => {
      const indexA = productKeys.indexOf(a);
      const indexB = productKeys.indexOf(b);
      
      // 如果两个键都在PRODUCT_MAP中，按照它们在PRODUCT_MAP中的顺序排序
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      }
      
      // 如果只有一个键在PRODUCT_MAP中，将存在的键排在前面
      if (indexA !== -1) return -1;
      if (indexB !== -1) return 1;
      
      // 如果两个键都不在PRODUCT_MAP中，按照原始字符串排序
      return a.localeCompare(b, 'zh-CN');
    });
    
    // 将排序后的产品代码转换为产品名称
    return codeArray.map(code => PRODUCT_MAP[code]);
  };

  /**
   * 获取申请列表数据
   */
  const fetchData = async () => {
    setLoading(true);
    try {
      const params = {
        queryValue: searchParams.value.info,
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
      };
      const { data } = await queryApproveList(params);
      
      // 更新分页信息
      pagination.total = data.total;
      
      // 处理申请内容显示
      renderData.value = data.list.map((item: any) => {
        // 创建新对象而不是直接修改原对象
        return {
          ...item,
          application: convertProductCodesToNames(item.product),
          throughApplication: convertProductCodesToNames(item.approvalProduct)
        };
      });
    } catch (err) {
      console.error('获取申请列表失败:', err);
      // Message.error(t('approve-apply.fetchDataFailed') || '获取数据失败');
      renderData.value = []; // 出错时清空数据
    } finally {
      setLoading(false);
    }
  };

  /**
   * 重置搜索条件
   */
  const reset = () => {
    searchParams.value.info = '';
    fetchData();
  };

  /**
   * 处理同意或拒绝申请
   * @param row 申请记录
   * @param type 类型：'agree' 或 'reject'
   */
  // const agreeOrRefuse = async (row: any, type: string) => {
  //   try {
  //     const res = await approveApply({
  //       id: row.id,
  //       status: type === 'agree' ? 1 : -1,
  //       approvalProduct: row.product,
  //     });
  //     console.log('response00', res);
  //     // const response = res.data as ApiResponse;
  //     if (res.status) {
  //       Message.success(res.message);
  //       fetchData();
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };

  /**
   * 处理页码变化
   * @param pageNo 页码
   */
  const onPageChange = (pageNo: number) => {
    pagination.current = pageNo;
    fetchData();
  };

  /**
   * 处理每页条数变化
   * @param pageSize 每页条数
   */
  const pageSizeChange = (pageSize: number): void => {
    pagination.pageSize = pageSize;
    fetchData();
  };

  // 初始化加载数据
  fetchData();

  // 监听列配置变化
  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );

  /**
   * 删除申请记录
   * @param id 申请ID
   */
  const quash = async (row: any, type: string) => {
    try {
      console.log('row', row);
      // const res = await deleteApply({
      //   applyId: row.id,
      // });
      const res = await deleteApply(row.id);
      console.log('response00', res);
      if (res.status) {
        Message.success(res.message);
        fetchData();
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 申请弹窗组件引用
  const applyModal = ref();
  // 当前操作类型：'apply'表示新增申请，'approve'表示审批
  const currentAction = ref('apply');
  // 当前选中的行数据
  const currentRow = ref<any>(null);
  // 当前传递给弹窗的产品列表
  const currentSelectProducts = ref('');

  /**
   * 显示申请弹窗
   */
  const showApplyModal = () => {
    currentAction.value = 'apply';
    currentSelectProducts.value = '';
    currentRow.value = null;
    applyModal.value.visible = true;
  };

  /**
   * 显示审批弹窗 - 审批申请
   * @param row 当前行数据
   */
  const showApproveModal = (row: any) => {
    currentAction.value = 'approve';
    currentRow.value = row;
    currentSelectProducts.value = row.product || '';
    applyModal.value.visible = true;
  };

  /**
   * 处理申请弹窗确认
   * @param selectedProducts 选中的产品字符串
   */
  const handleApplyOk = async (selectedProducts: string) => {
    try {
      if (currentAction.value === 'apply') {
        // 新增申请
        const params = {
          product: selectedProducts,
        };
        const res = await applyApplication(params);
        if (res.status) {
          Message.success(res.message || t('apply-application.applySuccess'));
        }
      } else if (currentAction.value === 'approve') {
        // 审批申请
        const params = {
          id: currentRow.value.id,
          status: 1, // 同意
          approvalProduct: selectedProducts, // 审批选择的产品
        };
        const res = await approveApply(params);
        if (res.status) {
          Message.success(res.message || t('approve-apply.approveSuccess'));
        }
      }
      // 刷新数据
      fetchData();
    } catch (error) {
      console.error(error);
      Message.error(t('apply-application.applyFailed'));
    } finally {
      applyModal.value.visible = false;
    }
  };

  /**
   * 处理申请弹窗取消
   */
  const handleApplyCancel = () => {
    applyModal.value.visible = false;
    currentAction.value = 'apply';
    currentRow.value = null;
    currentSelectProducts.value = '';
  };

  /**
   * 同意或拒绝申请
   * @param row 当前行数据
   * @param type 操作类型：'agree'同意，'reject'拒绝
   */
  const agreeOrRefuse = async (row: any, type: string) => {
    if (type === 'agree') {
      // 显示审批弹窗
      showApproveModal(row);
    } else {
      // 直接拒绝
      try {
        const params = {
          id: row.id,
          status: -1, // 拒绝
        };
        const res = await approveApply(params);
        if (res.status) {
          Message.success(res.message || t('approve-apply.rejectSuccess'));
          fetchData();
        }
      } catch (error) {
        console.error(error);
        Message.error(t('approve-apply.operationFailed'));
      }
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'Approve',
  };
</script>

<style scoped lang="less">
  .hidden {
    display: none;
  }
  .approve {
    padding: 10px 0 20px 20px;
    .general-card {
      height: 100%;
    }

    .card-title {
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
      img {
        position: relative;
        top: 3px;
        height: 20px;
      }
    }
    .search-title {
      height: 22px;
      font-size: 14px;
      font-weight: 400;
      color: #1d2129;
      line-height: 22px;
    }
  }

  .list-empty {
    display: flex;
    align-items: center;
    .empty-box {
      width: 100%;
      text-align: center;
      height: 400px;
      .text {
        width: 100%;
        text-align: center;
        margin-top: -80px;
      }
    }
  }

  :deep(.arco-table-tr) {
    cursor: pointer;
  }
  :deep(.arco-card-header) {
    height: auto;
    padding: 20px 20px 0px 20px;
    border: none;
  }
  :deep(.arco-card-bordered) {
    border: none;
  }
  :deep(.arco-scrollbar-thumb-bar) {
    width: 0;
  }
</style>
