/*
 Highcharts JS v11.1.0 (2023-06-09)

 Timeline series

 (c) 2010-2021 Highsoft AS
 Author: <PERSON>

 License: www.highcharts.com/license
*/
'use strict';
(function (b) {
  'object' === typeof module && module.exports
    ? ((b['default'] = b), (module.exports = b))
    : 'function' === typeof define && define.amd
    ? define('highcharts/modules/timeline', ['highcharts'], function (c) {
        b(c);
        b.Highcharts = c;
        return b;
      })
    : b('undefined' !== typeof Highcharts ? Highcharts : void 0);
})(function (b) {
  function c(b, k, c, t) {
    b.hasOwnProperty(k) ||
      ((b[k] = t.apply(null, c)),
      'function' === typeof CustomEvent &&
        window.dispatchEvent(
          new CustomEvent('HighchartsModuleLoaded', {
            detail: { path: k, module: b[k] },
          })
        ));
  }
  b = b ? b._modules : {};
  c(
    b,
    'Series/Timeline/TimelinePoint.js',
    [
      b['Core/Series/Point.js'],
      b['Core/Series/SeriesRegistry.js'],
      b['Core/Utilities.js'],
    ],
    function (b, c, r) {
      const {
          series: k,
          seriesTypes: {
            pie: {
              prototype: { pointClass: q },
            },
          },
        } = c,
        { defined: x, isNumber: l, merge: h, objectEach: u, pick: n } = r;
      class v extends k.prototype.pointClass {
        constructor() {
          super(...arguments);
          this.series = this.options = void 0;
        }
        alignConnector() {
          let b = this.series,
            g = this.connector,
            f = this.dataLabel,
            c = (this.dataLabel.options = h(
              b.options.dataLabels,
              this.options.dataLabels
            ));
          var a = this.series.chart,
            e = g.getBBox(),
            d = e.x + f.translateX;
          e = e.y + f.translateY;
          a.inverted
            ? (e -= f.options.connectorWidth / 2)
            : (d += f.options.connectorWidth / 2);
          a = a.isInsidePlot(d, e);
          g[a ? 'animate' : 'attr']({ d: this.getConnectorPath() });
          g.addClass(`highcharts-color-${this.colorIndex}`);
          b.chart.styledMode ||
            g.attr({
              'stroke': c.connectorColor || this.color,
              'stroke-width': c.connectorWidth,
              'opacity': f[x(f.newOpacity) ? 'newOpacity' : 'opacity'],
            });
        }
        drawConnector() {
          const b = this.series;
          this.connector ||
            (this.connector = b.chart.renderer
              .path(this.getConnectorPath())
              .attr({ zIndex: -1 })
              .add(this.dataLabel));
          this.series.chart.isInsidePlot(this.dataLabel.x, this.dataLabel.y) &&
            this.alignConnector();
        }
        getConnectorPath() {
          let b = this.series.chart,
            g = this.series.xAxis.len,
            f = b.inverted,
            c = f ? 'x2' : 'y2',
            a = this.dataLabel,
            e = a.targetPosition,
            d = {
              x1: this.plotX,
              y1: this.plotY,
              x2: this.plotX,
              y2: l(e.y) ? e.y : a.y,
            },
            m = (a.alignAttr || a)[c[0]] < this.series.yAxis.len / 2;
          f &&
            (d = {
              x1: this.plotY,
              y1: g - this.plotX,
              x2: e.x || a.x,
              y2: g - this.plotX,
            });
          m && (d[c] += a[f ? 'width' : 'height']);
          u(d, function (e, b) {
            d[b] -= (a.alignAttr || a)[b[0]];
          });
          return b.renderer.crispLine(
            [
              ['M', d.x1, d.y1],
              ['L', d.x2, d.y2],
            ],
            a.options.connectorWidth
          );
        }
        init() {
          const b = super.init.apply(this, arguments);
          b.name = n(b.name, 'Event');
          b.y = 1;
          return b;
        }
        isValid() {
          return null !== this.options.y;
        }
        setState() {
          const b = super.setState;
          this.isNull || b.apply(this, arguments);
        }
        setVisible(b, c) {
          const f = this.series;
          c = n(c, f.options.ignoreHiddenPoint);
          q.prototype.setVisible.call(this, b, !1);
          f.processData();
          c && f.chart.redraw();
        }
        applyOptions(c, g) {
          c = b.prototype.optionsToObject.call(this, c);
          this.userDLOptions = h(this.userDLOptions, c.dataLabels);
          return super.applyOptions(c, g);
        }
      }
      return v;
    }
  );
  c(b, 'Series/Timeline/TimelineSeriesDefaults.js', [], function () {
    '';
    return {
      colorByPoint: !0,
      stickyTracking: !1,
      ignoreHiddenPoint: !0,
      legendType: 'point',
      lineWidth: 4,
      tooltip: {
        headerFormat:
          '<span style="color:{point.color}">\u25cf</span> <span style="font-size: 0.8em"> {point.key}</span><br/>',
        pointFormat: '{point.description}',
      },
      states: { hover: { lineWidthPlus: 0 } },
      dataLabels: {
        enabled: !0,
        allowOverlap: !0,
        alternate: !0,
        backgroundColor: '#ffffff',
        borderWidth: 1,
        borderColor: '#999999',
        borderRadius: 3,
        color: '#333333',
        connectorWidth: 1,
        distance: 100,
        formatter: function () {
          let b;
          b = this.series.chart.styledMode
            ? '<span class="highcharts-color-' +
              this.point.colorIndex +
              '">\u25cf </span>'
            : '<span style="color:' + this.point.color + '">\u25cf </span>';
          return (b +=
            '<span class="highcharts-strong">' +
            (this.key || '') +
            '</span><br/>' +
            (this.point.label || ''));
        },
        style: { textOutline: 'none', fontWeight: 'normal', fontSize: '0.8em' },
        shadow: !1,
        verticalAlign: 'middle',
      },
      marker: {
        enabledThreshold: 0,
        symbol: 'square',
        radius: 6,
        lineWidth: 2,
        height: 15,
      },
      showInLegend: !1,
      colorKey: 'x',
      legendSymbol: 'rectangle',
    };
  });
  c(
    b,
    'Series/Timeline/TimelineSeries.js',
    [
      b['Core/Series/SeriesRegistry.js'],
      b['Core/Renderer/SVG/SVGElement.js'],
      b['Series/Timeline/TimelinePoint.js'],
      b['Series/Timeline/TimelineSeriesDefaults.js'],
      b['Core/Utilities.js'],
    ],
    function (b, c, r, t, w) {
      const {
          seriesTypes: { column: k, line: l },
        } = b,
        {
          addEvent: h,
          arrayMax: u,
          arrayMin: n,
          defined: v,
          extend: q,
          merge: g,
          pick: f,
        } = w;
      class p extends l {
        constructor() {
          super(...arguments);
          this.visibilityMap =
            this.userOptions =
            this.points =
            this.options =
            this.data =
              void 0;
        }
        alignDataLabel(a, e, b, c) {
          var d = this.chart.inverted,
            m = this.visibilityMap.filter(function (a) {
              return a;
            }),
            f = this.visiblePointsCount,
            g = m.indexOf(a);
          m = this.options.dataLabels;
          let k = a.userDLOptions || {};
          g = m.alternate ? (g && g !== f - 1 ? 2 : 1.5) : 1;
          let h;
          f = Math.floor(this.xAxis.len / f);
          let l = e.padding;
          a.visible &&
            ((h = Math.abs(k.x || a.options.dataLabels.x)),
            d
              ? ((d = 2 * (h - l) - a.itemHeight / 2),
                (d = {
                  width: d + 'px',
                  textOverflow:
                    ((e.width / d) * e.height) / 2 > f * g
                      ? 'ellipsis'
                      : 'none',
                }))
              : (d = { width: (k.width || m.width || f * g - 2 * l) + 'px' }),
            e.css(d),
            this.chart.styledMode || e.shadow(m.shadow));
          super.alignDataLabel.apply(this, arguments);
        }
        bindAxes() {
          const a = this;
          super.bindAxes.call(a);
          ['xAxis', 'yAxis'].forEach(function (b) {
            'xAxis' !== b ||
              a[b].userOptions.type ||
              (a[b].categories = a[b].hasNames = !0);
          });
        }
        distributeDL() {
          const a = this,
            b = a.options.dataLabels;
          let d = 1;
          if (b) {
            const e = b.distance || 0;
            a.points.forEach((c) => {
              c.options.dataLabels = g(
                {
                  [a.chart.inverted ? 'x' : 'y']: b.alternate && d % 2 ? -e : e,
                },
                c.userDLOptions
              );
              d++;
            });
          }
        }
        generatePoints() {
          const a = this;
          super.generatePoints.apply(a);
          a.points.forEach(function (b, d) {
            b.applyOptions({ x: a.xData[d] }, a.xData[d]);
          });
        }
        getVisibilityMap() {
          return (this.data.length ? this.data : this.userOptions.data).map(
            function (a) {
              return a && !1 !== a.visible && !a.isNull ? a : !1;
            }
          );
        }
        getXExtremes(a) {
          const b = this;
          a = a.filter(function (a, e) {
            return b.points[e].isValid() && b.points[e].visible;
          });
          return { min: n(a), max: u(a) };
        }
        init() {
          const a = this;
          super.init.apply(a, arguments);
          a.eventsToUnbind.push(
            h(a, 'afterTranslate', function () {
              let b,
                d = Number.MAX_VALUE;
              a.points.forEach(function (a) {
                a.isInside = a.isInside && a.visible;
                a.visible &&
                  !a.isNull &&
                  (v(b) && (d = Math.min(d, Math.abs(a.plotX - b))),
                  (b = a.plotX));
              });
              a.closestPointRangePx = d;
            })
          );
          a.eventsToUnbind.push(
            h(a, 'drawDataLabels', function () {
              a.distributeDL();
            })
          );
          a.eventsToUnbind.push(
            h(a, 'afterDrawDataLabels', function () {
              let b;
              a.points.forEach(function (a) {
                if ((b = a.dataLabel))
                  return (
                    (b.animate = function (a) {
                      this.targetPosition && (this.targetPosition = a);
                      return c.prototype.animate.apply(this, arguments);
                    }),
                    b.targetPosition || (b.targetPosition = {}),
                    a.drawConnector()
                  );
              });
            })
          );
          a.eventsToUnbind.push(
            h(a.chart, 'afterHideOverlappingLabel', function () {
              a.points.forEach(function (a) {
                a.connector &&
                  a.dataLabel &&
                  a.dataLabel.oldOpacity !== a.dataLabel.newOpacity &&
                  a.alignConnector();
              });
            })
          );
        }
        markerAttribs(a, b) {
          var d = this.options.marker;
          let c = a.marker || {},
            e = c.symbol || d.symbol,
            g = f(c.width, d.width, this.closestPointRangePx),
            k = f(c.height, d.height),
            h = 0;
          if (this.xAxis.dateTime) return super.markerAttribs.call(this, a, b);
          b &&
            ((d = d.states[b] || {}),
            (b = (c.states && c.states[b]) || {}),
            (h = f(b.radius, d.radius, h + (d.radiusPlus || 0))));
          a.hasImage = e && 0 === e.indexOf('url');
          a = {
            x: Math.floor(a.plotX) - g / 2 - h / 2,
            y: a.plotY - k / 2 - h / 2,
            width: g + h,
            height: k + h,
          };
          return this.chart.inverted
            ? {
                y: a.x && a.width && this.xAxis.len - a.x - a.width,
                x: a.y && a.y,
                width: a.height,
                height: a.width,
              }
            : a;
        }
        processData() {
          let a = 0,
            b;
          this.visibilityMap = this.getVisibilityMap();
          this.visibilityMap.forEach(function (b) {
            b && a++;
          });
          this.visiblePointsCount = a;
          for (b = 0; b < this.xData.length; b++) this.yData[b] = 1;
          super.processData.call(this, arguments);
        }
      }
      p.defaultOptions = g(l.defaultOptions, t);
      q(p.prototype, {
        drawTracker: k.prototype.drawTracker,
        pointClass: r,
        trackerGroups: ['markerGroup', 'dataLabelsGroup'],
      });
      b.registerSeriesType('timeline', p);
      ('');
      return p;
    }
  );
  c(b, 'masters/modules/timeline.src.js', [], function () {});
});
//# sourceMappingURL=timeline.js.map
