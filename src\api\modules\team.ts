import axios from 'axios';
import qs from 'query-string';
import type { UserState } from '@/store/modules/user/types';
import type { LoginData, LoginRes } from '@/views/login/types';

export interface LoginRes {
  access_token: string;
}

// 创建或者修改团队接口
export function createTeamApi(params: any) {
  return axios.post<LoginRes>('/sys-user/team/createOrEdit', params);
}

// 获取团队列表接口
export function searchTeamListApi(params: any) {
  return axios.post<LoginRes>('/sys-user/team/list', params);
}

// 获取团队用户列表接口
export function searchTeamUserListApi(params: any) {
  return axios.post<LoginRes>('/sys-user/team/member/page', params);
}

// 保存用户至团队接口
export function saveTeamUserApi(params: any) {
  return axios.post<LoginRes>('/sys-user/team/join', params);
}

// 团队移除成员接口
export function deleteTeamUserApi(data: any) {
  console.log("deleteTeamUserApi", data);
  return axios.delete<LoginRes>('/sys-user/team/remove',  { data });
}

// export function deleteTeamApi(data: any) {
//   return axios.post<LoginRes>('', { data });
// }

export function deleteTeamApi(id: string) {
  return axios({
    url: `/sys-user/team/delete`,
    method: 'get',
    params: {
      id,
    },
  });
}