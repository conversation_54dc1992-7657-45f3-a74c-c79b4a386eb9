<template>
  <div class="menu-layout" :style="{ '--menu-font-size': menuFontSize }">
    <div class="navbar-wrap">
      <NavBar />
    </div>
    <div class="content-wrap">
      <div class="left-wrap" :style="leftWrapStyle">
        <div class="left-top">
          <div class="menu-title">
            <div
              v-if="companyId && projectList?.length !== 0 && !isProjectTemplate"
              class="menu-model"
              @click="modeChange"
              >{{
                globalMode === 'work'
                  ? t('layout.switch.to.project')
                  : t('layout.switch.to.work')
              }}</div
            >
            <div v-else class="menu-text">{{ t('layout.navigation') }}</div>
          </div>

          <div class="menu-wrap">
            <Menu />
          </div>
        </div>

        <div class="left-footer" v-if="!isProjectTemplate">
          <a-divider></a-divider>
          <div class="footer-link-wrap">
            <div class="footer-item" @click="toIssue">
              <icon-question-circle :size="isEn ? '18' : '22'"  />
              <span class="footer-item-title">{{ t('menu.feedback.requirements') }}</span>
            </div>
            <div
              class="footer-item"
              :class="{ 'footer-item-active': isSetupActive }"
              @click="handleGoToSetup"
            >
              <icon-settings :size="isEn ? '18' : '22'" />
              <span class="footer-item-title">{{ t('menu.setup') }}</span>
            </div>
          </div>
        </div>
      </div>
      <div ref="contentArea" class="content-area" :style="contentAreaStyle">
        <div v-if="pageShowAI" class="button-a" @click="toggleAIChat">
          <img src="@/assets/images/ai.gif" alt="" />
          {{ t('layout.ai.assistant') }}
        </div>
        <router-view :key="$route.fullPath"></router-view>
      </div>
      <div v-if="pageShowAI && aiChatVisible" class="ai-chat-wrap">
        <div class="ai-iframe">
          <menuAi :agent-id="agentId"></menuAi>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, watch, ref, onMounted, nextTick } from 'vue';
  import NavBar from '@/components/navbar/index.vue';
  import Menu from '@/components/menu/index';
  import { useAppStore, useGlobalModeStore, useUserStore } from '@/store';
  import { getRatio } from '@/utils/common/setRem';
  import { useRouter, useRoute } from 'vue-router';
  import columnLine from '@/assets/images/dashboard/column-line.svg';
  // eslint-disable-next-line import/no-cycle
  import useAIChat from '@/hooks/aiChat';
  import { useI18n } from 'vue-i18n';
  import menuAi from '@/components/menuAi/index.vue';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';

  const { t, locale } = useI18n();

  const userStore = useUserStore();
  const companyId = computed(
    () => userStore.companyId || localStorage.getItem('work_companyId')
  );

  const agentId = ref('xz33285jrxhvfo4gqrto');

  const app = useAppStore();
  const router = useRouter();
  const route = useRoute();
  const aiChatVisible = computed(() => {
    console.log('app.AIChat: ', app.AIChat);
    return app.AIChat;
  });
  const menuSwitch = computed(() => {
    return app.menuSwitch;
  });

  const projectTemplateStore = computed(() => {
    return userStore.projectTemplate;
  });

  const pageShowAI = computed(() => {
    const showAI = router.currentRoute?.value.meta?.showAI;
    return !!showAI;
  });
  const { aiChatLink, genAILink } = useAIChat();

  genAILink('imhckjj08zfqok5qnwtn');
  const toggleAIChat = () => {
    if (route.path === '/schedule')
      agentId.value = 'imhckjj08zfqok5qnwtn'; // 日程
    else agentId.value = 'xz33285jrxhvfo4gqrto'; // 通用
    app.toggleAIChat(!aiChatVisible.value);
  };

  const MENU_WIDTH = '206px';
  const MENU_HIDDEN_WIDTH = '0px';

  const leftWrapStyle = ref({
    width: MENU_WIDTH,
  });
  const contentAreaStyle = ref({
    left: MENU_WIDTH,
    width: `calc(100% - ${MENU_WIDTH})`,
  });

  const userId = getUserId() || '';

  // 左侧菜单显隐设置
  const setMenuView = (isHideMenu: boolean) => {
    const menuWidth = isHideMenu ? MENU_HIDDEN_WIDTH : MENU_WIDTH;
    const contentLeft = isHideMenu ? MENU_HIDDEN_WIDTH : MENU_WIDTH;
    const contentWidth = isHideMenu ? '100%' : `calc(100% - ${MENU_WIDTH})`;

    leftWrapStyle.value.width = menuWidth;
    contentAreaStyle.value.left = contentLeft;
    contentAreaStyle.value.width = contentWidth;
  };

  watch(
    () => menuSwitch.value,
    () => {
      setMenuView(menuSwitch.value as boolean);
    },
    { immediate: true }
  );

  // const leftWrapWidth = 200;
  // const AIChatWidth = 520;
  // const contentArea = ref<HTMLElement | null>(null);
  // const setContentAreaWidthHandle = () => {
  //   if (!contentArea.value) return;
  //   if (aiChatVisible.value && pageShowAI.value) {
  //     contentArea.value.style.width = `calc(100% - ${leftWrapWidth}px - ${AIChatWidth}px)`;
  //   } else {
  //     contentArea.value.style.width = `calc(100% - ${leftWrapWidth}px)`;
  //   }
  // };
  // watch(
  //   () => aiChatVisible.value,
  //   () => {
  //     setContentAreaWidthHandle();
  //   }
  // );
  // watch(
  //   () => router.currentRoute.value.name,
  //   () => {
  //     setContentAreaWidthHandle();
  //   }
  // );
  // onMounted(() => {
  //   setContentAreaWidthHandle();
  //   // window.addEventListener('resize', setContentAreaWidthHandle);
  // });
  // onBeforeUnmount(() => {
  //   window.removeEventListener('resize', setContentAreaWidthHandle);
  // });

  // 判断当前是否在设置页面
  const isSetupActive = computed(() => {
    return route.path.startsWith('/setup');
  });

  // 修改跳转函数
  const handleGoToSetup = () => {
    router.push('/setup');
  };
  const toIssue = () => {
    window.open(
      'https://app.fusionpaas.com/lowcode/apps/desktop/external/eU3eaq'
    );
  };

  const globalModeStore = useGlobalModeStore();
  const globalMode = computed({
    get: () => {
      return globalModeStore.getGlobalMode;
    },
    set: (mode) => {
      globalModeStore.changeGlobalMode(mode);
    },
  });

  // 切换空间
  const modeChange = async () => {
    if(projectTemplateStore.value === '1'){
      userStore.setProjectTemplate('0');
    }

    // 关闭ai弹窗
    app.toggleAIChat(false);
    if (globalMode.value === 'work') {
      globalModeStore.changeGlobalMode('project');
      const pId: any = getLocalstorage(`last_project_${userId}`) || '';
      router.push({
        name: 'home',
        params: {
          projectId: pId,
        },
      });
    } else {
      globalModeStore.changeGlobalMode('work');
      router.push('/dashboard');
    }
  };

  const projectList = computed(() => userStore.projectList || []);
  if (!projectList.value?.length) globalModeStore.changeGlobalMode('work');

  const isEn = computed(() => locale.value === 'en-US');
  const menuFontSize = computed(() => (locale.value === 'en-US' ? '13px' : '16px'));
  defineExpose({ menuFontSize });

  const isProjectTemplate = computed(() => userStore.projectTemplate === '1');
</script>

<style scoped lang="less">
  @nav-size-height: 72px;
  @left-side-width: 206px;
  @ai-chat-width: 882px;
  // @ai-chat-width: 520px;
  @media screen and (max-width: 1440px) {
    .menu-layout {
      height: calc(100vh - 10px) !important;
      //border: 1px solid red;
    }
    .content-wrap {
      .left-wrap {
        position: fixed;
        top: 72px;
        left: 0px;
        height: calc(100vh - @nav-size-height - 20px) !important;
        z-index: 10;
        //border: 1px solid red;
      }
    }
  }
  :deep(.arco-divider-horizontal) {
    margin: 0px;
  }

  .button-a {
    width: 110px;
    height: 36px;
    border: 1px solid #d9d9d9;
    border-radius: 36px;
    background-color: #ffff;
    position: fixed;
    top: 92px;
    right: 20px;
    cursor: pointer;
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 16px;
    color: #4e5969;
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
  .menu-layout {
    /*overflow: hidden;*/
    //position: relative;
    background: #f5f6fb;
    height: 100vh;
    width: 100vw;
    min-width: 1440px;
    //min-height: 800px;
    //border: 1px solid black;
  }
  .navbar-wrap {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    width: 100%;
    height: @nav-size-height;
  }
  .content-wrap {
    //border: 1px solid black;
    position: relative;
    top: @nav-size-height;
    height: calc(100% - @nav-size-height);
    width: 100%;
    overflow: hidden;
    :deep(.arco-menu-item, .arco-menu-light .arco-menu-group-title) {
      background-color: initial !important;
      padding-left: 10px;
    }
    :deep(.arco-menu) {
      font-size: 16px !important;
    }
    :deep(.arco-menu-item) {
      width: 166px;
      line-height: 32px !important;
      height: 32px !important;
      margin-bottom: 13px;
      border-radius: 6px;
    }
    :deep(.arco-menu-title),
    :deep(.arco-menu-item-inner),
    .footer-item-title,
    :deep(.arco-menu-title span),
    :deep(.arco-menu-item-inner span) {
      font-size: var(--menu-font-size);
      font-weight: 500 !important;
      color: #4e5969;
      font-family: PingFang SC, PingFang SC;
    }

    :deep(.arco-menu-selected) {
      // background-color: #e8f2ff !important;
      background-color: #d2e6ff !important;
      color: #3366ff;
    }
    :deep(.arco-menu-selected .arco-icon) {
      color: rgb(var(--primary-6)) !important;
    }
    :deep(.arco-menu-inline-header) {
      background-color: initial;
      padding-left: 10px !important;
    }
    :deep(.arco-menu-inline .arco-menu-indent-list) {
      display: inline-block;
      width: 38px !important;
    }
    :deep(.arco-menu-inner) {
      padding: 0 20px;
      padding-left: 0px;
      overflow-x: hidden;
      .arco-menu-inline-header {
        display: flex;
        align-items: center;
        //background-color: transparent;
      }

      .arco-icon {
        &:not(.arco-icon-down) {
          color: var(--color-text-2);
          font-size: 22px;
        }
      }

      .arco-icon-down {
        //color: rgb(255 255 255 / 60%);
      }
    }
    //display: flex;
    //background-color: #c3c3c3;
    .left-wrap {
      background: #f5f6fb;
      width: @left-side-width;
      padding-left: 20px;
      height: 100%;
      display: flex;
      flex-direction: column;
      .left-top {
        flex: 1;
        .menu-wrap {
          height: calc(100vh - 240px);
          overflow: auto;
        }
      }
      //border: 1px solid red;
      .menu-title {
        // padding-left: 10px;
        font-family: Source Han Sans SC, Source Han Sans SC;
        font-weight: 500;
        margin: 18px 0 18px 0;
        div {
          height: 32px;
          line-height: 32px;
        }

        .menu-model {
          width: 166px;
          background-image: url('@/assets/images/menu-type.png');
          // color: #1d2129;
          color: #ffffff;
          font-size: 14px;
          text-align: center;
          background-size: 100% 100%;
          cursor: pointer;
        }
        .menu-text {
          font-size: 16px;
          padding-left: 10px;
        }
      }

      .left-footer {
        margin-right: 20px;
        margin-bottom: 16px;
        .footer-link-wrap {
          color: #4e5969;
          .footer-item {
            height: 32px;
            padding-left: 10px;
            // font-size: 16px;
            cursor: pointer;
            margin-top: 8px;
            line-height: 32px;
            display: flex;
            align-items: center;
            border-radius: 4px;
            transition: all 0.2s ease;
            span {
              display: inline-block;
              margin-left: 16px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
            }
            // &:hover {
            //   //background-color: #E8F2FF;
            //   background-color: var(--color-fill-2);
            //   color: rgb(var(--primary-6));
            // }
            &-active {
              background-color: #d2e6ff;
              color: #3366ff;
              :deep(svg) {
                // fill: rgb(var(--primary-6));
              }
            }
          }
        }
      }
    }
    .content-area {
      background: white;
      border-bottom-left-radius: 16px;
      border-top-left-radius: 16px;
      position: absolute;
      left: @left-side-width;
      top: 0px;
      height: 100%;
      // width: calc(100% - @left-side-width - @ai-chat-width);
      width: calc(100% - @left-side-width);
      overflow: auto;
      //border: 1px solid red;
    }
    :deep(.arco-input-wrapper) {
      background-color: #fff;
      border: 1px solid #c9cdd4 !important;
    }

    .ai-chat-wrap {
      width: @ai-chat-width;
      height: 100%;
      position: absolute;
      right: 0px;
      top: 0px;
      background: white;
      overflow: hidden;
      z-index: 999;
      //border: 1px solid red;
      .ai-iframe {
        border: 1px solid #e5e6eb;
        border-radius: 8px;
        height: calc(100% - 96px);
        overflow: hidden;
        box-shadow: 4px 4px 8px 4px rgba(140, 138, 138, 0.1),
          -4px -4px 8px 4px rgba(140, 138, 138, 0.1);
        position: absolute;
        top: 60px;
        right: 20px;
        width: 842px;
        height: calc(100% - 80px);
      }
    }
  }
  :deep(.arco-menu-light) {
    background: #f5f6fb;
  }
</style>
