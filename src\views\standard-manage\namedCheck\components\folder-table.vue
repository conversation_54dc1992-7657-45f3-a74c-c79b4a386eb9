<template>
  <a-spin ref="spin" :loading="tableLoading" class="folder-table-panel">
    <a-table
      ref="folderTable"
      :columns="columns"
      :data="tableData"
      :scroll="{ x: 1600, y: 'calc(100% - 86px)' }"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      :bordered="false"
      row-key="id"
      :pagination="{
        showTotal: true,
        showPageSize: true,
        showJumper: true,
        defaultPageSize: 10,
        pageSizeOptions: [10, 20, 50, 100],
      }"
      @selection-change="selectionChange"
    >
      <template #name="{ record }">
        <div class="table-name">
          <file-image
            :file-name="record.name"
            :is-sysFile="isSysFolder(record.sysType)"
            :is-file="!!record.folderId"
            style="margin-right: 8px"
          />
          <a-tooltip :content="record.name">
            <span
              style="
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
              @click="clickNameHandle(record)"
              >{{ i18FolderName(record) }}</span
            >
          </a-tooltip>
        </div>
      </template>
      <template #fileStandardName="{ record }">
        <a-space>
          <div v-if="!record.showInput">{{ record.fileStandardName }}</div>
          <!-- <div
            v-if="record.type === 'WIP' && record.folderId"
            class="edit-icon"
            @click="record.showInput = true"
            @mouseover="record.showIcon = true"
            @mouseout="record.showIcon = false"
          >
            <icon-pen />
          </div> -->
          <a-input
            ref="renameInput"
            v-else
            v-model="record.fileStandardName"
            @blur="handleRenameEvents"
            @keydown.enter="handleRenameEvents"
          />
        </a-space>
      </template>
      <template #fileCheckStatus="{ record }">
        <!-- <div
          v-if="record.folderId"
          :style="handleCheckColor(record.fileCheckStatus)"
          >{{ handleCheckStatus(record.fileCheckStatus) }}</div
        >
        <div v-else></div> -->

        <ul class="custom-list">
          <li
            v-show="record.folderId"
            :style="handleCheckColor(record.fileCheckStatus)"
            >{{ handleCheckStatus(record.fileCheckStatus) }}</li
          >
        </ul>
      </template>

      <template #standardName="{ record }">
        <a-tooltip :content="record.standardName">
          <a-tag
            v-if="record?.standardName"
            nowrap
            style="
              border-radius: 2px;
              background-color: #e8fffb;
              color: #0fc6c2;
            "
          >
            {{ record.standardName }}
          </a-tag>
        </a-tooltip>
      </template>

      <template #updateDate="{ record }">
        {{ record.updateDate || '' }}
      </template>
      <template #optional="{ record }">
        <a-button
          :disabled="
            record.parentId === 0 ||
            isSysFolder(record.sysType) ||
            [1, 2, 3, 4].includes(hiddenSlot)
          "
          type="text"
          size="small"
          @click="handleRename(record)"
        >
          {{ $t('standard-named.rename') }}
        </a-button>
        <a-button
          :disabled="
            record.parentId === 0 ||
            isSysFolder(record.sysType) ||
            [1, 2, 3, 4].includes(hiddenSlot)
          "
          type="text"
          size="small"
          @click="handleBind(record)"
        >
          {{ $t('standard-named.binding-standard') }}
        </a-button>
        <a-popconfirm
          :content="$t('standard-named.should-verification-initiated')"
          @ok="handleTest(record)"
        >
          <a-button
            :disabled="
              (record.folderId && !record.fileStandardBindId) ||
              record.parentId === 0 ||
              isSysFolder(record.sysType)
            "
            type="text"
            size="small"
            >{{ $t('standard-named.initiate-verification') }}
          </a-button>
        </a-popconfirm>
      </template>
    </a-table>
  </a-spin>
</template>

<script setup lang="ts">
  import { defineEmits, watch, ref, computed, toRaw, nextTick } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { FileMessage, FolderMessage, getFileList } from '@/api/tree-folder';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';

  import useFileStore from '@/store/modules/file/index';
  import { storeToRefs } from 'pinia';

  import {
    isSysFolder,
    isTopFolder,
    isWpsFile,
  } from '@/views/projectSpace/file/utils';
  import { getNameStandardList, renameStandard } from '../api';
  import { Message } from '@arco-design/web-vue';
  import { wpsViewHandle } from '@/hooks/wps';

  import modelViewBim from '@/utils/common/view';
  import { useRoute } from 'vue-router';
  import { useThrottleFn } from '@vueuse/core';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';

  const { t } = useI18n();
  const route = useRoute();

  const fileStore = useFileStore();
  const { i18FolderName } = useI18nHandleName();

  const currentFolder = computed(() => fileStore.currentFolder);
  const { hiddenSlot, tableLoading } = storeToRefs(fileStore);

  const emits = defineEmits(['expendFolder', 'handleBind', 'handleTest']);

  const columns = computed(() => {
    return [
      {
        title: t('standard-named.name'),
        dataIndex: 'name',
        slotName: 'name',
        sortable: {
          sortDirections: ['ascend', 'descend'],
        },
        width: 240,
        fixed: 'left',
        align: 'left',
      },
      {
        title: t('standard-named.standrad-name'),
        width: 120,
        dataIndex: 'fileStandardName',
        slotName: 'fileStandardName',
        align: 'left',
      },
      {
        title: t('standard-named.check-result'),
        width: 100,
        dataIndex: 'fileCheckStatus',
        slotName: 'fileCheckStatus',
        align: 'left',
      },
      {
        title: t('standard-named.binding-standard'),
        dataIndex: 'standardName',
        slotName: 'standardName',
        align: 'left',
        width: 180,
      },
      {
        title: t('standard-named.time'),
        dataIndex: 'standardCheckTime',
        slotName: 'standardCheckTime',
        align: 'left',
        width: 140,
      },
      {
        title: t('file-manage.operation'),
        slotName: 'optional',
        titleSlotName: 'optionalTitle',
        width: 120,
        fixed: 'right',
        align: 'left',
      },
    ];
  });

  const fileList = ref([]);
  const folderList = ref<FolderMessage[]>([]);

  const tableData = computed(() => fileStore.tableData);

  const projectId = route.params.projectId as string;
  async function getCheckList() {
    const params = {
      projectid: projectId,
      folderId: currentFolder.value.id,
      pageNo: 1,
      pageSize: 9999,
    };
    return getNameStandardList(params);
  }

  function isEditableFile(record: FileMessage) {
    return record.type === 'WIP' && !record.isLocked && isWpsFile(record);
  }

  async function clickNameHandle(record: any) {
    if (!record.folderId) {
      // 文件夹点击事件，进入下一层
      emits('expendFolder', record);
      return;
    }
    if (isEditableFile(record)) {
      // 编辑
      wpsViewHandle(record, 'edit', 'admin');
    } else {
      // 文件点击事件，查看文件
      let needParams: any = {
        noIssue: [1, 2, 3, 4].includes(hiddenSlot.value),
      };
      if (record.isCombination === 2) {
        const params = {
          type: 'collision',
          engine: 0,
          modelNumber: record.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
        };
        needParams = { ...params, ...needParams };
      }
      modelViewBim(record, route.params.projectId as string, needParams);
    }
  }

  const getFiles = async () => {
    if (!isTopFolder(currentFolder.value.id)) {
      const res: any = await getFileList(currentFolder.value.id!);
      const checkRes = await getCheckList();
      if (res.status) {
        const list = res?.data?.list;
        const checkList = checkRes.data.list;
        fileList.value = list?.length
          ? list.map((item: any) => {
              if (checkList.length) {
                const checkData = checkList.find(
                  (_it) => _it.fileId === item.id
                );
                if (checkData) {
                  return {
                    ...item,
                    ...checkData,
                    fileStandardName: item.standardName,
                    showInput: false,
                  };
                }
              }
              return {
                ...item,
                fileStandardName: item.standardName,
                standardName: '',
                showInput: false,
              };
            })
          : [];
      }
    }
  };

  function selectionChange(rowkeys: string[]) {
    fileStore.setSelectedTableRowkeys(rowkeys);
  }

  const folderTable = ref();
  async function refreshTableData(folder?: FolderMessage) {
    tableLoading.value = true;
    folderList.value =
      (folder ? folder.children : currentFolder.value.children) || [];
    fileList.value = [];

    await getFiles();

    const folders = toRaw(folderList.value);
    const files = toRaw(fileList.value);

    fileStore.setTableData([
      ...folders.map((item) => {
        return item;
      }),
      ...files,
    ]);

    folderTable?.value.selectAll(false);
    fileStore.setSelectedTableRowkeys([]);
    tableLoading.value = false;
  }

  function handleCheckStatus(status: number) {
    switch (status) {
      case 0:
        return t('standard-named.check-fail');
      case 1:
        return t('standard-named.check-success');
      default:
        return t('standard-named.no-check');
    }
  }
  function handleCheckColor(status: number | null) {
    switch (status) {
      case 0:
        return 'color: #f53f3f';
      case 1:
        return 'color: #00b42a';
      default:
        return 'color: #ff7d00';
    }
  }

  const renameInput = ref(null);
  let lastName = '';

  const currentRecord = ref<any>(null);
  const handleRename = useThrottleFn(async function (record: any) {
    if (currentRecord.value) {
      currentRecord.value.showInput = false;
    }
    record.showInput = true;
    currentRecord.value = record;
    nextTick(() => {
      renameInput.value?.focus();
      lastName = record.fileStandardName;
    });
  }, 500);

  const handleRenameEvents = useThrottleFn(async function () {
    if (currentRecord.value!.fileStandardName === lastName) {
      currentRecord.value.showInput = false;
      return;
    }
    const data = {
      id: currentRecord.value!.id,
      standardName: currentRecord.value!.fileStandardName,
      type: 1,
    };
    if (!currentRecord.value!.folderId) {
      data.type = 2;
    }
    const res = await renameStandard(data);
    Message.success(t('standard-named.update-success'));
    lastName = '';
    currentRecord.value!.showInput = false;
  }, 500);

  function handleBind(record: any) {
    emits('handleBind', record);
  }

  function handleTest(record: any) {
    emits('handleTest', record);
  }
  watch(
    () => currentFolder.value,
    async (folder: any) => {
      // 更新当前文件夹的系统类型
      if (folder && folder.sysType) {
        hiddenSlot.value = Math.min(Math.max(folder.sysType, 1), 4) || 5; // 确保值在 1 到 4 之间，否则默认为 5
      } else {
        hiddenSlot.value = 5;
      }
      if (!folder.id) return;
      await refreshTableData(folder);
    },
    {
      immediate: true,
      deep: true,
    }
  );
  defineExpose({ refreshTableData });
</script>

<style scoped lang="less">
  .folder-table-panel {
    width: 100%;
    height: 100%;
    padding: 0 20px;
  }
  .table-name {
    display: flex;
    align-items: center;
    color: rgb(22, 93, 255);
    cursor: pointer;
  }
  :deep(.arco-btn-size-small) {
    padding: 0 0;
    margin-right: 8px;
    font-size: 13px;
  }
  :deep(.arco-table-container) {
    height: calc(100% - 40px);
  }
  :deep(.arco-table-content .arco-scrollbar:nth-child(2)) {
    height: 100%;
  }
  :deep(.arco-table-header + .arco-scrollbar-track-direction-horizontal) {
    display: none;
  }

  .custom-list {
    list-style: none; /* 隐藏默认标记 */
    padding-left: 0; /* 移除默认内边距 */
  }

  .custom-list li {
    position: relative;
    padding-left: 12px; /* 为自定义标记留出空间 */
  }

  .custom-list li::before {
    content: '•'; /* 自定义标记符号（可以是文字、emoji或图标） */
    position: absolute;
    left: 0; /* 调整标记与文本的间距 */
  }
</style>
