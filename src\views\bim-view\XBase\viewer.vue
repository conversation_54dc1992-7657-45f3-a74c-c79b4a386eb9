<template>
  <div id="Xbase-viewer">
    <!-- 属性面板容器 -->
    <div id="properties-panel">
      <PropertiesPanel v-show="propertiesPanelShow"> </PropertiesPanel>
    </div>
  </div>
  <!-- 模型平移所需点位 -->
  <div id="point"></div>
  <!-- 构件树容器 -->
  <div v-show="modalTreeIsShow" class="tree-container"></div>
</template>

<script setup>
  import {
    ref,
    defineProps,
    defineEmits,
    onBeforeUnmount,
    onMounted,
  } from 'vue';
  import genXBaseOptions from '@/utils/BIM-Engine/XBase/utils/gen-xbase-options';
  import { parseViewerType } from '@/utils/BIM-Engine/XBase/utils/index';
  import XBaseEngine from '@/utils/BIM-Engine/XBase';
  import { storeToRefs } from 'pinia';
  import useModelToolsStore from '@/store/modules/model-viewer/index';
  import { useRoute } from 'vue-router';
  import ViewerAPI from '@/utils/BIM-Engine/XBase/utils/viewer-api';
  import PropertiesPanel from '@/utils/BIM-Engine/XBase/viewer/properties-panel/index.vue';
  import { GetPropertiesPanel } from '@/views/bim-view/api';
  import { Base64 } from 'js-base64';
  import {
    genPropertiesOptions,
    handleEntitySelected,
  } from '@/utils/BIM-Engine/XBase/viewer/properties-panel/properties-panel';

  const store = useModelToolsStore();
  const { modalTreeIsShow, propertiesPanelShow } = storeToRefs(store);
  const route = useRoute();
  const { type } = route.query;

  const emits = defineEmits([
    'afterRender',
    'getXBaseInstance',
    'getViewerAPIInstance',
  ]);
  const props = defineProps({
    modelFile: {
      type: Object,
      default() {
        return {};
      },
    },
    elementId: {
      type: String,
      default: '',
    },
    viewerType: {
      type: String,
      default: '',
    },
    guids: {
      type: Array,
      default() {
        return [];
      },
    },
  });
  let graphicEngineInfoData = {}; // 预览工具栏所需模型数据
  let renderPathNew = ''; // 模型渲染path
  const { graphicEngineInfo, name } = props.modelFile;
  const arr = graphicEngineInfo.split('|');
  const [fileId, renderPath, semanticModel] = arr;

  // 获取预览path
  if (type === 'collision') {
    // 碰撞检测预览
    graphicEngineInfoData = JSON.parse(graphicEngineInfo);
    const { modelInfo } = graphicEngineInfoData;
    renderPathNew = graphicEngineInfoData.renderPath || modelInfo.render_path;
  } else {
    // 其他预览
    renderPathNew = renderPath;
  }

  const lastThreeChars = name.slice(-3); // 模型后缀名
  localStorage.setItem('model_type', lastThreeChars);

  let XBaseViewer;
  const init = async () => {
    const options = await genXBaseOptions(
      props.modelFile,
      props.viewerType,
      type
    );
    options.elementId = props?.elementId;
    const XBaseEngineInstance = new XBaseEngine.viewer.BaseViewer(
      props.elementId || 'Xbase-viewer'
    );
    const ViewerTreeWithXBaseEngine = new XBaseEngine.viewer.TreeViewer(
      renderPath
    );
    emits('getXBaseInstance', XBaseEngineInstance, props.modelFile);
    XBaseViewer = await XBaseEngineInstance.render(options);
    XBaseViewer.createCustomButton({
      key: 'custom_attribute',
      hasActive: false,
      title: '属性',
      update: false,
      className: 'cusPlugin',
      handle: () => {
        store.setPropertiesPanelShow(!store.propertiesPanelShow);
      },
    });
    await ViewerTreeWithXBaseEngine.render(XBaseEngineInstance, renderPath);
    await XBaseEngineInstance.openFile(renderPathNew);
    const ViewerAPIInstance = new ViewerAPI(XBaseEngineInstance.getViewer());
    emits('getViewerAPIInstance', ViewerAPIInstance, props.modelFile);
    genPropertiesOptions(
      XBaseViewer,
      renderPathNew,
      route.query.projectId,
      XBaseEngineInstance
    );
  };

  onMounted(() => {
    init();
  });

  onBeforeUnmount(() => {
    XBaseViewer.destroy();
  });

  defineExpose({
    handleEntitySelected,
  });
</script>

<style scoped lang="less">
  #Xbase-viewer {
    height: 100%;
    width: 100%;
  }
  #point {
    width: 6px;
    height: 6px;
    background-color: #3e7de4;
    border-radius: 5px;
    display: block;
    z-index: 11;
    position: absolute;
  }

  // 自定义按钮样式
  :deep(.cusPlugin) {
    background-image: url('@/assets/images/model-view/attribute.svg?url') !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: 1.5rem !important;
  }
  :deep(.cusPlugin_click) {
    background-image: url('@/assets/images/model-view/attribute-click.svg?url') !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: 1.5rem !important;
  }
</style>
