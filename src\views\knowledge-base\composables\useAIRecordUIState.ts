import { ref } from 'vue';

export default function useAIRecordUIState() {
  // 存储AI对话显示状态
  const recordUIState = ref(
    new Map<
      string,
      {
        isEditing: boolean;
        isDeleting: boolean;
        showOptions: boolean;
        oldName: string;
      }
    >()
  );

  // 获取AI对话是否显示操作按钮状态
  const getRecordUIState = (id: string) => {
    if (!recordUIState.value.has(id)) {
      recordUIState.value.set(id, {
        isEditing: false,
        isDeleting: false,
        showOptions: false,
        oldName: '',
      });
    }
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return recordUIState.value.get(id)!;
  };

  // 清空AI对话是否显示操作按钮状态
  const clearRecordUIState = () => {
    recordUIState.value.clear();
  };

  return {
    getRecordUIState,
    clearRecordUIState,
  };
}
