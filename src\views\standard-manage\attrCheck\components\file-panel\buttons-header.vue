<template>
  <div class="button-box">
    <a-space>
      <a-button
        :disabled="multiBindDisabled"
        type="primary"
        style="gap: 4px"
        @click="multiBind"
      >
        <MultiBindIcon />
        {{ $t('standard-attribute.batch-binding') }}
      </a-button>
      <a-popconfirm
        :content="$t('standard-attribute.should-verification-initiated')"
        position="right"
        @ok="multiCheck"
      >
        <a-button
          :disabled="multiCheckDisabled"
          type="outline"
          style="gap: 4px"
        >
          <MultiCheckIcon class="multi-check-icon" />
          {{ $t('standard-attribute.batch-check') }}
        </a-button>
      </a-popconfirm>
    </a-space>
  </div>
  <initiate-validation
    v-if="validateVisible"
    v-model:visible="validateVisible"
    :type="bindType"
    :multi-ids="multiIds"
    @refresh="refreshTableData"
  ></initiate-validation>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive } from 'vue';
  import InitiateValidation from './initiate-validation.vue';
  import { storeToRefs } from 'pinia';
  import useFileStore from '@/store/modules/file/index';
  import { startCheckByFilesAndFolders } from '../../api';
  import { Message } from '@arco-design/web-vue';
  import { isSysFolder } from '@/views/projectSpace/file/utils';
  import MultiBindIcon from '@/assets/images/standard/checkbox-multiple-line.svg';
  import MultiCheckIcon from '@/assets/images/standard/file-shield-2-line.svg';
  import { useI18n } from 'vue-i18n';

  const props = defineProps({
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const { t } = useI18n();

  const emits = defineEmits(['expendFolder']);

  const fileStore = useFileStore();
  const { tableData, selectedTableRowkeys, currentFolder, hiddenSlot } =
    storeToRefs(fileStore);

  const checkedTableRows = computed(() =>
    tableData.value.filter(
      (row) =>
        selectedTableRowkeys.value.findIndex((key) => key === row.id) !== -1
    )
  );

  const validateVisible = ref(false);

  const bindType = ref(''); // 绑定类型

  const multiIds = reactive<any>({
    fileIds: [],
    folderIds: [],
  });

  const multiBindDisabled = computed(() => {
    const noSelection = selectedTableRowkeys.value.length === 0;
    const hasTopLevelItem = checkedTableRows.value.some(
      (row: any) =>
        row.parentId === 0 ||
        isSysFolder(row.sysType) ||
        [1, 2, 3, 4].includes(hiddenSlot.value)
    );

    return noSelection || hasTopLevelItem;
  });

  const multiCheckDisabled = computed(() => {
    const noSelection = selectedTableRowkeys.value.length === 0;
    const hasTopLevelItem = checkedTableRows.value.some(
      (row: any) =>
        row.parentId === 0 ||
        isSysFolder(row.sysType) ||
        [1, 2, 3, 4].includes(hiddenSlot.value)
    );

    return noSelection || hasTopLevelItem;
  });

  /**
   * 根据传入的文件/文件夹 ID 数组判断绑定类型
   */
  const getBindType = (data: any) => {
    const hasFiles = data.fileIds.length > 0;
    const hasFolders = data.folderIds.length > 0;

    const typeMap: any = {
      '10': 'multiFiles',
      '01': 'multiFolders',
      '11': 'multiFilesAndFolders',
    };

    return typeMap[`${hasFiles ? 1 : 0}${hasFolders ? 1 : 0}`] || '';
  };

  // 处理multiIds数据
  const handleMultiIds = () => {
    Object.assign(multiIds, {
      fileIds: [],
      folderIds: [],
    });
    checkedTableRows.value.forEach((row: any) => {
      // 批量绑定时将文件和文件夹的id分别都存起来
      if (row.folderId) {
        multiIds.fileIds.push(row.id);
      } else {
        multiIds.folderIds.push(row.id);
      }
    });
  };

  // 批量绑定
  const multiBind = () => {
    console.log('[ selectedTableRowkeys ] >', selectedTableRowkeys.value);

    validateVisible.value = true;
    handleMultiIds();
    bindType.value = getBindType(multiIds);
  };

  const refreshTableData = () => {
    emits('expendFolder', currentFolder.value);
  };

  // 批量校验
  const multiCheck = () => {
    handleMultiIds();
    try {
      const params = {
        fileIds: multiIds.fileIds,
        folderIds: multiIds.folderIds,
      };
      startCheckByFilesAndFolders(params).then((res) => {
        if (res.status) {
          Message.success(t('standard-attribute.success'));
          refreshTableData();
        }
      });
    } catch (error) {
      console.log(error);
    }
  };

  const multiCheckIconColor = computed(() => {
    return multiCheckDisabled.value ? '#94BFFF' : '#165DFF';
  });
</script>

<style scoped lang="less">
  :deep(.multi-check-icon path) {
    fill: v-bind(multiCheckIconColor); /* 直接强制覆盖路径颜色 */
  }
</style>
