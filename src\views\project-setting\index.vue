<template>
  <div class="project-setting-container">
    <commonTabs
      v-model="tabKey"
      :tabs="tabsData"
      @click-tab="changeTab"
    ></commonTabs>

    <div class="content-wrapper">
      <ProjectNew v-show="tabKey === 'project'" />
      <project-plan v-show="tabKey === 'project-plan'" />
      <Process v-show="tabKey === 'process-settings'" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onBeforeMount, ref, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import commonTabs from '@/components/common-tabs/index.vue';
  import ProjectPlan from './plan/index.vue';
  import ProjectNew from './projectNew/index.vue';
  import Process from './process/index.vue';
  import { useI18n } from 'vue-i18n';
  import { useUserStore } from '@/store';

  const route = useRoute();
  const { tab } = route.query;

  const userStore = useUserStore();
  const isProjectTemplate = computed(() => userStore.projectTemplate === '1');

  const getTabKey = () => {
    if (isProjectTemplate.value) return 'process-settings';
    if (tab === 'project-plan') return 'project-plan';
    return 'project';
  };

  const tabKey = ref(getTabKey());

  const { t } = useI18n();

  const tabsData = computed(() =>
    isProjectTemplate.value
      ? []
      : [
          {
            label: t('project-setting.project'),
            value: 'project',
          },
          {
            label: t('project-setting.project-plan'),
            value: 'project-plan',
          },
          {
            label: t('project-setting.process-settings'),
            value: 'process-settings',
          },
        ]
  );

  const changeTab = (key: string) => {
    if (!isProjectTemplate.value) {
      tabKey.value = key;
      sessionStorage.setItem('activeTab', key);
    }
  };
  onBeforeMount(() => {
    // 移除不必要的console.log
  });
</script>

<style scoped lang="less">
  .project-setting-container {
    height: 100%;
    padding: 16px 20px;
    background-color: #fff;

    .content-wrapper {
      overflow: hidden;
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      margin-top: -1px;
      padding: 16px;
      height: calc(100% - 52px);
    }
  }
</style>
