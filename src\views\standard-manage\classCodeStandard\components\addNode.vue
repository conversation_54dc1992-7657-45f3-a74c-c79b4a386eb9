-<template>
  <div>
    <a-modal
      v-if="visible"
      :visible="visible"
      :title="
        type === 'edit' ? t('list.options.btn.edit') : t('list.options.btn.new')
      "
      :unmount-on-close="true"
      :mask-closable="false"
      width="600px"
      title-align="start"
      @cancel="handleBeforeCancel"
      @ok="handleBeforeOk"
    >
      <a-spin style="width: 100%" :loading="loading">
        <a-form ref="formRef" :model="form" auto-label-width layout="vertical">
          <a-form-item
            field="parentId"
            :rules="[
              {
                required: true,
                message: $t('please-enter'),
              },
            ]"
            :label="$t('standard.parent')"
          >
            <a-tree-select
              v-model="form.parentId"
              :field-names="{
                title: 'name',
                children: 'childList',
                key: 'id',
              }"
              :data="treeData"
              :placeholder="$t('please-select')"
            ></a-tree-select
          ></a-form-item>
          <a-form-item
            field="fieldCode"
            :label="$t('standard.major')"
            validate-trigger="input"
            :rules="[
              {
                required: false,
                message: $t('please-enter'),
              },
            ]"
          >
            <a-select
              v-model="form.fieldCode"
              :placeholder="$t('please-select')"
            >
              <a-option
                v-for="item of professionalList"
                :key="item.code"
                :value="item.code"
                >{{ item.name }}</a-option
              >
            </a-select>
          </a-form-item>

          <a-form-item
            field="name"
            :label="$t('standard.name')"
            validate-trigger="input"
            :rules="[
              {
                required: true,
                message: $t('please-enter'),
              },
            ]"
          >
            <a-input v-model="form.name" :placeholder="$t('please-enter')" />
          </a-form-item>
          <a-form-item
            field="code"
            :label="$t('standard.code')"
            validate-trigger="input"
            :rules="[
              {
                required: true,
                message: $t('please-enter'),
              },
            ]"
          >
            <a-input v-model="form.code" :placeholder="$t('please-enter')" />
          </a-form-item>
          <a-form-item :label="$t('standard.ifc-type')">
            <a-input v-model="form.ifcType" :placeholder="$t('please-enter')" />
          </a-form-item>
          <a-form-item
            field="description"
            :label="$t('standard.description')"
            validate-trigger="input"
            :rules="[
              {
                required: false,
                message: $t('please-enter'),
              },
            ]"
          >
            <a-textarea
              v-model="form.description"
              :placeholder="$t('please-enter')"
              show-word-limit
              :auto-size="{
                minRows: 2,
              }"
            />
          </a-form-item>
          <!-- <a-col :span="12"
              ><a-form-item :label="$t('standard.level-category')">
                <a-select
                  v-model="form.levelType"
                  :placeholder="$t('please-select')"
                >
                  <a-option v-for="item of professionOption" :key="item.id">{{
                    item.name
                  }}</a-option>
                </a-select>
              </a-form-item></a-col
            > -->
        </a-form>
      </a-spin>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { ref, watch } from 'vue';
  import useStandardManageStore from '@/store/modules/standard-manage/index';
  import { storeToRefs } from 'pinia';
  import { addTree, editTree } from '../api';
  import { useI18n } from 'vue-i18n';

  const store = useStandardManageStore();
  const { treeSelectList, professionalList, selTreeData } = storeToRefs(store);

  const { t } = useI18n();

  const loading: any = ref(false);

  const treeData: any = ref([]);

  const props: any = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      required: false,
    },
    type: {
      type: String,
      required: false,
    },
  });

  const form = ref<any>({
    name: '',
    scenarioConfigList: [
      {
        aliasName: '',
        architecturalId: '',
      },
    ],
  });

  const emit = defineEmits(['update:visible', 'refresh']);

  const handleBeforeCancel = () => {
    emit('update:visible', false);
  };

  const formRef = ref(null);

  // 提交
  const handleBeforeOk = async () => {
    const res = await formRef.value?.validate();
    const param: any = {
      description: form.value.description,
      code: form.value.code,
      fieldCode: form.value.fieldCode,
      name: form.value.name,
      parentId: form.value.parentId,
      id: form.value.id || '',
      ifcType: form.value.ifcType,
    };

    if (!res) {
      loading.value = true;
      let resData: any = null;
      try {
        // 新增
        if (props.type === 'add') {
          resData = await addTree(param);
        } else {
          param.id = form.value?.id;
          // 编辑
          resData = await editTree(param);
        }
        if (resData) {
          Message.success(
            props.type === 'add' ? t('create-successful') : t('edit-successful')
          );
          emit('refresh');
          emit('update:visible', false);
        }
      } catch (err) {
        console.log(err);
      } finally {
        loading.value = false;
      }
    }
  };

  const init = () => {
    [treeData.value] = [treeSelectList.value];
  };

  watch(
    () => props.visible,
    () => {
      init();
      if (props.type === 'add') {
        form.value = {};
        form.value.scenarioConfigList = [];
        // 父级展示树当前选择的数据
        form.value.parentId = selTreeData.value?.id || '';
      } else {
        // 基础信息回显
        form.value = {};
        form.value.fieldCode = selTreeData.value.fieldCode;
        form.value.name = selTreeData.value.name;
        form.value.code = selTreeData.value.code;
        form.value.id = selTreeData.value.id;
        form.value.parentId = selTreeData.value.parentId;
        form.value.description = selTreeData.value.description;
        form.value.ifcType = selTreeData.value.ifcType;
      }
    },
    {
      immediate: true,
    }
  );
</script>

<script lang="ts">
  export default {
    name: 'AddNode',
  };
</script>

<style lang="less" scoped>
  .appMapLine {
    display: block;
    margin-bottom: 4px;
    padding: 0 8px;
    border-left: 5px solid #1890ff;
  }

  :deep(.arco-select),
  :deep(.arco-textarea),
  :deep(.arco-input-wrapper),
  :deep(.arco-select-view-single) {
    border: 1px solid #c9cdd4 !important;
    background-color: #fff;
    // border-radius: 8px;
  }
</style>
