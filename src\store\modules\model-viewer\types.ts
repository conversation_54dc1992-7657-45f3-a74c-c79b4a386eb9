export interface ModelToolsState {
  threeDMeasurementActive: boolean;
  createIssueToolActive: boolean;
  issueListToolActive: boolean;
  issueId: string;
  modelViewWidth: string;
  issueReplyActive: boolean;
  issueReplyModel: string;
  issueReplyData: any;
  attributeListToolActive: boolean;
  compareInfoShow: boolean;
  collisionInfoShow: boolean;
  annotationStart: boolean;
  quantitiesToolActive: boolean;
  viewManageGLVisible: boolean;
  startViewData: any;
  markManageGLVisible: boolean;
  nowPointData: any;
  navVisible: boolean;
  bidimensionalVisible: boolean;
  navManageVisible: boolean;
  twoViewToolActive: boolean;
  temporaryModelLoadFlag: boolean;
  temporaryModelData: any;
  modelAssemblyActive: boolean;
  motionPath: string;
  ModelAssemblyType: any;
  modalTreeIsShow: boolean;
  propertiesPanelShow: boolean;
  propertiesData: any;
  alertShow: boolean;
  alertSubmit: boolean;
}
