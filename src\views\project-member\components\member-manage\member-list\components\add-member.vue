<template>
  <a-modal
    :visible="visible"
    :title="title"
    :width="676"
    :unmount-on-close="true"
    :ok-loading="loading"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-form ref="memberRef" :model="form">
      <a-form-item
        field="member"
        :label="$t('prjMember.personnel')"
        validate-trigger="input"
        :rules="[
          {
            required: true,
            message: $t('please-select'),
          },
        ]"
      >
        <a-input-tag
          v-model="form.member"
          :placeholder="$t('please-select')"
          @focus="getUserFocus"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <select-members
    v-model:visible="selUserVisible"
    :data="memberData"
    @select-member="selectMember"
  ></select-members>
</template>

<script lang="ts" setup>
  import { ref, watch, inject } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { Message } from '@arco-design/web-vue';
  import { addMember } from '@/api/member';
  import useLoading from '@/hooks/loading';
  import selectMembers from '@/components/selectMembers/index.vue';
  import { useRoute } from 'vue-router';

  const selUserVisible = ref(false);
  const memberData = ref([]);
  const form: any = ref({});

  // 获取焦点时触发选人弹窗
  const getUserFocus = () => {
    selUserVisible.value = true;
  };

  // 获取选择成员
  const userName = ref([]);
  const selectMember = (user: any) => {
    userName.value = [];
    memberData.value = user;
    form.value.member = user?.map((item: any) => item.userFullname);
    memberData.value.forEach((item: any) => {
      item.username = item.userName;
      userName.value.push(item?.userName);
    });
  };

  const { t } = useI18n();
  const { loading, setLoading } = useLoading(false);

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits(['update:visible', 'refresh']);

  const route = useRoute();

  const handleCancel = () => {
    emit('update:visible', false);
  };

  const handleData: any = inject('handleData');
  const memberRef = ref<any>();
  const handleBeforeOk = async () => {
    const res = await memberRef.value?.validate();
    if (!res) {
      setLoading(true);
      const param: any = {
        projectId: route.params?.projectId,
        usernames: userName.value.join(','),
      };
      const res = await addMember(param);
      if (res.status) {
        Message.success(t('prjMember.success'));
        emit('update:visible', false);
        handleData();
        // emit('refresh');
      }
      setLoading(false);
    }
  };

  watch(
    () => props.visible,
    (n) => {
      form.value = {};
    }
  );
</script>

<script lang="ts">
  export default {
    name: 'AddMember',
  };
</script>

<style scoped lang="less">
  .user-item {
    width: 190px;
    //border: 1px solid red;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
