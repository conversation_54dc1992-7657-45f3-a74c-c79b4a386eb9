export default {
  'project-setting.please-enter': 'Please Enter',
  'project-setting.add-subordinate-team': 'Add Subordinate Team',
  'project-setting.project': 'Project',
  'project-setting.team-settings': 'Team Settings',
  'project-setting.project-plan': 'Project Plan',
  'project-setting.process-settings': 'Process Settings',
  'project-setting.project-information': 'Project Information',
  'project-setting.design-information': 'Design Information',
  'project-setting.project-template-information':
    'Project Template Information',
  'project-setting.project-name': 'Project Name',
  'project-setting.template-name': 'Template Name',
  'project-setting.project-code': 'Project Code',
  'project-setting.project-location': 'Project Location',
  'project-setting.project-type': 'Project Type',
  'project-setting.model-engine': 'Model Engine',
  'project-setting.X-Base-model-engine': 'project-setting.X-Base Model Engine',
  'project-setting.BIM-Base-model-engine': 'BIM-Base Model Engine',
  'project-setting.road-level': 'Road Level',
  'project-setting.coordinate-system': 'Coordinate System',
  'project-setting.elevation-datum': 'Elevation Datum',
  'project-setting.design-load': 'Design Load',
  'project-setting.safety-level': 'Safety Level',
  'project-setting.environmental-category': 'Environmental Category',
  'project-setting.seismic-grade': 'Seismic Grade',
  'project-setting.project-template': 'Project Template',
  'project-setting.project-time': 'Project Time',
  'project-setting.integrated-product-application':
    'Integrated Product Application',
  'project-setting.project-overview': 'Project Overview',
  'project-setting.edit': 'Edit',
  'project-setting.cancel': 'Cancel',
  'project-setting.ok': 'Ok',
  'project-edit-success': 'Edit Success',
  'project-add-success': 'Add Success',
  'project-setting.project-name-errMsg': 'Please enter the project name',
  'project-setting.project-name-length-errMsg':
    'Maximum of 255 characters allowed',
  'project-setting.name-exclude':
    'The name cannot contain any of the following characters: \\/:*?" <>|',
  'project-setting.model-engine-errMsg': 'Please select the model engine',
  'project-setting.project-code-errMsg': 'Please enter the project code',
  'project-setting.project-code-english-number-errMsg':
    'Only english letters and numbers are allowed',
  'project-setting.project-location-errMsg':
    'Please enter the project location',
  'project-setting.project-type-errMsg': 'Please select the project type',
  'project-setting.coordinate-system-errMsg':
    'Please select the coordinate system',
  'project-setting.elevation-reference-errMsg':
    'Please select the elevation reference',
  'project-setting.environmental-category-errMsg':
    'Please select the environmental category',
  'project-setting.earthquake-magnitude-errMsg':
    'Please select the earthquake magnitude',
  'project-setting.project-date-errMsg': 'Please select the project date',
  'project-setting.max-length-errMsg': 'Maximum of 500 characters allowed',
  'project-setting.road-level-errMsg': 'Please select the road level',
  'project-setting.team-name': 'Team',
  'project-setting.search': 'Search',
  'project-setting.clear': 'Clear',
  'project-setting.team-list': 'Team List',
  'project-setting.add-team': 'Add Team',
  'project-setting.color': 'Color',
  'project-setting.name': 'Name',
  'project-setting.location-change': 'Location Change',
  'project-setting.top': 'Top',
  'project-setting.bottom': 'Bottom',
  'project-setting.view': 'View',
  'project-setting.setting': 'Setting',
  'project-setting.move-up': 'Move Up',
  'project-setting.move-down': 'Down',
  'project-setting.team': 'Team',
  'project-setting.team-task-description': 'Team Task Description',
  'project-setting.team-members': 'Team Members',
  'project-setting.team-permission-config': 'Team Permission Config',
  'project-setting.team-relationship': 'Subordinate team affiliation',
  'project-setting.manage': 'Manage',
  'project-setting.share': 'Share',
  'project-setting.noData-title': 'No task division of labor',
  'project-setting.teamName': 'Team Name',
  'project-setting.teamMembers': 'Team Members',
  'project-setting.team-manage': 'Team Manage',
  'project-setting.not-active': 'Not Activated',
  'project-setting.delete': 'Delete',
  'project-setting.team-name-errMsg': 'Please enter the team name',
  'project-setting.max-width-errMsg': 'Maximum of 241 characters allowed',
  'project-setting.sequence-adjustment': 'Sequence Adjustment',
  'project-setting.team-admins': 'Team Admins',
  'project-setting.member-count': 'Member Count',
  'project-setting.operations': 'Operations',
  'project-setting.permission-level': 'Permission Level',
  'project-setting.index': 'Index',
  'project-setting.professional-attributes': 'Professional Attributes',
  'project-setting.attribute-list': 'Attribute List',
  'project-setting.return': 'Return',
  'project-setting.unit': 'Unit',
  'project-setting.type': 'Type',

  'project-setting.example-value': 'Example Value',
  'project-setting.description': 'Description',
  'project-setting.please-select-team-color': 'Please select team color',
  'project-setting.community': 'Community',

  'project-setting.schematic-diagram-of-milestone-nodes':
    'Schematic Diagram Of Milestone Nodes',
  'project-setting.project-start': 'Project Start',
  'project-setting.project-end': 'Project End',
  'project-setting.matrix-of-responsibilities': 'Matrix Of Responsibilities',
  'project-setting.start-end-time': 'Start End Time',
  'project-setting.description-of-deliverables': 'Description Of Deliverables',
  'project-setting.create-milestones': 'Create Milestones',
  'project-setting.time': 'Milestone Time',
  'project-setting.view-diagram': 'View Diagram',
  'project-setting.reset': 'Reset',
  'project-setting.is-delete': 'Are You Sure You Want to Delete This Project?',
  'project-setting.create': 'Create',
  'project-setting.milestones': 'Milestones',
  'project-setting.dialog-title': 'Milestone',
  'project-setting.end-time': 'End Time',
  'project-setting.describe': 'Deliverable Description',
  'project-setting.end-time-err-msg': 'End time is mandatory！',
  'project-setting.milestones-name-err-msg': 'Milestone name is mandatory！',
  'project-setting.milestones-name': 'Milestone Name',
  'project-setting.describe-err-msg': 'Describe is mandatory！',

  'project-setting.sequence-number': 'Sequence Number',
  'project-setting.process-template-name': 'Name',
  'project-setting.process-status': 'Process Status',
  'project-setting.node-number': 'Node Number',
  'project-setting.process-template-description':
    'Process Template Description',
  'project-setting.creator': 'Creator',
  'project-setting.updater': 'Updater',
  'project-setting.create-time': 'Create Time',
  'project-setting.update-time': 'Update Time',
  'project-setting.operation': 'Operation',
  'project-setting.process-list': 'Process List',
  'project-setting.add-process-template': 'Add Process Template',
  'project-setting.is-release-process-template':
    'Are you sure to publish the process template?',
  'project-setting.is-delete-process-template':
    'Are you sure to delete the process template?',
  'project-setting.release': 'Release',

  'project-setting.flow-template-name': 'Flow Template',
  'project-setting.please-enter-name': 'Please Enter Name',
  'project-setting.flow-template-description': 'Description',
  'project-setting.please-enter-description': 'Please Enter Description',
  'project-setting.please-select-team': 'Please Select Team',
  'project-setting.general': 'General',
  'project-setting.add-node': 'Add Node',
  'project-setting.auditor': 'Auditor',
  'project-setting.node-name': 'Node Name',
  'project-setting.flow-node-configuration': 'Flow Node Configuration',

  'project-setting.create-process-template': 'Create Process template',
  'project-setting.edit-process-template': 'Edit Process Template',
  'project-setting.view-process-template': 'View Process Template',
  'project-setting.please-complete-node-name-or-auditor-info':
    'Please complete node name or auditor information!',
  'project-setting.please-complete-basic-info':
    'Please complete basic information!',
  'project-setting.create-success': 'Create success!',
  'project-setting.update-success': 'Update success!',
  'project-setting.add-success': 'Add Success',
  'project-setting.edit-success': 'Edit Success',
  'project-setting.published': 'Published',
  'project-setting.unpublished': 'Unpublished',
  'project-setting.start': 'Start',
  'project-setting.end': 'End',
  'project-setting.node': 'Node',

  'project-setting.roadbed': 'Roadbed',
  'project-setting.road_surface': 'Road Surface',
  'project-setting.bridge': 'Bridge',
  'project-setting.beam_bridge': 'Beam Bridge',
  'project-setting.arch_bridge': 'Arch Bridge',
  'project-setting.cable-stayed_bridge': 'Cable-Stayed Bridge',
  'project-setting.suspension_bridge': 'Suspension Bridge',
  'project-setting.tunnel': 'Tunnel',

  'project-setting.deliver': 'Deliver',
  'project-setting.add_task': 'Add task',
  'project-setting.delate_task': 'Delate Task',
  'project-setting.team-task-name-errMsg': 'Pleast Select Team Name',
  'project-setting.team-start-end-time': 'Start And End Time',
  'project-setting.team-start-end-time-errMsg':
    'Pleast Select Start And End Time',
  'project-setting.task-description': 'Task Description',
  'project-setting.task-description-errMsg': 'Please Enter Task Description',
  'project-setting.dismantling-tasks': 'Dismantling Tasks',
  'project-setting.main-tasks': 'Main Tasks',
  'project-setting.subtasks': 'Subtasks',
  'project-setting.milestone-information': 'Director',
  'project-setting.team-tasks': 'Team Tasks',
  'project-setting.confirm-deletion': 'Confirm Deletion？',
  'project-setting.deletion-hint':
    'The current task contains the sub -task. If the Tongzi task is deleted and deleted, whether it continues?',
  'project-setting.milestone-deletion-hint':
    'The current milestone exists in task information. If you delete it, you will delete it with the same task？',
  'project-setting.add-task-error':
    'The end time of the team task should be less than the end time of the milestone',
  'project-setting.add-dismantling-task-error':
    'The end time of the disassembly task should be less than the end time that is equal to the disassembly of the first level task',
  'project-setting.application-expand': 'Expand All',

  'project-setting.custom-content': 'Custom Content',
  'project-setting.all-audit-required': 'All Audit Required',
  'project-setting.one-audit-required': 'One Audit Required',
};
