<template>
  <a-modal
    :visible="visible"
    draggable
    :width="800"
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="cancel"
  >
    <template #title>
      {{ $t('issues.operation-records') }}
    </template>
    <template #footer>
      <a-button @click="cancel">{{
        $t('model-collaboration.cancel')
      }}</a-button>
    </template>
    <div class="content">
      <a-table
        :columns="columns"
        :scroll="{ y: tableHeight }"
        :data="detailData"
        :pagination="PageConfigs"
        @page-change="pageChange"
        @page-size-change="pageSizeChange"
      >
        <template #operator="{ record }">
          {{ `${record.createUsername}` }}
        </template>
        <template #operation="{ record }">
          <span v-if="record.operate == 1">
            {{ $t('issues.create-issues') }}
          </span>
          <span v-if="record.operate == 2">
            {{ $t('issues.reply-issue') }}
          </span>
        </template>
        <template #modifyStatus="{ record }">
          <a-tag v-if="record.issueState == 0" color="red" bordered>
            {{ $t('issues.unresolved') }}
          </a-tag>
          <a-tag v-if="record.issueState == 1" color="green" bordered>
            {{ $t('issues.resolved') }}
          </a-tag>
          <a-tag v-if="record.issueState == 2" color="blue" bordered>
            {{ $t('issues.processing') }}
          </a-tag>
        </template>
        <template #operationTime="{ record }">
          {{ `${record.createDate}` }}
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue';
  import { useRoute } from 'vue-router';

  import { Column } from '@/views/design/components/table-column';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const route = useRoute();
  const tableHeight = ref(window.innerHeight - 400);

  const columns = computed<Column[]>(() => {
    return [
      {
        title: t('issues.operator'),
        dataIndex: 'operator',
        slotName: 'operator',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: t('issues.operation'),
        dataIndex: 'operation',
        slotName: 'operation',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: t('issues.modifyStatus'),
        dataIndex: 'modifyStatus',
        slotName: 'modifyStatus',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: t('issues.operation-time'),
        dataIndex: 'operationTime',
        slotName: 'operationTime',
        align: 'center',
      },
    ];
  });

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    detailData: {
      type: Array<any>,
      default: () => {
        return [];
      },
    },
  });

  // 分页
  const PageConfigs: any = reactive({
    current: 1,
    pageSize: 10,
    pageSizeOptions: [10, 20, 40],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
  });
  const pageChange = (val: number) => {
    PageConfigs.current = val;
  };
  const pageSizeChange = (val: number) => {
    PageConfigs.pageSize = val;
  };

  // const modelView = async (record: any) => {
  //   const type = record.name.split('.')[record.name.split('.').length - 1];
  //   if (modelall.includes(type))
  //     modelViewer({
  //       record,
  //       projectId: route.params.projectId as string,
  //       version: 1,
  //     });
  // };
  const emits = defineEmits(['update:visible', 'refresh']);
  const cancel = () => {
    emits('update:visible', false);
  };
</script>

<style scoped lang="less">
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .file-list-wrap {
    margin-top: 16px;
  }
</style>
