<template>
  <div v-for="(item, index) in files" :key="item.fileToken" class="file-box">
    <div class="file-item">
      <file-image
        :file-name="item.name || item.fileName"
        :is-file="true"
        style="width: 20px; height: 20px; border-radius: 4px; margin-right: 6px"
      />
      <div class="file-text" @click="modelView(item, index, 'preview')">
        {{ item.name || item.fileName }}
      </div>
      <div v-if="item.version" class="version">
        {{ 'V' + item.version }}
      </div>
      <div class="operate">
        <a-tooltip
          v-if="route.params.projectId"
          :content="$t('schedule.save-as')"
        >
          <icon-save
            v-show="userStore.username === item.createBy"
            size="16"
            @click="preMoveSetting(item)"
          />
        </a-tooltip>

        <a-tooltip :content="$t('schedule.meeting.download')">
          <icon-download size="16" @click="downLoadFile(item)" />
        </a-tooltip>
        <a-tooltip
          v-if="
            item.id &&
            isWpsFile(item.fileName) &&
            userStore.username === item.createBy
          "
          :content="$t('schedule.edit')"
          ><icon-edit size="16" @click="modelView(item, index, 'edit')"
        /></a-tooltip>
        <a-tooltip :content="$t('schedule.delete')">
          <a-popconfirm
            :content="$t('schedule.delete.attachment')"
            @ok="deleteFile(item)"
          >
            <icon-delete
              v-show="userStore.username === item.createBy"
              size="16"
              style="vertical-align: middle"
            />
          </a-popconfirm>
          <!-- <icon-close
            v-show="
              (type === 'edit' && !hasNoEditPermission) ||
              (type !== 'view' && !hasNoEditPermission)
            "
            size="16"
            @click="deleteFile(item)"
          /> -->
        </a-tooltip>
      </div>
    </div>

    <!-- <div v-show="type !== 'view'">
      <a-tooltip content="ai识别描述生成子事项清单">
        <img
          src="@/assets/images/matter/Group 1321317016.png"
          alt="暂无图片"
          class="ai-recognition"
          @click="aiRecognitionByFile(item)"
        />
      </a-tooltip>
    </div> -->
  </div>

  <move-dialog
    v-model:visible="moveModal"
    :title="$t('file-manage.select-target-folder')"
    :ok-function="beforeMoveOkHandler"
    :show-type="[]"
    :dis-check-hierarchy="[1]"
    check-type="single"
    output-type="id"
    is-clear-key
  ></move-dialog>
</template>

<script lang="ts" setup>
  import { computed, defineProps, ref } from 'vue';
  import FileImage from '@/components/uploadTheSpecifiedFolder/components/image-file.vue';
  import { useRoute } from 'vue-router';
  import { downloadSource } from '@/components/file-image/hooks/dropdow-events';
  import { wpsViewHandle } from '@/hooks/wps';
  import { useUserStore } from '@/store';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import wpsJson from '@/config/wpsType.json';
  import { AttachTableView } from '@/store/modules/file/types';
  import useFileStore from '@/store/modules/file/index';
  import { moveAttachment } from '@/views/projectSpace/file/api';
  import MoveDialog from '@/components/tree-folder/index.vue';

  const { t } = useI18n();
  const fileStore = useFileStore();
  const moveModal = ref<boolean>(false);

  const userStore = useUserStore();
  const userName = computed(() => userStore.username);
  const props = defineProps({
    files: {
      type: Array as any,
      default() {
        return [];
      },
    },
    fileArrList: {
      type: Array as any,
      default() {
        return [];
      },
    },
    type: {
      type: String,
      default: '',
    },
    hasFileEditPermission: {
      type: Boolean,
      default: false,
    },
  });

  const route = useRoute();
  const emits = defineEmits(['deleteFile', 'recognizeFile']);
  // 判断是否是wps文件
  const isWpsFile = (fileName: any) => {
    const type = fileName.split('.')[fileName.split('.').length - 1];
    const isWpsType: boolean = wpsJson.includes(type);
    return isWpsType;
  };
  const modelView = async (record: any, index: number, type: string) => {
    if (record.id) {
      record.name = record.fileName;
      wpsViewHandle(record, type, 'admin');
    } else {
      Message.info('请保存后查看');
    }
  };

  // 删除文件
  const deleteFile = (file: any) => {
    emits('deleteFile', file);
  };

  // 下载文件
  const downLoadFile = (file: any) => {
    downloadSource(file);
  };

  // ai识别文件
  const aiRecognitionByFile = async (file: any) => {
    const identityData = props.fileArrList?.find(
      (item: any) => item.uid === file.uid
    );
    // 创建一个 FormData 实例
    const formData = new FormData();

    // 将文件添加到 FormData 中，key 可以是后端要求的字段名，比如 "file"
    formData.append('file', identityData.file);
    emits('recognizeFile', 'file', formData);
  };

  let beforeMoveOkHandler = (
    treeDataPromise: () => Promise<any>,
    teamId: string
  ) => Promise.resolve(true);

  function handleMoveParams(record: AttachTableView[]) {
    // 会议这里暂时只有单个文件另存 因此 moveParamList 赋为 []
    fileStore.setAttachMoveIds({
      attachmentFileIdList: [record?.id],
      moveParamList: [],
    });
  }

  // 移动前的设置
  function preMoveSetting(record: any) {
    console.log('[ record ] >', record);
    handleMoveParams(record);

    beforeMoveOkHandler = async function moveRequest(
      treeDataPromise: () => Promise<any>,
      teamIdString: () => string
    ): Promise<boolean> {
      const parentId: string = await treeDataPromise();
      const teamId: string = teamIdString();
      if (!parentId || !teamId) {
        Message.error(t('file-manage.select-target-folder'));
        return false;
      }
      const attachMoveIds = fileStore.attachMoveIds;

      if (props.type !== 'view') {
        record.specifiedFolderId = parentId;
        record.teamId = teamId;
        return true;
      }
      const result: any = await moveAttachment({
        folderId: parentId,
        ...attachMoveIds,
      });

      if (result.code === 8000000) {
        Message.success(t('file-manage.success'));
        return true;
      }

      return false;
    };
    moveModal.value = true;
  }
</script>

<script lang="ts">
  export default {
    name: 'FileItem',
  };
</script>

<style scoped lang="less">
  .file-box {
    display: flex;
    width: 100%;
  }
  .file-item {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #f7f8fa;
    padding: 5px 12px;
    margin-bottom: 10px;
    justify-content: space-between;
    width: 95%;
    font-size: 14px;
    cursor: pointer;
  }
  .file-text {
    width: 90%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    margin-right: 20px;
    &:hover {
      color: #3366ff;
    }
  }
  .version {
    margin-right: 10px;
  }
  .operate {
    display: inline-flex;
    gap: 12px;
    justify-content: center;
    align-items: center;
  }
  .ai-recognition {
    cursor: pointer;
  }
</style>
