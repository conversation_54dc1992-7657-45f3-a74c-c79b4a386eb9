<template>
  <a-modal
    :visible="visible"
    :unmount-on-close="true"
    :mask-closable="false"
    width="900px"
    draggable
    :esc-to-close="false"
    @before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <template #title>
      <span>{{ dialogTitle }}</span>
      <div class="team-title">{{ i18TeamName(originData) }}</div>
      <span>{{ $t('project-setting.team') }}</span>
    </template>
    <a-form
      ref="teamRef"
      :model="formData"
      auto-label-width
      :disabled="handleType === 'view'"
      class="edit-form"
    >
      <a-row class="interval-row">
        <a-col :span="11">
          <a-form-item
            field="name"
            :label="$t('project-setting.team-name')"
            label-col-flex="auto"
            validate-trigger="input"
            :rules="[
              {
                required: true,
                message: $t('project-setting.team-name-errMsg'),
              },
              {
                maxLength: 241,
                message: $t('project-setting.max-width-errMsg'),
              },
            ]"
          >
            <remove-spaces-input
              v-if="currentLocale === 'en-US'"
              v-model="formData.englishName"
              :placeholder="$t('please-enter')"
              :max-length="255"
              show-word-limit
              :disabled="formData.defaultTeam === 0"
            />
            <remove-spaces-input
              v-else
              :disabled="formData.defaultTeam === 0"
              v-model="formData.name"
              :placeholder="$t('please-enter')"
              :max-length="100"
              show-word-limit
            />
            <!-- <a-input
              v-model="formData.name"
              :placeholder="$t('please-enter')"
            /> -->
          </a-form-item>
        </a-col>
        <a-col :span="11" :offset="1">
          <a-form-item
            field="userIds"
            :label="$t('project-setting.team-members')"
            label-col-flex="auto"
          >
            <user-selector
              v-model="formData.userIds"
              :project-id="formData.projectId"
              multiple
              @change="handleUsersChange"
            />
          </a-form-item>
        </a-col>
        <a-col
          :span="11"
          v-if="originData?.level !== '2'"
          v-permission="$btn.team.subordination"
        >
          <a-form-item
            field="isInherit"
            :label="$t('project-setting.team-relationship')"
            content-class="permission-item"
            required
          >
            <a-radio-group v-model="formData.isInherit">
              <a-radio :value="1">
                <span>有</span>
              </a-radio>
              <a-radio :value="0">
                <span>无</span>
              </a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- <a-form-item field="role" label="权限级别">
        <template #label>
          <img class="image" :src="infoImg" />
          <span class="title-text">权限级别</span>
        </template>
        <span>{{ getPermissionText(formData.role) }}</span>
        <permission-icon :active-count="formData.role" class="mini-icon" />
      </a-form-item> -->

      <a-table
        :loading="loading"
        :columns="(handleType === 'view' ? teamColumnsView  : teamColumnsEdit) as TableColumnData[]"
        :data="formData.teamUsers"
        :scroll="scrollConfig"
        :bordered="false"
        :pagination="false"
        class="people-table"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
        <template #name="{ record }">
          <div class="name">
            {{ record.name }}
            <a-tooltip
              v-if="record.accountState == 2"
              :content="$t('project-setting.not-active')"
            >
              <img class="image" :src="warningImg" />
            </a-tooltip>
          </div>
        </template>
        <template #role="{ record }">
          <template v-if="handleType === 'view'">
            {{ getPermissionText(record.role) }}
          </template>
          <template v-else>
            <a-select v-model="record.role" :options="options" />
          </template>
        </template>
        <template #operations="{ record }">
          <a-button
            type="text"
            size="small"
            status="danger"
            @click="handleDelete(record.id)"
            >{{ $t('project-setting.delete') }}</a-button
          >
        </template>
      </a-table>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import useLoading from '@/hooks/loading';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  // import infoImg from '@/assets/images/project-setting/<EMAIL>';
  import warningImg from '@/assets/images/project-setting/warning-icon.png';
  import UserSelector from '@/components/user-selector/index.vue';
  // import PermissionIcon from './permission-icon.vue';
  import { TeamRecord, UserRecord, queryTeamDetail, updateTeam } from '../api';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';

  const { t } = useI18n();
  // 国际化类型
  const { currentLocale } = useLocale();

  const { i18TeamName } = useI18nHandleName();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    handleType: {
      type: String,
      default: 'view',
    },
    originData: {
      type: Object,
      require: true,
    },
  });
  const emit = defineEmits(['update:visible', 'refresh']);

  const { loading, setLoading } = useLoading(true);
  const teamRef = ref<FormInstance>();
  const dialogTitle = computed(() => {
    return props.handleType === 'view'
      ? t('project-setting.view')
      : t('project-setting.edit');
  });
  const formData = ref<TeamRecord>({ ...props.originData } as TeamRecord);
  console.log(formData);
  // 通过人员列表，生成用户id字符串（用于传给选人组件）
  const getTeamUserIds = () => {
    return formData.value.teamUsers
      ?.map((user: UserRecord) => {
        return user.id;
      })
      .join(',');
  };

  // 查询团队数据
  const getTeamDetail = () => {
    setLoading(true);
    const params = {
      id: props.originData?.id,
    };
    queryTeamDetail(params)
      .then((res) => {
        formData.value = res.data || {};
        // 为选人组件赋初始值
        formData.value.userIds = getTeamUserIds();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  watch(
    () => props.originData,
    (n) => {
      formData.value = { ...n } as TeamRecord;
      getTeamDetail();
    }
  );

  // 监听选人组件变化，同步更新人员列表
  const handleUsersChange = (newUserAry: Array<UserRecord>) => {
    const newIds = newUserAry
      ?.map((user: UserRecord) => {
        return user.id;
      })
      .join(',');
    const oldIds = formData.value.teamUsers
      ?.map((user: UserRecord) => {
        return user.id;
      })
      .join(',');

    if (newIds !== oldIds) {
      const teamUserAry = newUserAry.map((newUser: UserRecord) => {
        const oldTeamUserObj = formData.value.teamUsers?.find(
          (oldUser: UserRecord) => {
            return oldUser.id === newUser.id;
          }
        );
        if (oldTeamUserObj) {
          return { ...oldTeamUserObj };
        }
        return {
          id: newUser.id,
          name: newUser.name,
          role: formData.value.role,
          accountState: newUser.accountState,
        };
      });

      formData.value.teamUsers = teamUserAry;
    }
  };

  const options = computed(() => [
    {
      label: t('project-setting.view'),
      value: 1,
    },
    {
      label: t('project-setting.edit'),
      value: 2,
    },
    {
      label: t('project-setting.community'),
      value: 3,
    },
    {
      label: t('project-setting.deliver'),
      value: 4,
    },
    {
      label: t('project-setting.manage'),
      value: 5,
    },
  ]);

  // 权限数值转文字
  const getPermissionText = (role: number) => {
    return options.value.filter((item) => item.value === role)[0]?.label;
  };

  // 人员列表
  const scrollConfig = {
    y: 234,
  };

  const teamColumnsView = computed(() => [
    {
      title: t('project-setting.index'),
      dataIndex: 'index',
      slotName: 'index',
      align: 'center',
      width: 62,
    },
    {
      title: t('project-setting.name'),
      dataIndex: 'name',
      slotName: 'name',
    },
    {
      title: t('project-setting.permission-level'),
      dataIndex: 'role',
      slotName: 'role',
      align: 'center',
      width: 160,
    },
  ]);

  const teamColumnsEdit = computed(() => [
    ...teamColumnsView.value,
    {
      title: t('project-setting.operations'),
      dataIndex: '',
      slotName: 'operations',
      align: 'center',
      width: 90,
    },
  ]);

  // 删除人员按钮点击事件
  const handleDelete = (id: string) => {
    // 删除列表人员
    formData.value.teamUsers = formData.value.teamUsers?.filter(
      (user: UserRecord) => {
        return user.id !== id;
      }
    );
    // 删除选人组件人员
    formData.value.userIds = getTeamUserIds();
  };

  // 调用接口修改团队信息
  const handleEdit = async () => {
    try {
      const params = {
        ...formData.value,
        count: formData.value.teamUsers?.length || 0,
      };
      delete params.userIds;

      const res = await updateTeam(params);
      return !!res.status;
    } catch (error) {
      return false;
    }
  };

  // 确定按钮点击事件
  const handleBeforeOk = async (done: any) => {
    if (props.handleType === 'view') {
      // 查看
      emit('update:visible', false);
    } else {
      // 编辑
      const res = await teamRef.value?.validate();
      if (!res) {
        const flg = await handleEdit();
        if (flg) {
          Message.success(t('project-edit-success'));
          emit('update:visible', false);
          emit('refresh');
        }
        done();
      }
    }
  };

  // 取消按钮点击事件
  const handleCancel = () => {
    emit('update:visible', false);
  };
</script>

<style lang="less" scoped>
  .team-title {
    max-width: 200px;
    margin: 0 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .edit-form {
    :deep(.arco-form-item) {
      margin-bottom: 16px;
    }
  }

  .image {
    margin-right: 8px;
    width: 18px;
    height: 18px;
    vertical-align: text-top;
  }

  .title-text {
    font-size: 16px;
    line-height: 16px;
  }

  :deep(.permission-item) {
    .arco-radio-label {
      display: flex;
    }
  }

  :deep(.mini-icon .icon) {
    width: 22px;
  }

  .people-table {
    min-height: 274px;

    .name {
      display: flex;
      align-items: center;

      .image {
        margin-left: 6px;
        width: 16px;
        height: 16px;
      }
    }
  }
</style>
