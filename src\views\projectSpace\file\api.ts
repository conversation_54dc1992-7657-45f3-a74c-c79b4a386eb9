import axios from 'axios';
import { Paging } from '@/types/global';
import { FolderMessage, FileMessage } from '@/api/tree-folder';
import { getBIMBaseToken, getXBaseToken } from '@/utils/auth';
// eslint-disable-next-line import/no-cycle
import qs from 'query-string';
import useFileStore from '@/store/modules/file';
import { AttachMoveParams } from '@/store/modules/file/types';

export interface FileAndFolderMessage extends FolderMessage, FileMessage {
  /** 0:folder 1:file */
  isFileOrFolder?: number;
}

export interface FileVersion {
  createBy?: string;
  createDate?: string;
  deleteFlag?: number;
  description?: string;
  fileId?: string;
  fileToken?: string;
  folderId?: string;
  id?: string;
  isLocked?: boolean;
  name?: string;
  projectId?: string;
  size?: string;
  teamId?: string;
  type?: string;
  updateBy?: string;
  updateDate?: string;
  updateName?: string;
  version?: string;
}

export function fileUnlock(id: string) {
  const formData = new FormData();
  formData.append('id', id);
  return axios.post('/cde-collaboration/file/file-locked', formData);
}

export function addChildFolder(folder?: FolderMessage) {
  return axios.post('/cde-collaboration/folder/save', folder);
}

export function updateFolder(folder?: FolderMessage) {
  return axios.post('/cde-collaboration/folder/update', folder);
}

export function updateFile(file?: FileMessage, moveFlag?: boolean) {
  return axios.post('/cde-collaboration/file/update', {
    ...file,
    moveFlag,
  });
}

export function fileNullify(data: any) {
  return axios.post('/cde-collaboration/file/abandon', data);
}

export function moveFileAndFolder(
  fileIds: string[],
  folderIds: string[],
  targetFolderId: string
) {
  return axios.post('/cde-collaboration/file/batchupdate/floder', {
    fileIds,
    folderIds,
    targetFolderId,
  });
}

export function addFile(url: string, file?: FileMessage) {
  return axios.post(url, file);
}

export function xbaseFileUpload(data: object) {
  return axios.post('/cde-collaboration/file/file-upload', qs.stringify(data));
}

// 重新转换api
export function reConvertApi(params: { fileId: string }) {
  return axios.get(`/cde-collaboration/file/reConvert?fileId=${params.fileId}`);
}

export function getFolderDetail(params: { id: string }) {
  return axios.get(`/cde-collaboration/folder/detail`, { params });
}

export const deleteApi = (data: any) =>
  axios.post('/cde-collaboration/file/batch-delete/file', data);

export function getFileVersionList(id?: string) {
  return axios.get<Paging<FileVersion>>(
    '/cde-collaboration/file/version/list',
    {
      params: {
        id,
        pageSize: 9999,
      },
    }
  );
}

export function fileRollback(id?: string, version?: string) {
  return axios.get('/cde-collaboration/file/version/revert', {
    params: {
      id,
      version,
    },
  });
}

export function fileDownload(file: any) {
  const date = Date.now();
  return axios.get('/sys-storage/download', {
    params: {
      f8s: file.fileToken,
    },
    responseType: 'blob',
    timeout: 0,
    onDownloadProgress: (progressEvent) => {
      const value = (progressEvent.loaded / progressEvent.total) * 100;
      // fileStore.setDownloadProgress({
      //   id: date,
      //   name: file.name,
      //   percent: value,
      // });
    },
  });
}

export function fileShare(shareInfo: any) {
  return axios.post('/cde-collaboration/sharelink/generatelink', shareInfo);
}

export function filedownloadinfo(sharelink: any) {
  return axios.post('/cde-collaboration/sharelink/getdownloadinfo', sharelink);
}

// 获取文件夹下所有的文件夹及文件
export function getFileChildrenAll(parentId: string) {
  return axios.get('/cde-collaboration/folder/childrenAll', {
    params: {
      parentId,
    },
  });
}
export function allFiles(folderId: string) {
  return axios.get('/cde-collaboration/folder/allFiles', {
    params: {
      folderId,
    },
  });
}
export function getProjectUser(id: string) {
  const data = new FormData();
  data.append('id', id);
  return axios.post('/cde-collaboration/project/query-config', data);
}

export function setProjectUserConfig(id: string, orderConfig: string) {
  return axios.post('/cde-collaboration/project/update-config', {
    id,
    orderConfig,
  });
}

// 构力
// 获取所有的bucket信息
export function GetBucketInfo() {
  return axios.get('/bimserver/storage/v3/buckets', {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getBIMBaseToken() || ''}`,
    },
    baseURL: '/bim_base_api',
  });
}

// 获取上传的url
export function GetUploadURL(params: any) {
  return axios.get('/bimserver/storage/v3/uploadUrl', {
    params,
    headers: {
      Authorization: `Bearer ${getBIMBaseToken() || ''}`,
    },
    baseURL: '/bim_base_api',
  });
}

// 获取模型guid
export function getModelMsg(data: string, lodType: string) {
  return axios.get(
    `/bimserver/viewing/v3/${data}?objectKeyEncoding=base64&jobType=${lodType}`,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getBIMBaseToken() || ''}`,
      },
      baseURL: '/bim_base_api',
    }
  );
}

// BIMBase模型对比
export function BIMBaseModelCompare(data: any) {
  return axios({
    method: 'POST',
    url: '/bimserver/translation/v3/compare?client_id=acme&isRegenerated=true',
    data,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getBIMBaseToken() || ''}`,
    },
    baseURL: '/bim_base_api',
  });
}

// 模型对比信息
export function getModelCompareState(data: any) {
  return axios.get(`/bimserver/translation/v3/compare/${data}`, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getBIMBaseToken() || ''}`,
    },
    baseURL: '/bim_base_api',
  });
}

// 模型对比结果
export function getModelCompareResult(data: any) {
  return axios.get(
    `/bimserver/storage/v3/buckets/${data.bucketName}/objects/${data.objectPath}?objectKeyEncoding=base64`,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getBIMBaseToken() || ''}`,
      },
      baseURL: '/bim_base_api',
    }
  );
}

export function BimBaseAgainUpload(data: any) {
  return axios.post('/cde-collaboration/bimbase/againUpload', data);
}

// 创建模型对比
export function XBaseModelCompare(data: any) {
  return axios.post('/cde-collaboration/xbase/modelDiff/create', data);
}

export function SetModelInfo(data: any) {
  return axios.post('/cde-collaboration/file/set-graphicengine-info', data);
}

export function addMergaFile(data: any) {
  return axios.post('/cde-collaboration/file/save-model', data);
}

// 查询维表详情
export function queryDimensionsInfo(id: any) {
  return axios.get<string>('/asset-system/dimension/detail', {
    params: {
      id,
    },
  });
}

// 新增版本对比
export function addCompareData(data: any) {
  return axios.post('/cde-collaboration/file/version-compare', data);
}

export function updateCompareData(data: any) {
  return axios.put('/cde-collaboration/file/version-compare', data);
}

export function getFileInfoById(id?: string) {
  return axios.get('/cde-collaboration/file/detail', {
    params: {
      id,
    },
  });
}

export function getCompareList(params: any) {
  return axios.get('/cde-collaboration/file/version-compare', {
    params,
  });
}

// 删除版本对比
export function deleteVersion(id: any) {
  return axios.post(`/cde-collaboration/file/compare-remove?ids=${id}`);
}
/* 文件废除 */
export const abandonFiles = (data: any) => {
  return axios.post('/cde-collaboration/file/abandon', data);
};

// 生成分享链接
export const reqCreateShareLink = (data: any) => {
  return axios.post('/cde-collaboration/sharelink/generatelink', data);
};

// 获取分享列表
export const reqGetShareList = (params: any) => {
  return axios.get('/cde-collaboration/sharelink/getShareInfo', { params });
};

export function queryClashList(params: any) {
  return axios.get('/cde-collaboration/file/list', {
    params,
  });
}

// 查询wpsurl
export function getWpsUrl(params: any) {
  return axios.get('/cde-collaboration/wps/link', { params });
}
// wps刷新版本
export function wpsRefresh(params: any) {
  return axios.get<any>(`/wps-system/wps/refresh`, {
    params,
    baseURL: '/wps',
  });
}
// wps转换
export function wpsConvert(params: any) {
  return axios.get('/wps-system/wps/async/convert', {
    params,
    baseURL: '/wps',
  });
}
// 修改文件转换状态
export function setConvertStatus(params: any) {
  return axios.get('/cde-collaboration/wps_system/fileConvertStatus', {
    params,
  });
}
// 获取可转换的格式
export function getConvertSeek(params: any) {
  return axios.get('/cde-collaboration/wps/convertSeek', { params });
}
// 查询当前文件夹下满足搜索条件的文件、文件夹数据（可搜索名称、更新人）
export function getSearchData(data: any) {
  return axios.post('/cde-collaboration/file/searchFile', data);
}

// 获取文件详情
export function GetXBaseDocumentDetails(params: any) {
  return axios.get('/api/open/v1/document/document', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 获取可转换的格式
export function getCode(params: any) {
  return axios.get('/cde-collaboration/wps_system/code', { params });
}
// 获取DownloadUrl
export function getDownloadUrl(params: any) {
  return axios.get('/sys-storage/minioFileUrl', { params });
}

export interface AttachParams {
  pageNo: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
  keyName?: string;
  projectId: string;
  switchType?: string;
  switchTypeView?: string[];
  teamId?: string;
}
// 获取附件列表
export function getAttachList(params: AttachParams) {
  return axios.get('/cde-collaboration/agenda/listAttachmentFile', { params });
}
// 下载附件
export function attachmentDownload(
  directoryId: string,
  switchType: string,
  fileName: string
) {
  const fileStore = useFileStore();
  const date = Date.now();
  return axios.post(
    `/cde-collaboration/agenda/downloadDirectoryZipById/${directoryId}?switchType=${switchType}`,
    {},
    {
      responseType: 'blob',
      timeout: 0,
      onDownloadProgress: (progressEvent: any) => {
        const value = (progressEvent.loaded / progressEvent.total) * 100;
        fileStore.setDownloadProgress({
          id: date,
          name: fileName,
          percent: value,
        });
      },
    }
  );
}

// 文件另存为
export function moveAttachment(data: AttachMoveParams) {
  return axios.post(`/cde-collaboration/agenda/moveAttachment`, data);
}
