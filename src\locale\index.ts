import { createI18n } from 'vue-i18n';
import en from './en-US';
import cn from './zh-CN';

export const LOCALE_OPTIONS = [
  { label: '中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
];

// 获取浏览器语言
const getBrowserLanguage = () => {
  const browserLang = navigator.language.toLowerCase();
  return browserLang.startsWith('zh') ? 'zh-CN' : 'en-US';
};

// 获取语言设置，优先级：localStorage > 浏览器语言 > 默认中文
const getLocale = () => {
  const savedLocale = localStorage.getItem('arco-locale');
  if (savedLocale) {
    return savedLocale;
  }
  return getBrowserLanguage();
};

const i18n = createI18n({
  locale: getLocale(),
  fallbackLocale: 'zh-CN',
  allowComposition: true,
  messages: {
    'en-US': en,
    'zh-CN': cn,
  },
});

export default i18n;
