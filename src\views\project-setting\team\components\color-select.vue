<template>
  <div class="color-select">
    <div class="title">
      <span>{{ $t('project-setting.please-select-team-color') }}</span>
    </div>
    <div>
      <a-grid :cols="5" :col-gap="8" :row-gap="8" class="color-grid">
        <a-grid-item
          v-for="color in colorList"
          :key="color"
          :class="{ normal: true, selected: color === selectedColor }"
          :style="{ 'background-color': color }"
          @click="handleSelect(color)"
        >
          <img v-show="color === selectedColor" :src="colorSelect" />
        </a-grid-item>
      </a-grid>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import colorSelect from '@/assets/images/project-setting/<EMAIL>';
  import colors from '../json/colors.json';

  const props = defineProps({
    initColor: {
      type: String,
    },
  });
  const emit = defineEmits(['colorChanged']);

  const colorList = colors;

  const selectedColor = ref(props.initColor);
  watch(
    () => props.initColor,
    (n) => {
      selectedColor.value = n;
    }
  );

  const handleSelect = (color: string) => {
    selectedColor.value = color;
    emit('colorChanged', color);
  };
</script>

<style lang="less" scoped>
  .color-select {
    padding: 0 12px 16px;
    width: 156px;
    height: 136px;
    background-color: var(--color-bg-popup);
    border-radius: 4px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e6eb;

    .title {
      display: flex;
      align-items: center;
      height: 22px;
      margin: 11px 0;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #4e5969;
    }

    .color-grid {
      .normal {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        border-radius: 50%;
      }

      .selected {
        box-shadow: 0px 2px 5px 0px #e5e6eb;
        border: 2px solid #ffffff;

        img {
          width: 10px;
          height: 7px;
        }
      }
    }
  }
</style>
