<template>
  <div class="knowledge-base">
    <div class="header">
      <commonTabs v-model="tabKey" :tabs="[]"></commonTabs>
    </div>
    <div v-if="routeName === 'knowledgeBase'" class="main">
      <div class="main-left">
        <a-tabs v-model:activeKey="activeTabKey" @change="changeTabs">
          <template #extra>
            <a-tooltip
              :content="
                isCard
                  ? $t('knowledgenew.list-mode')
                  : $t('knowledgenew.card-mode')
              "
            >
              <icon-menu
                v-if="isCard"
                :size="20"
                class="card-list-icon"
                @click="isCard = !isCard"
              />

              <icon-apps
                v-else
                :size="20"
                class="card-list-icon"
                @click="isCard = !isCard"
              />
            </a-tooltip>
          </template>
          <a-tab-pane :key="1" :title="$t('knowledgenew.my-file')">
            <PersonalCloud
              :isCard="isCard"
              @refreshFolder="handleRefreshFolder"
            ></PersonalCloud>
            <!-- <MyCard v-if="isCard" @refreshFolder="handleRefreshFolder" />
            <MyList v-else @refreshFolder="handleRefreshFolder" /> -->
          </a-tab-pane>
          <a-tab-pane :key="2" :title="$t('knowledgenew.project-file')">
            <ProjectCloud
              :isCard="isCard"
              @refreshFolder="refreshProjectFolder"
            ></ProjectCloud>
          </a-tab-pane>
        </a-tabs>
      </div>
      <div class="main-right"><KnowledgeBasePanel /></div>
    </div>
    <router-view></router-view>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, onMounted, toRefs, watch } from 'vue';
  import commonTabs from '@/components/common-tabs/index.vue';
  import i18n from '@/locale/index';
  import PersonalCloud from './components/personal-cloud.vue';
  import ProjectCloud from './components/project-cloud.vue';
  import { getPrivateInit } from './components/api';
  import { useKnowledgeBaseNewStore } from '@/store';
  import { useRoute } from 'vue-router';
  import KnowledgeBasePanel from './components/base/knowledge-base-panel.vue';
  import { storeToRefs } from 'pinia';
  import {
    FolderMessage,
    getChildFolderList,
    getFileList,
  } from '@/api/tree-folder';
  import {
    initSessionStorageData,
    setSessionStorageData,
  } from '@/store/modules/file/utils';
  import { getUserId } from '@/utils/auth';

  const route = useRoute();
  const routeName = computed(() => route.name);

  const isCard = ref(true);
  const userId = getUserId() || '';
  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { personal, activeTabKey, project } = storeToRefs(
    knowledgeBaseNewStore
  );
  const { projectId } = toRefs(personal.value);

  const tabKey = ref('knowledgeBase');

  const { t } = i18n.global;

  // 网盘初始化，获取后端分配的项目id，用于调用项目文件接口
  // 个人网盘相当于一个项目下的文件管理
  async function initKnowledgeBase() {
    // 1. 判断store里有没有projectId
    console.log('获取项目ID', personal.value.projectId);
    // if (!personal.value.projectId) {
    try {
      const res = await getPrivateInit();
      knowledgeBaseNewStore.setProjectId(res.data.projectId);
      knowledgeBaseNewStore.setPersonalType(res.data.type);
      const data = {
        projectId: res.data.projectId,
        type: res.data.type,
        parentId: res.data.parentId,
        name: res.data.name,
        id: res.data.id,
        nameEn: 'personalSpace',
      };
      knowledgeBaseNewStore.setBaseFolder(data);
      knowledgeBaseNewStore.setPersonCurrentFolder(data);
      knowledgeBaseNewStore.setPersonalBreadcrumb([data]);
      knowledgeBaseNewStore.setBaseFolder(data);
    } catch (e) {
      // 处理接口失败
      // 可以弹窗、提示、return等
      console.error('获取projectId失败', e);
    }
    // }
  }
  const allFolderData = ref<FolderMessage[]>();
  const handleRefreshFolder = () => {
    knowledgeBaseNewStore.getPersonalFolder('personal');
    knowledgeBaseNewStore.getfiles('personal');
  };
  const refreshProjectFolder = () => {
    // knowledgeBaseNewStore.getProjectFolder();
    // knowledgeBaseNewStore.getProjectFiles();
  };

  const initPersonalFolder = async () => {
    await initKnowledgeBase();
    if (projectId.value) {
      knowledgeBaseNewStore.getPersonalFolder('personal');
      knowledgeBaseNewStore.getfiles('personal');
    }
  };
  const initProjectFolder = async () => {
    if (project.value.breadcrumb.length === 1) {
      knowledgeBaseNewStore.getProjectFiles();
    } else {
      knowledgeBaseNewStore.getPersonalFolder('project');
      knowledgeBaseNewStore.getfiles('project');
    }
  };
  const changeTabs = (key: number) => {
    console.log('切换tab', key, typeof key);
    knowledgeBaseNewStore.setActiveTabKey(key);

    if (key === 1) {
      initPersonalFolder();
    } else {
      initProjectFolder();
    }
  };
  onMounted(async () => {
    if (activeTabKey.value === 1) {
      initPersonalFolder();
    } else {
      initProjectFolder();
    }
  });
</script>

<style scoped lang="less">
  .knowledge-base {
    background-color: #ffffff;
    border-radius: 16px 0 0 16px;
    padding: 20px;
    border: none;
    display: flex;
    flex-direction: column;
    height: 100%;

    .header {
      margin: 0 0 24px 0px;
      height: 24px;
      display: flex;
      justify-content: space-between;

      span {
        font-weight: bold;
        font-size: 24px;
        color: #1d2129;
        line-height: 24px;
      }
    }
  }

  .main {
    flex: 1;
    display: flex;
    overflow: hidden;

    .main-left {
      padding: 12px 20px 20px;
      flex: 1;
      border: 1px solid #d9d9d9;
      border-radius: 8px;
    }

    .main-right {
      margin-left: 20px;
      width: 410px;
    }
  }

  .card-list-icon {
    color: #4e5969;
    cursor: pointer;
  }

  :deep(.arco-tabs) {
    height: 100%;
  }
  :deep(.arco-tabs-content) {
    height: calc(100% - 40px);
    padding-top: 0;
  }
  :deep(.arco-tabs-tab) {
    font-size: 16px;
  }
  :deep(.arco-tabs-nav-type-line .arco-tabs-tab-title) {
    padding: 0;
    line-height: 24px;
  }
  :deep(.arco-tabs-tab-active, .arco-tabs-tab-active:hover) {
    color: #3366ff;
  }
  :deep(.arco-tabs-nav-type-line .arco-tabs-tab) {
    margin: 0 8px;
  }
  :deep(.arco-tabs-tab:not(:first-of-type)) {
    margin-left: 24px;
  }
</style>
