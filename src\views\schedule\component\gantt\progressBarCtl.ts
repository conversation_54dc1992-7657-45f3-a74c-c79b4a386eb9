
let el:any = '';
let recordIndex = 0;
let taskBarRect:any = '';
let currentRecord:any = '';
let offsetXNum = 0;
let newProgress:any = 0
let moveable = false;

//  鼠标移动事件
function mousemoveCtl(e){
  console.log('mouse move======?: ')
  if(moveable){
    offsetXNum += e.offsetX;
    const progress = Math.round(((currentRecord.progress * taskBarRect.width/100 + offsetXNum)/taskBarRect.width*100))
    console.log('progress change: ', offsetXNum, progress, e.offsetX)
    if(progress<=100 && progress >=0){
      el.style.left = Number(el.style.left.replace('px', '')) + e.offsetX + 'px'
      window['ganttInstance']?.updateTaskRecord({...currentRecord, progress}, recordIndex)
      newProgress = progress;
    }
  }
}

//  鼠标松开事件
// function mouseupCtl(e){
//   moveable = false;
//   offsetXNum = 0
//   document.removeEventListener('mousemove', mousemoveCtl)
//   el.removeEventListener('mouseup', mouseupCtl)
//   currentRecord.progress = newProgress;
// }

function documentMouseupCtl(e){
  moveable = false;
  offsetXNum = 0
  console.log('documentMouseupCtl: ')
  document.removeEventListener('mousemove', mousemoveCtl)
  // document.removeEventListener('mouseup', documentMouseupCtl)
  currentRecord.progress = newProgress;
}

//  鼠标按下事件
function mousedownCtl(e){

  moveable = true;
  // if(currentRecord.progress >=0 && currentRecord.progress <= 100){
  //   document.addEventListener('mousemove', mousemoveCtl)
  // }
  // document.addEventListener('mouseup', documentMouseupCtl);
  // document.addEventListener('mousemove', documentMouseupCtl);
  console.log('mousedownCtl: ', e)
  // el.addEventListener('mouseup', mouseupCtl)
}

export const createProgressBar = (rect: any, record: any, index: any) => {
  if(record.id !== currentRecord.id){
    currentRecord = record;
    // newProgress = record.progress
  }
  recordIndex = index;
  taskBarRect = rect;
  //
  const container = document.getElementById('schedule-gantt');
  document.getElementById('progress-ctl') ? container.removeChild(document.getElementById('progress-ctl')) : ''
  el = document.createElement('div');
  el.style.top =  `${rect.top + rect.height}px`
  el.style.left = `${rect.left - 12 + (record.progress * rect.width)/100}px`
  el.id = 'progress-ctl';
  el.style.position = 'absolute';
  el.style.borderLeft = '12px solid transparent';
  el.style.borderRight = '12px solid transparent';
  el.style.borderBottom = '12px solid #D9D9D9';
  // el.style.background = '#ffffff';
  el.style.zIndex = 100;
  el.style.cursor = 'pointer'

  // @ts-ignore
  container.append(el);
  el.addEventListener('mousedown', mousedownCtl);
  document.addEventListener('mousemove', mousemoveCtl)
}


export default null;