import { batchDownloadInfo, fileDownload, fileZipDownload } from '@/api/file';
import { toRefs } from 'vue';
import { storeToRefs } from 'pinia';
import { Message, Notification } from '@arco-design/web-vue';
import i18n from '@/locale';
import { encode } from 'js-base64';
import { download } from '@/utils/file';
import {
  FileAndFolderNodeMessage,
  getChildFolderList,
  getFileList,
} from '@/api/tree-folder';
import { shareFileDownload } from '@/views/share-download/api';
import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
import { updateFolder, updateFile } from '@/views/projectSpace/file/api';

const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
const { project } = storeToRefs(knowledgeBaseNewStore);
const { tableData, currentFolder, folderChildrenFolders, folderChildrenFiles } =
  toRefs(project.value);
const { t } = i18n.global;

// 下载单文件
async function handleSingleFile(file: any) {
  console.log('handleSingleFile', file);
  const res: any = await fileDownload(file);
  download(file, res.data);
}
// 下载压缩包
async function handleZipFile(fileList: any) {
  // 获取批量下载文件信息
  const res: any = await batchDownloadInfo({ fileList });
  if (res.status) {
    const { data } = res;
    let zipFileName = `${t('file-manage.batch-download')}.zip`;

    const downloadRes: any = await fileZipDownload(data);
    // 获取文件名称
    const disposition = downloadRes.headers['content-disposition'];
    if (disposition && disposition.includes('filename=')) {
      const [, encodeFilename] = disposition.split('filename=');
      zipFileName = decodeURI(encodeFilename);
    }
    download({ name: zipFileName }, downloadRes.data);
  } else {
    Message.error(res.message);
  }
}
// 下载项目文件按照项目展示的文件
function downloadSourceProject(crtFile: FileAndFolderNodeMessage) {
  Message.info(t('file-manage.wait-downloading'));

  if (crtFile.type === 'file') {
    console.log('xiazai');
    // 文件
    handleSingleFile(crtFile).then();
  } else {
    // 文件夹
    const files = [{ fileId: crtFile.id, fileType: crtFile.isFileOrFolder }];
    handleZipFile(files).then();
  }
  return crtFile;
}

async function batchDownload(record: any) {
  Message.info(t('file-manage.wait-downloading'));

  // 只选了一个文件，调用下载文件接口
  if (record.length === 1 && record[0].fileType === 1) {
    await shareFileDownload(record[0].fileName, record[0].fileToken);
    return;
  }
  // 批量下载
  const fileList = record.map((item: any) => {
    return {
      fileId: item.fileId,
      fileType: item.fileType,
    };
  });

  handleZipFile(fileList);
}

/**
 * 根据父文件夹id获取父文件夹
 */
function getParentFolder(treeData: any, folderId: any, parentFolder: any) {
  let parent: any;
  if (treeData.length) {
    treeData.forEach((ele: any) => {
      if (ele.id === folderId) {
        parent = parentFolder;
      } else if (!parent && ele.children?.length)
        parent = getParentFolder(ele.children, folderId, ele);
    });
  }
  return parent;
}

function parseFolder(treeData: any, folderId: string) {
  let result: any;
  treeData.forEach((e: any) => {
    const folder = getParentFolder(e.children, folderId, e);
    if (folder) result = folder;
  });
  return result;
}
// 检查文件夹名称是否正确
const checkFolderName = async (
  name: string,
  parentFolderId: string,
  projectId: string
) => {
  if (!name) {
    Message.warning(t('knowledge.input-file-name-tips'));
    return false;
  }

  // 名称长度限制
  if (name.length > 255) {
    Message.warning(t('knowledge.input-file-rule-tips'));
    return false;
  }

  // 禁用字符校验
  const pattern = /^[^\\/:*?"<>|]+$/;
  const regResult = pattern.test(name);
  if (!regResult) {
    Message.warning(t('knowledge.input-file-rule1-tips'));
    return false;
  }

  // 名称重复校验
  const queryRes = await getChildFolderList(
    projectId,
    '',
    'WIP',
    parentFolderId === 'WIP' ? '0' : parentFolderId
  );

  if (queryRes.status) {
    const isExist = Array.isArray(queryRes.data?.list)
      ? queryRes.data.list.some((folder: any) => folder.name === name)
      : false;
    if (isExist) {
      Message.warning(t('knowledge.file-name-exsit'));
      return false;
    }
  }
  return true;
};
// 检查文件名称是否正确
const checkFileName = async (name: string, parentFolderId: string) => {
  if (!name) {
    Message.warning('请输入文件名称！');
    return false;
  }

  // 名称长度限制
  if (name.length > 200) {
    Message.warning('文件名称不能超过200个字符！');
    return false;
  }

  // 名称重复校验
  const queryRes = await getFileList(parentFolderId);
  if (queryRes.status) {
    const isExist = Array.isArray(queryRes.data?.list)
      ? queryRes.data.list.some((file: any) => file.name === name)
      : false;
    if (isExist) {
      Message.warning('文件名称已存在！');
      return false;
    }
  }
  return true;
};
// 给项目文件原始数据设置编辑状态
const setTableDataEditType = (record: any, isEdit: boolean) => {
  console.log('重命名列表文件', record);

  const isProject = currentFolder.value.id === 'project';
  if (isProject) {
    tableData.value.forEach((item) => {
      item.folderVOList?.forEach((vo: any) => {
        if (vo.id === record.id) {
          vo.isEdit = isEdit;
        }
      });
    });
  } else {
    const targetList = record.folderId
      ? folderChildrenFiles.value
      : folderChildrenFolders.value;
    targetList.forEach((item: any) => {
      if (item.id === record.id) {
        item.isEdit = isEdit;
      }
    });
  }
};
const refreshProjectFolderAndFile = () => {
  const isProject = currentFolder.value.id === 'project';
  if (isProject) {
    knowledgeBaseNewStore.getProjectFiles();
  } else {
    knowledgeBaseNewStore.getPersonalFolder('project');
    knowledgeBaseNewStore.getfiles('project');
  }
};
// 重命名文件/文件夹
const renameFileOrFolder = async (record: any, newName: string) => {
  try {
    // 名称没有变化或者名称为空表示退出编辑
    if (
      (record.editFileType && record.name === newName + record.editFileType) ||
      (!record.editFileType && record.name === newName) ||
      newName.trim() === ''
    ) {
      // 给原始数据设置isEdit为false

      setTableDataEditType(record, false);
      Message.info('撤销重命名');
      return;
    }
    // 名称校验
    if (record.editFileType) {
      // 校验文件名称
      const isNameValid = await checkFileName(newName, record.folderId);
      if (!isNameValid) {
        return;
      }
    } else {
      // 校验文件夹名称
      // 取出文件夹的父级文件夹
      const segments = record.idPath.split('/').filter(Boolean); // 移除空字符串
      const parentFolderId = segments[segments.length - 2]; // 倒数第 2 项
      const isNameValid = await checkFolderName(
        newName,
        parentFolderId,
        record.projectId
      );
      if (!isNameValid) {
        return;
      }
    }

    const param: any = {
      ...record,
      id: record.id,
      name: record.editFileType
        ? newName + record.editFileType
        : encode(newName),
      type: 'WIP',
    };
    delete param.path; // 删除路径字段
    const res: any = record.editFileType
      ? await updateFile(param)
      : await updateFolder(param);

    if (res.status) {
      Message.success('重命名成功！');
      refreshProjectFolderAndFile();
    } else {
      console.error('重命名失败：', res.message);
    }
  } catch (error) {
    console.error('重命名失败：', error);
  }
};

export default function useFolderActions() {
  return {
    downloadSourceProject,
    batchDownload,
    getParentFolder,
    parseFolder,
    renameFileOrFolder,
    setTableDataEditType,
    checkFileName,
    refreshProjectFolderAndFile,
  };
}
