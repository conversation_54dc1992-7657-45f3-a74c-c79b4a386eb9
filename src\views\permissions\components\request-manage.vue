<template>
  <div
    class="arco-row arco-row-align-start arco-row-justify-start"
    style="margin-bottom: 20px"
  >
    <div class="arco-col" style="flex: 1 1 0%">
      <a-row>
        <a-col :span="14">
          <a-input
            v-model="searchValueParam"
            placeholder="请输入接口名称、接口路径进行查询"
            @keyup.enter="search"
            allow-clear
            @clear="reset"
          />
        </a-col>
      </a-row>
    </div>

    <div class="arco-col" style="flex: 0 0 86px; text-align: right">
      <a-space
        ><a-button type="outline" @click="search">
          <template #icon> <icon-search /> </template>查询</a-button
        >
        <a-button type="outline" @click="reset"
          ><template #icon><icon-loop /> </template>重置</a-button
        >
        <a-divider direction="vertical" :margin="10" />
        <a-button type="primary" @click="handelAddRequest">新增接口</a-button>
      </a-space>
    </div>
  </div>

  <a-divider :margin="20" style="margin-top: 4px" />
  <div class="table-box">
    <a-table
      stripe
      row-key="id"
      :loading="loading"
      :scroll="scroll"
      :pagination="pagination"
      :columns="columns"
      :data="RequestList"
      :bordered="false"
      table-layout-fixed
      @page-change="onPageChange"
      @page-size-change="onPageSizeChange"
    >
      <template #index="{ rowIndex }">
        {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
      </template>

      <!-- 编辑、查看、删除 -->
      <template #operation="{ record }">
        <a-button type="text" size="small" @click="updateRequest(record)">
          {{ $t('table.opt.edit') }}</a-button
        >
        <!-- <a-button type="text" size="small" @click="viewRole(record.id)">{{
          $t('user-center.table.view')
        }}</a-button> -->
        <a-popconfirm
          content="
              确定删除这个接口吗？
            "
          type="info"
          @ok="handleDelete(record)"
        >
          <a-button type="text" size="small"> 删除 </a-button>
        </a-popconfirm>
      </template>
    </a-table>
  </div>
  <AddRequest
    v-if="addDialogVisible"
    v-model:visible="addDialogVisible"
    :title="dialogTitle"
    :select="selectRecord"
    :type="type"
    @refresh="refreshData"
  />
</template>

<script lang="ts" setup>
  import { computed, ref, onMounted, reactive } from 'vue';
  import AddRequest from './add-requests.vue';
  import { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import { useI18n } from 'vue-i18n';
  import { getRequestList, deleteRequest } from '../api';
  import useLocale from '@/hooks/locale';
  import { Message } from '@arco-design/web-vue';

  const scroll = {
    y: '100%',
  };

  const { t } = useI18n();

  const searchValueParam = ref('');
  // 获取表格数据loading状态
  const loading = ref(false);

  const pagination = reactive({
    showJumper: true,
    showTotal: true,
    showPageSize: true,
    current: 1,
    pageSize: 20,
    total: 0,
    pageSizeOptions: [20, 50, 100],
    pageSizeChangeResetCurrent: true,
  });

  // table的data
  const RequestList = ref<Permission.Api.PageRequestDto[]>([]);
  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('prjMember.column.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 60,
      align: 'left',
    },
    {
      title: '接口名称',
      dataIndex: 'name',
      align: 'left',
      width: 140,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '接口路径',
      dataIndex: 'url',
      align: 'left',
      width: 350,
      ellipsis: true,
      tooltip: true,
    },

    {
      title: '接口类型',
      dataIndex: 'httpMethod',
      align: 'left',
    },
    {
      title: '所属模块',
      dataIndex: 'module',
      align: 'left',
    },
    {
      title: '操作',
      align: 'center',
      slotName: 'operation',
      width: 160,
    },
  ]);
  // 获取table数据
  const getTableData = async (params: any) => {
    const { data } = await getRequestList(params);
    RequestList.value = data.list;
    pagination.total = data.total || 0;
    loading.value = false;
  };

  // 查询
  const search = () => {
    if (loading.value) {
      return;
    }
    loading.value = true;
    const params = {
      pageNo: pagination.current || 1,
      pageSize: pagination.pageSize || 20,
      searchValue: searchValueParam.value,
    };
    getTableData(params);
  };

  // 弹框关闭刷新数据
  const refreshData = () => {
    pagination.current = 1;
    search();
  };
  // 重置
  const reset = () => {
    // formModel.value = {};
    searchValueParam.value = '';
    pagination.current = 1;
    search();
  };
  // table当前页变化
  const onPageChange = (val: any) => {
    pagination.current = val;
    search();
  };
  // table每页条数变化
  const onPageSizeChange = (val: any) => {
    pagination.pageSize = val;
    search();
  };

  const addDialogVisible = ref(false);
  const dialogTitle = ref('');
  const type = ref('');
  const selectRecord = ref<Permission.Api.PageRequestDto>();

  // 新增接口
  const handelAddRequest = () => {
    dialogTitle.value = '新增接口';
    type.value = 'add';
    addDialogVisible.value = true;
  };

  // 编辑接口
  const updateRequest = (record: Permission.Api.PageRequestDto) => {
    selectRecord.value = record;
    dialogTitle.value = '编辑接口信息';
    type.value = 'edit';
    addDialogVisible.value = true;
  };

  // 删除接口
  async function handleDelete(record: Permission.Api.PageRequestDto) {
    const res = await deleteRequest(record.id);
    if (res.status) {
      Message.success('删除成功！');
      search();
    }
  }

  onMounted(async () => {
    search();
  });
</script>

<style scoped lang="less">
  .table-box {
    // height: calc(100% - 230px);
    height: calc(100vh - 394px);
  }
  :deep(.arco-table) {
    height: 100%;
  }
  :deep(.arco-btn-size-small) {
    padding: 0 5px !important;
  }
  :deep(.arco-form-item-label) {
    width: 100px;
  }
  :deep(.arco-col-8) {
    padding: 0 8px;
  }
  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    white-space: nowrap !important;
  }
  .border-box {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 20px;
  }
</style>
