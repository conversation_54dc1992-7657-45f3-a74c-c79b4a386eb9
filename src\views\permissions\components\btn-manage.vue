<template>
  <a-space direction="vertical">
    <a-space
      align="center"
      style="
        justify-content: space-between;
        align-items: flex-start;
        width: 100%;
        padding: 0 10px 0 4px;
      "
    >
      <div class="title"> 按钮列表 </div>
      <a-tooltip content="新增按钮" v-if="currentMenu?.isLeaf">
        <IconPlus
          style="font-size: 20px; color: #666; cursor: pointer"
          @click="handelAddBtn"
        />
      </a-tooltip>
    </a-space>
    <div class="table-box">
      <a-table
        stripe
        row-key="id"
        :loading="loading"
        :scroll="scroll"
        :pagination="false"
        :columns="columns"
        :data="btnList"
        :bordered="false"
        table-layout-fixed
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>

        <!-- 编辑、查看、删除 -->
        <template #operation="{ record }">
          <a-button type="text" size="small" @click="updateBtn(record)">
            {{ $t('table.opt.edit') }}</a-button
          >
          <!-- <a-button type="text" size="small" @click="viewRole(record.id)">{{
          $t('user-center.table.view')
        }}</a-button> -->
          <a-popconfirm
            content="
              确定删除这个按钮吗？
            "
            type="info"
            @ok="handleDelete(record)"
          >
            <a-button type="text" size="small"> 删除 </a-button>
          </a-popconfirm>
        </template>
      </a-table>
    </div>
  </a-space>
  <AddBtn
    v-if="addDialogVisible"
    v-model:visible="addDialogVisible"
    :title="dialogTitle"
    :select-id="selectId"
    :menu-id="currentMenu?.key"
    :type="type"
    @refresh="refreshData"
  />
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import AddBtn from './add-btn.vue';
  import { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import { useI18n } from 'vue-i18n';
  import { deleteBtn, getBtnList } from '../api';
  import { Message } from '@arco-design/web-vue';

  const scroll = {
    y: '100%',
  };

  const { t } = useI18n();

  const searchValueParam = ref('');
  // 获取表格数据loading状态
  const loading = ref(false);

  // table的data
  const btnList = ref<Permission.Api.PageBtnDto[]>([]);
  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('prjMember.column.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 60,
      align: 'left',
    },
    {
      title: '按钮名称',
      dataIndex: 'name',
      align: 'left',
      width: 140,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '按钮编码',
      dataIndex: 'code',
      align: 'left',
      width: 140,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '操作',
      align: 'center',
      slotName: 'operation',
      width: 100,
    },
  ]);
  const currentMenu = ref<Permission.Model.MenuTree>();
  // 获取table数据
  const getBtnListByMenuId = async (menu: Permission.Model.MenuTree) => {
    if (!menu) {
      Message.error('没有找到菜单');
      return;
    }
    loading.value = true;
    currentMenu.value = menu;
    getBtnList(menu.key).then((res) => {
      const { data } = res;
      btnList.value = data;
      if (!data.length) {
        currentMenu.value.hasButtons = false;
      } else {
        currentMenu.value.hasButtons = true;
      }
      loading.value = false;
    });
  };
  // 查询
  const search = () => {
    if (loading.value) {
      return;
    }

    getBtnListByMenuId(currentMenu.value);
  };

  // 弹框关闭刷新数据
  const refreshData = () => {
    search();
  };

  const addDialogVisible = ref(false);
  const dialogTitle = ref('');
  const type = ref('');
  const selectId = ref('');

  // 新增按钮
  const handelAddBtn = () => {
    dialogTitle.value = '新增按钮';
    type.value = 'add';
    addDialogVisible.value = true;
  };

  // 编辑按钮
  const updateBtn = (record: Permission.Api.PageBtnDto) => {
    selectId.value = record.id;
    dialogTitle.value = '编辑按钮信息';
    type.value = 'edit';
    addDialogVisible.value = true;
  };

  // 删除按钮
  async function handleDelete(record: Permission.Api.PageBtnDto) {
    const res = await deleteBtn(record.id);
    if (res.status) {
      Message.success('删除成功！');
      search();
    }
  }
  defineExpose({ getBtnListByMenuId });
</script>

<style scoped lang="less">
  .title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
  .table-box {
    // height: calc(100% - 230px);
    margin-top: 4px;
    height: calc(100vh - 364px);
  }
  :deep(.arco-table) {
    height: 100%;
  }
  :deep(.arco-btn-size-small) {
    padding: 0 5px !important;
  }
  :deep(.arco-form-item-label) {
    width: 100px;
  }
  :deep(.arco-col-8) {
    padding: 0 8px;
  }
  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    white-space: nowrap !important;
  }
  .border-box {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 20px;
  }
</style>
