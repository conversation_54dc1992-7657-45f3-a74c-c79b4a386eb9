export default {
  'menu.org.department': 'Department',

  'department.operation.create': 'Create',

  // columns
  'department.columns.index': 'index',
  'department.columns.name': 'Department Name',
  'department.columns.name-require': 'Department name required',
  'department.columns.abbr-require': 'Department abbreviation required',
  'department.columns.superior-department': 'Superior Department',
  'department.columns.superior-department-require': 'This field is required',
  'department.columns.abbr': 'Department Abbr',
  'department.columns.orgType': 'Department OrgType',
  'department.columns.operations': 'Operations',

  'department.columns.operations.create': 'New child',
  'department.columns.operations.edit': 'Edit',
  'department.columns.operations.delete': 'Delete',

  // actions
  'department.actions.refresh': 'refresh',
  'department.actions.density': 'density',
  'department.actions.add': 'add',
};
