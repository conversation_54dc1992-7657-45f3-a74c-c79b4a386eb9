<template>
  <div class="project-setting">
    <commonTabs v-model="tabKey" :tabs="[]"></commonTabs>

    <div class="border-box">
      <MergeModel></MergeModel>
    </div>

    <!-- <a-tabs default-active-key="merge-model" lazy-load>
      <a-tab-pane
        key="merge-model"
        :title="$t('model-collaboration.merge-models')"
      >
        <MergeModel></MergeModel>
      </a-tab-pane>
      <a-tab-pane
        key="collision-check"
        :title="$t('model-collaboration.collision-check')"
      >
        <CollisionCheck></CollisionCheck>
      </a-tab-pane>
    </a-tabs> -->
  </div>
</template>

<script lang="ts" setup>
  import { onBeforeUnmount, ref, onMounted } from 'vue';
  import useUserStore from '@/store/modules/user/index';
  import MergeModel from './merge-model/index.vue';
  import CollisionCheck from './collision-check/index.vue';
  import { useI18n } from 'vue-i18n';
  import commonTabs from '@/components/common-tabs/index.vue';

  const { t } = useI18n();

  const store = useUserStore();
  const defaultTab = ref(store.standardTab);
  const init = () => {
    const tab = localStorage.getItem('project-setting-default-tab');
    if (tab) {
      defaultTab.value = tab;
    }
  };
  const tabKey = ref('merge-model');

  init();

  onBeforeUnmount(() => {
    localStorage.removeItem('project-setting-default-tab');
  });
</script>

<script lang="ts">
  export default {
    name: 'ProjectSetting',
  };
</script>

<style scoped lang="less">
  .project-setting {
    width: 100%;
    height: 100%;
    padding: 16px 20px;

    overflow: hidden;
  }

  :deep(.arco-tabs-nav-tab-list) {
    .arco-tabs-tab {
      margin: 0 18px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
  .border-box {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 20px;
  }
</style>
