<template>
  <a-space style="justify-content: flex-start; height: 100%" fill align="start">
    <div style="border-right: 1px solid #eee">
      <a-space direction="vertical">
        <a-space
          style="
            justify-content: space-between;
            align-items: flex-start;
            width: 300px;
            padding: 0 10px 0 4px;
          "
        >
          <div class="title"> 菜单列表 </div>
          <a-tooltip content="新增菜单">
            <IconPlus
              style="font-size: 20px; color: #666; cursor: pointer"
              @click="handleAddRootMenu"
            />
          </a-tooltip>
        </a-space>
        <a-scrollbar style="height: calc(100vh - 363px); overflow: auto">
          <a-tree
            v-if="menuData.length"
            style="width: 300px"
            v-model:selected-keys="menuKeys"
            :data="menuData"
            show-line
            default-expand-selected
            @select="handleNodeSelect"
            ref="menuTreeRef"
          >
            <template #extra="nodeData">
              <div
                v-if="currentMenu && currentMenu.key === nodeData.key"
                style="position: absolute; right: 10px; top: 8px"
              >
                <a-space style="justify-content: flex-end">
                  <a-tooltip content="新增子菜单" v-if="!nodeData.hasButtons">
                    <IconPlus
                      style="color: #666"
                      @click="() => handleAddChild(nodeData)"
                    />
                  </a-tooltip>

                  <a-tooltip content="编辑菜单">
                    <icon-edit
                      style="color: #666"
                      @click="() => handleEditMenu(nodeData)"
                    />
                  </a-tooltip>
                  <a-tooltip content="删除菜单">
                    <a-popconfirm
                      content="确认删除菜单吗?"
                      @before-ok="() => handleDelete(nodeData)"
                    >
                      <icon-delete style="color: #666" />
                    </a-popconfirm>
                  </a-tooltip>
                </a-space>
              </div>
            </template>
          </a-tree>
        </a-scrollbar>
      </a-space>
    </div>

    <div style="padding-left: 20px">
      <BtnManage ref="btnRef" />
    </div>

    <AddMenu
      v-if="showModal"
      v-model:visible="showModal"
      :select-id="selectId"
      :select-name="selectName"
      :type="currentType"
      :title="title"
      @refresh="refreshNode"
    />
  </a-space>
</template>
<script lang="ts" setup>
  import { DefineComponent, nextTick, onMounted, ref } from 'vue';
  import { deleteMenu, getMenuList } from '../api';
  import usePermissionStore from '@/store/modules/permission';
  import { storeToRefs } from 'pinia';
  import { getBtnTree } from '@/views/role/api';

  import AddMenu from './add-menu.vue';
  import BtnManage from './btn-manage.vue';
  import { Message } from '@arco-design/web-vue';

  const menuData = ref<Permission.Model.MenuTree[]>([]);

  const btnRef = ref<DefineComponent>();

  const permissionStore = usePermissionStore();
  const menuTreeRef = ref<DefineComponent>();

  const { currentMenu } = storeToRefs(permissionStore);
  const { selectedKeys } = permissionStore;
  const menuKeys = ref<string[]>(selectedKeys);

  // 抽离的节点点击逻辑
  function handleCheckedNode(node: Permission.Model.MenuTree) {
    console.log(node, 106);
    if (!node.isLeaf) {
      getMenuList(node.key).then((res) => {
        const { data } = res;
        if (!data.length) {
          // 删除了最后一个子节点的情况
          node.hasSubMenus = false;
          node.isLeaf = true;
        }

        node.children = res.data.map((item) => {
          return {
            parentId: item.parentId,
            title: item.title,
            code: item.code,
            hasButtons: item.hasButtons,
            isLeaf: !item.hasSubMenus,
            key: item.id,
            children: [],
          };
        });
      });
    }
    btnRef.value?.getBtnListByMenuId(node);
  }

  // 选中菜单事件
  function handleNodeSelect(
    keys: string[],
    data: {
      selected: boolean;
      node: Permission.Model.MenuTree;
    }
  ) {
    const { node } = data;

    if (currentMenu.value.key !== node.key) {
      permissionStore.setSelectKeys(keys);
      permissionStore.setCurrentMenu(node);
      handleCheckedNode(node);
    }
  }

  // 宽度优先递归菜单树
  function dfsRecursive(
    node: RoleMenu.Api.MenuTree
  ): Permission.Model.MenuTree {
    const modelTree: Permission.Model.MenuTree = {
      ...node,
      isLeaf: !(node.children && node.children.length),
      key: node.id,
      children: [],
      hasButtons: !!(node.buttons && node.buttons.length),
    };
    if (!modelTree.isLeaf) {
      modelTree.children = node.children.map((child) => dfsRecursive(child));
    }
    return modelTree;
  }

  function initRootNode() {
    getBtnTree().then((res) => {
      const { data } = res;
      menuData.value = data.map((item) => dfsRecursive(item));
      nextTick(() => {
        if (menuKeys.value.length) {
          const node = menuTreeRef.value?.getSelectedNodes()[0];
          permissionStore.setCurrentMenu(node);
          handleCheckedNode(node);
        } else {
          const defaultNode = menuData.value[0];
          menuKeys.value = [defaultNode.key];
          permissionStore.setSelectKeys(menuKeys.value);
          permissionStore.setCurrentMenu(defaultNode);
          handleCheckedNode(defaultNode);
        }
      });
    });
  }

  const selectId = ref('');
  const selectName = ref('');
  const showModal = ref(false);
  const currentType = ref('add');
  const title = ref('');

  // 添加菜单
  function handleAddRootMenu() {
    selectId.value = '0';
    currentType.value = 'add';
    title.value = '新增菜单';
    showModal.value = true;
  }

  // 添加子菜单
  function handleAddChild(node: Permission.Model.MenuTree) {
    selectId.value = node.key;
    selectName.value = node.title;
    currentType.value = 'add';
    title.value = '新增子菜单';
    showModal.value = true;
  }
  // 编辑菜单
  function handleEditMenu(node: Permission.Model.MenuTree) {
    selectId.value = node.key;
    selectName.value = node.title;
    currentType.value = 'edit';
    title.value = '编辑菜单';
    showModal.value = true;
  }

  // 删除菜单
  async function handleDelete(node: Permission.Model.MenuTree) {
    await deleteMenu(node.key);
    Message.success('菜单删除成功！');
    // 删除顶级菜单，刷新整个列表并定位到第一个菜单
    if (node.parentId === '0') {
      menuKeys.value = [];
      permissionStore.setSelectKeys(menuKeys.value);
      initRootNode();
    } else {
      // 删除其他菜单，定位到父级节点并刷新父级菜单
      menuKeys.value = [node.parentId];
      permissionStore.setSelectKeys(menuKeys.value);
      nextTick(() => {
        const parentNode = menuTreeRef.value?.getSelectedNodes()[0];
        permissionStore.setCurrentMenu(parentNode);
        handleCheckedNode(parentNode);
      });
    }
  }

  // 新增子节点后更新父节点
  function refreshNode(parentId: string) {
    // 新增顶级菜单，刷新整个列表
    if (parentId === '0') {
      initRootNode();
    } else {
      // 新增普通菜单，刷新父级列表
      currentMenu.value.hasSubMenus = true;
      currentMenu.value.isLeaf = false;
      getMenuList(currentMenu.value.key).then((res) => {
        currentMenu.value.children = res.data.map((item) => {
          return {
            ...item,
            isLeaf: !item.hasSubMenus,
            key: item.id,
            children: [],
          };
        });
      });
    }
  }

  onMounted(() => {
    initRootNode();
  });
</script>
<style scoped lang="less">
  .title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
  .menu-title {
    line-height: 32px;
    font-size: 14px;
    font-weight: bold;
    color: #444;
  }
  :deep(.arco-checkbox-label) {
    color: #666;
  }
</style>
