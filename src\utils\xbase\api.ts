import axios from 'axios';
import { getXBaseToken } from '@/utils/auth';

export const modelViewToken = (data: any) => {
  return axios({
    method: 'POST',
    url: '/api/open/v1/viewer/token',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
};

// 查找模型属性
export const queryModelAttributes = (params: any) => {
  return axios({
    method: 'GET',
    url: '/api/open/v1/model/ds/property/query',
    params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
};
// 大象云获取文件详情
export const getDocumentInfo = (params: any) => {
  return axios({
    method: 'GET',
    url: '/api/open/v1/document/document',
    params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
};

// 获取指定构件属性值
export const getComponentProperty = (data: any) => {
  return axios({
    method: 'POST',
    url: '/api/open/v1/semantic-model/element/property/value',
    data,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
};
