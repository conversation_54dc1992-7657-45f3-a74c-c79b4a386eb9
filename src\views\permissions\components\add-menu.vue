<template>
  <a-modal
    :visible="props.visible"
    :title="props.title"
    :width="500"
    :unmount-on-close="true"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-space style="justify-content: center" fill>
      <a-form
        ref="MenuRef"
        style="padding-top: 20px"
        :model="formData"
        auto-label-width
        :disabled="isView"
      >
        <a-form-item
          v-if="props.type === 'add'"
          field="parentName"
          label="父级菜单"
        >
          <span>{{ formData.parentName || '无' }}</span>
        </a-form-item>
        <a-form-item
          field="title"
          label="菜单名称"
          validate-trigger="input"
          :rules="[
            {
              required: true,
              message: '菜单名称未填写',
            },
          ]"
        >
          <a-input
            v-model="formData.title"
            :placeholder="$t('user-center.please-enter-name')"
          />
        </a-form-item>
        <a-form-item
          field="code"
          label="菜单编码"
          validate-trigger="input"
          :rules="[
            {
              required: true,
              message: '菜单编码未填写',
            },
          ]"
        >
          <a-input
            v-model="formData.code"
            :placeholder="$t('user-center.please-enter-name')"
          />
        </a-form-item>
        <a-form-item
          field="path"
          label="菜单路由"
          validate-trigger="input"
          :rules="[
            {
              required: true,
              message: '菜单路由未填写',
            },
          ]"
        >
          <a-input v-model="formData.path" placeholder="请输入路由路径" />
        </a-form-item>
        <a-form-item field="icon" label="菜单图标" validate-trigger="input">
          <a-input v-model="formData.icon" placeholder="请输入菜单图标" />
        </a-form-item>
      </a-form>
    </a-space>
  </a-modal>
</template>

<script lang="ts" setup name="addRole">
  import { ref, computed, toRefs } from 'vue';
  import { FormInstance, Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import { addMenu, getMenuInfo, updateMenuInfo } from '../api';

  const { t } = useI18n();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    selectId: {
      type: String,
      default: '',
    },
    selectName: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
  });

  const formData = ref<Permission.Api.MenuDto>({} as Permission.Api.MenuDto);
  const { selectId, selectName, type } = toRefs(props);

  const isView = computed(() => type.value === 'view');

  const emits = defineEmits(['update:visible', 'refresh']);
  // 绑定form的ref
  const MenuRef = ref<FormInstance>({});

  function initialFormData() {
    if (type.value === 'add') {
      formData.value = {
        parentId: selectId.value,
        parentName: selectName.value,
        code: '',
        title: '',
        path: '',
        icon: '',
      };
    } else if (type.value === 'edit') {
      getMenuInfo(selectId.value).then((res) => {
        const { data } = res;
        formData.value = data;
      });
    }
  }
  initialFormData();

  const handleBeforeOk = async () => {
    const validateRes = await MenuRef.value?.validate();
    if (!validateRes) {
      const data = { ...formData.value };

      if (props.type === 'add') {
        await addMenu(data);
        Message.success('菜单添加成功！');
        emits('refresh', data.parentId);
      } else if (props.type === 'edit') {
        await updateMenuInfo(data);
        Message.success('菜单修改成功！');
      }
      emits('update:visible', false);
    }
  };

  const handleCancel = () => {
    emits('update:visible', false);
  };
</script>

<style lang="less" scoped>
  .detailTitle {
    display: flex;
    padding-bottom: 16px;
    font-size: 16px;
    align-items: center;
    color: #1d2129;
    .titleContent {
      margin-left: 6px;
    }
  }
  .tab-pane {
    padding: 0 10px;
  }
  :deep(.arco-tabs-content) {
    padding-top: 20px;
  }
</style>
