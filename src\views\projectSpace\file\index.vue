<template>
  <div class="project-file">
    <CommonTabs v-model="tabKey" :tabs="tabs" @click-tab="clickTab" />
    <div class="content-area">
      <FilePanel v-if="tabKey === 'file'"></FilePanel>
      <Attachment v-if="tabKey === 'attachment'"></Attachment>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onBeforeMount } from 'vue';
  import CommonTabs from '@/components/common-tabs/index.vue';
  import FilePanel from './components/file-panel/index.vue';
  import Attachment from './components/attachment/index.vue';
  import useFileStore from '@/store/modules/file';
  import { useRoute } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import { setLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';

  const fileStore = useFileStore();
  const route = useRoute();
  const { t } = useI18n();
  const userId = getUserId() || '';

  function init() {
    const lastProjectId = sessionStorage.getItem('file_last_projectId');
    const projectId = route.params.projectId as string;
    if (lastProjectId && lastProjectId !== projectId) {
      // 切换项目，重置项目文件缓存内容
      fileStore.toggleProject();
    }
    sessionStorage.setItem('file_last_projectId', projectId);
  }
  init();

  const tabKey = ref('file');

  const tabs = computed(() => [
    {
      label: t('file-manage.folder'),
      value: 'file',
    },
    {
      label: t('file-manage.attachment'),
      value: 'attachment',
    },
  ]);
  function clickTab() {}
  onBeforeMount(() => {
    const type = route.query.type;
    const projectId = route.params.projectId as string;
    const key = `last_project_${userId}`;
    const oldProjectId = localStorage.getItem(key);
    if (type && projectId && oldProjectId !== projectId) {
      setLocalstorage(key, projectId);
    }
    const file = route.query.file;
    if (type === 'file') {
      tabKey.value = 'file';
      if (file === 'Shared') {
        fileStore.setCurrentIdPath('/SHARED');
      } else if (!!file) {
        if (typeof file === 'string') {
          fileStore.setCurrentIdPath(file);
        }
      }
    }
  });
</script>

<style scoped lang="less">
  .project-file {
    //border: 1px solid red
    padding: 16px 20px;
    height: 100%;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    overflow: hidden;
    .content-area {
      height: calc(100% - 60px);
    }
  }
</style>
