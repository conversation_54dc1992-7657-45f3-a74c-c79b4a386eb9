<template>
  <a-modal
    :visible="visible"
    :mask-closable="false"
    :unmount-on-close="true"
    :ok-loading="submitBtnLoading"
    draggable
    :esc-to-close="false"
    @cancel="cancel"
    @ok="submitData"
  >
    <template #title> {{ $t('check.add-review') }} </template>
    <a-spin style="width: 100%" :loading="loading">
      <div class="content">
        <a-form
          ref="formRef"
          :model="formData"
          label-align="left"
          :rules="rules"
        >
          <a-row :gutter="12">
            <a-col :span="24">
              <a-form-item
                field="processId"
                :label="$t('check.select-process')"
                label-col-flex="80px"
                :validate-trigger="['change', 'input']"
              >
                <a-select
                  v-model="formData.processId"
                  :placeholder="$t('check.please-select')"
                >
                  <a-option
                    v-for="template in processList"
                    :key="`${template.id}-${template.name}`"
                    :value="template.id"
                    :label="template.name"
                  ></a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="12">
            <a-col :span="24">
              <a-form-item
                field="name"
                :label="$t('check.name')"
                label-col-flex="80px"
                :validate-trigger="['change', 'input']"
              >
                <remove-spaces-input
                  v-model="formData.name"
                  :placeholder="$t('check.please-enter')"
                  :max-length="currentLocale === 'en-US' ? 200 : 15"
                  show-word-limit
                />
                <!-- <a-input
                  v-model="formData.name"
                  :placeholder="$t('check.please-enter')"
                /> -->
              </a-form-item>
            </a-col>
          </a-row>
          <a-divider />
          <a-row :gutter="12">
            <a-col :span="24">
              <div class="title">
                <div class="text">
                  <img
                    src="@/assets/images/check/<EMAIL>"
                    alt=""
                    style="width: 17px; height: 17px"
                  />
                  <span class="text-font">{{
                    $t('check.files-to-review')
                  }}</span>
                </div>
                <div class="file-count">
                  <span
                    >{{ $t('check.total') + '：' }}
                    {{ $t('check.file', { count: fileCounts || 0 }) }}
                  </span>

                  <a-button type="text" @click="treeFolderVisible = true">
                    {{ $t('check.add-file') }}
                  </a-button>
                </div>
              </div>
              <div class="file-list-wrap">
                <FileCollapse
                  v-if="fileList?.length"
                  v-model:files="fileList"
                ></FileCollapse>
              </div>
            </a-col>
          </a-row>
        </a-form>
        <TreeFolder
          v-model:visible="treeFolderVisible"
          :title="$t('check.add-file')"
          :ok-function="fileChange"
          :checked-data="fileIdList"
          :show-sys-folder="[3, 4]"
        ></TreeFolder>
        <TreeFolder
          v-if="fileToCheckInfo.id"
          v-model:visible="calculateModal"
          :ok-function="calculateFileChange"
          :checked-data="initFileList"
          :is-calculate="true"
          :show-sys-folder="[3, 4]"
        ></TreeFolder>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import {
    computed,
    defineEmits,
    defineProps,
    onMounted,
    ref,
    watch,
  } from 'vue';
  import FileCollapse from '@/views/check/components/file-collapse/index.vue';
  import TreeFolder from '@/components/tree-folder/index.vue';
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import useLoading from '@/hooks/loading';
  import { getDirectoryFileCounts, getDirectoryFileIds } from '../../api';
  import { getProcess, addCheck } from './api';
  import { useI18n } from 'vue-i18n';
  import { log } from '@braks/revue-draggable/dist/utils';
  import useLocale from '@/hooks/locale';

  const { t } = useI18n();
  const { loading, setLoading } = useLoading(false);

  interface ProcessObj {
    id?: string | number;
    name?: string;
  }

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    fileToCheckInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  const emits = defineEmits(['update:visible', 'submit']);
  const route = useRoute();
  const { currentLocale } = useLocale();
  const processList = ref<ProcessObj[]>([]);
  const getProcessList = () => {
    const params = {
      pageNo: 1,
      pageSize: 100000,
      projectId: route.params.projectId,
    };
    getProcess(params)
      .then((res: any) => {
        if (res.code === 8000000) {
          processList.value.length = 0;
          res.data.list.forEach((item: any) => {
            if (
              item.teamId === localStorage.getItem('check_teamId') ||
              item.teamId === 0
            ) {
              processList.value.push(item);
            }
          });
        }
      })
      .catch((e) => {
        if (e) {
          processList.value = [];
        }
      });
  };
  const formRef = ref();
  const formData = ref({
    name: '',
    processId: '',
    standard: '',
  });
  const rules = ref({
    name: [
      {
        required: true,
        message: t('check.please-enter-name'),
      },
    ],
    processId: [
      {
        required: true,
        message: t('check.please-select-process'),
      },
    ],
    // standard: [
    //   {
    //     required: true,
    //     message: '请选择标准',
    //   },
    // ],
  });
  const fileList = ref([]);

  const submitBtnLoading = ref(false);
  const fileCounts = computed(() => {
    let counts = 0;
    fileList?.value?.forEach((file) => {
      counts += getDirectoryFileCounts(file);
    });
    return counts;
  });
  const fileIds = computed(() => {
    const counts: any[] = getDirectoryFileIds(fileList.value);
    return counts;
  });
  const treeFolderVisible = ref(false);

  const fileChange = async (data: () => Promise<any>) => {
    console.log();

    const files = await data();
    fileList.value = files;
  };

  const getIdByFileList = (fileList2: any[]) => {
    const ids: any[] = [];
    fileList2?.forEach((item) => {
      if ('folderId' in item) {
        ids.push(item.id);
      }
      if (item.children) {
        ids.push(...getIdByFileList(item.children));
      }
    });
    return ids;
  };

  const fileIdList = computed(() => {
    const result = getIdByFileList(fileList.value);
    return result;
  });

  const cancel = () => {
    fileList.value = [];
    emits('update:visible', false);
  };
  const submitData = async () => {
    const params = {
      name: formData.value.name,
      cdeProcessId: formData.value.processId,
      fileIds: fileIds.value,
      projectId: route.params.projectId,
      fileCount: fileCounts.value,
      standardId: 0,
    };
    await formRef.value?.validate();
    if (!params.name || !params.cdeProcessId) {
      Message.error(
        t('check.please-refine-the-review-name-or-process-information')
      );
    } else if (!params.fileCount) {
      Message.error(t('check.please-select-file'));
    } else {
      submitBtnLoading.value = true;
      addCheck(params)
        .then((res: any) => {
          if (res.code === 8000000) {
            Message.success(res.message);
            setTimeout(() => {
              emits('submit', true);
              cancel();
            }, 500);
          }
          submitBtnLoading.value = false;
        })
        .catch((e) => {
          if (e) {
            submitBtnLoading.value = false;
          }
        });
    }
  };

  // 文件进入的回显
  const calculateModal = ref(false);
  const calculateFileChange = async (data: () => Promise<any>) => {
    const files = await data();
    fileList.value = files;
    calculateModal.value = false;
    setLoading(false);
  };
  const initFileList = computed(() => {
    let result = [];
    if (props.fileToCheckInfo.files) {
      result = props.fileToCheckInfo.files.map((item: any) => item.fileId);
    }
    return result;
  });

  const init = () => {
    if (props.fileToCheckInfo.id) {
      setLoading(true);
      calculateModal.value = true;
    }
    formData.value = {
      name: '',
      processId: '',
      standard: '',
    };
    getProcessList();
  };

  onMounted(() => {
    watch(
      () => props.visible,
      (val) => {
        if (val) {
          init();
        }
      }
    );
  });
</script>

<style scoped lang="less">
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .file-list-wrap {
    margin-top: 16px;
    min-height: 20px;
  }
</style>
