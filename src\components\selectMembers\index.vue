<template>
  <a-modal
    :visible="visible"
    width="1000px"
    title-align="start"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title>{{ $t('selectMembers.invite') }}</template>
    <div style="height: 60vh">
      <a-row class="invite-members">
        <a-col :span="14" class="structure-box">
          <a-input-search
            v-model="searchValue"
            :placeholder="$t('selectMembers.search')"
            :allow-clear="true"
            @keyup.enter="searchUserHandle"
            @search="searchUserHandle"
            @clear="clearSearch"
          />
          <div class="structure-body">
            <!-- 面包屑 -->
            <div v-if="tabType !== 3" class="breadcrumb-box">
              <span
                v-if="breadcrumbList.length <= 1"
                style="font-weight: bold"
                >{{ $t('selectMembers.contacts') }}</span
              >
              <a-breadcrumb v-else>
                <template #separator>
                  <icon-right />
                </template>
                <a-breadcrumb-item
                  v-for="(item, index) in breadcrumbList"
                  :key="index"
                  @click="setBreadcrumbHandle(index)"
                >
                  <a-tooltip :content="item.name">
                    <span
                      :class="
                        index === breadcrumbList.length - 1
                          ? 'breadcrumb-name last-breadcrumb'
                          : 'breadcrumb-name'
                      "
                      >{{ item.name }}</span
                    >
                  </a-tooltip>
                </a-breadcrumb-item>
              </a-breadcrumb>
            </div>
            <div
              v-if="(tabType === 0 || tabType === 1) && companyId !== ''"
              class="depart-area data-tab"
            >
              <DepartmentTab
                v-model:breadcrumb-list="breadcrumbList"
                :selected-member="selMemberData"
                :limit="limit"
                @tab-change="tabChangeHandle"
                @change="memberChange"
              ></DepartmentTab>
            </div>
            <div
              v-if="tabType === 0 || tabType === 2"
              class="group-area data-tab"
            >
              <GroupTab
                v-show="!hideGroup"
                v-model:breadcrumb-list="breadcrumbList"
                :selected-member="selMemberData"
                :limit="limit"
                @tab-change="tabChangeHandle"
                @change="memberChange"
              ></GroupTab>
            </div>
            <div v-if="tabType === 3" class="search-area data-tab">
              <SearchTab
                ref="searchTabRef"
                :selected-member="selMemberData"
                @change="memberChange"
                @close="clearSearch"
              ></SearchTab>
            </div>
          </div>
        </a-col>
        <!-- 已选成员 -->
        <a-col :span="10" class="selected-member">
          <div style="height: 30px"
            >{{ $t('selectMembers.selected') }} : {{ selMemberData?.length }}
            {{ $t('selectMembers.person') }}</div
          >
          <div class="member-box">
            <div
              v-for="(item, index) in selMemberData"
              :key="index"
              class="member-list"
            >
              <span v-if="item?.userFullname">
                <span
                  class="surnamed"
                  :class="item.type === 'team' ? 'surnamed2' : 'surnamed1'"
                  >{{ item.userFullname?.charAt(0) }}</span
                >
                {{ item.userFullname }}
              </span>
              <span @click="closeUser(index)"><icon-close /></span>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  // import { Message } from '@arco-design/web-vue';
  import { computed, nextTick, ref, toRaw, watch } from 'vue';
  import DepartmentTab from './components/departmentTab.vue';
  import GroupTab from './components/groupTab.vue';
  import SearchTab from './components/searchTab.vue';
  import { useUserStore, useGlobalModeStore } from '@/store';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const tabType = ref(0);
  const tabChangeHandle = (tab: any) => {
    tabType.value = tab;
  };

  const globalModeStore = useGlobalModeStore();

  const globalMode = globalModeStore.getGlobalMode;

  const userStore = useUserStore();
  const companyId = computed(() => userStore.companyId);

  const props: any = defineProps({
    visible: {
      // 弹窗开关
      type: Boolean,
      required: true,
    },
    data: {
      // 回显数据
      type: Array,
      required: false,
      default() {
        return [];
      },
    },
    limit: {
      // 限制最大选择人数
      type: Number,
      required: false,
    },
    hideGroup: {
      // 隐藏群组
      type: Boolean,
      required: false,
      default: false,
    },
  });
  const emit = defineEmits(['update:visible', 'refresh', 'selectMember']);

  const breadcrumbList = ref([
    {
      id: -1,
      name: computed(() => t('selectMembers.contacts')),
    },
  ]);
  const setBreadcrumbHandle = (index: any) => {
    const list = breadcrumbList.value.slice(0, index + 1);
    breadcrumbList.value = list;
  };

  const selMemberData: any = ref([]);
  const memberChange = (memList: []) => {
    nextTick(() => {
      selMemberData.value = [...memList] || [];
    });
  };
  const closeUser = (index: any) => {
    const a = toRaw(selMemberData.value);
    a.splice(index, 1);
    selMemberData.value = [...a];
    // console.log('ssss: ', selMemberData.value)
  };

  const init = () => {
    if (props.data) {
      selMemberData.value = [...props.data];
    }
  };

  const searchTabRef = ref(null);
  const searchValue = ref('');
  const searchUserHandle = () => {
    tabType.value = 3;
    nextTick(() => {
      if (searchValue.value) searchTabRef.value?.search(searchValue.value);
    });
  };
  const reset = (clearMember = false) => {
    tabType.value = 0;
    breadcrumbList.value = [
      {
        id: -1,
        name: computed(() => t('selectMembers.contacts')),
      },
    ];
    searchValue.value = '';
    if (clearMember) selMemberData.value = [];
  };
  const clearSearch = () => {
    reset();
  };
  const handleOk = () => {
    emit('selectMember', toRaw(selMemberData.value));
    emit('update:visible', false);
    reset(true);
  };
  const handleCancel = () => {
    emit('update:visible', false);
    reset(true);
  };
  watch(
    () => props.visible,
    (val) => {
      if (val) init();
    },
    {
      immediate: true,
    }
  );
</script>

<style lang="less" scoped>
  .invite-members {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    // padding: 20px;
    height: 100%;
    :deep(.arco-col) {
      padding: 20px;
      height: 100%;
    }
    .structure-box {
      border-right: 1px solid #d9d9d9;
      .structure-body {
        //border: 1px solid blue;
        height: calc(100% - 20px);
        display: flex;
        flex-direction: column;
        .search-area {
          height: calc(100% - 12px);
          overflow: auto;
          margin-top: 12px;
          //border: 1px solid red;
        }
        .breadcrumb-box {
          display: flex;
          flex-wrap: wrap;
          :deep(.arco-breadcrumb) {
            flex-wrap: wrap;
          }
          :deep(.arco-breadcrumb-item) {
            max-width: 300px;
          }
          margin: 12px 0;
          .breadcrumb-name {
            display: inline-block;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            //&:last-child{
            //  max-width: 100%;
            //}
          }
          .last-breadcrumb {
            max-width: 100%;
          }
        }
        .data-tab {
          //border: 1px solid blue;
          //flex: 1;
          overflow-y: auto;
          overflow-x: hidden;
        }
      }
    }
    .selected-member {
      height: 100%;
      .member-box {
        overflow: auto;
        margin-top: 8px;
        height: calc(100% - 40px);
        .member-list {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 38px;
          padding: 0 12px;
          margin-bottom: 8px;
          border-radius: 8px;
          // cursor: pointer;
          &:hover {
            background-color: #f2f3f5;
            .surnamed {
              background-color: #3491fa;
            }
          }

          :deep(.arco-icon-close) {
            cursor: pointer;
          }
        }
      }
    }
  }
  .surnamed {
    display: inline-block;
    width: 24px;
    height: 24px;
    font-size: 11px;
    text-align: center;
    line-height: 24px;
    border-radius: 50%;
    color: #ffffff;
    margin-right: 8px;
  }
  .surnamed1 {
    background: #3366ff;
  }
  .surnamed2 {
    background: #18c4eb;
  }

  .serchResult {
    height: 380px;
    overflow: auto;
    :deep(.arco-checkbox-group) {
      padding-top: 16px;
    }
  }
  .pathName {
    color: #86909c;
  }
</style>
