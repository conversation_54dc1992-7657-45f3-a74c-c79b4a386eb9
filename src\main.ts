import { createApp } from 'vue';
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import globalComponents from '@/components/index';
import store from '@/store';
import router from './router/index';
import i18n from './locale';
import directive from './directive';
import App from './App.vue';
import vue3TreeOrg from 'vue3-tree-org';
import 'vue3-tree-org/lib/vue3-tree-org.css';
import '@arco-design/web-vue/dist/arco.css';
import '@/assets/style/global.less';
import '@/assets/style/arcoDesign/form.less';
import '@/assets/style/arcoDesign/table.less';
import '@/api/interceptor';
// 导入截屏插件
import screenShort from 'vue-web-screen-shot';
import { setToken } from '@/utils/auth';

import { initLogCollectionSDK } from '@/utils/log-colletion';
import removeSpacesInput from '@/components/removeSpacesInput/index.vue';
import VCalendar from 'v-calendar';
import 'v-calendar/style.css';
import btnPermission from './directionary/btn-permission';
import '@/hooks/override-session-storage-method';

// 初始化token
const initToken = () => {
  const params = new URLSearchParams(window.location.search);
  const accessToken = params.get('accessToken');
  if (accessToken) {
    setToken(accessToken);
  }
};

const createClient = async (): Promise<void> => {
  // 在创建应用之前初始化token
  initToken();

  const app = createApp(App);
  // 注入按钮名称全局对象
  app.config.globalProperties.$btn = btnPermission;
  // 使用截屏插件
  app.use(screenShort, { enableWebRtc: false });
  app.use(VCalendar); // 日历
  app.use(ArcoVue, {});
  app.use(ArcoVueIcon);
  app.use(vue3TreeOrg);
  app.use(router);
  app.use(store);
  app.use(i18n);
  app.use(globalComponents);
  app.use(directive);
  app.component('RemoveSpacesInput', removeSpacesInput);
  app.mount('#app');
  console.log('app mounted!');
};

createClient();

// 日志上报
// initLogCollectionSDK();
