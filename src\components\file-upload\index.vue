<template>
  <div class="page">
    <!-- <a-button type="primary" class="up-btn" @click="showModal"
      >上传文件</a-button
    > -->
    <a-modal
      :visible="visible"
      :title="$t('task.upload-attachment')"
      centered
      width="420px"
      draggable
      unmount-on-close
      @cancel="handleCancel"
    >
      <!-- :accept="acceptFileType" -->
      <chunk-upload
        ref="uploadRef"
        :value="fileGroup"
        is-multiple
        :disabled="loading"
        :engine-percentages="enginePercentages"
        @file-change="handleFileChange"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
        @line-change="handleLineChange"
      >
        <template #default>
          <a-upload
            ref="uploadRef"
            :file-list="fileArr"
            multiple
            draggable
            :auto-upload="false"
            :custom-request="customRequest"
            :before-upload="beforeUpload"
            :show-file-list="false"
            @change="onChange"
          >
            <template #upload-button>
              <div class="arco-upload-wrapper arco-upload-wrapper-type-picture">
                <span class="arco-upload arco-upload-draggable">
                  <div class="arco-upload-drag">
                    <svg
                      viewBox="0 0 48 48"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      stroke="currentColor"
                      class="arco-icon arco-icon-plus"
                      stroke-width="4"
                      stroke-linecap="butt"
                      stroke-linejoin="miter"
                    >
                      <path d="M5 24h38M24 5v38"></path>
                    </svg>
                    <div class="arco-upload-drag-text"></div>
                  </div>
                </span>
              </div>
            </template>
          </a-upload>
        </template>
      </chunk-upload>
      <template #footer>
        <div class="footer">
          <span>共{{ fileArr.length }}个文件</span>
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button
              id="picker"
              type="primary"
              :disabled="fileArr.length === 0"
              :loading="loading"
              @click="handleSubmit"
              >确定</a-button
            >
          </a-space>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
  import ChunkUpload from '@/components/chunk-upload/index.vue';
  import { ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { storeToRefs } from 'pinia';

  import acceptFileType from '@/config/accept-file-types.json';
  import useUploadFileStore from '@/store/modules/upload-file/index';
  import { useRoute } from 'vue-router';
  import { useI18n } from 'vue-i18n';

  const emits = defineEmits(['uploadComplete']);

  const { t } = useI18n();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
    config: {
      type: Object,
      required: true,
      default() {
        return {
          url: '',
        };
      },
    },
  });
  const uploadFileStore = useUploadFileStore();

  // const uploadfileArr = uploadFileStore.uploadfileArr;

  const { fileArr, loading, uploadFileList } = storeToRefs(uploadFileStore);

  // const modalVisible = ref(false);

  const uploadRef = ref();
  function handleCancel() {
    emits('uploadComplete');
    uploadFileStore.fileArr = [];
    // uploadRef.value.abort();
    // modalVisible.value = false;
  }
  function handleSubmit() {
    uploadFileStore.handleUploadFile('', 0);
  }

  // function showModal() {
  //   visible.value = true;
  // }
  const route = useRoute();
  const onChange = (newFileList) => {
    const file = newFileList[newFileList.length - 1];
    if (!acceptFileType.includes(file.name.split('.').pop())) {
      Message.error(`不能上传${file.name.split('.').pop()}文件`);
      newFileList.pop();
    }

    const newFileArr = newFileList.filter(
      (item, index, self) =>
        index === self.findIndex((item2) => item2.name === item.name)
    );
    if (newFileArr.length !== newFileList.length) {
      Message.error(`不能上传相同文件`);
    }
    newFileArr.forEach((item) => {
      item.type = 'attach';
      // eslint-disable-next-line vue/no-mutating-props
      props.config.projectId = route.params.projectId;
      item.config = props.config;
    });
    // console.log(
    //   props.config,
    //   newFileArr,
    //   'newFileArr--------------------------------'
    // );
    uploadFileStore.handleFileArr(newFileArr);
  };

  // 触发上传完成事件
  watch(loading, (val, oldVal) => {
    if (!val && oldVal) {
      if (uploadFileList.value.length === 0) {
        fileArr.value = [];
        emits('uploadComplete');
      }
    }
  });
  // 暂停上传（是暂停剩下未上传的）
  // const pauseUpload = (taskArrItem, index, elsePause = true) => {
  //   // elsePause为true就是主动暂停，为false就是请求中断
  // };

  // 继续上传
  // const resumeUpload = (taskArrItem) => {
  //   // 2为上传中
  //   taskArrItem.state = 2;
  //   // 把刚才暂停的正在上传中所有切片放到待上传切片列表中
  //   taskArrItem.allChunkList.push(...taskArrItem.whileRequests);
  //   taskArrItem.whileRequests = [];
  //   uploadSignleFile(taskArrItem);
  // };

  // 取消单个
  // const cancelSingle = async (taskArrItem) => {
  //   pauseUpload(taskArrItem);
  //   // 取消上传后列表删除该文件
  //   uploadFileList.value = uploadFileList.value.filter(
  //     (itemB) => itemB.fileHash !== taskArrItem.fileHash
  //   );
  // };

  // 全部取消
  // const cancelAll = () => {
  //   for (const item of uploadFileList.value) {
  //     pauseUpload(item);
  //   }
  //   uploadFileList.value = [];
  // };

  // 点击ui组件单个上传
  // const customRequest = (option) => {
  //   console.log(option, 'customRequest--------------------------------');
  //   handleUploadFile(option);
  // };

  // 输入框change事件
  const handleUploadFile = async function (file) {
    uploadFileStore.handleUploadFile(file);
  };
</script>

<style lang="less" scoped>
  /* 覆盖文件拖拽上传默认样式 */
  :deep(.arco-upload-drag) {
    padding: 40px 0;
    background-color: var(--color-fill-2);
    border-radius: var(--border-radius-medium);
    .arco-icon-plus {
      margin-bottom: 0;
    }
  }

  /* 拖动文件到拖动框内样式 */
  :deep(.webuploader-dnd-over) {
    .arco-upload-drag {
      border: 1px dashed rgb(var(--primary-6));
      svg {
        color: rgb(var(--primary-6));
      }
    }
  }

  /* 拖动框hover样式 */
  :deep(.webuploader-pick-hover) {
    .arco-upload-drag {
      border: 1px dashed var(--color-neutral-4);
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      color: var(--color-text-2);
    }

    /* button {
        border-radius: var(--border-radius-medium);
      } */
  }
</style>
