import { createP<PERSON> } from 'pinia';
import useAppStore from './modules/app';
import useUserStore from './modules/user';
import useTabBarStore from './modules/tab-bar';
import useUploadFileStore from './modules/upload-file';
import useAiStore from './modules/ai';
import useKnowledgeBaseStore from './modules/knowledge-base';
import useKnowledgeBaseStore2 from './modules/knowledge-base2'; // 知识库
import useKnowledgeBaseNewStore from './modules/knowledge-base-new'; // 网盘
import userScheduleStore from './modules/schedule';
import usegGanntDrawerStore from './modules/gannt-drawer'; // 甘特图创建抽屉';
import usePrjPermissionStore from './modules/project-permission';
import useGlobalModeStore from './modules/global-mode';
import usePortalStore from './modules/portal';

const pinia = createPinia();

export {
  useAppStore,
  useUserStore,
  useTabBarStore,
  useAiStore,
  useUploadFileStore,
  useKnowledgeBaseStore,
  useKnowledgeBaseStore2,
  useKnowledgeBaseNewStore,
  userScheduleStore,
  usePrjPermissionStore,
  useGlobalModeStore,
  usePortalStore,
  usegGanntDrawerStore,
};
export default pinia;
