import axios from 'axios';
import { getXBaseToken } from '@/utils/auth';

export interface standardData {
  id: string;
  name: string;
  englishName: string;
  code: string;
  description: string;
  existStandardId: string;
  groupId: string;
  attributeLink: string;
  standardType: number;
  xbaseStandardId: string;
  accuracy: any;
}

export interface standardParams extends Partial<standardData> {
  pageNo: number;
  pageSize: number;
  standardCode?: string;
  standardName?: string;
  groupId?: string;
}

export interface standardListRes {
  list: standardData[];
  total: number;
  message: string;
}
export interface addStandardRes {
  status: boolean;
  data: any;
  message: string;
}

// 查询标准列表-分页
export function standardList(params: standardParams) {
  return axios.get<standardListRes>('/asset-system/standard/list', {
    params,
  });
}

// 创建标准
export function addStandard(data: standardData) {
  const param: standardData = { ...data };
  return axios.post<any>('/asset-system/standard/save', param);
}

/** 大象云新增自定义标准 */
export function addXbaseStandard(data) {
  return axios({
    method: 'POST',
    url: '/api/open/v1/standardization/property',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

/** 大象云自定义标准导入 */
export function uploadXbaseStandard(data) {
  return axios({
    method: 'POST',
    url: '/api/open/v1/standardization/property/upload',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 删除标准
export function deleteStandard(data: any) {
  return axios.post(`/asset-system/standard/remove?ids=${data}`);
}

export const uploadStandardProperty = (data: any) => {
  return axios({
    method: 'POST',
    url: '/api/open/v1/standardization/property/upload',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
};

export function deleteStandadAttribute(params: any) {
  return axios.post(`/api/open/v1/standardization/property/delete`, params, {
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

export function getStandadAttribute(params: any) {
  return axios.get(`/api/open/v1/standardization/property`, {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

export function deleteStandadCollection(params: any) {
  return axios.post(`/api/open/v1/model-check/collection/delete`, params, {
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}
