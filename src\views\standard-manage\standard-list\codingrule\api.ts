import axios from 'axios';
import { PaginationProps } from '@arco-design/web-vue';

export interface AddRuleFormInter {
  id: string;
  name: string;
  regularExpression: string;
  description: string;
}

export interface RuleList {
  id: string;
  index?: string;
  name: string;
  regularExpression: string;
  description: string;
  create_date?: string;
  create_by?: string;
  pageNo?: number;
  pageSize?: number;
  current?: number;
}

export interface addRuleParams {
  id: string;
  name: string;
  regularExpression: string;
  description: string;
  source: string;
  groupId: string;
}

export interface RuleListParams {
  name?: string;
  pageNo?: number;
  pageSize: number;
  current?: number;
}

export interface Pagination extends PaginationProps {
  current: number;
  pageSize: number;
  total?: number;
  pageSizeOptions?: any;
  showTotal: boolean;
  showJumper: boolean;
  showPageSize: boolean;
}

// 规则新增
export function addRule(data: addRuleParams) {
  return axios.post('/cde-collaboration/asset-system/rule/save', data);
}

// 获取规则列表
export function getRuleList(params: RuleListParams) {
  return axios.get('/cde-collaboration/asset-system/rule/page', {
    params,
  });
}

// 编辑规则列表
export function editRule(data: addRuleParams) {
  return axios.put('/cde-collaboration/asset-system/rule/update', data);
}

// 移除规则列表
export function removeRule(id: string) {
  return axios.delete(`/asset-system/rule/${id}`);
}

// 批量删除规则列表
export function multipleDeleteRule(data: any) {
  return axios.delete('/cde-collaboration/asset-system/rule/batchRemove', {
    data,
  });
}
