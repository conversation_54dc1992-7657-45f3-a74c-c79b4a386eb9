<template>
  <a-spin ref="spin" :loading="tableLoading" class="folder-table-panel">
    <a-table
      ref="folderTable"
      :columns="columns"
      :data="tableData"
      :scroll="{ x: 800, y: 'calc(100% - 86px)' }"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
        selectedRowKeys: selectedTableRowkeys,
        onChange: selectionChange,
      }"
      row-key="id"
      :pagination="{
        showTotal: true,
        showPageSize: true,
        showJumper: true,
        defaultPageSize: 10,
        pageSizeOptions: [10, 20, 50, 100],
      }"
      @selection-change="selectionChange"
    >
      <template #name="{ record }">
        <div class="table-name">
          <file-image
            :file-name="record.name"
            :is-sysFile="isSysFolder(record.sysType)"
            :is-file="!!record.folderId"
            style="margin-right: 8px"
          />
          <a-tooltip :content="record.name">
            <span
              style="
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
              @click="clickNameHandle(record)"
              >{{ record.name }}</span
            >
          </a-tooltip>
        </div>
      </template>
      <!-- 转换状态 -->
      <template #status="{ record }">
        <span>{{
          record.fileToken && !isPicFile(record) && isRequiredConversion(record)
            ? formatStatus(record.status, record.isCombination, record)
            : ''
        }}</span>
      </template>
      <template #updateUserName="{ record }">
        <div>{{ record.updateUserName }}</div>
      </template>
      <template #isLocked="{ record }">
        <div v-if="record.abandon === 1" class="locked-box">
          <img src="@/assets/images/file-manager/nullify.png" />作废
        </div>
        <div
          v-else-if="
            (record.type !== 'WIP' || currentFolderRole === 1) &&
            !(record.type === 'SHARED' || record.type === 'PUBLISHED')
          "
          class="locked-box"
        >
          <img
            v-if="!(record.type === 'SHARED' || record.type === 'PUBLISHED')"
            src="@/assets/images/file-manager/read.png"
          />
          {{ t('file-manage.readable') }}
        </div>
        <a-tooltip
          v-else-if="
            record.type === 'WIP' &&
            record.folderId &&
            record.isLocked &&
            currentFolderRole > 1
          "
          :content="`${record.updateUserName}${$t('file-manage.editing')}`"
        >
          <!--            @click="unlock(record)"-->
          <div class="locked-box" style="cursor: pointer">
            <img src="@/assets/images/file-manager/lock.png" />
            {{ t('file-manage.lock') }}
          </div>
        </a-tooltip>
        <div v-else class="locked-box">
          <img src="@/assets/images/file-manager/edit.png" />
          {{ t('file-manage.writeable') }}
        </div>
      </template>
      <template #size="{ record }">
        {{ record.size ? getFileSize(record.size) : '' }}
      </template>
      <template #version="{ record }">
        <a-tag
          v-if="record.version"
          class="tag"
          bordered
          color="cyan"
          @click="showVersions(record)"
          >V{{ record.version }}</a-tag
        >
      </template>
      <template #updateDate="{ record }">
        {{ record.updateDate || '' }}
      </template>
    </a-table>
  </a-spin>
  <!-- 图片预览 -->
  <ImgViewer v-if="imgViewModel.visible" :viewModal="imgViewModel"></ImgViewer>
  <!-- <DrawerVersion v-model:show="versionShow" :file="currentFile!" /> -->
  <move-dialog
    v-model:visible="moveModal"
    :title="$t('file-manage.select-target-folder')"
    :ok-function="beforeMoveOkHandler"
    :show-type="[]"
    :dis-check-hierarchy="[1]"
    check-type="single"
    output-type="id"
    is-clear-key
  ></move-dialog>
</template>

<script setup lang="ts">
  import { defineEmits, watch, ref, computed, toRaw, toRefs } from 'vue';
  import { useI18n } from 'vue-i18n';
  import {
    FileAndFolderNodeMessage,
    FileMessage,
    FolderMessage,
    getFileList,
  } from '@/api/tree-folder';
  import { getFileSize } from '@/utils/file';
  import FileImage from './image-file.vue';
  // import DrawerVersion from '../drawer-version.vue';

  import useFileStore from '@/store/modules/file/index';
  import { storeToRefs } from 'pinia';
  import ImgViewer from '@/components/imgView/index.vue';

  import { useRoute } from 'vue-router';
  import modelViewBim from '@/utils/common/view';
  import { downloadSource } from '@/views/projectSpace/file/hooks/events';

  import MoveDialog from '@/components/tree-folder/index.vue';
  import {
    isPicFile,
    isRequiredConversion,
    isWpsFile,
    isNeedToConvert,
    isSysFolder,
    isTopFolder,
  } from '@/views/projectSpace/file/utils';
  import {
    deleteApi,
    moveFileAndFolder,
    reConvertApi,
  } from '@/views/projectSpace/file/api';
  import { Message, Notification } from '@arco-design/web-vue';
  import { wpsViewHandle } from '@/hooks/wps';
  import usePrjPermissionStore from '@/store/modules/project-permission';

  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';

  const knowledgeStore = useKnowledgeBaseNewStore();
  const { referenceModal } = storeToRefs(knowledgeStore);
  const {
    selectedKeys,
    expandedKeys,
    tableLoading,
    currentFolder,
    selectedTableRowkeys,
  } = toRefs(referenceModal.value);

  const { t } = useI18n();

  const fileStore = useFileStore();
  const route = useRoute();
  console.log('currentFolder', currentFolder.value);
  // const { hiddenSlot, tableLoading } = storeToRefs(fileStore);

  const emits = defineEmits(['expendFolder', 'handleDownload']);

  const prjStore = usePrjPermissionStore();
  const currentFolderRole = computed(() => {
    const currentTeam = (prjStore.teamList as any[]).filter((item) => {
      return item.id === currentFolder.value.teamId;
    })[0];
    return currentTeam ? currentTeam.role : 5;
  });

  const moveModal = ref(false);

  const columns = computed(() => {
    return [
      {
        title: t('file-manage.name'),
        dataIndex: 'name',
        slotName: 'name',
        sortable: {
          sortDirections: ['ascend', 'descend'],
        },
        width: 120,
        fixed: 'left',
      },
      {
        title: t('file-manage.regenerator'),
        dataIndex: 'updateUserName',
        slotName: 'updateUserName',
        width: 120,
      },
      {
        title: t('file-manage.update-date'),
        width: 180,
        dataIndex: 'updateDate',
        slotName: 'updateDate',
      },
    ];
  });

  const fileList = ref([]);
  const folderList = ref<FolderMessage[]>([]);

  const getFiles = async () => {
    if (!isTopFolder(currentFolder.value.id)) {
      const res: any = await getFileList(currentFolder.value.id!);
      if (res.status) {
        const list = res?.data?.list;
        fileList.value = list?.length
          ? list.map((item: any) => {
              item.type = currentFolder.value.type;
              return item;
            })
          : [];
      }
    }
  };

  const tableData = computed(() => referenceModal.value.tableData);

  function selectionChange(rowkeys: string[]) {
    knowledgeStore.setSelectedTableRowkeys(rowkeys);
  }
  const folderTable = ref();
  async function refreshTableData(folder?: FolderMessage) {
    tableLoading.value = true;
    folderList.value =
      (folder ? folder.children : currentFolder.value.children) || [];
    fileList.value = [];

    await getFiles();

    const folders = toRaw(folderList.value);
    const files = toRaw(fileList.value);

    knowledgeStore.setTableData([
      ...folders.filter((_folder) => _folder.id !== 'add'),
      ...files,
    ]);

    folderTable?.value.selectAll(false);
    knowledgeStore.setSelectedTableRowkeys([]);
    tableLoading.value = false;
  }

  function handleDownload(record: FileAndFolderNodeMessage) {
    emits('handleDownload');
    downloadSource(record);
  }

  watch(
    () => currentFolder.value,
    async (folder: any) => {
      // // 更新当前文件夹的系统类型
      // if (folder && folder.sysType) {
      //   hiddenSlot.value = Math.min(Math.max(folder.sysType, 1), 4) || 5; // 确保值在 1 到 4 之间，否则默认为 5
      // } else {
      //   hiddenSlot.value = 5;
      // }
      console.log('这里');
      if (!folder.id) return;

      await refreshTableData(folder);
    },
    {
      immediate: true,
      deep: true,
    }
  );

  const versionShow = ref(false);
  const currentFile = ref<FileMessage>();
  function showVersions(file: FileAndFolderNodeMessage) {
    currentFile.value = file;
    versionShow.value = true;
  }

  const imgViewModel = computed(() => fileStore.imgViewModal);

  async function clickNameHandle(record: any) {
    if (!record.folderId) {
      // 文件夹点击事件，进入下一层
      emits('expendFolder', record);
      return;
    }

    // 文件点击事件，查看文件
    let needParams: any = {
      noIssue: [1, 2, 3, 4].includes(hiddenSlot.value),
    };
    if (record.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: record.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      needParams = { ...params, ...needParams };
    }
    modelViewBim(record, route.params.projectId as string, needParams);
  }

  const formatStatus = (status: number, type: number, record: any) => {
    if (type === 2) {
      // 碰撞检查
      if ([-1].includes(status)) return t('file-manage.failed');
      if ([2].includes(status)) return t('file-manage.success');
      if ([0, 1].includes(status)) return t('file-manage.in-conversion');
    } else if (type === 1) {
      return t('file-manage.success');
    } else {
      if (isWpsFile(record)) return t('');
      // 普通模型文件
      if ([-7, -2, -1].includes(status)) return t('file-manage.failed');
      if ([0, 3].includes(status)) return t('file-manage.success');
      if ([-3, 1, 4].includes(status)) return t('file-manage.in-conversion');
      if (status === 2) return t('file-manage.in-queue');
      if (!status) return t('file-manage.not-starts'); // 新增模型状态--未开始
    }
    return '';
  };

  const reConvert = async (record: any) => {
    const params = {
      fileId: record.id,
    };
    const res: any = await reConvertApi(params);
    if (res.code === 8000000) {
      Message.info(t('file-manage.reconvert'));
      refreshTableData().then();
    }
  };

  function deleteFileOrFolder(record: FileMessage) {
    const params: any = {
      targetTeamId: record.teamId,
    };
    if (record.folderId) {
      params.fileIds = [record.id];
    } else {
      params.folderIds = [record.id];
    }

    deleteApi(params)
      .then((res: any) => {
        if (res.code === 8000000) {
          Notification.success({
            id: 'delete',
            title: 'Success',
            content: t('file-manage.success'),
          });
          if (record.folderId) {
            refreshTableData().then();
          } else {
            emits('expendFolder', currentFolder.value);
          }
        }
      })
      .catch((error) => {
        console.error('请求错误', error);
      });
  }

  let beforeMoveOkHandler = (treeDataPromise: () => Promise<any>) =>
    Promise.resolve(true);

  function batchMoveHandle() {
    beforeMoveOkHandler = async function moveRequest(
      treeDataPromise: () => Promise<any>
    ): Promise<boolean> {
      const parentId: string = await treeDataPromise();
      if (!parentId) {
        Message.error(t('file-manage.select-target-folder'));
        return false;
      }
      const ids = fileStore.moveIds;

      if (ids.folderIds.includes(parentId)) {
        Message.error('无法将文件夹移动到自己目录下');
        return false;
      }
      const res: any = await moveFileAndFolder(
        ids.fileIds,
        ids.folderIds,
        parentId
      );
      const result: any = res;
      emits('expendFolder', currentFolder.value);

      if (result.code === 8000000) {
        Message.success(t('file-manage.success'));
        return true;
      }
      return false;
    };
    moveModal.value = true;
  }

  function optEventsHandle(event: string, record: FileAndFolderNodeMessage) {
    if (event === 'move') {
      beforeMoveOkHandler = async function moveRequest(
        treeDataPromise: () => Promise<any>
      ): Promise<boolean> {
        const parentId: string = await treeDataPromise();
        if (!parentId) {
          Message.error(t('file-manage.select-target-folder'));
          return false;
        }
        const ids = fileStore.moveIds;
        const res: any = await moveFileAndFolder(
          ids.fileIds,
          ids.folderIds,
          parentId
        );
        const result: any = res;
        if (record.folderId) {
          refreshTableData().then();
        } else {
          emits('expendFolder', currentFolder.value);
        }
        if (result.code === 8000000) {
          Message.success(t('file-manage.success'));
          return true;
        }
        return false;
      };
      moveModal.value = true;
    } else if (event === 'delete') {
      deleteFileOrFolder(record);
    } else if (event === 'nullify') {
      if (record.folderId) {
        refreshTableData().then();
      } else {
        emits('expendFolder', currentFolder.value);
      }
    }
  }

  defineExpose({ batchMoveHandle });
</script>

<style scoped lang="less">
  :deep(.arco-table-content .arco-scrollbar:nth-child(2)) {
    height: 100%;
  }
  :deep(.arco-table-header + .arco-scrollbar-track-direction-horizontal) {
    display: none;
  }
  .folder-table-panel {
    width: 100%;
    height: 100%;
    padding: 0 12px;
  }
  .table-name {
    display: flex;
    align-items: center;
    color: rgb(22, 93, 255);
    cursor: pointer;
  }
  :deep(.arco-btn-size-small) {
    padding: 0 0;
    margin-right: 8px;
    font-size: 13px;
  }
  :deep(.arco-table-container) {
    height: calc(100% - 60px);
  }
</style>
