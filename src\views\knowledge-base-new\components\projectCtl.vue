<template>
  <a-dropdown
    class="prj-dropdown"
    position="bl"
    :popup-max-height="500"
    @select="projectChange"
    @popup-visible-change="projectListShow"
  >
    <a-button
      class="project-select"
      style="
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px 4px 4px 4px;
      "
    >
      <span class="project-name"> {{ currenPrjName }} </span>
      <icon-down :size="16" />
    </a-button>
    <template #content>
      <div class="project-list">
        <a-tabs>
          <a-tab-pane key="1" :title="t('cloud.project')">
            <div class="pro-tab-wrap">
              <div class="search-wrap">
                <a-input-search
                  v-model="searchValue"
                  :placeholder="$t('navbar.logo.enter')"
                  allow-clear
                  @search="searchHandle"
                  @clear="searchHandle"
                  @keydown.enter="searchHandle"
                ></a-input-search>
              </div>
              <div class="pro-scroll">
                <!-- 为了不影响滚动加载 loading放在外部 -->
                <a-spin v-if="loading" dot> </a-spin>
                <div class="project-items" @scroll="projectScroll">
                  <a-doption
                    v-for="project in projectList.filter((e) => {
                      return e?.isTemplate === 0;
                    })"
                    :id="`pro-${project.id}`"
                    :key="project.id"
                    :value="project.id"
                  >
                    <div class="pro-item">
                      <img
                        v-if="currentProjectId === project.id"
                        src="@/assets/images/common/prefix.png"
                        alt=""
                        height="22"
                        width="4"
                      />
                      <img
                        v-else
                        src="@/assets/images/common/prefix1.png"
                        alt=""
                        height="22"
                        width="4"
                      />
                      <a-tooltip :content="project.name" position="top">
                        <span class="pro-name">{{ project.name }}</span>
                      </a-tooltip>
                    </div>
                  </a-doption>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
  import {
    computed,
    nextTick,
    onMounted,
    ref,
    defineProps,
    watch,
    toRefs,
  } from 'vue';
  import { getProjectList } from '@/api/project';
  import { queryProjectDetail } from '@/views/project-setting/projectNew/api';
  import { useI18n } from 'vue-i18n';
  import { useRoute, useRouter } from 'vue-router';
  import { setLocalstorage, getLocalstorage } from '@/utils/localstorage';
  import { storeCurrentProjectId } from '@/api/storage-project';
  import { useGlobalModeStore } from '@/store';
  import { getUserId } from '@/utils/auth';
  import useFileStore from '@/store/modules/file';
  import useProjectStore from '@/store/modules/project/index';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import { storeToRefs } from 'pinia';

  const projectState = useKnowledgeBaseNewStore();
  const { referenceModal } = storeToRefs(projectState);
  const { currentProjectId, currentProject } = toRefs(referenceModal.value);
  const { t } = useI18n();

  const route: any = useRoute();
  const router: any = useRouter();

  const props = defineProps({
    mode: {
      type: String,
      default: '',
    },
  });

  // const projectList = ref<ProjectRecord[]>([]);
  const projectList = ref([]);
  const searchValue = ref('');

  const allPage = ref(1);
  const pageSize = 20;

  // 上拉加载数据
  const loading = ref(false);
  const addProjectList = async (pageNo: number) => {
    const params = {
      pageNo,
      pageSize,
      projectType: 0,
      name: searchValue.value,
    };
    loading.value = true;
    const res: any = await getProjectList(params);
    if (res.status && res?.data?.list) {
      projectList.value.push(...res.data.list);
      loading.value = false;
    }
  };

  let index = 1;

  const getList = async (isSearch?: any) => {
    index = 1;
    const params = {
      pageNo: 1,
      pageSize,
      projectType: 0,
      name: searchValue.value,
    };
    loading.value = true;
    const res = await getProjectList(params);
    if (res.status) {
      allPage.value = Math.ceil(res.data.total / pageSize);
      // isSearch为false时 会在列表中过滤掉当前项目数据（ 搜索时<isSearch为true>不会过滤） 后面再添加到第一位
      if (!isSearch && currentProjectId.value) {
        res.data.list = res.data.list.filter(
          (item: any) => item.id !== currentProjectId.value
        );
      }
      projectList.value = res.data?.list || [];
      loading.value = false;
    }
  };
  const currenPrjName = computed(() => {
    return (
      currentProject.value?.name ||
      // projectList.value.find((v) => v.id === currentProjectId.value)?.name ||
      t('cloud.select-project')
    );
  });

  const userId = getUserId() || '';
  const projectChange = async (
    id: string | number | Record<string, any> | undefined
  ) => {
    projectState.setReferenceCurrentProjectId(id as string);
    // 设置当前所选项目数据
    const paramsNow = {
      id,
    };
    const res = (await queryProjectDetail(paramsNow)).data;
    projectState.setReferenceCurrentProject(res);
  };

  const projectListShow = async (visible: boolean) => {
    index = 1;
    if (visible) {
      searchValue.value = '';
      projectList.value = [];
      // await nextTick();
      await getList();
      if (currentProject.value) projectList.value.unshift(currentProject.value);
      nextTick(() => {
        const ele: any = document.getElementById(
          `pro-${currentProjectId.value}`
        );
        // eslint-disable-next-line no-unused-expressions
        ele ? (ele.style.backgroundColor = 'rgba(229, 230, 235, 0.5)') : '';
        ele?.scrollIntoView({ behavior: 'instant', block: 'center' });
      });
    }
  };

  const projectScroll = (event: any) => {
    const { scrollTop, clientHeight, scrollHeight } = event.target;
    const threshold = 60;
    // 当前翻页小于总页数 并且上次数据已经请求渲染完了后 翻动到底部 加载下页数据
    if (index < allPage.value && !loading.value) {
      if (scrollTop + clientHeight >= scrollHeight - threshold) {
        index++;
        addProjectList(index);
      }
    }
  };

  // 添加当前选择的项目到第一个
  const setFirstProject = async () => {
    if (currentProjectId.value) {
      await getList();
      if (currentProject.value) projectList.value.unshift(currentProject.value);
    }
  };

  watch(
    () => currentProjectId.value,
    async () => {
      setFirstProject();
    },
    {
      immediate: true,
    }
  );

  const searchHandle = async () => {
    await getList(true);
  };
  onMounted(() => {
    // getList();
  });
</script>

<style scoped lang="less">
  .prj-dropdown {
    margin-right: 8px;
    .project-list {
      max-height: 400px;
      width: 400px;
      overflow: hidden;
      .pro-tab-wrap {
        width: 100%;
      }
      .search-wrap {
        padding: 12px;
        padding-top: 0px;
      }
      .pro-scroll {
        height: 300px;
        //border: 1px solid red;
        overflow: hidden;
        position: relative;
        .arco-spin {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 99999;
        }
        .project-items {
          overflow: auto;
          height: 100%;
          .pro-item {
            display: flex;
            align-items: center;
            align-content: center;
            .pro-name {
              margin-left: 8px;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
    :deep(.project-select) {
      margin-left: 16px;
      padding: 0;
      background-color: transparent;
      display: flex;
      align-items: center;
      align-content: center;
      .project-name {
        display: inline-block;
        max-width: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    :deep(.arco-icon-down) {
      margin-left: 8px;
    }
  }
  .project-name {
    min-width: 200px;
    display: inline-block;
    max-width: 400px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
  }
</style>
