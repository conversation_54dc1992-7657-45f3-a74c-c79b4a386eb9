<template>
  <div v-if="showModal">
    <!-- <div class="modal-overlay" /> -->
    <a-modal
      class="account-switcher-modal"
      :footer="null"
      width="100%"
      :body-style="{ padding: '0' }"
      :mask-closable="false"
      :visible="showModal"
      @cancel="closeModal(false)"
      :maskStyle="{ background: '#e5e6eb', animation: 'none' }"
      :fullscreen="true"
      :closable="false"
    >
      <div class="modal-container">
        <div class="left-content">
          <div class="content-wrapper">
            <div class="switch-title">
              <img
                class="logo-image"
                alt="logo"
                src="@/assets/images/logo-signUp.png"
              />
              <div class="switch-title-text">CDex</div>
            </div>
            <div class="card-content">
              <div class="back-button" @click="closeModal(false)">
                <icon-left />
                <span>{{ $t('account.switcher.back') }}</span>
              </div>
              <a-spin
                :loading="isLoading"
                :tip="$t('account.switcher.loading')"
              >
                <div class="content-inner">
                  <h3 class="title">{{ $t('account.switcher.title') }}</h3>
                  <p class="description">
                    {{ maskedPhone }}
                    {{ $t('account.switcher.description') }}
                  </p>
                  <div class="account-list">
                    <!-- <div
                    v-for="account in accounts"
                    :key="account.id"
                    class="account-item"
                    :class="{
                      'account-hover':
                        hoverAccountId === account.id ||
                        account.id === companyId,
                    }"
                    @mouseenter="hoverAccountId = account.id"
                    @mouseleave="hoverAccountId = null"
                    @click="handleAccountSelect(account)"
                  > -->
                    <div
                      v-for="account in accounts"
                      :key="account.id"
                      class="account-item"
                      @mouseenter="hoverAccountId = account.id"
                      @mouseleave="hoverAccountId = null"
                      @click="handleAccountSelect(account)"
                    >
                      <div :class="['account-icon', account.type]">
                        {{ account.icon }}
                      </div>
                      <div class="account-info">
                        <div class="account-name">{{ account.name }}</div>
                        <div class="account-user">{{
                          account.userFullName
                        }}</div>
                      </div>
                      <div class="arrow-icon">›</div>
                    </div>
                  </div>
                </div>
              </a-spin>
            </div>
          </div>
        </div>
        <div class="right-banner">
          <img
            src="@/assets/images/login-bg2.png"
            alt="banner"
            class="banner-image"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, watch, onMounted, watchEffect } from 'vue';
  import { useUserStore } from '@/store';
  import { Message } from '@arco-design/web-vue';
  import { getCompanyListBySelf } from '@/api/user';
  import { useI18n } from 'vue-i18n';
  import { useRouter } from 'vue-router';

  const { t } = useI18n();
  const router = useRouter();

  // 定义 props
  const props = defineProps({
    showModal: {
      type: Boolean,
      required: true,
      default: false,
    },
    // 添加触发加载的标识
    shouldLoad: {
      type: Boolean,
      default: true,
    },
  });

  const userStore = useUserStore();
  const companyId = computed(() => userStore.companyId);
  const name = computed(() => userStore.name);
  const userStorePhone = computed(() => userStore.phone);
  const userId = computed(() => userStore.id);

  // 添加加载状态变量
  const isLoading = ref(false);

  const maskedPhone = computed(() => {
    const phone = userStorePhone.value;
    if (!phone) return '';

    // 处理国际号码
    if (phone.startsWith('+')) {
      const countryCodeMatch = phone.match(/^\+\d{1,2}/);
      if (countryCodeMatch) {
        const countryCode = countryCodeMatch[0];
        const remainingPart = phone.slice(countryCode.length);
        // console.log(remainingPart);
        // console.log(countryCode);
        if (remainingPart.length >= 4) {
          return `${countryCode}${remainingPart.slice(
            0,
            3
          )}****${remainingPart.slice(-4)}`;
        }
      }
    } else if (phone.length >= 7) {
      return `${phone.slice(0, 3)}****${phone.slice(-4)}`;
    }

    return phone;
  });

  // 定义 emit
  const emit = defineEmits(['update:showModal', 'select']);

  const hoverAccountId = ref(null);

  const accounts = reactive([]);

  const closeModal = (flag) => {
    // 如果正在加载中，不关闭弹窗
    if (isLoading.value && !flag) {
      return;
    }
    emit('update:showModal', false);
  };

  const handleAccountSelect = async (account) => {
    // 设置加载状态为 true
    isLoading.value = true;

    const userByStorage = localStorage.getItem(`work_user_id`);

    let isSameScene = true;

    // 有登录信息
    if (userByStorage) {
      const { redirect } = router.currentRoute.value.query;
      // 有默认跳转页面且为项目下页面
      if (redirect && redirect.includes('.project.')) {
        // 是上一次登录的账号，并且选的组织相同
        if (userByStorage === userId.value && account.id === companyId.value) {
          isSameScene = true;
        } else {
          isSameScene = false;
        }
      }
    }

    // 设置公司ID
    userStore.setCompanyId(account.id);

    // 触发选择事件，传递account和比较结果
    emit('select', { account, isSameScene });

    // 延迟关闭弹窗，等待路由跳转完成
    // 这里不调用 closeModal，由父组件在路由跳转完成后关闭弹窗
  };

  const loadCompanyList = async () => {
    try {
      isLoading.value = true;
      const { data } = await getCompanyListBySelf();

      // 情况1: 如果数据是null或者数组的长度为0，保留私人空间，并自动选择它
      if (!data || data.length === 0) {
        accounts.splice(0, accounts.length, {
          id: '',
          type: 'personal',
          name: computed(() => t('account.switcher.personal')),
          userFullName: name,
          icon: '个',
        });
        // 自动触发私人空间的选择
        setTimeout(() => {
          handleAccountSelect(accounts[0]);
        }, 0);
        return;
      }

      // 情况2: 如果数据长度为1，accounts只展示这个数据
      if (data.length === 1) {
        const item = {
          id: data[0].id,
          type: 'organization',
          name: data[0].companyName,
          userFullName: name,
          icon: '企',
        };
        accounts.splice(0, accounts.length, item);
        // 自动触发这个唯一数据的选择
        setTimeout(() => {
          handleAccountSelect(item);
        }, 0);
        return;
      }

      // 情况3: 如果数据长度大于1，accounts只展示这些数据
      if (data.length > 1) {
        const companyAccounts = data.map((company) => ({
          id: company.id,
          type: 'organization',
          name: company.companyName,
          userFullName: name,
          icon: '企',
        }));
        accounts.splice(0, accounts.length, ...companyAccounts);
      }
    } catch (error) {
      console.error('获取公司列表失败', error);
    } finally {
      isLoading.value = false;
    }
  };

  watch(
    () => props.showModal,
    async (newVal) => {
      console.log('props.showModal && props.shouldLoad', props.showModal);
      if (newVal) {
        await loadCompanyList();
      }
    },
    {
      immediate: true,
    }
  );
  // watchEffect(async () => {
  //   if (props.showModal && props.shouldLoad) {
  //     await loadCompanyList();
  //   }
  // });
</script>

<style scoped lang="less">
  .account-switcher-modal {
    :deep(.arco-modal-body) {
      padding: 0;
    }

    :deep(.arco-modal-mask) {
      background-color: #e5e6eb !important;
    }

    :deep(.arco-modal-header) {
      display: none;
    }
  }

  .modal-container {
    display: flex;
    height: 100vh;
    width: 100%;

    .left-content {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #ffffff;
      min-width: 600px;

      .content-wrapper {
        position: relative;
        width: 400px;
        background: #fff;

        .switch-title {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 80px;
          margin-bottom: 20px;
          padding: 20px;

          .logo-image {
            width: 80px;
            height: 80px;
          }

          .switch-title-text {
            flex: 1;
            margin-left: 6px;
            font-family: DingTalk JinBuTi;
            font-size: 60px;
            color: #222f56;
            line-height: 65px;
            letter-spacing: 2px;
            -webkit-text-stroke: 1px #222f56;
            text-align: left;
          }
        }

        .card-content {
          background: #ffffff;
          box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          border: 1px solid #d9d9d9;
          min-width: 400px;

          :deep(.arco-spin-mask) {
            // background-color: #F2F3F5;
            background-color: rgba(255, 255, 255, 0.85);
          }

          .back-button {
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #4e5969;
            font-size: 14px;
            border-bottom: 1px solid #e5e6eb;
            padding: 20px;

            &:hover {
              color: #165dff;
            }

            .icon-left {
              margin-right: 4px;
            }
          }

          .content-inner {
            padding: 20px;

            .title {
              margin: 0;
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 18px;
              color: #1d2129;
              line-height: 24px;
            }

            .description {
              font-family: PingFang SC;
              font-weight: 500;
              font-size: 14px;
              color: #86909c;
              line-height: 22px;
            }

            .account-list {
              display: flex;
              flex-direction: column;
              gap: 8px;

              .account-item {
                display: flex;
                align-items: center;
                padding: 12px;
                border-radius: 8px;
                cursor: pointer;
                transition: background-color 0.2s;
                border: 1px solid #e5e6eb;

                &:hover,
                &.account-hover {
                  background-color: #f2f3f5;
                }

                .account-icon {
                  width: 44px;
                  height: 44px;
                  border-radius: 8px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  font-size: 14px;
                  color: white;
                  margin-right: 12px;

                  &.organization {
                    background-color: #165dff;
                  }

                  &.personal {
                    background-color: #00b42a;
                  }
                }

                .account-info {
                  flex: 1;

                  .account-name {
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 16px;
                    color: #1d2129;
                    line-height: 21px;
                    margin-bottom: 2px;
                  }

                  .account-user {
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 14px;
                    color: #4e5969;
                    line-height: 21px;
                  }
                }

                .arrow-icon {
                  color: #c9cdd4;
                  font-size: 16px;
                }
              }
            }
          }
        }
      }
    }

    .right-banner {
      flex: 1;
      height: 100%;
      overflow: hidden;

      .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #e5e6eb;
    z-index: 250;
  }
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    padding: 10px;
  }
</style>
