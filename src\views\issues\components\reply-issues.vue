<template>
  <a-modal
    :visible="visible"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="cancel"
  >
    <template #title> {{ $t('issues.reply-issue') }} </template>
    <template #footer>
      <a-button @click="cancel">{{ $t('issues.cancel') }}</a-button>
      <a-button type="primary" @click="issueSubmit">{{
        $t('issues.confirm')
      }}</a-button>
    </template>
    <div class="content">
      <a-form ref="replyFormRef" :model="replyForm">
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item field="photo" :label="$t('model-viewer.uploadImg')">
              <a-upload
                ref="uploadRef"
                v-model:file-list="replyForm.imgList"
                action="/api/sys-storage/image/upload"
                :headers="{
                  'Fusion-Auth': getToken() || '',
                  'Fusion-Biz': setFusionBiz() || '',
                }"
                :limit="1"
                accept="image/png, image/jpeg,image/jpg"
                image-preview
                list-type="picture-card"
                :show-link="true"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item field="status" :label="$t('issues.modifyStatus')">
              <a-select v-model="replyForm.status">
                <a-option :value="0">{{
                  $t('model-viewer.unresolved')
                }}</a-option>
                <a-option :value="2">{{
                  $t('model-viewer.inProgress')
                }}</a-option>
                <a-option :value="1">{{
                  $t('model-viewer.resolved')
                }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item field="reply" :label="$t('model-viewer.replyIssue')">
              <a-textarea
                v-model="replyForm.reply"
                :placeholder="$t('model-viewer.enterIssueContent')"
                :max-length="500"
                :show-word-limit="true"
                :disabled="false"
                :auto-size="{
                  minRows: 4,
                  maxRows: 4,
                }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { computed, ref, reactive, watch, nextTick, watchEffect } from 'vue';
  import UserSelector from '@/components/user-selector/index.vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import isEmpty from 'lodash/isEmpty';
  import { useUserStore } from '@/store';
  import { setFusionBiz } from '@/api/interceptor';
  import { getToken } from '@/utils/auth';
  import { replyIssue } from '../api';

  import { useI18n } from 'vue-i18n';
  // import removeSpacesInput from '@/components/removeSpacesInput/index.vue';

  const { t } = useI18n();
  const userStore = useUserStore();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    replyInfoRow: {
      type: Object,
      default() {
        return {};
      },
    },
    replyContent: {
      type: String,
      default: '',
    },
  });
  interface PictureItem {
    fileId: string;
    picToken: string;
  }
  interface issueFormInter {
    id: string;
    reply: string;
    status: number;
    imgList: Array<any>;
    items: PictureItem[];
    picToken: string;
    projectId?: string;
  }

  const replyForm = reactive<issueFormInter>({
    id: '',
    reply: '',
    status: 0,
    items: [],
    imgList: [],
    picToken: '',
  });
  const route = useRoute();
  const projectId = ref(route.params.projectId);

  const receiversID = ref([]);
  const addFileVisible = ref(false);
  const fileList = reactive<any[]>([]);
  const fileIds = ref<Array<string>>([]);
  const replyFormRef = ref<FormInstance>();
  const emits = defineEmits(['update:visible', 'refresh']);
  const cancel = () => {
    replyForm.items = [];
    replyForm.picToken = '';
    replyForm.imgList = [];
    emits('update:visible', false);
  };
  const handleChange = (file: any) => {
    console.log(file);
  };

  const issueSubmit = async (done: any) => {
    let imgLists = '';
    if (replyForm.imgList.length > 0) {
      imgLists = replyForm.imgList[0].response.data.fileToken;
    }
    replyForm.items = [
      {
        fileId: props.replyInfoRow.issueFileList[0].fileId,
        picToken: imgLists,
      },
    ];
    replyForm.picToken = `/api/sys-storage/download_image?f8s=${imgLists}`;
    replyForm.projectId = projectId.value;
    try {
      const res = await replyIssue(replyForm);
      if (res.status) {
        Message.success(res.message);
        emits('refresh');
        cancel();
      }
    } catch (e) {
      console.log(e);
    }
  };

  watchEffect(() => {
    if (props.visible) {
      replyForm.id = props.replyInfoRow.id ?? '';
      replyForm.status = props.replyInfoRow.state ?? 0;
      replyForm.reply = props.replyContent ?? '';
    }
  });
</script>

<style scoped lang="less">
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .file-list-wrap {
    margin-top: 16px;
  }
</style>
