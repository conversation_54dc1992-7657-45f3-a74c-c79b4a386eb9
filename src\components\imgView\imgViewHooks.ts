import { ref } from 'vue';

export default function useImgViewHooks(initialVisible = false) {
  const viewModal = ref({ visible: initialVisible, title: '', fileToken: '' });
  // 设置图片弹窗开关
  function setViewModal(visible: boolean, record: any) {
    viewModal.value.visible = visible;
    viewModal.value.title = record?.name || '';
    viewModal.value.fileToken = record?.fileToken || '';
  }
  return {
    viewModal,
    setViewModal,
  };
}
