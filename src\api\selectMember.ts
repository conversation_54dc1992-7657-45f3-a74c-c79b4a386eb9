import axios from 'axios';

// 获取部门数据
export function getDepartmentList(params: any) {
  return axios.get('/sys-user/orgs/child', {
    params,
  });
}

// 获取人员数据
export function getOrgUser(params: any) {
  return axios.get('/sys-user/org/users', {
    params,
  });
}

// 获取团队数据
export function getTeamList(data: any) {
  return axios.post('/sys-user/team/list', data);
}

// 通过团队获取人员数据
export function getTeamMember(data: any) {
  return axios.post('/sys-user/team/member/page', data);
}

// 通过用户名查找用户数据
export function getMemberList(params: any) {
  return axios.get('/sys-user/users/search', {
    params,
  });
}
