<template>
  <a-drawer
    :visible="visible"
    :width="320"
    :loading="loading"
    unmount-on-close
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title> {{ detailsInfoRow.title }} </template>
    <div style="padding: 0px 4px">
      <a-row style="font-size: 16px; font-weight: 600">
        {{ $t('issues.sender') }}
      </a-row>
      <div class="info-title">
        <a-row>
          <a-col :span="6">
            <a-avatar
              :size="35"
              class="info-headPicture"
              :style="{ backgroundColor: '#7BC616' }"
              >{{ detailsInfoRow?.creater[0] }}</a-avatar
            >
          </a-col>
          <a-col :span="10" class="info-creatby">
            {{ detailsInfoRow?.creater }}
          </a-col>
          <a-col :span="8" class="info-create-date">
            {{ detailsInfoRow?.createDate.slice(0, 10) }}
          </a-col>
        </a-row>
        <a-row class="issues_set">
          <span v-if="detailsInfoRow.stage === 1">
            {{ $t('issues.issueStage') }}: {{ $t('model-viewer.shareBefore') }}
          </span>
          <span v-if="detailsInfoRow.stage === 2">
            {{ $t('issues.issueStage') }}: {{ $t('model-viewer.share') }}
          </span>
          <span v-if="detailsInfoRow.stage === 3">
            {{ $t('issues.issueStage') }}:
            {{ $t('model-viewer.deliveryBefore') }}
          </span>
          <span v-if="detailsInfoRow.stage === 4">
            {{ $t('issues.issueStage') }}: {{ $t('model-viewer.delivery') }}
          </span>
          <span v-if="detailsInfoRow.stage === null">
            {{ $t('issues.issueStage') }}: {{ $t('issues.nothing') }}
          </span>
        </a-row>
        <a-row class="issues_set">
          <span v-if="detailsInfoRow.type === 1">
            {{ $t('issues.type') }}: {{ $t('model-viewer.collisionDetection') }}
          </span>
          <span v-if="detailsInfoRow.type === 2">
            {{ $t('issues.type') }}: {{ $t('model-viewer.violateStandard') }}
          </span>
          <span v-if="detailsInfoRow.type == 3">
            {{ $t('issues.type') }}: {{ $t('model-viewer.ownerStandard') }}
          </span>
          <span v-if="detailsInfoRow.type == null">
            {{ $t('issues.type') }}: {{ $t('issues.nothing') }}
          </span>
        </a-row>
        <a-row v-if="detailsInfoRow.issueFileItems.length" class="issues_set">
          <a-image
            width="100%"
            height="273px"
            :src="`/api/sys-storage/download_image?f8s=${detailsInfoRow.issueFileItems[0].picToken}`"
          ></a-image>
        </a-row>
        <a-card
          :bordered="false"
          class="card-list"
          style="background-color: #f7f8fa"
        >
          <div class="title-file"> {{ $t('issues.file') }} </div>

          <a-list
            size="small"
            :bordered="false"
            :split="false"
            :data="detailsInfoRow?.issueFileList"
            :scrollbar="true"
            :max-height="130"
          >
            <template #item="{ item }">
              <a-list-item>
                <span
                  style="cursor: pointer; color: rgb(var(--primary-6))"
                  @click="issueFileView(item)"
                  >{{ item.name }}</span
                >
                <template #actions>
                  <icon-download
                    v-if="!downloadLoading"
                    style="color: rgb(var(--primary-6))"
                    @click="
                      async () => {
                        downloadLoading = true;
                        await issueFileDownload(item);
                        downloadLoading = false;
                      }
                    "
                  /><icon-loading v-else /> </template
              ></a-list-item>
            </template>
          </a-list>
        </a-card>
        <a-card
          :bordered="false"
          class="card-list"
          style="background-color: #f7f8fa"
        >
          <a-typography>
            <a-typography-title :heading="6" class="title-file">{{
              $t('issues.message')
            }}</a-typography-title>
            <a-typography-paragraph class="message-info">
              <a-scrollbar>
                {{ detailsInfoRow?.message }}
              </a-scrollbar>
            </a-typography-paragraph>
          </a-typography>
        </a-card>
      </div>
    </div>
    <a-divider />
    <div>
      <a-row style="font-size: 16px; font-weight: 600">
        {{ $t('issues.recipient') }}
      </a-row>
      <div class="info-title">
        <a-row>
          <a-space>
            <a-avatar-group :max-count="1">
              <a-avatar
                v-for="issueRecipient in issueRecipientList"
                :key="issueRecipient.id"
                :style="{ backgroundColor: '#7BC616' }"
                >{{ issueRecipient.userName }}</a-avatar
              >
            </a-avatar-group>
            <a-popover position="left">
              <span class="overflow-e">
                {{
                  issueRecipientList!
                    .map((item: any) => item.userName)
                    .join('、')
                }}
              </span>
              <template #content>
                {{
                  issueRecipientList!
                    .map((item: any) => item.userName)
                    .join('、')
                }}
              </template>
            </a-popover>
          </a-space>
        </a-row>
        <a-divider />
        <div>
          <a-space>
            <span style="width: 45px">{{ $t('issues.status') + '：' }}</span>
            <a-select
              v-model="status"
              :style="{ width: '220px' }"
              disabled
              :placeholder="$t('issues.please-select')"
              value-key="key"
              @change="handleOk"
            >
              <a-option
                v-for="item of optionData"
                :key="item.key"
                :value="item.value"
                :label="item.label"
              /> </a-select
          ></a-space>
        </div>
        <div style="margin-top: 15px">
          <a-space>
            <span style="width: 45px">{{
              $t('model-viewer.issueReply') + '：'
            }}</span>
            <a-textarea
              v-model="replyForm"
              disabled
              :style="{ width: '220px' }"
              :placeholder="$t('please-enter')"
              :max-length="500"
              show-word-limit
              :auto-size="{
                minRows: 3,
              }"
            />
          </a-space>
        </div>
        <a-row
          v-if="detailsInfoRow.issueFileItems.length > 1"
          class="issues_set"
        >
          <a-image
            width="100%"
            height="273px"
            :src="`/api/sys-storage/download_image?f8s=${
              detailsInfoRow.issueFileItems[
                detailsInfoRow.issueFileItems.length - 1
              ].picToken
            }`"
          ></a-image>
        </a-row>
      </div>
    </div>
  </a-drawer>
  <!-- 大象云文件预览 -->
  <xbase-doc-viewer
    v-if="xbaseVisible"
    :xbase-doc-info="xbaseDocInfo"
    @close-xbase-doc-viewer="xbaseVisible = false"
  ></xbase-doc-viewer>
  <!-- wps预览 -->
  <!-- <wpsViewer
    v-if="wpsViewModal.visible"
    v-model:visible="wpsViewModal.visible"
  ></wpsViewer> -->
  <!-- 图片预览 -->
  <!-- <imgViewer
    v-if="imgViewModal.visible"
    v-model:visible="imgViewModal.visible"
  ></imgViewer> -->
</template>

<script lang="ts" setup>
  import { watchEffect, ref, computed, onMounted } from 'vue';
  import useLoading from '@/hooks/loading';
  import { useRoute, useRouter } from 'vue-router';
  import { issueFileDownload, updateStatus, issuesStatus } from '../api';
  import { useI18n } from 'vue-i18n';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import wpsViewer from '@/components/wps/index.vue';
  import imgViewer from '@/components/imgView/index.vue';
  // import XbaseDocViewer from '@/components/x-base-doc-viewer/index.vue';
  import modelViewBim from '@/utils/common/view';

  const { t } = useI18n();
  const router = useRouter();
  const route = useRoute();
  const { loading, setLoading } = useLoading(true);

  const projectStore = usePrjPermissionStore();

  // 大象云文件预览
  const xbaseVisible = ref<boolean>(false);
  const xbaseDocInfo = ref({});

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    detailsInfoRow: {
      type: Object,
      default() {
        return {};
      },
    },
    replyContent: {
      type: String,
      default: '',
    },
  });

  const optionData = computed(() => {
    const data = [
      {
        value: 0,
        label: t('issues.unresolved'),
        key: '0',
      },
      {
        value: 2,
        label: t('issues.processing'),
        key: '2',
      },
      {
        value: 1,
        label: t('issues.resolved'),
        key: '1',
      },
    ];
    return data;
  });

  const status = ref();
  const replyForm = ref();
  const downloadLoading = ref(false);
  const issueRecipientList = computed(
    () => props.detailsInfoRow?.issueRecipientList
  );
  const emits = defineEmits(['update:visible', 'refresh']);
  const handleOk = async () => {
    const params: issuesStatus = {
      issueId: props.detailsInfoRow?.id as string,
      status: status.value,
    };
    setLoading(true);
    try {
      const res = await updateStatus(params);
      if (res.status) {
        emits('refresh');
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
      emits('update:visible', false);
    }
  };

  // const issueFileView = (record: any) => {
  //   const type = record.name.split('.')[record.name.split('.').length - 1];
  //   const isWpsType: boolean = wpsJson.includes(type);
  //   const isImgType: boolean = imgJson.includes(type);
  //   // 图片预览
  //   if (isImgType) {
  //     store.setImgViewModal(true, record);
  //   } else if (isWpsType) {
  //     // wps预览
  //     wpsViewHandle(record, 'preview');
  //   } else if (projectStore.modelEngine === 'XBase') {
  //     // 大象云预览
  //     if (xbaseTransformModel.includes(type)) {
  //       const url = router.resolve({
  //         path: '/model-view',
  //         query: {
  //           idStr: record.fileId,
  //           issueId: props.detailsInfoRow.id,
  //           projectId: route.params.projectId,
  //         },
  //       });
  //       window.open(url.href);
  //     } else if (
  //       xbaseVisualFile.filter((file) => file !== 'dwg').includes(type)
  //     ) {
  //       xbaseVisible.value = true;
  //       xbaseDocInfo.value = record;
  //     }
  //   }
  // };
  const issueFileView = async (record: any) => {
    modelViewBim(record, route.params.projectId as string);
    // const type = last(record.name.split('.')) as string;
    // const isWpsType: boolean = wpsJson.includes(type);
    // const isImgType: boolean = imgJson.includes(type);
    // // 图片预览
    // if (isImgType) {
    //   store.setImgViewModal(true, record);
    // } else if (isWpsType) {
    //   // wps预览
    //   wpsViewHandle(record, 'preview');
    // } else if (projectStore.modelEngine === 'XBase') {
    //   // 大象云预览
    //   if (record.status === 0 || (record.status === 3 && type === 'dwg')) {
    //     const url = router.resolve({
    //       path: `/bim-view`,
    //       query: {
    //         idStr: record.fileId,
    //         projectId: route.params.projectId as string,
    //       },
    //     }).href;
    //     window.open(url);
    //   } else {
    //     const statusInfo = getFileTypeStatus(type, record.status.toString());
    //     Message.error(statusInfo);
    //   }
    // }
  };
  const handleCancel = () => {
    emits('update:visible', false);
  };
  watchEffect(() => {
    if (props.visible) {
      status.value = props.detailsInfoRow?.state;
      replyForm.value = props.replyContent;
    }
  });
</script>

<style lang="less" scoped>
  .info-title {
    margin-top: 20px;
    .info-create-date {
      position: relative;
      top: 10px;
    }
    .info-creatby {
      position: relative;
      top: 10px;
      left: -10px;
    }
  }
  .card-list {
    background-color: #f7f8fa;
    margin-top: 10px;
    :deep(.arco-card-body) {
      padding: 0;
      .arco-list-header {
        padding: 8px 10px;
      }
      .arco-list-item {
        padding: 5px 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .arco-list-item-main {
          width: 100px;
          .arco-list-item-content {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 20px;
          }
        }
      }
    }
  }

  .title-icon {
    display: inline-block;
    margin-left: -10px;
    width: 16px;
    height: 12px;
    background: linear-gradient(90deg, #d9d9d9 0%, rgba(217, 217, 217, 0) 100%);
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
  }
  .title-file {
    font-weight: 600;
    padding: 8px 10px;
    font-size: 14px;
  }
  .message-info {
    height: 120px;
    overflow: auto;
    padding: 5px 10px;
  }
  .overflow-e {
    width: 150px;
    margin-top: 10px;
    margin-left: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
  }
  .issues_set {
    margin: 15px 0px;
    font-weight: 600;
  }
</style>
