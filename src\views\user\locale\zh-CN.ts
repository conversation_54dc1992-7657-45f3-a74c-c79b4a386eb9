export default {
  'menu.user.setting': '用户设置',
  'userSetting.title.baseinfo': '基本资料',
  'userSetting.title.editPwd': '修改密码',
  'userSetting.title.editPhone': '更换手机号',
  'userSetting.title.editEmail': '更换邮箱',
  'userSetting.title.bind-Phone': '绑定手机设置',
  'userSetting.title.bind-Email': '绑定邮箱',
  'userSetting.form.oldPwd': '当前密码',
  'userSetting.form.oldPwd.required': '当前密码必填',
  'userSetting.form.newPwd': '新密码',
  'userSetting.form.newPwd.required': '新密码必填',
  'userSetting.form.enterPwd': '确认密码',
  'userSetting.form.enterPwd.required': '确认密码必填',
  'userSetting.form.placeholder.common': '请输入',
  'userSetting.save': '保存',
  'userSetting.reset': '重置',
  'userSetting.password.edit.error': '两次密码输入不一致',
  'userSetting.password.edit.success': '密码修改成功',
  'userSetting.form.phone': '手机号',
  'userSetting.form.phone-new': '新手机号',
  'userSetting.form.captcha': '验证码',
  'userSetting.phone.edit.success': '手机号修改成功',
  'userSetting.form.email': '邮箱',
  'userSetting.form.email.required': '邮箱必填',
  'userSetting.form.email.error': '邮箱格式不正确',
  'userSetting.email.edit.success': '邮箱修改成功',
  'userSetting.email.equity': '产品权限申请',
  'userSetting.password-validation': '密码必须至少包含一个小写字母、一个大写字母、一个数字、一个特殊字符（如$%?^()=+,.;:等），并且长度至少为8个字符',
  'userSetting.password-rule-error': '密码不符合规则',
};
