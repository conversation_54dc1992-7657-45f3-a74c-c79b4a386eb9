<template>
  <div class="metas">
    <table-title
      class="table-title"
      :title="$t('standard-setting.attribute-list')"
    ></table-title>
    <!-- 搜索区 -->
    <a-row>
      <a-col :span="20">
        <a-form
          :model="searchParams"
          label-align="left"
          layout="inline"
          auto-label-width
          class="search-area"
        >
          <a-form-item
            field="name"
            :label="$t('standard-setting.attribute-name')"
            label-col-flex="auto"
            content-class="item"
          >
            <a-input
              v-model="searchParams.name"
              :placeholder="$t('standard-setting.please-enter')"
              allow-clear
              @press-enter="search"
              @search="search"
              @clear="search"
            />
          </a-form-item>
        </a-form>
      </a-col>
      <a-col :span="4" style="text-align: right">
        <a-space :size="8">
          <a-button type="outline" @click="search">
            <template #icon> <icon-search /> </template
            >{{ $t('standard-setting.search') }}</a-button
          >
          <a-button type="outline" @click="reset">
            <template #icon><icon-loop /> </template
            >{{ $t('standard-setting.clear') }}</a-button
          >
        </a-space>
      </a-col>
    </a-row>

    <a-divider margin="10px 0 24px" />
    <a-card class="general-card">
      <a-row style="margin-bottom: 16px">
        <a-space style="float: right">
          <a-button type="primary" class="button-add" @click="createMetasDialog"
            ><template #icon><icon-plus /></template>
            {{ $t('list.options.btn.new') }}
          </a-button>
        </a-space>
      </a-row>
      <a-table
        v-table-height
        stripe
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="false"
        :scroll="scroll"
        :scrollbar="true"
        @page-change="onPageChange"
        @page-size-change="pageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #type="{ record }">
          <a-tag class="tag" :color="colorList[record.type]">
            {{
              record.type == 0
                ? $t('standard-setting.date')
                : record.type == 1
                ? $t('standard-setting.text-field')
                : $t('standard-setting.dropdown-list')
            }}</a-tag
          >
        </template>
        <template #operation="{ record }">
          <a-button type="text" size="small" @click="handleView(record)">
            {{ $t('table.opt.details') }}</a-button
          >
          <a-button type="text" size="small" @click="edit(record)">
            {{ $t('table.opt.edit') }}
          </a-button>
          <a-popconfirm
            :content="$t('standard-setting.whether-delete-attribute')"
            position="left"
            @ok="Remove(record)"
          >
            <a-button type="text" size="small" status="danger">
              {{ $t('table.opt.delete') }}
            </a-button>
          </a-popconfirm>
        </template>
      </a-table>
    </a-card>
    <createMetas
      v-model:visible="addVisible"
      :current-data="currentData"
      :open-type="openType"
      :all-options="allOptions"
      @refresh="updateData"
    ></createMetas>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive, watch } from 'vue';
  import useLoading from '@/hooks/loading';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import cloneDeep from 'lodash/cloneDeep';
  import {
    queryMetasList,
    metasData,
    metasParams,
    deleteMetas,
    queryDimensionList,
    queryRuleList,
    queryStandardList,
  } from './api';
  import createMetas from './components/create-metas.vue';
  import { useI18n } from 'vue-i18n';
  import TableTitle from '@/components/table-title/index.vue';
  import { useUserStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';
  import { Message } from '@arco-design/web-vue';

  const { t } = useI18n();
  // 配置
  type Column = TableColumnData & { checked?: true };
  const { loading, setLoading } = useLoading(true);
  const scroll = {
    y: 'calc(100vh - 360px)',
  };
  const colorList = ['orangered', 'blue', 'green'];

  const searchParams = reactive<any>({
    name: '',
  });
  const allOptions = reactive<any>({
    dimensionOptions: [],
    ruleOptions: [],
    standardOptions: [],
  });
  const renderData = ref<metasData[]>([]);
  const addVisible = ref(false);
  const openType = ref('add');
  const currentData = ref({});
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  const pagination = reactive({
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    total: 0,
  });
  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('standard-setting.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 80,
    },
    {
      title: t('standard-setting.name'),
      dataIndex: 'name',
      width: 250,
    },
    {
      title: t('standard-setting.type'),
      dataIndex: 'type',
      slotName: 'type',
      align: 'left',
    },
    {
      title: t('standard-setting.description'),
      dataIndex: 'description',
      align: 'left',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('standard-setting.creation-time'),
      dataIndex: 'createDate',
      align: 'left',
    },
    {
      title: t('standard-setting.operation'),
      dataIndex: 'operation',
      align: 'left',
      slotName: 'operation',
      width: 200,
    },
  ]);

  const userStore = useUserStore();
  const userId = getUserId() || '';
  const projectId = getLocalstorage(`last_project_${userId}`) || '';

  const fetchData = async (
    params: metasParams = {
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      groupId: userStore.admin === 0 ? '0' : projectId,
    }
  ) => {
    setLoading(true);
    try {
      const { data } = await queryMetasList(params);
      renderData.value = data.list;
      pagination.total = data.total;
      localStorage.removeItem('project-setting-default-tab');
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };
  const createMetasDialog = () => {
    openType.value = 'add';
    addVisible.value = true;
  };
  const handleView = (record: object) => {
    openType.value = 'view';
    addVisible.value = true;
    currentData.value = record;
  };
  const Remove = async (record: any) => {
    const formData = new FormData();
    formData.append('id', record.id);
    const res = await deleteMetas(formData);
    if (res.status) Message.success('删除成功');
    if (res.status) {
      fetchData();
    }
  };

  const edit = (record: object) => {
    openType.value = 'edit';
    addVisible.value = true;
    currentData.value = record;
  };

  // 清空
  const reset = () => {
    searchParams.name = '';
    search();
  };
  const search = () => {
    fetchData({
      name: searchParams.name,
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      groupId: userStore.admin === 0 ? '0' : projectId,
    } as unknown as metasParams);
  };
  const onPageChange = (pageNo: number) => {
    pagination.current = pageNo;
    fetchData({
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      groupId: userStore.admin === 0 ? '0' : projectId,
    });
  };
  const pageSizeChange = (pageSize: number): void => {
    pagination.pageSize = pageSize;
    fetchData({
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      groupId: userStore.admin === 0 ? '0' : projectId,
    });
  };
  const updateData = () => {
    search();
  };

  const tableHeight = ref(0);
  // table根据父组件计算空白高度
  const vTableHeight = {
    mounted(el: Element) {
      tableHeight.value = Math.max(
        (el.parentElement?.offsetHeight || 0) - 148,
        0
      );
    },
  };

  const initAddInfo = () => {
    const params = {
      groupId: userStore.admin === 0 ? '0' : projectId,
      pageNo: 1,
      pageSize: 10000,
      name: '',
      source: 'CDE',
    };
    queryDimensionList(params).then((res) => {
      if (res.status) {
        allOptions.dimensionOptions = res.data.list.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
    queryRuleList({ ...params, groupId: '1' }).then((res: any) => {
      if (res.status) {
        allOptions.ruleOptions = res.data.list.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
      queryRuleList(params).then((res1: any) => {
        if (res1.status) {
          const ruleOptions = res1.data.list.map((item: any) => {
            return {
              value: item.id,
              label: item.name,
            };
          });
          allOptions.ruleOptions = [...allOptions.ruleOptions, ...ruleOptions];
        }
      });
    });
    queryStandardList(params).then((res) => {
      if (res.status) {
        allOptions.standardOptions = res.data.list.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
  };
  fetchData();
  initAddInfo();
  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );

  /** 新建属性-规则校验刷新 */
  const refreshRuleList = () => {
    const params = {
      groupId: userStore.admin === 0 ? '0' : projectId,
      pageNo: 1,
      pageSize: 10000,
      name: '',
      source: 'CDE',
    };
    queryRuleList({ ...params, groupId: '1' }).then((res: any) => {
      if (res.status) {
        allOptions.ruleOptions = res.data.list.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
      queryRuleList(params).then((res1: any) => {
        if (res1.status) {
          const ruleOptions = res1.data.list.map((item: any) => {
            return {
              value: item.id,
              label: item.name,
            };
          });
          allOptions.ruleOptions = [...allOptions.ruleOptions, ...ruleOptions];
        }
      });
    });
  };

  defineExpose({
    refreshRuleList,
  });
</script>

<script lang="ts">
  export default {
    name: 'Metas',
  };
</script>

<style scoped lang="less">
  .general-card {
    padding: 0;
  }
  .metas {
    padding: 0;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
      img {
        position: relative;
        top: 3px;
        height: 20px;
      }
    }
    .search-title {
      height: 22px;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #1d2129;
      line-height: 22px;
    }
  }
  .tag {
    border-radius: 4px;
  }
  .list-empty {
    display: flex;
    align-items: center;
    .empty-box {
      width: 100%;
      text-align: center;
      height: 400px;
      .text {
        width: 100%;
        text-align: center;
        margin-top: -80px;
      }
    }
  }
  .button-add {
    width: auto;
    height: 32px;
    background: #3366ff;
    border-radius: 4px 4px 4px 4px;
  }
  :deep(.arco-table-tr) {
    cursor: pointer;
  }
  :deep(.arco-card-header) {
    height: auto;
    padding: 0;
    border: none;
    margin-top: 20px;
  }
  :deep(.arco-card-body) {
    padding: 0;
  }
  :deep(.arco-card-bordered) {
    border: none;
  }
  :deep.arco-btn-size-small {
    padding: 0 15px 0 0;
  }
  .table-title {
    margin-bottom: 16px;
  }
</style>
