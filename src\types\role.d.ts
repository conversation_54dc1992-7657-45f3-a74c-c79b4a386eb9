namespace Role {
  namespace Api {
    // 获取列表数据的参数
    interface PageRoleParam {
      columnName?: string;
      isGeneral?: string;
      pageNo?: number;
      pageSize?: number;
      portalId?: string;
      searchValue?: string;
      sort?: string;
      types?: string;
    }
    // 列表展示数据类型
    interface PageRoleDto {
      id: string;
      code: string;
      name: string;
      createDate: string;
      isGeneral: boolean;
    }

    //新增时类型
    interface RoleDto {
      id?: string;
      buttonIdList: string[];
      requestIdList: string[];
      requestList: RequestDto[];
      userIdList: string[];
      code: string;
      createBy?: string;
      createDate?: string;
      deleteFlag?: number;
      isGeneral: boolean;
      name: string;
    }

    // 用户信息类型
    interface UserDto {
      id: string;
      phone: string;
      userFullname: string;
      email: string;
    }

    // 接口信息类型
    interface RequestDto {
      id: string;
      description?: string;
      name: string;
      httpMethod: string;
      module: string;
      url: string;
    }
  }
}

namespace RoleMenu {
  namespace Api {
    interface Button {
      code: string;
      deleteFlag: number;
      httpMethod: string;
      menuId: string;
      name: string;
      id: string;
    }

    interface MenuTree {
      buttons: Button[];
      code: string;
      path: string;
      id: string;
      parentId: string;
      title: string;
      type: number;
      createBy: string;
      deleteFlag: number;
      children: MenuTree[];
    }
  }
  namespace Model {
    type Button = RoleMenu.Api.Button & { checked?: boolean };

    interface MenuTree {
      title: string;
      key: string;
      buttons: RoleMenu.Api.Button[];
      children?: MenuTree[];
      code: string;
      isLeaf: boolean;
    }
    interface BtnsView {
      key: string;
      title: string;
      buttons: Button[];
      code: string;
      checkedAll: boolean;
      indeterminate: boolean;
      checkedBtns: string[];
    }
  }
}
