import axios from 'axios';
import qs from 'query-string';
import { UserState } from '@/store/modules/user/types';
import XBaseConfig from '@/config/xbase-config.json';

export interface LoginData {
  username: string;
  password: string;
  grant_type?: string;
  scope?: string;
  captcha_code?: string;
  captcha_key?: string;
}

export interface LoginRes {
  access_token: string;
}

export function login(data: LoginData) {
  const q: LoginData = { ...data, scope: 'all' };
  const param = qs.stringify(q);
  return axios.post<LoginRes>('/sys-auth/oauth/token', param);
}
// 提交验证码
export function submitCode(params: any) {
  return axios.get<LoginRes>('/sys-auth/oauth/check_captcha', { params });
}
// 获取用户所属的企业列表信息
export function getCompanyListBySelf() {
  return axios.get<LoginRes>('/sys-user/company/user/joined');
}

export function getRoleList() {
  return axios.get<LoginRes>('/cde-work/user/roleList');
}

export function changeCDexWorkUserInfoApi(params: any) {
  return axios.put<LoginRes>('/cde-work/user/work/userInfo', params);
}

export function getCDexWorkUserInfoApi(params: any) {
  return axios.get<LoginRes>('/cde-work/user/work/userInfo', { params });
}

export function loginDualVerification(data: LoginData) {
  return axios.post('/sys-user/users/check-password', data);
}

export function logout() {
  return axios.delete<LoginRes>('/sys-auth/oauth/exit');
}

export function BIMBaseLogin() {
  const param = qs.stringify({
    grant_type: 'client_credentials',
    client_id: 'acme',
    client_secret: 'acmesecret',
  });
  return axios.post('/bimserver/auth/oauth/token', param, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'isNeedToken': 'false',
    },
    baseURL: '/bim_base_api',
  });
}

export function XBaseLogin() {
  const param = {
    app_id: XBaseConfig.app_id,
    app_key: XBaseConfig.app_key,
  };
  return axios.post('/api/open/v1/auth/client/token', param, {
    headers: {
      isNeedToken: 'false',
    },
    baseURL: '/x_base_api',
  });
}
// 本地服务获取token
export function getXbaseToken() {
  return axios.post('/cde-collaboration/xbase/getXbaseToken');
}
export function getUserInfo(userName: string) {
  return axios.get<UserState>('/sys-user/userInfo', {
    params: {
      userName,
    },
  });
}

export function getCdexUserInfo(userName: string, portalId = '') {
  return axios.get<UserState>('/cde-collaboration/user/cdeUserInfo', {
    params: {
      userName,
      portalId,
    },
  });
}

export interface UserParams {
  ids?: string;
  names?: string;
}

// 选人时通过userid/userName获取的成员列表
export function getUserList(params: UserParams) {
  return axios.get('/cde-collaboration/user/getUsers', {
    params,
  });
}

// 搜索用户
export function searchUser(
  searchValue: string,
  projectId: string | undefined = undefined
) {
  // 如果 projectId 是 undefined 或者是字符串 "undefined"，则设置默认值
  const processedProjectId =
    projectId === undefined || String(projectId) === 'undefined'
      ? '1721357192716677122'
      : projectId;

  return axios.get('/cde-collaboration/user/search', {
    params: {
      searchValue,
      projectId: processedProjectId,
    },
  });
}

export function getSms(phone: string) {
  return axios.get<string>('/sys-auth/oauth/sms_captcha', {
    params: {
      phone,
    },
  });
}

// 激活用户
export function setUserPwd(id: string, pwd: string) {
  return axios.get<string>('/cde-collaboration/user/active', {
    params: {
      id,
      pwd,
    },
  });
}

export interface PasswordParams {
  captcha: string;
  key: string;
  phone: string;
  pwd: string;
}
// 忘记密码通过验证码修改密码
export function editPassword(data: PasswordParams) {
  // return axios.put('/sys-user/user/retrieve/pwd', data);
  // 新接口
  return axios.put('/cde-work/user/retrieve/pwd', data);
}

export interface PwdParams {
  oldPwd: string;
  newPwd: string;
  enterPwd?: string;
}
// 通过旧密码修改密码
export function modifyPassword(data: PwdParams) {
  return axios.put('/sys-user/user/pwd', data);
}

export interface PhoneParams {
  phone: string;
  captcha: string;
  key?: string;
}
// 更新用户手机号
export function updataPhone(data: PhoneParams) {
  return axios.post('/cde-collaboration/user/changePhone', data);
}

// 更新手机号获取验证码
export function getPhoneCode(phone: string) {
  return axios.get('/sys-user/user/sms_change_phone_captcha', {
    params: {
      phone,
    },
  });
}

export interface EmailParams {
  username?: string;
  id?: string;
  email: string;
}
// 更新用户邮箱
export function updateEmail(data: EmailParams) {
  return axios.post('/cde-collaboration/user/update', data);
}

// 第三方登录相关接口
export function getThird(key: string) {
  return axios.get(`/sys-auth/oauth/render_url/${key}`);
}

// 根据第三方登录返回的token获取用户名相关内容
export function getSocialInfoByToken(token: string) {
  return axios.get('/sys-auth/oauth/user_info', {
    headers: {
      Authorization: `bearer ${token}`,
    },
  });
}

// 记录登录单位活跃度
export function activeCompanyRecords(params: any) {
  return axios.get(`/cde-collaboration/board/activeRecords`, { params });
}

// 添加用户至演示项目
export function addInfoToProject(params: any) {
  return axios.get(`/cde-collaboration/demo/addToProject`, { params });
}

// 查询用户在当前企业下的用户类型和部门信息
export function getCompanyUserInfo(params: any) {
  return axios.get('/sys-user/company/user/info', { params });
}
