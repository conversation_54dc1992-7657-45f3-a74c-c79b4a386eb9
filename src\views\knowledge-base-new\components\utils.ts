import wpsJson from '@/config/wpsType.json';

// 判断是否是wps文件
export const isWpsFile = (fileName: any) => {
  const type = fileName.split('.')[fileName.split('.').length - 1];
  const isWpsType: boolean = wpsJson.includes(type);
  return isWpsType;
};
export const isNeedToConvert = (status: number, type: number) => {
  if (type === 2) {
    if (status === 2) return true;
    if ([0, 1].includes(status)) return true;
  } else if (type === 1) {
    return true;
  } else {
    // 普通模型文件
    if ([0, 3].includes(status)) return true;
    if ([-3, 1, 4].includes(status)) return true;
  }
  return false;
};
