import axios from 'axios';

// 项目信息
export interface ProjectRecord {
  id: string;
  name: string;
  modelEngine: string;
  code: string;
  type: string;
  protemId?: string;
  planStart: string;
  planEnd: string;
  description?: string;
  rangeDate?: Array<string>; // 项目时间临时字段
  actualEnd?: string;
  actualStart?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: string;
  isTemplate?: string;
  protemName: string;
  position: string;
  taskEnd?: string;
  coordinate: string;
  elevation: string;
  environmentType: string;
  magnitude: string;
  roadLevel: string;
  safetyLevel: string;
  loadDesign: string;
  projectStatus: string;
  participaUnit: string;
  projectPhase: string;
  projectProperties: string;
  application: Array<string> | string;
}

// 查询项目详情
export function queryProjectDetail(params: object) {
  return axios.get('/cde-collaboration/project/detail', {
    params,
  });
}

