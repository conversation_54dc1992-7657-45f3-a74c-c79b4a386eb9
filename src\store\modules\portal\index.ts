import { defineStore } from 'pinia';
import { Message } from '@arco-design/web-vue';
import { getPortals } from '@/api/modules/user';
import defaultSettings from '@/config/sys-settings';
import { PortalState, PortalInfo, EditPortal } from './types';

const usePortalStore = defineStore('portal', {
  state: (): PortalState => ({
    portalList: [],
    currentPortal: {
      id: '',
      name: '',
      type: 0,
      parentId: '',
      pathId: '',
      pathName: '',
      parentName: '',
      tenantId: 0,
    },
    // 是否开启多门户配置
    multiPortal: defaultSettings.multiPortal,
    // 当前正在编辑的门户，给门户等组件使用，为不同门户设置不同权限
    editPortal: {
      portalId: '',
      type: 0,
    },
  }),

  getters: {
    portalId(state: PortalState) {
      if (state.multiPortal) {
        return state.currentPortal.id || '';
      }
      return undefined;
    },
  },

  actions: {
    updateCurrentPortal(portal: PortalInfo) {
      localStorage.setItem('currentPortal', JSON.stringify(portal));
      this.currentPortal = portal;
    },

    updateEditPortal(portal: EditPortal) {
      localStorage.setItem('editPortal', JSON.stringify(portal));
      this.editPortal = portal;
    },

    async fetchPortalList() {
      try {
        if (!this.multiPortal) return;
        const { data } = await getPortals();
        const sortList = data?.sort((f, b) => f.type - b.type) || [];
        this.portalList = sortList;
        const localPortal = JSON.parse(
          localStorage.getItem('currentPortal') || '{}'
        );
        const localEditPortal = JSON.parse(
          localStorage.getItem('editPortal') || '{}'
        );
        // 设置默认的门户
        if (sortList.find((p) => p.id === localPortal.id)) {
          this.currentPortal = localPortal;
        } else {
          this.updateCurrentPortal(this.portalList[0]);
        }
        // 设置默认的当前编辑的门户
        if (sortList.find((p) => p.id === localEditPortal.portalId)) {
          this.editPortal = localEditPortal;
        } else {
          this.updateEditPortal({
            portalId: this.portalList[0].id,
            type: this.portalList[0].type,
          });
        }
      } catch (e) {
        Message.warning({
          content: '无门户权限',
          duration: 2 * 1000,
        });
      }
    },

    changeCurrentPortal(portalId: string) {
      const portal = this.portalList.find((p) => p.id === portalId);
      if (!portal) return;
      this.updateCurrentPortal(portal);
      // 需要刷新页面清除其他内容，重新请求
      window.location.reload();
    },
  },
});

export default usePortalStore;
