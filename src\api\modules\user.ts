import axios from 'axios';
import qs from 'query-string';
import type { UserState } from '@/store/modules/user/types';
import type { LoginData, LoginRes } from '@/views/login/types';
import { HttpResponse } from '../interceptor';

// 登录，仅获取token
export function login(data: LoginData) {
  const q: LoginData = { ...data, scope: 'all' };
  const param = qs.stringify(q);
  return axios.post<LoginRes>('/sys-auth/oauth/token', param);
}

// 退出登录
export function logout() {
  return axios.delete<LoginRes>('/sys-auth/oauth/exit');
}

export function GlLogin() {
  const param = qs.stringify({
    grant_type: 'client_credentials',
    client_id: 'HvEdk1yLgLu3OKaQ63fkkRZ',
    client_secret: 'HvEdl1yLgLu3OKcJ21x6Nj2',
  });
  return axios.post('/bimserver/auth/oauth/token', param, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'isNeedToken': 'false',
    },
    baseURL: '/gl_api',
  });
}

// 获取用户信息
export function getUserInfo(userName: string, portalId = '') {
  return axios.get<UserState>('/sys-user/userInfo', {
    params: {
      userName,
      portalId,
    },
  });
}
// cdex用户信息
export function getCdexUserInfo(userName: string, portalId = '') {
  return axios.get<UserState>('/cde-collaboration/user/cdeUserInfo', {
    params: {
      userName,
      portalId,
    },
  });
}

// 获取用户下的所有门户
export function getPortals() {
  return axios.get<any[]>('/sys-user/user/portals');
}

// 查询用户下的所有菜单
export function getRouter(portalId: undefined | string = undefined) {
  return axios.get<any[]>('/sys-user/user/menus', {
    params: {
      portalId,
    },
  });
}

// 查询用户菜单下按钮
export function getPermissions(
  menuId: string,
  portalId: undefined | string = undefined
) {
  return axios.get<any>('/sys-user/user/buttons', {
    params: {
      menuId,
      portalId,
    },
  });
}

// TODO: 获取用户锁定次数，后续移动到login
export function getUserLockNum(userName: string) {
  return axios.get<string>('/sys-auth/oauth/user/lock/num', {
    params: {
      userName,
    },
  });
}

export function getSms(phone: string) {
  return axios.get<string>('/sys-auth/oauth/sms_captcha', {
    params: {
      phone,
    },
  });
}

export interface PasswordParams {
  captcha: string;
  key: string;
  phone: string;
  pwd: string;
}
// 忘记密码通过验证码修改密码
export function editPassword(data: PasswordParams) {
  return axios.put('/sys-user/user/password', data);
}

export interface PwdParams {
  oldPwd: string;
  newPwd: string;
  enterPwd?: string;
}
// 通过旧密码修改密码
export function modifyPassword(data: PwdParams) {
  return axios.put('/sys-user/user/pwd', data);
}

// export function getThird(key: string) {
//   return axios.get(`/sys-auth/oauth/render_url/${key}`);
// }
export function getThird(key: any) {
  return axios({
    // baseURL: '/local',
    url: `/sys-auth/oauth/render_url/${key}`,
    method: 'get',
    params: {
      clientId: 'asset',
      tenantId: '100010',
    },
  });
}

// 查询用户角色
export function getRole(id: string) {
  return axios({
    // baseURL: '/local',
    url: `/sys-system/user/roles`,
    method: 'get',
    params: {
      id,
    },
  });
}

export function getPwdSms(params: {
  email?: string;
  phone?: string;
  phoneReceive?: string;
}) {
  return axios.get<string>('/sys-auth/oauth/send_captcha', { params });
}

export function register(data: any) {
  // const q: LoginData = { ...data, scope: 'all' };
  // const param = qs.stringify(q);
  return axios.post('/cde-work/user/register', data);
}

// 获取除项目外的系统级别权限
export function getBtnPermissionOfSys(): Promise<
  HttpResponse<Permission.Api.PageBtnDto[]>
> {
  return axios.get('/sys-system/user/buttons');
}

// 获取当前项目的按钮权限
export function getBtnPermissionOfProject(
  projectId: string
): Promise<HttpResponse<Permission.Api.ProjectPageBtnDto>> {
  return axios.get('/cde-collaboration/project/user/buttons', {
    params: { projectId },
  });
}
