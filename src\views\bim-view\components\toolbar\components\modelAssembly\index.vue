<template>
  <a-card>
    <template #extra>
      <icon-close class="icon-close" @click="close" />
    </template>
    <template #title>
      <strong class="title">{{ $t(t('model-viewer.modelAssembly')) }}</strong>
    </template>

    <a-tabs default-active-key="translation" @tab-click="tabClickHandle">
      <a-tab-pane
        key="translation"
        :title="$t('model-viewer.model.translation')"
      >
      </a-tab-pane>
      <a-tab-pane key="rotate" :title="$t('model-viewer.model.rotate')"
        ><a-form ref="formRef" :model="form" auto-label-width>
          <a-form-item :label="$t('model-viewer.model.AxialDirection')">
            <div class="shaft">
              <div
                class="btn-coord axis"
                style="color: #ec3730"
                @click="coordinateClick('x')"
                >X</div
              >
              <div
                class="btn-coord axis"
                style="color: #27b562"
                @click="coordinateClick('y')"
                >Y</div
              >
              <div
                class="btn-coord axis"
                style="color: #0e80e7"
                @click="coordinateClick('z')"
                >Z</div
              >
            </div>
          </a-form-item>

          <a-form-item :label="$t('model-viewer.model.Angle')">
            <a-input-number
              v-model="form.angle"
              style="width: 180px"
              :placeholder="$t('please-enter')"
              @change="changeInput"
            />
          </a-form-item> </a-form
      ></a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, defineEmits, watch, reactive, toRaw } from 'vue';

  import useModelToolsStore from '@/store/modules/model-viewer/index';
  import { storeToRefs } from 'pinia';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';

  const props = defineProps({
    viewer: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  const viewer = toRaw(props.viewer);
  const point: any = ref(null);
  point.value = document.getElementById('point'); // 平移点位

  const { t } = useI18n();

  const emits = defineEmits(['close']);

  const store = useModelToolsStore();
  const { motionPath } = storeToRefs(store);

  const form: any = ref({
    angle: 90,
  });

  const changeInput = (value: any) => {
    if (value > 360) form.value.angle = 360;
    if (value < -360) form.value.angle = -360;
  };

  const ModelAssemblyType: any = ref('translation');

  const rotateSelectModel = ref('');

  const tabClickHandle = (key: any) => {
    ModelAssemblyType.value = key;
    viewer.rotateSelectModel('', rotateSelectModel.value);
  };

  // 关闭弹窗
  const close = () => {
    ModelAssemblyType.value = null;
    // 清空旋转选择模型
    viewer.rotateSelectModel(null);
    // 关闭点位捕捉
    viewer.pointPicked(false);
    emits('close');
  };

  // 平移所需要的坐标数据
  const movePoint: any = {
    startPoint: null,
    endPoint: null,
    motionPath: null,
  };

  // 重置平移点位
  const resetPoint = () => {
    movePoint.startPoint = null;
    movePoint.endPoint = null;
    movePoint.motionPath = null;
  };

  // 显示/隐藏点位
  const pointshow = (value: any) => {
    point.value.style.display = value;
  };

  // 设置平移选中点位
  const updateStartPoint = (x: any, y: any) => {
    pointshow('block');
    point.value.style.left = `${x - 3}px`;
    point.value.style.top = `${y - 3}px`;
  };

  // 点击点位平移操作
  const pointChangeHandle = (data: any) => {
    if (!ModelAssemblyType.value) return;
    if (!movePoint.motionPath) movePoint.motionPath = data.path;
    if (!data.clientPoint) return;
    // 设置初始点和结束点
    if (!movePoint.startPoint) {
      movePoint.startPoint = data.worldPosition;
      updateStartPoint(data.clientPoint.x, data.clientPoint.y);
      Message.info('请选择目标点');
    } else if (!movePoint.endPoint) {
      movePoint.endPoint = data.worldPosition;
      updateStartPoint(data.clientPoint.x, data.clientPoint.y);
      pointshow('none');
    }
    // 平移
    if (movePoint.startPoint && movePoint.endPoint) {
      // translation(movePoint);
      viewer.translateModel({
        motionPath: movePoint.motionPath,
        startPoint: movePoint.startPoint,
        endPoint: movePoint.endPoint,
      });
      // 点位重置
      movePoint.motionPath = null;
      movePoint.startPoint = null;
      movePoint.endPoint = null;
    }
  };

  // 选择旋转坐标轴旋转
  const coordinateClick = (axis: string) => {
    store.setModelAssemblyType('rotate');
    viewer.setEnableSelection(false);
    if (!motionPath.value) {
      Message.error(t('model-viewer.model.selectModel'));
      return;
    }
    if (!form.value.angle) {
      Message.error(t('model-viewer.model.EnterAngle'));
      return;
    }
    const data = {
      path: motionPath.value,
      axis,
      angle: form.value.angle,
    };
    viewer.rotateModel(data);
  };

  // 禁止勾选构件
  viewer.setEnableSelection(false);

  const pointClickHandle = async (data: any) => {
    if (ModelAssemblyType.value === 'translation') {
      pointChangeHandle(data);
    } else if (ModelAssemblyType.value === 'rotate') {
      motionPath.value = data.path;
      viewer.rotateSelectModel(data.path, rotateSelectModel.value);
      rotateSelectModel.value = data.path; // 上一次选择的
    }
  };

  // 监听点位点击
  viewer.modelPointPickedListen(pointClickHandle);

  watch(
    () => ModelAssemblyType.value,
    (val: any) => {
      // 清除选择构件
      viewer.clearSelectedEntities();
      // 清空旋转选择模型
      motionPath.value = '';
      // 清除点位
      pointshow('none');
      if (val === 'translation') viewer.pointPicked(true);
      // 重置点位
      resetPoint();
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .shaft {
    display: flex;
    .btn-coord {
      user-select: none;
      cursor: pointer;
      width: 45px;
      height: 28px;
      background: #f2f9ff;
      box-shadow: inset 0px 1px 4px 0px rgba(0, 113, 215, 0.65);
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8px;
      flex-direction: row-reverse;
    }
    :deep(.arco-input-wrapper .arco-input) {
      width: 150px;
    }
  }
  .modelAssmbly-card {
    position: absolute;
    top: 50px;
    left: 50px;
    width: 300px;
    height: auto;
    z-index: 10;
  }
  .icon-close {
    cursor: pointer;
  }
</style>
