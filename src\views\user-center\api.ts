import axios from 'axios';
import type { UserState } from '@/store/modules/user/types';

export interface MemberRecord {
  id?: string;
  userName?: string;
  name?: string;
  phone?: string;
  email?: string;
  admin?: string;
  accountNumber?: string;
  post?: string;
  pathNo?: string;
  pathName?: string;
  secondLevel?: string;
  unitManagement?: string;
  accountStateCode?: string;
  subsidiaryCorporation?: string;
  application?: Array<string>;
  ext1?: string;
  secondaryUnit?: string;
  subsidiary?: string;
  unitAdmin?: number;
}

export interface SysUserMemberRecord {
  id?: string;
  userName?: string;
  userFullname?: string;
  phone?: string;
  email?: string;
  accountStatus?: string;
}
export interface searchData {
  searchValue?: string;
  orgNo?: string;
  pageNo?: number;
  pageSize?: number;
}

export interface searchSysUserParams {
  searchValue?: string;
  accountStatus?: string;
  pageNo?: number;
  pageSize?: number;
  companyId?: string;
}
export interface unitTreeData {
  id?: string;
  parentId?: string;
  children?: unitTreeData[];
  content?: {
    id?: string;
    name?: string;
    orgNo?: string;
    pathName?: string;
    pathNo?: string;
  };
}
type updateParam = {
  logout?: number;
  manager?: string;
  orgName?: string;
  orgNo?: string;
  post?: string;
  userId?: string;
};

export interface userInfoType {
  secondLevel?: string;
  secondLevelCode?: string;
  admin?: string;
  isSecondLevel?: string;
}

export interface orgUserListType {
  orgNo?: string;
  userIdList?: string[];
}

export interface companyUser {
  companyId?: string;
  companyMembers?: string[];
}

export interface checkExisted {
  phone?: string;
  email?: string;
}

export interface invitationUserUrl {
  companyUserType?: string;
  orgNo?: string;
  registerUrl?: string;
  userInfoList?: string;
}
export interface InvitationUser {
  companyUserType?: string;
  orgNo?: string;
  orgNoList?: any;
  registerUrl?: string;
  userInfoList?: any;
}

// 用户中心分页列表接口
export function queryTableData(params: searchData) {
  return axios.get('/cde-collaboration/user/unitUser', { params });
}
// sys-user 分页接口
export function getSysUserList(params: searchSysUserParams) {
  // return axios.get('/sys-user/users/page', { params });
  return axios.get('/sys-user/users/list', { params });
}

// sys-user 组织新增用户接口
export function orgAddMember(data: orgUserListType) {
  return axios.post('/sys-user/org/addMember', data);
}

// sys-user 组织移除用户接口
export function orgRemoveMember(data: orgUserListType) {
  return axios.post('/sys-user/org/removeMember', data);
}

// 部门树接口
export function queryUnitTree(isAll: boolean) {
  return axios.get('/cde-collaboration/sys-user/orgs/tree', {
    params: { isAll },
  });
}

// 获取人员数据
export function getOrgUser(params: any) {
  return axios.get('/sys-user/org/users', {
    params,
  });
}

// 用户详情接口
export function queryUserDetail(id: string) {
  return axios.get('/cde-collaboration/user/userInfoCde', {
    params: { userId: id },
  });
}

// 用编辑接口
export function updateUserDetail(data: updateParam) {
  return axios.post('/cde-collaboration/user/userEditCde', data);
}

// 导出接口
export function exportUser(params: searchData) {
  return axios.get('/cde-collaboration/user/userExport', {
    params,
    responseType: 'blob',
  });
}

// 新增接口
export function addUser(data: searchData) {
  return axios.post('/cde-collaboration/user/unitSaveUser', data);
}

// 获取用户权限接口
export function querryUserInfo() {
  return axios.get('/cde-collaboration/user/unitUserLimits');
}

// 导入接口
export function userImport() {
  return axios.post('/cde-collaboration/user/importUnit');
}

export function companyAndOrgRemoveUserApi(data: companyUser) {
  return axios.post('/sys-user/company/removeCompanyAndOrg',  data );
}

export function checkExistedUserApi(params: checkExisted) {
  return axios.get('/sys-user/company/user/match', { params });
}

// 发送邀请链接
export function sendInvitationUrl(data: InvitationUser) {
  return axios.post('/sys-user/company/send/invitation', data);
}

export function companyJoinCompanyAndOrg(data: any) {
  return axios.post('/sys-user/company/joinCompanyAndOrg', data);
}

export function getCompanyUserInfo( params : any) {
  return axios.get('/sys-user/company/user/info', { params });
}

export function changeCompanyUserInfo(data: any) {
  return axios.put('/sys-user/company/user/info', data);
}