<template>
  <div class="file-tree">
    <a-spin style="width: 100%" :loading="treeLoading">
      <div class="tree-content">
        <a-tree
          ref="folderTree"
          :key="treekey"
          block-node
          default-expand-all
          :default-expanded-keys="expandedKeys"
          v-model:selected-keys="selectedKeys"
          :data="folderData"
          :field-names="{
            key: 'id',
            title: 'name',
          }"
          size="large"
          @select="nodeClick"
          @expand="nodeExpand"
        >
          <template #switcher-icon="node">
            <IconDown
              v-if="
                !('children' in node) ||
                !node.children ||
                node.children?.length > 0
              "
            />
          </template>
          <template #icon="node">
            <a-tooltip
              :content="
                node.node.standardName && node.node.deliveryStandardName
                  ? `${$t('file-manage.naming-standard')}：${
                      node.node.standardName
                    }\n${$t('file-manage.delivery-criteria')}：${
                      node.node.deliveryStandardName
                    }`
                  : node.node.standardName && !node.node.deliveryStandardName
                  ? `${$t('file-manage.naming-standard')}：${
                      node.node.standardName
                    }`
                  : `${$t('file-manage.delivery-criteria')}：${
                      node.node.deliveryStandardName
                    }`
              "
              background-color="#165dff"
              style="white-space: pre-line"
            >
              <icon-check-circle-fill
                v-if="node.node.deliveryStandardId || node.node.standardId"
                style="
                  position: absolute;
                  top: 15px;
                  left: 9px;
                  color: #00d88c;
                  font-size: 12px;
                "
              />
            </a-tooltip>

            <file-image
              :file-name="node.node.name"
              :is-sysFile="isSysFolder(node.node.sysType)"
              :is-file="!!node.node.folderId"
            />
          </template>
          <template #title="nodeData">
            <a-input
              ref="addInputRef"
              v-if="nodeData.isAdd"
              v-model="newFolderName"
              style="width: 100%"
              :placeholder="$t('file-manage.need-place-input')"
              @keydown.enter="addFolderRequest(nodeData, newFolderName)"
              @blur="addFolderRequest(nodeData, newFolderName)"
            />
            <a-input
              ref="editInputRef"
              v-else-if="nodeData.isEdit"
              v-model="newFolderName"
              style="width: 100%"
              :placeholder="$t('file-manage.need-place-input')"
              @keydown.enter="renameFolder(nodeData)"
              @blur="renameFolder(nodeData)"
            />
            <div
              v-else
              :style="{
                overflow: 'hidden',
                whiteSpace: 'nowrap',
              }"
            >
              {{ i18FolderName(nodeData) }}
            </div>
          </template>
          <template #extra="nodeData">
            <div
              class="extra"
              v-if="
                nodeData.id === 'COMMUNALSPACE' ||
                (!isTopFolder(nodeData.id) &&
                  nodeData.id !== 'add' &&
                  selectedKeys.includes(nodeData.id) &&
                  !nodeData.sysType) ||
                nodeData.sysType === 5
              "
            >
              <FolderOptBtns
                :node-data="nodeData"
                @eventsHandle="eventsHandler"
              ></FolderOptBtns>
            </div>
          </template>
        </a-tree>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, ref, defineProps, defineEmits, PropType } from 'vue';
  import { useI18n } from 'vue-i18n';
  import FolderOptBtns from './folder-opt-btns.vue';

  import { FolderMessage } from '@/api/tree-folder';
  import useFileStore from '@/store/modules/file/index';
  import { useThrottleFn } from '@vueuse/core';

  import { Message } from '@arco-design/web-vue';
  import { addChildFolder, updateFolder } from '../../api';
  import { encode } from 'js-base64';
  import { storeToRefs } from 'pinia';

  import { getParentFolder } from '@/views/projectSpace/file/hooks/events';
  import { isTopFolder, isSysFolder } from '@/views/projectSpace/file/utils';
  import FileImage from '../image-file.vue';
  import useI18nHandleName from '../../hooks/backups';

  const { t } = useI18n();
  const fileStore = useFileStore();

  const { i18FolderName } = useI18nHandleName();
  const props = defineProps({
    folderData: {
      type: Array as PropType<FolderMessage[]>,
      default() {
        return [];
      },
    },
  });
  const emits = defineEmits([
    'nodeClick',
    'refresh',
    'handleUpload',
    'handleShare',
    'handleDownload',
  ]);

  const folderTree = ref<any>();
  const treekey = ref('started');
  const treeLoading = ref(true);
  function updateTreeKey() {
    treekey.value = 'finished';
    treeLoading.value = false;
  }

  const { newFolderName, selectedKeys, expandedKeys } = storeToRefs(fileStore);

  const expendNodes = (nodeKeys: any) => {
    if (nodeKeys.length) {
      nodeKeys.forEach((key: string) => {
        folderTree.value?.expandNode(key);
      });
    }
  };

  // 点击树节点事件
  const nodeClick = async (
    selectedNodes: (string | number)[],
    nodeData: any
  ) => {
    const nodeInfo = nodeData.node;

    if (nodeInfo.isAdd || nodeInfo.isEdit) {
      return;
    }

    fileStore.setSelectedKeys([nodeInfo.id as string]);
    fileStore.setCurrentIdPath(nodeInfo.idPath || '');

    emits(
      'nodeClick',
      {
        nodes: selectedNodes,
        nodeInfo,
      },
      () => expendNodes(selectedNodes)
    );
  };

  function setNodeSelected(nodeData: FolderMessage) {
    nodeClick([nodeData.id as string], { node: nodeData });
  }

  // 节点展开或关闭
  const nodeExpand = (expandKeys: string[]) => {
    fileStore.setExpandedKeys(expandKeys);
  };

  const addInputRef = ref();
  const editInputRef = ref();

  const currentData = ref();
  function eventsHandler(eventType: string, data?: any) {
    switch (eventType) {
      case 'add-folder':
        currentData.value = data;
        nextTick(() => {
          folderTree.value.expandNode(data.id);
        }).then(() => {
          addInputRef.value.focus();
        });
        break;
      case 'rename-folder':
        nextTick(() => {
          editInputRef.value.focus();
        });
        break;
      case 'upload':
        emits('handleUpload', 0);
        break;
      case 'download':
        emits('handleDownload');
        break;
      case 'delete-folder':
        setNodeSelected(
          getParentFolder(props.folderData, data.id, props.folderData)
        );
        break;
      default:
        break;
    }
  }

  defineExpose({ setNodeSelected, updateTreeKey });

  // 添加文件夹请求
  const addFolderRequest = useThrottleFn(
    async (currentFile: any, newName: string) => {
      delete currentFile.id;
      currentFile.name = newName;
      currentFile.englishName = newName;
      const param: FolderMessage = currentFile;
      // 名称为空校验
      if (newName === '') {
        currentData.value.children!.pop();
        // Message.info('撤销新增');
        return;
      }
      // 禁用字符校验
      // eslint-disable-next-line no-useless-escape
      // const pattern = /^[^\\/:\*\?"<>\|\s]+$/;
      const pattern = /^[^\\/:*?"<>|]+$/;
      const regResult = pattern.test(newName);
      if (!regResult) {
        Message.warning(t('file-manage.name-exclude-2'));
        return;
      }
      // 名称重复校验，这里的 currentFile应为上级
      // const repeated = await isFolderRepeated(currentFile, newName);
      // if (repeated) {
      //   Message.error(t('file-manage.name-duplication'));
      //   return;
      // }
      // 新增文件夹接口
      const res: any = await addChildFolder(param);
      if (res.code === 8000000) {
        Message.success(t('file-manage.success'));
        currentFile.isAdd = false;
        // 需要刷新列表

        // store.getTreeChild(currentFile, false);
        // fileStore.setCurrentFolder(currentFile);
      } else {
        Message.error(res.data);
      }
      emits('refresh', currentData.value);
    },
    1000
  );

  const renameFolder = useThrottleFn(async (currentFile: any) => {
    // 名称没有变化或者名称为空表示退出编辑
    if (
      currentFile.name === newFolderName.value ||
      newFolderName.value === ''
    ) {
      currentFile.isEdit = false;
      Message.info('撤销重命名');
      return;
    }
    // 禁用字符校验
    // eslint-disable-next-line no-useless-escape
    // const pattern = /^[^\\/:\*\?"<>\|\s]+$/;
    const pattern = /^[^\\/:*?"<>|]+$/;
    const regResult = pattern.test(newFolderName.value);
    if (!regResult) {
      Message.warning(t('file-manage.name-exclude')); // 允许空格
      newFolderName.value = '';
      return;
    }

    const param: any = {
      ...currentFile,
      name: encode(newFolderName.value),
      englishName: encode(newFolderName.value),
    };
    delete param.sunFolders;
    delete param.children;
    delete param.files;
    delete param.path;
    const res: any = await updateFolder(param);
    if (res.code === 8000000) {
      Message.success(t('file-manage.success'));
      currentFile.name = newFolderName.value;
      currentFile.englishName = newFolderName.value;
      // 更新path
      const index = currentFile.path.lastIndexOf('/');
      currentFile.path =
        currentFile.path.substring(0, index + 1) + newFolderName.value;
      // 更新english path
      const englishIndex = currentFile.englishPath.lastIndexOf('/');
      currentFile.englishPath =
        currentFile.englishPath.substring(0, englishIndex + 1) +
        newFolderName.value;

      fileStore.setBreadcrumbList(currentFile);
    }
    currentFile.isEdit = false;
    newFolderName.value = '';
  }, 1000);
</script>

<style scoped lang="less">
  :deep(.arco-tree-node-title:hover) {
    background-color: #e8f2ff;
  }
  :deep(.arco-tree-node-selected) {
    background-color: #e8f2ff;
  }
  :deep(.arco-tree-node:hover) {
    background-color: #e8f2ff;
  }
  .file-tree {
    width: 100%;
    height: 100%;
  }
  .tree-content {
    padding-left: 16px;
    padding-top: 20px;
    height: calc(100% - 20px);
  }

  .extra {
    width: 30px;
  }
  // .extra {
  //   position: absolute;
  //   top: 6px;
  //   right: 10px;
  //   width: 16px;
  //   height: 30px;
  //   padding-left: 10px;
  //   background: inherit;
  // }
</style>
