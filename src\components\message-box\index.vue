<template>
  <a-drawer :width="420" :visible="visible" @cancel="handleMessageCancel">
    <template #header>
      <div class="header">
        <span style="font-weight: bold">{{
          $t('messageBox.tab.title.message')
        }}</span>
        <span @click="handleMessageCancel">
          <icon-close style="float: right; cursor: pointer"></icon-close>
        </span>
      </div>
    </template>
    <div>
      <a-tabs :default-active-key="-1" lazy-load @change="tabChange">
        <!--        <a-tab-pane :key="1" title="全部">-->
        <!--          <List :render-list="msgList"></List>-->
        <!--        </a-tab-pane>-->
        <a-tab-pane :key="-1" :title="$t('messageBox.unread-messages')">
          <List
            :render-list="msgList"
            :default-select-keys="defaultSelectKeys"
            @read-message="readMsg"
            @close="handleMessageCancel"
            @delete="delMsg"
            @change="selectChange"
          ></List>
        </a-tab-pane>
        <a-tab-pane :key="0" :title="$t('messageBox.read-messages')">
          <List
            :render-list="msgList"
            :default-select-keys="defaultSelectKeys"
            @read-message="readMsg"
            @close="handleMessageCancel"
            @delete="delMsg"
            @change="selectChange"
          ></List>
        </a-tab-pane>
        <template #extra>
          <a-select
            v-model="notificationForm.type"
            :bordered="false"
            style="width: 110px"
            @change="typeSelect"
          >
            <a-option :value="'0'">{{ $t('messageBox.all-types') }}</a-option>
            <a-option :value="FormKeys.review">{{
              $t('messageBox.review-task')
            }}</a-option>
            <a-option :value="FormKeys.reviewNotify">{{
              $t('messageBox.review-notification')
            }}</a-option>
            <a-option :value="FormKeys.delivery">{{
              $t('messageBox.delivery-task')
            }}</a-option>
            <a-option :value="FormKeys.deliveryNotify">{{
              $t('messageBox.delivery-notification')
            }}</a-option>
            <a-option :value="FormKeys.collaborate">{{
              $t('messageBox.collaboration-task')
            }}</a-option>
            <a-option :value="FormKeys.collaborateNotify">{{
              $t('messageBox.collaboration-notification')
            }}</a-option>
            <a-option :value="FormKeys.collaborateRemind">{{
              $t('messageBox.collaboration-remind')
            }}</a-option>
            <a-option :value="FormKeys.issue">{{
              $t('messageBox.issue-notification')
            }}</a-option>
            <a-option :value="FormKeys.issueNotify">{{
              $t('messageBox.issue-reply')
            }}</a-option>
            <a-option :value="FormKeys.enclosure">{{
              $t('messageBox.enclosure-notification')
            }}</a-option>
            <a-option :value="FormKeys.meetingNews">{{
              $t('messageBox.meeting-notification')
            }}</a-option>
            <!--            <a-option :value="FormKeys.share">{{-->
            <!--              $t('messageBox.shared-notification')-->
            <!--            }}</a-option>-->
          </a-select>
        </template>
      </a-tabs>
    </div>

    <template #footer>
      <div class="footer-btns">
        <a-button style="float: left" type="text" @click="selectAll">{{
          $t('messageBox.select-all')
        }}</a-button>
        <div class="btn" style="float: right">
          <a-button
            status="danger"
            style="margin-right: 8px"
            @click="delAllMsg"
            >{{ $t('messageBox.batch-delete') }}</a-button
          >
          <a-button
            type="primary"
            :disabled="tabValue === 0"
            @click="readAllMsg"
            >{{ $t('messageBox.all-read') }}</a-button
          >
        </div>
      </div>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
  import { ref, defineProps, onMounted, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import List from './list.vue';
  import { getMsgList, updateMsgState } from './api';
  import { FormKeys } from '../../directionary/process';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const notificationForm = ref({
    type: '0',
  });

  const initData = ref([]);
  const emits = defineEmits(['update:visible']);
  const tabValue = ref(-1);
  const msgList = ref([]);
  const getMessageList = () => {
    const params = {
      status: tabValue.value as number,
    };
    getMsgList(params).then((res) => {
      if (res.status) {
        const data = res.data.list || [];
        if (data.length) {
          data.forEach((e: any) => {
            if (e.msgTemplateParam) {
              e.msgTemplateParam = JSON.parse(e.msgTemplateParam);
            }
          });
        }
        msgList.value = data;
        initData.value = data;
      }
    });
  };

  const typeSelect = () => {
    if (notificationForm.value.type === '0') {
      msgList.value = initData.value;
    } else if (initData.value.length > 0) {
      for (let i = 0; i < initData.value.length; i++) {
        msgList.value = initData.value.filter((item: any) => {
          return item.msgTemplateParam.formKey === notificationForm.value.type;
        });
      }
    } else {
      msgList.value = [];
    }
  };

  const refreshMessageList = () => {
    const params = {
      status: tabValue.value as number,
    };
    getMsgList(params).then((res) => {
      if (res.status) {
        const data = res.data.list || [];
        if (data.length) {
          data.forEach((e: any) => {
            if (e.msgTemplateParam) {
              e.msgTemplateParam = JSON.parse(e.msgTemplateParam);
            }
          });
        }
        initData.value = data;
        typeSelect();
      }
    });
  };
  const selectedMsg = ref<any>([]);
  const defaultSelectKeys = ref();

  const tabChange = (val: number) => {
    tabValue.value = val;
    refreshMessageList();
    selectedMsg.value = [];
    defaultSelectKeys.value = [];
  };
  const selectChange = (val: any) => {
    selectedMsg.value = val || [];
  };
  const selectAll = () => {
    if (defaultSelectKeys?.value?.length) {
      defaultSelectKeys.value = [];
    } else {
      defaultSelectKeys.value = msgList.value.map((e: any) => e.receiveLogId);
    }
    selectedMsg.value = [...defaultSelectKeys.value];
  };
  const handleMessageCancel = () => {
    emits('update:visible', false);
  };

  const changeMsgStatus = (status: any, ids?: any[]): string => {
    const idList = ids?.length ? ids : selectedMsg.value || [];
    const params = {
      receiveLogIdList: idList.join(','),
      status,
    };
    if (!params.receiveLogIdList.length) {
      Message.error(t('messageBox.no-selected-messages'));
      return '';
    }
    updateMsgState(params).then((res: any) => {
      if (res.status) {
        refreshMessageList();
      }
    });
    selectedMsg.value = [];
    defaultSelectKeys.value = [];
    return '';
  };
  const readAllMsg = () => {
    changeMsgStatus(0);
  };
  const delAllMsg = () => {
    changeMsgStatus(-2);
  };
  const delMsg = (id: any) => {
    changeMsgStatus(-2, [id]);
  };
  const readMsg = (id: any) => {
    if (id) {
      changeMsgStatus(0, [id]);
    }
  };
  onMounted(() => {
    // getMessageList();
  });

  watch(
    () => props.visible,
    (val) => {
      if (val) {
        defaultSelectKeys.value = [];
        getMessageList();
        notificationForm.value.type = '0';
      }
    }
  );
</script>

<style scoped lang="less">
  .header {
    //border: 1px solid red;
    width: 100%;
    height: 20px;
    line-height: 20px;
  }
  :deep('.arco-trigger-popup') {
    width: 150px;
  }
</style>
