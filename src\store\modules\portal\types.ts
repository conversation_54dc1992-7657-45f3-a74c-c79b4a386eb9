export interface PortalInfo {
  id: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  name: string;
  type: number;
  parentId: string;
  relationId?: string;
  pathId: string;
  pathName: string;
  parentName: string;
  sort?: number;
  tenantId: number;
  ext1?: any;
  ext2?: any;
  ext3?: any;
  ext4?: any;
  ext5?: any;
}

export interface EditPortal {
  portalId: string;
  type: number;
}

export interface PortalState {
  portalList: PortalInfo[];
  currentPortal: PortalInfo;
  multiPortal: boolean;
  editPortal: EditPortal;
}
