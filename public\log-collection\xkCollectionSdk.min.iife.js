var xkCollectionSdk=function(Ut){"use strict";var ec=Object.defineProperty;var rc=(Ut,Rt,ie)=>Rt in Ut?ec(Ut,Rt,{enumerable:!0,configurable:!0,writable:!0,value:ie}):Ut[Rt]=ie;var ha=(Ut,Rt,ie)=>rc(Ut,typeof Rt!="symbol"?Rt+"":Rt,ie);typeof globalThis<"u"||typeof window<"u"||(typeof global<"u"?global:typeof self<"u");function Rt(S){return S&&S.__esModule&&Object.prototype.hasOwnProperty.call(S,"default")?S.default:S}var ie={exports:{}};ie.exports=function(){function S(t){if(!t)return!1;var e=Object.prototype.toString.call(t);return e=="[object Function]"||e=="[object AsyncFunction]"}function V(){return Date.now&&S(Date.now)?Date.now():new Date().getTime()}function d(t){return t!=null&&Object.prototype.toString.call(t)=="[object Object]"}function Z(){if(typeof Uint32Array=="function"){var t="";if(typeof crypto<"u"?t=crypto:typeof msCrypto<"u"&&(t=msCrypto),d(t)&&t.getRandomValues){var e=new Uint32Array(1);return t.getRandomValues(e)[0]/Math.pow(2,32)}}return _i(1e19)/1e19}function W(t){var e=null;try{e=JSON.parse(t)}catch{}return e}function bt(t,e){this.lockGetPrefix=t||"lock-get-prefix",this.lockSetPrefix=e||"lock-set-prefix"}function hr(t){return typeof t=="function"||!(!t||typeof t!="object")&&hr(t.listener)}function wt(){this._events={}}function Ht(t){var e=t;try{e=decodeURIComponent(t)}catch{e=t}return e}function ya(){function t(){}return typeof Object.create!="function"?(t.prototype=null,new t):Object.create(null)}function Ve(t){t=t||"";for(var e=ya(),r=t.substring(1).split("&"),n=0;n<r.length;n++){var i=r[n].indexOf("=");if(i!==-1){var a=r[n].substring(0,i),l=r[n].substring(i+1);a=Ht(a),l=Ht(l),a!=="__proto__"&&a!=="constructor"&&a!=="prototype"&&(e[a]=l)}}return e}function h(t){return Object.prototype.toString.call(t)=="[object String]"}function mt(t){return t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function mr(t){var e=function(r){this._fields={Username:4,Password:5,Port:7,Protocol:2,Host:6,Path:8,URL:0,QueryString:9,Fragment:10},this._values={},this._regex=/^((\w+):\/\/)?((\w+):?(\w+)?@)?([^\/\?:]+):?(\d+)?(\/?[^\?#]+)?\??([^#]+)?#?(\w*)/,r!==void 0&&this._parse(r)};return e.prototype.setUrl=function(r){this._parse(r)},e.prototype._initValues=function(){for(var r in this._fields)this._values[r]=""},e.prototype.addQueryString=function(r){if(typeof r!="object")return!1;var n=this._values.QueryString||"";for(var i in r)n=new RegExp(i+"[^&]+").test(n)?n.replace(new RegExp(i+"[^&]+"),i+"="+r[i]):n.slice(-1)==="&"?n+i+"="+r[i]:n===""?i+"="+r[i]:n+"&"+i+"="+r[i];this._values.QueryString=n},e.prototype.getUrl=function(){var r="";return r+=this._values.Origin,r+=this._values.Port?":"+this._values.Port:"",r+=this._values.Path,(r+=this._values.QueryString?"?"+this._values.QueryString:"")+(this._values.Fragment?"#"+this._values.Fragment:"")},e.prototype._parse=function(r){this._initValues();var n=this._regex.exec(r);n||Q.log("URLParser::_parse -> Invalid URL");var i=r.split("#"),a=i[0],l=i.slice(1).join("#");for(var u in n=this._regex.exec(a),this._fields)n[this._fields[u]]!==void 0&&(this._values[u]=n[this._fields[u]]);this._values.Hostname=this._values.Host.replace(/:\d+$/,""),this._values.Origin=this._values.Protocol+"://"+this._values.Hostname,this._values.Fragment=l},new e(t)}function Jt(t){var e={},r=function(){try{return new URL("http://modernizr.com/").href==="http://modernizr.com/"}catch{return!1}};if(typeof window.URL=="function"&&r())(e=new URL(t)).searchParams||(e.searchParams=function(){var i=Ve(e.search);return{get:function(a){return i[a]}}}());else{if(h(t)||(t=String(t)),t=mt(t),/^https?:\/\/.+/.test(t)===!1)return void Q.log("Invalid URL");var n=mr(t);e.hash=n._values.Fragment,e.host=n._values.Host?n._values.Host+(n._values.Port?":"+n._values.Port:""):"",e.href=n._values.URL,e.password=n._values.Password,e.pathname=n._values.Path,e.port=n._values.Port,e.search=n._values.QueryString?"?"+n._values.QueryString:"",e.username=n._values.Username,e.hostname=n._values.Hostname,e.protocol=n._values.Protocol?n._values.Protocol+":":"",e.origin=n._values.Origin?n._values.Origin+(n._values.Port?":"+n._values.Port:""):"",e.searchParams=function(){var i=Ve("?"+n._values.QueryString);return{get:function(a){return i[a]}}}()}return e}function rt(t){return!(!t||t.nodeType!==1)}function It(t){return t===void 0}function A(t){return Array.isArray&&S(A)?Array.isArray(t):Object.prototype.toString.call(t)==="[object Array]"}function dt(t){return new hi(t)}function ae(t,e,r,n){function i(u){return u&&(u.preventDefault=i.preventDefault,u.stopPropagation=i.stopPropagation,u._getPath=i._getPath),u}function a(u,c,f,_){var T=function(I){if(I=I||i(window.event)){I.target=I.srcElement;var F,Et,pt=!0;return typeof f=="function"&&(F=f(I)),Et=c.call(u,I),_!=="beforeunload"?(F!==!1&&Et!==!1||(pt=!1),pt):void 0}};return T}i._getPath=function(){var u=this;return this.path||this.composedPath&&this.composedPath()||dt(u.target).getParents()},i.preventDefault=function(){this.returnValue=!1},i.stopPropagation=function(){this.cancelBubble=!0};var l=function(u,c,f){if(n===void 0&&c==="click"&&(n=!0),u&&u.addEventListener)u.addEventListener(c,function(I){I._getPath=i._getPath,f.call(this,I)},n);else{var _="on"+c,T=u[_];u[_]=a(u,f,T,c)}};l.apply(null,arguments)}function fn(t){var e="pushState"in window.history?"popstate":"hashchange";ae(window,e,t)}function gn(t){if(t)return window.XMLHttpRequest!==void 0&&"withCredentials"in new XMLHttpRequest?new XMLHttpRequest:typeof XDomainRequest<"u"?new XDomainRequest:null;if(window.XMLHttpRequest!==void 0)return new XMLHttpRequest;if(window.ActiveXObject)try{return new ActiveXObject("Msxml2.XMLHTTP")}catch{try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(r){Q.log(r)}}}function $(t,e,r){if(Object.prototype.toString.call(t)==="[object Array]")if(Array.prototype.forEach&&t.forEach)t.forEach(e,r);else for(var n=0;n<t.length;n++)e.call(r,t[n],n,t);else{if(Object.prototype.toString.call(t)!=="[object Object]")return t;for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.call(r,t[i],i,t)}}function x(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];if(Object.prototype.toString.call(r)==="[object Object]")for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&r[n]!==void 0&&(t[n]=r[n])}return t}function _n(t){function e(u){if(!u)return"";try{return JSON.parse(u)}catch{return{}}}function r(){try{n&&typeof n=="object"&&n.abort&&n.abort()}catch(u){Q.log(u)}i&&(clearTimeout(i),i=null,t.error&&t.error(),n.onreadystatechange=null,n.onload=null,n.onerror=null)}t.timeout=t.timeout||2e4,t.credentials=t.credentials===void 0||t.credentials;var n=gn(t.cors);if(!n)return!1;t.type||(t.type=t.data?"POST":"GET");var i,a=(t=x({success:function(){},error:function(){}},t)).success,l=t.error;t.success=function(u){a(u),i&&(clearTimeout(i),i=null)},t.error=function(u){l(u),i&&(clearTimeout(i),i=null)},i=setTimeout(function(){r()},t.timeout),typeof XDomainRequest<"u"&&n instanceof XDomainRequest&&(n.onload=function(){t.success&&t.success(e(n.responseText)),n.onreadystatechange=null,n.onload=null,n.onerror=null},n.onerror=function(){t.error&&t.error(e(n.responseText),n.status),n.onreadystatechange=null,n.onerror=null,n.onload=null}),n.onreadystatechange=function(){try{n.readyState==4&&(n.status>=200&&n.status<300||n.status==304?t.success(e(n.responseText)):t.error(e(n.responseText),n.status),n.onreadystatechange=null,n.onload=null)}catch{n.onreadystatechange=null,n.onload=null}},n.open(t.type,t.url,!0);try{t.credentials&&(n.withCredentials=!0),d(t.header)&&$(t.header,function(u,c){n.setRequestHeader&&n.setRequestHeader(c,u)}),t.data&&(t.cors||n.setRequestHeader&&n.setRequestHeader("X-Requested-With","XMLHttpRequest"),t.contentType==="application/json"?n.setRequestHeader&&n.setRequestHeader("Content-type","application/json; charset=UTF-8"):n.setRequestHeader&&n.setRequestHeader("Content-type","application/x-www-form-urlencoded"))}catch(u){Q.log(u)}n.send(t.data||null)}function hn(t,e){var r=[];return t==null?r:Array.prototype.map&&t.map===Array.prototype.map?t.map(e):($(t,function(n,i,a){r.push(e(n,i,a))}),r)}function ve(t){var e=[];try{e=hn(atob(t).split(""),function(r){return"%"+("00"+r.charCodeAt(0).toString(16)).slice(-2)})}catch{e=[]}try{return decodeURIComponent(e.join(""))}catch{return e.join("")}}function je(t){var e="";try{e=btoa(encodeURIComponent(t).replace(/%([0-9A-F]{2})/g,function(r,n){return String.fromCharCode("0x"+n)}))}catch{e=t}return e}function ba(t,e){e=e||window;var r=!1,n=!0,i=e.document,a=i.documentElement,l=i.addEventListener,u=l?"addEventListener":"attachEvent",c=l?"removeEventListener":"detachEvent",f=l?"":"on",_=function(I){I.type=="readystatechange"&&i.readyState!="complete"||((I.type=="load"?e:i)[c](f+I.type,_,!1),!r&&(r=!0)&&t.call(e,I.type||I))},T=function(){try{a.doScroll("left")}catch{return void setTimeout(T,50)}_("poll")};if(i.readyState=="complete")t.call(e,"lazy");else{if(!l&&a.doScroll){try{n=!e.frameElement}catch(I){Q.log(I)}n&&T()}i[u](f+"DOMContentLoaded",_,!1),i[u](f+"readystatechange",_,!1),e[u](f+"load",_,!1)}}function vr(t){return $(Array.prototype.slice.call(arguments,1),function(e){for(var r in e)e[r]!==void 0&&t[r]===void 0&&(t[r]=e[r])}),t}function ye(t){var e=t;try{e=decodeURI(t)}catch{e=t}return e}function wa(t){var e="t6KJCZa5pDdQ9khoEM3Tj70fbP2eLSyc4BrsYugARqFIw1mzlGNVXOHiWvxUn8",r=e.length-1,n={},i=0;for(i=0;i<e.length;i++)n[e.charAt(i)]=e.charAt(r-i);var a="";for(i=0;i<t.length;i++)a+=t.charAt(i)in n?n[t.charAt(i)]:t.charAt(i);return a}function Ne(t){return Object.prototype.toString.call(t)=="[object Date]"}function yr(t){function e(r){return r<10?"0"+r:r}return t.getFullYear()+"-"+e(t.getMonth()+1)+"-"+e(t.getDate())+" "+e(t.getHours())+":"+e(t.getMinutes())+":"+e(t.getSeconds())+"."+e(t.getMilliseconds())}function mn(t){return $(t,function(e,r){Ne(e)?t[r]=yr(e):d(e)&&(t[r]=mn(e))}),t}function vn(t){return $(Array.prototype.slice.call(arguments,1),function(e){for(var r in e)e[r]!==void 0&&(d(e[r])&&d(t[r])?x(t[r],e[r]):t[r]=e[r])}),t}function yn(t,e,r){var n=Object.prototype.hasOwnProperty;if(t.filter)return t.filter(e);for(var i=[],a=0;a<t.length;a++)if(n.call(t,a)){var l=t[a];e.call(r,l,a,t)&&i.push(l)}return i}function Sa(t){try{return JSON.stringify(t,null,"  ")}catch{return JSON.stringify(t)}}function ka(t){return typeof t=="string"&&t.match(/^[a-zA-Z0-9\u4e00-\u9fa5\-\.]+$/)?t:""}function br(t,e,r){e=e||"domain_test";var n=ka(t=t||location.hostname),i=n.split(".");if(A(i)&&i.length>=2&&!/^(\d+\.)+\d+$/.test(n)){for(var a="."+i.splice(i.length-1,1);i.length>0;)if(a="."+i.splice(i.length-1,1)+a,ue.set(e,"true",0,null,r,"; domain="+a),document.cookie.indexOf(e+"=true")!==-1)return ue.set(e,"true","-1s",null,r,"; domain="+a),a}return""}function qe(t){function e(a,l){var u;if((a=mt(a))==="body")return document.getElementsByTagName("body")[0];if(a.indexOf("#")===0)a=a.slice(1),u=document.getElementById(a);else if(a.indexOf(":nth-of-type")>-1){var c=a.split(":nth-of-type");if(!c[0]||!c[1])return null;var f=c[0],_=c[1].match(/\(([0-9]+)\)/);if(!_||!_[1])return null;var T=Number(_[1]);if(!(rt(l)&&l.children&&l.children.length>0))return null;for(var I=l.children,F=0;F<I.length;F++)if(rt(I[F])&&I[F].tagName.toLowerCase()===f&&--T==0){u=I[F];break}if(T>0)return null}return u||null}function r(a){var l,u=n.shift();if(!u)return a;try{l=e(u,a)}catch(c){Q.log(c)}return l&&rt(l)?r(l):null}if(!h(t))return null;var n=t.split(">"),i=null;return(i=r())&&rt(i)?i:null}function bn(t,e){var r="",n="";return t.textContent?r=mt(t.textContent):t.innerText&&(r=mt(t.innerText)),r&&(r=r.replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)),n=r||"",e!=="input"&&e!=="INPUT"||(n=t.value||""),n}function Tt(t,e){e&&typeof e=="string"||(e="hostname解析异常");var r=null;try{r=Jt(t).hostname}catch{Q.log("getHostname传入的url参数不合法！")}return r||e}function wr(){try{var t=navigator.appVersion.match(/OS (\d+)[._](\d+)[._]?(\d+)?/);return t&&t[1]?Number.parseInt(t[1],10):""}catch{return""}}function Mt(t,e){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),t=Ht(t);var r=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(t);return r===null||r&&typeof r[1]!="string"&&r[1].length?"":Ht(r[1])}function Ie(t){var e={},r=t.split("?")[1]||"";return r&&(e=Ve("?"+r)),e}function wn(){return window.matchMedia!==void 0||window.msMatchMedia!==void 0}function Pa(){var t=screen.msOrientation||screen.mozOrientation||(screen.orientation||{}).type,e="未取到值";if(t)e=t.indexOf("landscape")>-1?"landscape":"portrait";else if(wn()){var r=window.matchMedia||window.msMatchMedia;r("(orientation: landscape)").matches?e="landscape":r("(orientation: portrait)").matches&&(e="portrait")}return e}function Sr(){var t,e={},r=navigator.userAgent.toLowerCase();return(t=r.match(/ qq\/([\d.]+)/))?e.qqBuildinBrowser=Number(t[1].split(".")[0]):(t=r.match(/mqqbrowser\/([\d.]+)/))?e.qqBrowser=Number(t[1].split(".")[0]):(t=r.match(/opera.([\d.]+)/))?e.opera=Number(t[1].split(".")[0]):(t=r.match(/msie ([\d.]+)/))?e.ie=Number(t[1].split(".")[0]):(t=r.match(/edge.([\d.]+)/))?e.edge=Number(t[1].split(".")[0]):(t=r.match(/firefox\/([\d.]+)/))?e.firefox=Number(t[1].split(".")[0]):(t=r.match(/chrome\/([\d.]+)/))?e.chrome=Number(t[1].split(".")[0]):(t=r.match(/version\/([\d.]+).*safari/))?e.safari=Number(t[1].match(/^\d*.\d*/)):(t=r.match(/trident\/([\d.]+)/))&&(e.ie=11),e}function st(t){return h(t)?ye(t=mt(t)):ye(location.href)}function se(t){return h(t)?ye(t=mt(t)):ye(location.pathname)}function kr(t,e){return t.hasAttribute?t.hasAttribute(e):t.attributes?!(!t.attributes[e]||!t.attributes[e].specified):void 0}function Pr(t,e){if(typeof e=="string")return kr(t,e);if(A(e)){for(var r=!1,n=0;n<e.length;n++)if(kr(t,e[n])){r=!0;break}return r}}function Sn(t){if(typeof t!="string")return 0;var e=0;if(t.length==0)return e;for(var r=0;r<t.length;r++)e=(e<<5)-e+t.charCodeAt(r),e&=e;return e}function Cr(t){var e=9007199254740992,r=-9007199254740992,n=31,i=0;if(t.length>0)for(var a=t.split(""),l=0;l<a.length;l++){var u=a[l].charCodeAt(),c=n*i+u;if(c>e)for(i=r+i;(c=n*i+u)<r;)i=i/2+u;if(c<r)for(i=e+i;(c=n*i+u)>e;)i=i/2+u;i=n*i+u}return i}function nt(t,e){var r=t.indexOf;if(r)return r.call(t,e);for(var n=0;n<t.length;n++)if(e===t[n])return n;return-1}function Ca(t,e){return t.prototype=new e,t.prototype.constructor=t,t.superclass=e.prototype,t}function kn(t){return!(!t||!Nl.call(t,"callee"))}function Pn(t){return Object.prototype.toString.call(t)=="[object Boolean]"}function it(t){if(d(t)){for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}return!1}function Cn(t){return typeof t=="string"&&(/^https?:\/\/.+/.test(t)!==!1||(Q.log("Invalid URL"),!1))}function On(){return!!navigator.userAgent.match(/iPhone|iPad|iPod/i)}function ze(t){try{JSON.parse(t)}catch{return!1}return!0}function $t(t){return Object.prototype.toString.call(t)=="[object Number]"&&/[\d\.]+/.test(String(t))}function Oa(){var t=!1;if(typeof navigator!="object"||typeof navigator.sendBeacon!="function")return t;var e=Sr(),r=navigator.userAgent.toLowerCase();if(/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)){var n=/os [\d._]*/gi,i=(r.match(n)+"").replace(/[^0-9|_.]/gi,"").replace(/_/gi,".").split(".");e.safari===void 0&&(e.safari=i[0]),i[0]&&(e.qqBuildinBrowser||e.qqBrowser)?t=!1:i[0]&&i[0]<13?(e.chrome>41||e.firefox>30||e.opera>25||e.safari>12)&&(t=!0):(e.chrome>41||e.firefox>30||e.opera>25||e.safari>11.3)&&(t=!0)}else(e.chrome>38||e.edge>13||e.firefox>30||e.opera>25||e.safari>11)&&(t=!0);return t}function ja(){return window.XMLHttpRequest!==void 0&&("withCredentials"in new XMLHttpRequest||typeof XDomainRequest<"u")}function jn(t){if(!d(t)||!h(t.callbackName))return Q.log("JSONP 请求缺少 callbackName"),!1;t.success=S(t.success)?t.success:function(){},t.error=S(t.error)?t.error:function(){},t.data=t.data||"";var e=document.createElement("script"),r=document.getElementsByTagName("head")[0],n=null,i=!1;r.appendChild(e);var a=3e4;$t(t.timeout)&&(n=setTimeout(function(){return!i&&(t.error("timeout"),window[t.callbackName]=function(){Q.log("call jsonp error")},n=null,r.removeChild(e),void(i=!0))},Math.min(t.timeout,a))),window[t.callbackName]=function(){clearTimeout(n),n=null,t.success.apply(null,arguments),window[t.callbackName]=function(){Q.log("call jsonp error")},r.removeChild(e)};var l=encodeURIComponent(t.callbackName),u="";if(t.url.indexOf("?")>-1?t.url+="&callbackName="+l:t.url+="?callbackName="+l,d(t.data)){var c=[];$(t.data,function(f,_){c.push(encodeURIComponent(_)+"="+encodeURIComponent(f))}),(u=c.join("&"))&&(t.url+="&"+u)}e.onerror=function(f){return!i&&(window[t.callbackName]=function(){Q.log("call jsonp error")},clearTimeout(n),n=null,r.removeChild(e),t.error(f),void(i=!0))},e.src=t.url}function Nn(t){({visibleHandler:S(t.visible)?t.visible:function(){},hiddenHandler:S(t.hidden)?t.hidden:function(){},visibilityChange:null,hidden:null,isSupport:function(){return document[this.hidden]!==void 0},init:function(){document.hidden!==void 0?(this.hidden="hidden",this.visibilityChange="visibilitychange"):document.mozHidden!==void 0?(this.hidden="mozHidden",this.visibilityChange="mozvisibilitychange"):document.msHidden!==void 0?(this.hidden="msHidden",this.visibilityChange="msvisibilitychange"):document.webkitHidden!==void 0&&(this.hidden="webkitHidden",this.visibilityChange="webkitvisibilitychange"),this.listen()},listen:function(){if(this.isSupport()){var e=this;ae(document,this.visibilityChange,function(){document[e.hidden]?e.hiddenHandler():e.visibleHandler()},1)}else ae(window,"focus",this.visibleHandler),ae(window,"blur",this.hiddenHandler)}}).init()}function We(t){t=x({success:function(){},error:function(){},appendCall:function(r){document.getElementsByTagName("head")[0].appendChild(r)}},t);var e=null;t.type==="css"&&((e=document.createElement("link")).rel="stylesheet",e.href=t.url),t.type==="js"&&((e=document.createElement("script")).async="async",e.setAttribute("charset","UTF-8"),e.src=t.url,e.type="text/javascript"),e.onload=e.onreadystatechange=function(){this.readyState&&this.readyState!=="loaded"&&this.readyState!=="complete"||(t.success(),e.onload=e.onreadystatechange=null)},e.onerror=function(){t.error(),e.onerror=null},t.appendCall(e)}function Na(t){if(typeof t!="string")return"";for(var e=/^\s*javascript/i;e.test(t);)t=t.replace(e,"");return t}function In(t,e){e=typeof e=="number"?e:13;for(var r=126,n=(t=String(t)).split(""),i=0;i<n.length;i++)n[i].charCodeAt(0)<r&&(n[i]=String.fromCharCode((n[i].charCodeAt(0)+e)%r));return n.join("")}function Tn(t){var e=13,r=126;return In(t=String(t),r-e)}function Or(t){d(t)&&$(t,function(e,r){d(e)?Or(t[r]):Ne(e)&&(t[r]=yr(e))})}function Xe(t){var e=document.createElement("style");e.type="text/css";try{e.appendChild(document.createTextNode(t))}catch{e.styleSheet.cssText=t}var r=document.getElementsByTagName("head")[0],n=document.getElementsByTagName("script")[0];r?r.children.length?r.insertBefore(e,r.children[0]):r.appendChild(e):n.parentNode.insertBefore(e,n)}function Ia(t){if(typeof t!="string")return Q.log("转换unicode错误",t),t;for(var e="",r=0;r<t.length;r++)e+="\\"+t.charCodeAt(r).toString(16);return e}function Ta(t,e,r){var n,i,a,l=null,u=0;r||(r={});var c=function(){u=r.leading===!1?0:V(),l=null,a=t.apply(n,i),l||(n=i=null)};return function(){var f=V();u||r.leading!==!1||(u=f);var _=e-(f-u);return n=this,i=arguments,_<=0||_>e?(l&&(clearTimeout(l),l=null),u=f,a=t.apply(n,i),l||(n=i=null)):l||r.trailing===!1||(l=setTimeout(c,_)),a}}function $n(t){var e=[];return t==null||$(t,function(r){e[e.length]=r}),e}function $a(t){return t?t.toArray?t.toArray():A(t)||kn(t)?Array.prototype.slice.call(t):$n(t):[]}function Ze(t){for(var e,r=[],n={},i=0;i<t.length;i++)(e=t[i])in n||(n[e]=!0,r.push(e));return r}function jr(t,e,r){return r=r||0,t.substr(r,e.length)===e}function Nr(t){return t!=="__proto__"&&t!=="constructor"&&t!=="prototype"}function Dt(){ce.msg.apply(ce,arguments).log()}function j(){ce.msg.apply(ce,arguments).level("warn").log()}function D(){ce.msg.apply(ce,arguments).level("error").log()}function Ir(t){var e=y.current_domain;switch(typeof e){case"function":var r=e();return r===""||mt(r)===""?"url解析失败":r.indexOf(".")!==-1?r:"url解析失败";case"string":return e===""||mt(e)===""?"url解析失败":e.indexOf(".")!==-1?e:"url解析失败";default:var n=br(null,yi,y.is_secure_cookie);return t===""||n===""?"url解析失败":n}}function Dn(t,e){var r="";if(y.cross_subdomain===!1){try{if(!e){var n=location.host;It(s.para.white_list[n])||(r=s.para.white_list[n])}}catch(i){D(i)}r=typeof r=="string"&&r!==""?"sajssdk_2015_"+y.sdk_id+t+"_"+r.replace(/\./g,"_"):"sajssdk_2015_root_"+y.sdk_id+t}else r="sajssdk_2015_cross_"+y.sdk_id+t;return r}function Ge(){var t="new_user";return vt.isSupport()?vt.get("sensorsdata_is_new_user")!==null||vt.get(Dn(t))!==null:nr.get(nr.getNewUserFlagMemoryKey(t))!==null}function oe(t,e,r){var n=!(!d(y.heatmap)||!y.heatmap.useCapture);return d(y.heatmap)&&It(y.heatmap.useCapture)&&e==="click"&&(n=!0),ae(t,e,r,n)}function Te(){var t=document.referrer,e="baidu.com";if(!t)return!1;try{var r=Jt(t).hostname;return r&&r.substring(r.length-e.length)===e}catch{return!1}}function xn(){var t=Ie(document.referrer);if(it(t)||!t.eqid){var e=Ie(location.href);return t.ck||e.utm_source?"baidu_sem_keyword_id":"baidu_other_keyword_id"}return"baidu_seo_keyword_id"}function An(){var t=Ie(document.referrer);return it(t)||!t.eqid?Xt().replace(/-/g,""):t.eqid}function Wt(t,e){return h(t=t||document.referrer)?((t=ye(t=mt(t))).indexOf("https://www.baidu.com/")!==0||e||(t=t.split("?")[0]),h(t=t.slice(0,y.max_referrer_string_length))?t:""):"取值异常_referrer异常_"+String(t)}function Qe(t){if((t=t||document.referrer)==="")return!0;var e=br(null,yi,y.is_secure_cookie),r=Tt(t);return(r="."+r).indexOf(e)===-1&&e!==""}function $e(t,e){t=t||document.referrer;var r=y.source_type.keyword;if(document&&h(t)){if(t.indexOf("http")===0){var n=En(t),i=Ie(t);if(it(i))return y.preset_properties.search_keyword_baidu&&Te()?void 0:"未取到值";var a=null;for(var l in r)if(n===l&&d(i)){if(A(a=r[l]))for(l=0;l<a.length;l++){var u=i[a[l]];if(u)return e?{active:u}:u}else if(i[a])return e?{active:i[a]}:i[a]}return y.preset_properties.search_keyword_baidu&&Te()?void 0:"未取到值"}return t===""?"未取到值_直接打开":"未取到值_非http的url"}return"取值异常_referrer异常_"+String(t)}function En(t){var e=Tt(t);if(!e||e==="hostname解析异常")return"";var r={baidu:[/^.*\.baidu\.com$/],bing:[/^.*\.bing\.com$/],google:[/^www\.google\.com$/,/^www\.google\.com\.[a-z]{2}$/,/^www\.google\.[a-z]{2}$/],sm:[/^m\.sm\.cn$/],so:[/^.+\.so\.com$/],sogou:[/^.*\.sogou\.com$/],yahoo:[/^.*\.yahoo\.com$/]};for(var n in r)for(var i=r[n],a=0,l=i.length;a<l;a++)if(i[a].test(e))return n;return"未知搜索引擎"}function Tr(){function t(u,c){for(var f=0;f<u.length;f++)if(c.split("?")[0].indexOf(u[f])!==-1)return!0}var e="("+y.source_type.utm.join("|")+")\\=[^&]+",r=y.source_type.search,n=y.source_type.social,i=document.referrer||"",a=L.pageProp.url;if(a){var l=a.match(new RegExp(e));return l&&l[0]?"付费广告流量":t(r,i)?"自然搜索流量":t(n,i)?"社交网站流量":i===""?"直接流量":"引荐流量"}return"获取url异常"}function Ln(t){var e=Mt(t,"gdt_vid"),r=Mt(t,"hash_key"),n=Mt(t,"callbacks"),i={click_id:"",hash_key:"",callbacks:""};return h(e)&&e.length&&(i.click_id=e.length==16||e.length==18?e:"参数解析不合法",h(r)&&r.length&&(i.hash_key=r),h(n)&&n.length&&(i.callbacks=n)),i}function Un(t){var e=t.properties,r=JSON.parse(JSON.stringify(t));d(e)&&$(e,function(n,i){if(S(n))try{e[i]=n(r),S(e[i])&&(j("您的属性- "+i+" 格式不满足要求，我们已经将其删除"),delete e[i])}catch{delete e[i],j("您的属性- "+i+" 抛出了异常，我们已经将其删除")}})}function Rn(t){if(d(t)&&t.$option){var e=t.$option;return delete t.$option,e}return{}}function Hn(t){var e={};return $(t,function(r,n){r!=null&&(e[n]=r)}),e}function Da(t){var e=!t.type||t.type.slice(0,7)!=="profile",r="取值异常";d(t.properties)&&e&&("$referrer"in t.properties&&(t.properties.$referrer_host=t.properties.$referrer===""?"":Tt(t.properties.$referrer,r)),y.preset_properties.latest_referrer&&y.preset_properties.latest_referrer_host&&(t.properties.$latest_referrer_host=t.properties.$latest_referrer===""?"":Tt(t.properties.$latest_referrer,r)))}function xa(t){var e=!t.type||t.type.slice(0,7)!=="profile",r=y.preset_properties&&e;r&&y.preset_properties.url&&It(t.properties.$url)&&(t.properties.$url=st()),r&&y.preset_properties.title&&It(t.properties.$title)&&(t.properties.$title=document.title)}function Jn(t){if(!rt(t.target))return!1;var e=t.target,r=h(e.tagName)?e.tagName.toLowerCase():"unknown",n={};return n.$element_type=r,n.$element_name=e.getAttribute("name"),n.$element_id=e.getAttribute("id"),n.$element_class_name=h(e.className)?e.className:null,n.$element_target_url=e.getAttribute("href"),n.$element_content=Ye(e,r),(n=Hn(n)).$url=st(),n.$url_path=se(),n.$title=document.title,n}function Aa(t){var e=y.heatmap&&S(y.heatmap.collect_input)&&y.heatmap.collect_input(t);return(t.type==="button"||t.type==="submit"||e)&&t.value||""}function Ye(t,e){return h(e)&&e.toLowerCase()==="input"?Aa(t):bn(t,e)}function $r(t){return Zt.protocol.ajax(t.url),_n(t)}function Mn(t){if(typeof t=="string"&&(t=mt(t))&&(t.slice(0,3)==="://"?t=location.protocol.slice(0,-1)+t:t.slice(0,2)==="//"?t=location.protocol+t:t.slice(0,4)!=="http"&&(t="")),A(t)&&t.length)for(var e=0;e<t.length;e++)/sa\.gif[^\/]*$/.test(t[e])||(t[e]=t[e].replace(/\/sa$/,"/sa.gif").replace(/(\/sa)(\?[^\/]+)$/,"/sa.gif$2"));else/sa\.gif[^\/]*$/.test(t)||typeof t!="string"||(t=t.replace(/\/sa$/,"/sa.gif").replace(/(\/sa)(\?[^\/]+)$/,"/sa.gif$2"));return t}function Bn(t){h(t)||(t=JSON.stringify(t));var e=je(t),r="crc="+Sn(e);return"data="+encodeURIComponent(e)+"&ext="+encodeURIComponent(r)}function Ea(t){var e,r=location.href,n=window.history.pushState,i=window.history.replaceState;S(window.history.pushState)&&(window.history.pushState=function(){n.apply(window.history,arguments),t(r),r=location.href}),S(window.history.replaceState)&&(window.history.replaceState=function(){i.apply(window.history,arguments),t(r),r=location.href}),e=window.document.documentMode?"hashchange":n?"popstate":"hashchange",ae(window,e,function(){t(r),r=location.href})}function Fn(t,e){var r=[];typeof t=="string"&&t in q.EVENT_LIST&&(r=q.EVENT_LIST[t],q[r[0]].on(r[1],e))}function Kn(){this.sendTimeStamp=0,this.timer=null,this.serverUrl="",this.hasTabStorage=!1}function H(t,e){for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&!Al.check(r,t[r],e))return!1;return!0}function La(t,e){return d(t)&&$(t,function(r,n){if(A(r)){var i=[];$(r,function(l){if(h(l))i.push(l);else if(It(l))i.push("null");else try{i.push(JSON.stringify(l))}catch{j("您的数据-",n,r,"数组里值有错误,已将其删除")}}),t[n]=i}var a=nt([],n)>-1;if(d(r)&&n!=="$option"&&!a)try{t[n]=JSON.stringify(r)}catch{delete t[n],j("您的数据-",n,r,"数据值有错误，已将其删除")}else h(r)||$t(r)||Ne(r)||Pn(r)||A(r)||S(r)||n==="$option"||a||(j("您的数据-",n,r,"-格式不满足要求，我们已经将其删除"),delete t[n])}),t}function Ua(t,e){return $t(e)&&t.length>e?(j("字符串长度超过限制，已经做截取--"+t),t.slice(0,e)):t}function Ra(t,e){var r=["distinct_id","user_id","id","date","datetime","event","events","first_id","original_id","device_id","properties","second_id","time","users"];d(t)&&$(r,function(n,i){n in t&&(nt([],n)>-1||(i<3?(delete t[n],j("您的属性- "+n+"是保留字段，我们已经将其删除")):j("您的属性- "+n+"是保留字段，请避免其作为属性名")))})}function Vn(t){var e=["$element_selector","$element_path"],r=["sensorsdata_app_visual_properties"];d(t)&&$(t,function(n,i){if(d(n))Vn(t[i]);else if(h(n)){if(nt(r,i)>-1)return;t[i]=Ua(n,nt(e,i)>-1?1024:y.max_string_length)}})}function Ha(t){t.properties.$project!==void 0&&(t.project=t.properties.$project,delete t.properties.$project),t.properties.$token!==void 0&&(t.token=t.properties.$token,delete t.properties.$token)}function Ja(t){if("item_type"in t){var e=function(n){return n||delete t.item_type,!0};H({item_type:t.item_type},e)}if("item_id"in t){var r=function(n,i,a){return n||a!=="string"||delete t.item_id,!0};H({item_id:t.item_id},r)}}function Ma(t,e){$(t,function(r,n){var i=function(a,l,u){return a||u==="keyLength"||delete t[n],!0};nt([],n)===-1&&H({propertyKey:n},i)})}function Ba(t){var e=t.properties;Or(t),d(e)?(La(e),Ra(e),Ha(t),Ma(e),Vn(e)):"properties"in t&&(t.properties={}),Ja(t)}function Fa(t,e){var r=e.sensors;return t._track_id=Number(String(Z()).slice(2,5)+String(Z()).slice(2,4)+String(new Date().getTime()).slice(-4)),t._flush_time=new Date().getTime(),r.events.tempAdd("send",t),t}function Ka(t,e){try{var r=e.sensors,n={};d(t)&&d(t.identities)&&!it(t.identities)?x(n,t.identities):x(n,p.getIdentities());var i={identities:n,distinct_id:p.getDistinctId(),lib:{$lib:"js",$lib_method:"code",$lib_version:String(r.lib_version)},properties:{}};return d(t)&&d(t.properties)&&!it(t.properties)&&(t.properties.$lib_detail&&(i.lib.$lib_detail=t.properties.$lib_detail,delete t.properties.$lib_detail),t.properties.$lib_method&&(i.lib.$lib_method=t.properties.$lib_method,delete t.properties.$lib_method)),vn(i,p.getUnionId(),t),d(t.properties)&&!it(t.properties)&&x(i.properties,t.properties),i.event==="$UnbindID"&&(i.login_id&&delete i.login_id,i.anonymous_id&&delete i.anonymous_id),t.type&&t.type.slice(0,7)==="profile"||(i.properties=x({},L.properties(),p.getProps(),p.getSessionProps(),L.currentProps,i.properties),r.para.preset_properties.latest_referrer&&!h(i.properties.$latest_referrer)&&(i.properties.$latest_referrer="取值异常"),r.para.preset_properties.latest_search_keyword&&!h(i.properties.$latest_search_keyword)&&(r.para.preset_properties.search_keyword_baidu&&h(i.properties.$search_keyword_id)&&$t(i.properties.$search_keyword_id_hash)&&h(i.properties.$search_keyword_id_type)||(i.properties.$latest_search_keyword="取值异常")),r.para.preset_properties.latest_traffic_source_type&&!h(i.properties.$latest_traffic_source_type)&&(i.properties.$latest_traffic_source_type="取值异常"),r.para.preset_properties.latest_landing_page&&!h(i.properties.$latest_landing_page)&&(i.properties.$latest_landing_page="取值异常"),r.para.preset_properties.latest_wx_ad_click_id==="not_collect"?(delete i.properties._latest_wx_ad_click_id,delete i.properties._latest_wx_ad_hash_key,delete i.properties._latest_wx_ad_callbacks):r.para.preset_properties.latest_wx_ad_click_id&&!h(i.properties._latest_wx_ad_click_id)&&(i.properties._latest_wx_ad_click_id="取值异常",i.properties._latest_wx_ad_hash_key="取值异常",i.properties._latest_wx_ad_callbacks="取值异常"),h(i.properties._latest_wx_ad_click_id)&&(i.properties.$url=st())),i.properties.$time&&Ne(i.properties.$time)?(i.time=1*i.properties.$time,delete i.properties.$time):i.time=1*new Date,function(a){if(r.bridge&&r.bridge.bridge_info.verify_success==="success"){var l=Kr.customProp.geth5Props(JSON.parse(JSON.stringify(a)));d(l)&&!it(l)&&(a.properties=x(a.properties,l))}var u=Gt.customProp.getVtrackProps(JSON.parse(JSON.stringify(a)));d(u)&&!it(u)&&(a.properties=x(a.properties,u))}(i),Un(i),Ee.checkIsAddSign(i),Ee.checkIsFirstTime(i),Da(i),xa(i),i}catch(a){return{_debug_web_msg:String(a)}}}function Va(t){return Vr.stage.process("basicProps",t)}function qa(t){return Vr.stage.process("formatData",t)}function qn(t,e,r,n){function i(l){function u(){c||(c=!0,location.href=a.href)}l.stopPropagation(),l.preventDefault();var c=!1;setTimeout(u,1e3),n(e,r,u)}var a=null;return(t=t||{}).ele&&(a=t.ele),t.event&&(a=t.target?t.target:t.event.target),r=r||{},!(!a||typeof a!="object")&&(!a.href||/^javascript/.test(a.href)||a.target||a.download||a.onclick?(n(e,r),!1):(t.event&&i(t.event),void(t.ele&&oe(t.ele,"click",function(l){i(l)}))))}function zn(){var t=location.protocol;return t==="http:"||t==="https:"?t:"http:"}function za(t){return qr.stage.process("webClickEvent",t)}function Wa(t){return qr.stage.process("webStayEvent",t)}function Dr(){var t=L.campaignParams(),e={};return $(t,function(r,n,i){(" "+s.source_channel_standard+" ").indexOf(" "+n+" ")!==-1?e["$"+n]=i[n]:e[n]=i[n]}),e}function Wn(t,e,r){if(s.is_first_visitor&&r){var n={};s.para.preset_properties.search_keyword_baidu&&Qe(document.referrer)&&Te()&&(n.$search_keyword_id=Le.id(),n.$search_keyword_id_type=Le.type(),n.$search_keyword_id_hash=Cr(n.$search_keyword_id));var i=Wt(null,e);t(x({$first_visit_time:new Date,$first_referrer:i,$first_referrer_host:i?Tt(i,"取值异常"):"",$first_browser_language:h(navigator.language)?navigator.language.toLowerCase():"取值异常",$first_browser_charset:h(document.charset)?document.charset.toUpperCase():"取值异常",$first_traffic_source_type:Tr(),$first_search_keyword:$e()},Dr(),n)),s.is_first_visitor=!1}}function Xn(t,e){var r=t.id,n=t.callback,i=t.name,a=p.getFirstId(),l=p.getOriginDistinctId();if(!H({distinct_id:r}))return D("login id is invalid"),!1;if(r===p.getOriginDistinctId()&&!a)return D("login id is equal to distinct_id"),!1;if(d(p._state.identities)&&p._state.identities.hasOwnProperty(i)&&r===p._state.first_id)return!1;if(p._state.history_login_id.name!==i||r!==p._state.history_login_id.value){p._state.identities[i]=r,p.set("history_login_id",{name:i,value:r}),a||p.set("first_id",l),e(r,"$SignUp",{},n);var u={$identity_cookie_id:p._state.identities.$identity_cookie_id};return u[i]=r,Zn(u),!0}return!1}function Zn(t){var e={};for(var r in t)e[r]=t[r];p._state.identities=e,p.save()}function Xa(t,e){if(!H({unbindKey:t,bindValue:e}))return!1;if(d(p._state.identities)&&p._state.identities.hasOwnProperty(t)&&p._state.identities[t]===e){var r=p.getUnionId().login_id;r&&t+"+"+e===r&&(p._state.distinct_id=p._state.first_id,p._state.first_id="",p.set("history_login_id",{name:"",value:""})),t!=="$identity_cookie_id"&&(delete p._state.identities[t],p.save())}var n={};return n[t]=e,n}function Za(){s._t=s._t||1*new Date,s.is_first_visitor=!1,s.source_channel_standard=vi}function Ga(t){x(y,t||s.para||{}),s.para=y;var e,r={};if(d(s.para.is_track_latest))for(var n in s.para.is_track_latest)r["latest_"+n]=s.para.is_track_latest[n];for(e in s.para.preset_properties=x({},er.preset_properties,r,s.para.preset_properties||{}),er)s.para[e]===void 0&&(s.para[e]=er[e]);typeof s.para.web_url!="string"||s.para.web_url.slice(0,3)!=="://"&&s.para.web_url.slice(0,2)!=="//"||(s.para.web_url.slice(0,3)==="://"?s.para.web_url=location.protocol.slice(0,-1)+s.para.web_url:s.para.web_url=location.protocol+s.para.web_url),Zt.protocol.serverUrl(),s.bridge&&s.bridge.initPara();var i=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"],a=["www.baidu.","m.baidu.","m.sm.cn","so.com","sogou.com","youdao.com","google.","yahoo.com/","bing.com/","ask.com/"],l=["weibo.com","renren.com","kaixin001.com","douban.com","qzone.qq.com","zhihu.com","tieba.baidu.com","weixin.qq.com"],u={baidu:["wd","word","kw","keyword"],google:"q",bing:"q",yahoo:"p",sogou:["query","keyword"],so:"q",sm:"q"};typeof s.para.source_type=="object"&&(s.para.source_type.utm=A(s.para.source_type.utm)?s.para.source_type.utm.concat(i):i,s.para.source_type.search=A(s.para.source_type.search)?s.para.source_type.search.concat(a):a,s.para.source_type.social=A(s.para.source_type.social)?s.para.source_type.social.concat(l):l,s.para.source_type.keyword=d(s.para.source_type.keyword)?x(u,s.para.source_type.keyword):u);var c={div:!1},f=["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"];if(s.para.heatmap&&!d(s.para.heatmap)&&(s.para.heatmap={}),d(s.para.heatmap)){s.para.heatmap.clickmap=s.para.heatmap.clickmap||"default",s.para.heatmap.scroll_notice_map=s.para.heatmap.scroll_notice_map||"default",s.para.heatmap.scroll_delay_time=s.para.heatmap.scroll_delay_time||4e3,s.para.heatmap.scroll_event_duration=s.para.heatmap.scroll_event_duration||18e3,s.para.heatmap.renderRefreshTime=s.para.heatmap.renderRefreshTime||1e3,s.para.heatmap.loadTimeout=s.para.heatmap.loadTimeout||1e3,s.para.heatmap.get_vtrack_config!==!0&&(s.para.heatmap.get_vtrack_config=!1);var _=A(s.para.heatmap.track_attr)?yn(s.para.heatmap.track_attr,function(T){return T&&typeof T=="string"}):[];_.push("data-sensors-click"),s.para.heatmap.track_attr=_,d(s.para.heatmap.collect_tags)?s.para.heatmap.collect_tags.div===!0?s.para.heatmap.collect_tags.div={ignore_tags:f,max_level:1}:d(s.para.heatmap.collect_tags.div)?(s.para.heatmap.collect_tags.div.ignore_tags?A(s.para.heatmap.collect_tags.div.ignore_tags)||(j("ignore_tags 参数必须是数组格式"),s.para.heatmap.collect_tags.div.ignore_tags=f):s.para.heatmap.collect_tags.div.ignore_tags=f,s.para.heatmap.collect_tags.div.max_level&&nt([1,2,3],s.para.heatmap.collect_tags.div.max_level)===-1&&(s.para.heatmap.collect_tags.div.max_level=1)):s.para.heatmap.collect_tags.div=!1:s.para.heatmap.collect_tags=c}s.para.server_url=Mn(s.para.server_url),s.para.noCache===!0?s.para.noCache="?"+new Date().getTime():s.para.noCache="",s.para.callback_timeout>s.para.datasend_timeout&&(s.para.datasend_timeout=s.para.callback_timeout),s.para.heatmap&&s.para.heatmap.collect_tags&&d(s.para.heatmap.collect_tags)&&$(s.para.heatmap.collect_tags,function(T,I){I!=="div"&&T&&s.heatmap.otherTags.push(I)}),s.para.heatmap&&s.para.heatmap.clickmap==="default"&&s.heatmap.initUnlimitedTags()}function Qa(){var t=Array.prototype.slice.call(arguments),e=t[0],r=t.slice(1);return typeof e=="string"&&sr[e]?sr[e].apply(sr,r):void(typeof e=="function"?e.apply(s,r):j("quick方法中没有这个功能"+t[0]))}function Gn(t,e){function r(){return!n.plugin_is_init&&n.init(s,e),n.plugin_is_init=!0,s.modules=s.modules||{},s.modules[n.plugin_name||"unnamed_"+Ul++]=n,n}if(h(t)||d(t)){var n;if(d(t)){var i=s.modules&&s.modules[t.plugin_name];i&&i!==t&&j(t.name+" is conflict with builtin plugin, and sdk uses builtin plugin."),n=i||t}return h(t)&&(d(s.modules)&&d(s.modules[t])?n=s.modules[t]:d(window.SensorsDataWebJSSDKPlugin)&&d(window.SensorsDataWebJSSDKPlugin[t])?n=window.SensorsDataWebJSSDKPlugin[t]:window.sensorsDataAnalytic201505&&window.sensorsDataAnalytic201505.modules[t]&&(n=window.sensorsDataAnalytic201505.modules[t])),n&&S(n.init)?n.plugin_is_init?n:(n.plugin_name||j("warning: invalid plugin, plugin_name required."),n.plugin_version?n.plugin_version!==s.lib_version&&j("warning: plugin version not match SDK version. plugin may not work correctly. "):j("warning: invalid plugin, plugin version required."),r()):(j((t.plugin_name||t)+" is not found or it's not a standard plugin. Please check sensorsdata official documents."),n)}D("use's first arguments must be string or object.")}function Ya(t,e,r){H({event:t,properties:e})&&ot.send({type:"track",event:t,properties:e},r)}function ts(t,e){return!!H({bindKey:t,bindValue:e})&&(p._state.identities[t]=e,p.save(),void ot.send({type:"track_id_bind",event:"$BindID",properties:{}}))}function es(t,e){var r=Xa(t,e);r&&ot.send({identities:r,type:"track_id_unbind",event:"$UnbindID",properties:{}})}function rs(t,e,r){typeof t=="object"&&t.tagName?qn({ele:t},e,r,s.track):typeof t=="object"&&t.target&&t.event&&qn(t,e,r,s.track)}function ns(t,e,r){return r=r||{},!(!t||typeof t!="object")&&!(!t.href||/^javascript/.test(t.href)||t.target)&&void oe(t,"click",function(n){function i(){a||(a=!0,location.href=t.href)}n.preventDefault();var a=!1;setTimeout(i,1e3),s.track(e,r,i)})}function is(t,e,r){H({item_type:t,item_id:e,properties:r})&&ot.sendItem({type:"item_set",item_type:t,item_id:e,properties:r||{}})}function as(t,e){H({item_type:t,item_id:e})&&ot.sendItem({type:"item_delete",item_type:t,item_id:e})}function ss(t,e){H({propertiesMust:t})&&ot.send({type:"profile_set",properties:t},e)}function os(t,e){H({propertiesMust:t})&&ot.send({type:"profile_set_once",properties:t},e)}function ls(t,e){H({propertiesMust:t})&&($(t,function(r,n){h(r)&&Nr(n)?t[n]=[r]:A(r)&&Nr(n)?t[n]=r:(delete t[n],j("appendProfile属性的值必须是字符串或者数组"))}),it(t)||ot.send({type:"profile_append",properties:t},e))}function us(t,e){function r(i){for(var a in i)if(Object.prototype.hasOwnProperty.call(i,a)&&!/-*\d+/.test(String(i[a])))return!1;return!0}var n=t;h(t)&&((t={})[n]=1),H({propertiesMust:t})&&(r(t)?ot.send({type:"profile_increment",properties:t},e):D("profile_increment的值只能是数字"))}function cs(t){ot.send({type:"profile_delete"},t),p.set("distinct_id",Xt()),p.set("first_id","")}function ps(t,e){var r=t,n={};h(t)&&(t=[]).push(r),A(t)?($(t,function(i){h(i)?n[i]=!0:j("profile_unset给的数组里面的值必须时string,已经过滤掉",i)}),ot.send({type:"profile_unset",properties:n},e)):D("profile_unset的参数必须是数组")}function ds(t){typeof t=="number"&&(t=String(t));var e=p.getFirstId();if(t===void 0){var r=Xt();e?p.set("first_id",r):p.set("distinct_id",r)}else H({distinct_id:t})&&(e?p.set("first_id",t):p.set("distinct_id",t))}function fs(t){if(p.getFirstId())return D("resetAnonymousIdentity must be used in a logout state ！"),!1;if(typeof t=="number"&&(t=String(t)),t===void 0){var e=Xt();p._state.identities.$identity_cookie_id=e,p.set("distinct_id",e)}else H({distinct_id:t})&&(p._state.identities.$identity_cookie_id=t,p.set("distinct_id",t))}function xr(t,e,r,n){var i=p.getFirstId()||p.getDistinctId();p.set("distinct_id",t),ot.send({original_id:i,distinct_id:p.getDistinctId(),type:"track_signup",event:e,properties:r},n)}function gs(t,e,r,n){typeof t=="number"&&(t=String(t)),H({distinct_id:t,event:e,properties:r})&&xr(t,e,r,n)}function _s(t){H({properties:t})?x(L.currentProps,t):D("register输入的参数有误")}function hs(t){p.clearAllProps(t)}function ms(t){var e;if(A(t)&&t.length>0)for(e=0;e<t.length;e++)h(t[e])&&t[e]in L.currentProps&&delete L.currentProps[t[e]];else if(t===!0)for(e in L.currentProps)delete L.currentProps[e]}function Qn(t){H({properties:t})?p.setProps(t):D("register输入的参数有误")}function vs(t){H({properties:t})?p.setPropsOnce(t):D("registerOnce输入的参数有误")}function ys(t){s.log("registerSession 方法已经弃用，有问题联系技术顾问"),H({properties:t})?p.setSessionProps(t):D("registerSession输入的参数有误")}function bs(t){s.log("registerSessionOnce 方法已经弃用，有问题联系技术顾问"),H({properties:t})?p.setSessionPropsOnce(t):D("registerSessionOnce输入的参数有误")}function Yn(t,e){typeof t=="number"&&(t=String(t)),!Xn({id:t,callback:e,name:Ae.LOGIN},xr)&&S(e)&&e()}function ws(t,e){return j("loginWithKey is deprecated !!!"),typeof e=="number"&&(e=String(e)),typeof t=="number"&&(t=String(t)),!!H({loginIdKey:t})&&(Ae.LOGIN===t?(Yn(e),!1):void Xn({id:e,callback:null,name:t},xr))}function Ss(t){var e=p.getFirstId();if(e)if(p.set("first_id",""),t===!0){var r=Xt();p.set("distinct_id",r)}else p.set("distinct_id",e);Zn({$identity_cookie_id:p._state.identities.$identity_cookie_id}),p.set("history_login_id",{name:"",value:""})}function ks(){function t(){var n=L.campaignParams(),i={};return $(n,function(a,l,u){(" "+s.source_channel_standard+" ").indexOf(" "+l+" ")!==-1?i["$"+l]=u[l]:i[l]=u[l]}),i}var e={$is_first_day:Ge(),$is_first_time:Ee.is_page_first_visited,$referrer:L.pageProp.referrer||"",$referrer_host:L.pageProp.referrer?Tt(L.pageProp.referrer):"",$url:st(),$url_path:se(),$title:document.title||"",_distinct_id:p.getDistinctId(),identities:p.getIdentities()},r=x({},L.properties(),p.getProps(),t(),e);return s.para.preset_properties.latest_referrer&&s.para.preset_properties.latest_referrer_host&&(r.$latest_referrer_host=r.$latest_referrer===""?"":Tt(r.$latest_referrer)),r}function Ps(){var t="",e=" { cursor: pointer; -webkit-tap-highlight-color: rgba(0,0,0,0); }";s.heatmap&&A(s.heatmap.otherTags)&&$(s.heatmap.otherTags,function(r){t+=r+e}),On()&&wr()&&wr()<13&&(s.para.heatmap&&s.para.heatmap.collect_tags&&s.para.heatmap.collect_tags.div&&Xe("div, [data-sensors-click]"+e),s.para.heatmap&&s.para.heatmap.track_attr&&Xe("["+s.para.heatmap.track_attr.join("], [")+"]"+e),t!==""&&Xe(t))}function De(t){var e=this;this.type=t,this.resultCbs={},this.timeoutCbs={},this.timerId=null,this.appCallJsCallback=null,window.sensorsdata_app_call_js||(window.sensorsdata_app_call_js=function(r,n){if(r in window.sensorsdata_app_call_js.modules)return window.sensorsdata_app_call_js.modules[r](n)}),window.sensorsdata_app_call_js.modules=window.sensorsdata_app_call_js.modules||{},window.sensorsdata_app_call_js.modules[this.type]=function(r){try{var n=ve(r)||r;try{n=JSON.parse(n)}catch{}var i=n&&n.message_id;if(i&&e.resultCbs[i]){if(r=n,e.timeoutCbs[i]&&e.timeoutCbs[i].isTimeout)return void(e.resultCbs[i].callbacks.length=0);if(e.resultCbs[i])for(var a in e.resultCbs[i].result=r,clearTimeout(e.timerId),e.timeoutCbs[i].callbacks.length=0,e.resultCbs[i].callbacks)e.resultCbs[i].callbacks[a].call(null,r),e.resultCbs[i].callbacks.splice(a,1);return}return e.appCallJsCallback&&e.appCallJsCallback.call(null,r)}catch{D("app 回调 js 异常",r)}}}function ti(t){try{if(s.bridge.activeBridge&&S(s.bridge.activeBridge.handleCommand))return s.bridge.activeBridge.handleCommand(t)}catch(e){D("Error: handle command exception:"+e)}return D("数据发往App失败，App没有暴露bridge,type:"+t.callType),!1}function Cs(t){function e(l){var u={hostname:"",project:""};try{l=Jt(l),u.hostname=l.hostname,u.project=l.searchParams.get("project")||"default"}catch(c){D("validateAppUrl:"+c)}return u}var r=e(t),n=e(s.para.server_url);if(r.hostname===n.hostname&&r.project===n.project)return!0;if(A(s.para.app_js_bridge.white_list))for(var i=0;i<s.para.app_js_bridge.white_list.length;i++){var a=e(s.para.app_js_bridge.white_list[i]);if(a.hostname===r.hostname&&a.project===r.project)return!0}return!1}function ei(t){this.bridge=new De(t.type)}function Os(){var t=L.pageProp.url_domain,e={};t===""&&(t="url解析失败");var r=$e(document.referrer,!0);if(y.preset_properties.search_keyword_baidu?Qe(document.referrer)&&(!Te()||d(r)&&r.active?p._state&&p._state.props&&(p._state.props.$search_keyword_id&&delete p._state.props.$search_keyword_id,p._state.props.$search_keyword_id_type&&delete p._state.props.$search_keyword_id_type,p._state.props.$search_keyword_id_hash&&delete p._state.props.$search_keyword_id_hash):(e.$search_keyword_id=Le.id(),e.$search_keyword_id_type=Le.type(),e.$search_keyword_id_hash=Cr(e.$search_keyword_id))):p._state&&p._state.props&&(p._state.props.$search_keyword_id&&delete p._state.props.$search_keyword_id,p._state.props.$search_keyword_id_type&&delete p._state.props.$search_keyword_id_type,p._state.props.$search_keyword_id_hash&&delete p._state.props.$search_keyword_id_hash),p.save(),$(y.preset_properties,function(l,u){if(u.indexOf("latest_")===-1)return!1;if(u=u.slice(7),l){if(u==="wx_ad_click_id"&&l==="not_collect")return!1;if(u!=="utm"&&t==="url解析失败")u==="wx_ad_click_id"?(e._latest_wx_ad_click_id="url的domain解析失败",e._latest_wx_ad_hash_key="url的domain解析失败",e._latest_wx_ad_callbacks="url的domain解析失败"):e["$latest_"+u]="url的domain解析失败";else if(Qe(document.referrer))switch(u){case"traffic_source_type":e.$latest_traffic_source_type=Tr();break;case"referrer":e.$latest_referrer=L.pageProp.referrer;break;case"search_keyword":$e()?e.$latest_search_keyword=$e():d(p._state)&&d(p._state.props)&&p._state.props.$latest_search_keyword&&delete p._state.props.$latest_search_keyword;break;case"landing_page":e.$latest_landing_page=st();break;case"wx_ad_click_id":var c=Ln(location.href);e._latest_wx_ad_click_id=c.click_id,e._latest_wx_ad_hash_key=c.hash_key,e._latest_wx_ad_callbacks=c.callbacks}}else if(u==="utm"&&p._state&&p._state.props)for(var f in p._state.props)(f.indexOf("$latest_utm")===0||f.indexOf("_latest_")===0&&f.indexOf("_latest_wx_ad_")<0)&&delete p._state.props[f];else p._state&&p._state.props&&"$latest_"+u in p._state.props?delete p._state.props["$latest_"+u]:u=="wx_ad_click_id"&&p._state&&p._state.props&&l===!1&&$(["_latest_wx_ad_click_id","_latest_wx_ad_hash_key","_latest_wx_ad_callbacks"],function(_){_ in p._state.props&&delete p._state.props[_]})}),y.preset_properties.latest_utm){var n=L.campaignParamsStandard("$latest_","_latest_"),i=n.$utms,a=n.otherUtms;it(i)||x(e,i),it(a)||x(e,a)}Qn(e)}function ri(t){var e=null;try{var r=JSON.parse(window.name);e=r[t]?Ht(r[t]):null}catch{e=null}return e===null&&(e=Mt(location.href,t)||null),e}function ni(t){function e(){var i=[];r.touch_app_bridge||i.push(Zt.defineMode("1")),d(s.para.app_js_bridge)||(i.push(Zt.defineMode("2")),r.verify_success=!1),d(s.para.heatmap)&&s.para.heatmap.clickmap=="default"||i.push(Zt.defineMode("3")),r.verify_success==="fail"&&i.push(Zt.defineMode("4")),new s.SDKJSBridge("app_alert").notifyApp({data:i})}var r=s.bridge.bridge_info;if(s.bridge.hasVisualModeBridge())if(d(s.para.heatmap)&&s.para.heatmap.clickmap=="default")if(d(s.para.app_js_bridge)&&r.verify_success==="success")if(t)window.sa_jssdk_app_define_mode(s,t);else{var n=location.protocol;We({success:function(){setTimeout(function(){typeof sa_jssdk_app_define_mode<"u"&&window.sa_jssdk_app_define_mode(s,t)},0)},error:function(){},type:"js",url:(n=nt(["http:","https:"],n)>-1?n:"https:")+"//static.sensorsdata.cn/sdk/"+s.lib_version+"/vapph5define.min.js"})}else e();else e()}function js(t){s.para.is_track_single_page&&Jr.on("switch",function(e){var r=function(i){if(i=i||{},e!==location.href){L.pageProp.referrer=st(e);var a=x({$url:st(),$referrer:st(e)},i);S(t)?t(a):s.quick&&s.quick("autoTrack",a)}};if(typeof s.para.is_track_single_page=="boolean")r();else if(typeof s.para.is_track_single_page=="function"){var n=s.para.is_track_single_page();d(n)?r(n):n===!0&&r()}})}function Ns(){s._q&&A(s._q)&&s._q.length>0&&$(s._q,function(t){s[t[0]].apply(s,Array.prototype.slice.call(t[1]))}),d(s.para.heatmap)&&(St.initHeatmap(),St.initScrollmap())}function Is(){s.readyState.setState(3),new s.SDKJSBridge("visualized").onAppNotify(function(){ni(typeof sa_jssdk_app_define_mode<"u")}),ni(!1),s.bridge.app_js_bridge_v1(),L.initPage(),js(),p.init(),Os(),$s(),s.readyState.setState(4),Ns()}function Ts(){Qt.isSeachHasKeyword()?Qt.hasKeywordHandle():window.parent!==self&&Ct.isSearchHasKeyword()?Ct.verifyVtrackMode():Qt.isWindowNameHasKeyword()?Qt.windowNameHasKeywordHandle():Qt.isStorageHasKeyword()?Qt.storageHasKeywordHandle():window.parent!==self&&Ct.isStorageHasKeyword()?Ct.verifyVtrackMode():(Is(),Ct.notifyUser())}function $s(){Gt.init(),s.bridge.bridge_info.verify_success==="success"&&Kr.init()}function ii(){$(Jl,function(t){var e=s[t];s[t]=function(){return s.readyState.state<3?(A(s._q)||(s._q=[]),j("calling sdk api before init is deprecated."),s._q.push([t,arguments]),!1):S(s.getDisabled)&&s.getDisabled()?void 0:s.readyState.getState()?e.apply(s,arguments):void D("请先初始化神策JS SDK")}})}function Ds(t,e){this.cancel=function(){t=!0},this.getCanceled=function(){return t||!1},this.stop=function(){e=!0},this.getStopped=function(){return e||!1}}function xs(t,e,r){var n=null;try{n=JSON.parse(JSON.stringify(t||null))}catch{}this.getOriginalData=function(){return n},this.getPosition=function(){return e},this.cancellationToken=new Ds,this.sensors=r}function le(t){if(!d(t))throw"error: Stage constructor requires arguments.";this.processDef=t,this.registeredInterceptors={}}function Ar(t){t&&t.buildDataStage&&Ci.registerStageImplementation(t.buildDataStage),t&&t.businessStage&&ji.registerStageImplementation(t.businessStage),t&&t.sendDataStage&&Oi.registerStageImplementation(t.sendDataStage),t&&t.viewStage&&Ni.registerStageImplementation(t.viewStage)}function Er(t,e){Ii[t]&&Ii[t](e)}function As(){return Wr.stage&&Wr.stage.process("getUtmData")}function Es(t){return Ti.stage.process("send",t)}function Ls(t){t.kit=Bl,t.saEvent=ot,this.buildDataStage=Vr,this.sendDataStage=Ti,this.businessStage=Wr}function Us(t){t.heatmap=St,this.viewStage=qr}function Rs(t){if(S(t.properties))if(!t.isMatchedWithFilter||S(t.isMatchedWithFilter)){var e={finalAdjustData:{priority:100,entry:function(r){try{if(S(t.isMatchedWithFilter))return t.isMatchedWithFilter(r)&&t.properties(r);t.properties(r)}catch(n){D("execute registerPropertyPlugin callback error:"+n)}}}};Er("buildDataStage",e)}else D("registerPropertyPlugin arguments error, isMatchedWithFilter must be function");else D("registerPropertyPlugin arguments error, properties must be function")}function Lr(t){t&&(s.events=wi,s.bridge=Hl,s.SDKJSBridge=De,s.JSBridge=ei,s.store=p,s.unlimitedDiv=ar,s.customProp=ki,s.vtrackcollect=Gt,s.vapph5collect=Kr,s.detectMode=Ts,s.registerFeature=Ar,s.registerInterceptor=Er,s.commonWays=sr,Ar(new Ls(s)),Ar(new Us(s)),Er("viewStage",Fl));var e=t?Rl:Ml;for(var r in e)s[r]=e[r];s.logger=ce,s.log=Dt,s._=Pi,s.on=Fn,s.ee=q,s.use=Gn,s.lib_version=rr,s.registerPropertyPlugin=Rs}function Hs(t,e,r){return t.plugin_version=Kl,t}function Js(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return Ms(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function Ms(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function Bs(t,e,r){return Js(t,e,r),t.plugin_version=zl,t}function Fs(t){return xi&&xi.call(xt,JSON.stringify(t))}function ai(t){return lr.call(xt)&&Ai&&Ai.call(xt,JSON.stringify(t))}function Ks(t,e){return e&&typeof e[t.callType]=="function"&&e[t.callType]()}function Vs(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return qs(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function qs(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function zs(t,e,r){return Vs(t,e,r),t.plugin_version=Zl,t}function Ws(){if(Ot=window.SensorsData_APP_New_H5_Bridge,or=Ot&&Ot.sensorsdata_track,Ue=or&&Ot.sensorsdata_get_server_url&&Ot.sensorsdata_get_server_url(),pe("---test---fail---",!J,J.bridge.activeBridge,!Ue),J&&!J.bridge.activeBridge&&Ue)return J.bridge.activeBridge=Li,J.para.app_js_bridge&&!J.para.app_js_bridge.is_mui&&(J.bridge.is_verify_success=Ue&&J.bridge.validateAppUrl(Ue),pe("---test---bridge-verify-",J.bridge.is_verify_success)),J.bridge.bridge_info={touch_app_bridge:!0,platform:"android",verify_success:J.bridge.is_verify_success?"success":"fail",support_two_way_call:!!Ot.sensorsdata_js_call_app},J.para.app_js_bridge?void J.registerInterceptor("sendDataStage",{send:{priority:60,entry:Xs}}):void pe("---test---app_js_bridge is not configured, data will not be sent by android bridge.")}function Xs(t,e){if(pe("---test---datasend-",J.bridge.is_verify_success),J.para.app_js_bridge.is_mui||t.data.type==="item_set"||t.data.type==="item_delete")return t;var r=t.callback;return J.bridge.is_verify_success?(pe("---test---bridge-verify-success---",t.data),or&&or.call(Ot,JSON.stringify(Re.extend({server_url:J.para.server_url},t.data))),Re.isFunction(r)&&r(),e.cancellationToken.cancel(),t):(pe("---test---bridge-verify-fail-----",J.bridge.is_verify_success),J.para.app_js_bridge.is_send?(J.debug.apph5({data:t.data,step:"4.2",output:"all"}),t):(Re.isFunction(r)&&r(),e.cancellationToken.cancel(),t))}function Zs(t){var e=t.callType;return e in Ei.commands?Ei.commands[e](t,Ot):void(Ot&&Re.isFunction(Ot.sensorsdata_js_call_app)&&Ot.sensorsdata_js_call_app(JSON.stringify(t)))}function Gs(t){return Ri&&Ri.call(At,JSON.stringify(t))}function si(t){return ur.call(At)&&Hi&&Hi.call(At,JSON.stringify(t))}function Qs(t,e){return e&&typeof e[t.callType]=="function"&&e[t.callType]()}function Ys(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return to(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function to(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function eo(t,e,r){return Ys(t,e,r),t.plugin_version=Gl,t}function ro(){if(kt("ObsoleteBridge---test---init---"),ft=window.SensorsData_APP_JS_Bridge,we=ft&&ft.sensorsdata_track,de=ft&&ft.sensorsdata_verify,He=ft&&ft.sensorsdata_visual_verify,kt("ObsoleteBridge-",Y.bridge.activeBridge,de,we,He),Y&&!Y.bridge.activeBridge&&(de||we||He)){Y.bridge.activeBridge=Ji;var t=de||we;if(He&&(t=!!He.call(ft,JSON.stringify({server_url:Y.para.server_url})),kt("ObsoleteBridge---called-return",t)),Y.bridge.bridge_info={touch_app_bridge:!0,platform:"android",verify_success:t?"success":"fail"},!Y.para.app_js_bridge)return void kt("app_js_bridge is not configured, data will not be sent by android obsolete bridge.");Y.registerInterceptor("sendDataStage",{send:{priority:80,entry:no}}),kt("Android obsolete bridge inits succeed.")}}function no(t,e){if(kt("ObsoleteBridge---senddata"),Y.para.app_js_bridge.is_mui||t.data.type==="item_set"||t.data.type==="item_delete")return t;var r=t.callback;if(de){var n=de&&de.call(ft,JSON.stringify(fe.extend({server_url:Y.para.server_url},t.data)));return kt("ObsoleteBridge---anVerify-success",n),n?(fe.isFunction(r)&&r(),e.cancellationToken.cancel(),t):Y.para.app_js_bridge.is_send?(Y.debug.apph5({data:t.data,step:"3.1",output:"all"}),t):(fe.isFunction(r)&&r(),e.cancellationToken.cancel(),t)}return kt("ObsoleteBridge---is-send-old-way",Y.para.app_js_bridge.is_send),we&&we.call(ft,JSON.stringify(fe.extend({server_url:Y.para.server_url},t.data))),fe.isFunction(r)&&r(),e.cancellationToken.cancel(),t}function io(t){kt("ObsoleteBridge---handleCommadn");var e=t.callType;return e in Gr.commands?(kt("ObsoleteBridge---",e,Gr.commands),Gr.commands[e](t,ft)):ft&&fe.isFunction(ft.sensorsdata_js_call_app)?(kt("ObsoleteBridge---handleCommadn-abridge"),ft.sensorsdata_js_call_app(JSON.stringify(t))):void 0}function ao(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return so(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function so(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function oo(t,e,r){return ao(t,e,r),t.plugin_version=Ql,t}function lo(t,e,r){if(t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return uo(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function uo(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function co(t,e,r){return lo(t,e,r),t.plugin_version=tu,t}function oi(){return Je!==void 0&&document[Je]}function po(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return fo(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function fo(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function go(t,e,r){return po(t,e,r),t.plugin_version=lu,t}function _o(){if(Qr=window.SensorsData_iOS_JS_Bridge&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url,ge=function(){return window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker},z&&!z.bridge.activeBridge&&ge()&&ge().postMessage){if(z.bridge.activeBridge=Ki,z.para.app_js_bridge&&!z.para.app_js_bridge.is_mui&&(z.bridge.is_verify_success=Qr&&z.bridge.validateAppUrl(Qr)),z.bridge.bridge_info={touch_app_bridge:!0,platform:"ios",verify_success:z.bridge.is_verify_success?"success":"fail",support_two_way_call:!0},!z.para.app_js_bridge)return void Yr("app_js_bridge is not configured, data will not be sent by iOS bridge.");z.registerInterceptor("sendDataStage",{send:{priority:70,entry:ho}}),Yr("IOS bridge inits succeed.")}}function ho(t,e){if(z.para.app_js_bridge.is_mui||t.data.type==="item_set"||t.data.type==="item_delete")return t;var r=t.callback;return z.bridge.is_verify_success?(ge()&&ge().postMessage(JSON.stringify({callType:"app_h5_track",data:Me.extend({server_url:z.para.server_url},t.data)})),Me.isFunction(r)&&r(),e.cancellationToken.cancel(),t):z.para.app_js_bridge.is_send?(z.debug.apph5({data:t.data,step:"4.1",output:"all"}),t):(Me.isFunction(r)&&r(),e.cancellationToken.cancel(),t)}function mo(t){var e=t.callType;return e!=="page_info"&&e!=="visualized_track"||z.bridge.hasVisualModeBridge()?e==="sensorsdata_get_app_visual_config"?Me.isObject(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge[e]:ge()&&ge().postMessage(JSON.stringify(t)):null}function vo(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return yo(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function yo(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function bo(t,e,r){return vo(t,e,r),t.plugin_version=uu,t}function wo(){if(tt&&!tt.bridge.activeBridge&&So()){if(tt.bridge.activeBridge=qi,tt.bridge.bridge_info={touch_app_bridge:!0,platform:"ios",verify_success:ko()?"success":"fail"},!tt.para.app_js_bridge)return void tn("app_js_bridge is not configured, data will not be sent by iOS obsolete bridge.");tt.registerInterceptor("sendDataStage",{send:{priority:90,entry:Po}}),tn("IOS obsolete bridge inits succeed.")}}function So(){return(/sensors-verify/.test(navigator.userAgent)||/sa-sdk-ios/.test(navigator.userAgent))&&!window.MSStream}function ko(){if(/sensors-verify/.test(navigator.userAgent)){var t=navigator.userAgent.match(/sensors-verify\/([^\s]+)/);if(t&&t[0]&&typeof t[1]=="string"&&t[1].split("?").length===2){t=t[1].split("?");var e=null,r=null;try{e=Se.URL(tt.para.server_url).hostname,r=Se.URL(tt.para.server_url).searchParams.get("project")||"default"}catch(n){tt.log(n)}return!(!e||e!==t[0]||!r||r!==t[1])}return!1}return!!/sa-sdk-ios/.test(navigator.userAgent)}function Po(t,e){function r(l){var u=JSON.stringify(Se.extend({server_url:tt.para.server_url},l));return u=u.replace(/\r\n/g,""),"sensorsanalytics://trackEvent?event="+(u=encodeURIComponent(u))}if(tt.para.app_js_bridge.is_mui||t.data.type==="item_set"||t.data.type==="item_delete")return t;var n=t.callback;if(tt.bridge.bridge_info.verify_success){var i=document.createElement("iframe"),a=r(t.data);return i.setAttribute("src",a),document.documentElement.appendChild(i),i.parentNode.removeChild(i),i=null,Se.isFunction(n)&&n(),e.cancellationToken.cancel(),!0}return tt.para.app_js_bridge.is_send?(tt.debug.apph5({data:t.data,step:"3.2",output:"all"}),t):(Se.isFunction(n)&&n(),e.cancellationToken.cancel(),t)}function Co(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return Oo(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function Oo(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function jo(t,e,r){return Co(t,e,r),t.plugin_version=cu,t}function X(){this.sd=null,this.start_time=+new Date,this.page_show_status=!0,this.page_hidden_status=!1,this._={},this.timer=null,this.current_page_url=document.referrer,this.url=location.href,this.title=document.title||"",this.option={},this.heartbeat_interval_time=5e3,this.heartbeat_interval_timer=null,this.page_id=null,this.storage_name="sawebjssdkpageleave",this.max_duration=du}function No(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return Io(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function Io(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function To(t,e,r){return No(t,e,r),t.plugin_version=gu,t}function $o(t,e){if(t.type!=="track")return t;var r=e.sd,n=r._,i=r.saEvent.check,a=n.extend2Lev({properties:{}},t),l=e.customRegister,u=a.properties,c=a.event,f={};return n.each(l,function(_){if(n.isObject(_))n.indexOf(_.events,c)>-1&&i({properties:_.properties})&&(f=n.extend(f,_.properties));else if(n.isFunction(_)){var T=_({event:c,properties:u,data:a});n.isObject(T)&&!n.isEmptyObject(T)&&i({properties:T})&&(f=n.extend(f,T))}}),t.properties=n.extend(u,f),t}function xe(){this.sd=null,this.log=window.console&&window.console.log||function(){},this.customRegister=[]}function Do(t,e,r){return t.plugin_version=mu,t}function xo(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return Ao(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function Ao(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function Eo(t,e,r){return xo(t,e,r),t.plugin_version=yu,t}function Lo(t){try{if(t.event!=="$pageview"&&(!t.type||t.type.slice(0,7)!=="profile")){var e=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight||0,r=document.documentElement.scrollHeight||0,n={$page_height:Math.max(e,r)||0};t.properties=ke._.extend(t.properties||{},n)}}catch{Fe("页面高度获取异常。")}return Xi.call(ke.kit,t)}function Uo(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return Ro(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function Ro(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function Ho(t,e,r){return Uo(t,e,r),t.plugin_version=Su,t}function Jo(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return Mo(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function Mo(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function Bo(t,e,r){return Jo(t,e,r),t.plugin_version=Ou,t}function Fo(t,e,r){if(t.plugin_name=e,t.init){var n=t.init;t.init=function(i,a){function l(){n.call(t,i,a)}return Ko(i,t,e),i.readyState&&i.readyState.state>=3||!i.on?l():void i.on(r,l)}}return t}function Ko(t,e,r){function n(i,a){t.logger?t.logger.msg.apply(t.logger,a).module(r+"").level(i).log():t.log&&t.log.apply(t,a)}e.log=function(){n("log",arguments)},e.warn=function(){n("warn",arguments)},e.error=function(){n("error",arguments)}}function Vo(t,e,r){return Fo(t,e,r),t.plugin_version=Nu,t}function qo(){rn=!0}function zo(){rn=!1}function Wo(){return rn}function Xo(t,e,r){return t.plugin_version=Tu,t}function Zo(t){var e=t,r="";r=B.para.debug_mode_url.indexOf("?")!==-1?B.para.debug_mode_url+"&"+B.kit.encodeTrackData(t):B.para.debug_mode_url+"?"+B.kit.encodeTrackData(t),_e.ajax({url:r,type:"GET",cors:!0,header:{"Dry-Run":String(B.para.debug_mode_upload)},success:function(n){_e.isEmptyObject(n)===!0?alert("debug数据发送成功"+e):alert("debug失败 错误原因"+JSON.stringify(n))}})}function Go(t,e){if(B.para.debug_mode===!0){var r=t.data;t.callback,Zo(JSON.stringify(r)),e.cancellationToken.stop()}return t}function Qo(){B.para.debug_mode===!0&&(B.para.debug_mode_upload=B.para.debug_mode_upload||!1,_e.isString(B.para.debug_mode_url)||(_e.isString(B.para.server_url)?B.para.debug_mode_url=B.para.server_url.replace("sa.gif","debug"):_e.isArray(B.para.server_url)&&_e.isString(B.para.server_url[0])?B.para.debug_mode_url=B.para.server_url[0].replace("sa.gif","debug"):B.para.debug_mode=!1))}function Yo(){B.on("sdkInitPara",function(){Qo()}),B.on("sdkAfterInitPara",function(){B.registerInterceptor("sendDataStage",{send:{priority:30,entry:Go}})})}function tl(t,e,r){return t.plugin_version=Du,t}function el(t,e){if(he.isObject(ut.para.jsapp)&&!ut.para.jsapp.isOnline&&typeof ut.para.jsapp.setData=="function"){var r=t;delete r.callback,r=JSON.stringify(r),ut.para.jsapp.setData(r),e.cancellationToken.stop()}return t}function rl(){ut.on("sdkAfterInitAPI",function(){he.isObject(ut.commonWays)&&(ut.commonWays.setOnlineState=nl),ut.registerInterceptor("sendDataStage",{send:{priority:40,entry:el}})})}function nl(t){if(t===!0&&he.isObject(ut.para.jsapp)&&typeof ut.para.jsapp.getData=="function"){ut.para.jsapp.isOnline=!0;var e=ut.para.jsapp.getData();he.isArray(e)&&e.length>0&&he.each(e,function(r){he.isJSONString(r)&&ut.kit.sendData(JSON.parse(r))})}else ut.para.jsapp.isOnline=!1}function il(t,e,r){return t.plugin_version=Au,t}function al(t,e){return!ct.para.app_js_bridge&&ct.para.batch_send&&Yt.localStorage.isSupport()&&localStorage.length<ct.para.batch_send.storage_length&&(pr.add(t.data),e.cancellationToken.stop()),t}function sl(){var t={datasend_timeout:6e3,send_interval:6e3,storage_length:200};Yt.localStorage.isSupport()&&Yt.isSupportCors()&&typeof localStorage=="object"?ct.para.batch_send===!0?ct.para.batch_send=Yt.extend({},t):typeof ct.para.batch_send=="object"&&(ct.para.batch_send=Yt.extend({},t,ct.para.batch_send)):ct.para.batch_send=!1}function ol(){ct.on("sdkInitPara",function(){sl()}),ct.on("sdkAfterInitPara",function(){!ct.para.app_js_bridge&&ct.para.batch_send&&Yt.localStorage.isSupport()&&(pr||(pr=new Yt.BatchSend),pr.batchInterval(),ct.registerInterceptor("sendDataStage",{send:{priority:100,entry:al}}))})}function ll(t,e,r){return t.plugin_version=Lu,t}function li(t){new te.BeaconSend(t).start()}function ul(t,e){var r=null,n=null;if(te.isObject(t.config)&&(r=t.config.send_type,n=te.optimizeServerUrl(t.config.server_url)),(r==="beacon"||!r&&Ft.para.send_type==="beacon")&&te.isSupportBeaconSend()){var i=n||t.server_url;t.server_url=i,t.data=Ft.kit.encodeTrackData(t.data),te.isArray(i)&&i.length?te.each(i,function(a){t.callback=null,t.server_url=a,li(t)}):typeof i=="string"&&i!==""?li(t):Ft.log("当前 server_url 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 server_url！"),e.cancellationToken.stop()}return t}function cl(){Ft.para.send_type!=="beacon"||te.isSupportBeaconSend()||(Ft.para.send_type="image")}function pl(){Ft.on("sdkInitPara",function(){cl()}),Ft.on("sdkAfterInitPara",function(){Ft.registerInterceptor("sendDataStage",{send:{priority:110,entry:ul}})})}function dl(t,e,r){return t.plugin_version=Ru,t}function ui(t){new ee.AjaxSend(t).start()}function fl(t,e){var r=null,n=null;if(ee.isObject(t.config)&&(r=t.config.send_type,n=ee.optimizeServerUrl(t.config.server_url)),(r==="ajax"||!r&&Kt.para.send_type==="ajax")&&ee.isSupportCors()){var i=n||t.server_url;t.server_url=i,t.data=Kt.kit.encodeTrackData(t.data),ee.isArray(i)&&i.length?ee.each(i,function(a){t.callback=null,t.server_url=a,ui(t)}):typeof i=="string"&&i!==""?ui(t):Kt.log("当前 server_url 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 server_url！"),e.cancellationToken.stop()}return t}function gl(){Kt.para.send_type!=="ajax"||ee.isSupportCors()||(Kt.para.send_type="image")}function _l(){Kt.on("sdkInitPara",function(){gl()}),Kt.on("sdkAfterInitPara",function(){Kt.registerInterceptor("sendDataStage",{send:{priority:120,entry:fl}})})}function hl(t,e,r){return t.plugin_version=Ju,t}function ci(t,e){var r=jt.kit.encodeTrackData(e);return t.indexOf("?")!==-1?t+"&"+r:t+"?"+r}function pi(t){new Pe.ImageSend(t).start()}function ml(t,e){var r=null;Pe.isObject(t.config)&&(r=Pe.optimizeServerUrl(t.config.server_url));var n=r||t.server_url,i=t.data;t.server_url=n,Pe.isArray(n)&&n.length?Pe.each(n,function(a){a&&(t.data=ci(a,i),t.callback=null,t.server_url=a,pi(t))}):typeof n=="string"&&n!==""?(t.data=ci(n,i),pi(t)):jt.logger&&jt.logger.msg("当前 server_url 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 server_url！").level("warn").log(),e.cancellationToken.stop()}function vl(){jt.para.send_type!=="image"&&jt.para.send_type!=="ajax"&&jt.para.send_type!=="beacon"&&(jt.para.send_type="image")}function yl(){jt.on("sdkInitPara",function(){vl()}),jt.on("sdkAfterInitPara",function(){jt.registerInterceptor("sendDataStage",{send:{priority:130,entry:ml}})})}function bl(t,e,r){return t.plugin_name=e,t}function wl(t,e,r){return bl(t,e,r),t.plugin_version=rr,t}function Sl(t){return Ke===null?void di(t):void Ke.push(t)}function di(t){try{if(t.level==="log"&&fi()||t.level==="warn"&&kl()||t.level==="error"&&Pl())return void Ur(t)}catch{}}function fi(){return!!Rr()||at.para.show_log===!0||ht.isObject(at.para.show_log)&&at.para.show_log.level==="log"}function kl(){return!!Rr()||fi()||ht.isObject(at.para.show_log)&&at.para.show_log.level==="warn"}function Pl(){return!!Rr()||!ht.isObject(at.para.show_log)||at.para.show_log.level!=="none"}function Ur(t){var e=t.content,r=ht.isObject(e[0])?ht.formatJsonString(e[0]):e[0],n=Cl(t);e[0]=n+(n.length>0?": ":"")+r;try{console&&(ht.isFunction(console[t.level])?console[t.level].apply(console,e):ht.isObject(console[t.level])&&console[t.level](e[0]))}catch{}}function Cl(t){var e="",r="",n=at.para.show_log;return ht.isObject(n)&&n.show_brand===!1||(e+=t.brand),ht.isObject(n)&&n.show_level===!1||(e+=(e.length>0?"-":"")+t.level),e.length>0&&(e="["+e+"]"),ht.isObject(n)&&n.show_module===!1||(r=t.module),e+r}function Ol(){ht.sessionStorage.isSupport()&&sessionStorage.setItem(an,"true")}function jl(){ht.sessionStorage.isSupport()&&sessionStorage.removeItem(an)}function Rr(){return ht.sessionStorage.isSupport()&&sessionStorage.getItem(an)==="true"}var s={};(function(){function t(u,c){function f(b,k){try{b()}catch{k&&k()}}function _(b){if(_[b]!=null)return _[b];var k;if(b=="bug-string-char-index")k=!1;else if(b=="json")k=_("json-stringify")&&_("date-serialization")&&_("json-parse");else if(b=="date-serialization"){if(k=_("json-stringify")&&Lt){var g=c.stringify;f(function(){k=g(new pt(-864e13))=='"-271821-04-20T00:00:00.000Z"'&&g(new pt(864e13))=='"+275760-09-13T00:00:00.000Z"'&&g(new pt(-621987552e5))=='"-000001-01-01T00:00:00.000Z"'&&g(new pt(-1))=='"1969-12-31T23:59:59.999Z"'})}}else{var C,v='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if(b=="json-stringify"){var P=typeof(g=c.stringify)=="function";P&&((C=function(){return 1}).toJSON=C,f(function(){P=g(0)==="0"&&g(new I)==="0"&&g(new F)=='""'&&g(Pt)===Nt&&g(Nt)===Nt&&g()===Nt&&g(C)==="1"&&g([C])=="[1]"&&g([Nt])=="[null]"&&g(null)=="null"&&g([Nt,Pt,null])=="[null,null,null]"&&g({a:[C,!0,!1,null,`\0\b
\f\r	`]})==v&&g(null,C)==="1"&&g([1,2],null,1)==`[
 1,
 2
]`},function(){P=!1})),k=P}if(b=="json-parse"){var N,w=c.parse;typeof w=="function"&&f(function(){w("0")!==0||w(!1)||(C=w(v),(N=C.a.length==5&&C.a[0]===1)&&(f(function(){N=!w('"	"')}),N&&f(function(){N=w("01")!==1}),N&&f(function(){N=w("1.")!==1})))},function(){N=!1}),k=N}}return _[b]=!!k}function T(b){return ln(this)}u||(u=r.Object()),c||(c=r.Object());var I=u.Number||r.Number,F=u.String||r.String,Et=u.Object||r.Object,pt=u.Date||r.Date,Fu=u.SyntaxError||r.SyntaxError,Ku=u.TypeError||r.TypeError,Vu=u.Math||r.Math,dr=u.JSON||r.JSON;if(typeof dr=="object"&&dr)return c.stringify=dr.stringify,c.parse=dr.parse,c.runInContext=t,c;var Nt,la=Et.prototype,Pt=la.toString,fr=la.hasOwnProperty,Lt=new pt(-0xc782b5b800cec);if(f(function(){Lt=Lt.getUTCFullYear()==-109252&&Lt.getUTCMonth()===0&&Lt.getUTCDate()===1&&Lt.getUTCHours()==10&&Lt.getUTCMinutes()==37&&Lt.getUTCSeconds()==6&&Lt.getUTCMilliseconds()==708}),_["bug-string-char-index"]=_["date-serialization"]=_.json=_["json-stringify"]=_["json-parse"]=null,!_("json")){var gr="[object Function]",qu="[object Date]",ua="[object Number]",ca="[object String]",sn="[object Array]",zu="[object Boolean]",on=_("bug-string-char-index"),Oe=function(b,k){var g,C,v,P=0;for(v in(g=function(){this.valueOf=0}).prototype.valueOf=0,C=new g)fr.call(C,v)&&P++;return g=C=null,P?Oe=function(N,w){var R,lt,M=Pt.call(N)==gr;for(R in N)M&&R=="prototype"||!fr.call(N,R)||(lt=R==="constructor")||w(R);(lt||fr.call(N,R="constructor"))&&w(R)}:(C=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],Oe=function(N,w){var R,lt,M=Pt.call(N)==gr,ne=!M&&typeof N.constructor!="function"&&e[typeof N.hasOwnProperty]&&N.hasOwnProperty||fr;for(R in N)M&&R=="prototype"||!ne.call(N,R)||w(R);for(lt=C.length;R=C[--lt];)ne.call(N,R)&&w(R)}),Oe(b,k)};if(!_("json-stringify")&&!_("date-serialization")){var Wu={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},Xu="000000",Vt=function(b,k){return(Xu+(k||0)).slice(-b)},ln=function(b){var k,g,C,v,P,N,w,R,lt;if(Lt)k=function(U){g=U.getUTCFullYear(),C=U.getUTCMonth(),v=U.getUTCDate(),N=U.getUTCHours(),w=U.getUTCMinutes(),R=U.getUTCSeconds(),lt=U.getUTCMilliseconds()};else{var M=Vu.floor,ne=[0,31,59,90,120,151,181,212,243,273,304,334],qt=function(U,zt){return ne[zt]+365*(U-1970)+M((U-1969+(zt=+(zt>1)))/4)-M((U-1901+zt)/100)+M((U-1601+zt)/400)};k=function(U){for(v=M(U/864e5),g=M(v/365.2425)+1970-1;qt(g+1,0)<=v;g++);for(C=M((v-qt(g,0))/30.42);qt(g,C+1)<=v;C++);v=1+v-qt(g,C),N=M((P=(U%864e5+864e5)%864e5)/36e5)%24,w=M(P/6e4)%60,R=M(P/1e3)%60,lt=P%1e3}}return(ln=function(U){return U>-1/0&&U<1/0?(k(U),U=(g<=0||g>=1e4?(g<0?"-":"+")+Vt(6,g<0?-g:g):Vt(4,g))+"-"+Vt(2,C+1)+"-"+Vt(2,v)+"T"+Vt(2,N)+":"+Vt(2,w)+":"+Vt(2,R)+"."+Vt(3,lt)+"Z",g=C=v=N=w=R=lt=null):U=null,U})(b)};if(_("json-stringify")&&!_("date-serialization")){var Zu=c.stringify;c.stringify=function(b,k,g){var C=pt.prototype.toJSON;pt.prototype.toJSON=T;var v=Zu(b,k,g);return pt.prototype.toJSON=C,v}}else{var Gu="\\u00",Qu=function(b){var k=b.charCodeAt(0),g=Wu[k];return g||Gu+Vt(2,k.toString(16))},un=/[\x00-\x1f\x22\x5c]/g,pa=function(b){return un.lastIndex=0,'"'+(un.test(b)?b.replace(un,Qu):b)+'"'},cn=function(b,k,g,C,v,P,N){var w,R,lt,M,ne,qt,U,zt,dn;if(f(function(){w=k[b]}),typeof w=="object"&&w&&(w.getUTCFullYear&&Pt.call(w)==qu&&w.toJSON===pt.prototype.toJSON?w=ln(w):typeof w.toJSON=="function"&&(w=w.toJSON(b))),g&&(w=g.call(k,b,w)),w==Nt)return w===Nt?w:"null";switch((R=typeof w)=="object"&&(lt=Pt.call(w)),lt||R){case"boolean":case zu:return""+w;case"number":case ua:return w>-1/0&&w<1/0?""+w:"null";case"string":case ca:return pa(""+w)}if(typeof w=="object"){for(U=N.length;U--;)if(N[U]===w)throw Ku();if(N.push(w),M=[],zt=P,P+=v,lt==sn){for(qt=0,U=w.length;qt<U;qt++)ne=cn(qt,w,g,C,v,P,N),M.push(ne===Nt?"null":ne);dn=M.length?v?`[
`+P+M.join(`,
`+P)+`
`+zt+"]":"["+M.join(",")+"]":"[]"}else Oe(C||w,function(ga){var _a=cn(ga,w,g,C,v,P,N);_a!==Nt&&M.push(pa(ga)+":"+(v?" ":"")+_a)}),dn=M.length?v?`{
`+P+M.join(`,
`+P)+`
`+zt+"}":"{"+M.join(",")+"}":"{}";return N.pop(),dn}};c.stringify=function(b,k,g){var C,v,P,N;if(e[typeof k]&&k){if((N=Pt.call(k))==gr)v=k;else if(N==sn){P={};for(var w,R=0,lt=k.length;R<lt;)w=k[R++],(N=Pt.call(w))!="[object String]"&&N!="[object Number]"||(P[w]=1)}}if(g)if((N=Pt.call(g))==ua){if((g-=g%1)>0)for(g>10&&(g=10),C="";C.length<g;)C+=" "}else N==ca&&(C=g.length<=10?g:g.slice(0,10));return cn("",((w={})[""]=b,w),v,P,C,"",[])}}}if(!_("json-parse")){var O,_r,Yu=F.fromCharCode,tc={92:"\\",34:'"',47:"/",98:"\b",116:"	",110:`
`,102:"\f",114:"\r"},et=function(){throw O=_r=null,Fu()},re=function(){for(var b,k,g,C,v,P=_r,N=P.length;O<N;)switch(v=P.charCodeAt(O)){case 9:case 10:case 13:case 32:O++;break;case 123:case 125:case 91:case 93:case 58:case 44:return b=on?P.charAt(O):P[O],O++,b;case 34:for(b="@",O++;O<N;)if((v=P.charCodeAt(O))<32)et();else if(v==92)switch(v=P.charCodeAt(++O)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:b+=tc[v],O++;break;case 117:for(k=++O,g=O+4;O<g;O++)(v=P.charCodeAt(O))>=48&&v<=57||v>=97&&v<=102||v>=65&&v<=70||et();b+=Yu("0x"+P.slice(k,O));break;default:et()}else{if(v==34)break;for(v=P.charCodeAt(O),k=O;v>=32&&v!=92&&v!=34;)v=P.charCodeAt(++O);b+=P.slice(k,O)}if(P.charCodeAt(O)==34)return O++,b;et();default:if(k=O,v==45&&(C=!0,v=P.charCodeAt(++O)),v>=48&&v<=57){for(v==48&&(v=P.charCodeAt(O+1))>=48&&v<=57&&et(),C=!1;O<N&&(v=P.charCodeAt(O))>=48&&v<=57;O++);if(P.charCodeAt(O)==46){for(g=++O;g<N&&!((v=P.charCodeAt(g))<48||v>57);g++);g==O&&et(),O=g}if((v=P.charCodeAt(O))==101||v==69){for((v=P.charCodeAt(++O))!=43&&v!=45||O++,g=O;g<N&&!((v=P.charCodeAt(g))<48||v>57);g++);g==O&&et(),O=g}return+P.slice(k,O)}C&&et();var w=P.slice(O,O+4);if(w=="true")return O+=4,!0;if(w=="fals"&&P.charCodeAt(O+4)==101)return O+=5,!1;if(w=="null")return O+=4,null;et()}return"$"},pn=function(b){var k,g;if(b=="$"&&et(),typeof b=="string"){if((on?b.charAt(0):b[0])=="@")return b.slice(1);if(b=="["){for(k=[];(b=re())!="]";)g?b==","?(b=re())=="]"&&et():et():g=!0,b==","&&et(),k.push(pn(b));return k}if(b=="{"){for(k={};(b=re())!="}";)g?b==","?(b=re())=="}"&&et():et():g=!0,b!=","&&typeof b=="string"&&(on?b.charAt(0):b[0])=="@"&&re()==":"||et(),k[b.slice(1)]=pn(re());return k}et()}return b},da=function(b,k,g){var C=fa(b,k,g);C===Nt?delete b[k]:b[k]=C},fa=function(b,k,g){var C,v=b[k];if(typeof v=="object"&&v)if(Pt.call(v)==sn)for(C=v.length;C--;)da(Pt,Oe,v,C,g);else Oe(v,function(P){da(v,P,g)});return g.call(b,k,v)};c.parse=function(b,k){var g,C;return O=0,_r=""+b,g=pn(re()),re()!="$"&&et(),O=_r=null,k&&Pt.call(k)==gr?fa(((C={})[""]=g,C),"",k):g}}}return c.runInContext=t,c}var e={function:!0,object:!0},r=e[typeof window]&&window||this,n=r.JSON,i=r.JSON3,a=!1,l=t(r,r.JSON3={noConflict:function(){return a||(a=!0,r.JSON=n,r.JSON3=i,n=i=null),l}});r.JSON?(r.JSON.parse=l.parse,r.JSON.stringify=l.stringify):r.JSON={parse:l.parse,stringify:l.stringify}}).call(window),function(t,e){e(window)}(0,function(t){if(t.atob)try{t.atob(" ")}catch{t.atob=function(i){var a=function(l){return i(String(l).replace(/[\t\n\f\r ]+/g,""))};return a.original=i,a}(t.atob)}else{var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;t.btoa=function(n){for(var i,a,l,u,c="",f=0,_=(n=String(n)).length%3;f<n.length;){if((a=n.charCodeAt(f++))>255||(l=n.charCodeAt(f++))>255||(u=n.charCodeAt(f++))>255)return"";i=a<<16|l<<8|u,c+=e.charAt(i>>18&63)+e.charAt(i>>12&63)+e.charAt(i>>6&63)+e.charAt(63&i)}return _?c.slice(0,_-3)+"===".substring(_):c},t.atob=function(n){if(n=String(n).replace(/[\t\n\f\r ]+/g,""),!r.test(n))return"";n+="==".slice(2-(3&n.length));for(var i,a,l,u="",c=0;c<n.length;)i=e.indexOf(n.charAt(c++))<<18|e.indexOf(n.charAt(c++))<<12|(a=e.indexOf(n.charAt(c++)))<<6|(l=e.indexOf(n.charAt(c++))),u+=a===64?String.fromCharCode(i>>16&255):l===64?String.fromCharCode(i>>16&255,i>>8&255):String.fromCharCode(i>>16&255,i>>8&255,255&i);return u}}});var gi,Q={setup:function(t){gi=t},log:function(){(gi||console&&console.log||function(){}).apply(null,arguments)}},E={get:function(t){return window.localStorage.getItem(t)},parse:function(t){var e;try{e=JSON.parse(E.get(t))||null}catch(r){Q.log(r)}return e},set:function(t,e){try{window.localStorage.setItem(t,e)}catch(r){Q.log(r)}},remove:function(t){window.localStorage.removeItem(t)},isSupport:function(){var t=!0;try{var e="__local_store_support__",r="testIsSupportStorage";E.set(e,r),E.get(e)!==r&&(t=!1),E.remove(e)}catch{t=!1}return t}},_i=function(){function t(){return(e=(9301*e+49297)%233280)/233280}var e=new Date().getTime();return function(r){return Math.ceil(t()*r)}}();bt.prototype.get=function(t,e,r,n){if(!t)throw new Error("key is must");e=e||1e4,r=r||1e3,n=n||function(){};var i=this.lockGetPrefix+t,a=E.get(i),l=String(Z());return a&&(a=W(a)||{randomNum:0,expireTime:0}).expireTime>V()?n(null):(E.set(i,JSON.stringify({randomNum:l,expireTime:V()+e})),void setTimeout(function(){(a=W(E.get(i))||{randomNum:0,expireTime:0})&&a.randomNum===l?(n(E.get(t)),E.remove(t),E.remove(i)):n(null)},r))},bt.prototype.set=function(t,e,r,n,i){if(!t||!e)throw new Error("key and val is must");r=r||1e4,n=n||1e3,i=i||function(){};var a=this.lockSetPrefix+t,l=E.get(a),u=String(Z());return l&&(l=W(l)||{randomNum:0,expireTime:0}).expireTime>V()?i({status:"fail",reason:"This key is locked"}):(E.set(a,JSON.stringify({randomNum:u,expireTime:V()+r})),void setTimeout(function(){(l=W(E.get(a))||{randomNum:0,expireTime:0}).randomNum===u?E.set(t,e)&&i({status:"success"}):i({status:"fail",reason:"This key is locked"})},n))},wt.prototype.on=function(t,e){if(!t||!e)return!1;if(!hr(e))throw new Error("listener must be a function");this._events[t]=this._events[t]||[];var r=typeof e=="object";return this._events[t].push(r?e:{listener:e,once:!1}),this},wt.prototype.prepend=function(t,e){if(!t||!e)return!1;if(!hr(e))throw new Error("listener must be a function");this._events[t]=this._events[t]||[];var r=typeof e=="object";return this._events[t].unshift(r?e:{listener:e,once:!1}),this},wt.prototype.prependOnce=function(t,e){return this.prepend(t,{listener:e,once:!0})},wt.prototype.once=function(t,e){return this.on(t,{listener:e,once:!0})},wt.prototype.off=function(t,e){var r=this._events[t];if(!r)return!1;if(typeof e=="number")r.splice(e,1);else if(typeof e=="function")for(var n=0,i=r.length;n<i;n++)r[n]&&r[n].listener===e&&r.splice(n,1);return this},wt.prototype.emit=function(t,e){var r=this._events[t];if(!r)return!1;for(var n=0;n<r.length;n++){var i=r[n];i&&(i.listener.call(this,e||{}),i.once&&this.off(t,n))}return this},wt.prototype.removeAllListeners=function(t){t&&this._events[t]?this._events[t]=[]:this._events={}},wt.prototype.listeners=function(t){return t&&typeof t=="string"?this._events[t]:this._events};var Xt=function(){var t=function(){for(var n=1*new Date,i=0;n==1*new Date;)i++;return n.toString(16)+i.toString(16)},e=function(){return Z().toString(16).replace(".","")},r=function(){function n(f,_){var T,I=0;for(T=0;T<_.length;T++)I|=u[T]<<8*T;return f^I}var i,a,l=navigator.userAgent,u=[],c=0;for(i=0;i<l.length;i++)a=l.charCodeAt(i),u.unshift(255&a),u.length>=4&&(c=n(c,u),u=[]);return u.length>0&&(c=n(c,u)),c.toString(16)};return function(){var n=String(screen.height*screen.width);n=n&&/\d{5,}/.test(n)?n.toString(16):String(31242*Z()).replace(".","").slice(0,8);var i=t()+"-"+e()+"-"+r()+"-"+n+"-"+t();return i||(String(Z())+String(Z())+String(Z())).slice(2,15)}}(),hi=function(t){this.ele=t},mi=function(t,e){for(var r=[];t;t=t.nextSibling)t.nodeType===1&&t!==e&&r.push(t);return r};hi.prototype={addClass:function(t){return(" "+this.ele.className+" ").indexOf(" "+t+" ")===-1&&(this.ele.className=this.ele.className+(this.ele.className===""?"":" ")+t),this},removeClass:function(t){var e=" "+this.ele.className+" ";return e.indexOf(" "+t+" ")!==-1&&(this.ele.className=e.replace(" "+t+" "," ").slice(1,-1)),this},hasClass:function(t){return(" "+this.ele.className+" ").indexOf(" "+t+" ")!==-1},attr:function(t,e){return typeof t=="string"&&It(e)?this.ele.getAttribute(t):(typeof t=="string"&&(e=String(e),this.ele.setAttribute(t,e)),this)},offset:function(){var t=this.ele.getBoundingClientRect();if(t.width||t.height){var e=this.ele.ownerDocument.documentElement;return{top:t.top+window.pageYOffset-e.clientTop,left:t.left+window.pageXOffset-e.clientLeft}}return{top:0,left:0}},getSize:function(){if(!window.getComputedStyle)return{width:this.ele.offsetWidth,height:this.ele.offsetHeight};try{var t=this.ele.getBoundingClientRect();return{width:t.width,height:t.height}}catch{return{width:0,height:0}}},getStyle:function(t){return this.ele.currentStyle?this.ele.currentStyle[t]:this.ele.ownerDocument.defaultView.getComputedStyle(this.ele,null).getPropertyValue(t)},wrap:function(t){var e=document.createElement(t);return this.ele.parentNode.insertBefore(e,this.ele),e.appendChild(this.ele),dt(e)},getCssStyle:function(t){var e=this.ele.style.getPropertyValue(t);if(e)return e;var r=null;if(typeof window.getMatchedCSSRules=="function"&&(r=window.getMatchedCSSRules(this.ele)),!r||!A(r))return null;for(var n=r.length-1;n>=0;n--)if(e=r[n].style.getPropertyValue(t))return e},sibling:function(t,e){for(;(t=t[e])&&t.nodeType!==1;);return t},next:function(){return this.sibling(this.ele,"nextSibling")},prev:function(){return this.sibling(this.ele,"previousSibling")},siblings:function(){return mi((this.ele.parentNode||{}).firstChild,this.ele)},children:function(){return mi(this.ele.firstChild)},parent:function(){var t=this.ele.parentNode;return dt(t=t&&t.nodeType!==11?t:null)},previousElementSibling:function(){var t=this.ele;if("previousElementSibling"in document.documentElement)return dt(t.previousElementSibling);for(;t=t.previousSibling;)if(t.nodeType===1)return dt(t);return dt(null)},getSameTypeSiblings:function(){for(var t=this.ele,e=t.parentNode,r=t.tagName.toLowerCase(),n=[],i=0;i<e.children.length;i++){var a=e.children[i];a.nodeType===1&&a.tagName.toLowerCase()===r&&n.push(e.children[i])}return n},getParents:function(){try{var t=this.ele;if(!rt(t))return[];var e=[t];if(t===null||t.parentElement===null)return[];for(;t.parentElement!==null;)t=t.parentElement,e.push(t);return e}catch{return[]}}};var ue={get:function(t){for(var e=t+"=",r=document.cookie.split(";"),n=0;n<r.length;n++){for(var i=r[n];i.charAt(0)==" ";)i=i.substring(1,i.length);if(i.indexOf(e)==0)return Ht(i.substring(e.length,i.length))}return null},set:function(t,e,r,n,i,a){function l(pt){return!!pt&&pt.replace(/\r\n/g,"")}var u=a,c="",f="",_="";if((r=r??73e3)!==0){var T=new Date;String(r).slice(-1)==="s"?T.setTime(T.getTime()+1e3*Number(String(r).slice(0,-1))):T.setTime(T.getTime()+24*r*60*60*1e3),c="; expires="+T.toGMTString()}n&&n!==""&&(_="; SameSite="+n),i&&(f="; secure");var I="",F="",Et="";t&&(I=l(t)),e&&(F=l(e)),u&&(Et=l(u)),I&&F&&(document.cookie=I+"="+encodeURIComponent(F)+c+"; path=/"+Et+_+f)},remove:function(t,e){this.set(t,"1",-1,e)},isSupport:function(t,e,r,n,i){function a(){return l.set(t,e,r,n,i),l.get(t)===e&&(l.remove(t),!0)}t=t||"cookie_support_test",e=e||"1";var l=this;return navigator.cookieEnabled&&a()}},Nl=Object.prototype.hasOwnProperty,be={isSupport:function(){var t=!0,e="__session_storage_support__",r="testIsSupportStorage";try{sessionStorage&&sessionStorage.setItem?(sessionStorage.setItem(e,r),sessionStorage.removeItem(e,r),t=!0):t=!1}catch{t=!1}return t}},Il={"+":"-","/":"_","=":"."},Tl={"-":"+",_:"/",".":"="},$l={encode:function(t){return t.replace(/[+\/=]/g,function(e){return Il[e]})},decode:function(t){return t.replace(/[-_.]/g,function(e){return Tl[e]})},trim:function(t){return t.replace(/[.=]{1,2}$/,"")},isBase64:function(t){return/^[A-Za-z0-9+\/]*[=]{0,2}$/.test(t)},isUrlSafeBase64:function(t){return/^[A-Za-z0-9_-]*[.]{0,2}$/.test(t)}},Dl={__proto__:null,noPrototypePollution:Nr,ConcurrentStorage:bt,EventEmitter:wt,URL:Jt,UUID:Xt,addEvent:ae,addHashEvent:fn,ajax:_n,base64Decode:ve,base64Encode:je,bindReady:ba,cookie:ue,coverExtend:vr,decodeURI:ye,decodeURIComponent:Ht,dfmapping:wa,each:$,encodeDates:mn,extend:x,extend2Lev:vn,filter:yn,formatDate:yr,formatJsonString:Sa,getCookieTopLevelDomain:br,getDomBySelector:qe,getElementContent:bn,getHostname:Tt,getIOSVersion:wr,getQueryParam:Mt,getQueryParamsFromUrl:Ie,getRandom:Z,getRandomBasic:_i,getScreenOrientation:Pa,getUA:Sr,getURL:st,getURLPath:se,getURLSearchParams:Ve,hasAttribute:kr,hasAttributes:Pr,hashCode:Sn,hashCode53:Cr,indexOf:nt,inherit:Ca,isArguments:kn,isArray:A,isBoolean:Pn,isDate:Ne,isElement:rt,isEmptyObject:it,isFunction:S,isHttpUrl:Cn,isIOS:On,isJSONString:ze,isNumber:$t,isObject:d,isString:h,isSupportBeaconSend:Oa,isSupportCors:ja,isUndefined:It,jsonp:jn,listenPageState:Nn,loadScript:We,localStorage:E,logger:Q,map:hn,mediaQueriesSupported:wn,now:V,removeScriptProtocol:Na,rot13defs:Tn,rot13obfs:In,ry:dt,safeJSONParse:W,searchObjDate:Or,sessionStorage:be,setCssStyle:Xe,strToUnicode:Ia,throttle:Ta,toArray:$a,trim:mt,unique:Ze,urlParse:mr,urlSafeBase64:$l,values:$n,xhr:gn,startsWith:jr},tr=[],ce={appendWriter:function(t){tr.push(t)},msg:function(){var t={module:"",level:"log",brand:"web-sdk",content:null};return t.content=Array.prototype.slice.call(arguments),{module:function(e){return h(e)&&(t.module=e),this},level:function(e){return h(e)&&(t.level=e),this},brand:function(e){return h(e)&&(t.brand=e),this},log:function(){if(t.content&&t.content.length){for(var e=0;e<tr.length;e++)if(typeof tr[e]=="function")try{tr[e].call(null,t)}catch{}}return this}}}},y={},er={preset_properties:{search_keyword_baidu:!1,latest_utm:!0,latest_traffic_source_type:!0,latest_search_keyword:!0,latest_referrer:!0,latest_referrer_host:!1,latest_landing_page:!1,latest_wx_ad_click_id:void 0,url:!0,title:!0},encrypt_cookie:!1,enc_cookie:!1,img_use_crossorigin:!1,name:"sa",max_referrer_string_length:200,max_string_length:500,max_id_length:255,max_key_length:100,cross_subdomain:!0,show_log:!1,is_debug:!1,source_channel:[],sdk_id:"",vtrack_ignore:{},auto_init:!0,is_track_single_page:!1,is_single_page:!1,batch_send:!1,source_type:{},callback_timeout:200,datasend_timeout:8e3,is_track_device_id:!1,ignore_oom:!0,app_js_bridge:!1,white_list:{}};er.white_list[location.host]=Jt(location.href).hostname;var vi="utm_source utm_medium utm_campaign utm_content utm_term",rr="1.26.16",yi="sensorsdata_domain_test",Ae={EMAIL:"$identity_email",MOBILE:"$identity_mobile",LOGIN:"$identity_login_id"},vt={get:function(t){return ue.get(t)},set:function(t,e,r,n){var i="";if(n=It(n)?y.cross_subdomain:n){var a=Ir(location.href);a==="url解析失败"&&(a=""),i=a?"; domain="+a:""}return ue.set(t,e,r,y.set_cookie_samesite,y.is_secure_cookie,i)},remove:function(t,e){return e=It(e)?y.cross_subdomain:e,ue.remove(t,e)},isSupport:function(t,e){return t=t||"sajssdk_2015_cookie_access_test",e=e||"1",ue.isSupport(t,e,0,null,y.is_secure_cookie)}};vt.getNewUser=Ge;var nr={data:{},get:function(t){var e=this.data[t];return e===void 0?null:e._expirationTimestamp_!==void 0?new Date().getTime()>e._expirationTimestamp_?null:e.value:e},set:function(t,e,r){if(r){var n=new Date;e={value:e,_expirationTimestamp_:String(r).slice(-1)==="s"?n.getTime()+1e3*Number(String(r).slice(0,-1)):n.getTime()+24*r*60*60*1e3}}this.data[t]=e},getNewUserFlagMemoryKey:function(t){return"sajssdk_2015_"+y.sdk_id+t}},Ee={checkIsAddSign:function(t){t.type==="track"&&(Ge()?t.properties.$is_first_day=!0:t.properties.$is_first_day=!1)},is_first_visit_time:!1,is_page_first_visited:!1,checkIsFirstTime:function(t){t.type==="track"&&t.event==="$pageview"&&(this.is_first_visit_time?(t.properties.$is_first_time=!0,this.is_first_visit_time=!1):t.properties.$is_first_time=!1)},setDeviceId:function(){},storeInitCheck:function(){if(s.is_first_visitor){var t=new Date,e={h:23-t.getHours(),m:59-t.getMinutes(),s:59-t.getSeconds()};vt.isSupport()?vt.set(Dn("new_user"),"1",3600*e.h+60*e.m+e.s+"s"):nr.set(nr.getNewUserFlagMemoryKey("new_user"),"1",3600*e.h+60*e.m+e.s+"s"),this.is_first_visit_time=!0,this.is_page_first_visited=!0}else Ge()||(this.checkIsAddSign=function(r){r.type==="track"&&(r.properties.$is_first_day=!1)}),this.checkIsFirstTime=function(r){r.type==="track"&&r.event==="$pageview"&&(r.properties.$is_first_time=!1)}}},Hr=function(){this._events=[],this.pendingEvents=[]};Hr.prototype={emit:function(t){var e=[].slice.call(arguments,1);$(this._events,function(r){r.type===t&&r.callback.apply(r.context,e)}),this.pendingEvents.push({type:t,data:e}),this.pendingEvents.length>20&&this.pendingEvents.shift()},on:function(t,e,r,n){S(e)&&(this._events.push({type:t,callback:e,context:r||this}),n=n!==!1,this.pendingEvents.length>0&&n&&$(this.pendingEvents,function(i){i.type===t&&e.apply(r,i.data)}))},tempAdd:function(t,e){if(e&&t)return this.emit(t,e)},isReady:function(){}};var Le={data:{},id:function(){return this.data.id||(this.data.id=An()),this.data.id},type:function(){return this.data.type||(this.data.type=xn()),this.data.type}},Zt={distinct_id:function(){},jssdkDebug:function(){},_sendDebug:function(t){},apph5:function(t){var e="app_h5打通失败-",r={1:e+"use_app_track为false",2:e+"Android或者iOS，没有暴露相应方法",3.1:e+"Android校验server_url失败",3.2:e+"iOS校验server_url失败",4.1:e+"H5 校验 iOS server_url 失败",4.2:e+"H5 校验 Android server_url 失败"},n=t.output,i=t.step,a=t.data||"";n!=="all"&&n!=="console"||Dt(r[i]),(n==="all"||n==="code")&&d(y.is_debug)&&y.is_debug.apph5&&(a.type&&a.type.slice(0,7)==="profile"||(a.properties._jssdk_debug_info="apph5-"+String(i)))},defineMode:function(t){var e={1:{title:"当前页面无法进行可视化全埋点",message:"App SDK 与 Web JS SDK 没有进行打通，请联系贵方技术人员修正 App SDK 的配置，详细信息请查看文档。",link_text:"",link_url:""},2:{title:"当前页面无法进行可视化全埋点",message:"App SDK 与 Web JS SDK 没有进行打通，请联系贵方技术人员修正 Web JS SDK 的配置，详细信息请查看文档。",link_text:"",link_url:""},3:{title:"当前页面无法进行可视化全埋点",message:"Web JS SDK 没有开启全埋点配置，请联系贵方工作人员修正 SDK 的配置，详细信息请查看文档。",link_text:"",link_url:""},4:{title:"当前页面无法进行可视化全埋点",message:"Web JS SDK 配置的数据校验地址与 App SDK 配置的数据校验地址不一致，请联系贵方工作人员修正 SDK 的配置，详细信息请查看文档。",link_text:"",link_url:""}};return!(!t||!e[t])&&e[t]},protocol:{protocolIsSame:function(t,e){try{if(Jt(t).protocol!==Jt(e).protocol)return!1}catch{return j("不支持 _.URL 方法"),!1}return!0},serverUrl:function(){h(y.server_url)&&y.server_url!==""&&!this.protocolIsSame(y.server_url,location.href)&&j(`SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。
因为：1、https 下面发送 http 的图片请求会失败。2、http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。`)},ajax:function(t){return t!==y.server_url&&void(h(t)&&t!==""&&!this.protocolIsSame(t,location.href)&&j("SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。因为 http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。"))}}},L={initPage:function(){var t=Wt(),e=st(),r=Ir(e);r||Zt.jssdkDebug("url_domain异常_"+e+"_"+r),this.pageProp={referrer:t,referrer_host:t?Tt(t):"",url:e,url_host:Tt(e,"url_host取值异常"),url_domain:r}},pageProp:{},campaignParams:function(){return s.kit.getUtmData()},campaignParamsStandard:function(t,e){t=t||"",e=e||"";var r=L.campaignParams(),n={},i={};return $(r,function(a,l,u){(" "+vi+" ").indexOf(" "+l+" ")!==-1?n[t+l]=u[l]:i[e+l]=u[l]}),{$utms:n,otherUtms:i}},properties:function(){var t=window.innerHeight||document.documentElement.clientHeight||document.body&&document.body.clientHeight||0,e=window.innerWidth||document.documentElement.clientWidth||document.body&&document.body.clientWidth||0;return{$timezone_offset:new Date().getTimezoneOffset(),$screen_height:Number(screen.height)||0,$screen_width:Number(screen.width)||0,$viewport_height:t,$viewport_width:e,$lib:"js",$lib_version:rr}},currentProps:{},register:function(t){x(L.currentProps,t)}},q={},Jr=new wt;q.spa=Jr,q.sdk=new wt,q.data=new wt,q.initSystemEvent=function(){Ea(function(t){Jr.emit("switch",t)})},q.EVENT_LIST={spaSwitch:["spa","switch"],sdkBeforeInit:["sdk","beforeInit"],sdkInitPara:["sdk","initPara"],sdkAfterInitPara:["sdk","afterInitPara"],sdkInitAPI:["sdk","initAPI"],sdkAfterInitAPI:["sdk","afterInitAPI"],sdkAfterInit:["sdk","afterInit"],sdkReady:["sdk","ready"],dataSendSuccess:["data","sendSuccess"],dataSendFail:["data","sendFail"]};var Mr=function(t){this.callback=t.callback,this.server_url=t.server_url,this.data=t.data,this.origin_data=t.origin_data};Mr.prototype.start=function(){var t=this,e=new Date;$r({url:this.server_url,type:"POST",data:t.data,credentials:!1,timeout:y.datasend_timeout,cors:!0,success:function(r,n){q.data.emit("sendSuccess",{status:String(n),resText:r,type:"ajax_single",timeout_config:y.datasend_timeout,request_timeout:new Date-e,data:t.origin_data}),t.end()},error:function(r,n){q.data.emit("sendFail",{status:String(n),resText:r,type:"ajax_single",timeout_config:y.datasend_timeout,request_timeout:new Date-e,data:t.origin_data}),t.end()}})},Mr.prototype.end=function(){if(this.callback){if(Dt("warning: sdk callback is deprecated."),!S(this.callback))return void Dt("error: sdk callback must be function.");this.callback()}};var bi="sawebjssdk-",Br="tab-sawebjssdk-";Kn.prototype={batchInterval:function(){this.serverUrl===""&&this.getServerUrl(),this.hasTabStorage||(this.generateTabStorage(),this.hasTabStorage=!0);var t=this;t.timer=setTimeout(function(){t.updateExpireTime(),t.recycle(),t.send(),clearTimeout(t.timer),t.batchInterval()},y.batch_send.send_interval)},getServerUrl:function(){return h(y.server_url)&&y.server_url!==""||A(y.server_url)&&y.server_url.length?void(this.serverUrl=A(y.server_url)?y.server_url[0]:y.server_url):D("当前 server_url 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 server_url！")},send:function(){if(this.sendTimeStamp&&V()-this.sendTimeStamp<y.batch_send.send_interval)return!1;var t=E.get(this.tabKey);if(t){this.sendTimeStamp=V();var e=Ze((t=W(t)||this.generateTabStorageVal()).data);if(e.length){for(var r=[],n=0;n<e.length;n++){var i=s.store.readObjectVal(e[n]);i&&(i._flush_time=new Date().getTime(),r.push(i))}r.length&&this.request(r,t.data)}}},updateExpireTime:function(){var t=E.get(this.tabKey);t&&((t=W(t)||this.generateTabStorageVal()).expireTime=V()+2*y.batch_send.send_interval,t.serverUrl=this.serverUrl,E.set(this.tabKey,JSON.stringify(t)))},request:function(t,e){var r=this,n=new Date;$r({url:this.serverUrl,type:"POST",data:"data_list="+encodeURIComponent(je(JSON.stringify(t))),credentials:!1,timeout:y.batch_send.datasend_timeout,cors:!0,success:function(i,a){q.data.emit("sendSuccess",{status:String(a),resText:i,type:"ajax_batch",timeout_config:y.batch_send.datasend_timeout,request_timeout:new Date-n,data:t}),r.remove(e),r.sendTimeStamp=0},error:function(i,a){q.data.emit("sendFail",{status:String(a),resText:i,type:"ajax_batch",timeout_config:y.batch_send.datasend_timeout,request_timeout:new Date-n,data:t}),r.sendTimeStamp=0}})},remove:function(t){var e=E.get(this.tabKey);if(e){for(var r=(W(e)||this.generateTabStorageVal()).data,n=0;n<t.length;n++){var i=nt(r,t[n]);i>-1&&r.splice(i,1),E.remove(t[n])}r=Ze(r),E.set(this.tabKey,JSON.stringify(this.generateTabStorageVal(r)))}},add:function(t){var e=bi+String(Z()),r=E.get(this.tabKey);r===null?(this.tabKey=Br+String(Z()),r=this.generateTabStorageVal()):r=W(r)||this.generateTabStorageVal(),r.data.push(e),r.expireTime=V()+2*y.batch_send.send_interval,E.set(this.tabKey,JSON.stringify(r)),s.store.saveObjectVal(e,t),t.type!=="track_signup"&&t.event!=="$pageview"||this.sendImmediately()},generateTabStorage:function(){this.tabKey=Br+String(Z()),E.set(this.tabKey,JSON.stringify(this.generateTabStorageVal()))},generateTabStorageVal:function(t){return{data:t=t||[],expireTime:V()+2*y.batch_send.send_interval,serverUrl:this.serverUrl}},sendImmediately:function(){this.send()},recycle:function(){for(var t={},e=1e4,r="sajssdk-lock-get-",n=0;n<localStorage.length;n++){var i=localStorage.key(n),a=this;if(i.indexOf(Br)===0){for(var l=W(E.get(i))||this.generateTabStorageVal(),u=0;u<l.data.length;u++)t[l.data[u]]=!0;i!==a.tabKey&&V()>l.expireTime&&this.serverUrl===l.serverUrl&&new bt(r).get(i,e,1e3,function(T){if(T){E.get(a.tabKey)===null&&a.generateTabStorage();var I=W(T)||a.generateTabStorageVal(),F=W(E.get(a.tabKey))||a.generateTabStorageVal();F.data=Ze(F.data.concat(I.data)),E.set(a.tabKey,JSON.stringify(F))}})}else if(i.indexOf(r)===0){var c=W(E.get(i))||{expireTime:0};V()-c.expireTime>e&&E.remove(i)}}for(var f=0;f<localStorage.length;f++){var _=localStorage.key(f);_.indexOf(bi)!==0||t[_]||E.remove(_)}}};var Fr=function(t){this.callback=t.callback,this.server_url=t.server_url,this.data=t.data};Fr.prototype.start=function(){var t=this;typeof navigator=="object"&&typeof navigator.sendBeacon=="function"&&navigator.sendBeacon(this.server_url,this.data),setTimeout(function(){t.end()},40)},Fr.prototype.end=function(){if(this.callback){if(Dt("warning: sdk callback is deprecated."),!S(this.callback))return void Dt("error: sdk callback must be function.");this.callback()}};var ir=function(t){this.callback=t.callback,this.img=document.createElement("img"),this.img.width=1,this.img.height=1,y.img_use_crossorigin&&(this.img.crossOrigin="anonymous"),this.server_url=t.data};ir.prototype.start=function(){var t=this;y.ignore_oom&&(this.img.onload=function(){this.onload=null,this.onerror=null,this.onabort=null,t.end()},this.img.onerror=function(){this.onload=null,this.onerror=null,this.onabort=null,t.end()},this.img.onabort=function(){this.onload=null,this.onerror=null,this.onabort=null,t.end()}),this.img.src=this.server_url},ir.prototype.lastClear=function(){Sr().ie!==void 0?this.img.src="about:blank":this.img.src=""},ir.prototype.end=function(){if(this.callback){if(Dt("warning: sdk callback is deprecated."),!S(this.callback))return void Dt("error: sdk callback must be function.");this.callback()}self.lastClear&&self.lastClear()};var xl={__proto__:null,addEvent:oe,EventEmitterSa:Hr,cookie:vt,info:L,getReferrer:Wt,getCurrentDomain:Ir,isBaiduTraffic:Te,getReferrerEqid:An,getReferrerEqidType:xn,getBaiduKeyword:Le,isReferralTraffic:Qe,getKeywordFromReferrer:$e,getReferSearchEngine:En,getSourceFromReferrer:Tr,getWxAdIdFromUrl:Ln,parseSuperProperties:Un,searchConfigData:Rn,strip_empty_properties:Hn,getEleInfo:Jn,getElementContent:Ye,ajax:$r,optimizeServerUrl:Mn,encodeTrackData:Bn,AjaxSend:Mr,BatchSend:Kn,BeaconSend:Fr,ImageSend:ir},wi=new Hr,p={requests:[],_sessionState:{},_state:{distinct_id:"",first_id:"",props:{},identities:{}},getProps:function(){return this._state.props||{}},getSessionProps:function(){return this._sessionState},getOriginDistinctId:function(){return this._state._distinct_id||this._state.distinct_id},getOriginUnionId:function(t){var e={},r=(t=t||this._state)._first_id||t.first_id,n=t._distinct_id||t.distinct_id;return r&&n?(e.login_id=n,e.anonymous_id=r):e.anonymous_id=n,e},getIdentities:function(){var t=JSON.parse(JSON.stringify(this._state.identities));return t.$identity_anonymous_id=this.getAnonymousId(),t},getAnonymousId:function(){return p._state._first_id||p._state.first_id||p._state._distinct_id||p._state.distinct_id},getDistinctId:function(){var t=this.getUnionId();return t.login_id||t.anonymous_id},getUnionId:function(t){var e=this.getOriginUnionId(t);return e.login_id&&this._state.history_login_id&&this._state.history_login_id.name&&this._state.history_login_id.name!==Ae.LOGIN&&(e.login_id=this._state.history_login_id.name+"+"+e.login_id),e},getFirstId:function(){return this._state._first_id||this._state.first_id},initSessionState:function(){var t=vt.get("sensorsdata2015session"),e=null;(t=s.kit.userDecryptIfNeeded(t))!==null&&typeof(e=W(t))=="object"&&(this._sessionState=e||{})},setOnce:function(t,e){t in this._state||this.set(t,e)},set:function(t,e){this._state=this._state||{};var r=this._state.distinct_id;this._state[t]=e,t==="first_id"?delete this._state._first_id:t==="distinct_id"&&delete this._state._distinct_id,this.save(),t==="distinct_id"&&r&&wi.tempAdd("changeDistinctId",e)},change:function(t,e){this._state["_"+t]=e},setSessionProps:function(t){s.log("initSessionState 方法已经弃用，请不要使用该功能，如有需求联系技术顾问");var e=this._sessionState;x(e,t),this.sessionSave(e)},setSessionPropsOnce:function(t){s.log("initSessionState 方法已经弃用，请不要使用该功能，如有需求联系技术顾问");var e=this._sessionState;vr(e,t),this.sessionSave(e)},setProps:function(t,e){var r={};for(var n in r=e?t:x(this._state.props||{},t))typeof r[n]=="string"&&(r[n]=r[n].slice(0,s.para.max_referrer_string_length));this.set("props",r)},setPropsOnce:function(t){var e=this._state.props||{};vr(e,t),this.set("props",e)},clearAllProps:function(t){var e;if(this._sessionState={},A(t)&&t.length>0)for(e=0;e<t.length;e++)h(t[e])&&t[e].indexOf("latest_")===-1&&d(this._state.props)&&t[e]in this._state.props&&delete this._state.props[t[e]];else if(d(this._state.props))for(e in this._state.props)e.indexOf("latest_")!==1&&delete this._state.props[e];this.sessionSave({}),this.save()},sessionSave:function(t){s.log("initSessionState 方法已经弃用，请不要使用该功能，如有需求联系技术顾问"),this._sessionState=t;var e=JSON.stringify(this._sessionState);s.para.encrypt_cookie&&(e=s.kit.userEncrypt(e)),vt.set("sensorsdata2015session",e,0)},save:function(){var t=JSON.parse(JSON.stringify(this._state));delete t._first_id,delete t._distinct_id,t.identities&&(t.identities=je(JSON.stringify(t.identities)));var e=JSON.stringify(t);s.para.encrypt_cookie&&(e=s.kit.userEncrypt(e)),vt.set(this.getCookieName(),e,360,s.para.cross_subdomain)},getCookieName:function(){var t="";if(s.para.cross_subdomain===!1){try{var e=location.host;It(s.para.white_list[e])||(t=s.para.white_list[e])}catch(r){j(r)}t=typeof t=="string"&&t!==""?"sa_jssdk_2015_"+s.para.sdk_id+t.replace(/\./g,"_"):"sa_jssdk_2015_root"+s.para.sdk_id}else t="sensorsdata2015jssdkcross"+s.para.sdk_id;return t},init:function(){function t(a){var l;a.identities&&(a.identities.indexOf(`
/`)===0?a.identities=W(Tn(a.identities)):a.identities=W(ve(a.identities)));var u=p.getOriginUnionId(a);a.identities&&d(a.identities)&&!it(a.identities)||(a.identities={},a.identities.$identity_cookie_id=Xt()),a.history_login_id=a.history_login_id||{};var c=a.history_login_id.name;if(u.login_id)if(c&&a.identities.hasOwnProperty(c)){if(a.identities[c]!==u.login_id){for(l in a.identities[c]=u.login_id,a.identities)a.identities.hasOwnProperty(l)&&l!=="$identity_cookie_id"&&l!==c&&delete a.identities[l];a.history_login_id.value=u.login_id}}else{var f=c||Ae.LOGIN;for(l in a.identities[f]=u.login_id,a.identities)a.identities.hasOwnProperty(l)&&l!=="$identity_cookie_id"&&l!==f&&delete a.identities[l];a.history_login_id={name:f,value:u.login_id}}else{if(a.identities.hasOwnProperty("$identity_login_id")||a.identities.hasOwnProperty(c))for(l in a.identities)a.identities.hasOwnProperty(l)&&l!=="$identity_cookie_id"&&l!=="$identity_anonymous_id"&&delete a.identities[l];a.history_login_id={name:"",value:""}}return a}function e(a){p.set("distinct_id",a),p.set("identities",{$identity_cookie_id:a}),p.set("history_login_id",{name:"",value:""})}this.initSessionState();var r,n,i=Xt();vt.isSupport()&&(r=vt.get(this.getCookieName()),n=W(r=s.kit.userDecryptIfNeeded(r))),vt.isSupport()&&r!==null&&ze(r)&&d(n)&&(!d(n)||n.distinct_id)?(p._state=x(t(n)),p.save()):(s.is_first_visitor=!0,e(i)),Ee.setDeviceId(i,this),Ee.storeInitCheck()},saveObjectVal:function(t,e){h(e)||(e=JSON.stringify(e)),s.para.encrypt_cookie==1&&(e=s.kit.userEncrypt(e)),E.set(t,e)},readObjectVal:function(t){var e=E.get(t);return e?W(e=s.kit.userDecryptIfNeeded(e)):null}},K={string:function(t){j(t+" must be string")},emptyString:function(t){j(t+"'s is empty")},regexTest:function(t){j(t+" is invalid")},idLength:function(t){j(t+" length is longer than "+y.max_id_length)},keyLength:function(t){j(t+" length is longer than "+y.max_key_length)},stringLength:function(t){j(t+" length is longer than "+y.max_string_length)},voidZero:function(t){j(t+"'s is undefined")},reservedLoginId:function(t){j(t+" is invalid")},reservedBind:function(t){j(t+" is invalid")},reservedUnbind:function(t){j(t+" is invalid")}},Si={regName:/^((?!^distinct_id$|^original_id$|^time$|^properties$|^id$|^first_id$|^second_id$|^users$|^events$|^event$|^user_id$|^date$|^datetime$|^user_tag.*|^user_group.*)[a-zA-Z_$][a-zA-Z\d_$]*)$/i,loginIDReservedNames:["$identity_anonymous_id","$identity_cookie_id"],bindReservedNames:["$identity_login_id","$identity_anonymous_id","$identity_cookie_id"],unbindReservedNames:["$identity_anonymous_id",Ae.LOGIN],string:function(t){return!!h(t)},emptyString:function(t){return!(!h(t)||mt(t).length===0)},regexTest:function(t){return!(!h(t)||!this.regName.test(t))},idLength:function(t){return!(!h(t)||t.length>y.max_id_length)},keyLength:function(t){return!(!h(t)||t.length>y.max_key_length)},stringLength:function(t){return!(!h(t)||t.length>y.max_string_length)},voidZero:function(t){return t!==void 0},reservedLoginId:function(t){return!(nt(this.loginIDReservedNames,t)>-1)},reservedUnbind:function(t){return!(nt(this.unbindReservedNames,t)>-1)},reservedBind:function(t){var e=p._state.history_login_id;return!(e&&e.name&&e.name===t||nt(this.bindReservedNames,t)>-1)}},Al={distinct_id:{rules:["string","emptyString","idLength"],onComplete:function(t,e,r){return!t&&(r==="emptyString"&&(e="Id"),S(K[r])&&K[r](e),r==="idLength")||t}},event:{rules:["string","emptyString","keyLength","regexTest"],onComplete:function(t,e,r){return t||(r==="emptyString"&&(e="eventName"),S(K[r])&&K[r](e)),!0}},propertyKey:{rules:["string","emptyString","keyLength","regexTest"],onComplete:function(t,e,r){return t||(r==="emptyString"&&(e="Property key"),S(K[r])&&K[r](e)),!0}},propertyValue:{rules:["voidZero"],onComplete:function(t,e,r){return t||(e="Property Value",S(K[r])&&K[r](e)),!0}},properties:function(t){return d(t)?$(t,function(e,r){H({propertyKey:r});var n=function(i,a,l){return i||(a=r+"'s Value",S(K[l])&&K[l](a)),!0};H({propertyValue:e},n)}):Si.voidZero(t)&&j("properties可以没有，但有的话必须是对象"),!0},propertiesMust:function(t){return t!==void 0&&d(t)&&!it(t)?this.properties.call(this,t):j("properties必须是对象"),!0},item_type:{rules:["string","emptyString","keyLength","regexTest"],onComplete:function(t,e,r){return t||(r==="emptyString"&&(e="item_type"),S(K[r])&&K[r](e)),!0}},item_id:{rules:["string","emptyString","stringLength"],onComplete:function(t,e,r){return t||(r==="emptyString"&&(e="item_id"),S(K[r])&&K[r](e)),!0}},loginIdKey:{rules:["string","emptyString","keyLength","regexTest","reservedLoginId"],onComplete:function(t,e,r){return!t&&(r==="emptyString"&&(e="login_id_key"),S(K[r])&&K[r](e),r==="keyLength")||t}},bindKey:{rules:["string","emptyString","keyLength","regexTest","reservedBind"],onComplete:function(t,e,r){return!t&&(r==="emptyString"&&(e="Key"),S(K[r])&&K[r](e),r==="keyLength")||t}},unbindKey:{rules:["string","emptyString","keyLength","regexTest","reservedUnbind"],onComplete:function(t,e,r){return!t&&(r==="emptyString"&&(e="Key"),S(K[r])&&K[r](e),r==="keyLength")||t}},bindValue:{rules:["string","emptyString","idLength"],onComplete:function(t,e,r){return!t&&(r==="emptyString"&&(e="Value"),S(K[r])&&K[r](e),r==="idLength")||t}},check:function(t,e,r){var n=this[t];if(S(n))return n.call(this,e);if(!n)return!1;for(var i=0;i<n.rules.length;i++){var a=n.rules[i],l=Si[a](e),u=S(r)?r(l,e,a):n.onComplete(l,e,a);if(!l)return u}return!0}},G={initUrl:function(){var t,e,r={server_url:{project:"",host:""},page_url:{host:"",pathname:""}};if(!Cn(s.para.server_url))return D("----vcollect---server_url必须为有效 URL 字符串"),!1;try{t=Jt(s.para.server_url),r.server_url.project=t.searchParams.get("project")||"default",r.server_url.host=t.host}catch(n){return D("----vcollect---server_url解析异常",n),!1}try{e=Jt(location.href),r.page_url.host=e.hostname,r.page_url.pathname=e.pathname}catch(n){return D("----vcollect---页面地址解析异常",n),!1}return r},isDiv:function(t){return!t.element_path||mt(t.element_path.split(">").pop()).slice(0,3)==="div"},configIsMatchNew:function(t,e){if(h(t.$element_selector)&&h(e.element_selector)){if(e.element_field==="element_selector"&&e.function==="equal")return t.$element_selector===e.element_selector;if(e.element_field==="element_selector"&&e.function==="contain")return t.$element_selector.indexOf(e.element_selector)>-1}if(h(t.$element_path)&&h(e.element_path)){if(e.element_field==="element_path"&&e.function==="equal")return t.$element_path===e.element_path;if(e.element_field==="element_path"&&e.function==="contain")return t.$element_path.indexOf(e.element_path)>-1}return!1},configIsMatch:function(t,e){return(!e.limit_element_content||e.element_content===t.$element_content)&&(!e.limit_element_position||e.element_position===String(t.$element_position))&&(e.element_field&&e.function?G.configIsMatchNew(t,e):G.configIsMatchOldVersion(t,e))},configIsMatchOldVersion:function(t,e){if(!e.element_path)return!1;if(t.$element_position!==void 0){if(e.element_path!==t.$element_path)return!1}else if(G.isDiv({element_path:e.element_path})){if(t.$element_path.indexOf(e.element_path)<0)return!1}else if(e.element_path!==t.$element_path)return!1;return!0},filterConfig:function(t,e,r){var n=[];if(!r){var i=G.initUrl();if(!i)return[];r=i.page_url}return t.event==="$WebClick"&&$(e,function(a){d(a)&&(a.event_type==="webclick"||a.event_type==="appclick")&&d(a.event)&&a.event.url_host===r.host&&a.event.url_path===r.pathname&&G.configIsMatch(t.properties,a.event)&&n.push(a)}),n},getPropElInLi:function(t,e){if(!(t&&rt(t)&&h(e))||t.tagName.toLowerCase()!=="li")return null;var r=s.heatmap.getDomSelector(t);if(r){var n=qe(r+e);return n||null}return j("----custom---获取同级属性元素失败，selector信息异常",r,e),null},getProp:function(t,e){if(!d(t))return!1;if(!(h(t.name)&&t.name.length>0))return j("----vcustom----属性名不合法,属性抛弃",t.name),!1;var r,n,i={};if(t.method==="content"){var a;if(h(t.element_selector)&&t.element_selector.length>0)a=qe(t.element_selector);else{if(!e||!h(t.list_selector))return j("----vcustom----属性配置异常，属性抛弃",t.name),!1;var l=qe(e.properties.$element_selector);if(!l)return j("----vcustom----点击元素获取异常，属性抛弃",t.name),!1;var u=s.heatmap.getClosestLi(l);a=G.getPropElInLi(u,t.list_selector)}if(!a||!rt(a))return j("----vcustom----属性元素获取失败，属性抛弃",t.name),!1;if(a.tagName.toLowerCase()==="input")r=a.value||"";else if(a.tagName.toLowerCase()==="select"){var c=a.selectedIndex;$t(c)&&rt(a[c])&&(r=Ye(a[c],"select"))}else r=Ye(a,a.tagName.toLowerCase());if(t.regular){try{n=new RegExp(t.regular).exec(r)}catch{return j("----vcustom----正则处理失败，属性抛弃",t.name),!1}if(n===null)return j("----vcustom----属性规则处理，未匹配到结果,属性抛弃",t.name),!1;if(!A(n)||!h(n[0]))return j("----vcustom----正则处理异常，属性抛弃",t.name,n),!1;r=n[0]}if(t.type==="STRING")i[t.name]=r;else if(t.type==="NUMBER"){if(r.length<1)return j("----vcustom----未获取到数字内容，属性抛弃",t.name,r),!1;if(isNaN(Number(r)))return j("----vcustom----数字类型属性转换失败，属性抛弃",t.name,r),!1;i[t.name]=Number(r)}return i}return D("----vcustom----属性不支持此获取方式",t.name,t.method),!1},getAssignConfigs:function(t,e){var r=G.initUrl();if(!r||!r.page_url)return[];if(!d(e))return[];var n=[];return e.events=e.events||e.eventList,A(e.events)&&e.events.length>0?($(e.events,function(i){d(i)&&d(i.event)&&i.event.url_host===r.page_url.host&&i.event.url_path===r.page_url.pathname&&t(i)&&n.push(i)}),n):[]}},El={events:[],getAssignConfigs:G.getAssignConfigs,filterConfig:G.filterConfig,getProp:G.getProp,initUrl:G.initUrl,updateEvents:function(t){A(t)&&(this.events=t)},init:function(){this.initAppGetPropsBridge()},geth5Props:function(t){var e={},r=[],n=this;if(!this.events.length)return{};if(t.event==="$WebClick"){var i=this.filterConfig(t,this.events);if(!i.length)return{};$(i,function(a){d(a)&&(A(a.properties)&&a.properties.length>0&&$(a.properties,function(l){if(d(l))if(l.h5===!1)A(e.sensorsdata_app_visual_properties)||(e.sensorsdata_app_visual_properties=[]),e.sensorsdata_app_visual_properties.push(l);else{var u=n.getProp(l,t);d(u)&&(e=x(e,u))}}),h(a.event_name)&&r.push(a.event_name))}),s.bridge.hasVisualModeBridge()&&(e.sensorsdata_web_visual_eventName=r)}return e.sensorsdata_app_visual_properties&&(e.sensorsdata_app_visual_properties=je(JSON.stringify(e.sensorsdata_app_visual_properties))),e},initAppGetPropsBridge:function(){var t=this,e=new s.SDKJSBridge("getJSVisualProperties");return e.onAppNotify(function(r){var n={};try{r=JSON.parse(ve(r))}catch{D("getJSVisualProperties data parse error!")}if(d(r)){var i=r.sensorsdata_js_visual_properties,a=t.initUrl();a&&(a=a.page_url,A(i)&&i.length>0&&$(i,function(l){if(d(l)&&l.url_host===a.host&&l.url_path===a.pathname&&l.h5){var u=t.getProp(l);d(u)&&(n=x(n,u))}}))}return s.bridge.bridge_info.platform==="android"&&e.notifyApp({data:n},r.message_id),n}),e}},Kr={events:[],customProp:El,getAssignConfigs:G.getAssignConfigs,initUrl:G.initUrl,init:function(){if(this.initUrl()){var t=this.getConfigFromApp();t&&this.updateConfigs(t),this.customProp.init(),this.initAppUpdateConfigBridge()}},initAppUpdateConfigBridge:function(){var t=this;return new s.SDKJSBridge("updateH5VisualConfig").onAppNotify(function(e){if(e){try{e=JSON.parse(ve(e))}catch{return void D("updateH5VisualConfig result parse error！")}t.updateConfigs(e)}})},getConfigFromApp:function(){var t=new s.SDKJSBridge("sensorsdata_get_app_visual_config").notifyApp();if(t)try{t=JSON.parse(ve(t))}catch{t=null,D("getAppVisualConfig result parse error！")}return t},updateConfigs:function(t){this.events=this.filterConfigs(t),this.customProp.updateEvents(this.events)},filterConfigs:function(t){return this.getAssignConfigs(function(e){return!(!d(e)||e.h5===!1)},t)}},ar={events:[],init:function(t){this.filterWebClickEvents(t)},filterWebClickEvents:function(t){this.events=Gt.getAssignConfigs(function(e){return!(!d(e)||e.event.unlimited_div!==!0||e.event_type!=="webclick")},t)},isTargetEle:function(t){var e=s.heatmap.getEleDetail(t);if(!d(e)||!h(e.$element_path))return!1;for(var r=0;r<this.events.length;r++)if(d(this.events[r])&&d(this.events[r].event)&&Gt.configIsMatch(e,this.events[r].event))return!0;return!1}},ki={events:[],configSwitch:!1,collectAble:function(){return this.configSwitch&&d(s.para.heatmap)&&s.para.heatmap.get_vtrack_config},updateEvents:function(t){this.events=Gt.getAssignConfigs(function(e){return!!(d(e)&&A(e.properties)&&e.properties.length>0)},t),this.events.length?this.configSwitch=!0:this.configSwitch=!1},getVtrackProps:function(t){var e={};return this.collectAble()?(t.event==="$WebClick"&&(e=this.clickCustomPropMaker(t,this.events)),e):{}},clickCustomPropMaker:function(t,e,r){var n=this;r=r||this.filterConfig(t,e,Gt.url_info.page_url);var i={};return r.length?($(r,function(a){A(a.properties)&&a.properties.length>0&&$(a.properties,function(l){var u=n.getProp(l,t);d(u)&&x(i,u)})}),i):{}},getProp:G.getProp,getPropElInLi:G.getPropElInLi,filterConfig:G.filterConfig},Gt={unlimitedDiv:ar,config:{},storageEnable:!0,storage_name:"webjssdkvtrackcollect",para:{session_time:18e5,timeout:5e3,update_interval:18e5},url_info:{},timer:null,update_time:null,customProp:ki,initUrl:function(){var t=G.initUrl();if(t){var e;try{(e=new mr(s.para.server_url))._values.Path="/config/visualized/Web.conf",t.api_url=e.getUrl()}catch(r){return D("----vtrackcollect---API地址解析异常",r),!1}this.url_info=t}return t},init:function(){if(!d(s.para.heatmap)||!s.para.heatmap.get_vtrack_config)return!1;if(E.isSupport()||(this.storageEnable=!1),!this.initUrl())return D("----vtrackcustom----初始化失败，url信息解析失败"),!1;if(this.storageEnable){var t=p.readObjectVal(this.storage_name);if(d(t)&&d(t.data))if(this.serverUrlIsSame(t.serverUrl)){this.config=t.data,this.update_time=t.updateTime,this.updateConfig(t.data);var e=new Date().getTime()-this.update_time;if($t(e)&&e>0&&e<this.para.session_time){var r=this.para.update_interval-e;this.setNextFetch(r)}else this.getConfigFromServer()}else this.getConfigFromServer();else this.getConfigFromServer()}else this.getConfigFromServer();this.pageStateListenner()},serverUrlIsSame:function(t){return!!d(t)&&t.host===this.url_info.server_url.host&&t.project===this.url_info.server_url.project},getConfigFromServer:function(){var t=this,e=function(n,i){t.update_time=new Date().getTime();var a={};n===200?i&&d(i)&&i.os==="Web"&&(a=i,t.updateConfig(a)):n===205?t.updateConfig(a):n===304?a=t.config:(D("----vtrackcustom----数据异常",n),t.updateConfig(a)),t.updateStorage(a),t.setNextFetch()},r=function(n){t.update_time=new Date().getTime(),D("----vtrackcustom----配置拉取失败",n),t.setNextFetch()};this.sendRequest(e,r)},setNextFetch:function(t){var e=this;this.timer&&(clearTimeout(this.timer),this.timer=null),t=t||this.para.update_interval,this.timer=setTimeout(function(){e.getConfigFromServer()},t)},pageStateListenner:function(){var t=this;Nn({visible:function(){var e=new Date().getTime()-t.update_time;if($t(e)&&e>0&&e<t.para.update_interval){var r=t.para.update_interval-e;t.setNextFetch(r)}else t.getConfigFromServer()},hidden:function(){t.timer&&(clearTimeout(t.timer),t.timer=null)}})},updateConfig:function(t){return!!d(t)&&(this.config=t,this.customProp.updateEvents(t),void this.unlimitedDiv.init(t))},updateStorage:function(t){if(!this.storageEnable||!d(t))return!1;var e;if(this.url_info.server_url)e=this.url_info.server_url;else{var r=Gt.initUrl();if(!r)return!1;e=r.server_url}var n={updateTime:new Date().getTime(),data:t,serverUrl:e};p.saveObjectVal(this.storage_name,n)},sendRequest:function(t,e){var r=this,n={app_id:this.url_info.page_url.host};this.config.version&&(n.v=this.config.version),jn({url:r.url_info.api_url,callbackName:"saJSSDKVtrackCollectConfig",data:n,timeout:r.para.timeout,success:function(i,a){t(i,a)},error:function(i){e(i)}})},getAssignConfigs:G.getAssignConfigs,configIsMatch:G.configIsMatch},Vr={stage:null,init:function(t){this.stage=t},interceptor:{basicProps:{priority:0,entry:Ka},formatData:{priority:0,entry:Ba},finalAdjustData:{priority:0,entry:Fa}}},ot={};ot.check=H,ot.sendItem=function(t){var e={lib:{$lib:"js",$lib_method:"code",$lib_version:String(s.lib_version)},time:1*new Date};x(e,t),qa(e),s.kit.sendData(e)},ot.send=function(t,e){var r=s.kit.buildData(t);s.kit.sendData(r,e)};var qr={stage:null,init:function(t){this.stage=t}},zr={label:!1,li:!1,a:!0,button:!0},St={otherTags:[],initUnlimitedTags:function(){$(St.otherTags,function(t){t in zr&&(zr[t]=!0)})},isUnlimitedTag:function(t){if(!t||t.nodeType!==1)return!1;var e=t.nodeName.toLowerCase();return zr[e]||Pr(t,s.para.heatmap.track_attr)},getTargetElement:function(t,e){var r=this,n=t;if(typeof n!="object"||typeof n.tagName!="string")return null;var i=n.tagName.toLowerCase();if(i.toLowerCase()==="body"||i.toLowerCase()==="html"||!n||!n.parentNode||!n.parentNode.children)return null;var a=n.parentNode,l=r.otherTags;if(i==="a"||i==="button"||i==="input"||i==="textarea"||nt(l,i)>-1)return n;if(i==="area"&&a.tagName.toLowerCase()==="map"&&dt(a).prev().tagName&&dt(a).prev().tagName.toLowerCase()==="img")return dt(a).prev();if(i==="div"&&s.para.heatmap.collect_tags.div&&r.isDivLevelValid(n)&&((s.para.heatmap&&s.para.heatmap.collect_tags&&s.para.heatmap.collect_tags.div&&s.para.heatmap.collect_tags.div.max_level||1)>1||r.isCollectableDiv(n)))return n;if(r.isStyleTag(i)&&s.para.heatmap.collect_tags.div){var u=r.getCollectableParent(n);if(u&&r.isDivLevelValid(u))return u}var c=r.hasElement({event:e&&e.originalEvent||e,element:t},function(f){return r.isUnlimitedTag(f)});return c||null},getDivLevels:function(t,e){var r=St.getElementPath(t,!0,e).split(" > "),n=0;return $(r,function(i){i==="div"&&n++}),n},isDivLevelValid:function(t){for(var e=s.para.heatmap&&s.para.heatmap.collect_tags&&s.para.heatmap.collect_tags.div&&s.para.heatmap.collect_tags.div.max_level||1,r=t.getElementsByTagName("div"),n=r.length-1;n>=0;n--)if(St.getDivLevels(r[n],t)>e)return!1;return!0},getElementPath:function(t,e,r){for(var n=[];t.parentNode&&rt(t);){if(!h(t.tagName))return"unknown";if(t.id&&!e&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(t.id)){n.unshift(t.tagName.toLowerCase()+"#"+t.id);break}if(r&&t===r){n.unshift(t.tagName.toLowerCase());break}if(t===document.body){n.unshift("body");break}n.unshift(t.tagName.toLowerCase()),t=t.parentNode}return n.join(" > ")},getClosestLi:function(t){var e=function(r,n){for(;r&&r!==document&&r.nodeType===1;r=r.parentNode)if(r.tagName&&h(r.tagName)&&r.tagName.toLowerCase()===n)return r;return null};return e(t,"li")},getElementPosition:function(t,e,r){function n(_){if(!_.parentNode)return"";if(dt(_).getSameTypeSiblings().length===1)return 0;for(var T=0,I=_;dt(I).previousElementSibling().ele;I=dt(I).previousElementSibling().ele,T++);return T}var i=s.heatmap.getClosestLi(t);if(!i||!rt(t)||!h(t.tagName))return null;var a=t.tagName.toLowerCase(),l=i.getElementsByTagName(a),u=l.length,c=[];if(u>1){for(var f=0;f<u;f++)s.heatmap.getElementPath(l[f],r)===e&&c.push(l[f]);if(c.length>1)return nt(c,t)}return n(i)},setNotice:function(t){s.is_heatmap_render_mode=!0,s.para.heatmap||(s.errorMsg="您SDK没有配置开启点击图，可能没有数据！"),t&&t.slice(0,5)==="http:"&&location.protocol==="https:"&&(s.errorMsg="您的当前页面是https的地址，神策分析环境也必须是https！"),s.para.heatmap_url||(s.para.heatmap_url=zn()+"//static.sensorsdata.cn/sdk/"+s.lib_version+"/heatmap.min.js")},getDomIndex:function(t){if(!t.parentNode)return-1;for(var e=0,r=t.tagName,n=t.parentNode.children,i=0;i<n.length;i++)if(n[i].tagName===r){if(t===n[i])return e;e++}return-1},selector:function(t,e){if(!t||!rt(t)||!h(t.tagName))return"";var r=t.parentNode&&t.parentNode.nodeType==9?-1:this.getDomIndex(t);return t.getAttribute&&t.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(t.getAttribute("id"))&&(!s.para.heatmap||s.para.heatmap&&s.para.heatmap.element_selector!=="not_use_id")&&!e?"#"+t.getAttribute("id"):t.tagName.toLowerCase()+(~r?":nth-of-type("+(r+1)+")":"")},getDomSelector:function(t,e,r){if(!(t&&t.parentNode&&t.parentNode.children&&h(t.tagName)))return"unknown";e=e&&e.join?e:[];var n=t.nodeName.toLowerCase();return t&&n!=="body"&&t.nodeType==1?(e.unshift(this.selector(t,r)),t.getAttribute&&t.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(t.getAttribute("id"))&&s.para.heatmap&&s.para.heatmap.element_selector!=="not_use_id"&&!r?e.join(" > "):this.getDomSelector(t.parentNode,e,r)):(e.unshift("body"),e.join(" > "))},na:function(){var t=document.documentElement.scrollLeft||window.pageXOffset;return parseInt(isNaN(t)?0:t,10)},i:function(){var t=0;try{t=o.documentElement&&o.documentElement.scrollTop||m.pageYOffset,t=isNaN(t)?0:t}catch{t=0}return parseInt(t,10)},getBrowserWidth:function(){var t=window.innerWidth||document.body.clientWidth;return isNaN(t)?0:parseInt(t,10)},getBrowserHeight:function(){var t=window.innerHeight||document.body.clientHeight;return isNaN(t)?0:parseInt(t,10)},getScrollWidth:function(){var t=parseInt(document.body.scrollWidth,10);return isNaN(t)?0:t},getEleDetail:function(t){var e=this.getDomSelector(t),r=Jn({target:t});r.$element_selector=e||"",r.$element_path=s.heatmap.getElementPath(t,s.para.heatmap&&s.para.heatmap.element_selector==="not_use_id");var n=s.heatmap.getElementPosition(t,r.$element_path,s.para.heatmap&&s.para.heatmap.element_selector==="not_use_id");return $t(n)&&(r.$element_position=n),r},getPointerEventProp:function(t,e){function r(){return{scrollLeft:document.body.scrollLeft||document.documentElement.scrollLeft||0,scrollTop:document.body.scrollTop||document.documentElement.scrollTop||0}}function n(l){if(document.documentElement.getBoundingClientRect){var u=l.getBoundingClientRect();return{targetEleX:u.left+r().scrollLeft||0,targetEleY:u.top+r().scrollTop||0}}}function i(l){return Number(Number(l).toFixed(3))}function a(l){var u=l.pageX||l.clientX+r().scrollLeft||l.offsetX+n(e).targetEleX||0,c=l.pageY||l.clientY+r().scrollTop||l.offsetY+n(e).targetEleY||0;return{$page_x:i(u),$page_y:i(c)}}return t?a(t):{}},start:function(t,e,r,n,i){if(d(s.para.heatmap)&&S(s.para.heatmap.collect_element)&&!s.para.heatmap.collect_element(e))return!1;za(St.getBasicEleInfo(t,e,r,n,i))},getBasicEleInfo:function(t,e,r,n,i){var a=d(n)?n:{},l=S(i)?i:S(n)?n:void 0,u=this.getEleDetail(e);if(s.para.heatmap&&s.para.heatmap.custom_property){var c=s.para.heatmap.custom_property(e);d(c)&&(u=x(u,c))}return{event:t,target:e,props:u=x(u,this.getPointerEventProp(t,e),a),tagName:r,callback:l}},hasElement:function(t,e){var r;if(t.event){var n=t.event;r=n.path||n._getPath&&n._getPath()}else t.element&&(r=dt(t.element).getParents());if(r&&A(r)&&r.length>0){for(var i=0;i<r.length;i++)if(typeof r[i]=="object"&&r[i].nodeType===1&&e(r[i]))return r[i]}},isStyleTag:function(t,e){var r=["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"];return!(nt(["a","div","input","button","textarea"],t)>-1)&&(!e||s.para.heatmap&&s.para.heatmap.collect_tags&&s.para.heatmap.collect_tags.div?!!(d(s.para.heatmap)&&d(s.para.heatmap.collect_tags)&&d(s.para.heatmap.collect_tags.div)&&A(s.para.heatmap.collect_tags.div.ignore_tags)&&nt(s.para.heatmap.collect_tags.div.ignore_tags,t)>-1):nt(r,t)>-1)},isCollectableDiv:function(t,e){try{if(t.children.length===0)return!0;for(var r=0;r<t.children.length;r++)if(t.children[r].nodeType===1){var n=h(t.children[r].tagName)?t.children[r].tagName.toLowerCase():"unknown",i=s.para&&s.para.heatmap&&s.para.heatmap.collect_tags&&s.para.heatmap.collect_tags.div&&s.para.heatmap.collect_tags.div.max_level;if(!(n==="div"&&i>1||this.isStyleTag(n,e))||!this.isCollectableDiv(t.children[r],e))return!1}return!0}catch(a){D("isCollectableDiv:"+a)}return!1},getCollectableParent:function(t,e){try{var r=t.parentNode,n=r?r.tagName.toLowerCase():"";if(n==="body")return!1;var i=s.para&&s.para.heatmap&&s.para.heatmap.collect_tags&&s.para.heatmap.collect_tags.div&&s.para.heatmap.collect_tags.div.max_level;if(n&&n==="div"&&(i>1||this.isCollectableDiv(r,e)))return r;if(r&&this.isStyleTag(n,e))return this.getCollectableParent(r,e)}catch(a){D("getCollectableParent:"+a)}return!1},listenUrlChange:function(t){t(),s.ee.spa.on("switch",function(){t()})},initScrollmap:function(){if(!d(s.para.heatmap)||s.para.heatmap.scroll_notice_map!=="default")return!1;var t=!0;s.para.scrollmap&&S(s.para.scrollmap.collect_url)&&this.listenUrlChange(function(){t=!!s.para.scrollmap.collect_url()});var e=function(n){var i={};return i.timeout=n.timeout||1e3,i.func=n.func,i.hasInit=!1,i.inter=null,i.main=function(a,l){this.func(a,l),this.inter=null},i.go=function(a){var l={};this.inter||(l.$viewport_position=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop||0,l.$viewport_position=Math.round(l.$viewport_position)||0,a?i.main(l,!0):this.inter=setTimeout(function(){i.main(l)},this.timeout))},i},r=e({timeout:1e3,func:function(n,i){var a=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop||0,l=new Date,u=l-this.current_time;(u>s.para.heatmap.scroll_delay_time&&a-n.$viewport_position!=0||i)&&(n.$url=st(),n.$title=document.title,n.$url_path=se(),n.event_duration=Math.min(s.para.heatmap.scroll_event_duration,parseInt(u)/1e3),n.event_duration=n.event_duration<0?0:n.event_duration,Wa(n)),this.current_time=l}});r.current_time=new Date,oe(window,"scroll",function(){return!!t&&void r.go()}),oe(window,"beforeunload",function(){return!!t&&void r.go("notime")})},initHeatmap:function(){var t=this,e=!0;return!(!d(s.para.heatmap)||s.para.heatmap.clickmap!=="default")&&(S(s.para.heatmap.collect_url)&&this.listenUrlChange(function(){e=!!s.para.heatmap.collect_url()}),s.para.heatmap.collect_elements==="all"?s.para.heatmap.collect_elements="all":s.para.heatmap.collect_elements="interact",void(s.para.heatmap.collect_elements==="all"?oe(document,"click",function(r){if(!e)return!1;var n=r||window.event;if(!n)return!1;var i=n.target||n.srcElement;if(typeof i!="object"||typeof i.tagName!="string")return!1;var a=i.tagName.toLowerCase();if(a==="body"||a==="html"||!i||!i.parentNode||!i.parentNode.children)return!1;var l=h(i.parentNode.tagName)?i.parentNode.tagName.toLowerCase():"unknown";l==="a"||l==="button"?t.start(n,i.parentNode,l):t.start(n,i,a)}):oe(document,"click",function(r){if(!e)return!1;var n=r||window.event;if(!n)return!1;var i=n.target||n.srcElement,a=s.heatmap.getTargetElement(i,r);return!(!rt(a)&&!h(i.tagName))&&void(rt(a)&&h(a.tagName)?t.start(n,a,a.tagName.toLowerCase()):rt(i)&&i.tagName.toLowerCase()==="div"&&d(s.para.heatmap)&&s.para.heatmap.get_vtrack_config&&ar.events.length>0&&ar.isTargetEle(i)&&t.start(n,i,i.tagName.toLowerCase(),{$lib_method:"vtrack"}))})))}},sr={autoTrackIsUsed:!1,isReady:function(t){return S(t)?void t():void s.log("error: isReady callback must be function")},getUtm:function(){return L.campaignParams()},getStayTime:function(){return(new Date-s._t)/1e3},setProfileLocal:function(t){if(!E.isSupport())return s.setProfile(t),!1;if(!d(t)||it(t))return!1;var e=p.readObjectVal("sensorsdata_2015_jssdk_profile"),r=!1;if(d(e)&&!it(e)){for(var n in t)(!(n in e)||e[n]===t[n])&&n in e||(e[n]=t[n],r=!0);r&&(p.saveObjectVal("sensorsdata_2015_jssdk_profile",e),s.setProfile(t))}else p.saveObjectVal("sensorsdata_2015_jssdk_profile",t),s.setProfile(t)},setInitReferrer:function(){var t=Wt();s.setOnceProfile({_init_referrer:t,_init_referrer_host:L.pageProp.referrer_host})},setSessionReferrer:function(){var t=Wt();p.setSessionPropsOnce({_session_referrer:t,_session_referrer_host:L.pageProp.referrer_host})},setDefaultAttr:function(){L.register({_current_url:location.href,_referrer:Wt(),_referring_host:L.pageProp.referrer_host})},trackHeatMap:function(t,e,r){if(typeof t=="object"&&t.tagName&&rt(t.parentNode)){var n=t.tagName.toLowerCase(),i=t.parentNode.tagName.toLowerCase(),a=s.para.heatmap&&s.para.heatmap.track_attr?s.para.heatmap.track_attr:["data-sensors-click"];n==="button"||n==="a"||i==="a"||i==="button"||n==="input"||n==="textarea"||Pr(t,a)||St.start(null,t,n,e,r)}},trackAllHeatMap:function(t,e,r){if(typeof t=="object"&&t.tagName){var n=t.tagName.toLowerCase();St.start(null,t,n,e,r)}},autoTrackSinglePage:function(t,e){function r(a,l){s.track("$pageview",x({$referrer:n,$url:st(),$url_path:se(),$title:document.title},a,Dr()),l),n=st()}var n;n=this.autoTrackIsUsed?L.pageProp.url:L.pageProp.referrer;var i=!(t=d(t)?t:{}).not_set_profile;t.not_set_profile&&delete t.not_set_profile,r(t,e),this.autoTrackSinglePage=r,Wn(s.setOnceProfile,!1,i)},autoTrackWithoutProfile:function(t,e){t=d(t)?t:{},this.autoTrack(x(t,{not_set_profile:!0}),e)},autoTrack:function(t,e){t=d(t)?t:{};var r=Dr(),n=!t.not_set_profile;t.not_set_profile&&delete t.not_set_profile;var i=location.href;s.para.is_single_page&&fn(function(){var a=Wt(i,!0);s.track("$pageview",x({$referrer:a,$url:st(),$url_path:se(),$title:document.title},r,t),e),i=st()}),s.track("$pageview",x({$referrer:Wt(null,!0),$url:st(),$url_path:se(),$title:document.title},r,t),e),Wn(s.setOnceProfile,!0,n),this.autoTrackIsUsed=!0},getAnonymousID:function(){return it(p._state)?"SDK is not initialized.":p.getAnonymousId()},setPlugin:function(t){return!!d(t)&&void $(t,function(e,r){S(e)&&(d(window.SensorsDataWebJSSDKPlugin)&&window.SensorsDataWebJSSDKPlugin[r]?e(window.SensorsDataWebJSSDKPlugin[r]):d(s.modules)&&s.modules[r]?e(s.modules[r]):j(r+"is not found,please check sensorsdata documents."))})},useModulePlugin:function(){s.use.apply(s,arguments)},useAppPlugin:function(){this.setPlugin.apply(this,arguments)}},Ll={state:0,historyState:[],stateType:{1:"1-init未开始",2:"2-init开始",3:"3-store完成"},getState:function(){return this.historyState.join(`
`)},setState:function(t){String(t)in this.stateType&&(this.state=t),this.historyState.push(this.stateType[t])}},Ul=1,Rl={__proto__:null,setInitVar:Za,initPara:Ga,quick:Qa,use:Gn,track:Ya,bind:ts,unbind:es,trackLink:rs,trackLinks:ns,setItem:is,deleteItem:as,setProfile:ss,setOnceProfile:os,appendProfile:ls,incrementProfile:us,deleteProfile:cs,unsetProfile:ps,identify:ds,resetAnonymousIdentity:fs,trackSignup:gs,registerPage:_s,clearAllRegister:hs,clearPageRegister:ms,register:Qn,registerOnce:vs,registerSession:ys,registerSessionOnce:bs,login:Yn,loginWithKey:ws,logout:Ss,getPresetProperties:ks,readyState:Ll,debug:Zt,on:Fn,log:Dt};Q.setup(Dt);var Pi=x({},Dl,xl),Hl={bridge_info:{touch_app_bridge:!1,verify_success:!1,platform:"",support_two_way_call:!1},is_verify_success:!1,initPara:function(){var t={is_send:s.para.use_app_track_is_send!==!1&&s.para.use_app_track!=="only",white_list:[],is_mui:s.para.use_app_track==="mui"};typeof s.para.app_js_bridge=="object"?s.para.app_js_bridge=x({},t,s.para.app_js_bridge):s.para.use_app_track!==!0&&s.para.app_js_bridge!==!0&&s.para.use_app_track!=="only"&&s.para.use_app_track!=="mui"||(s.para.app_js_bridge=x({},t)),s.para.app_js_bridge.is_send===!1&&j("设置了 is_send:false,如果打通失败，数据将被丢弃!")},app_js_bridge_v1:function(){function t(a){ze(n=a)&&(n=JSON.parse(n)),i&&(i(n),i=null,n=null)}function e(){typeof window.SensorsData_APP_JS_Bridge=="object"&&window.SensorsData_APP_JS_Bridge.sensorsdata_call_app&&ze(n=window.SensorsData_APP_JS_Bridge.sensorsdata_call_app())&&(n=JSON.parse(n))}function r(){if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream){var a=document.createElement("iframe");a.setAttribute("src","sensorsanalytics://getAppInfo"),document.documentElement.appendChild(a),a.parentNode.removeChild(a),a=null}}var n=null,i=null;window.sensorsdata_app_js_bridge_call_js=function(a){t(a)},s.getAppStatus=function(a){return r(),e(),a?void(n===null?i=a:(a(n),n=null)):n}},hasVisualModeBridge:function(){var t=window.SensorsData_App_Visual_Bridge,e="sensorsdata_visualized_mode";return d(t)&&t[e]&&(t[e]===!0||t[e]())},validateAppUrl:Cs};De.prototype.call=function(t,e){var r=this,n=new Date().getTime().toString(16)+String(Z()).replace(".","").slice(1,8);this.resultCbs[n]=r.resultCbs[n]||{result:null,callbacks:[]},this.timeoutCbs[n]=r.timeoutCbs[n]||{isTimeout:!1,callbacks:[]},(t=t.data?t:{data:t}).data.message_id=n;var i=x({callType:this.type},t);return e&&(this.timerId=setTimeout(function(){for(var a in r.timeoutCbs[n].isTimeout=!0,r.timeoutCbs[n].callbacks)r.timeoutCbs[n].callbacks[a].call(null),r.timeoutCbs[n].callbacks.splice(a,1)},e)),ti(i),{onResult:function(a){return r.resultCbs[n].result?(a(r.resultCbs[n].result),this):(!r.timeoutCbs[n].isTimeout&&r.resultCbs[n].callbacks.push(a),this)},onTimeout:function(a){return r.timeoutCbs[n].isTimeout?(a(),this):(!r.resultCbs[n].result&&r.timeoutCbs[n].callbacks.push(a),this)}}},De.prototype.onAppNotify=function(t){this.appCallJsCallback=t},De.prototype.notifyApp=function(t,e){var r=x({callType:this.type},t);return e&&(r.message_id=e),ti(r)},ei.prototype={double:function(){},getAppData:function(){},hasAppBridge:function(){return s.bridge.bridge_info.support_two_way_call},init:function(){},jsCallApp:function(){},requestToApp:function(t){this.bridge.call(t,t.timeout.time).onResult(function(e){S(t.callback)&&t.callback(e)}).onTimeout(function(){S(t.timeout.callback)&&t.timeout.callback()})}};var Qt={isSeachHasKeyword:function(){return Mt(location.href,"sa-request-id")!==""&&(typeof sessionStorage.getItem("sensors-visual-mode")=="string"&&sessionStorage.removeItem("sensors-visual-mode"),!0)},hasKeywordHandle:function(){var t=location.href,e=Mt(t,"sa-request-id")||null,r=Mt(t,"sa-request-type")||null,n=Mt(t,"sa-request-url")||null;if(St.setNotice(n),be.isSupport())if(n!==null&&sessionStorage.setItem("sensors_heatmap_url",n),sessionStorage.setItem("sensors_heatmap_id",e),r!==null)r==="1"||r==="2"||r==="3"?sessionStorage.setItem("sensors_heatmap_type",r):r=null;else{var i=sessionStorage.getItem("sensors_heatmap_type");r=i!==null?i:null}this.isReady(e,r)},isReady:function(t,e,r){s.para.heatmap_url?We({success:function(){setTimeout(function(){typeof sa_jssdk_heatmap_render<"u"&&(sa_jssdk_heatmap_render(s,t,e,r),typeof console=="object"&&typeof console.log=="function"&&(s.heatmap_version&&s.heatmap_version===s.lib_version||j("heatmap.js与sensorsdata.js版本号不一致，可能存在风险!")))},0)},error:function(){},type:"js",url:s.para.heatmap_url}):D("没有指定heatmap_url的路径")},isStorageHasKeyword:function(){return be.isSupport()&&typeof sessionStorage.getItem("sensors_heatmap_id")=="string"},storageHasKeywordHandle:function(){St.setNotice(),Qt.isReady(sessionStorage.getItem("sensors_heatmap_id"),sessionStorage.getItem("sensors_heatmap_type"),location.href)},isWindowNameHasKeyword:function(){try{var t=JSON.parse(window.name),e=h(t["sa-request-page-url"])?Ht(t["sa-request-page-url"]):null;return t["sa-request-id"]&&h(t["sa-request-id"])&&e===location.href}catch{return!1}},windowNameHasKeywordHandle:function(){function t(a){var l=e[a];return h(l)?Ht(l):null}var e=JSON.parse(window.name),r=t("sa-request-id"),n=t("sa-request-type"),i=t("sa-request-url");St.setNotice(i),be.isSupport()&&(i!==null&&sessionStorage.setItem("sensors_heatmap_url",i),sessionStorage.setItem("sensors_heatmap_id",r),n!==null?n==="1"||n==="2"||n==="3"?sessionStorage.setItem("sensors_heatmap_type",n):n=null:n=sessionStorage.getItem("sensors_heatmap_type")!==null?sessionStorage.getItem("sensors_heatmap_type"):null),Qt.isReady(r,n)}},Ct={isStorageHasKeyword:function(){return be.isSupport()&&typeof sessionStorage.getItem("sensors-visual-mode")=="string"},isSearchHasKeyword:function(){return(ri("sa-visual-mode")===!0||ri("sa-visual-mode")==="true")&&(typeof sessionStorage.getItem("sensors_heatmap_id")=="string"&&sessionStorage.removeItem("sensors_heatmap_id"),!0)},loadVtrack:function(){We({success:function(){},error:function(){},type:"js",url:s.para.vtrack_url?s.para.vtrack_url:zn()+"//static.sensorsdata.cn/sdk/"+s.lib_version+"/vtrack.min.js"})},messageListener:function(t){if(!t||!t.data||t.data.source!=="sa-fe")return!1;if(t.data.type==="v-track-mode"){if(t.data.data&&t.data.data.isVtrack)if(be.isSupport()&&sessionStorage.setItem("sensors-visual-mode","true"),t.data.data.userURL&&location.href.match(/sa-visual-mode=true/)){var e=t.data.data.userURL;h(e)&&(jr(e,"http://")||jr(e,"https://"))&&(window.location.href=encodeURI(e))}else Ct.loadVtrack();window.removeEventListener("message",Ct.messageListener,!1)}},removeMessageHandle:function(){window.removeEventListener&&window.removeEventListener("message",Ct.messageListener,!1)},verifyVtrackMode:function(){window.addEventListener&&window.addEventListener("message",Ct.messageListener,!1),Ct.postMessage()},postMessage:function(){try{window.parent&&window.parent.postMessage&&window.parent.postMessage({source:"sa-web-sdk",type:"v-is-vtrack",data:{sdkversion:rr}},"*")}catch{D("浏览器版本过低，不支持 postMessage API")}},notifyUser:function(){var t=function(e){return!(!e||!e.data||e.data.source!=="sa-fe")&&void(e.data.type==="v-track-mode"&&(e.data.data&&e.data.data.isVtrack&&alert("当前版本不支持，请升级部署神策数据治理"),window.removeEventListener("message",t,!1)))};window.addEventListener&&window.addEventListener("message",t,!1),Ct.postMessage()}},Jl=["setItem","deleteItem","getAppStatus","track","quick","register","registerPage","registerOnce","trackSignup","setProfile","setOnceProfile","appendProfile","incrementProfile","deleteProfile","unsetProfile","identify","resetAnonymousIdentity","login","logout","trackLink","clearAllRegister","clearPageRegister","bind","unbind","loginWithKey"],Ml={track:function(t,e,r){},quick:function(t,e,r,n){},register:function(t){},registerPage:function(t){},registerOnce:function(t){},clearAllRegister:function(t){},trackSignup:function(t,e,r,n){},setProfile:function(t,e){},setOnceProfile:function(t,e){},appendProfile:function(t,e){},incrementProfile:function(t,e){},deleteProfile:function(t){},unsetProfile:function(t,e){},identify:function(t,e){},resetAnonymousIdentity:function(t){},login:function(t,e){},logout:function(t){},trackLink:function(t,e,r){},deleteItem:function(t,e){},setItem:function(t,e,r){},getAppStatus:function(t){},clearPageRegister:function(t){}};le.prototype.process=function(t,e){if(t&&t in this.processDef){var r=this.registeredInterceptors[t];if(r&&A(r)&&r.length>0)for(var n={current:0,total:r.length},i=new xs(e,n,s),a=0;a<r.length;a++)try{if(n.current=a+1,e=r[a].call(null,e,i)||e,i.cancellationToken.getCanceled())break;if(i.cancellationToken.getStopped())return}catch(l){D("interceptor error:"+l)}return this.processDef[t]&&this.processDef[t]in this.processDef&&(e=this.process(this.processDef[t],e)),e}D("process ["+t+"] is not supported")},le.prototype.registerStageImplementation=function(t){t&&t.init&&S(t.init)&&(t.init(this),t.interceptor&&this.registerInterceptor(t.interceptor))},le.prototype.registerInterceptor=function(t){if(t)for(var e in t){var r=t[e];if(r&&d(r)&&S(r.entry)){$t(r.priority)||(r.priority=Number.MAX_VALUE),this.registeredInterceptors[e]||(this.registeredInterceptors[e]=[]);var n=this.registeredInterceptors[e];r.entry.priority=r.priority,n.push(r.entry),n.sort(function(i,a){return i.priority-a.priority})}}};var Ci=new le({basicProps:"extendProps",extendProps:"formatData",formatData:"finalAdjustData",finalAdjustData:null}),Oi=new le({send:null}),ji=new le({getUtmData:null,callSchema:null}),Ni=new le({webClickEvent:null,webStayEvent:null}),Ii={buildDataStage:function(t){t&&Ci.registerInterceptor(t)},businessStage:function(t){t&&ji.registerInterceptor(t)},sendDataStage:function(t){t&&Oi.registerInterceptor(t)},viewStage:function(t){t&&Ni.registerInterceptor(t)}},Wr={stage:null,init:function(t){this.stage=t}},Ti={stage:null,init:function(t){this.stage=t},interceptor:{send:{entry:function(t){return t}}}},Bl={buildData:function(t){return Va(t)},sendData:function(t,e){var r=Rn(t.properties);Es({origin_data:t,server_url:s.para.server_url,data:t,config:r||{},callback:e}),s.log(t)},encodeTrackData:function(t){return Bn(t)},getUtmData:function(){return As()}},Fl={webClickEvent:{entry:function(t,e){var r=e.sensors;t.tagName==="a"&&r.para.heatmap&&r.para.heatmap.isTrackLink===!0?r.trackLink({event:t.event,target:t.target},"$WebClick",t.props):r.track("$WebClick",t.props,t.callback)}},webStayEvent:{entry:function(t,e){e.sensors.track("$WebStay",t)}}},$i=window.sensors_data_pre_config,Di=!!Pi.isObject($i)&&$i.is_compliance_enabled;s.init=function(t){return q.sdk.emit("beforeInit"),!(s.readyState&&s.readyState.state&&s.readyState.state>=2)&&(Di&&(Lr(!0),ii()),q.initSystemEvent(),s.setInitVar(),s.readyState.setState(2),s.initPara(t),q.sdk.emit("initPara"),q.sdk.emit("afterInitPara"),q.sdk.emit("initAPI"),q.sdk.emit("afterInitAPI"),s.detectMode(),Ps(),q.sdk.emit("afterInit"),void q.sdk.emit("ready"))},Di?Lr(!1):(Lr(!0),ii());var Ot,or,Ue,J,Re,pe,ft,we,de,He,Y,fe,kt,gt,_t,Xr,Zr,Je,Kl="1.26.16",Vl={init:function(t){var e=t._.isString,r=t._.rot13defs,n=t._.dfmapping,i="data:enc;",a="dfm-enc-";t.ee.sdk.on("afterInitPara",function(){t.kit.userEncrypt=function(l){return a+n(l)},t.kit.userDecrypt=function(l){return l.indexOf(i)===0?(l=l.substring(i.length),l=r(l)):l.indexOf(a)===0&&(l=l.substring(a.length),l=n(l)),l},t.kit.userDecryptIfNeeded=function(l){return!e(l)||l.indexOf(i)!==0&&l.indexOf(a)!==0||(l=t.kit.userDecrypt(l)),l}})},plugin_name:"UserEncryptDefault"},ql=Hs(Vl),zl="1.26.16",Wl={sd:null,init:function(t){if(this.sd||(this.sd=t,!this.sd||!this.sd._))return!1;var e=this.sd._.cookie.get("sensors_amp_id"),r=this.sd.store._state.distinct_id;if(e&&e.length>0){var n=e.slice(0,4)==="amp-";if(e!==r){if(!n)return!1;this.sd.store._state.first_id?(this.sd.identify(e,!0),this.sd.saEvent.send({original_id:e,distinct_id:r,type:"track_signup",event:"$SignUp",properties:{}},null),this.setAmpId(r)):this.sd.identify(e,!0)}}else this.setAmpId(r);this.addListener()},addListener:function(){var t=this;this.sd.events.on("changeDistinctId",function(e){t.setAmpId(e)}),this.sd.events.isReady()},setAmpId:function(t){this.sd._.cookie.set("sensors_amp_id",t)}},Xl=Bs(Wl,"Amp","sdkReady"),xt=window.SensorsData_App_Visual_Bridge,lr=xt&&xt.sensorsdata_visualized_mode,xi=xt&&xt.sensorsdata_visualized_alert_info,Ai=xt&&xt.sensorsdata_hover_web_nodes,Ei={isVerify:function(){return lr&&(lr===!0||lr.call(xt))},commands:{app_alert:Fs,visualized_track:ai,page_info:ai,sensorsdata_get_app_visual_config:Ks}},Zl="1.26.16",Li={init:function(t){Re=(J=t)&&J._,pe=J&&J.log||console&&console.log||function(){},Ws()},handleCommand:Zs},Ui=zs(Li,"AndroidBridge","sdkAfterInitPara"),At=window.SensorsData_App_Visual_Bridge,ur=At&&At.sensorsdata_visualized_mode,Ri=At&&At.sensorsdata_visualized_alert_info,Hi=At&&At.sensorsdata_hover_web_nodes,Gr={isVerify:function(){return ur&&(ur===!0||ur.call(At))},commands:{app_alert:Gs,visualized_track:si,page_info:si,sensorsdata_get_app_visual_config:Qs}},Gl="1.26.16",Ji={init:function(t){fe=(Y=t)&&Y._,kt=Y&&Y.log||console&&console.log||function(){},ro()},handleCommand:io},Mi=eo(Ji,"AndroidObsoleteBridge","sdkAfterInitPara"),Ql="1.26.16",yt={event_list:[],latest_event_initial_time:null,max_save_time:2592e6,init:function(t,e){function r(){return gt=_t._,Xr=_t.store,!!gt.localStorage.isSupport()&&(_t.para.max_string_length=1024,n.eventList.init(),n.addLatestChannelUrl(),void n.addIsChannelCallbackEvent())}if(_t||!t)return!1;Zr=(e=e||{}).cookie_name||"sensorsdata2015jssdkchannel",_t=t;var n=this;r()},addIsChannelCallbackEvent:function(){_t.registerPage({$is_channel_callback_event:function(t){if(gt.isObject(t)&&t.event&&t.event!=="$WebClick"&&t.event!=="$pageview"&&t.event!=="$WebStay"&&t.event!=="$SignUp")return!yt.eventList.hasEvent(t.event)&&(yt.eventList.add(t.event),!0)}})},addLatestChannelUrl:function(){var t=this.getUrlDomain(),e=this.cookie.getChannel();if(t==="url解析失败")this.registerAndSave({_sa_channel_landing_url:"",_sa_channel_landing_url_error:"url的domain解析失败"});else if(gt.isReferralTraffic(document.referrer)){var r=gt.getQueryParam(location.href,"sat_cf");gt.isString(r)&&r.length>0?(this.registerAndSave({_sa_channel_landing_url:location.href}),yt.channelLinkHandler()):this.registerAndSave({_sa_channel_landing_url:""})}else e?_t.registerPage(e):_t.registerPage({_sa_channel_landing_url:"",_sa_channel_landing_url_error:"取值异常"})},registerAndSave:function(t){_t.registerPage(t),this.cookie.saveChannel(t)},cookie:{getChannel:function(){var t=_t.kit.userDecryptIfNeeded(gt.cookie.get(Zr));return t=gt.safeJSONParse(t),!(!gt.isObject(t)||!t.prop)&&t.prop},saveChannel:function(t){var e={prop:t},r=JSON.stringify(e);_t.para.encrypt_cookie&&(r=_t.kit.userEncrypt(r)),gt.cookie.set(Zr,r)}},channelLinkHandler:function(){this.eventList.reset(),_t.track("$ChannelLinkReaching")},getUrlDomain:function(){var t=gt.info.pageProp.url_domain;return t===""&&(t="url解析失败"),t},eventList:{init:function(){var t=this.get(),e=new Date().getTime();if(t&&gt.isNumber(t.latest_event_initial_time)&&gt.isArray(t.eventList)){var r=e-t.latest_event_initial_time;r>0&&r<yt.max_save_time?(yt.event_list=t.eventList,yt.latest_event_initial_time=t.latest_event_initial_time):this.reset()}else this.reset()},get:function(){var t={};try{t=Xr.readObjectVal("sawebjssdkchannel")}catch(e){_t.log(e)}return t},add:function(t){yt.event_list.push(t),this.save()},save:function(){var t={latest_event_initial_time:yt.latest_event_initial_time,eventList:yt.event_list};Xr.saveObjectVal("sawebjssdkchannel",t)},reset:function(){yt.event_list=[],yt.latest_event_initial_time=new Date().getTime(),this.save()},hasEvent:function(t){var e=!1;return gt.each(yt.event_list,function(r){r===t&&(e=!0)}),e}}},Yl=oo(yt,"SensorsChannel","sdkAfterInitAPI"),tu="1.26.16",Bi=function(){var t={};return document.hidden!==void 0?(t.hidden="hidden",t.visibilityChange="visibilitychange"):document.msHidden!==void 0?(t.hidden="msHidden",t.visibilityChange="msvisibilitychange"):document.webkitHidden!==void 0&&(t.hidden="webkitHidden",t.visibilityChange="webkitvisibilitychange"),t};Je=Bi().hidden;var Qr,ge,z,Me,Yr,tt,Se,tn,en={android:/Android/i,iOS:/iPhone|iPad|iPod/i},eu=function(){for(var t in en)if(navigator.userAgent.match(en[t]))return t;return""},Fi=eu(),ru=function(){return en.hasOwnProperty(Fi)},cr=function(t){return t!=null&&Object.prototype.toString.call(t)=="[object Object]"},nu=function(t){var e=/\/sd\/(\w+)\/(\w+)$/;return t.match(e)},iu=function(t){var e=t._.URL(t.para.server_url);return{origin:e.origin,project:e.searchParams.get("project")||"default"}},au=function(t,e,r){t.log("尝试唤起 android app");var n=e;t.log("唤起APP的地址："+n),window.location=n,t.timer=setTimeout(function(){var i=oi();return t.log("hide:"+Je+":"+document[Je]),i?(t.log("The page is hidden, stop navigating to download page"),!1):(t.log("App可能未安装，跳转到下载地址"),void(window.location=r))},t.timeout)},su=function(t,e,r){t.log("尝试唤起 iOS app:"+e),window.location.href=e,t.timer=setTimeout(function(){return oi()?(t.log("The page is hidden, stop navigating to download page"),!1):(t.log("App可能未安装，跳转到下载地址"),void(window.location.href=r))},t.timeout),t.log("new timer:"+t.timer)},Be={plugin_name:["deeplink","Deeplink"],key:null,timer:null,sd:null,data:null,timeout:2500,apiURL:"{origin}/sdk/deeplink/param?key={key}&system_type=JS&project={project}",init:function(t){if(this.sd)return this.logger("deeplink已经初始化"),!1;arguments[0]?cr(arguments[0])&&typeof arguments[0].timeout=="number"?this.sd=window.sensorsDataAnalytic201505:this.sd=t:this.sd=window.sensorsDataAnalytic201505;var e={};if(arguments.length>0&&(arguments.length===1&&cr(arguments[0])?e=arguments[0]:arguments.length>=2&&cr(arguments[1])&&(e=arguments[1])),!ru())return this.logger("不支持当前系统，目前只支持Android和iOS"),!1;if(cr(e)&&this.sd._.isNumber(e.timeout)&&e.timeout>=2500&&(this.timeout=e.timeout),!this.sd.para.server_url)return this.logger("神策JS SDK配置项server_url未正确配置"),!1;var r=iu(this.sd);this.apiURL=this.apiURL.replace("{origin}",r.origin).replace("{project}",r.project);var n=this.sd._.getQueryParam(window.location.href,"deeplink");if(!n)return this.logger("当前页面缺少deeplink参数"),!1;n=window.decodeURIComponent(n);var i=nu(n);return i?(this.key=i[2],this.apiURL=this.apiURL.replace("{key}",window.encodeURIComponent(i[2])),this.sd._.ajax({url:this.apiURL,type:"GET",cors:!0,credentials:!1,success:(function(a){return a.errorMsg?(Be.logger("API报错："+a.errorMsg),!1):(Be.data=a,Be.logger("API查询成功，数据："+JSON.stringify(a,null,"  ")),void(this.data.app_key&&(this.data.android_info&&this.data.android_info.url_schemes&&(this.data.android_info.url_schemes+="://sensorsdata/sd/"+this.data.app_key+"/"+this.key),this.data.ios_info&&this.data.ios_info.url_schemes&&(this.data.ios_info.url_schemes+="://sensorsdata/sd/"+this.data.app_key+"/"+this.key))))}).bind(this),error:function(){Be.logger("API查询出错")}}),void this.addListeners()):(this.logger("当前页面的deeplink参数无效"),!1)},openDeepLink:function(){if(this.logger("openDeeplink()"),!this.data)return this.logger("没有Deep link数据!"),!1;if(Fi==="iOS"){this.logger("当前系统是iOS");var t=this.sd&&this.sd._&&this.sd._.getIOSVersion()>=9&&this.data.ios_info.ios_wake_url?this.data.ios_info.ios_wake_url:this.data.ios_info.url_schemes;this.logger("唤起APP的地址："+t),su(this,t,this.data.ios_info.download_url)}else this.logger("当前系统是 android"),au(this,this.data.android_info.url_schemes,this.data.android_info.download_url)},logger:function(t){this.sd&&this.sd.log(t)},addListeners:function(){var t=Bi().visibilityChange,e=this;t&&document.addEventListener(t,function(){clearTimeout(e.timer),e.logger("visibilitychange, clear timeout:"+e.timer)},!1),window.addEventListener("pagehide",function(){e.logger("page hide, clear timeout:"+e.timer),clearTimeout(e.timer)},!1)}},ou=co(Be,null,"sdkReady"),lu="1.26.16",Ki={init:function(t){Me=(z=t)&&z._,Yr=z&&z.log||console&&console.log||function(){},_o()},handleCommand:mo},Vi=go(Ki,"IOSBridge","sdkAfterInitPara"),uu="1.26.16",qi={init:function(t){Se=(tt=t)&&tt._,tn=tt&&tt.log||console&&console.log||function(){},wo()}},zi=bo(qi,"IOSObsoleteBridge","sdkAfterInitPara"),cu="1.26.16",pu=5e3,du=432e3;X.prototype.init=function(t,e){if(t){if(this.sd=t,this._=this.sd._,e){this.option=e;var r=e.heartbeat_interval_time;r&&(this._.isNumber(r)||this._.isNumber(1*r))&&1*r>0&&(this.heartbeat_interval_time=1e3*r);var n=e.max_duration;n&&(this._.isNumber(n)||this._.isNumber(1*n))&&1*n>0&&(this.max_duration=n)}this.page_id=Number(String(this._.getRandom()).slice(2,5)+String(this._.getRandom()).slice(2,4)+String(new Date().getTime()).slice(-4)),this.addEventListener(),document.hidden===!0?this.page_show_status=!1:this.addHeartBeatInterval(),this.log("PageLeave初始化完毕")}else this.log("神策JS SDK未成功引入")},X.prototype.log=function(t){this.sd&&this.sd.log(t)},X.prototype.refreshPageEndTimer=function(){var t=this;this.timer&&(clearTimeout(this.timer),this.timer=null),this.timer=setTimeout(function(){t.page_hidden_status=!1},pu)},X.prototype.hiddenStatusHandler=function(){clearTimeout(this.timer),this.timer=null,this.page_hidden_status=!1},X.prototype.pageStartHandler=function(){this.start_time=+new Date,!document.hidden==1?this.page_show_status=!0:this.page_show_status=!1,this.url=location.href,this.title=document.title},X.prototype.pageEndHandler=function(){if(this.page_hidden_status!==!0){var t=this.getPageLeaveProperties();this.page_show_status===!1&&delete t.event_duration,this.page_show_status=!1,this.page_hidden_status=!0,this.isCollectUrl(this.url)&&this.sd.track("$WebPageLeave",t),this.refreshPageEndTimer(),this.delHeartBeatData()}},X.prototype.addEventListener=function(){this.addPageStartListener(),this.addPageSwitchListener(),this.addSinglePageListener(),this.addPageEndListener()},X.prototype.addPageStartListener=function(){var t=this;"onpageshow"in window&&this._.addEvent(window,"pageshow",function(){t.pageStartHandler(),t.hiddenStatusHandler()})},X.prototype.isCollectUrl=function(t){return typeof this.option.isCollectUrl!="function"||typeof t!="string"||t===""||this.option.isCollectUrl(t)},X.prototype.addSinglePageListener=function(){var t=this;this.sd.ee&&this.sd.ee.spa.prepend("switch",function(e){e!==location.href&&(t.url=e,t.pageEndHandler(),t.stopHeartBeatInterval(),t.current_page_url=t.url,t.pageStartHandler(),t.hiddenStatusHandler(),t.addHeartBeatInterval())})},X.prototype.addPageEndListener=function(){var t=this;this._.each(["pagehide","beforeunload","unload"],function(e){"on"+e in window&&t._.addEvent(window,e,function(){t.pageEndHandler(),t.stopHeartBeatInterval()})})},X.prototype.addPageSwitchListener=function(){var t=this;this._.listenPageState({visible:function(){t.pageStartHandler(),t.hiddenStatusHandler(),t.addHeartBeatInterval()},hidden:function(){t.url=location.href,t.title=document.title,t.pageEndHandler(),t.stopHeartBeatInterval()}})},X.prototype.addHeartBeatInterval=function(){this._.localStorage.isSupport()&&this.startHeartBeatInterval()},X.prototype.startHeartBeatInterval=function(){var t=this;this.heartbeat_interval_timer&&this.stopHeartBeatInterval();var e=!0;this.isCollectUrl(this.url)||(e=!1),this.heartbeat_interval_timer=setInterval(function(){e&&t.saveHeartBeatData()},this.heartbeat_interval_time),e&&this.saveHeartBeatData("is_first_heartbeat"),this.reissueHeartBeatData()},X.prototype.stopHeartBeatInterval=function(){clearInterval(this.heartbeat_interval_timer),this.heartbeat_interval_timer=null},X.prototype.saveHeartBeatData=function(t){var e=this.getPageLeaveProperties(),r=new Date;e.$time=r,t==="is_first_heartbeat"&&(e.event_duration=3.14);var n=this.sd.kit.buildData({type:"track",event:"$WebPageLeave",properties:e});n.heartbeat_interval_time=this.heartbeat_interval_time,this.sd.store.saveObjectVal(this.storage_name+"-"+this.page_id,n)},X.prototype.delHeartBeatData=function(t){this._.localStorage.isSupport()&&this._.localStorage.remove(t||this.storage_name+"-"+this.page_id)},X.prototype.reissueHeartBeatData=function(){for(var t=window.localStorage.length-1;t>=0;t--){var e=window.localStorage.key(t);if(e&&e!==this.storage_name+"-"+this.page_id&&e.indexOf(this.storage_name+"-")===0){var r=this.sd.store.readObjectVal(e);this._.isObject(r)&&1*new Date-r.time>r.heartbeat_interval_time+5e3&&(delete r.heartbeat_interval_time,r._flush_time=new Date().getTime(),this.sd.kit.sendData(r),this.delHeartBeatData(e))}}},X.prototype.getPageLeaveProperties=function(){var t=(+new Date-this.start_time)/1e3;(isNaN(t)||t<0||t>this.max_duration)&&(t=0),t=Number(t.toFixed(3));var e=this._.getReferrer(this.current_page_url),r=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body&&document.body.scrollTop||0;r=Math.round(r)||0;var n={$title:this.title,$url:this._.getURL(this.url),$url_path:this._.getURLPath(this._.URL(this.url).pathname),$referrer_host:e?this._.getHostname(e):"",$referrer:e,$viewport_position:r};return t!==0&&(n.event_duration=t),this._.extend(n,this.option.custom_props)};var fu=jo(new X,"PageLeave","sdkReady"),gu="1.26.16",Wi=!1,_u={init:function(t,e){function r(l,u){if(l.getEntries&&typeof l.getEntries=="function"){for(var c=l.getEntries(),f=null,_=0;_<c.length;_++)"transferSize"in c[_]&&(f+=c[_].transferSize);t._.isNumber(f)&&f>=0&&f<10737418240&&(u.$page_resource_size=Number((f/1024).toFixed(3)))}}function n(l){var u=0;if(l.timing){var c=l.timing;c.fetchStart!==0&&t._.isNumber(c.fetchStart)&&c.domContentLoadedEventEnd!==0&&t._.isNumber(c.domContentLoadedEventEnd)?u=c.domContentLoadedEventEnd-c.fetchStart:t.log("performance 数据获取异常")}return u}function i(l){var u=0;return t._.isFunction(l.getEntriesByType)&&(u=((l.getEntriesByType("navigation")||[{}])[0]||{}).domContentLoadedEventEnd||0),u}function a(){var l=0,u=window.performance||window.webkitPerformance||window.msPerformance||window.mozPerformance,c={$url:t._.getURL(),$title:document.title,$url_path:t._.getURLPath(),$referrer:t._.getReferrer(null,!0)};if(u?(l=i(u)||n(u),r(u,c)):t.log("浏览器未支持 performance API."),l>0){var f=t._.isObject(e)&&e.max_duration||1800;l=Number((l/1e3).toFixed(3)),(!t._.isNumber(f)||f<=0||l<=f)&&(c.event_duration=l)}Wi||(t.track("$WebPageLoad",c),Wi=!0),window.removeEventListener?window.removeEventListener("load",a):window.detachEvent&&window.detachEvent("onload",a)}document.readyState=="complete"?a():window.addEventListener?window.addEventListener("load",a):window.attachEvent&&window.attachEvent("onload",a)}},hu=To(_u,"PageLoad","sdkReady");xe.prototype.init=function(t){if(t){this.sd=t,this._=t._,this.log=t.log;var e=this;t.registerInterceptor("buildDataStage",{extendProps:{priority:0,entry:function(r){return $o(r,e)}}})}else this.log("神策JS SDK未成功引入")},xe.prototype.register=function(t){return this.sd?void(this._.isObject(t)&&this._.isArray(t.events)&&t.events.length>0&&this._.isObject(t.properties)&&!this._.isEmptyObject(t.properties)?this.customRegister.push(t):this.log("RegisterProperties: register 参数错误")):void this.log("神策JS SDK未成功引入")},xe.prototype.hookRegister=function(t){return this.sd?void(this._.isFunction(t)?this.customRegister.push(t):this.log("RegisterProperties: hookRegister 参数错误")):void this.log("神策JS SDK未成功引入")};var mu="1.26.16";xe.prototype.plugin_name="RegisterProperties";var ke,Xi,Bt,B,_e,ut,he,ct,Yt,Ft,te,Kt,ee,jt,Pe,vu=Do(new xe),yu="1.26.16",Fe=window.console&&window.console.log||function(){},bu={init:function(t){return Fe=(ke=t)&&ke.log||Fe,t&&t.kit&&t.kit.buildData?(Xi=ke.kit.buildData,ke.kit.buildData=Lo,void Fe("RegisterPropertyPageHeight 插件初始化完成")):void Fe("RegisterPropertyPageHeight 插件初始化失败,当前主sdk不支持 RegisterPropertyPageHeight 插件，请升级主sdk")}},wu=Eo(bu,"RegisterPropertyPageHeight","sdkReady"),Su="1.26.16",ku={getPart:function(t){var e=!1,r=this.option.length;if(r){for(var n=0;n<r;n++)if(t.indexOf(this.option[n].part_url)>-1)return!0}return e},getPartHash:function(t){var e=this.option.length,r=!1;if(e){for(var n=0;n<e;n++)if(t.indexOf(this.option[n].part_url)>-1)return this.option[n].after_hash}return!!r},getCurrenId:function(){var t=this.store.getDistinctId()||"",e=this.store.getFirstId()||"";return this._.urlSafeBase64&&this._.urlSafeBase64.encode?t=t?this._.urlSafeBase64.trim(this._.urlSafeBase64.encode(this._.base64Encode(t))):"":this._.rot13obfs&&(t=t?this._.rot13obfs(t):""),encodeURIComponent(e?"f"+t:"d"+t)},rewriteUrl:function(t,e){var r=this,n=/([^?#]+)(\?[^#]*)?(#.*)?/.exec(t),i="";if(n){var a,l=n[1]||"",u=n[2]||"",c=n[3]||"",f="_sasdk="+this.getCurrenId(),_=function(T){var I=T.split("&"),F=[];return r._.each(I,function(Et){Et.indexOf("_sasdk=")>-1?F.push(f):F.push(Et)}),F.join("&")};return this.getPartHash(t)?(a=c.indexOf("_sasdk"),i=c.indexOf("?")>-1?a>-1?l+u+"#"+c.substring(1,a)+_(c.substring(a,c.length)):l+u+c+"&"+f:l+u+"#"+c.substring(1)+"?"+f):(a=u.indexOf("_sasdk"),i=/^\?(\w)+/.test(u)?a>-1?l+"?"+_(u.substring(1))+c:l+u+"&"+f+c:l+"?"+f+c),e&&(e.href=i),i}},getUrlId:function(){var t=location.href.match(/_sasdk=([aufd][^\?\#\&\=]+)/);if(this._.isArray(t)&&t[1]){var e=decodeURIComponent(t[1]);return!e||e.substring(0,1)!=="f"&&e.substring(0,1)!=="d"||(this._.urlSafeBase64&&this._.urlSafeBase64.isUrlSafeBase64&&this._.urlSafeBase64.isUrlSafeBase64(e)?e=e.substring(0,1)+this._.base64Decode(this._.urlSafeBase64.decode(e.substring(1))):this._.rot13defs&&(e=e.substring(0,1)+this._.rot13defs(e.substring(1)))),e}return""},setRefferId:function(t){var e=this.store.getDistinctId(),r=this.getUrlId();if(r&&r!==""){var n=r.substring(0,1)==="a"||r.substring(0,1)==="d";(r=r.substring(1))!==e&&(n?(this.sd.identify(r,!0),this.store.getFirstId()&&this.sd.saEvent.send({original_id:r,distinct_id:e,type:"track_signup",event:"$SignUp",properties:{}},null)):this.store.getFirstId()&&!t.re_login||this.sd.login(r))}},addListen:function(){var t=this,e=function(r){var n,i,a=r.target,l=a.tagName.toLowerCase(),u=a.parentNode;if(l==="a"&&a.href||u&&u.tagName&&u.tagName.toLowerCase()==="a"&&u.href){l==="a"&&a.href?(n=a.href,i=a):(n=u.href,i=u);var c=t._.URL(n).protocol;c!=="http:"&&c!=="https:"||t.getPart(n)&&t.rewriteUrl(n,i)}};t._.addEvent(document,"mousedown",e),window.PointerEvent&&"maxTouchPoints"in window.navigator&&window.navigator.maxTouchPoints>=0&&t._.addEvent(document,"pointerdown",e)},init:function(t,e){function r(n){for(var i=n.length,a=[],l=0;l<i;l++)/[A-Za-z0-9]+\./.test(n[l].part_url)&&Object.prototype.toString.call(n[l].after_hash)=="[object Boolean]"?a.push(n[l]):t.log("linker 配置的第 "+(l+1)+" 项格式不正确，请检查参数格式！");return a}return this.sd=t,this._=t._,this.store=t.store,this.para=t.para,this._.isObject(e)&&this._.isArray(e.linker)&&e.linker.length>0?(this.setRefferId(e),this.addListen(),this.option=e.linker,void(this.option=r(this.option))):void t.log("请配置打通域名参数！")}},Pu=Ho(ku,"SiteLinker","sdkReady"),Cu="utm_source utm_medium utm_campaign utm_content utm_term",Ou="1.26.16",ju={init:function(t){function e(){var r=Cu.split(" "),n="",i={};return Bt._.isArray(Bt.para.source_channel)&&Bt.para.source_channel.length>0&&(r=r.concat(Bt.para.source_channel),r=Bt._.unique(r)),Bt._.each(r,function(a){(n=Bt._.getQueryParam(location.href,a)).length&&(i[a]=n)}),i}t&&!Bt&&(Bt=t).registerInterceptor("businessStage",{getUtmData:{priority:0,entry:function(){return e()}}})}},Zi=Bo(ju,"Utm","sdkAfterInitPara"),Nu="1.26.16",rn=!1,nn=null,Iu={init:function(t){(nn=t).disableSDK=qo,nn.enableSDK=zo,nn.getDisabled=Wo}},Gi=Vo(Iu,"DisableSDK","sdkInitAPI"),Tu="1.26.16",$u={plugin_name:"DebugSender",init:function(t){_e=(B=t)._,Yo()}},Qi=Xo($u),Du="1.26.16",xu={plugin_name:"JsappSender",init:function(t){he=(ut=t)._,rl()}},Yi=tl(xu),Au="1.26.16",pr=null,Eu={plugin_name:"BatchSender",init:function(t){Yt=(ct=t)._,ol()}},ta=il(Eu),Lu="1.26.16",Uu={plugin_name:"BeaconSender",init:function(t){te=(Ft=t)._,pl()}},ea=ll(Uu),Ru="1.26.16",Hu={plugin_name:"AjaxSender",init:function(t){ee=(Kt=t)._,_l()}},ra=dl(Hu),Ju="1.26.16",Mu={plugin_name:"ImageSender",init:function(t){Pe=(jt=t)._,yl()}},na=hl(Mu),at=null,ht=null,Ke=[],Bu={init:function(t){t&&(ht=(at=t)._,at.logger&&at.logger.appendWriter(Sl),at.on&&at.on("sdkAfterInitPara",function(){for(var e=0;e<Ke.length;e++)di(Ke[e]);Ke=null}),at.on&&at.on("sdkInitAPI",function(){at.enableLocalLog=Ol,at.disableLocalLog=jl}))}},ia=wl(Bu,"ConsoleLogger"),an="sensorsdata_jssdk_debug";s.modules=s.modules||{};for(var aa=[ia,Xl,Ui,Mi,Yl,ou,Vi,zi,fu,hu,vu,wu,Pu,Zi,Gi,Qi,Yi,ta,ea,ra,na],sa=[ia,ql,Zi,Gi,Yi,Qi,Ui,Vi,Mi,zi,ta,ea,ra,na],me=0;me<aa.length;me++){var Ce=aa[me];s._.isString(Ce.plugin_name)?s.modules[Ce.plugin_name]=Ce:s._.isArray(Ce.plugin_name)&&s._.each(Ce.plugin_name,function(t){s.modules[t]=Ce})}for(me=0;me<sa.length;me++)s.use(sa[me]);var oa=s;try{typeof window.sensorsDataAnalytic201505=="string"?(s.para=window[sensorsDataAnalytic201505].para,s._q=window[sensorsDataAnalytic201505]._q,window[sensorsDataAnalytic201505]=s,window.sensorsDataAnalytic201505=s,s.init()):window.sensorsDataAnalytic201505===void 0?window.sensorsDataAnalytic201505=s:oa=window.sensorsDataAnalytic201505}catch(t){D(t)}return oa}();const ma=Rt(ie.exports);class va{constructor(V){ha(this,"api");this.api=ma,this.init(V)}init(V={$custromData:{}}){var d=Object.assign({server_url:"https://cdex.ccccltd.cn/logSubmit/opc/eventTrack/submit",is_track_single_page:!0,use_client_time:!0,send_type:"beacon",show_log:!1,heatmap:{clickmap:"default",scroll_notice_map:"default",collect_tags:{div:!0,li:!0,img:!0,a:!0,span:!0,svg:!0},track_attr:["hotrep","anotherprop","data-prop2"]}},V);this.api.init(d),this.api.quick("autoTrack",{...V.$custromData}),this.api.registerPropertyPlugin({properties:function(Z){Z.properties.$_element_name=Z.properties.$element_content||(W=>{try{const bt=document.querySelector(W);return bt.getAttribute("xk-event-name")||bt.innerText||bt.textContent||bt.placeholder||bt.parentNode.innerText||bt.parentNode.parentNode.innerText||'当前点击事件缺少描述文案，请在点击元素上挂载文案描述，例如：<li xk-event-name="点击列表1"></li>'}catch{return"捕获错误"}})(Z.properties.$element_selector),Object.assign(Z.properties,V.$custromData)}}),this.api.use("PageLeave",{custom_props:{page_title:V.$custromData.$_app_key},heartbeat_interval_time:50})}}return Ut.RunXkCollectionSdk=function(S){return new va(S)},Object.defineProperty(Ut,Symbol.toStringTag,{value:"Module"}),Ut}({});
