<template>
  <template v-if="!isView">
    <a-space
      style="justify-content: space-between; width: 100%; margin-bottom: 16px"
    >
      <a-input
        style="width: 360px; flex-grow: 1"
        v-model="searchName"
        :placeholder="$t('prjMember.search.name.placeholder')"
        @keyup.enter="filterUsers"
      />
      <a-space>
        <a-button type="outline" @click="filterUsers">
          <template #icon>
            <icon-search />
          </template>
          {{ $t('list.options.btn.search') }}</a-button
        >
        <a-button type="outline" @click="resetUsers">
          <template #icon><icon-loop /> </template
          >{{ $t('list.options.btn.reset') }}</a-button
        >
        <a-button type="primary" @click="addMember">{{
          $t('prjMember.table.opt.add')
        }}</a-button>
      </a-space>
    </a-space>

    <div class="table-box">
      <a-table
        stripe
        row-key="id"
        :loading="loading"
        :columns="columns"
        :data="filterList"
        :scroll="scroll"
        :bordered="false"
        table-layout-fixed
        :pagination="false"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
        <template #phone="{ record }">
          {{ phoneDesensitize(record.phone) }}
        </template>
        <template #email="{ record }">
          {{ emailDesensitize(record.email) }}
        </template>
        <template #accountState="{ record }">
          <span
            v-if="record.accountState === 1"
            class="accountState"
            style="color: #3c7eff"
          >
            <a-badge color="#3C7EFF" />{{ $t('prjMember.table.status.normal') }}
          </span>

          <span
            v-else-if="record.accountState === 2"
            class="accountState"
            style="color: #ff7d00"
          >
            <a-badge color="#FF7D00" />{{
              $t('prjMember.table.status.deactived')
            }}
          </span>

          <span
            v-else-if="record.accountState === 4"
            class="accountState"
            style="color: #eb0aa4"
          >
            <a-badge color="#EB0AA4" />{{
              $t('prjMember.table.status.uncheck')
            }}
          </span>
          <span v-else class="accountState" style="color: gray">
            <a-badge color="gray" />{{ $t('prjMember.table.status.offline') }}
          </span>
        </template>

        <template #operations="{ record }">
          <a-button
            type="text"
            size="small"
            status="danger"
            @click="removeMembers(record.id)"
            >{{ $t('table.opt.remove') }}</a-button
          >
        </template>
      </a-table>
    </div>
  </template>
  <a-space v-else>
    <div class="table-box">
      <a-table
        stripe
        row-key="id"
        :loading="loading"
        :columns="columns"
        :data="filterList"
        :scroll="{ y: 'calc(100vh - 358px)' }"
        :bordered="false"
        table-layout-fixed
        :pagination="false"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
        <template #phone="{ record }">
          {{ phoneDesensitize(record.phone) }}
        </template>
        <template #email="{ record }">
          {{ emailDesensitize(record.email) }}
        </template>
        <template #accountState="{ record }">
          <span
            v-if="record.accountState === 1"
            class="accountState"
            style="color: #3c7eff"
          >
            <a-badge color="#3C7EFF" />{{ $t('prjMember.table.status.normal') }}
          </span>

          <span
            v-else-if="record.accountState === 2"
            class="accountState"
            style="color: #ff7d00"
          >
            <a-badge color="#FF7D00" />{{
              $t('prjMember.table.status.deactived')
            }}
          </span>

          <span
            v-else-if="record.accountState === 4"
            class="accountState"
            style="color: #eb0aa4"
          >
            <a-badge color="#EB0AA4" />{{
              $t('prjMember.table.status.uncheck')
            }}
          </span>
          <span v-else class="accountState" style="color: gray">
            <a-badge color="gray" />{{ $t('prjMember.table.status.offline') }}
          </span>
        </template>
        <template #operations> 暂无 </template>
      </a-table>
    </div>
  </a-space>

  <SelectMembers
    v-model:visible="selectMemberVisible"
    :data="userList"
    @select-member="selectMember"
  ></SelectMembers>
</template>

<script lang="ts" setup>
  import { ref, computed, toRefs } from 'vue';
  import { useI18n } from 'vue-i18n';
  import useLoading from '@/hooks/loading';

  import { TableColumnData } from '@arco-design/web-vue/es/table/interface';

  import SelectMembers from '@/components/selectMembers/index.vue';
  import { getUsersInRole } from '../api';

  const scroll = {
    y: 'calc(100vh - 400px)',
  };

  type Column = TableColumnData & { checked?: true; userId?: string };

  const { t, locale } = useI18n();
  // 国际化类型

  const searchName = ref('');
  // 列表表格展示

  const props = defineProps({
    roleId: {
      type: String,
      default: '',
    },
    isView: {
      type: Boolean,
      default: false,
    },
  });

  const { loading, setLoading } = useLoading(false);
  const { roleId } = toRefs(props);

  const userList = ref<Role.Api.UserDto[]>([]);
  const filterList = ref<Role.Api.UserDto[]>([]);

  const columns = computed<Column[]>(() => [
    {
      title: t('prjMember.column.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 60,
      align: 'center',
    },
    {
      title: t('prjMember.column.name'),
      dataIndex: 'userFullname',
      slotName: 'userFullname',
      align: 'center',
    },
    {
      title: t('prjMember.column.phone'),
      dataIndex: 'phone',
      slotName: 'phone',
      align: 'center',
    },
    {
      title: t('prjMember.column.email'),
      dataIndex: 'email',
      slotName: 'email',
      align: 'center',
    },
    {
      title: t('prjMember.column.opt'),
      dataIndex: 'operations',
      slotName: 'operations',
      width: 80,
      align: 'center',
    },
  ]);

  // 手机号脱敏
  const phoneDesensitize = (phone?: string): string => {
    if (!phone) {
      return '';
    }
    const reg = /(?<=\d{3})\d{4}(?=\d{4})/gi;
    return phone.replace(reg, '****');
  };

  // 邮箱号脱敏
  const emailDesensitize = (email?: string): string => {
    if (!email) {
      return '';
    }
    const emailArr = email.split('@');
    const strStart = email.slice(0, 3);
    let midStr = '';
    for (let i = 0; i < emailArr[0].length - 3; i++) {
      midStr += '*';
    }
    const newEmail = `${strStart}${midStr}@${emailArr[1].toString()}`;
    return newEmail;
  };

  // 列表数据相关
  const fetchData = async () => {
    if (!roleId.value) return;
    setLoading(true);
    try {
      const { data } = await getUsersInRole(roleId.value);
      userList.value = data.list || [];
      filterUsers();
    } catch (err) {
      // you can report use errorHandler or other
    } finally {
      setLoading(false);
    }
  };
  fetchData();

  function filterUsers() {
    filterList.value = userList.value.filter((item) => {
      return item.userFullname.includes(searchName.value);
    });
  }
  function resetUsers() {
    searchName.value = '';
    filterUsers();
  }

  const selectMemberVisible = ref(false);

  const addMember = () => {
    selectMemberVisible.value = true;
  };

  const selectMember = (users: Role.Api.UserDto[]) => {
    userList.value = [...users];
    filterUsers();
  };

  const removeMembers = (id: string) => {
    userList.value = userList.value.filter((item) => item.id !== id);
    filterUsers();
  };

  function getUserIds(): string[] {
    return userList.value.map((item) => item.id);
  }

  defineExpose({ getUserIds });
</script>

<script lang="ts">
  export default {
    name: 'ProjectMember',
  };
</script>

<style scoped lang="less">
  .table-box {
    height: calc(100% - 128px);
    display: flex;
    justify-content: space-between;
    gap: 20px;
  }
  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
  :deep(.arco-btn-size-small) {
    padding: 0 6px;
  }
  :deep(.arco-link:hover) {
    background-color: transparent;
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }
  .blue-link {
    color: rgb(var(--primary-6));
    cursor: pointer;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .setting {
    display: flex;
    align-items: center;
    width: 200px;
    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }
  .mini-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
    margin-bottom: 2px;
  }
  .danger-text {
    color: rgb(var(--red-6));
    &:hover {
      color: rgb(var(--red-6));
    }
  }
  :deep(.arco-badge-status-dot) {
    margin-right: 4px;
  }
  .accountState {
    text-align: left;
  }
  :deep(.arco-form-item) {
    margin-bottom: 0;
  }
  :deep(.arco-select-view) {
    background-color: #fff;
    border: 1px solid #c9cdd4 !important;
  }

  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    color: #1d2129;
  }
</style>
