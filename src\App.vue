<template>
  <a-config-provider :locale="locale">
    <div class="all-box">
      <router-view class="content" />
    </div>
    <global-setting />
  </a-config-provider>
</template>

<script lang="ts" setup>
import {computed, onBeforeUnmount, onMounted} from 'vue';
  import enUS from '@arco-design/web-vue/es/locale/lang/en-us';
  import zhCN from '@arco-design/web-vue/es/locale/lang/zh-cn';
  import useLocale from '@/hooks/locale';
  import setRem from "@/utils/common/setRem";
import { useGlobalModeStore } from "@/store";

  const { currentLocale } = useLocale();
  const locale = computed(() => {
    switch (currentLocale.value) {
      case 'zh-CN':
        return zhCN;
      case 'en-US':
        return enUS;
      default:
        return enUS;
    }
  });
  // const setRemHandle = () => {
  //   setRem();
  // }
  // onMounted(()=>{
  //   setRemHandle()
  //   window.addEventListener('resize', setRemHandle)
  // })
  // onBeforeUnmount(()=>{
  //   window.removeEventListener('resize', setRemHandle)
  // })
const globalModeStore = useGlobalModeStore();
globalModeStore.initGlobalMode();
</script>

<style lang="less" scoped>
  @media screen and (max-width: 1440px) {
    .all-box {
      overflow: auto;
    }
  }
  .content{
    background: #F5F6FB;
  }
  .all-box {
    display: flex;
    height: 100vh;
    width: 100vw;
    //border: 1px solid red;
    //overflow: auto;
    overflow-y: auto;
    flex-wrap: wrap;
  }
  .content {
    flex: 1;
  }
  .ai {
    position: relative;
    top: 60px;
  }

</style>
