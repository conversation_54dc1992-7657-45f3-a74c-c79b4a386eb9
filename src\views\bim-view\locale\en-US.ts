export default {
  'model-viewer.model-detail': 'Model Details',
  'model-viewer.close': 'Close',
  'model-viewer.problemList': 'Problem List',
  'model-viewer.modelAssembly': 'model Assembly',
  'model-viewer.model.translation': 'model Translation',
  'model-viewer.model.rotate': 'model Rotate',
  'model-viewer.model.AxialDirection': 'model AxialDirection',
  'model-viewer.model.Angle': 'model Angle',
  'model-viewer.model.selectModel': 'Please select a model',
  'model-viewer.model.EnterAngle': 'Please Enter The Correct Angle',
  'model-viewer.attributeCheck': 'Attribute Check',
  'model-viewer.roam': 'Roam',
  'model-viewer.share': 'Share',
  'model-viewer.mergeInfo': 'Merge Info',
  'model-viewer.collisionList': 'Collision List',
  'model-viewer.annotation': 'Annotation',

  'model-viewer.issueReply': 'Issue Reply',
  'model-viewer.problemScreenshot': 'Problem Screenshot',
  'model-viewer.modifyStatus': 'Modify Status',
  'model-viewer.replyIssue': 'Reply Issue',
  'model-viewer.enterIssueContent': 'Enter Issue Content',
  'model-viewer.enterIssueContentStage': 'Enter Issue Stage',
  'model-viewer.enterIssueContentType': 'Enter Issue Type',
  'model-viewer.confirm': 'Confirm',
  'model-viewer.unresolved': 'Unresolved',
  'model-viewer.resolved': 'Resolved',
  'model-viewer.inProgress': 'In Progress',

  'model-viewer.issueTitle': 'Issue Title',
  'model-viewer.issueDescription': 'Description',
  'model-viewer.creator': 'Creator',
  'model-viewer.issueStatus': 'Status',
  'model-viewer.createTime': 'Time',
  'model-viewer.screenshot': 'Screenshot',
  'model-viewer.uploadImg': 'Upload Image',
  'model-viewer.published-status': 'Published status',
  'model-viewer.issueStage': 'Issue Stage',
  'model-viewer.type': 'Issue Type',
  'model-viewer.recipient': 'Recipient',
  'model-viewer.operationLogs': 'Issue Response',
  'model-viewer.view': 'View',
  'model-viewer.reply': 'Reply',
  'model-viewer.details': 'Details',
  'model-viewer.return': 'Return',
  'model-viewer.missingViews': 'Missing views',

  'model-viewer.meetingIssueListTitle': 'Meeting Issue List',
  'model-viewer.modelIssueListTitle': 'Model Issue List',
  'model-viewer.issueFilterTitle': 'Issue Title',
  'model-viewer.selectAll': 'All',
  'model-viewer.createUser': 'Creator',
  'model-viewer.allUsers': 'All',
  'model-viewer.searchPlaceholder': 'Please Select ...',
  'model-viewer.createIssue': 'Create Issue',
  'model-viewer.issueXMLDownload': 'BCF Download',

  'model-viewer.issueAnnotation': 'Issue Annotation',
  'model-viewer.issueScreenshot': 'Issue Screenshot',
  'model-viewer.photoTooltip':
    'When taking a screenshot, the viewport is automatically saved. Clicking on the issue in the list will quickly locate it.',
  'model-viewer.title': 'Title',
  'model-viewer.titleRuleMessage': 'Please enter a title',
  'model-viewer.stage': 'Issue Stage',
  'model-viewer.stageRuleMessage': 'Please select a stage',
  'model-viewer.shareBefore': 'Before Sharing',
  'model-viewer.deliveryBefore': 'Before Delivery',
  'model-viewer.delivery': 'Delivery',
  'model-viewer.receivers': 'Recipients',
  'model-viewer.receiverRuleMessage': 'Please select recipients',
  'model-viewer.typeRuleMessage': 'Please select a type',
  'model-viewer.collisionDetection': 'Collision Detection',
  'model-viewer.violateStandard': 'Violation of Standards',
  'model-viewer.ownerStandard': 'Owner Standards',
  'model-viewer.description': 'Description',
  'model-viewer.descriptionPlaceholder': 'Please enter',

  'model-viewer.compareListTitle': 'Version Comparison',
  'model-viewer.compareInfo.add': 'Add',
  'model-viewer.compareInfo.delete': 'Delete',
  'model-viewer.compareInfo.update': 'Update',
  'model-viewer.errorResult':
    'Error (Does not comply with "Public Environmental Data Standard")',
  'model-viewer.greenResult': 'Correct',
  'model-viewer.showDiffOnly': 'Show Only Different Properties',
  'model-viewer.componentName': 'Component Name',
  'model-viewer.componentType': 'Component Type',
  'model-viewer.componentID': 'Component ID',
  'model-viewer.propertyName': 'Property Name',
  'model-viewer.basePropertyValue': 'Property Value (Base Version)',
  'model-viewer.newPropertyValue': 'Property Value (Comparison Version)',

  'model-viewer.partStructure': 'Partial Structure',
  'model-viewer.all': 'All',
  'model-viewer.exportFile': 'Export',
  'model-viewer.hardCollision': 'Hard Collision',
  'model-viewer.gapCollision': 'Gap Collision',
  'model-viewer.objectA': 'Object A',
  'model-viewer.objectB': 'Object B',
  'model-viewer.checkModel': 'Check Model',
  'model-viewer.collisionTolerance': 'Collision Tolerance',

  'model-viewer.collisionID': 'Collision ID',
  'model-viewer.model': 'model',
  'model-viewer.collisionObject1': 'Collision Object one',
  'model-viewer.collisionObject2': 'Collision Object two',
  'model-viewer.collisionType': 'Collision Type',

  'model-viewer.annotationTitle': 'Create Tag Annotation',
  'model-viewer.annotationTooltip':
    'Enter content and click on an element to automatically create an annotation. Click the tag icon to delete the tag, double-click to collapse the text tag.',
  'model-viewer.annotationPlaceholder': 'Enter annotation content',
  'model-viewer.content': 'Content',
  'model-viewer.confirmPositionMessage':
    'Please click to confirm a tag position in the view area first',

  'model-viewer.quantities': 'Engineering Quantity Statistics',
  'model-viewer.upload-quantities': 'Upload',
  'model-viewer.enter-query': 'Enter any keyword query',
  'model-viewer.search': 'Search',
  'model-viewer.clear': 'Clear',

  'model-viewer.quantities-name': 'Detail Name',
  'model-viewer.quantities-number': 'Detail Number',
  'model-viewer.quantities-unit': 'Unit',
  'model-viewer.quantities-code': 'WBS',
  'model-viewer.quantities-amount': '0# Number Of Checklists',
  'model-viewer.no-wbs': 'The WBS-encoded widget was not found',
  'model-viewer.upload-tip': "Please upload the correct 'xls' format",
  'model-viewer.cannot-select-self': "You can't choose yourself",

  // 视角
  'model-viewer.viewMange': 'View Management',
  'model-viewer.addViewMange': 'Add Viewpoint',
  'model-viewer.setStartViewMange': 'Set Initial Viewpoint',
  'model-viewer.PleaseEnterName': 'Please Enter Viewpoint Name',
  'model-viewer.cancel': 'Cancel',
  'model-viewer.confirmDeletion': 'Confirm Deletion of This Viewpoint',
  'model-viewer.name': 'Name',
  'model-viewer.action': 'Action',
  'model-viewer.createdSuccess': 'Created Successfully',
  'model-viewer.editSuccess': 'Edited Successfully',
  'model-viewer.startSetSuccess': 'Initial Viewpoint Set Successfully',
  'model-viewer.deleteSuccess': 'Deleted Successfully',

  // 标注
  'model-viewer.markerTitle': '3D Marker',
  'model-viewer.addMarker': 'Add Marker',
  'model-viewer.markerName': 'Marker Name',
  'model-viewer.xOffset': 'X Offset',
  'model-viewer.yOffset': 'Y Offset',
  'model-viewer.markerType': 'Marker Type',
  'model-viewer.url': 'URL',
  'model-viewer.show': 'Show',
  'model-viewer.interaction': 'Icon Interaction',
  'model-viewer.windowShow': 'Open in New Window',
  'model-viewer.save': 'Save',
  'model-viewer.time': 'Time',
  'model-viewer.webJump': 'WebJump',

  // 导航管理
  'model-viewer.navigation-management': 'Navigation Management',
  'model-viewer.add': 'Add',
  'model-viewer.edit': 'Edit',
  'model-viewer.bind': 'Bind',
  'model-viewer.delete': 'Delete',
  'model-viewer.component-bind': 'Component Bind',
  'model-viewer.bind-success': 'Bind Success',
  'model-viewer.Please-select-component': 'Please Select Component',
  'model-viewer.confirm-delete-node': 'Confirm Delete node?',
  'model-viewer.WBS': 'WBS',
  'model-viewer.runway': 'Construction Runway',
  'model-viewer.profession': 'Profession',
  'model-viewer.space': 'Space',
  'model-viewer.floor': 'Floor',
  'model-viewer.custom': 'Custom',
  'model-viewer.structure-tree': 'Structure Tree',
  'model-viewer.noComponentData': 'No Component Data',
  'model-viewer.do-not-repeat': 'The omponent has already been added',
  'model-viewer.2D-3D-links': 'Two- and three-dimensional linkage',
  'model-viewer.2D-viewer': '2D drawings',

  'model-viewer.created-a-problem': 'Created a problem',
  'model-viewer.answered-the-question': 'Answered the question',
  'model-collaboration.cancel': 'Cancel',
  'model-collaboration.click-add-pin': 'Click to add a problem pin',
  'model-collaboration.complete': 'Done',
  'model-collaboration.screenshot-result': 'Screenshot result',
};
