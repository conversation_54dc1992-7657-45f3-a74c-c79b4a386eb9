import axios from 'axios';
import type { UserState } from '@/store/modules/user/types';

export interface MemberRecord extends Partial<UserState> {
  index?: number;
  projectAdmin?: number;
  moduleVisible?: string | string[];
  projectId?: string;
  userId?: string;
  registerUrl?: string;
}

export interface MemberListParams {
  projectId?: string;
  name?: string;
  userFullName?: string;
  accountState?: number;
  pageSize: number;
  pageNo?: number;
  current?: number;
}

// 获取项目下的成员列表
export function getMemberList(params: MemberListParams) {
  return axios.get('/cde-collaboration/project/getUsers', {
    params,
  });
}

// 新增项目成员
export function addMember(data: MemberRecord) {
  return axios.post('/cde-collaboration/project/saveUser', data);
}

// 修改项目成员信息
export function updateMember(data: MemberRecord) {
  return axios.post('/cde-collaboration/project/updateUser', data);
}

// 移除项目成员
export function removeMember(projectId: string, userId: string) {
  return axios.delete('/cde-collaboration/project/removeUser', {
    params: {
      projectId,
      userId,
    },
  });
}

// 导入成员接口，不使用，直接使用上传文件组件发送接口逻辑
export function importMember(data: any) {
  return axios.post('/cde-collaboration/project/importUser', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 新生成邀请
export function invitationGenerate(data: MemberRecord) {
  return axios.post('/sys-user/invitation/generate', data);
}

// 成员未激活数据编辑
export function invitationEdit(data: MemberRecord) {
  return axios.put('/sys-user/invitation/follow', data);
}

// 成员未激活数据删除
export function invitationDel(followId: MemberRecord) {
  return axios.delete(`/sys-user/invitation/follow?followId=${followId}`);
}

// 获取邀请记录数据
export function getInvitedRecord(params: MemberListParams) {
  return axios.get('/sys-user/invitation/follow', {
    params,
  });
}
