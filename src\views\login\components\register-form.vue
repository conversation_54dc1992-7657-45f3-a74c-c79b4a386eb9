<template>
  <div class="register-form-wrapper">
    <a-tabs
      v-model:active-key="activeKey"
      class="ant-tabs-center register-tabs"
    >
      <a-tab-pane key="register" :title="$t('login.register')">
        <a-form
          ref="formRef"
          :model="formState"
          autocomplete="off"
          class="login-form"
          layout="vertical"
        >
          <a-form-item
            field="userFullname"
            :rules="[
              { required: true, message: $t('login.form.userName.errMsg') },
            ]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-input
              v-model="formState.userFullname"
              :disabled="isInviter"
              :placeholder="$t('login.form.userFullNamePlaceholder')"
            >
              <template #prefix>
                <img
                  src="@/assets/images/login/user-line.png"
                  class="input-icon"
                />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            field="phone"
            :rules="[
              { required: true, message: $t('login.form.telRequired') },
              {
                pattern: /^(\+\d{1,3})?\d{7,13}$/,
                message: $t('login.form.telInvalid'),
              },
            ]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-input
              v-model="formState.phone"
              :disabled="isInviter"
              :placeholder="$t('login.form.telPlaceholder')"
              :maxlength="18"
            >
              <template #prefix>
                <img
                  src="@/assets/images/login/smartphone-line.png"
                  class="input-icon"
                />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            field="email"
            :rules="[
              { required: true, message: $t('login.form.emailPlaceholder') },
              {
                pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                message: $t('login.form.emailInvalid'),
              },
            ]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-input
              v-model="formState.email"
              :disabled="isInviter"
              :placeholder="$t('login.form.emailPlaceholder')"
              :maxlength="30"
            >
              <template #prefix>
                <img
                  src="@/assets/images/login/mail-line.png"
                  class="input-icon"
                />
              </template>
            </a-input>
          </a-form-item>
          <!-- 验证码校验方式 -->
          <a-form-item
            field="captcha"
            :label="$t('userSetting.form.captcha')"
            :rules="[
              { required: true, message: $t('login.form.captchaRequired') },
            ]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-select
              v-model="formState.captchaType"
              class="code-way"
              @click="changCodeWay"
            >
              <a-option value="1">{{ $t('login.form.mobileCaptcha') }}</a-option>
              <a-option value="0">{{ $t('login.form.emailCaptcha') }}</a-option>
            </a-select>

            <a-divider
              direction="vertical"
              style="margin: 0 8px; height: 24px"
            />

            <a-input
              v-model="formState.captcha"
              :placeholder="$t('login.form.captchaPlaceholder')"
              :maxlength="50"
            >
              <template #append>
                <a-button
                  type="text"
                  :loading="smsLoading"
                  @click="getSMSCaptcha"
                >
                  <span v-if="countDown === -2">{{
                    $t('login.form.getCaptcha')
                  }}</span>
                  <span v-else-if="countDown === -1">{{
                    $t('login.form.regainCaptcha')
                  }}</span>
                  <span v-else>{{ `${countDown}s` }}</span>
                </a-button>
              </template>
              <!-- <template #prefix>
                <img
                  src="@/assets/images/login/shield-keyhole-line.png"
                  class="input-icon"
                />
              </template> -->
            </a-input>
          </a-form-item>

          <a-form-item
            field="password"
            :rules="[
              { required: true, message: $t('login.form.password.errMsg') },
            ]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-input-password v-model="formState.password" :placeholder="$t('login.form.password')">
              <template #prefix>
                <img
                  src="@/assets/images/login/lock-line.png"
                  class="input-icon"
                />
              </template>
            </a-input-password>
            <template #extra>
              <div>{{ $t('userSetting.password-validation') }}</div>
            </template>
          </a-form-item>

          <a-form-item
            field="confirmPassword"
            :label="$t('login.form.confirmPassword')"
            :rules="[{ required: true, message: $t('login.form.confirmPasswordRequired') }]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-input-password
              v-model="formState.confirmPassword"
              :placeholder="$t('login.form.confirmPassword')"
            >
              <template #prefix>
                <img
                  src="@/assets/images/login/lock-password-line.png"
                  class="input-icon"
                />
              </template>
            </a-input-password>
          </a-form-item>
          <a-form-item
            v-if="isInviter"
            field="invitationCode"
            label="企业邀请码"
            :rules="[{ required: false, message: '请输入企业邀请码' }]"
            hide-label
          >
            <a-input
              v-model="formState.invitationCode"
              disabled
              placeholder="请输入企业邀请码"
            >
              <template #prefix>
                <img
                  src="@/assets/images/login/building-line.png"
                  class="input-icon"
                />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item class="button-group">
            <div class="buttons">
              <a-button
                type="primary"
                html-type="submit"
                class="primary-btn"
                @click="handleSubmit"
                >注册</a-button
              >
              <a-button
                type="outline"
                class="return-btn"
                @click="changeLogin(LoginMethods.password)"
                >返回登录</a-button
              >
            </div>
          </a-form-item>
        </a-form>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, onBeforeMount, computed, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { getSms, register, getPwdSms } from '@/api/modules/user';
  import { getRegisterInvitation } from '../api';
  import { useI18n } from 'vue-i18n';
  import LoginMethods from '../constant';
  import checkPassWordFormat from '@/utils/password-validation';
  import pwdEncrypt from '@/utils/encryption/pwd';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { useRoute } from 'vue-router';

  const props = defineProps<{
    companyCode?: string;
  }>();

  // 是否是邀请激活
  const isInviter = ref(false);

  const companyCode = computed(() => props.companyCode);

  const { t } = useI18n();
  const activeKey = ref('register'); // 默认选中注册
  const emit = defineEmits(['changeLogin']);

  interface FormState {
    userFullname: string;
    password: string;
    confirmPassword: string;
    phone: string;
    email: string;
    captcha: string;
    invitationCode: string;
    captchaType: string;
  }

  // 修改响应式对象类型
  const formState = reactive<FormState>({
    userFullname: '',
    password: '',
    confirmPassword: '',
    phone: '',
    email: '',
    captcha: '',
    invitationCode: '',
    captchaType: '1',
  });

  // 验证码相关状态
  const formRef = ref<FormInstance>();
  const countDown = ref(-2);
  const smsLoading = ref(false);
  const captchaKey = ref('');

  const counter: any = ref(null);
  // 更新验证码倒计时
  const updateCountDown = () => {
    countDown.value = 60;
    counter.value = setInterval(() => {
      if (countDown.value === 0) {
        clearInterval(counter.value);
        countDown.value = -1;
      } else {
        countDown.value--;
      }
    }, 1000);
  };

  // 修改验证码方式
  const changCodeWay = () => {
    clearInterval(counter.value);
    countDown.value = -2;
  };

  // 获取短信验证码
  const getSMSCaptcha = async () => {
    console.log('getSMSCaptcha called');
    if (countDown.value >= 0) {
      Message.warning(t('login.form.captchaHoldOn'));
      return;
    }

    try {
      // 验证码为手机号
      if (formState.captchaType === '1') {
        // 使用正则表达式验证手机号格式和长度
        const phoneRegex = /^(\+\d{1,3})?\d{7,13}$/;
        if (!phoneRegex.test(formState.phone)) {
          // 校验手机号格式
          if (
            !formState.phone.startsWith('+') &&
            formState.phone.length !== 11
          ) {
            // 如果没有国际区号且长度不是11位，提示手机号长度错误
            Message.error('请输入正确的手机号');
            console.error('请输入正确的手机号');
          } else {
            // 其他格式错误
            Message.error(t('login.form.telInvalid'));
          }
          return;
        }
      } else {
        // 验证码为邮箱
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(formState.email)) {
          Message.error('请输入正确的邮箱');
          return;
        }
      }

      // 发送验证码请求
      smsLoading.value = true;
      const params = {
        phone: formState.captchaType === '1' ? formState.phone : '',
        // 手机号加phoneReceive 为1 则给手机发送验证码，否则给邮箱加phoneReceive 为0发送验证码
        email: formState.captchaType === '0' ? formState.email : '',
        phoneReceive: formState.captchaType,
      };
      const { data, status, message } = await getPwdSms(params);
      captchaKey.value = data;
      console.log('getSMSCaptcha response:', data, status, message);
      if (!status) {
        console.error('getSMSCaptcha error:', message);
        // Message.warning(message);
      } else {
        console.log('getSMSCaptcha success:', data);
        updateCountDown();
        Message.success(t('login.form.captchaSent'));
      }
    } catch (err) {
      if (typeof err === 'string') {
        Message.error(err);
      }
    } finally {
      smsLoading.value = false;
      // updateCountDown();
    }
  };

  const changeLogin = (method: LoginMethods) => {
    console.log('changeLogin called with method:', method);
    emit('changeLogin', method);
  };

  // 表单提交处理
  const handleSubmit = async () => {
    const values = formState;

    try {
      // 使用正则表达式验证手机号格式和长度
      const phoneRegex = /^(\+\d{1,3})?\d{7,13}$/;
      if (!phoneRegex.test(formState.phone)) {
        // 校验手机号格式
        if (!formState.phone.startsWith('+') && formState.phone.length !== 11) {
          // 如果没有国际区号且长度不是11位，提示手机号长度错误
          Message.error('请输入正确的手机号');
          console.error('请输入正确的手机号');
        } else {
          // 其他格式错误
          Message.error(t('login.form.telInvalid'));
        }
        return;
      }

      // 使用正则表达式验证邮箱格式
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      // 验证邮箱格式
      if (formState.email && !emailRegex.test(formState.email)) {
        Message.error(t('login.form.emailInvalid'));
        console.error('邮箱格式不正确');
        return;
      }
      // 验证密码是否一致
      if (values.password !== values.confirmPassword) {
        Message.error(t('userSetting.password.edit.error'));
        return;
      }

      // 验证密码格式
      if (!checkPassWordFormat(values.password)) {
        Message.error(t('userSetting.password-rule-error'));
        return;
      }
      console.log('formState:', formState);
      console.log('captchaKey:', captchaKey.value);
      const registerParams = {
        createUserParam: {
          userFullname: values.userFullname,
          phone: values.phone,
          email: values.email,
          key: captchaKey.value,
          captcha: values.captcha,
          password: pwdEncrypt(values.password),
          phoneReceive: formState.captchaType,
        },
        invitationCode: values.invitationCode,
      };
      console.log('registerParams:', registerParams);
      if (!captchaKey.value) {
        console.log('没有key');
        return;
      }
      // 请求注册接口
      const registerResult = await register(registerParams);
      if (registerResult?.status) {
        Message.success(t('login.form.register.success'));
        formState.captcha = '';
        formState.confirmPassword = '';
        formState.password = '';
        formState.userFullname = '';
        formState.phone = '';
        formState.email = '';
        formState.invitationCode = '';
        captchaKey.value = '';
        changeLogin(LoginMethods.password); // 注册成功后跳转到登录页
      } else {
        // Message.error(
        //   registerResult?.message || t('login.form.register.failed')
        // );
      }
    } catch (error) {
      console.error('注册失败:', error);
      // Message.error(
      //   error instanceof Error ? error.message : t('login.form.register.failed')
      // );
    }
  };

  // 使用watch来监听companyCode的变化
  watch(companyCode, (newVal) => {
    if (newVal) {
      formState.invitationCode = newVal;
    }
  });
  const route = useRoute();

  watch(
    () => route.params?.invitationId,
    async () => {
      if (
        route.query?.invitationId &&
        route.query?.followId &&
        route.query?.isRegister === '1'
      ) {
        // 是邀请激活页面 则获取激活用户信息 回显
        isInviter.value = true;
        const { data } = await getRegisterInvitation({
          invitationId: route.query?.invitationId,
          followId: route.query?.followId,
        });

        formState.userFullname = data.name;
        formState.phone = data?.phone;
        formState.email = data?.email;
        formState.invitationCode = data?.invitationCode;
      } else isInviter.value = false;
    },
    {
      immediate: true,
    }
  );
</script>

<style lang="less" scoped>
  .input-icon {
    width: 18px;
    height: 18px;
    vertical-align: -0.2em;
  }
  .register-form-wrapper {
    width: 430px;
    padding-left: 16px;
  }

  :deep(.arco-form-item) {
    margin-bottom: 24px;
  }

  :deep(.arco-form-item-content) {
    background-color: transparent;
    border: 1px solid #c9cdd4;
    border-radius: 8px;
  }

  :deep(.arco-input-wrapper) {
    background-color: transparent;
  }
  :deep(.arco-input-append) {
    border: none;
    background-color: transparent;
    :deep(.arco-btn-text) {
      width: 100%;
      height: 100%;
      background-color: transparent;
    }
    :deep(.arco-btn-text):hover {
      background-color: transparent !important;
    }
  }

  :deep(.arco-btn-text[type='button']) {
    padding: 0;
  }

  .login-form {
    &-password-actions {
      display: flex;
      justify-content: space-between;
    }
  }

  .button-group {
    :deep(.arco-form-item-content) {
      background-color: transparent;
      border: none;
    }
    .buttons {
      width: 100%;
      border: none;
      .primary-btn {
        width: 100%;
        height: 38px;
      }

      .return-btn {
        width: 100%;
        height: 45px;
        border: none;
      }
    }
  }

  .register-tabs {
    width: 100%;
  }
  :deep(.code-way) {
    width: 126px !important;
    background-color: #ffff;
    border-radius: 8px !important;
  }
  :deep(.arco-tabs-nav-tab) {
    justify-content: center;
  }
</style>
