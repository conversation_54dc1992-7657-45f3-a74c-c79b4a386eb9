import axios from 'axios';

const api = '/dic';
// 获取字典数据
export function getDictionary(params: any) {
  return axios.get<any>('/sys-system/dictionary/list', {
    params,
  });
}
// 信息类型  stdType   1922529320684429314
// 级类     stdClass   1922530260028170241
// 专业     stdField   1922530508708454402
// 精度     stdPrecision   1922530533261910018
// 单位    stdUnit     1922530554266984449

export function getStandardTree() {
  return axios.get<any>(
    `${api}/DataDictionaryServiceApi/ClassCodeStandard/GetTreeList`
  );
}
