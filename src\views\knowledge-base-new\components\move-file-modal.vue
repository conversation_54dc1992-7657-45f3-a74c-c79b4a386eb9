<template>
  <div class="move-file-wrap">
    <a-modal
      :visible="visible"
      width="700px"
      title-align="start"
      :render-to-body="false"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <template #title> {{ $t('knowledge.move-to') }} </template>
      <div style="height: 60vh" class="modal-content">
        <div class="header">
          <span
            v-if="breadcrumbList.length <= 1"
            style="font-weight: bold; font-size: 15px"
            >{{ $t('knowledge.all-file') }}</span
          >
          <a-breadcrumb v-else :max-count="3" style="cursor: pointer">
            <a-breadcrumb-item
              v-for="(item, index) in breadcrumbList"
              :key="item.id"
              @click="breadcrumbChange(item, index)"
              >{{ item.name }}</a-breadcrumb-item
            >
          </a-breadcrumb>
        </div>
        <div class="file-list-content">
          <a-list header="List title" :bordered="false" :data="folderList">
            <template #item="{ item }">
              <a-list-item
                v-if="!item.isNew"
                :key="item.id"
                @click="debouncedSelect(item)"
              >
                <a-list-item-meta :title="item.name">
                  <template #avatar>
                    <a-avatar shape="square" :image-url="folderIcon" :size="24">
                    </a-avatar>
                  </template>
                </a-list-item-meta>
              </a-list-item>
              <a-list-item v-if="item.isNew" :key="item.id">
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar shape="square" :image-url="folderIcon" :size="24">
                    </a-avatar>
                  </template>
                  <template #title>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        align-content: center;
                      "
                    >
                      <a-input ref="newFolderRef" v-model="item.name"></a-input>
                      <icon-check
                        style="margin-left: 8px; cursor: pointer"
                        size="18"
                        @click.stop="debouncedfolderBlur(item)"
                      />
                      <icon-close
                        style="margin-left: 8px; cursor: pointer"
                        size="18"
                        @click.stop="cancelAdd"
                      />
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
      <template #footer>
        <div class="modal-footer">
          <a-button
            style="float: left"
            type="primary"
            shape="round"
            :disabled="folderList.find((e) => e.isNew)"
            @click="addFolder"
            >{{ $t('knowledge.create-folder') }}</a-button
          >
          <a-button style="margin-right: 12px" @click="handleCancel">{{
            $t('knowledge.cancel')
          }}</a-button>
          <a-button type="primary" @click="handleOk">{{
            $t('knowledge.move-to-here')
          }}</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import {
    ref,
    defineProps,
    defineEmits,
    watch,
    computed,
    toRaw,
    nextTick,
  } from 'vue';
  import { useKnowledgeBaseStore2 } from '@/store';
  import {
    FolderMessage,
    getChildFolderList,
    getFileList,
  } from '@/api/tree-folder';
  import {
    moveFileAndFolder,
    addChildFolder,
  } from '@/views/projectSpace/file/api';
  import { batchMoveFile } from '../api';
  import folderIcon from '@/assets/images/knowledge-base/folder.svg?url';
  import { Message } from '@arco-design/web-vue';
  import { useKnowledgeBaseNewStore } from '@/store';
  import { storeToRefs } from 'pinia';
  import { useDebounceFn } from '@vueuse/core';

  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { personal } = storeToRefs(knowledgeBaseNewStore);

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    files: {
      type: Array,
      default() {
        return [];
      },
    },
  });
  const emits = defineEmits(['update:visible', 'moveFileSuccess', 'refresh']);
  const knowledgeStore2 = useKnowledgeBaseStore2();
  const folderV0Id = computed(() => {
    return personal.value.baseFolder?.id ?? '';
  });
  const currentFolderId = ref(folderV0Id.value || '');
  const folderList: any = ref([]);
  const getFolder = async (type: string, parentId: string) => {
    console.log(personal.value.baseFolder.id, '222', currentFolderId.value);
    return getChildFolderList(personal.value.projectId, '', type, parentId);
  };
  const getPersonalFolder = async () => {
    console.log(personal.value.baseFolder, '移动22222');
    const res = await getFolder(
      personal.value.baseFolder?.type ?? '',
      currentFolderId.value
    );
    if (res.status) {
      const fileIds = props.files?.map((e: any) => e.id); // 获取 files 数组中所有的 id
      folderList.value = res.data?.list?.filter((folder: any) => {
        return !fileIds.includes(folder.id); // 过滤掉 folderList 中 id 和 files 中 id 相同的项
      });
      // 如果this.personal.currentFolder?.parentId==='0',则查出来的个人文件去掉不显示
    }
  };
  const breadcrumbList: any = ref([]);
  const breadcrumbChange = (item: any, index: number) => {
    breadcrumbList.value = breadcrumbList.value.slice(0, index + 1);
    currentFolderId.value = item.id;
    getPersonalFolder();
  };

  const changeFolder = (item: any) => {
    breadcrumbList.value.push({
      id: item.id,
      name: item.name,
    });
    currentFolderId.value = item.id;
    getPersonalFolder();
  };
  // 防抖延迟 300ms 执行 onSelect 函数
  const debouncedSelect = useDebounceFn((item) => {
    // 这里是点击时要执行的操作
    changeFolder(item);
  }, 300);
  const handleCancel = () => {
    folderList.value = [];
    currentFolderId.value = '';
    emits('update:visible', false);
  };
  const handleOk = () => {
    const files = toRaw(props.files);
    const fileIds = files
      ?.filter((e: any) => {
        return e.folderId;
      })
      ?.map((e: any) => e.id);
    const folderIds = files
      ?.filter((e: any) => {
        return !e.folderId;
      })
      ?.map((e: any) => e.id);
    const params: any = {
      sourceParentId: files[0]?.parentId || files[0]?.folderId,
      targetParentId: currentFolderId.value,
    };
    if (fileIds.length) {
      params.fileIds = fileIds;
    }
    if (folderIds.length) {
      params.folderIds = folderIds;
    }
    console.log(files, '111', params.targetParentId);
    if (params.sourceParentId === params.targetParentId) {
      Message.warning('请勿移动至相同文件夹！');
      return;
    }
    moveFileAndFolder(fileIds, folderIds, currentFolderId.value).then((res) => {
      if (res.status) {
        Message.success('移动成功！');
        emits('moveFileSuccess');
        handleCancel();
      }
    });
  };

  const newFolderRef: any = ref(null);
  const addFolder = () => {
    const list = toRaw(folderList.value);
    list.unshift({
      id: new Date().getTime(),
      name: '新建文件夹',
      isNew: true,
    });
    folderList.value = [...list];
    nextTick(() => {
      newFolderRef.value?.focus();
    });
  };
  // 新建文件夹
  const folderBlur = (item: any) => {
    const params = {
      name: item.name,
      parentId: currentFolderId.value,
      projectId: personal.value.projectId,
      type: personal.value.baseFolder.type,
    };
    if (!params.name) return;
    addChildFolder(params).then((res) => {
      console.log('resL: ', res.data);
      if (res.status) {
        Message.success('新建文件夹成功！');
        getPersonalFolder();
        emits('refresh');
      }
    });
  };
  // 防抖延迟 300ms 执行 folderBlur 函数
  const debouncedfolderBlur = useDebounceFn((item) => {
    // 这里是点击时要执行的操作
    folderBlur(item);
  }, 300);
  const cancelAdd = () => {
    const list = toRaw(folderList.value);
    folderList.value = list.slice(1, list?.length) || [];
  };

  const init = () => {
    currentFolderId.value = folderV0Id.value || '';
    breadcrumbList.value = [
      {
        id: currentFolderId.value,
        name: '个人文件',
      },
    ];
    getPersonalFolder();
  };
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        init();
      }
    }
  );
</script>

<style scoped lang="less">
  .move-file-wrap {
    :deep(.arco-modal-body) {
      padding: 0px !important;
    }
  }
  .modal-content {
    .header {
      height: 50px;
      background-color: #ececec;
      padding: 13px 20px;
    }
    :deep(.arco-avatar) {
      background-color: white !important;
    }
  }
</style>
