const utilsModules = import.meta.webpackContext('@/utils', {
  recursive: true,
  regExp: /en-US\.ts$/,
});
const apiModules = import.meta.webpackContext('@/api', {
  recursive: true,
  regExp: /en-US\.ts$/,
});
const directionaryModules = import.meta.webpackContext('@/directionary', {
  recursive: true,
  regExp: /en-US\.ts$/,
});

const viewModules = import.meta.webpackContext('@/views', {
  recursive: true,
  regExp: /en-US\.ts$/,
});

const componentModules = import.meta.webpackContext('@/components', {
  recursive: true,
  regExp: /en-US\.ts$/,
});

const globleModules = import.meta.webpackContext('./en-US', {
  recursive: false,
  regExp: /\.ts$/,
});

function formatModules(_modules: any) {
  let result = {};
  // eslint-disable-next-line no-restricted-syntax
  for (const path of _modules.keys()) {
    const mod = _modules(path);
    // eslint-disable-next-line no-unused-expressions
    mod.default ? (result = { ...result, ...mod.default }) : '';
  }
  return result;
}
const locale = {
  ...formatModules(viewModules),
  ...formatModules(componentModules),
  ...formatModules(globleModules),
  ...formatModules(utilsModules),
  ...formatModules(apiModules),
  ...formatModules(directionaryModules),
};

// console.log('en-US i18n =====xxxxx: ', formatModules(viewModules))
export default {
  'menu.result': 'Result',
  'menu.feedback.requirements': 'Feedback',
  'menu.exception': 'Exception',
  'menu.project-member': 'Member',
  'menu.project-setting': 'Project Settings',
  'menu.setting': 'Settings',
  'menu.project-component': 'Component',
  'menu.issues': 'Issue communication',
  'menu.check': 'Check',
  'menu.files': 'Files',
  'menu.enclosure': 'Enclosure',
  'menu.design': 'Profile',
  'menu.mold-application': 'Model Application',
  'menu.conference': 'Conference',
  'menu.component': 'Component',
  'menu.matters': 'Matters',

  'menu.standard-manage': 'Standard Manage',
  'menu.standard-list': 'Standard List',
  'menu.class-code-standard': 'Class Code Standard',
  'menu.attr-features-standard': 'Attribute Features Standard',
  'menu.dictionary-onfig': 'Dictionary Configuration',
  'menu.attr-check': 'Attribute Check',
  'menu.named-check': 'Named Check',

  'menu.task': 'Task',
  'menu.cdex.bim': 'Project Space',
  'menu.cdex.approval': 'Approval',
  'menu.cdex.home': 'Home',
  'menu.org': 'org Organization',
  'menu.user-manage': 'user Manage',
  'menu.portalCenter': 'portal Center',
  'menu.typeManagement': 'type Management',
  'menu.documentManagement': 'document Management',
  'menu.videoGroupManagement': 'videoGroup Management',
  'menu.videoManagement': 'video Management',
  'menu.bannerAdvertisingImage': 'banner AdvertisingImage',
  'menu.announcementSettings': 'announcement Settings',
  'menu.feedbackCollection': 'feedback Collection',
  'menu.temporary-storage': 'Temporary Storage',
  'menu.schedule': 'schedule',
  'menu.createSchedule': 'Create Schedule',
  'navbar.docs': 'Docs',
  'navbar.action.locale': 'Switch to English',
  'navbar.logo.title': 'CDex',
  'navbar.logo.to': 'To Home',
  'navbar.logo.enter': 'Please Enter',
  'navbar.mode.design': 'Co-Office',
  'navbar.mode.design.mini': 'Co',
  'navbar.mode.file': 'File',
  'navbar.mode.file.mini': 'File',
  'navbar.mode.user': 'User Center',
  'navbar.mode.portal': 'Portal Center',
  'navbar.mode.user.mini': 'U',
  'navbar.mode.portal.mini': 'P',
  'navbar.mode.component.library': 'Component Library',
  'navbar.global.view': 'Global View',
  'ai.new.chat': 'New Chat',
  'ai.search': 'Search',
  'ai.confirm.delete': 'Confirm Delete?',
  'ai.delete.success': 'Delete Successful!',
  'ai.rename.success': 'Rename Successful!',
  'global.search.title': 'Global Search',
  'global.search.placeholder': 'Please Enter',
  'global.search.results': 'Search Results',
  'global.search.schedule': 'Schedule',
  'global.search.knowledge': 'Knowledge Base',
  'account.switcher.back': 'Back',
  'account.switcher.title': 'You can switch to the following accounts',
  'account.switcher.description':
    'This user has multiple accounts bound, you can enter any of the following enterprises or organizations',
  'account.switcher.personal': 'Personal Space',
  'account.switcher.loading': 'Loading...',
  // Common Locale
  'list.options.btn.search': 'Search',
  'list.options.btn.clear': 'Clear',
  'list.options.btn.reset': 'Reset',
  'list.options.btn.add': 'Added',
  'list.options.btn.new': 'New',
  'list.options.btn.edit': 'Edit',
  'table.yes': 'Yes',
  'table.no': 'No',
  'table.opt.preview': 'Preview',
  'table.opt.details': 'Details',
  'table.opt.setting': 'Setting',
  'table.opt.edit': 'Edit',
  'table.opt.delete': 'Delete',
  'table.opt.sure.delete': 'confirm the deletion?',
  'table.opt.remove': 'Remove',
  'please-enter': 'Please Enter',
  'please-enter-name-email-phone-to-search':
    'Please enter your name, account, email, or phone number to search',
  'please-select': 'Please Select',
  'delete-successful': 'Delete Successful',
  'delete-failed': 'Delete Failed',
  'create-successful': 'Create Successful',
  'edit-successful': 'Edit Successful',
  'zip': 'Zip',
  'super-administrator': 'Super Administrator',
  'administrator': 'Administrator',
  'ordinary-members': 'Ordinary Mmembers',
  'not-allowed-special-characters':
    'The name cannot contain any of the following characters:',
  'login.register': 'Register',
  'login.complete.info': 'Complete the information',
  'login.form.mobileCaptcha': 'Mobile Verification Code',
  'login.form.emailCaptcha': 'Email Verification Code',
  'login.form.password': 'Password',
  'login.form.confirmPassword': 'Confirm Password',
  'login.form.confirmPasswordRequired': 'Please confirm password',
  'home.chat.general-dialog': 'General Dialog-DeepSeek',
  'home.last-seven-days': 'Last 7 Days',
  'home.no-data': 'No Data Available',
  'menu.cdex.meeting': 'Meeting Matters',
  'layout.switch.to.project': 'Switch to Project Space',
  'layout.switch.to.work': 'Switch to Personal Space',
  'layout.navigation': 'Navigation',
  'layout.ai.assistant': 'AI Assistant',
  'menu.merge-model': 'Merge Model',
  'menu.standard': 'Standard',
  'menu.dashboard': 'Dashboard',
  'menu.knowledgeBase': 'Knowledge Base',
  'menu.cdex.file': 'Project Files',
  'user.transfer.name': 'name',
  'user.transfer.account': 'account',
  'user.transfer.phone': 'phone',
  'user.transfer.email': 'email',
  'user.transfer.operate': 'operate',
  'user.transfer.userList': 'user List',
  'user.transfer.add.user': 'Add User',
  'user.transfer.search.placeholder':
    'Please enter name or phone number to search',
  'user.transfer.remove.confirm': 'Confirm to delete this item?',
  'user.transfer.remove': 'Remove',
  'user.transfer.loading': 'Loading, please wait...',
  'user.transfer.no.data': 'No Data',
  'user.transfer.no.org': 'Data sending error, please try again',
  'user.transfer.no.user': 'No user selected',
  'user.transfer.all.user.list': 'All User List',
  'user.transfer.add.user.list': 'Add User List',
  'user.transfer.confirm': 'Confirm',
  'user.transfer.cancel': 'Cancel',
  'selectMembers.invite': 'Invite Members',
  'selectMembers.search': 'Search',
  'selectMembers.contacts': 'Contacts',
  'selectMembers.selected': 'Selected',
  'selectMembers.person': 'person',
  'selectMembers.selectAll': 'Select All',
  'selectMembers.organization': 'Organization',
  'selectMembers.group': 'Group',
  'selectMembers.close': 'Close',
  'selectMembers.root': 'Root',
  'selectMembers.team': 'Team',
  'navbar.marquee.text':
    'The current project is a sample project. To ensure the security of your project data, please do not upload sensitive data here. All uploaded data will be cleared at 3:30 AM the next day. Please be aware!',
  'task.sync-push-to-jjt': 'Sync Push to JJT',
  'task.submitter': 'Submitter',
  ...locale,
};
