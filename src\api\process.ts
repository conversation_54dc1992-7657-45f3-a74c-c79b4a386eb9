import axios from 'axios';

interface ProListReqParam {
  formKey: string;
  formName?: string;
}

interface CompleteProReqParam {
  taskId: string;
  formBizId: string;
}

interface ProHistoryReqParam {
  bizId: string;
}

interface PassProReqParam {
  taskId: string;
  id: string;
  comment: string;
}

// 获取个人所有任务
export const getProcessList = (params: ProListReqParam) => {
  return axios.get('/sys-bpm/relTasks', { params });
};

export const completeProcess = (data: CompleteProReqParam) => {
  return axios.put('/sys-bpm/process/complete', data);
};

// 获取流程历史操作记录
export const getProcessHistory = (params: ProHistoryReqParam) => {
  return axios.get('/cde-collaboration/collaborate/process/history', {
    params,
  });
};

// 审批通过
export const passProcess = (url: string, params: PassProReqParam) => {
  return axios.get(url, { params });
};
// 审批不通过
export const rejectProcess = (url: string, params: PassProReqParam) => {
  return axios.get(url, { params });
};

// 审批不通过时，删除流程节点上传文件的文件
export const removeAttach = (params: { processInstanceId: string }) => {
  return axios.post('/cde-collaboration/file/removeAttach', params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
};

export const getFormInfo = (url: string, params: { id: string }) => {
  return axios.get(url, { params });
};

export const getDetailTree = (params: { id: string }) => {
  return axios.get('/cde-collaboration/review/detailTree', {
    params,
  });
};
export const getSharedOrDeliveryTree = (params: { id: string }) => {
  return axios.get('/cde-collaboration/collaborate/sharedOrDeliveryTree', {
    params,
  });
};
// 删除负载文件接口
export const deleteAttachFile = (params: { fileId: string }) => {
  return axios.get('/cde-collaboration/file/deleteAttachFile', { params });
};
export default null;
