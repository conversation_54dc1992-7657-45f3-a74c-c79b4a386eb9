import axios from 'axios';
import { getXBaseToken } from '@/utils/auth';

export function getCompareList(params: any) {
  return axios.get('/cde-collaboration/file/version-compare', {
    params,
  });
}

export function GetXBaseSemanticInfo(params: any) {
  return axios.get('/api/open/v1/semantic-model/info', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 模型对比结果
export function getModelCompareResult(data: any) {
  return axios.get(
    `/bimserver/storage/v3/buckets/${data.bucketName}/objects/${data.objectPath}?objectKeyEncoding=base64`,
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getXBaseToken() || ''}`,
      },
      baseURL: '/bim_base_api',
    }
  );
}

export function GetXBaseModelDiffContent(params: any) {
  return axios.get('/cde-collaboration/xbase/modelDiff/content', { params });
}

export function GetXBaseModelDiffProperty(params: any) {
  return axios.get('/cde-collaboration/xbase/modelDiff/content/property', {
    params,
  });
}

export function GetElementldsByGuids(data: any) {
  return axios.post('/api/open/v2/modeltree/guids/elementids', data, {
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}
