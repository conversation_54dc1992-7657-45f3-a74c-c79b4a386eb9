<template>
  <div>
    <div class="search-container">
      <a-tooltip v-if="searchType === 1" :content="$t('global.search.title')">
        <search class="search-btn" @click="searchType = 2" />
      </a-tooltip>
      <!-- 搜索框 -->
      <a-input-search
        v-else
        v-model="resultData"
        allow-clear
        :style="{ width: '320px' }"
        :placeholder="$t('global.search.placeholder')"
        @clear="searchHandle"
        @change="searchHandle"
        @search="searchHandle"
        @keydown.enter="searchHandle"
      />
      <!-- 结果列表 -->
      <div v-if="resultIsShow" class="searchResult" data-ignore-click>
        <div class="result-title">{{ $t('global.search.results') }}</div>
        <div class="result-tag">
          <a-tag
            :color="activeType === 'schedule' ? activeColor : defaultCcolor"
            :class="{ active: activeType === 'schedule' }"
            @click="changeType('schedule')"
            >{{ $t('global.search.schedule') }}</a-tag
          >
          <a-tag
            :color="activeType === 'knowledge' ? activeColor : defaultCcolor"
            :class="{ active: activeType === 'knowledge' }"
            @click="changeType('knowledge')"
            >{{ $t('global.search.knowledge') }}</a-tag
          >
        </div>
        <div v-if="!resultList?.length">
          <a-empty />
        </div>
        <div class="result-body">
          <div
            v-for="item in resultList"
            :key="item.key"
            class="result-list"
            @click="toUrl(item)"
          >
            <div class="list-title">
              <span :class="getResultTypeClass(item)">
                <img :src="getResultTypeIcon(item)" alt="" />
              </span>
              <span class="title">{{ item.title }}</span>
            </div>
            <div class="list-content">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { getSearchList } from '@/api/search';
  import { useKnowledgeBaseStore } from '@/store';
  import search from '@/assets/images/dashboard/search-line.svg';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const knowledgeBaseStore = useKnowledgeBaseStore();

  const router = useRouter();

  const resultData = ref('');

  const searchType = ref(1);

  const resultList: any = ref([]);

  const activeType = ref('schedule');
  const defaultCcolor = '#fff';
  const activeColor = '';

  const getResultTypeClass = (item: any) => {
    switch (item.type) {
      case 'meeting':
        return 'result-type result-type1';
      case 'item':
      case 'PDF':
      case 'DOC':
        return 'result-type result-type2';
      default:
        return '';
    }
  };

  const getResultTypeIcon = (item: any) => {
    switch (item.type) {
      case 'meeting':
        return new URL(
          '@/assets/images/schedule/icon-meeting.png',
          import.meta.url
        ).href;
      case 'item':
        return new URL(
          '@/assets/images/schedule/icon-matter.png',
          import.meta.url
        ).href;
      case 'PDF':
        return new URL(
          '@/assets/images/knowledge-base/pdf.svg',
          import.meta.url
        ).href;
      case 'DOC':
        return new URL(
          '@/assets/images/knowledge-base/doc.svg',
          import.meta.url
        ).href;
      default:
        return '';
    }
  };

  const resultIsShow: any = ref(null);

  // 获取搜索结果数据
  const getSearchListHandle = async () => {
    const param = {
      companyId: '100000',
      keyword: resultData.value,
      pageNo: 1,
      pageSize: 9999,
      type: activeType.value,
    };
    const { data } = await getSearchList(param);
    data.list.forEach((item: any) => {
      item.title = item.title || item.name;
    });
    resultList.value = data.list;
  };

  //   搜索结果
  const searchHandle = () => {
    resultIsShow.value = resultData.value;
    getSearchListHandle();
  };

  //   结果类型切换
  const changeType = (val: any) => {
    activeType.value = val;
    getSearchListHandle();
  };

  const toUrl = (item: any) => {
    // 临时解决日程没有监听到路由变化时无法调用定位数据的bug 后续参考知识库优化
    router.push({
      name: 'dashboard',
    });
    setTimeout(() => {
      if (activeType.value === 'schedule') {
        router.push({
          name: 'schedule',
          query: {
            editType: 'edit',
            type: item.type === 'meeting' ? 'meeting' : 'matters',
            id: item.type === 'meeting' ? item.subId : item.id,
          },
        });
      } else {
        knowledgeBaseStore.setSearchInfo(true, item.folderId, item.fileId);
        router.push({
          name: 'knowledgeBase',
        });
      }
      resultIsShow.value = false;
      resultData.value = '';
      searchType.value = 1;
    }, 100);
  };

  // 点击空白关闭
  const handleDocumentClick = (event: any) => {
    const targetDiv: any = document.querySelector('.search-container');
    if (!targetDiv.contains(event.target)) {
      resultIsShow.value = false;

      if (
        event.target.className.baseVal !== 'search-btn' &&
        event.target.className.baseVal !== 'arco-icon arco-icon-close'
      ) {
        searchType.value = 1;
      }
    }
  };

  onMounted(() => {
    // 监听全局点击事件
    document.addEventListener('click', handleDocumentClick);
  });
  onUnmounted(() => {
    // 组件销毁前移除事件监听
    document.removeEventListener('click', handleDocumentClick);
  });
</script>

<style scoped lang="less">
  :deep(.arco-input-wrapper) {
    border: 1px solid #c9cdd4;
    background-color: inherit;
  }
  .search-container {
    cursor: pointer;
    position: relative;
    .searchResult {
      position: absolute;
      right: 0;
      top: 50px;
      width: 493px;
      background-color: #fff;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
      border-radius: 8px 8px 8px 8px;
      padding: 16px 0;
      .result-title {
        font-size: 20px;
        color: #86909c;
        padding-left: 20px;
      }
      .result-tag {
        padding-left: 20px;
        margin-top: 16px;
        display: flex;
        .arco-tag {
          color: #4e5969;
          cursor: pointer;
          margin-right: 12px;
          padding: 0 16px;
          font-size: 14px;
        }
        .active {
          color: #3366ff;
        }
      }
      .result-body {
        max-height: 400px;
        overflow: auto;
      }
      .result-list {
        margin-top: 8px;
        cursor: pointer;
        padding: 16px 20px;
        &:hover {
          background-color: #ebf0ff;
        }
        .result-type {
          display: inline-block;
          width: 24px;
          height: 24px;
          border-radius: 4px;
          margin-right: 8px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          img {
            width: 20px;
            height: 20px;
            vertical-align: middle;
          }
        }
        .result-type1 {
          background-color: #fff3e8;
        }
        .result-type2 {
          background-color: #e8f7ff;
        }
        .list-title {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          .title {
            font-size: 16px;
            color: #1d2129;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .list-content {
          font-size: 14px;
          color: #86909c;
        }
      }
    }
  }

  svg:focus,
  svg *:focus {
    outline: none;
  }
</style>
