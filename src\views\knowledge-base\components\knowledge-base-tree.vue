<template>
  <div class="menu-tree">
    <div
      class="base-type"
      :class="{ 'base-type-selected': personSelected }"
      @click="changeBaseType(true)"
    >
      <personBase v-if="!personSelected" />
      <personBaseSelect v-else />
      <span>{{ $t('knowledge.personal-knowledge') }}</span>
    </div>
    <a-divider :margin="9" />
    <!-- <div
          class="base-type share-type"
          :class="{
            'base-type-selected': !personSelected && selectedKeys.length === 0,
          }"
          @click="changeBaseType(false)"
        >
          <shareBase v-if="personSelected" />
          <shareBaseSelect v-else />
          <span>共享知识库</span>
        </div>
        <div class="tree-box">
          <a-tree
            ref="treeRef"
            v-model:selected-keys="selectedKeys"
            :data="treeData"
            :selectable="nodeSelectable"
            size="large"
            block-node
            default-expand-all
            auto-expand-parent
            @select="changeSelect"
          >
            <template #title="nodeData"> -->
    <!-- <a-input
            v-if="nodeData.key === renameKey"
            ref="renameInput"
            v-model="treeRenameValue"
            v-focus
            :style="{ width: '100%' }"
            @click.stop
            @blur="renameFolderRequest(nodeData, renameInput)"
            @keydown.enter="renameFolderRequest(nodeData, renameInput)"
          /> -->
    <!-- <div
                class="custom-node-title"
                :class="
                  nodeData.key === '0' || nodeData.key === '1'
                    ? 'root-title'
                    : 'leaf-title'
                "
              >
                {{ nodeData.title }}
              </div>
            </template>
            <template #extra="nodeData">
              <IconPlus
                v-if="nodeData.key === '0'"
                style="
                  margin-right: 2px;
                  font-size: 16px;
                  top: 12px;
                  color: #86909c;
                "
                @click="onIconClick(nodeData)"
              />
            </template>
          </a-tree>
        </div> -->
    <div style="flex: 1"></div>
    <div class="progress-box">
      <a-tooltip
        :content="
          $t('knowledge.personal-knowledge') +
          $t('knowledge.used') +
          getFileSize(usedStorage)
        "
      >
        <div class="progress-text">
          {{ $t('knowledge.used') + getFileSize(usedStorage) }}/2GB</div
        >
        <a-progress
          :percent="usedStorage / totalStorage"
          :stroke-width="8"
          :show-text="false"
        />
      </a-tooltip>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { h, ref, onMounted, watch } from 'vue';
  import { storeToRefs } from 'pinia';
  import { TreeNodeData } from '@arco-design/web-vue/es/tree/interface';
  import { useKnowledgeBaseStore } from '@/store';
  import { getBaseList, getKnowledgeBaseUsage } from '../api';
  import { getFileSize } from '@/utils/file';
  import { IconDown } from '@arco-design/web-vue/es/icon';
  import personBase from '@/assets/images/knowledge-base/person-base.svg';
  import personBaseSelect from '@/assets/images/knowledge-base/person-base-select.svg';
  // import shareBase from '@/assets/images/knowledge-base/share-base.svg';
  // import shareBaseSelect from '@/assets/images/knowledge-base/share-base-select.svg';

  import leafIcon from '@/assets/images/knowledge-base/leaf-icon.svg';
  import i18n from '@/locale/index';

  const { t } = i18n.global;
  const knowledgeBaseStore = useKnowledgeBaseStore();
  const { baseRootId, usedStorage, isSearch, needRefresh } =
    storeToRefs(knowledgeBaseStore);

  const totalStorage = 2 * 1024 * 1024 * 1024;

  // 个人知识库是否选中
  const personSelected = ref(true);

  // 共享知识库选中的节点
  const selectedKeys = ref<string[]>([]);

  // 切换知识库大类
  const changeBaseType = (isPersonSelected: boolean) => {
    personSelected.value = isPersonSelected;
    selectedKeys.value = [];
  };

  // 共享知识库节点是否可选中
  const nodeSelectable = (
    _: TreeNodeData,
    info: { level: number; isLeaf: boolean }
  ) => {
    // console.log(_, 'level: ', info.level);
    return true; // info.level !== 0;
  };

  // 共享知识库树结构
  const treeData = ref<TreeNodeData[]>([
    {
      title: t('knowledge.i-creat-share-kownledge'),
      key: '0',
      switcherIcon: () => h(IconDown),
      children: [
        {
          title: t('knowledge.port-design-share'),
          key: '0-1',
          icon: () => h(leafIcon),
        },
        {
          title: t('knowledge.dep-standard-share'),
          key: '0-2',
          icon: () => h(leafIcon),
        },
      ],
    },
    {
      title: t('knowledge.shared-i-joined'),
      key: '1',
      switcherIcon: () => h(IconDown),
      children: [],
    },
  ]);

  // 选中共享知识库节点
  const changeSelect = (item: string[], data: any) => {
    personSelected.value = false;
  };

  // 创建共享知识库
  const onIconClick = (nodeData: any) => {};

  // 个人知识库id
  const personBaseId = ref('');

  onMounted(async () => {
    const searchTemp = isSearch.value;
    if (!searchTemp) {
      // 初始化数据
      knowledgeBaseStore.initData();
    }
    const params = {
      pageParam: {
        pageNum: 1,
        pageSize: 1000,
      },
      userName: '',
    };
    // 获取知识库列表
    const res = await getBaseList(params);
    if (res.status) {
      const baseList = res.data.list;
      baseList.forEach((base: any) => {
        if (base.type === 'PERSONAL') {
          const baseInfo = res.data.list[0];
          personBaseId.value = baseInfo.id;
          knowledgeBaseStore.setRootId(baseInfo.id);
          knowledgeBaseStore.setRagId(baseInfo.aiRagId);
          knowledgeBaseStore.setFolderVOId(baseInfo.folderVO.id);
          if (!searchTemp) {
            knowledgeBaseStore.openFolder({
              id: baseInfo.folderVO.id,
              name: baseInfo.folderVO.name,
            });
          }
        }
      });

      if (personBaseId.value) {
        // 查询知识库使用情况
        const usageRes = await getKnowledgeBaseUsage({
          kbId: personBaseId.value,
        });
        if (usageRes.status) {
          const usage = usageRes.data;
          knowledgeBaseStore.setKnowledgeBaseUsage(usage);
        }
      }
    }
  });

  watch(
    () => needRefresh.value,
    async (nValue: boolean) => {
      if (nValue) {
        // 查询知识库使用情况
        const usageRes = await getKnowledgeBaseUsage({
          kbId: personBaseId.value,
        });
        if (usageRes.status) {
          const usage = usageRes.data;
          knowledgeBaseStore.setKnowledgeBaseUsage(usage);
        }
      }
    }
  );
</script>

<script lang="ts">
  export default {
    name: 'KnowledgeBaseTree',
  };
</script>

<style scoped lang="less">
  .menu-tree {
    padding: 13px 20px 20px;
    width: 320px;
    min-width: 320px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--color-neutral-3);

    .base-type {
      height: 40px;
      display: flex;
      align-items: center;
      border-radius: 8px;
      color: #4e5969;

      svg {
        margin: 0 10px 0 16px;
      }

      span {
        font-size: 20px;
        font-weight: 500;
        line-height: 30px;
      }
    }

    .base-type:hover {
      cursor: pointer;
    }
    .base-type-selected {
      color: #1d2129;
      background-color: #e8f2ff;
    }

    .share-type {
      margin: 2px 0 5px;
    }
  }

  .progress-text {
    margin-bottom: 6px;
    font-size: 14px;
    color: #86909c;
    line-height: 22px;
    text-align: center;
  }
</style>
