<template>
  <div class="opt-line">
    <div class="breadcrumb-wrapper">
      <a-breadcrumb v-if="breadcrumbList.length > 1" :max-count="3">
        <template #separator>
          <icon-right />
        </template>

        <a-breadcrumb-item
          v-for="(item, index) in breadcrumbList"
          :key="item.id"
          class="has-pointer-nav"
        >
          <a-tooltip :content="index === 0 ? t(item.name) : item.name" mini>
            <div class="breadcrumb-item" @click="jumpToFolder(item, index)">{{
              index === 0 ? t(item.name) : item.name
            }}</div>
          </a-tooltip>
        </a-breadcrumb-item>
      </a-breadcrumb>
      <!-- 占位空元素 -->
      <div v-else class="breadcrumb-placeholder"></div>
    </div>

    <a-space
      :size="16"
      class="opt-list"
      style="width: auto; justify-content: flex-end"
    >
      <a-tooltip :content="t('cloud.import')">
        <importIcon
          class="has-pointer"
          :class="{ 'not-active': referenceIsDisabled }"
          @click="handleReference"
        />
      </a-tooltip>
      <a-tooltip :content="t('knowledgenew.download')" @click="handleDownload">
        <downloadIcon
          class="has-pointer"
          :class="{ 'not-active': isDisabled }"
        />
      </a-tooltip>
      <a-popconfirm
        :content="t('cloud.cancel-import-tips')"
        type="info"
        position="left"
        @ok="handleDelete()"
      >
        <a-tooltip :content="t('cloud.cancel-import')">
          <icon-delete
            :size="16"
            class="has-pointer icon-color"
            :class="{ 'not-active': deleteReferenceIsDisabled }"
          />
        </a-tooltip>
      </a-popconfirm>
    </a-space>
  </div>
  <referenceModals />
</template>

<script setup>
  import { storeToRefs } from 'pinia';
  import { ref, computed, toRefs, defineProps, defineEmits } from 'vue';
  import i18n from '@/locale/index';

  import downloadIcon from '@/assets/images/knowledge-base/download2.svg';
  import uploadIcon from '@/assets/images/knowledge-base/upload2.svg';

  import folderAdd from '@/assets/images/knowledge-base/folder-add.svg';
  import importIcon from '@/assets/images/knowledge-base/reference.svg';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import referenceModals from '@/views/knowledge-base-new/components/reference-modal.vue';
  import useFolderActions from '@/views/knowledge-base-new/composables/useFolderActions';
  import { downloadSource } from '@/views/projectSpace/file/hooks/events';
  import { useI18n } from 'vue-i18n';

  const { downloadSourceProject } = useFolderActions();
  const { t } = useI18n();

  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits([
    'newPersonItemAdded',
    'personHandleUpload',
    'personHandleDownload',
  ]);
  const knowledgeStore = useKnowledgeBaseNewStore();
  const {
    project,
    selectedProjectItems,
    referenceModal,
    selectedProjectChildrenItems,
  } = storeToRefs(knowledgeStore);
  const { breadcrumb, currentFolder } = toRefs(project.value);
  const breadcrumbList = computed(() => breadcrumb.value);
  // 下载按钮禁用条件
  const isDisabled = computed(() => {
    if (currentFolder.value.id === 'project') {
      return selectedProjectItems.value.length === 0;
    }
    return selectedProjectChildrenItems.value.length === 0;
  });
  // 取消引用禁用条件
  const deleteReferenceIsDisabled = computed(() => {
    if (currentFolder.value.id === 'project') {
      return selectedProjectItems.value.length === 0;
    }
    return true;
  });
  // 引用按钮禁用条件
  const referenceIsDisabled = computed(
    () => currentFolder.value.id !== 'project'
  );
  const jumpToFolder = (item, index) => {
    // 如果点击的是最后一个，就直接返回
    if (index === breadcrumb.value.length - 1) {
      return;
    }
    const newBreadcrumb = breadcrumb.value.slice(0, index + 1);
    knowledgeStore.setProjectCurrentFolder(item);
    knowledgeStore.setProjectBreadcrumb(newBreadcrumb);
    if (item.id === 'project') {
      console.log('点击的面包屑信息', item);
      knowledgeStore.getProjectFiles();
    } else {
      knowledgeStore.getPersonalFolder('project');
      knowledgeStore.getfiles('project');
    }
  };
  const handleDelete = () => {
    if (currentFolder.value.id === 'project') {
      knowledgeStore.deleteProjectItems(selectedProjectItems.value, t);
    } else {
      knowledgeStore.deleteProjectItems(selectedProjectChildrenItems.value, t);
    }
  };
  const handleDownload = () => {
    console.log(selectedProjectItems.value, '选中的文件');
    emit('personHandleDownload');
    if (currentFolder.value.id === 'project') {
      selectedProjectItems.value.forEach((row) => {
        const item = {
          ...row,
          name: row.fileName || row.folderName || row.name,
        };
        downloadSourceProject(item);
      });
    } else {
      selectedProjectChildrenItems.value.forEach((row) => {
        downloadSource(row);
      });
    }
  };
  const handleReference = () => {
    knowledgeStore.setReferenceModalVisible(true);
  };
</script>

<style scoped lang="less">
  .opt-line {
    padding: 16px 0;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .icon-color {
    color: #4e5969;
  }
  .opt-list {
    padding: 8px 16px;
    background: rgba(232, 242, 255, 0.6);
    border-radius: 4px;
  }

  :deep(.has-pointer-nav) {
    cursor: pointer;
  }
  :deep(.has-pointer) {
    cursor: pointer;
    outline: none;
  }
  :deep(.has-pointer:hover) {
    color: rgb(var(--arcoblue-5));
  }
  :deep(.not-active) {
    filter: grayscale(100%);
    pointer-events: none;
    cursor: not-allowed;
    opacity: 0.6;
  }
  :deep(.arco-breadcrumb) {
    flex: 1;
  }
  :deep(.arco-breadcrumb-item) {
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .breadcrumb-item {
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100px;
  }
  :deep(.arco-breadcrumb-item:last-child) {
    font-weight: 600;
  }
  :deep(.arco-breadcrumb-item-separator) {
    margin: 0;
  }
  .breadcrumb-placeholder {
    width: 24px; /* 跟 <a-breadcrumb> 高度差不多 */
  }
</style>
