<template>
  <a-spin :loading="loading" dot>
    <div class="describe">
      <taskDependency class="describe-icon" />
      <span class="describe-span">
        {{ $t('schedule.matter.dependency') }}
      </span>
      <a-button
        v-if="type !== 'view' && !hasNoEditPermission"
        type="text"
        style="margin-left: 12px; height: 24px"
        @click="addDependency"
      >
        <template #default> {{ $t('schedule.add') }}</template>
        <template #icon>
          <icon-plus />
        </template>
      </a-button>
    </div>
  </a-spin>
  <div v-if="nodeData.length > 0" style="margin: 0 0 20px 0" class="node-list">
    <a-form layout="vertical">
      <a-row
        v-for="(node, index) in nodeData"
        :key="node.id"
        :gutter="24"
        class="node-input-group"
        :class="!node.editing ? 'dependency-row' : 'editing-row'"
        style="margin-left: 0"
        :align="node.editing ? 'end' : 'center'"
      >
        <!-- 编辑态 -->
        <template v-if="node.editing">
          <a-col :span="uiType === 'noDrawer' ? 8 : 7">
            <a-form-item
              field="passivityId"
              :label="$t('schedule.project-task')"
              :rules="[
                { required: false, message: $t('schedule.selectProjectTask') },
              ]"
            >
              <a-select
                v-model="node.passivityId"
                :placeholder="$t('schedule.task-placeholder')"
                allow-search
                style="width: 100%"
                :max-tag-count="1"
                :disabled="type === 'view' || hasNoEditPermission"
                @popup-visible-change="handlePopupVisibleChange"
                @change="handleTaskChange(node, index)"
              >
                <a-option
                  v-for="item in availableTaskList"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                />
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="uiType === 'noDrawer' ? 9 : 12">
            <a-form-item
              field="relyType"
              :label="$t('schedule.relyType')"
              :rules="[
                { required: false, message: $t('schedule.relyType-tips') },
              ]"
              class="no-border"
            >
              <div class="card-type-group" :style="{ gap: cardTypeGap }">
                <div
                  v-for="card in cardTypeList"
                  :key="card.value"
                  class="card-type-item"
                  :class="{ active: node.relyType === card.value }"
                  @click="selectCardType(index, card.value)"
                >
                  <a-tooltip placement="top" background-color="#fff">
                    <template #content>
                      <component :is="card.tooltipIcon" />
                    </template>
                    <component
                      :is="
                        node.relyType === card.value
                          ? card.activeIcon
                          : card.icon
                      "
                      class="card-type-icon"
                      :class="{ 'scale-svg': uiType === 'drawer' }"
                    />
                  </a-tooltip>
                </div>
              </div>
            </a-form-item>
          </a-col>
          <a-col
            :span="uiType === 'noDrawer' ? 3 : 5"
            style="padding-bottom: 5px"
          >
            <a-button
              type="outline"
              size="small"
              style="min-width: 40px; padding: 0 4px; margin-right: 10px"
              @click="saveDependency(node, index)"
              >{{ $t('schedule.relyType-save') }}</a-button
            >
            <a-popconfirm
              :popup-visible="activePopIndex === index"
              :content="$t('schedule.delete-dependency')"
              position="lt"
              @ok="deleteDependency(index, node)"
              @cancel="cancelDelete()"
            >
              <icon-close
                v-show="type !== 'view' && !hasNoEditPermission"
                size="16"
                class="icon-delete"
                @click="beforeNodeDelete(index, node)"
              />
            </a-popconfirm>
          </a-col>
        </template>
        <!-- 非编辑态 -->
        <template v-else>
          <a-col :span="uiType === 'noDrawer' ? 2 : 3">
            <a-tooltip placement="top" background-color="#fff">
              <template #content>
                <component :is="getCardIcon(node.relyType, 'tooltipIcon')" />
              </template>
              <component
                :is="getCardIcon(node.relyType, 'icon')"
                class="card-type-icon"
              />
            </a-tooltip>
          </a-col>
          <a-col :span="uiType === 'noDrawer' ? 19 : 18" class="task">
            <div class="task-name"
              >{{ node.agendaTitle }}
              <span
                v-if="node?.relationType === '被动' && node?.correlationId"
                class="flag"
                >前任</span
              ></div
            >
            <div class="typelabel">{{ getRelyTypeName(node.relyType) }}</div>
          </a-col>
          <a-col :span="2">
            <a-space>
              <a-popconfirm
                :popup-visible="activePopIndex === index"
                :content="$t('schedule.delete-dependency')"
                position="lt"
                @ok="deleteDependency(index, node)"
                @cancel="cancelDelete()"
              >
                <icon-delete
                  v-show="type !== 'view' && !hasNoEditPermission"
                  size="16"
                  class="icon-delete"
                  @click="beforeNodeDelete(index, node)"
                />
              </a-popconfirm>
            </a-space>
          </a-col>
        </template>
      </a-row>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import { useI18n } from 'vue-i18n';
  import {
    getDependency,
    addDeleteEditDependency,
    getDependencyAgenda,
    addDependencyBatch,
  } from '../api';
  import { Message } from '@arco-design/web-vue';
  import taskDependency from '@/assets/images/matter/git-pull-request-line.svg';
  import FS from '@/assets/images/matter/FS.svg';
  import FSActive from '@/assets/images/matter/FS-active.svg';
  import FF from '@/assets/images/matter/FF.svg';
  import FFActive from '@/assets/images/matter/FF-active.svg';
  import SS from '@/assets/images/matter/SS.svg';
  import SSActive from '@/assets/images/matter/SS-active.svg';
  import SF from '@/assets/images/matter/SF.svg';
  import SFActive from '@/assets/images/matter/SF-active.svg';
  import FSTooltip from '@/assets/images/matter/FSTooltip.svg';
  import FFTooltip from '@/assets/images/matter/FFTooltip.svg';
  import SSTooltip from '@/assets/images/matter/SSTooltip.svg';
  import SFTooltip from '@/assets/images/matter/SFTooltip.svg';

  const { t } = useI18n();

  // props
  const props = defineProps({
    scheduleDetailId: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    hasNoEditPermission: {
      type: Boolean,
      default: false,
    },
    uiType: {
      type: String,
      default: 'noDrawer',
    },
  });
  const emit = defineEmits(['success']);

  const cardTypeGap = computed(() =>
    props.uiType === 'noDrawer' ? '8px' : '2px'
  );
  const loading = ref(false);
  const nodeData = ref<any[]>([]);
  const taskList = ref<any[]>([]); // 任务列表
  const activePopIndex = ref<number | null>(null);
  const selectedIds = computed(() =>
    nodeData.value.map((node) => node.passivityId)
  );
  // 计算未被选中的任务列表
  const availableTaskList = computed(() => {
    // 只保留非编辑状态下未被选中的任务
    // 1. 获取所有处于编辑状态的 passivityId
    const editingIds = nodeData.value
      .filter((n) => n.editing)
      .map((n) => n.passivityId);
    // 2. 过滤掉已被非编辑状态选中的任务
    const usedIds = nodeData.value
      .filter((n) => !n.editing)
      .map((n) => n.passivityId);
    // 3. 返回未被非编辑态选中的任务
    return taskList.value.filter((item) => !usedIds.includes(item.id));
  });

  // 获取任务依赖下拉数据
  const fetchTaskList = async () => {
    try {
      const res = await getDependency({
        name: '',
        projectId: props.projectId,
        scheduleDetailId: props.scheduleDetailId,
      });
      // 假设接口返回数组
      taskList.value = res?.data || [];
    } catch (e) {
      taskList.value = [];
    }
  };
  // 查询该事项绑定的依赖
  const fetchDependencyAgenda = async () => {
    try {
      const res = await getDependencyAgenda({
        projectId: props.projectId,
        scheduleDetailId: props.scheduleDetailId,
      });
      // 假设接口返回数组
      // 返回的字段都需要替换一下
      nodeData.value = (res?.data || []).map((item: any) => ({
        ...item,
        passivityId: item.correlationId,
        relyType: item.type,
        // ...其他字段映射
      }));
    } catch (e) {
      nodeData.value = [];
    }
  };
  // 下拉弹窗显示时查询最新数据（类型声明）
  const handlePopupVisibleChange = (visible: boolean) => {
    if (visible) fetchTaskList();
  };
  // 计算属性hasEmptyEditingNode，用来判断有没有未填写的编辑项
  // 页面加载时自动请求

  onMounted(async () => {
    if (props.projectId) {
      await fetchTaskList();
    }
    if (props.scheduleDetailId) {
      await fetchDependencyAgenda();
    }
  });
  // 监听 id 变化自动刷新
  watch(
    () => props.projectId,
    (val) => {
      if (val) fetchTaskList();
    }
  );
  watch(
    () => props.scheduleDetailId,
    (val) => {
      if (val) fetchDependencyAgenda();
    }
  );

  // 卡片类型列表，直接用 import 的 SVG 变量
  const cardTypeList = [
    {
      value: 1,
      label: 'FS',
      name: '结束到开始',
      icon: FS,
      activeIcon: FSActive,
      tooltipIcon: FSTooltip,
    },
    {
      value: 3,
      label: 'FF',
      name: '结束到结束',
      icon: FF,
      activeIcon: FFActive,
      tooltipIcon: FFTooltip,
    },
    {
      value: 2,
      label: 'SS',
      name: '开始到开始',
      icon: SS,
      activeIcon: SSActive,
      tooltipIcon: SSTooltip,
    },
    {
      value: 4,
      label: 'SF',
      name: '开始到结束',
      icon: SF,
      activeIcon: SFActive,
      tooltipIcon: SFTooltip,
    },
  ];

  // 获取依赖类型对应的图标
  function getCardIcon(
    relyType: number,
    type: 'icon' | 'activeIcon' | 'tooltipIcon' = 'icon'
  ) {
    const card = cardTypeList.find((c) => c.value === relyType);
    return card ? card[type] : '';
  }
  // 获取依赖类型对应的中文
  function getRelyTypeName(relyType: number) {
    const card = cardTypeList.find((c) => c.value === relyType);
    return card ? card.label : '';
  }
  // 获取事项名称
  function getTaskName(passivityId: string) {
    const task = taskList.value.find((item) => item.id === passivityId);
    return task ? task.name : '';
  }
  // 选择卡片类型
  function selectCardType(index: number, value: number) {
    nodeData.value[index].relyType = value;
  }
  // 生成本地唯一 ID
  const generateLocalId = () => {
    return (
      Date.now().toString() + Math.floor(Math.random() * 1000000).toString()
    );
  };
  // 新增依赖时默认选中第一个卡片
  const addDependency = () => {
    // 这个新增需要限制一下，当availableTaskList长度是0的时候，就不允许添加了
    // 新增的时候，如果列表里有一个是编辑状态，再次点击新增就给去除掉
    const editingIndex = nodeData.value.findIndex((item) => item.editing);
    if (editingIndex !== -1) {
      nodeData.value.splice(editingIndex, 1);
    } else {
      nodeData.value.unshift({
        id: generateLocalId(),
        add: true, // 标记为新增
        projectId: props.projectId,
        passivityId: '',
        relyType: '',
        agendaTitle: '',
        editing: true,
        // 其他依赖项字段
      });
    }
  };
  // 调用接口删除添加的依赖
  const deleteDependency = async (index: number, node: any) => {
    const params = {
      deleteFlag: -1,
      id: node.id, // 确保传入正确的依赖项 ID
    };
    try {
      const res = await addDeleteEditDependency(params);
      if (res.status) {
        // 删除成功后从 nodeData 中移除对应项
        nodeData.value.splice(index, 1);
        activePopIndex.value = null;
        Message.success(t('schedule.delete.success'));
        await fetchDependencyAgenda();
      }
    } catch (err) {
      console.log(err);
    }
  };
  // 取消删除操作
  const cancelDelete = () => {
    activePopIndex.value = null; // 关闭当前行的 popconfirm
  };
  // 删除节点
  const nodeDelete = (index: number) => {
    // 直接使用当前索引删除元素
    nodeData.value.splice(index, 1);
    // 删除后关闭 popconfirm
    activePopIndex.value = null;
  };
  // 删除依赖项
  // 删除节点前校验
  const beforeNodeDelete = (index: number, record: any) => {
    // 如果是新建的依赖项，没有id,直接删除
    if (record.add) {
      nodeDelete(index);
    } else {
      // 如果是已有的依赖项，显示确认弹窗
      activePopIndex.value = index; // 只显示当前行的 popconfirm
    }
  };
  // 保存单个依赖
  const saveDependency = async (node: any, index: number) => {
    // 如果是新建依赖项，就添加到列表去，如果是编辑添加依赖项，就调用接口
    console.log('saveDependency', node);
    if (!node.passivityId) {
      Message.warning(t('schedule.no-relyType'));
      return;
    }
    if (!node.relyType) {
      Message.warning(t('schedule.relyType-tips'));
      return;
    }
    if (props.scheduleDetailId) {
      try {
        // 保存单个也需要把值塞进agendaRely数组里面
        const res = await addDeleteEditDependency({
          ...node,
          id: node.add ? '' : node.id, // 新增时生成本地 ID
          deleteFlag: 0,
          initiativeId: props.scheduleDetailId,
        });
        if (res.status) {
          Message.success(t('schedule.save-success'));
          await fetchDependencyAgenda();
        }
      } catch (e) {
        console.log(e);
      }
    } else {
      nodeData.value[index].editing = false; // 保存后退出编辑状态
    }
  };
  // 保存多个依赖
  const saveAllDependency = async (scheduleDetailId: string) => {
    try {
      // 便利 nodeData，构建 agendaRely 数组
      const param = nodeData.value
        .filter((item) => !item.editing)
        .map((item) => {
          const { id, add, ...rest } = item;
          return {
            ...rest,
            deleteFlag: 0,
            initiativeId: scheduleDetailId,
          };
        });
      const res = await addDependencyBatch(param);
      if (res) {
        // Message.success(t('schedule.save-success'));
        // console.log(111111111);
        emit('success', true);
      }
    } catch (e) {
      console.log(e);
    }
  };
  const handleTaskChange = (node: any, index: number) => {
    // 当任务选择变化时，更新 agendaTitle
    console.log('handleTaskChange', node, index);
    nodeData.value[index].agendaTitle = node.passivityId
      ? getTaskName(node.passivityId)
      : '';
  };

  defineExpose({
    saveAllDependency,
    nodeData: nodeData.value,
  });
</script>

<style lang="less" scoped>
  .describe {
    display: flex;
    align-items: center;
    height: 32px;
    margin-bottom: 8px;
    .describe-icon {
      width: 20px;
      height: 20px;
    }
    .describe-span {
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
      color: #1d2129;
      margin-left: 8px;
    }
    .ai-recognition {
      margin-left: 12px;
      cursor: pointer;
      height: 22px;
    }
  }
  .node-input-group {
    width: 100%;
    // margin: 8px 0;
    :deep(.arco-picker) {
      border: 1px solid #c9cdd4;
    }
  }
  .card-type-group {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .card-type-item {
    cursor: pointer;
    border-radius: 4px;
    padding: 2px;
    transition: box-shadow 0.2s;
  }
  :deep(.no-border .arco-form-item-content-wrapper) {
    border: none !important;
  }
  .card-type-icon {
    width: 47px;
    height: 30px;
  }
  .typelabel {
    color: #4e5969;
    font-size: 12px;
    margin-top: 3px;
  }
  .task {
    margin-left: 10px;
  }
  .task-name {
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
  }
  .dependency-row {
    border-bottom: 1px solid #d9d9d9;
    transition: background 0.2s;
    &:hover {
      background: #e8f2ff;
    }
    /* 让一行内容更紧凑 */
    align-items: center;
    min-height: 50px;
    margin-bottom: 0;
    padding-bottom: 0;
  }
  .editing-row {
    margin-bottom: 8px;
  }
  .flag {
    font-size: 10px;
    color: #4e5969;
    background: #e5e6eb;
    border-radius: 2px 2px 2px 2px;
    padding: 3px 8px;
    line-height: 14px;
  }
  // .scale-svg {
  //   max-width: 100%;
  //   max-height: 1rem;
  //   height: auto;
  //   width: auto;
  //   display: block;
  // }
  :deep(.arco-form-item) {
    margin-bottom: 0;
  }
</style>
