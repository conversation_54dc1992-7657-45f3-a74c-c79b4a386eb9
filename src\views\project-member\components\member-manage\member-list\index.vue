<template>
  <div class="container">
    <a-row :gutter="16" style="margin-bottom: 16px">
      <a-col :flex="1">
        <table-title
          :title="$t('prjMember.activated-member-list')"
        ></table-title>
      </a-col>
    </a-row>
    <!-- <a-row :gutter="16">
      <a-col :flex="1">
        <a-form :model="formModel" label-align="left">
          <a-row :gutter="16">
            <a-col flex="312px">
              <a-form-item
                field="name"
                :label="$t('prjMember.search.name')"
                label-col-flex="60px"
              >
                <a-input
                  v-model="formModel.name"
                  :placeholder="$t('prjMember.search.name.placeholder')"
                  @keyup.enter="search"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <a-col :flex="'128px'" style="text-align: right">
        <a-space :size="8">
          <a-button type="outline" @click="search">
            <template #icon>
              <icon-search />
            </template>
            {{ $t('list.options.btn.search') }}</a-button
          >
          <a-button type="outline" @click="reset">
            <template #icon><icon-loop /> </template
            >{{ $t('list.options.btn.reset') }}</a-button
          >
        </a-space>
      </a-col>
    </a-row>
    <a-divider margin="20px 0 20px" /> -->

    <!-- <a-row style="margin-bottom: 16px">
      <a-col v-if="isPrjAdmin" v-permission="`${$btn.team.addMember}`">
        <a-space :size="8">
          <a-button type="primary" @click="add">{{
            $t('prjMember.table.opt.add')
          }}</a-button>
        </a-space>
      </a-col>
    </a-row> -->

    <div class="table-box">
      <a-table
        stripe
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="columns"
        :data="renderData"
        :scroll="scroll"
        :bordered="false"
        table-layout-fixed
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #phone="{ record }">
          {{ phoneDesensitize(record.phone) }}
        </template>
        <template #email="{ record }">
          {{ emailDesensitize(record.email) }}
        </template>
        <template #name="{ record }">
          <span class="blue-link" @click="detail(record)">{{
            record.name
          }}</span>
        </template>
        <template #projectAdmin="{ record }">
          <!-- <span
            class="mini-dot"
            :style="{
              backgroundColor: record.projectAdmin ? '#33B737' : '#FF4D4F',
            }"
          ></span>
          <span>{{
            record.projectAdmin ? $t('table.yes') : $t('table.no')
          }}</span> -->
          <a-switch
            v-model="record.projectAdmin"
            v-permission="$btn.team.updateMember"
            checked-color="#3366FF"
            unchecked-color="#C9CDD4"
            :disabled="!isPrjAdmin || record.userId === userId"
            :checked-value="1"
            :unchecked-value="0"
            :checked-text="$t('table.yes')"
            :unchecked-text="$t('table.no')"
            @change="projectAdminChange(record, $event)"
          ></a-switch>
        </template>
        <template #accountState="{ record }">
          <span
            v-if="record.accountState === 1"
            class="accountState"
            style="color: #3c7eff"
          >
            <a-badge color="#3C7EFF" />{{ $t('prjMember.table.status.normal') }}
          </span>

          <span
            v-else-if="record.accountState === 2"
            class="accountState"
            style="color: #ff7d00"
          >
            <a-badge color="#FF7D00" />{{
              $t('prjMember.table.status.deactived')
            }}
          </span>

          <span
            v-else-if="record.accountState === 4"
            class="accountState"
            style="color: #eb0aa4"
          >
            <a-badge color="#EB0AA4" />{{
              $t('prjMember.table.status.uncheck')
            }}
          </span>
          <span v-else class="accountState" style="color: gray">
            <a-badge color="gray" />{{ $t('prjMember.table.status.offline') }}
          </span>
        </template>

        <template #operations="{ record }">
          <!-- <a-button type="text" size="small" @click="update(record)">{{
            $t('table.opt.edit')
          }}</a-button> -->
          <a-button
            type="text"
            size="small"
            status="danger"
            @click="removeMembers(record.userId)"
            v-permission="$btn.team.removeMember"
            >{{ $t('table.opt.remove') }}</a-button
          >
          <!-- <a-button
            v-if="record.accountState === 2"
            type="text"
            size="small"
            @click="produceLink(record.id)"
            >{{ $t('prjMember.table.opt.invite') }}</a-button
          > -->
        </template>
      </a-table>
    </div>
    <AddMember
      v-if="dialogVisible"
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      @refresh="search"
    />
    <memberDetail
      v-if="memeberVisible"
      v-model:visible="memeberVisible"
      :title="dialogTitle"
      :form="selectForm"
      :user-id="userId"
      :disabled="disabled"
      @refresh="search"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, inject } from 'vue';
  import { useRoute } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import useLoading from '@/hooks/loading';
  import {
    getMemberList,
    MemberListParams,
    MemberRecord,
    removeMember,
    updateMember,
  } from '@/api/member';
  import { Pagination } from '@/types/global';
  import Modal from '@arco-design/web-vue/es/modal';
  import { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import TableTitle from '@/components/table-title/index.vue';
  import { Message } from '@arco-design/web-vue';
  import { usePrjPermissionStore, useUserStore } from '@/store';
  import AddMember from './components/add-member.vue';
  import memberDetail from './components/memberDetail.vue';

  const scroll = {
    y: '100%',
  };

  type Column = TableColumnData & { checked?: true; userId?: string };

  const { t, locale } = useI18n();
  // 国际化类型
  const prjPermissionStore = usePrjPermissionStore();
  const userStore = useUserStore();
  // 当前用户id
  const userId = computed(() => {
    return userStore.id;
  });
  const { isPrjAdmin, teamList } = prjPermissionStore;
  // const isPrjAdmin = userStore.admin === 0;
  // 获取到具管理权限团队的数量
  const ownTeamList = computed(() => {
    const teamLists: string[] = [];
    teamList.forEach((item: any) => {
      if (item.role === 5) {
        teamLists.push(item.id);
      }
    });
    return teamLists.length;
  });
  // 列表表格展示
  const generateFormModel = () => {
    const initData: MemberRecord = {
      name: undefined,
      accountState: undefined,
    };
    return initData;
  };
  const { loading, setLoading } = useLoading(true);
  const renderData = ref<MemberRecord[]>([]);
  const formModel = ref(generateFormModel());
  const columns = computed<Column[]>(() => {
    const constColumn = [
      {
        title: t('prjMember.column.index'),
        dataIndex: 'index',
        slotName: 'index',
        width: 60,
        align: 'left',
      },
      {
        title: t('prjMember.column.name'),
        dataIndex: 'name',
        slotName: 'name',
        align: 'left',
      },
      {
        title: t('prjMember.column.phone'),
        dataIndex: 'phone',
        slotName: 'phone',
        align: 'left',
      },
      {
        title: t('prjMember.column.email'),
        dataIndex: 'email',
        slotName: 'email',
        align: 'left',
      },
      // {
      //   title: t('prjMember.column.role'),
      //   dataIndex: 'role',
      //   align: 'left',
      // },
      // {
      //   // accountState 1,"账号已激活";2,"账号未激活";3、"账号注销”；4:"账号未审批"
      //   title: t('prjMember.column.status'),
      //   dataIndex: 'accountState',
      //   slotName: 'accountState',
      //   width: 110,
      //   align: 'left',
      // },
      {
        title: t('prjMember.column.admin'),
        dataIndex: 'projectAdmin',
        slotName: 'projectAdmin',
        width: 90,
        align: 'left',
      },
    ];
    return (
      isPrjAdmin
        ? [
            ...constColumn,
            {
              title: t('prjMember.column.opt'),
              dataIndex: 'operations',
              slotName: 'operations',
              width: 80,
              align: 'left',
            },
          ]
        : constColumn
    ) as Column[];
  });
  const basePagination: Pagination = {
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
  };
  const pagination = reactive({
    ...basePagination,
  });

  //  编辑是否是管理员
  const projectAdminChange = (obj: Column, val: any) => {
    const data = {
      ...obj,
      projectId: projectId.value,
    };
    editMember(data);
  };
  //  发送管理员编辑请求
  const editMember = async (userData: object) => {
    await updateMember(userData);
    // 更新列表
    fetchData({ current: pagination.current, pageSize: pagination.pageSize });
  };
  // 手机号脱敏
  const phoneDesensitize = (phone: string): string => {
    if (!phone) return '';
    const reg = /(?<=\d{3})\d{4}(?=\d{4})/gi;
    return phone.replace(reg, '****');
  };

  // 邮箱号脱敏
  const emailDesensitize = (email: string): string => {
    if (!email) return '';
    const emailArr = email.split('@');
    const strStart = email.slice(0, 3);
    let midStr = '';
    for (let i = 0; i < emailArr[0].length - 3; i++) {
      midStr += '*';
    }
    const newEmail = `${strStart}${midStr}@${emailArr[1].toString()}`;
    return newEmail;
  };

  // 列表数据相关
  const route = useRoute();
  const projectId = ref<string>((route.params?.projectId as string) || '');
  const fetchData = async (
    params: MemberListParams = { pageNo: 1, pageSize: 20 }
  ) => {
    setLoading(true);
    try {
      const { current, ...rest } = params;
      const apiParams = {
        ...rest,
        pageNo: current || 1,
        projectId: projectId.value,
      };
      const { data } = await getMemberList(apiParams);
      renderData.value = data.list || [];
      pagination.current = apiParams.pageNo;
      pagination.pageSize = apiParams.pageSize;
      pagination.total = data.total;
    } catch (err) {
      // you can report use errorHandler or other
    } finally {
      setLoading(false);
    }
  };
  
  // 获取父组件提供的 formModel
  const parentFormModel: any = inject('parentFormModel');
  
  // 修改 search 方法
  const search = () => {
  // search时重新回到第一页
  const { pageSize } = pagination;
  fetchData({
    pageSize,
    current: 1,
    name: parentFormModel?.value?.name, // 使用父组件的 formModel
  });
  };
  
  // 修改 reset 方法
  const reset = () => {
    console.log("触发zi组件的clear")
  formModel.value = generateFormModel();
  const { pageSize } = pagination;
  fetchData({ pageSize, current: 1 });
  };

  const onPageChange = (current: number) => {
    const { pageSize } = pagination;
    fetchData({ pageSize, current, ...formModel.value });
  };
  const onPageSizeChange = (pageSize: number) => {
    fetchData({ current: 1, pageSize, ...formModel.value });
  };
  fetchData();

  // 功能按钮相关
  const disabled = ref(false);
  const dialogVisible = ref(false);
  const memeberVisible = ref(false);
  const dialogTitle = ref('');
  const defaultSelected: MemberRecord = {
    username: '',
    name: '',
    email: '',
    phone: '',
    role: '',
    projectAdmin: 0,
    moduleVisible: '0,1',
  };
  const selectForm = ref<MemberRecord>({
    ...defaultSelected,
  });
  const add = () => {
    dialogTitle.value = t('prjMember.add.title');
    disabled.value = false;
    dialogVisible.value = true;
    selectForm.value = { projectId: projectId.value, ...defaultSelected };
  };

  const detail = (record: MemberRecord) => {
    dialogTitle.value = t('prjMember.detail.title');
    disabled.value = true;
    memeberVisible.value = true;
    selectForm.value = { projectId: projectId.value, ...record };
  };
  const removeMembers = (id: string) => {
    // 禁止自己移除自己时
    if (id === userId.value) {
      Message.warning(t('prjMember.table.opt.remove.self'));
      return;
    }
    Modal.warning({
      title: t('prjMember.table.opt.remove'),
      content: t('prjMember.table.opt.remove.confirm'),
      closable: true,
      hideCancel: false,
      onOk: async () => {
        const res = await removeMember(projectId.value, id);
        if (res.status) {
          Message.success(t('prjMember.table.opt.remove.success'));
          const { current, pageSize } = pagination;
          fetchData({ pageSize, current, ...formModel.value });
        }
      },
    });
  };

  
  defineExpose({
    search,
    reset,
    add,
  });
</script>

<script lang="ts">
  export default {
    name: 'ProjectMember',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 20px;
    height: 100%;
  }
  .table-box {
    height: calc(100% - 40px);
    display: flex;
    justify-content: space-between;
    gap: 20px;
    :deep(.arco-table-container) {
      height: 100%;
    }
  }
  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
  :deep(.arco-btn-size-small) {
    padding: 0 6px;
  }
  :deep(.arco-link:hover) {
    background-color: transparent;
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }
  .blue-link {
    color: rgb(var(--primary-6));
    cursor: pointer;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .setting {
    display: flex;
    align-items: center;
    width: 200px;
    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }
  .mini-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
    margin-bottom: 2px;
  }
  .danger-text {
    color: rgb(var(--red-6));
    &:hover {
      color: rgb(var(--red-6));
    }
  }
  :deep(.arco-badge-status-dot) {
    margin-right: 4px;
  }
  .accountState {
    text-align: left;
  }
  :deep(.arco-form-item) {
    margin-bottom: 0;
  }
  :deep(.arco-select-view) {
    background-color: #fff;
    border: 1px solid #c9cdd4 !important;
  }

  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    color: #1d2129;
  }
</style>
