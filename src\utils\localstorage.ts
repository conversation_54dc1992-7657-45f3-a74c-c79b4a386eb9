const prefix = 'work';

export const getLocalstorage = (key: string) => {
  return localStorage.getItem(`${prefix}_${key}`)
}

export const setLocalstorage = (key: string, value: string) => {
  return localStorage.setItem(`${prefix}_${key}`, value)
}

export const removeLocalstorageItem = (key: string) => {
  return localStorage.removeItem(`${prefix}_${key}`)
}

export const getSessionStorage = (key: string) => {
  return sessionStorage.getItem(`${prefix}_${key}`)
}

export const setSessionStorage = (key: string, value: string) => {
  return sessionStorage.setItem(`${prefix}_${key}`, value)
}

export const removeSessionStorageItem = (key: string) => {
  return sessionStorage.removeItem(`${prefix}_${key}`)
}

export default  {
  getLocalstorage,
  setLocalstorage,
  removeLocalstorageItem,
  getSessionStorage,
  setSessionStorage,
  removeSessionStorageItem
}