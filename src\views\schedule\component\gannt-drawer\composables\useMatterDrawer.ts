// composables/useTaskDialog.ts
import { ref } from 'vue';

export default function useMatterDrawer() {
  const visible = ref(false); // 创建事项弹窗开关
  const isEdit = ref(false); // 是否编辑模式
  const currentTask = ref<any>(null); // 编辑时传入的任务

  const openCreate = () => {
    isEdit.value = false;
    currentTask.value = null;
    visible.value = true;
  };

  const openEdit = (task: any) => {
    isEdit.value = true;
    currentTask.value = task;
    visible.value = true;
  };

  const close = () => {
    visible.value = false;
    currentTask.value = null;
  };

  return {
    visible,
    isEdit,
    currentTask,
    openCreate,
    openEdit,
    close,
  };
}
