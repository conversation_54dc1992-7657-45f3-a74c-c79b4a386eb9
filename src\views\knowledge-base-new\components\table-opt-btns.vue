<template>
  <a-dropdown
    v-model:popup-visible="popupVisible"
    :hide-on-select="false"
    @select="handleSelect($event)"
  >
    <icon-more class="has-pointer" @click="popupVisible = !popupVisible" />
    <template #content>
      <!-- <a-doption value="pathNavigation">
        <template #icon>
          <icon-location />
        </template>
        <template #default>定位</template>
      </a-doption> -->
      <a-doption v-if="!isSysFolder(props.item.sysType)" value="rename">
        <template #icon>
          <InputField />
        </template>
        <template #default>{{ t('knowledgenew.rename') }}</template>
      </a-doption>
      <a-doption v-if="isWpsFile(props.item.name)" value="edit">
        <template #icon>
          <icon-pen />
        </template>
        <template #default>{{ t('cloud.edit') }}</template>
      </a-doption>
      <a-doption v-if="props.item.editFileType" value="replace">
        <template #icon>
          <icon-swap />
        </template>
        <template #default>{{ t('cloud.replace') }}</template>
      </a-doption>
      <!-- <a-popconfirm
        v-if="props.deleteBtn"
        content="确认取消这个文件/文件夹的引用吗"
        type="info"
        position="left"
        @ok="() => handleDeleteAction('delete', props.item)"
      >
        <div @click.stop>
          <a-doption value="delete">
            <template #icon>
              <icon-delete />
            </template>
            <template #default>取消引用</template>
          </a-doption>
        </div>
      </a-popconfirm> -->
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
  import { defineEmits, ref, defineProps, toRefs, computed } from 'vue';
  import i18n from '@/locale/index';
  import { storeToRefs } from 'pinia';
  import { useRouter } from 'vue-router';
  import { getUserId } from '@/utils/auth';
  import { isWpsFile } from './utils';
  import { isSysFolder } from '@/views/projectSpace/file/utils';
  import { useKnowledgeBaseNewStore } from '@/store';
  import useFileStore from '@/store/modules/file/index';
  import InputField from '@/assets/images/knowledge-base/input-field.svg';

  const { t } = i18n.global;
  const props = defineProps({
    item: {
      type: Object,
      required: true,
    },
    deleteBtn: {
      type: Boolean,
      default: false,
    },
    projectId: {
      type: String,
      default: '',
    },
  });
  const emits = defineEmits(['action']);
  const fileStore = useFileStore();
  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { project } = storeToRefs(knowledgeBaseNewStore);
  const { currentFolder } = toRefs(project.value);
  const router: any = useRouter();
  const userId = getUserId() || '';
  // 替换的按钮控制权限
  const replaceIsShown = computed(() => {
    console.log(props.item, '替换当前文件夹信息');
    if (currentFolder.value.id === 'project') {
      return props.item.type === 'file';
    }
    return !!props.item.folderId;
  });
  // 编辑wps文件按钮权限控制
  const editShowmn = computed(() => {
    if (currentFolder.value.id === 'project' && props.item.type === 'file') {
      return isWpsFile(props.item.fileName);
    }
    if (currentFolder.value.id !== 'project' && props.item.folderId) {
      return isWpsFile(props.item.name);
    }
    return false;
  });

  const popupVisible = ref(false);
  const handleSelect = (value: string) => {
    popupVisible.value = false;
    emits('action', {
      type: value,
      item: props.item,
    });
  };
  const handleDeleteAction = (type: string, item: any) => {
    popupVisible.value = false;
    const data = {
      ...item,
      id: item.folderId || item.fileId,
      projectId: props.projectId,
    };
    emits('action', { type, item: data });
  };
  // watch(
  //   () => popupVisible.value,
  //   (val) => {
  //     emits('action', { type: 'popupVisible', item: props.item, value: val });
  //   }
  // );
</script>

<style scoped lang="less"></style>
