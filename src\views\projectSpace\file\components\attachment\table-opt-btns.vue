<template>
  <a-dropdown position="br" @select="handleSelect($event, rowData)">
    <a-button type="text">
      <template #icon>
        <icon-more-vertical style="transform: rotate(90deg)" /> </template
    ></a-button>
    <template #content>
      <!-- 另存为 -->
      <a-doption value="move">
        <template #icon>
          <icon-save />
        </template>
        <template #default>{{ $t('file-manage.save') }}</template>
      </a-doption>
      <a-doption value="checkDetail">
        <template #icon>
          <icon-link />
        </template>
        <template #default>{{ $t('file-manage.go-detail') }}</template>
      </a-doption>
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
  import { PropType } from 'vue';
  import usePrjPermissionStore from '@/store/modules/project-permission';

  import { AttachTableView } from '@/store/modules/file/types';
  import { useRoute, useRouter } from 'vue-router';

  const projectStore = usePrjPermissionStore();

  const router = useRouter();
  const route = useRoute();

  const { teamList } = projectStore;

  const ownTeamList: any[] = [];
  if (teamList && teamList.length > 0) {
    const teamLists = teamList.filter((item: any) => {
      return item.role === 5;
    });
    if (teamLists.length > 0) {
      teamLists.forEach((element) => {
        ownTeamList.push(element.id);
      });
    }
  }

  defineProps({
    rowData: {
      type: Object as PropType<AttachTableView>,
      required: true,
    },
  });

  const emits = defineEmits(['eventHandle']);

  function handleMove(row: AttachTableView) {
    emits('eventHandle', 'move', row);
  }

  // dropdown事件委托
  const handleSelect = (event: any, row: AttachTableView) => {
    switch (event) {
      case 'move':
        handleMove(row);
        break;
      case 'checkDetail':
        // 跳转事项会议
        router.push({
          path: `/project/${route.params.projectId}/meeting`,
          query: {
            id: row.detailId,
            editType: 'edit',
            type: row.type === 'item' ? 'matters' : row.type,
          },
        });
        break;
      default:
    }
  };
</script>
