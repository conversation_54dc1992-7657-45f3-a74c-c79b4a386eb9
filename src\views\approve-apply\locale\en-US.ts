export default {
  'approve-apply.search': 'Search',
  'approve-apply.enterInfo': 'Enter Name, Phone Number, or Email',
  'approve-apply.query': 'Query',
  'approve-apply.clear': 'Clear',
  'approve-apply.applicationList': 'Application List',
  'approve-apply.quash': 'Quash',
  'approve-apply.approve': 'Approve',
  'approve-apply.reject': 'Reject',
  'approve-apply.agreeConfirm': 'Confirm to Approve This Application',
  'approve-apply.rejectConfirm': 'Confirm to Reject This Application',
  'approve-apply.quashConfirm': 'Confirm the withdrawal of the request',
  'approve-apply.approved': 'Approved',
  'approve-apply.rejected': 'Rejected',
  'approve-apply.name': 'Name',
  'approve-apply.companyDepartment': 'Company/Department',
  'approve-apply.position': 'Position',
  'approve-apply.phoneNumber': 'Phone Number',
  'approve-apply.email': 'Email',
  'approve-apply.application': 'Application',
  'approve-apply.applyApplication': 'Apply for an application',
  'approve-apply.throughApplication': 'Through the application',
  'approve-apply.apply': 'apply',
  'approve-apply.operation': 'Operation',
  'approve-apply.status': 'Status',
  'approve-apply.applyType': 'Apply Type',
  'approve-apply.newUser': 'New User',
  'approve-apply.deferredUser': 'Deferred User',
  'approve-apply.applyTime': 'Apply Time',
  'apply-application.name': 'Name',
  'apply-application.enterInfoName': 'Please enter name',
  'apply-application.phone': 'Phone',
  'apply-application.enterInfoPhone': 'Please enter a valid phone number',
  'apply-application.enterCode': 'Please enter the verification code',
  'apply-application.code-error': 'code error',
  'apply-application.email': 'Email',
  'apply-application.enterInfoEmail': 'Please enter a valid email address',
  'apply-application.production/unit': 'Production/Unit',
  'apply-application.enterProduction/Unit': 'Please enter production/unit',
  'apply-application.post': 'Position',
  'apply-application.enterInfoPost': 'Please enter position/title',
  'apply-application.productUse': 'Apply to Use the Following Products',
  'apply-application.selectProduct': 'Please select at least one product',
  'apply-application.confirm': 'Confirm',
  'apply-application.please-input': 'Please enter',
  'apply-application.secondary-unit': 'Secondary Unit',
  'apply-application.selectSecondaryUnit': 'Please select secondary unit',
  'apply-application.please-select': 'Please Select',
  'apply-application.email-phone-coverage':
    'Email or mobile phone number are duplicate, whether to apply for registration again?',
  'apply-application.email-coverage':
    'Email is duplicate, whether to apply for registration again?',
  'apply-application.phone-coverage':
    'mobile phone number is duplicate, whether to apply for registration again?',
  'apply-application.getCaptcha': 'Get Verification Code',
  'apply-application.regainCaptcha': 'Reacquire Verification Code',
  'apply-application.account-phone-already-exists':
    'Account already exists, please enter the correct email address',
  'apply-application.account-email-already-exists':
    'Account already exists, please enter the correct mobile phone number',
  'apply-application.message-success': 'Success',
  'apply-application.message-error': 'Error',
  'apply-application.chbim-message-error':
    'Only "Highway Digital Scheme Design System" was registered successfully',
  'apply-application.hdsds-message-error':
    'Only Highway BIM Cloud Platform is registered successfully',
  'apply-application.submit-successfully': 'Submit Successfully',
  'apply-application.further-registration': 'Further Registration',
  'apply-application.loading': 'Loading',
  'apply-application.cccc-bim-application':
    'CCCC Lantu BIM platform product application',
  'apply-application.phone-verification-code': 'Phone Verification Code',
  'apply-application.email-verification-code': 'Email Verification Code',
  'apply-application.cccc-internal-fill-in':
    'CCCC internal unit personnel to fill in',
  'apply-application.tips': 'Tips',
  'apply-application.code-sent': 'The verification code has been sent',
  'apply-application.non-cccc-users-select':
    'Non-cccc users select "External Trial Unit"',
};
