<template>
  <div class="merge">
    <a-row style="margin-bottom: 16px">
      <table-title
        :title="$t('model-collaboration.merge-models')"
      ></table-title>
    </a-row>

    <a-card class="general-card">
      <template #title>
        <a-row>
          <a-col :span="20">
            <div>
              <span :class="['search-title']">{{
                $t('model-collaboration.name')
              }}</span>
              <a-input
                v-model="searchParams.fileName"
                :style="{ width: '196px', marginLeft: '16px' }"
                :placeholder="$t('model-collaboration.please-input')"
                allow-clear
                search-button
                @search="search"
                @press-enter="search"
                @clear="search"
              />
            </div>
          </a-col>
          <a-col :span="4" style="text-align: right">
            <a-space :size="8">
              <a-button type="outline" @click="search">
                <template #icon> <icon-search /> </template
                >{{ $t('model-collaboration.search') }}</a-button
              >
              <a-button type="outline" @click="reset">
                <template #icon><icon-loop /> </template
                >{{ $t('model-collaboration.clear') }}</a-button
              >
            </a-space>
          </a-col>
        </a-row>
      </template>
      <a-divider style="margin: 20px 0" />
      <a-row style="margin-bottom: 16px">
        <a-col>
          <a-space>
            <a-button type="primary" @click="createMergeDialog">{{
              $t('model-collaboration.create')
            }}</a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-table-height
        stripe
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="false"
        :scroll="scroll"
        :scrollbar="true"
        @page-change="onPageChange"
        @page-size-change="pageSizeChange"
        @row-click="mergeInfoView"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #name="{ record }">
          <a-link @click="modelView(record)">{{ record.name }}</a-link>
        </template>
        <template #filesSource="{ record }">
          <span
            style="color: rgb(var(--primary-6))"
            @click="fileTableShow(record)"
            >{{ record.files?.length }}</span
          >
        </template>
        <template #handle="{ record }">
          <a-popconfirm
            :content="$t('model-collaboration.delete-content')"
            position="left"
            @ok="DeleteHandle(record)"
          >
            <a-button type="text" size="small">{{
              $t('model-collaboration.delete')
            }}</a-button>
          </a-popconfirm>
        </template>
      </a-table>
    </a-card>
    <merge-model
      v-model:visible="createVisible"
      @refresh="updateData"
    ></merge-model>
    <fileTable
      v-model:visible="filesVisible"
      :files-data="filesData"
      :asm-name="asmName"
    ></fileTable>
  </div>
</template>

<script lang="ts" setup>
  import { useRoute, useRouter } from 'vue-router';
  import { computed, ref, reactive, watch, provide } from 'vue';
  import useLoading from '@/hooks/loading';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import cloneDeep from 'lodash/cloneDeep';
  import mergeModel from './components/merge-model.vue';
  import fileTable from './components/fileTable.vue';
  import { queryMergeList, deleteFile } from './api';
  import { useI18n } from 'vue-i18n';
  import Modal from '@arco-design/web-vue/es/modal';
  import modelViewBim from '@/utils/common/view';
  import { Message } from '@arco-design/web-vue';
  import { getUserTeamsInPrj } from '@/views/projectSpace/home/<USER>';
  import TableTitle from '@/components/table-title/index.vue';

  const { t } = useI18n();
  const router = useRouter();
  // Configurations
  type Column = TableColumnData & { checked?: true };
  const { loading, setLoading } = useLoading(true);
  const scroll = {
    y: 'calc(100vh - 370px)',
  };

  // Data
  const generateSearch = () => {
    return {
      fileName: '',
    };
  };

  const route = useRoute();
  const { projectId }: any = route.params;
  const renderData = ref([]);
  const createVisible = ref(false);
  const filesVisible = ref(false);
  const filesData: any = ref([]);
  const infoVisible = ref(false);
  const detailsInfoRow = ref({});
  const asmName = ref('');
  const searchParams = ref(generateSearch());
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  const pagination = reactive({
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    total: 0,
  });

  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('model-collaboration.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 80,
    },
    {
      title: t('model-collaboration.name'),
      dataIndex: 'name',
      slotName: 'name',
      width: 320,
    },
    {
      title: t('model-collaboration.modelSource'),
      dataIndex: 'filesSource',
      slotName: 'filesSource',
      align: 'left',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('model-collaboration.creator'),
      dataIndex: 'updateUserName',
      align: 'left',
    },
    {
      title: t('model-collaboration.createTime'),
      dataIndex: 'createDate',
      align: 'left',
    },
    {
      title: t('model-collaboration.operate'),
      align: 'left',
      width: 120,
      slotName: 'handle',
    },
  ]);

  // 打开文件数量弹窗
  const fileTableShow = (record: any) => {
    filesVisible.value = true;
    filesData.value = record?.files || [];
    asmName.value = record?.name || '';
  };

  // 清空
  const reset = () => {
    searchParams.value.fileName = '';
    search();
  };
  const fetchData = async () => {
    const params = {
      ...searchParams.value,
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      isCombination: 1,
      projectId,
    };
    setLoading(true);
    try {
      const { data } = await queryMergeList(params);
      renderData.value = data.list;
      pagination.total = data.total;
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };
  const createMergeDialog = () => {
    createVisible.value = true;
  };
  const mergeInfoView = (row: any) => {
    infoVisible.value = true;
    detailsInfoRow.value = row;
  };

  const modelView = (record: any) => {
    modelViewBim(record, route.params.projectId as string, { type: 'merge' });
  };

  const search = () => {
    fetchData();
  };
  const onPageChange = (pageNo: number) => {
    pagination.current = pageNo;
    fetchData();
  };
  const pageSizeChange = (pageSize: number): void => {
    pagination.pageSize = pageSize;
    fetchData();
  };
  const updateData = () => {
    search();
  };
  const tableHeight = ref(0);
  // table根据父组件计算空白高度
  const vTableHeight = {
    mounted(el: Element) {
      tableHeight.value = Math.max(
        (el.parentElement?.offsetHeight || 0) - 148,
        0
      );
    },
  };
  fetchData();

  const DeleteHandle = async (record: any) => {
    const res = await deleteFile({
      fileIds: [record.id],
      folderIds: [],
      targetTeamId: record.teamId,
      txId: record.id,
    });
    if (res.status) {
      Message.success(t('delete-successful'));
      fetchData();
    }
  };
  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );

  const teamList = ref<any[]>([]);
  provide('teamList', teamList);

  //  获取用户在当前项目下的teams和权限;
  const getTeamData = async () => {
    try {
      const res = await getUserTeamsInPrj(route.params.projectId as string);
      if (res.status) {
        teamList.value = res.data[0].teamList || [];
      }
    } catch (err) {
      console.log(err);
    } finally {
      // handle finally
    }
  };
  getTeamData();
</script>

<script lang="ts">
  export default {
    name: 'Merge',
  };
</script>

<style scoped lang="less">
  .merge {
    .card-title {
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
      img {
        position: relative;
        top: 3px;
        height: 20px;
      }
    }
    .search-title {
      height: 22px;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #1d2129;
      line-height: 22px;
    }
  }

  .list-empty {
    display: flex;
    align-items: center;
    .empty-box {
      width: 100%;
      text-align: center;
      height: 400px;
      .text {
        width: 100%;
        text-align: center;
        margin-top: -80px;
      }
    }
  }
  :deep(.arco-table-tr) {
    cursor: pointer;
  }
  :deep(.arco-card-header) {
    height: auto;
    padding: 0;
    border: none;
  }
  :deep(.arco-card-body) {
    padding: 0;
  }
  :deep(.arco-card-bordered) {
    border: none;
  }
  :deep(.arco-scrollbar-thumb-bar) {
    width: 0;
  }
  :dep(.arco-divider) {
    margin: 20px 0 !important;
  }

  :deep(.arco-table-container) {
    height: 100%;
  }
</style>
