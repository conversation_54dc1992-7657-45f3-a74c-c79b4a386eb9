﻿const processModel = `<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.flowable.org/processdef">
  <process id="processModel1" name="流程模板1" isExecutable="true">
    <extensionElements>
      <flowable:eventListener delegateExpression="$\{eventListener}" events="TASK_CREATED,TASK_COMPLETED" />
    </extensionElements>
    <startEvent id="startEvent1">
      <outgoing>SequenceFlow_0d4utxf</outgoing>
    </startEvent>
    <userTask id="UserTask_0" name="流程发起">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler">false</modeler:initiator-can-complete>
      </extensionElements>
      <incoming>SequenceFlow_0d4utxf</incoming>
      <outgoing>SequenceFlow_1i3v44l</outgoing>
    </userTask>
    <sequenceFlow id="SequenceFlow_0d4utxf" sourceRef="startEvent1" targetRef="UserTask_0" />
    <userTask id="UserTask_0uthlc9" name="节点1" flowable:assignee="$\{multiAssignee}">
      <extensionElements>
        <flowable:taskListener delegateExpression="$\{taskEventListener}" event="create" />
      </extensionElements>
      <incoming>SequenceFlow_1i3v44l</incoming>
      <outgoing>SequenceFlow_0d5xuud</outgoing>
      <multiInstanceLoopCharacteristics flowable:collection="$\{multiService.getCollection(multiAssignee)}" flowable:elementVariable="multiAssignee" flowable:assigneeType="person">
        <completionCondition xsi:type="tFormalExpression">$\{ nrOfCompletedInstances == nrOfInstances }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <sequenceFlow id="SequenceFlow_1i3v44l" sourceRef="UserTask_0" targetRef="UserTask_0uthlc9" />
    <userTask id="UserTask_0az3jbl" name="节点2" flowable:assignee="$\{multiAssignee}">
      <incoming>SequenceFlow_0d5xuud</incoming>
      <outgoing>SequenceFlow_1og2b1h</outgoing>
      <multiInstanceLoopCharacteristics flowable:collection="$\{multiService.getCollection(multiAssignee)}" flowable:elementVariable="multiAssignee" flowable:assigneeType="person">
        <completionCondition xsi:type="tFormalExpression">$\{ nrOfCompletedInstances == nrOfInstances }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <sequenceFlow id="SequenceFlow_0d5xuud" sourceRef="UserTask_0uthlc9" targetRef="UserTask_0az3jbl" />
    <userTask id="UserTask_0vaze6e" name="节点3" flowable:assignee="$\{multiAssignee}">
      <incoming>SequenceFlow_1og2b1h</incoming>
      <outgoing>SequenceFlow_1069grr</outgoing>
      <multiInstanceLoopCharacteristics flowable:collection="$\{multiService.getCollection(multiAssignee)}" flowable:elementVariable="multiAssignee" flowable:assigneeType="person">
        <completionCondition xsi:type="tFormalExpression">$\{ nrOfCompletedInstances == nrOfInstances }</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <sequenceFlow id="SequenceFlow_1og2b1h" sourceRef="UserTask_0az3jbl" targetRef="UserTask_0vaze6e" />
    <endEvent id="EndEvent_1e2v7d5">
      <incoming>SequenceFlow_1069grr</incoming>
    </endEvent>
    <sequenceFlow id="SequenceFlow_1069grr" sourceRef="UserTask_0vaze6e" targetRef="EndEvent_1e2v7d5" />
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_sssss">
    <bpmndi:BPMNPlane id="BPMNPlane_sssss" bpmnElement="processModel1">
      <bpmndi:BPMNShape id="BPMNShape_startEvent1" bpmnElement="startEvent1">
        <omgdc:Bounds x="90" y="145" width="30" height="30" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_UserTask_0" bpmnElement="UserTask_0">
        <omgdc:Bounds x="150" y="120" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0d4utxf_di" bpmnElement="SequenceFlow_0d4utxf">
        <omgdi:waypoint x="120" y="160" />
        <omgdi:waypoint x="150" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_0uthlc9_di" bpmnElement="UserTask_0uthlc9">
        <omgdc:Bounds x="280" y="120" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1i3v44l_di" bpmnElement="SequenceFlow_1i3v44l">
        <omgdi:waypoint x="250" y="160" />
        <omgdi:waypoint x="280" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_0az3jbl_di" bpmnElement="UserTask_0az3jbl">
        <omgdc:Bounds x="410" y="120" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0d5xuud_di" bpmnElement="SequenceFlow_0d5xuud">
        <omgdi:waypoint x="380" y="160" />
        <omgdi:waypoint x="410" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="UserTask_0vaze6e_di" bpmnElement="UserTask_0vaze6e">
        <omgdc:Bounds x="540" y="120" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1og2b1h_di" bpmnElement="SequenceFlow_1og2b1h">
        <omgdi:waypoint x="510" y="160" />
        <omgdi:waypoint x="540" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1e2v7d5_di" bpmnElement="EndEvent_1e2v7d5">
        <omgdc:Bounds x="670" y="142" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1069grr_di" bpmnElement="SequenceFlow_1069grr">
        <omgdi:waypoint x="640" y="160" />
        <omgdi:waypoint x="670" y="160" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>`;

const str = ``;
export default processModel;
