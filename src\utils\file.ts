export interface FileType {
  name: string;
}

export function download(file: FileType, blobs: Blob) {
  const blob: File = new File([blobs], '', {
    type: 'application/octet-stream',
  });
  if ('download' in document.createElement('a')) {
    const downloadElement = document.createElement('a');
    let href = '';
    if (window.URL) {
      href = window.URL.createObjectURL(blob);
    } else {
      href = window.webkitURL.createObjectURL(blob);
    }
    downloadElement.href = href;
    downloadElement.download = file.name;
    document.body.appendChild(downloadElement);
    downloadElement.click();
    if (window.URL) {
      window.URL.revokeObjectURL(href);
    } else {
      window.webkitURL.revokeObjectURL(href);
    }
    document.body.removeChild(downloadElement);
    URL.revokeObjectURL(href); // 释放 Blob
  } else if (
    'msSaveBlob' in navigator &&
    typeof navigator.msSaveBlob === 'function'
  ) {
    navigator.msSaveBlob(blob, file.name);
  }
}

export function getFileSize(size: number) {
  let result = '';
  if (size < 1024) {
    result = `${size}B`;
  } else if (size < 1024 ** 2) {
    result = `${(size / 1024).toFixed(1)}KB`;
  } else if (size < 1024 ** 3) {
    result = `${(size / 1024 ** 2).toFixed(1)}MB`;
  } else if (size < 1024 ** 4) {
    result = `${(size / 1024 ** 3).toFixed(1)}GB`;
  } else {
    result = `${(size / 1024 ** 4).toFixed(1)}TB`;
  }
  return result;
}
