import axios from 'axios';
import { AttachMoveParams } from '@/store/modules/file/types';

// 里程碑主任务
export interface MilestoneRecordTeam {
  teamData: Array<{
    id?: string;
    projectId?: string | undefined;
    endTime: string;
    createBy?: string;
    createDate?: string;
    updateBy?: string;
    updateDate?: string;
    deleteFlag?: number;
    description?: string;
    milestoneId?: string | number;
    parentId?: string;
    startTime?: string;
    teamId?: string;
    type?: number;
    color: string;
    date: Array<string>;
  }>;
}
export interface MilestoneRecord {
  id?: string;
  projectId?: string;
  name?: string;
  endTime: string;
  describe: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  milestoneId: string;
  startTime: string;
  teamId: number;
  date: Array<string>;
  taskTreeList: Array<MilestoneRecordTeam>;
}
// 分页查询参数
export interface MilestoneSearchParams {
  pageNo: number;
  pageSize: number;
  projectId: string;
  name?: string;
  startTime?: string;
  endTime?: string;
}
// 查询里程碑列表
export function queryMilestoneList(params: MilestoneSearchParams) {
  return axios.get('/cde-collaboration/milestone/list', {
    params,
  });
}
// 获取归属项目
export const queryProject = (params: any) => {
  return axios.get('/cde-work/agenda/cdeProjectList', {
    params,
  });
};

// 获取所属项目
export const getProjectList = (params: any) => {
  return axios.get('/cde-work/agenda/cdeProjectList', {
    params,
  });
};

// 新增事项
export const addAgenda = (data: any) => {
  return axios.post('/cde-work/agenda/save', data);
};

// 新增评论
export const addComment = (data: any) => {
  return axios.post('/cde-work/agenda/saveComment', data);
};

// 事项详情
export const agendaDetail = (id: string) => {
  return axios.get(`/cde-work/agenda/detail?scheduleDetailId=${id}`);
};

// 修改事项
export const editAgendaAll = (data: any) => {
  return axios.post('/cde-work/agenda/editAll', data);
};

// 创建会议
export function createMeeting(data: any) {
  return axios.post('/cde-work/meetings/save', data);
}

// 删除事项标识(是否管理会议是否有子事项)
export function deleteMatterFlag(params: { scheduleDetailId: any }) {
  return axios.get(`/cde-work/agenda/removeFlag`, { params });
}

// 删除事项
export function deleteMatter(params: { scheduleDetailId: any }) {
  return axios.get(`/cde-work/agenda/remove`, { params });
}

// 查询日程
export function getPanelList(params: any) {
  return axios.get('/cde-work/schedule/panel/list', {
    params,
  });
}

// Ai解析事项里面的内容
export function getAiIdentity(data: any) {
  if (data?.prompt) {
    return axios.post(`/cde-work/agenda/streamChat?prompt=${data.prompt}`);
  }
  return axios.post(`/cde-work/agenda/streamChat`, data);
}

// 删除事项评论
export function deleteComment(data: any) {
  return axios.delete(
    `/cde-work/agenda/deleteCommentId/${data.commentId}?username=${data.username}`
  );
}

// 获取项目下的人员
export const getProjectUsers = (params: any) => {
  return axios.get('/cde-collaboration/project/getUsers', {
    params,
  });
};
export interface TeamSearchParams {
  pageNo: number;
  pageSize: number;
  projectId: string;
  name?: string;
}
// 查询团队列表
export function queryTeamList(params: TeamSearchParams) {
  return axios.get('/cde-collaboration/team/list', {
    params,
  });
}
// 进入到项目日历，获取该项目的项目日历
export function getProjectPanel(params: any) {
  return axios.get('/cde-collaboration/schedule/project/panel', { params });
}

// 添加合并文件的信息
export function addMergaFile(data: any) {
  return axios.post('/cde-collaboration/file/save', data);
}
// 查询未绑定的事项--依赖
export function getDependency(params: any) {
  return axios.get('/cde-collaboration/agenda/findDependency', { params });
}
// 添加修改删除依赖(三合一接口),删除的时候传主键id和deleteFlag传-1即可
export function addDeleteEditDependency(data: any) {
  return axios.post('/cde-collaboration/agenda/addDependency', data);
}
// 查询该事项绑定的依赖
export function getDependencyAgenda(params: any) {
  return axios.get('/cde-collaboration/agenda/dependencyAgenda', { params });
}
// 批量添加修改删除依赖
export function addDependencyBatch(data: any) {
  return axios.post('/cde-collaboration/agenda/addDependencyBatch', data);
}
// 批量导入事项
export function importAgenda(data: any) {
  return axios.post('/cde-collaboration/agenda/importAgenda', data);
}
