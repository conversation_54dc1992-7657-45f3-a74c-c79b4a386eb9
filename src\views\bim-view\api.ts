import axios from 'axios';
import { getXBaseToken } from '@/utils/auth';

export function getModelInfoById(id?: string) {
  return axios.get('/cde-collaboration/file/query-model', {
    params: {
      id,
    },
  });
}

export function getFileInfoById(id?: string) {
  return axios.get('/cde-collaboration/file/detail', {
    params: {
      id,
    },
  });
}

export function getFileVersionInfoById(params: any) {
  return axios.get('/cde-collaboration/file/version/detail', {
    params,
  });
}

export function getCompareList(params: any) {
  return axios.get('/cde-collaboration/file/version-compare', {
    params,
  });
}

// 模型详情
export function GetXBaseTransferStates(params: any) {
  return axios.get('/api/open/v1/model/model', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 获取语义模型详情
export function GetXBaseSemanticInfo(params: any) {
  return axios.get('/api/open/v1/semantic-model/info', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 文件详情
export function getFileInfoWithTest(id?: string) {
  return axios.get('/cde-collaboration/file/detailAuth', {
    params: {
      id,
    },
  });
}

// 项目检查结果
export function getCheckResult(params: any) {
  return axios.get(`/api/open/v1/model-check/project/standard/check/result`, {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 项目检查结果详情
export function getProjectCheckDetail(params: any) {
  return axios.get(`/api/open/v1/model-check/project/standard/check/detail`, {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 获取构件详情
export function GetXBaseComponentInfo(params: any) {
  return axios.get('/api/open/v1/component/component', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 获取文件详情
export function GetXBaseDocumentDetails(params: any) {
  return axios.get('/api/open/v1/document/document', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 属性面板
export function GetPropertiesPanel(data: any) {
  return axios({
    method: 'POST',
    url: '/api/open/v1/semantic-model/property',
    data,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 根据ElementId获取Guid
export function getGuidByElementId(data: any) {
  return axios({
    method: 'POST',
    url: '/api/open/v2/modeltree/elementids/guids',
    data,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 获取模型构件属性
export function getModelComponentAttribute(params: any) {
  return axios.get('/api/open/v1/model/ds/property/uid', {
    params,
    headers: {
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
      'Content-Type': 'application/json',
    },
    baseURL: '/x_base_api',
  });
}
