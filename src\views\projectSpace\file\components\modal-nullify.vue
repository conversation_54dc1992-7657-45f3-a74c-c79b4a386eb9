<!-- <template>
  <a-modal
    v-model:visible="nullifyModalvisable"
    unmount-on-close
    :width="450"
    :ok-text="okText"
    @ok="nullifyAll"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
  >
    <div class="content">
      {{
        isAbandon
          ? $t('file-manage.cancellations')
          : $t('file-manage. are-you-sure-you-want-to-cancel-the-file')
      }}
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { useI18n } from 'vue-i18n';
  import useFileStore from '@/store/modules/file/index';
  import { computed, onMounted, ref } from 'vue';
  import { storeToRefs } from 'pinia';
  import { Message, Notification } from '@arco-design/web-vue';
  import { abandonFiles, getFileInfoById } from '../api';

  const { t } = useI18n();
  const abandon = ref<any>('');
  const store = useFileStore();
  const { isAbandon, checkTableData } = storeToRefs(store);

  const props = defineProps({
    nullifyModal: { type: Boolean, default: false },
  });

  function nullifyAll() {
    let idsArry: any = [];
    idsArry = checkTableData.value.map((item) => item.id);
    let contentText = ''; // 需要国际化
    if (isAbandon.value === 1) {
      contentText = '取消作废成功';
      store.setIsAbandon(null);
    } else {
      contentText = '作废成功';
      store.setIsAbandon(1);
    }
    const res: any = abandonFiles(idsArry)
      .then((response: any) => {
        if (response.code === 8000000) {
          Notification.success({
            id: 'nullify',
            title: 'Success',
            content: contentText,
          });
        }
      })
      .catch((error) => {
        // 处理请求错误
        console.error(error);
      });

    store.getTableData(store.currentFolder || {}, false);
  }

  // 取消按钮
  const handleCancel = () => {
    store.setNullifyModal(false);
  };
</script>

<style scoped lang="less">
  .text-font {
    display: inline-block;
    font-size: 16px;
    margin-left: 8px;
  }
  .file-count {
    position: absolute;
    top: 20px;
    right: 0;
  }
  .share-to {
    margin: 10px;
  }
  .copy-all {
    position: absolute;
    top: 6px;
    right: -15px;
  }
  .file-name {
    width: 80px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .file-link {
    width: 180px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .icon-copy {
    position: relative;
    left: 65px;
    bottom: 4px;
    cursor: pointer;
  }
</style> -->
