<template>
  <div class="bim-view-toolbar">
    <div v-for="tool in toolsList" :key="tool.name" class="tool-item">
      <a-tooltip
        :content="tool.name"
        position="left"
        background-color="#FFF"
        :content-style="{ color: '#666', borderRadius: '.25rem' }"
        mini
      >
        <img
          v-if="!tool.disabled"
          :src="tool.defaultIcon"
          @click="changeTool(tool)"
        />
      </a-tooltip>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {
    defineProps,
    computed,
    reactive,
    defineEmits,
    ref,
    watch,
  } from 'vue';
  import toolsConfig from './toolsConfig';

  const props = defineProps({
    toolGroup: {
      type: String,
      default: 'defaultTools',
    },
    resetTool: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['changeTool', 'closeTool']);

  const currentTool = ref({
    name: '',
    visible: false,
  });
  const toolsList = computed(() => {
    return toolsConfig[props.toolGroup];
  });

  const changeTool = (tool: any) => {
    if (tool.name === currentTool.value.name) {
      currentTool.value.visible = !currentTool.value.visible;
    } else {
      currentTool.value = {
        ...tool,
        visible: true,
      };
    }
    emits('changeTool', currentTool.value);
  };
  watch(
    () => props.resetTool,
    (val) => {
      if (val) {
        currentTool.value = {
          name: '',
          visible: false,
        };
      }
    }
  );
</script>

<style scoped lang="less">
  .tool-item {
    cursor: pointer;
  }
  .bim-view-toolbar {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    .tool-item {
      margin-left: 16px;
    }
  }
</style>
