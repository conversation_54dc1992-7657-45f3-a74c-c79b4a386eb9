<template>
  <div class="forget-form">
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" :title="$t('login.form.changePhone')">
        <a-form
          ref="loginForm"
          :model="userInfo"
          class="login-form"
          layout="vertical"
          @submit="handleSubmit"
        >
          <a-form-item
            field="username"
            :rules="[
              { required: true, message: $t('login.form.userName.errMsg') },
            ]"
            :validate-trigger="['change', 'blur']"
            hide-label
          >
            <a-input
              v-model="userInfo.username"
              :placeholder="$t('login.form.userName.placeholder')"
            >
              <template #prefix>
                <icon-user />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item
            field="password"
            :rules="[
              { required: true, message: $t('login.form.password.errMsg') },
            ]"
            :validate-trigger="['change', 'blur']"
            hide-label
          >
            <a-input-password
              v-model="userInfo.password"
              :placeholder="$t('login.form.password.placeholder')"
              allow-clear
            >
              <template #prefix>
                <icon-lock />
              </template>
            </a-input-password>
          </a-form-item>
          <a-form-item
            field="phone"
            :rules="[
              { required: true, message: $t('login.form.telRequired') },
              {
                match: /^(\+\d{1,3})?\d{7,13}$/, //正则替换  *匹配大陆港澳台
                message: $t('login.form.telInvalid'),
              },
            ]"
            :validate-trigger="['change', 'blur']"
            hide-label
          >
            <a-input
              v-model="userInfo.phone"
              :placeholder="$t('login.form.telPlaceholder')"
              :maxlegth="11"
            >
              <template #prefix>
                <icon-mobile />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item
            field="captcha_code"
            :rules="[
              { required: true, message: $t('login.form.captchaRequired') },
            ]"
            :validate-trigger="['change', 'blur']"
            hide-label
          >
            <a-input
              v-model="userInfo.captcha_code"
              :placeholder="$t('login.form.captchaPlaceholder')"
              :maxlegth="50"
            >
              <template #prefix>
                <icon-safe />
              </template>
              <template #append>
                <a-button
                  type="text"
                  :loading="smsLoading"
                  @click="getSMSCaptcha"
                >
                  <span v-if="countDown === -2" class="captcha-word">{{
                    $t('login.form.getCaptcha')
                  }}</span>
                  <span v-else-if="countDown === -1" class="captcha-word">{{
                    $t('login.form.regainCaptcha')
                  }}</span>
                </a-button>
                <span v-if="countDown >= 0" class="captcha-word">{{
                  `${countDown}s`
                }}</span>
              </template>
            </a-input>
          </a-form-item>
          <div class="option-box">
            <a-button type="primary" html-type="submit" :loading="loading">{{
              $t('login.form.confirmReplacement')
            }}</a-button>
            <a-link @click="changeLogin(LoginMethods.password)">{{
              $t('login.form.backLogin')
            }}</a-link>
          </div>
        </a-form>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { getSms } from '@/api/user';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import useLoading from '@/hooks/loading';
  import { changePhone } from '../api';
  import pwdEncrypt from '@/utils/encryption/pwd';
  import LoginMethods from '../constant';

  const emit = defineEmits(['changeLogin']);

  const changeLogin = (method: LoginMethods) => {
    emit('changeLogin', method);
  };

  const { t } = useI18n();
  const { loading, setLoading } = useLoading();

  const userInfo = reactive({
    username: '',
    password: '',
    phone: '',
    captcha_code: '',
  });

  // 存储验证码
  let captchaKey = '';
  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (loading.value) return;
    if (!errors) {
      setLoading(true);
      try {
        const data = {
          username: userInfo.username,
          password: pwdEncrypt(userInfo.password),
          phone: userInfo.phone,
          captcha: userInfo.captcha_code,
          key: captchaKey,
        };
        const res: any = await changePhone(data);
        changeLogin(LoginMethods.password);
        if (res.code === 8000000) {
          Message.success(res.message);
        }
      } catch (err: any) {
        if (err?.response?.status === 400) {
          Message.error(err.response.data);
        } else if (typeof err === 'string') {
          Message.error(err);
        }
      } finally {
        setLoading(false);
      }
    }
  };

  // 处理验证码
  const countDown = ref(-2);
  const smsLoading = ref(false);
  const loginForm = ref<FormInstance>();

  const getSMSCaptcha = async () => {
    if (countDown.value >= 0) {
      Message.warning(t('login.form.captchaHoldOn'));
      return;
    }
    const res = await loginForm.value?.validateField('phone');
    // 仅判断手机号是否校验通过
    if (res) {
      return;
    }
    smsLoading.value = true;
    try {
      countDown.value = 60;
      const { code, data, message } = await getSms(userInfo.phone);
      if (code === -8000040) {
        Message.info(message);
        countDown.value = 60 - data;
        updataCountDown();
      } else if (code === -1000105) {
        Message.info(t('login.form.all-too-often'));
        countDown.value = -2;
      } else {
        captchaKey = data;
        updataCountDown();
      }
    } catch (err) {
      // you can report use errorHandler or other
      if (typeof err === 'string') {
        Message.error(err);
      }
    } finally {
      smsLoading.value = false;
    }
  };

  const updataCountDown = () => {
    const counter = setInterval(() => {
      if (countDown.value === 0) {
        clearInterval(counter);
        countDown.value = -1;
      } else {
        countDown.value--;
      }
    }, 1000);
  };
</script>

<script lang="ts">
  export default {
    name: 'ChangePhone',
  };
</script>

<style lang="less" scoped>
  .option-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
