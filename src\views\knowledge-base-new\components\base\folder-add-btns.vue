<template>
  <a-dropdown
    v-model:popup-visible="popupVisible"
    position="bl"
    :hide-on-select="false"
    @select="handleSelect($event)"
  >
    <a-button
      v-if="isRoot"
      type="outline"
      size="small"
      style="margin-left: 16px"
    >
      <template #icon> <icon-plus /> </template
      >{{ t('knowledgenew.add') }}</a-button
    >
    <icon-plus v-else :size="14" style="color: #4e5969" />
    <template #content>
      <!-- 新建子文件夹 -->
      <a-doption v-if="hasEditPermission" value="add-folder">
        <template #icon>
          <icon-plus :size="16" style="color: #4e5969" />
        </template>
        <template #default>{{
          isRoot
            ? t('knowledgenew.add-folder')
            : t('knowledgenew.add-subfolder')
        }}</template>
      </a-doption>
      <!-- 上传文档 -->
      <a-doption v-if="hasUploadPermission" value="upload">
        <template #icon>
          <icon-upload :size="16" style="color: #4e5969" />
        </template>
        <template #default>{{ t('knowledgenew.upload-document') }}</template>
      </a-doption>
      <!-- 导入 -->
      <a-doption v-if="hasUploadPermission" value="import">
        <template #icon>
          <icon-import :size="16" style="color: #4e5969" />
        </template>
        <template #default>{{ t('knowledgenew.import') }}</template>
      </a-doption>
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
  import { computed, PropType, toRefs, defineEmits, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { storeToRefs } from 'pinia';
  import { useKnowledgeBaseStore2 } from '@/store';
  import { Node } from '../../types';

  const { t } = useI18n();

  const knowledgeBaseStore2 = useKnowledgeBaseStore2();
  const { baseInfo } = storeToRefs(knowledgeBaseStore2);

  const props = defineProps({
    isRoot: {
      type: Boolean,
      default: false,
    },
    nodeData: {
      type: Object as PropType<Node>,
      default: () => ({}),
    },
  });

  // 是否有上传权限
  const hasUploadPermission = computed(() => {
    return (
      baseInfo.value?.type === 'PERSONAL' ||
      baseInfo.value?.owner ||
      baseInfo.value?.upload
    );
  });

  // 是否有编辑权限
  const hasEditPermission = computed(() => {
    return (
      baseInfo.value?.type === 'PERSONAL' ||
      baseInfo.value?.owner ||
      baseInfo.value?.edit
    );
  });

  const emits = defineEmits(['eventsHandle']);

  const { nodeData } = toRefs(props);
  const popupVisible = ref(false);

  // dropdown事件委托
  const handleSelect = async (event: any) => {
    switch (event) {
      case 'add-folder':
        popupVisible.value = false;
        emits('eventsHandle', event, nodeData.value);
        break;
      case 'upload':
        popupVisible.value = false;
        emits('eventsHandle', event, nodeData.value);
        break;
      case 'import':
        popupVisible.value = false;
        emits('eventsHandle', event, nodeData.value);
        break;
      default:
        break;
    }
  };
</script>
