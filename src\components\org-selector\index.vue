<!-- 公司部门组件 -->
<template>
  <div class="fks-org-selector-page" :style="{ width }">
    <a-tree-select
      v-bind="$attrs"
      v-model="selected"
      :allow-search="true"
      :allow-clear="true"
      :data="treeData"
      :filter-tree-node="filterTreeNode"
      :fallback-option="fallback"
      :placeholder="$t('orgSelector.select')"
      :style="{ width }"
      :load-more="loadMore"
      :field-names="{ key: 'key', title: 'title', children: 'children' }"
      class="org-tree-select"
      @change="handleChange"
      @clear="clear"
    ></a-tree-select>
  </div>
</template>

<script lang="ts" setup>
  // TODO: 不支持多选，未加入deptName
  import { TreeNodeData } from '@arco-design/web-vue/es/tree/interface';
  import { ref, watch } from 'vue';
  import { getDepartmentList } from '@/api/modules/department';
  import { LabelValue } from '@arco-design/web-vue/es/tree-select/interface';

  interface CustomTreeNodeData extends TreeNodeData {
    hasChildren?: boolean;
    content?: {
      orgNo?: number | string;
      name?: string;
      parentName?: string;
      pathName?: string;
      pathNo?: string | number;
    };
  }

  const props = defineProps({
    width: {
      type: String,
      default: '100%',
    },
    urlPath: {
      type: String,
      default: '/cde-collaboration/sys-user/orgs/tree',
    },
    deptId: {
      type: String,
      default: '0',
    },
    deptName: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits([
    'update:deptId',
    'update:deptName',
    'change',
    'ready',
    'clear',
  ]);
  const selected = ref(props.deptId);
  const treeData = ref<CustomTreeNodeData[]>([]);

  watch(
    () => props.deptId,
    (n) => {
      selected.value = n;
    }
  );

  const fallback = (key: string | number): CustomTreeNodeData | boolean => {
    return { key, title: '根节点' };
  };

  // 获取初始数据
  const fetchData = async () => {
    try {
      const { data } = await getDepartmentList({ parentNo: 0 });
      const newData = (data || []).map((item: any) => ({
        key: item.orgNo?.toString() || '',
        title: item.name || '',
        isLeaf: false,
        children: [],
        hasChildren: true,
      }));
      // 使用新数组替换旧数组，确保视图更新
      treeData.value = [...newData];
      emit('ready', true);
    } catch (err) {
      console.log(err);
    }
  };

  // 加载更多数据
  const loadMore = async (record: CustomTreeNodeData) => {
    return new Promise<CustomTreeNodeData[]>((resolve) => {
      getDepartmentList({ parentNo: Number(record.key) })
        .then(({ data }) => {
          if (data && data.length > 0) {
            const children = data.map((item: any) => ({
              key: item.orgNo?.toString() || '',
              title: item.name || '',
              isLeaf: false,
              children: [],
              hasChildren: true,
            }));
            // 直接更新当前节点的children
            record.children = children;
            resolve(children);
          } else {
            record.isLeaf = true;
            record.hasChildren = false;
            record.children = [];
            resolve([]);
          }
        })
        .catch((err) => {
          console.log(err);
          record.isLeaf = true;
          record.hasChildren = false;
          record.children = [];
          resolve([]);
        });
    });
  };

  // 根据name过滤数据
  const filterTreeNode = (
    searchValue: string,
    nodeData: CustomTreeNodeData
  ) => {
    const title = nodeData.title || '';
    return title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  };

  const clear = () => {
    selected.value = '';
    emit('update:deptId', '');
    emit('update:deptName', '');
    emit('clear');
  };

  const handleChange = async (value: string) => {
    console.log('选择的值:', value);
    if (!value) {
      clear();
      return;
    }

    try {
      // 从树数据中查找选中的节点
      const findNode = (nodes: CustomTreeNodeData[]): CustomTreeNodeData | undefined => {
        const found = nodes.find(node => node.key === value);
        if (found) return found;
        
        return nodes.reduce<CustomTreeNodeData | undefined>((result, node) => {
          if (result) return result;
          if (node.children && node.children.length > 0) {
            return findNode(node.children);
          }
          return undefined;
        }, undefined);
      };

      const selectedNode = findNode(treeData.value);
      console.log('从树中查找的节点:', selectedNode);

      if (selectedNode) {
        const path = {
          key: selectedNode.key,
          title: selectedNode.title,
          content: {
            orgNo: selectedNode.key,
            name: selectedNode.title,
            parentName: selectedNode.title,
            pathName: selectedNode.title,
            pathNo: selectedNode.key
          }
        };
        console.log('发送的数据:', path);
        emit('update:deptId', value);
        emit('update:deptName', selectedNode.title);
        emit('change', path);
      }
    } catch (err) {
      console.error('获取节点数据失败:', err);
    }
  };

  // 获取节点的完整路径
  const getNodePath = async (nodeId: string): Promise<CustomTreeNodeData | null> => {
    try {
      const { data } = await getDepartmentList({ parentNo: Number(nodeId) });
      const node = data?.find((item: any) => item.orgNo?.toString() === nodeId);
      if (node) {
        return {
          key: node.orgNo?.toString() || '',
          title: node.name || '',
          content: {
            orgNo: node.orgNo,
            name: node.name,
            parentName: node.parentName,
            pathName: node.pathName,
            pathNo: node.pathNo
          }
        };
      }
      return null;
    } catch (err) {
      console.error('获取节点路径失败:', err);
      return null;
    }
  };

  fetchData();
</script>

<script lang="ts">
  // <span>Fallthrough attribute: {{ $attrs }}</span>
  // 使用普通的 <script> 来声明选项
  export default {
    name: 'OrgSelector',
    inheritAttrs: false,
  };
</script>

<style lang="less" scoped>
  .fks-org-selector-page {
    :deep(.org-tree-select) {
      max-width: 370px;
    }
    :deep(.org-tree-select) {
      .arco-tree-node-title {
        white-space: normal;
        word-break: break-all;
        line-height: 1.5;
        padding: 4px 0;
      }

      .arco-select-view {
        max-width: 100%;
      }

      .arco-select-dropdown {
        max-width: 100%;
      }

      .arco-tree-node {
        padding: 4px 0;
      }
    }

    .readonly-input {
      :deep(.arco-input) {
        background-color: var(--color-fill-2);
        cursor: not-allowed;
      }
    }
  }
</style>
