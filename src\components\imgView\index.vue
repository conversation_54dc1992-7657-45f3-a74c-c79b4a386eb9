<template>
  <a-image-preview v-model:visible="model.visible" :src="imgUrl" />
</template>

<script lang="ts" setup>
  import { computed, defineProps, PropType, toRefs } from 'vue';

  const props = defineProps({
    viewModal: {
      type: Object as PropType<{
        fileToken: string;
        visible: boolean;
        title: string;
      }>,
      required: true,
    },
  });
  const { viewModal: model } = toRefs(props);
  const imgUrl = computed(
    () => `/work/api/sys-storage/download_image?f8s=${model.value.fileToken}`
  );
</script>

<script lang="ts">
  export default {
    name: 'ImgViewer',
    inheritAttrs: false,
  };
</script>
