<template>
  <div class="base-content">
    <div class="scroll-container">
      <div class="base-header">
        <span class="title">{{ $t('knowledge.personal-knowledge') }}</span>
        <div class="path-container">
          <div class="btn-box">
            <arrowLeft
              :class="canGoBack ? 'has-pointer' : 'btn-disabled'"
              @click="goBack"
            />
          </div>
          <div class="btn-box">
            <arrowRight
              :class="canGoForward ? 'has-pointer' : 'btn-disabled'"
              @click="goForward"
            />
          </div>

          <a-breadcrumb :max-count="3">
            <template #separator>
              <icon-right />
            </template>

            <a-breadcrumb-item
              v-for="(item, index) in currentPath"
              :key="item.id"
              class="has-pointer"
              @click="jumpToFolder(item, index)"
              >{{ item.name }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <a-space>
          <a-input-search
            v-model="searchVal"
            style="width: 250px"
            :placeholder="$t('knowledge.need-place-input')"
            allow-clear
            @change="searchHandle"
            @search="searchHandle"
            @keydown.enter="searchHandle"
          />

          <a-upload
            action="/"
            multiple
            :show-file-list="false"
            :auto-upload="false"
            :file-list="selectedFileList"
            @change="beforeUpload"
          >
            <template #upload-button>
              <div ref="uploadRef"></div>
            </template>
          </a-upload>
          <a-button @click="uploadClick">{{
            $t('knowledge.upload-file')
          }}</a-button>
          <a-button type="primary" @click="createFolderBtnClick">{{
            $t('knowledge.upload-folders')
          }}</a-button>
        </a-space>
      </div>

      <div class="file-list">
        <div v-if="isBaseEmpty" class="empty-file">
          <!-- <UploadProgress /> -->
          <!-- :upload-list="selectedFileList" -->
          <emptyFile />
          <span>{{ $t('knowledge.before-chat-tips') }}</span>
        </div>
        <a-table
          v-else
          :columns="columns"
          :data="tableData"
          row-key="id"
          :bordered="false"
          :loading="tableLoading"
          :scroll="{
            y: '100%',
          }"
          scrollbar
          :pagination="false"
          row-class="custom-table-row"
          @sorter-change="handleSortChange"
        >
          <template #empty>
            <a-space
              v-if="!tableData.length"
              :size="16"
              direction="vertical"
              class="empty-container"
            >
              <EmptyFolder />
              <span>{{ $t('knowledge.no-result') }}</span>
            </a-space>
          </template>
          <template #name="{ record, rowIndex }">
            <div
              class="file-name-column"
              :class="fileId === record.id ? 'search-file' : ''"
              @mouseenter="showOptions(record)"
              @mouseleave="hideOptions(record)"
            >
              <img
                :src="getFileIcon(record.type)"
                style="width: 28px; height: 28px"
              />
              <div class="name-box">
                <div
                  v-if="getDisplayMode(record).showText"
                  class="name-text-wrapper"
                >
                  <span
                    :title="record.name"
                    class="has-pointer name-text"
                    @click="handleOpenFileOrFolder(record)"
                    >{{ record.name }}</span
                  >
                </div>
                <div
                  v-if="getDisplayMode(record).showInput"
                  class="input-wrapper"
                >
                  <a-input
                    ref="newFolderNameRef"
                    v-model="record.name"
                    :placeholder="$t('knowledge.please-input-folder-name')"
                    allow-clear
                    class="name-input"
                    @blur="handleCreateOrRenameFolder(record)"
                    @press-enter="($event.target as any)?.blur()"
                  />
                </div>
              </div>
              <a-space style="width: auto; justify-content: flex-end">
                <editIcon
                  v-if="getDisplayMode(record).showOptions"
                  class="has-pointer"
                  @click="editBtnClick(record)"
                />
                <downloadIcon
                  v-if="getDisplayMode(record).showOptions"
                  class="has-pointer"
                  @click="downloadBtnClick(record)"
                />

                <a-popconfirm
                  :content="
                    record.isFolder
                      ? $t('knowledge.confirm-delete-folder')
                      : $t('knowledge.confirm-delete-file')
                  "
                  @ok="handleDelete(record, rowIndex)"
                  @popup-visible-change="popupVisibleChange($event, record)"
                >
                  <icon-delete
                    v-if="getDisplayMode(record).showOptions || record.isNew"
                    :size="16"
                    class="has-pointer icon-color"
                    @click="record.isDelete = true"
                  />
                </a-popconfirm>
                <IconSwap
                  v-if="getDisplayMode(record).showOptions && !record.isNew"
                  style="color: rgb(22, 93, 255); cursor: pointer"
                  @click="moveFile(record)"
                />
              </a-space>
            </div>
          </template>
          <template #size="{ record }">
            <span v-if="record.isFolder">-</span>
            <template v-else>
              <!-- <span class="process">{{ record.process + '/' }}</span> -->
              <span class="size">{{ getFileSize(record.size) }}</span>
            </template>
          </template>
          <template #type="{ record }">
            {{ record.type.toLowerCase() }}
          </template>
          <template #aiStatus="{ record }">
            <div v-if="record.aiStatus" class="file-aiStatus">
              <a-tag :color="AiStatusColorMap[record.aiStatus] || 'gray'">{{
                AiStatusMap[record.aiStatus] || ''
              }}</a-tag>
            </div>
          </template>
          <template #updateDate="{ record }">
            {{ record.updateDate ? record.updateDate.slice(0, -3) : '' }}
          </template>
        </a-table>

        <!-- <div
      v-for="node in treeData"
      :key="node.id"
      class="file-node"
      :class="{ 'is-folder': node.isFolder }"
      draggable="true"
      @dragstart="onDragStart(node, $event)"
      @dragover="onDragOver(node, $event)"
      @dragend="onDragEnd"
      @drop="onDrop(node, $event)"
    >
      <div class="node-content">
        <span class="icon">{{ node.isFolder ? '📁' : '📄' }}</span>
        {{ node.name }}

        <div v-if="node.children" class="children">
          <FileTreeNode :nodes="node.children" />
        </div>
      </div>
    </div> -->
        <!-- </div> -->
      </div>
      <div class="question-box">
        <AIInput
          v-if="!isBaseEmpty && hasFile && baseRagId"
          :placeholder="$t('knowledge.question-tips')"
          :loading="isLoading"
          @submit="handleAIQuestion"
        />
      </div>
    </div>
    <MoveFileModal
      v-model:visible="moveFileVisible"
      :files="moveFiles"
      @move-file-success="moveFileSuccess"
      @refresh="queryFolderContent(() => clearUIState())"
    ></MoveFileModal>
    <UploadPanel
      v-model:visible="uploadModalVisible"
      :files="selectedFileList"
      :position="{
        top: 210,
        right: 70,
      }"
      :show-success-msg="false"
      @upload-single-success="uploadSuccessCb"
      @finish="selectedFileList = []"
    ></UploadPanel>
  </div>
</template>

<script lang="ts" setup>
  import { computed, nextTick, onMounted, ref, toRaw, watch } from 'vue';
  import { storeToRefs } from 'pinia';
  import { Message } from '@arco-design/web-vue';
  import { useKnowledgeBaseStore, useUploadFileStore } from '@/store';
  import { wpsViewHandle } from '@/hooks/wps';
  import AIInput from './ai-input.vue';
  import MoveFileModal from './move-file-modal.vue';
  import UploadPanel from '@/components/upload-panel/index.vue';
  import {
    createFolder,
    renameFolder,
    deleteFolder,
    deleteFile,
    saveFile,
    getFolderPath,
  } from '../api';
  import { getFileSize } from '@/utils/file';
  import getFileType from '../utils';
  import { Node, CustomFileItem } from '../types';
  import { PathNode } from '@/store/modules/knowledge-base/types';
  import useFolderContent from '../composables/useFolderContent';
  import useUIState from '../composables/useUIState';
  import acceptFileType from '@/config/accept-file-types.json';
  import { AiStatusMap, AiStatusColorMap } from '../aiStatus-map';
  import editIcon from '@/assets/images/knowledge-base/edit.svg';
  import downloadIcon from '@/assets/images/knowledge-base/download.svg';
  import emptyFile from '@/assets/images/knowledge-base/empty-file.svg';
  import arrowLeft from '@/assets/images/knowledge-base/arrow-left.svg';
  import arrowRight from '@/assets/images/knowledge-base/arrow-right.svg';
  import EmptyFolder from '@/assets/images/knowledge-base/empty-folder.svg';
  import i18n from '@/locale/index';

  const { t } = i18n.global;
  const knowledgeBaseStore = useKnowledgeBaseStore();

  const {
    baseRagId,
    folderId,
    history,
    currentIndex,
    isBaseEmpty,
    hasFile,
    isSearch,
    fileId,
    baseRootId,
  } = storeToRefs(knowledgeBaseStore);

  const uploadFileStore = useUploadFileStore();

  const newFolderNameRef = ref<HTMLInputElement | null>(null);

  const emit = defineEmits(['sendQuestion']);

  const {
    tableData,
    folderList,
    fileList,
    tableLoading,
    queryFolderContent,
    sortByUpdateDate,
    openFileOrFolder,
    downloadBtnClick,
    getFileIcon,
    checkFolderName,
    getSearchFilehandle,
  } = useFolderContent();

  // 获取UI状态用于显隐控制，清空UI状态
  const { getItemState, clearUIState } = useUIState();

  const columns = [
    {
      title: t('knowledge.file-name'),
      dataIndex: 'name',
      slotName: 'name',
      ellipsis: true,
      tooltip: true,
      width: 520,
      headerCellClass: 'custom-header',
    },
    {
      title: t('knowledge.file-size'),
      dataIndex: 'size',
      slotName: 'size',
      ellipsis: true,
      tooltip: true,
      width: 260,
      headerCellClass: 'custom-header',
    },
    {
      title: t('knowledge.file-type'),
      dataIndex: 'type',
      slotName: 'type',
      ellipsis: true,
      tooltip: true,
      minWidth: 160,
      headerCellClass: 'custom-header',
    },
    {
      title: t('knowledge.ai-analyze-state'),
      dataIndex: 'aiStatus',
      slotName: 'aiStatus',
      ellipsis: true,
      tooltip: true,
      minWidth: 160,
      headerCellClass: 'custom-header',
    },
    {
      title: t('knowledge.update-time'),
      dataIndex: 'updateDate',
      slotName: 'updateDate',
      ellipsis: true,
      tooltip: true,
      sortable: {
        sorter: true,
        sortDirections: ['ascend', 'descend'],
      },
      minWidth: 220,
      headerCellClass: 'custom-header',
    },
  ];

  /** 搜索结果展示begin */
  // const searchFileId = ref('');
  const afterSearch = () => {
    clearUIState();
    // console.log(fileId.value);
  };

  // 搜索结果的完整路径
  const searchFullPath = ref<Array<PathNode>>([]);

  // 监听是否是搜索状态
  watch(
    () => isSearch.value,
    (nValue) => {
      if (nValue) {
        // searchFileId.value = fileId.value;
        queryFolderContent(afterSearch);
        getFolderPath({
          folderId: folderId.value,
          fullTree: true,
        }).then((res) => {
          if (res.status) {
            searchFullPath.value = res.data;
            knowledgeBaseStore.setSearchFullPath(searchFullPath.value);
          }
        });
      }
    },
    {
      immediate: true,
    }
  );
  /** 搜索结果展示end */

  const isLoading = ref(false);

  // 向AI发送消息
  const handleAIQuestion = async (question: any) => {
    emit('sendQuestion', question);
  };

  // 当前显示路径
  const currentPath = computed(() => {
    return history.value.slice(1, currentIndex.value + 1);
  });

  // 是否可后退
  const canGoBack = computed(() => currentIndex.value > 0);
  // 是否可前进
  const canGoForward = computed(
    () => currentIndex.value < history.value.length - 1
  );

  // 导航方法
  const goBack = () => {
    if (currentIndex.value > 0) {
      const newFolderId = history.value[currentIndex.value - 1].id;
      knowledgeBaseStore.changePath(newFolderId, currentIndex.value - 1);
    }
  };
  const goForward = () => {
    if (currentIndex.value < history.value.length - 1) {
      const newFolderId = history.value[currentIndex.value + 1].id;
      knowledgeBaseStore.changePath(newFolderId, currentIndex.value + 1);
    }
  };

  // 点击面包屑的路径
  const jumpToFolder = (item: PathNode, index: number) => {
    knowledgeBaseStore.changePath(item.id, index + 1);
  };

  // 打开文件夹/查看文件
  const handleOpenFileOrFolder = (item: Node) => {
    openFileOrFolder(item);
  };

  // 排序
  const handleSortChange = (dataIndex: string, direction: string) => {
    if (direction === 'descend') {
      sortByUpdateDate(folderList.value, false);
      sortByUpdateDate(fileList.value, false);
      tableData.value = [...folderList.value, ...fileList.value];
    } else if (direction === 'ascend') {
      sortByUpdateDate(folderList.value, true);
      sortByUpdateDate(fileList.value, true);
      tableData.value = [...folderList.value, ...fileList.value];
    }
  };

  // 显示控制的计算属性
  const getDisplayMode = computed(() => {
    return (record: Node) => {
      const state = getItemState(record.id);
      return {
        showText: !state.isEditing,
        showInput: state.isEditing,
        showOptions: state.showOptions || state.isEditing || state.isDeleting,
      };
    };
  });

  // 鼠标悬停事件处理
  const showOptions = (record: Node) => {
    const state = getItemState(record.id);
    if (!state.isEditing && !state.isDeleting) {
      state.showOptions = true;
    }
  };

  const hideOptions = (record: Node) => {
    const state = getItemState(record.id);
    if (!state.isEditing && !state.isDeleting) {
      state.showOptions = false;
    }
  };

  // 暂存编辑的文件夹节点信息
  const editingFolder = ref<Node | null>(null);

  // 编辑按钮点击处理
  const editBtnClick = (record: Node) => {
    if (record.isFolder) {
      const state = getItemState(record.id);
      state.isEditing = true;
      editingFolder.value = { ...record };
      nextTick(() => {
        newFolderNameRef.value?.focus();
      });
    } else {
      wpsViewHandle(record, 'edit', 'admin');
    }
  };

  // 删除确认弹窗处理
  const popupVisibleChange = (visible: boolean, record: Node) => {
    const state = getItemState(record.id);
    state.isDeleting = visible;
    if (!visible && !state.isEditing) {
      state.showOptions = false;
    }
  };

  // 新建文件夹处理
  const createFolderBtnClick = () => {
    const newId = Date.now().toString();
    const newFolder = {
      id: newId,
      name: '',
      parentId: folderId.value,
      type: '文件夹',
      updateDate: '',
      isFolder: true,
      isNew: true,
    };

    tableData.value.unshift(newFolder);
    const state = getItemState(newId);
    state.isEditing = true;

    knowledgeBaseStore.setIsBaseEmpty(false);

    nextTick(() => {
      newFolderNameRef.value?.focus();
    });
  };

  // 文件夹创建/重命名处理
  const handleCreateOrRenameFolder = async (item: Node) => {
    const name = item.name.trim();

    if (
      !item.isNew &&
      editingFolder.value &&
      item.id === editingFolder.value.id &&
      name === editingFolder.value.name
    ) {
      const state = getItemState(item.id);
      state.isEditing = false;
      state.showOptions = false;
      editingFolder.value = null;
      return;
    }

    const isNameValid = await checkFolderName(name);
    if (!isNameValid) {
      return;
    }

    try {
      if (item.isNew) {
        const res = await createFolder({
          name,
          parentId: item.parentId,
        });

        if (res.status) {
          const state = getItemState(item.id);
          state.isEditing = false;
          state.showOptions = false;
          Message.success(t('knowledge.create-folder-success-msg'));
          await queryFolderContent(() => clearUIState());
        }
      } else if (editingFolder.value && name !== editingFolder.value.name) {
        const res = await renameFolder({
          folderId: item.id,
          name,
        });

        if (res.status) {
          const state = getItemState(item.id);
          state.isEditing = false;
          state.showOptions = false;
          Message.success(t('knowledge.rename-folder-success-msg'));
          await queryFolderContent(() => clearUIState());
        }
      } else {
        const state = getItemState(item.id);
        item.name = name;
        state.isEditing = false;
        state.showOptions = false;
      }
    } catch (error) {
      Message.error(t('knowledge.operate-fail-msg'));
      console.error('Folder operation error:', error);
    }
  };

  // 确认删除
  const handleDelete = async (item: Node, index: number) => {
    if (item.isNew) {
      // tableData.value.splice(index, 1);
      // clearUIState();
      await queryFolderContent(() => clearUIState());
    } else if (item.isFolder) {
      // 删除文件夹
      const res = await deleteFolder({ folderId: item.id || '' });
      if (res.status) {
        knowledgeBaseStore.setNeedRefresh(true);
        await queryFolderContent(() => clearUIState());
        Message.success(t('knowledge.delete-folder-success-msg'));
      }
    } else {
      // 删除文件
      const res = await deleteFile({ fileId: item.id });
      if (res.status) {
        await queryFolderContent(() => clearUIState());
        Message.success(t('knowledge.delete-folder-success-msg'));
        knowledgeBaseStore.setNeedRefresh(true);
      }
    }
  };

  // 监听 folderId 变化时也需要清空状态
  watch(
    () => folderId.value,
    async (nValue) => {
      // console.log('watch folderId, isSearch: ', nValue, isSearch.value);
      if (nValue && !isSearch.value) {
        queryFolderContent(() => clearUIState());
      }
    },
    {
      immediate: true,
    }
  );

  /** 上传文件begin */
  const uploadModalVisible = ref(false);
  const uploadRef = ref(null);
  // 上传文件列表
  const selectedFileList = ref([]);
  const fileSizeLimit = 20; // 单位MB

  const uploadClick = () => {
    if (uploadFileStore.uploadFileList.length) {
      uploadModalVisible.value = true;
    } else {
      uploadRef.value?.click();
    }
  };
  // 文件保存逻辑
  const handleSaveFile = async (fileItem: CustomFileItem) => {
    try {
      const params = {
        fileSize: fileItem.size,
        folderId: folderId.value,
        name: fileItem.name,
        ossToken: fileItem.fileToken || '',
        type: getFileType(fileItem.name),
      };

      const res = await saveFile(params);
      if (res.status) {
        await queryFolderContent(() => clearUIState());
        // Message.success('文件上传成功！');
        knowledgeBaseStore.setNeedRefresh(true);
      }
    } catch (error) {
      Message.error(t('knowledge.save-folder-fail'));
      console.error('Save file error:', error);
    }
  };

  // 单个文件上传成功回调
  const uploadSuccessCb = (file: any) => {
    handleSaveFile(file);
  };
  // 上传前校验
  const beforeUpload = (fileArr: any) => {
    const files = fileArr.map((fileItem: any) => {
      const fileA = fileItem.file;
      const suffix = fileA.name.split('.').pop()?.toLowerCase() || '';
      if (!acceptFileType.includes(suffix)) {
        Message.error(
          `${t('knowledge.unsupport-upload')} ${suffix} ${t(
            'knowledge.types-file'
          )}！`
        );
        return false;
      }
      // // 文件大小验证
      if (fileA.size > fileSizeLimit * 1024 * 1024) {
        Message.error(
          `${t('knowledge.file-size-no-exceed')} ${fileSizeLimit}MB！`
        );
        return false;
      }
      return fileA;
    });

    selectedFileList.value = files.filter((item: any) => {
      let result = false;
      if (item) result = true;
      return result;
    });
    if (!uploadModalVisible.value && selectedFileList.value.length)
      uploadModalVisible.value = true;
  };

  /** 上传文件end */

  /** 移动文件相关逻辑 */
  const moveFileVisible = ref(false);
  const moveFiles = ref([]);
  const moveFile = (record: any) => {
    moveFiles.value = [toRaw(record)];
    moveFileVisible.value = true;
  };
  const moveFileSuccess = () => {
    queryFolderContent(() => clearUIState());
    moveFiles.value = [];
  };
  /** 移动文件逻辑结束 */

  // const isSearch = ref(false);
  const searchVal = ref(''); // 搜索值

  // 搜索文件
  const searchHandle = async () => {
    if (searchVal.value) {
      const data = {
        fileName: searchVal.value,
        personBaseId: baseRootId.value,
      };
      getSearchFilehandle(data);
    } else await queryFolderContent(() => clearUIState());
  };

  onMounted(() => {});
  /** 拖拽begin */
  //   // 文件树数据结构
  //   const treeData = ref([
  //     {
  //       id: 1,
  //       name: '我的文件',
  //       isFolder: true,
  //       children: [
  //         { id: 2, name: '文档2.pdf', isFolder: false },
  //         {
  //           id: 3,
  //           name: '图片3',
  //           isFolder: true,
  //           children: [{ id: 4, name: 'photo4.jpg', isFolder: false }],
  //         },
  //       ],
  //     },
  //     { id: 5, name: '文档5.pdf', isFolder: false },
  //     { id: 6, name: '文档6.pdf', isFolder: false },
  //   ]);

  //   // 拖拽状态管理
  //   const dragState = ref({
  //     isDragging: false,
  //     sourceNode: null,
  //     targetNode: null,
  //   });

  //   // 工具函数：判断是否子节点
  //   const isDescendant = (parent, child) => {
  //     if (!parent.children) return false;
  //     const queue = [...parent.children];
  //     while (queue.length) {
  //       const node = queue.shift();
  //       if (node.id === child.id) return true;
  //       if (node.children) queue.push(...node.children);
  //     }
  //     return false;
  //   };

  //   // 工具函数：查找父节点
  //   const findParent = (nodes, targetId, parent = null) => {
  //     for (let i = 0; i < nodes.length; i++) {
  //       const node = nodes[i];
  //       if (node.id === targetId) return parent;
  //       if (node.children) {
  //         const found = findParent(node.children, targetId, node);
  //         if (found) return found;
  //       }
  //     }

  //     return null;
  //   };

  //   // 核心移动逻辑
  //   const moveNode = (sourceNode, targetFolder) => {
  //     // 禁止移动到自身或子目录
  //     if (isDescendant(targetFolder, sourceNode)) return;

  //     // 从原位置删除
  //     const oldParent = findParent(treeData.value, sourceNode.id);
  //     if (oldParent) {
  //       oldParent.children = oldParent.children.filter(
  //         (n) => n.id !== sourceNode.id
  //       );
  //     } else {
  //       // 从根目录下删除
  //     }

  //     // 添加到新位置
  //     if (!targetFolder.children) targetFolder.children = [];
  //     targetFolder.children.push(sourceNode);
  //   };

  //   // 拖拽开始事件
  //   const onDragStart = (node, event) => {
  //     if (!node.isFolder) {
  //       dragState.value = {
  //         isDragging: true,
  //         sourceNode: node,
  //         targetNode: null,
  //       };
  //       event.dataTransfer.setData('text/plain', node.id);
  //     }
  //   };

  //   // 拖拽悬停事件（实现视觉反馈）
  //   const onDragOver = (node, event) => {
  //     if (node.isFolder && dragState.value.sourceNode?.id !== node.id) {
  //       event.preventDefault();
  //       document.querySelectorAll('.file-node').forEach((el) => {
  //         el.classList.remove('drag-over');
  //       });
  //       event.currentTarget.classList.add('drag-over');
  //     }
  //   };

  //   // 拖拽结束事件
  //   const onDragEnd = () => {
  //     dragState.value.isDragging = false;
  //     document.querySelectorAll('.file-node').forEach((el) => {
  //       el.classList.remove('drag-over');
  //     });
  //   };

  //   // 放置处理逻辑
  //   const onDrop = (targetNode, event) => {
  //     event.preventDefault();
  //     if (targetNode.isFolder) {
  //       moveNode(dragState.value.sourceNode, targetNode);
  //     }
  //     onDragEnd();
  //   };
  //* *拖拽end */
</script>

<style scoped lang="less">
  .base-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: auto;

    .base-header {
      padding: 0 20px;
      margin-bottom: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        margin-right: 20px;
        font-size: 20px;
        font-weight: 500;
        line-height: 32px;
      }
      :deep(.arco-input-wrapper) {
        background-color: #fff;
        border: 1px solid #c9cdd4;
        border-radius: 8px !important;
      }
    }
  }

  .scroll-container {
    min-height: 600px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  :deep(.has-pointer) {
    cursor: pointer;
  }

  .icon-color {
    color: #3366ff;
  }

  .edit-btn {
    display: none;
  }

  .btn-disabled {
    cursor: not-allowed;
  }

  .demo-basic {
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
    padding: 10px;
    width: 350px;
    height: 400px;
    background-color: var(--color-bg-popup);
    border-radius: 4px;
  }

  .path-container {
    flex: 1;
    display: flex;
    align-items: center;
    height: 24px;
    // overflow: hidden;

    .btn-box {
      margin-right: 8px;
      display: inline-block;
      width: 24px;
      height: 24px;
      display: inline-block;
      line-height: 24px;
      text-align: center;
    }
  }

  .file-manager {
    height: 100%;
    width: 100%;
  }

  .file-list {
    flex: 1;
    overflow: hidden;
  }

  .empty-container {
    height: calc(100vh - 460px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    span {
      font-size: 14px;
      line-height: 24px;
      color: #4e5969;
    }
  }

  .file-name-column {
    height: 42px;
    display: flex;
    align-items: center;

    .name-box {
      position: relative;
      height: 32px;
      flex: 1;
      margin-right: 20px;
      margin-left: 13px;

      .name-text-wrapper,
      .input-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      .name-text {
        display: block;
        width: 100%;
        height: 100%;
        line-height: 32px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .name-text:hover {
        color: #3366ff;
      }
    }
  }

  .process {
    color: #86909c;
  }
  .empty-file {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    span {
      margin-top: 16px;
      font-size: 16px;
      line-height: 24px;
      color: #4e5969;
    }
  }

  .question-box {
    margin-top: 20px;
    min-height: 60px;
  }

  /**拖拽begin */
  .file-node {
    padding: 8px;
    margin: 4px 0;
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s;
  }

  .file-node.is-folder {
    background-color: #f5f5f5;
  }

  .drag-over {
    background-color: #e6f7ff !important;
    box-shadow: 0 0 8px rgba(24, 144, 255, 0.2);
  }

  .node-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .children {
    margin-left: 20px;
    border-left: 2px solid #ddd;
    padding-left: 10px;
  }

  .icon {
    font-size: 1.2em;
  }
  /**拖拽end */
  :deep(.arco-table) {
    .arco-table-cell {
      overflow: hidden !important;
      padding-left: 20px;
    }
  }

  :deep(.arco-table-tr:has(.search-file) .arco-table-td) {
    // background-color: rgba(232, 242, 255, 0.4) !important; // TODO
  }

  // table行hover背景色
  :deep(.arco-table-tr:hover:not(.arco-table-tr-empty)) {
    .arco-table-td {
      background-color: rgba(232, 242, 255, 0.4) !important;
    }

    .edit-btn {
      display: block !important;
    }
  }

  :deep(.arco-table-td) {
    font-size: 16px !important;
    font-weight: 500;
    line-height: 24px;
    color: #4e5969;
    border-bottom: none;
  }

  :deep(.custom-header) {
    border-top: 1px solid #ededed;
    border-bottom: 1px solid #ededed;
    border-left: 1px solid #ededed;
    .arco-table-cell {
      padding-top: 10px;
      padding-bottom: 6px;
      padding-left: 20px;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #4e5969;
    }
  }

  :deep(.custom-header:first-of-type) {
    border-left: none;
  }

  :deep(.arco-table-th) {
    background-color: white;
  }

  :deep(.arco-input-wrapper) {
    transition: none !important;
  }

  :deep(.arco-breadcrumb) {
    flex: 1;
  }
  :deep(.arco-breadcrumb-item) {
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
