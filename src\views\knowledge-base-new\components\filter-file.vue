<template>
  <a-input-search
    v-model="filterForm.name"
    :placeholder="$t('file-manage.enter-name')"
    @search="handleChange"
    @keydown.enter="handleChange"
    @input="changeInput"
    style="
      background-color: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px 4px 4px 4px;
    "
  />
</template>

<script lang="ts" setup>
  import { PropType, reactive, ref, toRefs, defineProps } from 'vue';
  import useFileStore from '@/store/modules/file/index';
  import { storeToRefs } from 'pinia';
  import { getSearchData } from '@/views/projectSpace/file/api';
  import { cloneDeep } from 'lodash';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';

  const knowledgeStore = useKnowledgeBaseNewStore();
  const { referenceModal } = storeToRefs(knowledgeStore);
  const { selectedKeys, expandedKeys, currentFolder } = toRefs(
    referenceModal.value
  );

  const fileStore = useFileStore();
  // const { currentFolder } = storeToRefs(fileStore);

  const filterForm = reactive({ name: '' });
  const FOLDER_TYPE = ['WIP', 'SHARED', 'PUBLISHED'];

  async function setCurrentFolderSearch() {
    // 有搜索条件 通过搜索接口获取
    const folderIds = [];
    if (filterForm.name) {
      // 顶级文件夹需要传入所以子文件夹id
      if (FOLDER_TYPE.includes(currentFolder.value.name!)) {
        currentFolder.value.children?.forEach((item) => {
          folderIds.push(item.id);
        });
      } else {
        folderIds.push(currentFolder.value.id);
      }
      const param: any = {
        folderIds,
        pageParam: {
          pageNo: 1,
          pageSize: 99999,
        },
        search: filterForm.name,
      };
      const res = await getSearchData(param);
      const searchData = res.data ? res.data.list : [];
      // 添加是否是文件字段 isFileOrFolder为1：文件 0：文件夹
      searchData.forEach((item: any) => {
        if (!item.folderId) item.isFileOrFolder = 0;
        else {
          item.isFileOrFolder = 1;

          // 对文件夹添加上级文件夹名称
          if (!item.path) return;
          const pathArr = item.path.split('/');
          const folderName =
            pathArr.length > 1 ? pathArr[pathArr.length - 2] : '';
          item.folderName = folderName;
        }
      });

      knowledgeStore.setTableData(searchData);
    } else {
      // 无搜索条件使用树上点击的方式获取数据
      knowledgeStore.setCurrentFolder(cloneDeep(currentFolder.value));
    }
  }

  const handleChange = () => {
    setCurrentFolderSearch();
  };
  // 搜索为空时 请求数据
  const changeInput = (value: any) => {
    if (!value) setCurrentFolderSearch();
  };
  function clearSearch() {
    filterForm.name = '';
  }

  defineExpose({
    clearSearch,
  });
</script>

<style lang="less" scoped>
  :deep(.arco-form-item) {
    margin-bottom: 0;
  }
</style>
