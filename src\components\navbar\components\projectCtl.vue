<template>
  <a-dropdown
    class="prj-dropdown"
    position="bl"
    :popup-max-height="500"
    @select="projectChange"
    @popup-visible-change="projectListShow"
  >
    <a-button class="project-select">
      <span class="project-name">
        {{ currenPrjName }}
      </span>
      <icon-down :size="16" />
    </a-button>
    <template #content>
      <div class="project-list">
        <a-tabs>
          <a-tab-pane
            key="1"
            :title="
              projectTemplate === '1'
                ? $t('settings.navbar.project_template')
                : $t('settings.navbar.project')
            "
          >
            <div class="pro-tab-wrap">
              <div class="search-wrap">
                <a-input-search
                  v-model="searchValue"
                  :placeholder="$t('navbar.logo.enter')"
                  allow-clear
                  @search="searchHandle"
                  @clear="searchHandle"
                  @keydown.enter="searchHandle"
                ></a-input-search>
              </div>
              <div class="pro-scroll">
                <!-- 为了不影响滚动加载 loading放在外部 -->
                <a-spin v-if="loading" dot> </a-spin>
                <div class="project-items" @scroll="projectScroll">
                  <a-doption
                    v-for="project in projectList.filter((e) => {
                      return (
                        e?.isTemplate === (projectTemplate === '1' ? 1 : 0)
                      );
                    })"
                    :id="`pro-${project.id}`"
                    :key="project.id"
                    :value="project.id"
                  >
                    <div class="pro-item">
                      <img
                        v-if="currentProjectId === project.id"
                        src="@/assets/images/common/prefix.png"
                        alt=""
                        height="22"
                        width="4"
                      />
                      <img
                        v-else
                        src="@/assets/images/common/prefix1.png"
                        alt=""
                        height="22"
                        width="4"
                      />
                      <a-tooltip :content="project.name" position="top">
                        <span class="pro-name">{{ project.name }}</span>
                      </a-tooltip>
                    </div>
                  </a-doption>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </template>
  </a-dropdown>

  <!-- 暂无项目弹窗 -->
  <a-modal
    :visible="noProjectVisible"
    hide-cancel
    :closable="false"
    @ok="handleOk"
  >
    <template #title>暂无项目 </template>
    <div>您当前无项目，请跳转到首页创建项目</div>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed, nextTick, onMounted, ref, defineProps, watch } from 'vue';
  import { getProjectList } from '@/api/project';
  import { queryProjectDetail } from '@/views/project-setting/projectNew/api';
  import { useI18n } from 'vue-i18n';
  import { useRoute, useRouter } from 'vue-router';
  import { setLocalstorage, getLocalstorage } from '@/utils/localstorage';
  import { storeCurrentProjectId } from '@/api/storage-project';
  import { useGlobalModeStore, useUserStore } from '@/store';
  import { getUserId } from '@/utils/auth';
  import useFileStore from '@/store/modules/file';
  import useProjectStore from '@/store/modules/project/index';

  const projectState = useProjectStore();
  const { t } = useI18n();

  const route: any = useRoute();
  const router: any = useRouter();
  const userStore = useUserStore();

  const props = defineProps({
    mode: {
      type: String,
      default: '',
    },
  });
  const currentProjectId = ref(route.params.projectId || '');
  const currentProject: any = ref({}); // 当前项目数据
  const projectTemplate = computed(() => userStore.projectTemplate);

  interface Project {
    id: string;
    name: string;
    isTemplate: number;
  }

  const projectList = ref<Project[]>([]);
  const searchValue = ref('');

  const allPage = ref(1);
  const pageSize = 20;

  // 上拉加载数据
  const loading = ref(false);
  const addProjectList = async (pageNo: number) => {
    const params = {
      pageNo,
      pageSize,
      projectType: projectTemplate.value === '1' ? 1 : 0,
      name: searchValue.value,
    };
    console.log(
      '11111111111111111params',
      params,
      'projectTemplate.value',
      projectTemplate.value
    );
    loading.value = true;
    const res: any = await getProjectList(params);
    if (res.status && res?.data?.list) {
      projectList.value.push(...res.data.list);
      loading.value = false;
    }
  };

  // 无项目时返回首页
  const globalModeStore = useGlobalModeStore();
  const noProjectVisible = ref(false);
  const handleOk = () => {
    globalModeStore.changeGlobalMode('');
    router.push({
      name: 'homePage',
    });
  };

  let index = 1;

  const getList = async (isSearch?: any) => {
    index = 1;
    const params = {
      pageNo: 1,
      pageSize,
      projectType: projectTemplate.value === '1' ? 1 : 0,
      name: searchValue.value,
    };
    console.log(
      '22222params',
      params,
      'projectTemplate.value',
      projectTemplate.value
    );

    // await projectListStore.setProjectList(params);
    // 设置当前所选项目数据
    if (currentProjectId.value) {
      const paramsNow = {
        id: currentProjectId.value,
      };
      currentProject.value = (await queryProjectDetail(paramsNow)).data;
    }
    loading.value = true;
    const res = await getProjectList(params);
    if (res.status) {
      if (!res.data.list?.length && !isSearch) {
        // 无项目时出弹窗跳回到首页
        noProjectVisible.value = true;
      } else {
        allPage.value = Math.ceil(res.data.total / pageSize);
        // isSearch为false时 会在列表中过滤掉当前项目数据（ 搜索时<isSearch为true>不会过滤） 后面再添加到第一位
        if (!isSearch && currentProjectId.value) {
          res.data.list = res.data.list.filter(
            (item: any) => item.id !== currentProjectId.value
          );
        }
        projectList.value = res.data?.list || [];

        // 存一个gropuId 用于标准模块需要传递的参数  等后端接口改造好后 可去掉
        if (projectList.value?.length > 0) {
          const groupId =
            projectList.value[projectList.value.length - 1]?.id || '';
          setLocalstorage('groupId', groupId);
        }
      }
      loading.value = false;
    }
  };
  const currenPrjName = computed(() => {
    return (
      currentProject.value?.name ||
      // projectList.value.find((v) => v.id === currentProjectId.value)?.name ||
      t('settings.navbar.project-not-found')
    );
  });

  const userId = getUserId() || '';
  const projectChange = (
    id: string | number | Record<string, any> | undefined
  ) => {
    currentProjectId.value = id as string;
    projectState.projectId = id as string;
    router.push({
      name: route.name || 'home',
      params: {
        projectId: currentProjectId.value,
      },
    });
    setLocalstorage(`last_project_${userId}`, currentProjectId.value);
    storeCurrentProjectId(currentProjectId.value);
  };

  const projectListShow = async (visible: boolean) => {
    index = 1;
    if (visible) {
      searchValue.value = '';
      projectList.value = [];
      // await nextTick();
      await getList();
      if (currentProject.value) projectList.value.unshift(currentProject.value);
      nextTick(() => {
        const ele: any = document.getElementById(
          `pro-${currentProjectId.value}`
        );
        // eslint-disable-next-line no-unused-expressions
        ele ? (ele.style.backgroundColor = 'rgba(229, 230, 235, 0.5)') : '';
        ele?.scrollIntoView({ behavior: 'instant', block: 'center' });
      });
    }
  };

  const projectScroll = (event: any) => {
    const { scrollTop, clientHeight, scrollHeight } = event.target;
    const threshold = 60;
    // 当前翻页小于总页数 并且上次数据已经请求渲染完了后 翻动到底部 加载下页数据
    if (index < allPage.value && !loading.value) {
      if (scrollTop + clientHeight >= scrollHeight - threshold) {
        index++;
        addProjectList(index);
      }
    }
  };

  watch(
    () => props.mode,
    async (val) => {
      if (val === 'project') {
        const pId: any = getLocalstorage(`last_project_${userId}`);
        // 有缓存的项目id
        if (pId) {
          // 缓存里的项目id和路由里的项目id不一致时，需要判断路由里的在不在项目列表，在的话，把缓存里的改掉
          if (route.params.projectId && route.params.projectId !== pId) {
            currentProjectId.value = route.params.projectId;
            setLocalstorage(`last_project_${userId}`, currentProjectId.value);
            storeCurrentProjectId(currentProjectId.value);
          } else {
            // 缓存里的项目id和路由里的项目id一致时，直接使用缓存的
            currentProjectId.value = pId;
          }
          // router.push({
          //   name: 'home',
          //   params: {
          //     projectId: pId,
          //   },
          // });
        } else {
          if (!projectList.value.length) {
            await getList();
          }
          const prjId = route.params.projectId || '';
          if (!prjId) {
            currentProjectId.value = projectList.value[0]?.id;
            if (currentProjectId.value) {
              router.push({
                name: 'home',
                params: {
                  projectId: currentProjectId.value,
                },
              });
              setLocalstorage(`last_project_${userId}`, currentProjectId.value);
              storeCurrentProjectId(currentProjectId.value);
            }
          }
        }
      }
    },
    {
      immediate: true,
    }
  );

  // 添加当前选择的项目到第一个
  const setFirstProject = async () => {
    if (currentProjectId.value) {
      await getList();
      if (currentProject.value) projectList.value.unshift(currentProject.value);
    }
  };

  watch(
    () => currentProjectId.value,
    async () => {
      setFirstProject();
    },
    {
      immediate: true,
    }
  );

  const searchHandle = async () => {
    await getList(true);
  };

  onMounted(() => {
    // getList();
  });
</script>

<style scoped lang="less">
  .prj-dropdown {
    margin-right: 8px;
    .project-list {
      max-height: 400px;
      width: 400px;
      overflow: hidden;
      .pro-tab-wrap {
        width: 100%;
      }
      .search-wrap {
        padding: 12px;
        padding-top: 0px;
      }
      .pro-scroll {
        height: 300px;
        //border: 1px solid red;
        overflow: hidden;
        position: relative;
        .arco-spin {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 99999;
        }
        .project-items {
          overflow: auto;
          height: 100%;
          .pro-item {
            display: flex;
            align-items: center;
            align-content: center;
            .pro-name {
              margin-left: 8px;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
    :deep(.project-select) {
      margin-left: 16px;
      padding: 0;
      background-color: transparent;
      display: flex;
      align-items: center;
      align-content: center;
      .project-name {
        display: inline-block;
        max-width: 400px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    :deep(.arco-icon-down) {
      margin-left: 8px;
    }
  }
</style>
