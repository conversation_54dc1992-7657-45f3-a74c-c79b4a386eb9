import axios from 'axios';
import { FormKeys } from '@/directionary/process';

import i18n from '@/locale/index';

const { t } = i18n.global;
interface MsgListReqParams {
  status: string | number;
}
interface MsgUpdateReqParams {
  status: string | number;
  receiveLogIdList: any;
}
export const getMsgList = (params: MsgListReqParams) => {
  return axios.get('/sys-msg/socketMsg/page', { params });
};
export const updateMsgState = (params: MsgUpdateReqParams) => {
  return axios.post('/sys-msg/socketMsg/updateStatus', params);
};

export const msgConfig = {
  // 审阅
  [FormKeys.review]: {
    color: 'blue',
    name: 'messageBox.review-task',
  },
  [FormKeys.reviewNotify]: {
    color: 'blue',
    name: 'messageBox.review-notification',
  },
  // 交付
  [FormKeys.delivery]: {
    color: 'arcoblue',
    name: 'messageBox.delivery-task',
  },
  [FormKeys.deliveryNotify]: {
    color: 'arcoblue',
    name: 'messageBox.delivery-notification',
  },
  // 提资 | 共享
  [FormKeys.collaborate]: {
    color: 'green',
    name: 'messageBox.collaboration-task',
  },
  [FormKeys.collaborateNotify]: {
    color: 'green',
    name: 'messageBox.collaboration-notification',
  },
  [FormKeys.collaborateRemind]: {
    color: 'green',
    name: 'messageBox.collaboration-remind',
  },
  // 问题
  [FormKeys.issue]: {
    color: 'orange',
    name: 'messageBox.issue-notification',
  },
  [FormKeys.issueNotify]: {
    color: 'orange',
    name: 'messageBox.issue-reply',
  },
  // 附函
  [FormKeys.enclosure]: {
    color: 'cyan',
    name: 'messageBox.enclosure-notification',
  },
  // 文件共享
  [FormKeys.share]: {
    color: 'gold',
    name: 'messageBox.shared-notification',
  },
  // 会议消息通知
  [FormKeys.meetingNews]: {
    color: 'orange',
    name: 'messageBox.meeting-news',
  },
};
export default null;
