<template>
  <a-drawer
    v-if="visible"
    :width="520"
    :body-style="bodyStyle"
    style="padding: 0"
    :visible="visible"
    unmount-on-close
    :footer="null"
    @cancel="handleCancel"
  >
    <template #title>
      <!-- <icon-left @click="handleCancel" /> -->
      <span class="type-name">{{
        drawerData.type === 'meeting'
          ? t('schedule.meeting')
          : t('schedule.matters')
      }}</span>
      <a-button
        v-show="drawerData.parentScheduleDetailId"
        type="outline"
        size="small"
        style="
          position: absolute;
          right: 40px;
          border-radius: var(--border-radius-medium);
        "
        @click="
          jumpMatterOrMeeting('matters', drawerData.parentScheduleDetailId)
        "
        >{{ $t('calendar.head-to-main-task') }}
      </a-button>

      <a-button
        v-show="drawerData.meetingId"
        type="outline"
        size="small"
        style="
          position: absolute;
          right: 40px;
          border-radius: var(--border-radius-medium);
          color: #ff6b00;
          border: 1px solid #ff6b00;
        "
        @click="jumpMatterOrMeeting('meeting', drawerData.meetingId)"
        >{{ $t('calendar.go-to-meeting') }}
      </a-button>
    </template>
    <a-spin dot :loading="drawerLoading">
      <div>
        <div class="schedule-head">
          <div v-if="type !== 'new'" class="summary-head">
            <a-select
              :model-value="status"
              :class="'status' + status"
              style="width: 98px"
              placeholder="事项"
              @change="(val: any) =>changeStatusHandle(val)"
            >
              <template #prefix>
                <img v-if="!status" :src="status0Img" alt="" />
                <img v-if="status === 1" :src="status1Img" alt="" />
                <img v-if="status === 2" :src="status3Img" alt="" />
              </template>
              <a-option
                v-for="item in statusOptions"
                :key="item.id"
                :value="item.id"
              >
                {{ t(item.name) }}
              </a-option>
            </a-select>
            <!-- <span class="title">{{ matterData.title }}</span> -->
          </div>
          <!-- 编辑标题 -->
          <div class="describe-box">
            <a-input
              v-model="title"
              placeholder="请输入标题"
              class="title-input"
              :disabled="hasNoEditPermission"
            >
              <!-- <template
              v-if="drawerData.parentScheduleDetailId || drawerData.meetingId"
              #append
            >
              <a-button
                v-show="drawerData.parentScheduleDetailId"
                type="outline"
                @click="
                  jumpMatterOrMeeting(
                    'matters',
                    drawerData.parentScheduleDetailId
                  )
                "
                >{{ $t('calendar.head-to-main-task') }}</a-button
              >

              <a-button
                v-show="drawerData.meetingId"
                type="outline"
                class="go-to-meeting-detail"
                @click="jumpMatterOrMeeting('meeting', drawerData.meetingId)"
                >{{ $t('calendar.go-to-meeting-detail') }}</a-button
              >
            </template> -->
            </a-input>
          </div>
        </div>

        <div style="width: 516px">
          <MeetingDetail
            v-if="drawerData.type === 'meeting'"
            :title="drawerData.title"
            type="view"
            :data="drawerData"
          />
          <createMatter
            v-else
            :type="type"
            :title="title"
            :data="drawerData"
            @refresh="refreshHandle"
            @cancel="handleCancel"
          />
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { ref, watch, computed, inject } from 'vue';
  import { useUserStore, usegGanntDrawerStore } from '@/store';
  import { storeToRefs } from 'pinia';
  import { setscheduleStatus } from '@/views/schedule/component/calendar/api';
  import createMatter from './createMatter.vue';
  import MeetingDetail from '@/views/schedule/component/meeting/componemt/createEditMeeting.vue';

  import { useI18n } from 'vue-i18n';
  import status0Img from '@/assets/images/meeting/status0.png';
  import status1Img from '@/assets/images/meeting/status1.png';
  import status3Img from '@/assets/images/meeting/status3.png';

  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'new',
    },
    drawerLoading: {
      type: Boolean,
      default: false,
    },
  });
  const gGanntDrawerStore = usegGanntDrawerStore();
  const { drawerData } = storeToRefs(gGanntDrawerStore);
  const bodyStyle = { padding: '20px 0' };

  const userStore = useUserStore();
  const userName = computed(() => userStore.username);
  const title = ref('');
  const propsCopyData = ref<any>({});
  // 只有创建人和参与人有权限编辑
  const hasNoEditPermission = computed(() => {
    const currentUser = userStore.username;
    const creator = propsCopyData.value.createBy;
    const chargePersonIdList = propsCopyData.value?.chargePersonIdList || [];
    // 提取所有参与人的 username
    const chargePersonIdListUserNames = chargePersonIdList.map(
      (p: any) => p.userName
    );
    // 假设是username数组
    return (
      props.type === 'edit' &&
      currentUser !== creator &&
      !chargePersonIdListUserNames.includes(currentUser)
    );
  });

  const status = ref(0);
  // 会议的状态
  const statusData: any = {
    meeting: [
      {
        id: 0,
        name: 'schedule.notStarted',
      },
      {
        id: 1,
        name: 'schedule.inProgress',
      },
      {
        id: 2,
        name: 'schedule.completed',
      },
    ],
    item: [
      {
        id: 1,
        name: 'schedule.inProgress',
      },
      {
        id: 2,
        name: 'schedule.completed',
      },
      {
        id: 3,
        name: 'schedule.closed',
      },
    ],
  };

  const statusOptions = ref();

  const emits = defineEmits([
    'refresh',
    'jump',
    'update:visible',
    'update:drawerLoading',
  ]);

  const handleCancel = () => {
    console.log('关闭抽屉');
    emits('update:visible', false);
    drawerData.value = {};
    propsCopyData.value = {};
    emits('update:drawerLoading', false);
    title.value = '';
  };

  // 通用设置状态方法
  const updateScheduleStatus = async (val: string) => {
    const param = {
      scheduleDetailId: drawerData.value.scheduleDetailId,
      status: val,
      type: drawerData.value.type === 'meeting' ? 2 : 1,
    };
    const res = await setscheduleStatus(param);
    if (res.status) {
      Message.success(t('schedule.status.success'));
      emits('refresh');
    }
  };

  // 检查子事项状态
  const checkMatterStatus = async (val: string) => {
    const allSubMatterCompleted = drawerData.value?.children.every(
      (item: { agendaStatus: number }) => item.agendaStatus === 2
    ); // 获取所有子事项状态是否都为已完成

    // if (allSubMatterCompleted) {
    //   await updateScheduleStatus(val);
    // } else {
    //   Message.info('请先完成所有子事项');
    //   status.value = summaryData.value.agendaStatus;
    // }

    if (
      (allSubMatterCompleted && drawerData.value.status === 2) ||
      drawerData.value.status !== 2
    ) {
      await updateScheduleStatus(val);
    } else if (drawerData.value.status === 2) {
      Message.info(t('schedule.status.completeSubMatters'));
      status.value = drawerData.value.agendaStatus;
    }
  };

  // 修改状态
  const changeStatusHandle = async (val: any) => {
    // 只有创建人可以修改状态
    if (userName.value !== drawerData.value.createBy) {
      Message.info(t('schedule.status.notCreator'));
      return;
    }
    // 只有通过校验才赋值
    status.value = val;
    if (drawerData.value.type === 'item') {
      // 如果是事项修改状态
      await checkMatterStatus(val);
    } else {
      await updateScheduleStatus(val);
    }
  };

  /**
   * 跳转事项或会议
   */
  const jumpMatterOrMeeting = async (type: string, id: string) => {
    emits('update:visible', true);
    const data = {
      id,
      type,
    };
    await gGanntDrawerStore.setDrawerData(data);
  };

  // 修正：监听 matterData 响应式对象本身（适配父组件传递 ref 的情况）
  watch(
    () => drawerData.value,
    (val) => {
      // 备份一份详情数据
      propsCopyData.value = JSON.parse(JSON.stringify(val));
      if (val && val.type === 'meeting') {
        statusOptions.value = statusData.meeting;
      } else {
        statusOptions.value = statusData.item;
      }
      status.value = val?.status || 0;
      title.value = val?.title || '';
      console.log('11111111111111111', val);
    },
    { immediate: true }
  );
  const refreshHandle = (val: any) => {
    console.log('创建成功通知到抽屉组件');
    // 关闭抽屉，通知树形结构刷新
    emits('update:visible', false);
    drawerData.value = {};
    propsCopyData.value = {};
    title.value = '';
    emits('refresh');
  };
</script>

<style lang="less" scoped>
  :deep(.drawer-body) {
    padding: 20px 0 !important;
  }

  .summary-head {
    padding: 0 16px;
    font-size: 14px;
    :deep(.arco-select-view-single) {
      height: 24px;
    }
    :deep(.arco-select-view-value) {
      min-height: 0;
    }

    .type-name {
      margin-left: 8px;
      font-size: 20px;
      color: #1d2129;
    }

    :deep(.arco-select) {
      border-radius: 4px;
    }
    :deep(.status0) {
      background-color: #ffece8 !important;
      color: #ff4d4f;
      .arco-select-view-icon {
        color: #ff4d4f;
      }
    }
    :deep(.status1) {
      background-color: #e8f2ff !important;
      color: #3366ff;
      .arco-select-view-icon {
        color: #3366ff;
      }
    }
    :deep(.status2) {
      background-color: #e5e6eb !important;
      color: #86909c;
      .arco-select-view-icon {
        color: #86909c;
      }
    }
    img {
      width: 16px;
      height: 16px;
    }
    :deep(.arco-select-view-single) {
      padding: 0 8px;
    }
    :deep(.arco-select-view-prefix) {
      padding-right: 4px;
    }
    :deep(.arco-select-view-suffix) {
      padding-left: 4px;
    }

    .title {
      margin-left: 16px;
      color: #1d2129;
      font-size: 20px;
    }
  }
  .describe-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .title-input {
      font-size: 20px;
      background-color: #fff;
      color: #1d2129;
      border: none !important;
      height: 60px;
      line-height: 60px;

      :deep(.arco-input) {
        font-size: 20px;
        color: #1d2129;
      }
      :deep(.arco-input-wrapper) {
        background-color: #fff;
        border: 0 !important;
      }

      .arco-input-wrapper {
        padding: 0 20px 0 20px;
      }

      :deep(.arco-input-append) {
        background-color: #fff;
        margin-right: 16px;
        border: 0;
      }

      .go-to-meeting-detail {
        color: #ff6b00;
        border: 1px solid #ff6b00;
      }
    }
  }
  .schedule-head {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ddd;
  }
</style>
