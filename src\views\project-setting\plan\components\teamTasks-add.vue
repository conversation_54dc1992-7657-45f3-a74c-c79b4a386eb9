<template>
  <a-modal
    :visible="visible"
    :title="dialogTitle + ' ' + $t('project-setting.dismantling-tasks')"
    :unmount-on-close="true"
    :mask-closable="false"
    width="660px"
    draggable
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-form ref="teamListRef" :model="teamForm" auto-label-width class="form">
      <div v-for="(item, index) in teamForm.teamData" :key="index">
        <div class="teamItem">
          <div>
            <a-form-item
              :field="`teamData[${index}].teamId`"
              :label="$t('project-setting.teamName')"
              :rules="[
                {
                  required: true,
                  message: t('project-setting.team-task-name-errMsg'),
                },
              ]"
            >
              <a-select
                v-model="item.teamId"
                :placeholder="$t('project-setting.team-task-name-errMsg')"
              >
                <a-option
                  v-for="team in teamList"
                  :key="`${team.id}-${team.name}`"
                  :value="team.id"
                  :label="team.name"
                ></a-option>
              </a-select>
            </a-form-item>
            <a-form-item
              :field="`teamData[${index}].date`"
              :label="$t('project-setting.team-start-end-time')"
              :rules="[
                {
                  required: true,
                  message: t('project-setting.team-start-end-time-errMsg'),
                },
              ]"
            >
              <a-range-picker
                style="width: 100%"
                v-model="item.date"
                :disabled-date="(current: any) => dayjs(current).isBefore(planStart) || dayjs(current).isAfter(planEnd)"
                @change="pickerChange(index, item)"
              ></a-range-picker>
            </a-form-item>
            <a-form-item
              :field="`teamData[${index}].description`"
              :label="$t('project-setting.task-description')"
              :rules="[
                {
                  required: true,
                  message: t('project-setting.task-description-errMsg'),
                },
                {
                  maxLength: 255,
                  message: t('project-setting.project-name-length-errMsg'),
                },
              ]"
              validate-trigger="input"
            >
              <a-textarea
                v-model="item.description"
                :placeholder="$t('project-setting.task-description-errMsg')"
                allow-clear
                :max-length="currentLocale === 'en-US' ? 2000 : 1000"
                show-word-limit
              />
            </a-form-item>
          </div>
          <div>
            <span>
              <a-button
                type="text"
                shape="circle"
                @click="handleAddTeam()"
                v-if="props.handleType == 'add'"
                ><icon-plus-circle
              /></a-button>
              <a-button
                type="text"
                status="danger"
                shape="circle"
                @click="handleDelTeam(index)"
                v-if="teamForm.teamData.length > 1"
              >
                <icon-minus-circle />
              </a-button>
            </span>
          </div>
        </div>
        <a-divider v-if="index !== teamForm.teamData.length - 1"></a-divider>
      </div>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
  import { computed, ref, watch, reactive } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';
  import {
    getTeamList,
    MilestoneRecordTeam,
    saveMainTask,
    updateMainTask,
    parentDataeRecord,
  } from '../api';
  import colors from '../json/colors.json';
  import dayjs from 'dayjs';
  import useLocale from '@/hooks/locale';

  const { t } = useI18n();
  // 国际化类型
  const { currentLocale } = useLocale();
  const emit = defineEmits(['update:visible', 'refresh']);
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    handleType: {
      type: String,
      default: 'view',
    },
    originData: {
      type: Object,
      require: true,
    },
    parentData: {
      type: Object,
      require: true,
    },
    planStart: {
      type: String,
      default: '',
    },
    planEnd: {
      type: String,
      default: '',
    },
  });
  const teamForm = ref<MilestoneRecordTeam>({
    ...props.originData,
  } as MilestoneRecordTeam);
  const parentForm = ref<parentDataeRecord>({
    ...props.parentData,
  } as parentDataeRecord);
  // 列表
  const route = useRoute();
  const projectId = computed(() => {
    return String(route?.params?.projectId);
  });
  interface TeamObj {
    id?: string | number;
    name?: string;
  }
  const teamList = ref<TeamObj[]>([]);
  watch(
    () => props.originData,
    (n) => {
      teamForm.value = { ...n } as MilestoneRecordTeam;
    }
  );
  watch(
    () => props.parentData,

    (n) => {
      parentForm.value = { ...n } as parentDataeRecord;
      const params: any = {
        teamId: parentForm.value.parentTeamId,
        projectId: projectId.value,
      };
      getTeamList(params)
        .then((res) => {
          teamList.value = res.data || [];
        })
        .catch((err) => {
          // eslint-disable-next-line no-console
          console.log(err);
        });
    }
  );
  const dialogTitle = computed(() => {
    switch (props.handleType) {
      case 'add':
        return t('project-setting.create');
      case 'view':
        return t('project-setting.view');
      default:
        return t('project-setting.edit');
    }
  });
  const handleDelTeam = (index: number) => {
    teamForm.value.teamData.splice(index, 1);
  };
  interface Person {
    teamId: string;
    startTime: string;
    endTime: string;
    date: [string, string];
    content: string;
  }
  const pickerChange = (index: number, item: Person) => {
    const a: string = item.date[0];
    const b: string = item.date[1];
    teamForm.value.teamData[index].startTime = a;
    teamForm.value.teamData[index].endTime = b;
  };
  const handleAddTeam = () => {
    teamForm.value.teamData.push({
      teamId: '',
      startTime: '',
      endTime: '',
      date: [],
      description: '',
      type: 1,
      milestoneId: parentForm.value.milestoneId,
      parentId: parentForm.value.parentId,
      projectId: parentForm.value.projectId,
      id: '',
      color: '',
    });
  };

  // 查询列表数据
  const getTwoTeamList = () => {
    const params: any = {
      teamId: parentForm.value.parentTeamId,
      projectId: projectId.value,
    };
    getTeamList(params)
      .then((res) => {
        teamList.value = res.data || [];
      })
      .catch((err) => {
        // eslint-disable-next-line no-console
        console.log(err);
      });
  };
  // 新增里程碑子任务
  const teamHandleAdd = async () => {
    const params = {
      ...teamForm.value,
    };
    params.teamData.forEach((element) => {
      if (!element.color) {
        const colorIndex = Math.floor(Math.random() * colors.length);
        element.color = colors[colorIndex];
      }
    });
    const res = await saveMainTask(params.teamData);
    return !!res.status;
  };
  // 编辑里程碑子任务
  const teamHandleEdit = async () => {
    const params = {
      ...teamForm.value,
    };
    const res = await updateMainTask(params.teamData);
    return !!res.status;
  };
  const teamListRef = ref<FormInstance>();
  // 确定按钮点击事件
  const handleBeforeOk = async (done: any) => {
    const res = await teamListRef.value?.validate();
    if (!res) {
      if (teamForm.value.teamData && teamForm.value.teamData.length > 0) {
        let flag = false;
        teamForm.value.teamData.forEach((element) => {
          if (
            new Date(element.endTime).getTime() >
            new Date(props.planEnd).getTime()
          ) {
            flag = true;
          }
        });
        if (flag) {
          Message.error(t('project-setting.add-dismantling-task-error'));
          return;
        }
      }
      let flg = false;
      let msg = t('project-setting.add-success');
      if (props.handleType === 'add') {
        flg = await teamHandleAdd();
      } else if (props.handleType === 'edit') {
        flg = await teamHandleEdit();
        msg = t('project-setting.edit-success');
      }

      if (flg) {
        Message.success(msg);
        emit('update:visible', false);
        emit('refresh');
      }
      done();
    }
  };

  // 取消按钮点击事件
  const handleCancel = () => {
    emit('update:visible', false);
  };
  getTwoTeamList();
</script>
<style lang="less" scoped>
  .teamItem {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
  }
  .addButton {
    width: 13px;
    height: 13px;
    color: #3366ff;
    border-color: #3366ff;
    border-radius: 50%;
  }
</style>
