<template>
  <a-modal
    :visible="visible"
    :title="title"
    :hide-cancel="true"
    :unmount-on-close="true"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-form
      ref="deptRef"
      :model="formData"
      :label-col-props="{ span: currentLocale === 'en-US' ? 8 : 5 }"
      :wrapper-col-props="{ span: currentLocale === 'en-US' ? 16 : 19 }"
    >
      <a-form-item
        field="name"
        :label="$t('department.columns.name')"
        validate-trigger="input"
        :rules="[
          {
            required: true,
            message: $t('department.columns.name-require'),
          },
        ]"
      >
        <remove-spaces-input
          v-model="formData.name"
          :max-length="currentLocale === 'en-US' ? 255 : 50"
          show-word-limit
        />
      </a-form-item>
      <a-form-item
        field="entName"
        :label="$t('department.columns.abbr')"
        validate-trigger="input"
        :rules="[
          {
            required: true,
            message: $t('department.columns.abbr-require'),
          },
        ]"
      >
        <remove-spaces-input
          v-model="formData.entName"
          :max-length="currentLocale === 'en-US' ? 255 : 50"
          show-word-limit
        />
      </a-form-item>
      <!-- <a-form-item
        :label="$t('department.columns.orgType')"
        validate-trigger="input"
      >
        <a-select
          v-model="formData.orgType"
          disabled
          placeholder="Please select ..."
        >
          <a-option
            v-for="item in orgTypeOption"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </a-option>
        </a-select>
      </a-form-item> -->
      <a-form-item
        field="parentId"
        :label="$t('department.columns.superior-department')"
        :rules="[
          {
            required: false,
            message: $t('department.columns.superior-department-require'),
          },
        ]"
      >
        <!-- >{{ formData.parentId }} -- {{ formData.parentName }} -->
        <template v-if="disableOrg">
          <a-input
            v-model="formData.parentName"
            readonly
            class="readonly-input"
          />
        </template>
        <template v-else>
          <OrgSelector
            v-model:dept-id="formData.parentId"
            v-model:dept-name="formData.parentName"
            @change="handleDeptChange"
          />
        </template>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import OrgSelector from '@/components/org-selector/index.vue';
  import { usePortalStore } from '@/store';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import {
    SelectDept,
    addOrgs,
    OrgServiceRecord,
    modifyOrg,
  } from '@/api/modules/department';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import removeSpacesInput from '@/components/removeSpacesInput/index.vue';

  const { currentLocale } = useLocale();
  const { t } = useI18n();

  const portalStore = usePortalStore();

  const orgTypeOption: any = [
    {
      id: '0',
      name: '内部单位',
    },
    {
      id: '1',
      name: '外部单位',
    },
  ];
  console.log('portalStore', portalStore);

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
      default() {
        return {
          id: '',
          name: '',
          entName: '',
          parentId: '',
          parentName: '',
          pathName: '',
          pathNo: '',
          orgType: '',
          operator: '',
          content: null,
        };
      },
    },
  });
  const emit = defineEmits([
    'update:visible',
    'refreshTable',
  ]);

  const title = ref('');
  const formData = ref<SelectDept>({ ...props.form } as SelectDept);
  watch(
    () => props.form,
    (newForm) => {
      formData.value = { ...newForm } as SelectDept;
      // 设置标题
      switch (formData.value.operator) {
        case 'edit':
          title.value = '编辑';
          break;
        case 'add':
          title.value = '新增';
          break;
        case 'addSub':
          title.value = '新增子部门';
          break;
        default:
          title.value = '新增';
      }
    },
    { immediate: true }
  );
  const deptRef = ref<FormInstance>();

  const disableOrg = computed(() => {
    return formData.value.operator === 'addSub' && !!props.form.parentId;
  });

  const handleBeforeOk = async (done: (closed: boolean) => void) => {
    try {
      const res = await deptRef.value?.validate();
      if (!res) {
        let success = false;
        let message = t('department.message.add-success');

        switch (formData.value.operator) {
          case 'edit':
            success = await editOrg();
            message = t('department.message.edit-success');
            break;
          // case 'add':
          //   if (formData.value.pathNo) {
          //     success = await addSubOrg();
          //   } else {
          //     success = await addOrg();
          //   }
          //   break;
          case 'addSub':
            success = await addSubOrg();
            break;
          default:
            // 未知操作类型，记录错误并返回失败
            console.error('未知的操作类型:', formData.value.operator);
            Message.error(t('department.message.unknown-operation'));
            done(false);
            return;
        }
        if (success) {
          Message.success(message);
          emit('update:visible', false);
          emit('refreshTable');
        }
      }
      done(true);
    } catch (error) {
      console.error('提交失败:', error);
      Message.error(t('department.message.submit-fail'));
      done(false);
    }
  };
  const editOrg = async () => {
    const params = {
      ...formData.value.content,
      id: props.form.id,
      name: formData.value.name,
      entName: formData.value.entName,
      orgType: formData.value.orgType,
      parentNo: formData.value.parentId || undefined,
      ...(formData.value.parentId && {
        parentName: formData.value.parentName,
        pathName: formData.value.pathName,
        pathNo: String(formData.value.pathNo),
      }),
    };
    const res = await modifyOrg(params);
    return !!res.status;
  };
  // 新增根节点下的组织机构，即最大的组织机构
  const addOrg = async () => {
    const params = {
      name: formData.value.name,
      entName: formData.value.entName,
      parentName: '根节点',
      pathName: `.根节点.${formData.value.name}.`,
      portalId: portalStore.portalId,
      type: portalStore.currentPortal.type,
      orgType: formData.value.orgType,
    };
    const res = await addOrgs(params);
    return !!res.status;
  };
  const addSubOrg = async () => {
    const params = {
      name: formData.value.name,
      entName: formData.value.entName,
      parentName: formData.value.parentName,
      parentNo: formData.value.parentId,
      pathName: `${formData.value.pathName}`,
      pathNo: `${formData.value.pathNo}`,
      portalId: portalStore.portalId,
      type: portalStore.currentPortal.type,
      orgType: formData.value.orgType,
    };
    const res = await addOrgs(params);
    return !!res.status;
  };
  const handleCancel = () => {
    emit('update:visible', false);
  };

  const handleDeptChange = (val: OrgServiceRecord) => {
    console.log('变化了1111', val);
    if (val?.content) {
      formData.value.parentName = val.content.name || '';
      formData.value.parentId = val.content.orgNo?.toString() || '';
      formData.value.pathName = val.content.pathName || '';
      formData.value.pathNo = val.content.pathNo || '';
    } else {
      formData.value.parentName = '';
      formData.value.parentId = '';
      formData.value.pathName = '';
      formData.value.pathNo = '';
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'OrgInfo',
  };
</script>

<style lang="less" scoped>
  .readonly-input {
    :deep(.arco-input) {
      cursor: not-allowed;
    }
  }
</style>
