<template>
  <div class="user-select-group-tab">
    <div v-if="!teamMemberList.length" class="structure-list">
      <div
        v-for="(item, index) in teamList"
        :key="index"
        class="structure-block"
        @click="handleGroupChange(item)"
      >
        <div
          style="
            flex: 1;
            display: flex;
            align-items: center;
            width: calc(100% - 20px);
          "
        >
          <span class="teamImg"></span>
          <span class="name">{{ item.teamName }}</span>
        </div>
        <div style="width: 20px">
          <icon-right />
        </div>
      </div>
    </div>
    <div v-if="teamMemberList.length" class="group-member-list">
      <a-space direction="vertical">
        <!-- 若有人数限制 全选置灰 -->
        <a-checkbox
          :model-value="checkedAll"
          :indeterminate="indeterminate"
          :disabled="limit"
          @change="handleChangeAll"
        >
          {{ $t('selectMembers.selectAll') }}
        </a-checkbox>
        <!-- 群组人员数据 -->
        <a-checkbox-group
          v-model="nowMeberId"
          direction="vertical"
          @change="changeTeamMember"
        >
          <a-grid :cols="3" :col-gap="48" :row-gap="12">
            <a-grid-item v-for="(item, index) in teamMemberList" :key="index">
              <a-checkbox :value="item?.id" :disabled="item.disabled">
                <span class="surnamed surnamed1">
                  {{ item.userFullname.charAt(0) }}
                </span>
                {{ item?.userFullname }}
              </a-checkbox>
            </a-grid-item>
          </a-grid>

          <!--        <a-checkbox-->
          <!--          v-for="(item, index) in teamMemberList"-->
          <!--          :key="index"-->
          <!--          :value="item?.id"-->
          <!--          :disabled="item.disabled"-->
          <!--        >-->
          <!--          <span class="surnamed surnamed2">-->
          <!--            {{-->
          <!--              item.userFullname?.charAt(0)-->
          <!--            }}-->
          <!--          </span>-->
          <!--          {{ item?.userFullname }}-->
          <!--        </a-checkbox>-->
        </a-checkbox-group>
      </a-space>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { defineEmits, defineProps, ref, toRaw, watch, computed } from 'vue';
  import { getTeamList, getTeamMember } from '@/api/selectMember';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const props = defineProps({
    breadcrumbList: {
      type: Array,
      default() {
        return [];
      },
    },
    selectedMember: {
      type: Array,
      default() {
        return [];
      },
    },
    // 人数限制
    limit: {
      type: Number,
      default() {
        return null;
      },
    },
  });
  const emits = defineEmits(['change', 'tabChange', 'update:breadcrumbList']);
  const teamList: any = ref([
    {
      teamName: computed(() => t('selectMembers.team')),
      id: 0,
    },
  ]);
  const teamMemberList = ref([]);
  const nowMeberId: any = ref([]);

  const changeBreadcrumb = (line: any) => {
    const breadcrumb = {
      id: line.id,
      name: line.teamName,
    };
    // breadcrumbPath.value.push(breadcrumb)
    emits('update:breadcrumbList', [...props.breadcrumbList, breadcrumb]);
  };
  const handleGroupChange = (item: any) => {
    emits('tabChange', 2);
    // currentDep.value = {...item};
    changeBreadcrumb(item);
  };

  let allSelectMembers: any = [];
  const changeTeamMember = (list: any) => {
    if (list.length > allSelectMembers.length) {
      const allIds = allSelectMembers.map((e: any) => e.id);
      const a = list.filter((e: any) => {
        return !allIds.includes(e);
      });
      a.forEach((id: string) => {
        const member = teamMemberList.value.find((e: any) => e.id === id);
        if (member) allSelectMembers.push(member);
      });
    } else {
      allSelectMembers = allSelectMembers.filter((e: any) => {
        return list.includes(e.id);
      });
    }
    emits('change', allSelectMembers);
  };

  const checkedAll = ref(false);
  const indeterminate = ref(false);
  const handleChangeAll = (val: any) => {
    const ids = toRaw(nowMeberId.value);
    const teamMembers = teamMemberList.value.map((e: any) => e.id);
    if (val) {
      checkedAll.value = true;
      nowMeberId.value = Array.from(new Set([...ids, ...teamMembers]));
    } else {
      checkedAll.value = false;
      nowMeberId.value = ids.filter((id: string) => {
        return !teamMembers.includes(id);
      });
    }
    changeTeamMember(nowMeberId.value);
  };

  const isCheckedAll = () => {
    const ids = toRaw(nowMeberId.value);
    const teamMembers = teamMemberList.value.map((e: any) => e.id);
    let len = 0;
    ids.forEach((id: string) => {
      if (teamMembers.includes(id)) len += 1;
    });
    if (len > 0 && len < teamMembers.length) {
      indeterminate.value = true;
      checkedAll.value = false;
    } else if (len > 0 && len === teamMembers.length) {
      indeterminate.value = false;
      checkedAll.value = true;
    } else {
      indeterminate.value = false;
      checkedAll.value = false;
    }
  };
  watch(() => nowMeberId.value, isCheckedAll);

  // 选择人数超过limit则进行禁止选择
  const setDisabledState = () => {
    if (!(props.selectedMember?.length < props.limit)) {
      teamMemberList.value.forEach((item: any) => {
        item.disabled = true;
      });
    } else {
      teamMemberList.value.forEach((item: any) => {
        item.disabled = false;
      });
    }
  };

  const getTeamUserList = async (line: any) => {
    const param = {
      teamId: line.id || '',
      pageNo: 1,
      pageSize: 9999,
    };
    const { data } = await getTeamMember(param);
    teamMemberList.value = data.list || [];
    if (props.limit) setDisabledState();
    isCheckedAll();
  };
  const getTeamListData = async () => {
    const param = {
      pageNo: 1,
      pageSize: 2000,
    };
    const { data } = await getTeamList(param);
    // 设置部门列表数据
    teamList.value = data;
  };
  watch(
    () => props.breadcrumbList,
    (val) => {
      if (val.length) {
        const data: any = val[val.length - 1];
        teamMemberList.value = [];
        if (data.id >= 0) {
          if (data.id === 0) {
            getTeamListData();
          } else {
            getTeamUserList({ id: data.id });
          }
        } else {
          teamList.value = [
            {
              teamName: computed(() => t('selectMembers.team')),
              id: 0,
            },
          ];
          emits('tabChange', 0);
        }
      }
    },
    {
      immediate: true,
    }
  );
  watch(
    () => props.selectedMember,
    (val) => {
      allSelectMembers = [...val];
      nowMeberId.value = val?.map((e: any) => e.id) || [];
      // 若有人数限制 设置已选择人数超过limit则进行禁止选择
      if (props.limit) setDisabledState();
    },
    {
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  .structure-list {
    cursor: pointer;
    .structure-block {
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;
      align-items: center;
      //width: 340px;
      height: 38px;
      //background-color: #f2f3f5;
      padding: 0 12px;
      line-height: 38px;
      font-size: 14px;
      color: #1d2129;
      margin-bottom: 8px;
      //border: 1px solid red;
      width: calc(100% - 8px);
      border-radius: 8px;
      overflow: hidden;
      &:hover {
        background-color: #f2f3f5;
      }
      .departImg {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-color: #3366ff;
        background-image: url(@/assets/images/schedule/organization-chart.png);
        background-size: 12px 12px;
        background-repeat: no-repeat;
        border-radius: 50%;
        background-position: center center;
        vertical-align: middle;
      }
      .teamImg {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-color: #18c4eb;
        background-image: url(@/assets/images/schedule/user-community-line.png);
        background-size: 12px 12px;
        background-repeat: no-repeat;
        border-radius: 50%;
        background-position: center center;
        vertical-align: middle;
      }
      .name {
        display: inline-block;
        margin-left: 8px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: calc(100% - 40px);
        //border: 1px solid black;
      }
      :deep(.listitem) {
        cursor: pointer;
      }
    }
  }
  .surnamed {
    display: inline-block;
    width: 24px;
    height: 24px;
    font-size: 11px;
    text-align: center;
    line-height: 24px;
    border-radius: 50%;
    color: #ffffff;
    margin-right: 8px;
    &:hover {
      background-color: #3491fa;
    }
  }
  .surnamed1 {
    background: #3366ff;
  }
  .surnamed2 {
    background: #18c4eb;
  }
</style>
