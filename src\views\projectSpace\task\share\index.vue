<template>
  <div class="process-list">
    <div class="content">
      <div class="content-title">
        <div>
          <img src="@/assets/images/table-title.png" class="title-img" alt="" />
          <span class="title-text">{{ $t('task.share-list') }}</span>
        </div>
        <a-input-search
          v-model="searchParams.name"
          :placeholder="$t('task.please-enter')"
          class="search-input"
          allow-clear
          @search="getList"
          @keydown.enter="getList"
          @clear="getList"
        />
      </div>
      <div class="table-wrap">
        <a-table
          :data="projectList"
          stripe
          :bordered="false"
          :scroll="{ x: '100%', y: '100%' }"
          :pagination="pageConfig"
          @page-change="pageChange"
          @page-size-change="pageSizeChange"
        >
          <template #empty>
            <div class="empty-wrapper">
              <img src="@/assets/images/schedule/schedule-bg.png" alt="" />
              <div>{{ $t('task.no-data-available') }}</div>
            </div>
          </template>
          <template #columns>
            <a-table-column
              :title="$t('task.index')"
              align="left"
              :width="60"
            >
              <template #cell="{ rowIndex }">
                {{
                  (pageConfig.pageSize ? pageConfig.pageSize : 10) *
                    ((pageConfig.current ? pageConfig.current : 1) - 1) +
                  (rowIndex + 1)
                }}
              </template>
            </a-table-column>
            <a-table-column :title="$t('task.share-name')" :width="200">
              <template #cell="{ record }">
                <span class="name" @click="showProcess(record)">{{
                  record.formName
                }}</span>
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('task.task-name')"
              data-index="taskName"
              align="left"
            ></a-table-column>
            <a-table-column
              :title="$t('task.task-status')"
              data-index="processState"
              align="left"
            >
              <template #cell="{ record }">
                <div class="">
                  <a-tag :color="TaskStatusColor[record.taskState]">
                    {{ TaskStateMap[record.taskState] || '' }}</a-tag
                  >
                </div>
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('task.process-templates')"
              data-index="processName"
              align="left"
            >
              <template #cell="{ record }">
                {{ record?.formExtObj?.processName || '' }}
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('task.process-status')"
              data-index="processState"
              align="left"
            >
              <template #cell="{ record }">
                <div class="">
                  <div class="">
                    <a-tag :color="ProcessStatusColor[record.processState]">
                      {{ ProcessStateMap[record.processState] || '' }}</a-tag
                    >
                  </div>
                </div>
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('task.initiator')"
              data-index="submiter"
              align="left"
            >
              <template #cell="{ record }">
                {{ record?.formExtObj?.submiter || '' }}
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('task.creation-time')"
              data-index="createDate"
              align="left"
            ></a-table-column>
            <a-table-column
              :title="$t('task.file-count')"
              data-index="fileCount"
              align="left"
              :width="100"
            >
              <template #cell="{ record }">
                {{ record?.formExtObj?.fileNumber || '' }}
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('task.operation')"
              data-index="fileCount"
              align="left"
            >
              <template #cell="{ record }">
                <a-button
                  v-if="record.processState === '3'"
                  type="text"
                  size="small"
                  @click="viewProcess(record)"
                  >{{ $t('task.view') }}</a-button
                >

                <a-button
                  v-if="isNeedCheck(record)"
                  size="small"
                  type="text"
                  @click="checkProcess(record)"
                  >{{ $t('task.review') }}</a-button
                >

                <a-popconfirm
                  :content="$t('task.whether-revoke-share')"
                  position="left"
                  @ok="cancelProcess(record)"
                >
                  <a-button
                    v-if="
                      userName === record.taskCreator &&
                      record.processState === '0'
                    "
                    size="small"
                    type="text"
                    >{{ $t('task.revocation') }}</a-button
                  >
                </a-popconfirm>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </div>
    <div class="components">
      <ProcessDetail
        v-model:visible="checkProcessVisible"
        :task-detail="currentProcess"
        :show-type="processDialogType"
        :form-config="FormConfig.collaborate"
        @agreed="getList"
        @refused="getList"
        type="share"
      ></ProcessDetail>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref } from 'vue';
  import { PaginationProps, Notification } from '@arco-design/web-vue';
  import { getProcessList } from '@/api/process';
  import {
    FormKeys,
    FormConfig,
    TaskStatusColor,
    ProcessStatusColor,
    TaskStateMap,
    ProcessStateMap,
  } from '@/directionary/process';
  import { useUserStore, useGlobalModeStore } from '@/store';
  import ProcessDetail from '@/components/process-detail/index.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { cancleShareProcess } from './api';

  const userStore = useUserStore();
  const user = computed(() => {
    return userStore.userInfo;
  });
  const route = useRoute();
  const router = useRouter();
  const searchParams = reactive({
    name: '',
  });
  const projectList = ref<object[]>([]);
  const pageConfig: PaginationProps = reactive({
    showTotal: true,
    showMore: false,
    showJumper: true,
    showPageSize: true,
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    total: 100,
  });
  const modeStore = useGlobalModeStore();
  const getList = () => {
    const params = {
      formKey: FormKeys.collaborate,
      formName: searchParams.name || '',
      column: 'createDate',
      customId: route.params.projectId,
      page: pageConfig.current,
      size: pageConfig.pageSize,
    };
    getProcessList(params)
      .then((res: any) => {
        if (res.code === 8000000) {
          const data = res.data.list || [];
          if (data.length) {
            data.forEach((e: any) => {
              if (e.formExt) {
                e.formExtObj = JSON.parse(e.formExt) || {};
              }
            });
          }
          projectList.value = data;
          // pageConfig.total = res.data.total || checkList.value.length || 0;
          // projectList.value = res.data.list || [];
          pageConfig.total = res.data.total || projectList.value.length || 0;
        } else {
          projectList.value = [];
        }
      })
      .catch((e) => {
        if (e) {
          projectList.value = [];
        }
      });
  };

  const checkProcessVisible = ref(false);
  const currentProcess = ref();
  const processDialogType = ref(0);
  const checkProcess = (row: any) => {
    processDialogType.value = 0;
    currentProcess.value = row;
    checkProcessVisible.value = true;
  };
  const isNeedCheck = (row: any) => {
    let result = false;
    if (row.taskAsignee === user.value.username && row.taskState === '0') {
      result = true;
    }
    return result;
  };
  const showProcess = (row: any) => {
    processDialogType.value = 1;
    currentProcess.value = row;
    checkProcessVisible.value = true;
  };

  const viewProcess = (row: any) => {
    modeStore.changeGlobalMode('file');
    router.push({
      path: `/project/${route.params.projectId}/file`,
      query: {
        formBizId: row.formBizId,
      },
    });
    // 设置tab跳转
    sessionStorage.setItem('activeTab', '2');
  };
  const userName = ref(userStore.username);
  const cancelProcess = async (row: any) => {
    const params = {
      id: row.formBizId,
      comment: '',
      taskId: row.taskId,
    };
    const res: any = await cancleShareProcess(params);
    if (res.code === 8000000) {
      Notification.success({
        id: 'cancleProcess',
        title: 'Success',
        content: res.message,
      });
      getList();
    } else {
      Notification.error({
        id: 'cancleProcess',
        title: 'error',
        content: res.message,
      });
    }
  };
  const pageSizeChange = (size: number): void => {
    pageConfig.pageSize = size;
    getList();
  };
  const pageChange = (current: number): void => {
    pageConfig.current = current;
    getList();
  };

  const init = async () => {
    getList();
  };
  onMounted(() => {
    init();
  });
</script>

<style scoped lang="less">
  ::v-deep .arco-btn-size-small {
    padding: 0 6px;
  }
  .process-list {
    height: calc(100vh - 165px);
    padding: 20px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #d9d9d9;
  }
  .search-title {
    display: inline-block;
    height: 22px;
    color: #000000;
    margin-bottom: 20px;
  }
  .search-bar {
    //margin-top: 8px;
    border-bottom: 1px solid var(--color-border);
  }
  .content {
    //height: calc(100% - 60px);
    height: 100%;
    .content-title {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      //height: 25px;
      position: relative;
      //border: 1px solid red;
      .btns {
        position: absolute;
        right: 0;
        top: 0;
      }
      .title-img {
        height: 20px;
        width: 20px;
      }
      .title-text {
        display: inline-block;
        margin-left: 8px;
        font-size: 18px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN, serif;
        font-weight: 600;
        color: #1d2129;
        line-height: 21px;
        position: absolute;
        left: 20px;
        top: 0;
      }
      .search-input {
        width: 220px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #c9cdd4;
        background-color: #fff;
      }
    }
    .table-wrap {
      margin-top: 16px;
      height: calc(100% - 45px);
      //border: 1px solid red;

      :deep(.arco-table-container) {
        height: 100%;
      }

      .empty-wrapper {
        height: calc(100vh - 315px);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        img {
          display: block;
          width: 140px;
          height: 140px;
        }
        div {
          margin-top: 16px;
          color: #4e5969;
        }
      }
    }
  }
  .name {
    color: rgb(var(--arcoblue-6));
    cursor: pointer;
  }
</style>
