<template>
  <div class="navbar">
    <div class="left-side" @click="toHomePage">
      <a-tooltip :content="$t('navbar.logo.to')">
        <!-- <div class="left-side" @click="toHomePage"> -->
          <a-space>
            <img alt="logo" :src="logoTop" class="logoTop" />
            <a-typography-title
              :style="{
                margin: 0,
                fontSize: '32px',
                fontWeight: 'bold',
                whiteSpace: 'nowrap',
              }"
              :heading="5"
            >
              {{ $t('navbar.logo.title') }}
            </a-typography-title>
          </a-space>
        <!-- </div> -->
      </a-tooltip>
    </div>
    <div class="center-side">
      <!-- 添加滚动文本容器 -->
      <div
        v-show="globalMode === 'project' && showMarquee.includes(projectCode)"
        class="marquee"
      >
        <div class="marquee-content">
          {{ $t('navbar.marquee.text') }}
        </div>
      </div>
      <div class="center-side-left">
        <div class="mode-box">
          <!--          <div class="mode-logo">{{ miniMode }}</div>-->
          <!-- 团队切换 -->
          <div
            v-show="isTeamFlag && globalMode === 'work'"
            class="team-selector"
            @click.stop
          >
            <div class="team-header" @click="toggleTeamList">
              <span class="team-text">{{ currentTeam }}</span>
              <icon-down :class="{ 'icon-rotate': isTeamListOpen }" />
            </div>
            <div v-show="isTeamListOpen" class="team-dropdown">
              <div
                class="team-item"
                :class="{ 'team-item-active': currentTeam === '全局视角' }"
                @click="selectTeam({ teamName: '全局视角', id: 'global' })"
              >
                {{ $t('navbar.global.view') }}
              </div>
              <div
                v-for="team in teamList"
                :key="team.id"
                class="team-item"
                :class="{ 'team-item-active': currentTeam === team.teamName }"
                @click="selectTeam(team)"
              >
                <a-tooltip :content="team.teamName" position="top">
                  <span class="pro-name">{{ team.teamName }}</span>
                </a-tooltip>
              </div>
            </div>
          </div>

          <!-- 项目选择 -->
          <ProjectCtl
            v-if="globalMode === 'project' && route.path.includes('/project/')"
            :mode="globalMode"
          ></ProjectCtl>

          <!-- <div class="return-home" @click="toHomePage"
            ><icon-home /> <span>返回首页</span></div
          > -->
        </div>
      </div>
      <div class="center-side-right">
        <div class="body">
          <!-- 全局搜索 -->
          <globalSearch v-if="globalMode === 'work'"></globalSearch>
        </div>
        <div class="languageBox">
          <a-tooltip :content="$t('settings.language')">
            <img
              src="@/assets/images/iconLanage.png"
              alt=""
              width="18"
              @click="setDropDownVisible"
            />
          </a-tooltip>
          <a-dropdown trigger="click" @select="changeLocale as any">
            <div ref="triggerBtn" class="trigger-btn"></div>
            <template #content>
              <a-doption
                v-for="item in LOCALE_OPTIONS"
                :key="item.value"
                :value="item.value"
              >
                <template #icon>
                  <icon-check v-show="item.value === currentLocale" />
                </template>
                {{ item.label }}
              </a-doption>
            </template>
          </a-dropdown>
        </div>
      </div>
    </div>

    <a-divider
      direction="vertical"
      style="margin: 0px; padding: 0px; height: 36px"
    ></a-divider>
    <ul class="right-side">
      <li>
        <a-dropdown
          trigger="click"
          position="bl"
          @popup-visible-change="(visible) => visible && loadCompanyList()"
        >
          <div class="trigger-box">
            <div class="user-name">{{ userFullname }}</div>
            <a-avatar
              :size="32"
              :style="{
                marginRight: '8px',
                cursor: 'pointer',
                backgroundColor: userStore.color,
              }"
            >
              <img
                v-if="avatar"
                alt="avatar"
                :src="'/work/api/sys-storage/download_image?f8s=' + avatar"
              />
              <span v-else>{{ nickname }}</span>
            </a-avatar>
          </div>
          <template #content>
            <!-- <a-dsubmenu value="switch-account" position="lt" trigger="hover"> -->
            <a-dsubmenu value="switch-account" position="lt">
              <template #default>
                <a-space>
                  <switchAccountIcon style="width: 16px; height: 16px" />
                  <span>{{ $t('messageBox.switch.identity') }}</span>
                </a-space>
              </template>
              <template #content>
                <div class="account-list">
                  <a-doption
                    v-for="account in accounts"
                    :key="account.id"
                    :class="{
                      'account-hover':
                        hoverAccountId === account.id ||
                        account.id === companyId,
                    }"
                    @mouseenter="hoverAccountId = account.id"
                    @mouseleave="hoverAccountId = null"
                    @click="handleAccountSelected(account)"
                  >
                    <div class="account-item">
                      <div :class="['account-icon', account.type]">
                        {{ account.icon }}
                      </div>
                      <div class="account-info">
                        <a-tooltip :content="account.name">
                          <div class="account-name">{{ account.name }}</div>
                        </a-tooltip>
                        <a-tooltip :content="account.userFullName">
                          <div class="account-user">{{
                            account.userFullName
                          }}</div>
                        </a-tooltip>
                      </div>
                      <icon-check
                        v-if="
                          (account.id === '' && companyId === '') ||
                          account.id === companyId
                        "
                        class="check-icon"
                      />
                    </div>
                  </a-doption>
                </div>
              </template>
            </a-dsubmenu>
            <!-- <a-doption>
              <a-space @click="handleSwitchLocale">
                <applyAuditIcon style="width: 16px; height: 16px" />
                <span>{{
                  currentLocale === 'zh-CN'
                    ? '切换语言为英文'
                    : 'Switch the language to Chinese'
                }}</span>
              </a-space>
            </a-doption> -->
            <a-doption>
              <a-space @click="toApproveApply">
                <applyAuditIcon style="width: 16px; height: 16px" />
                <span>{{ $t('settings.navbar.application-review') }}</span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="handleLogout">
                <logoutICon style="width: 16px; height: 16px" />
                <span>{{ $t('setup.logout') }}</span>
              </a-space>
            </a-doption>
          </template>
        </a-dropdown>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
  import logoTop from '@/assets/images/logo-top.png';
  import {
    computed,
    ref,
    onMounted,
    onBeforeUnmount,
    nextTick,
    watch,
    reactive,
    type ComputedRef,
  } from 'vue';
  import { useUserStore, useGlobalModeStore, userScheduleStore } from '@/store';
  import useUser from '@/hooks/user';
  import { useI18n } from 'vue-i18n';
  import globalSearch from '@/components/globalSearch/index.vue';
  import { IconDown, IconRight, IconCheck } from '@arco-design/web-vue/es/icon';
  import { searchTeamListApi } from '@/api/modules/team';
  import { getCDexWorkUserInfoApi, getCompanyListBySelf } from '@/api/user';
  import AccountSwitcherModal from '@/components/AccountSwitcherModal/AccountSwitcherModal.vue';
  import { getProjectList } from '@/api/project';
  import switchAccountIcon from '@/assets/images/navbar/repeat-line.svg';
  import applyAuditIcon from '@/assets/images/navbar/instance-line.svg';
  import logoutICon from '@/assets/images/navbar/logout-circle-r-line.svg';
  import router from '@/router';
  import ProjectCtl from './components/projectCtl.vue';
  import { useRoute } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import useLocale from '@/hooks/locale';
  import {
    removeLocalstorageItem,
    getLocalstorage,
    setLocalstorage,
  } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';
  import { LOCALE_OPTIONS } from '@/locale';
  import { queryProjectDetail } from './api';

  // 添加Account接口定义
  interface Account {
    id: string;
    type: 'personal' | 'organization';
    name: string | ComputedRef<string>;
    userFullName: string | ComputedRef<string | undefined>;
    icon: string;
  }

  const scheduleStore = userScheduleStore();
  const { scheduleTab } = storeToRefs(scheduleStore);
  const lastUserId = getUserId() || '';

  const { t } = useI18n();
  const userStore = useUserStore();
  const { logout } = useUser();
  const avatar = computed(() => {
    return userStore.avatarToken;
  });
  const projectTemplate = computed(() => userStore.projectTemplate);
  const username = computed(() => userStore.username);
  const storeCompanyId = computed(() => userStore.companyId);

  const globalModeStore = useGlobalModeStore();

  const route = useRoute(); // 获取当前路由信息
  const { changeLocale } = useLocale();

  // 获取当前使用的语言
  const currentLocale = computed(
    () => localStorage.getItem('arco-locale') || 'zh-CN'
  );

  // 切换公司后 则获取项目列表数据 设置last_projectId 当前页面路由包含projectId则替换项目路由projectId 刷新页面 否则直接刷新页面
  const getProjectListHandle = async () => {
    const currentUrl = window.location.pathname.replace('/work', '');
    // 带项目id的页面
    const params = {
      pageNo: 1,
      pageSize: 20,
      projectType: projectTemplate.value === '1' ? 1 : 0,
    };
    const userId = getUserId() || '';

    // 获取当前身份下的项目列表 存储项目id
    const res = await getProjectList(params);

    if (res.status && res.data.list?.length) {
      const newId: string = res.data.list[0]?.id;
      setLocalstorage(`last_project_${userId}`, newId);
      // 含有projectId的页面 替换路由项目id 刷新页面
      const newUrl = currentUrl.replace(/\/project\/\d+/, `/project/${newId}`);
      if (currentUrl.includes('/project/')) router.replace(newUrl);
    } else {
      // 项目列表无数据 回到首页
      router.push({
        path: '/home-page',
      });
    }
    setTimeout(() => {
      window.location.reload(); // 刷新页面
    }, 100);
  };

  // 添加处理账号选择的方法
  const handleAccountSelected = (account: any) => {
    // 如果选择的账号ID和当前store中的companyId相同，直接返回
    if (account.id === storeCompanyId.value) {
      return;
    }

    // 清除项目相关的localStorage
    removeLocalstorageItem(`last_project_${lastUserId}`);

    // 设置新的companyId
    userStore.setCompanyId(account.id);

    if (!account.id) {
      // 切换私人空间时需要跳转到个人空间首页
      globalModeStore.changeGlobalMode('work');
      if (route.path !== '/home-page')
        router.push({
          name: 'dashboard',
        });
    } else {
      getProjectListHandle();
    }
    // else {
    //   removeLocalstorageItem(`last_project_${lastUserId}`);
    //   globalModeStore.changeGlobalMode('');
    //   if (route.path !== '/home-page')
    //     router.push({
    //       name: 'homePage',
    //     });
    // }
  };

  const triggerBtn = ref();
  // 项目id
  const projectId = computed(() => {
    return String(route?.params?.projectId);
  });

  // 项目id
  const projectCode = ref('');
  // 能够显示滚动条项目编码数组
  const showMarquee = ['CDex001'];
  const setDropDownVisible = () => {
    const event = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
    });
    triggerBtn.value.dispatchEvent(event);
  };

  const userId = computed(() => {
    return userStore.id;
  });
  const userFullname = computed(() => {
    // return userStore.name || t('settings.navbar.not-logged-in');
    return userStore.userFullname;
  });
  const nickname = computed(() => {
    if (userFullname.value) {
      return userFullname?.value.substring(0, 1);
    }
    return '';
  });
  const teamList: any = computed(() => {
    return userStore.teamList;
  });

  const handleLogout = () => {
    logout();
  };

  interface Team {
    id: string | number;
    teamName: string;
  }

  const STORAGE_KEY = 'selectedTeamId';
  const isTeamListOpen = ref(false);
  const currentTeam = ref('全局视角');

  const handleOutsideClick = (event: MouseEvent) => {
    const teamSelector = document.querySelector('.team-selector');
    if (teamSelector && !teamSelector.contains(event.target as Node)) {
      isTeamListOpen.value = false;
    }
  };

  const toggleTeamList = () => {
    isTeamListOpen.value = !isTeamListOpen.value;
  };

  const selectTeam = (team: Team) => {
    // console.log("选择团队", team);
    currentTeam.value = team.teamName;
    isTeamListOpen.value = false;
    userStore.setTeamId(team.id.toString());
    // console.log("设置团队ID", userStore.teamId);
  };

  const setTeamById = (teamId: string) => {
    if (teamId === 'global') {
      currentTeam.value = '全局视角';
      return;
    }

    const team = teamList.value.find((a) => a.id.toString() === teamId);
    if (team) {
      currentTeam.value = team.teamName;
    } else {
      currentTeam.value = '全局视角';
      localStorage.setItem(STORAGE_KEY, 'global');
    }
  };

  const loadTeamList = async () => {
    try {
      const params = { companyId: '100000' };
      const { data } = await searchTeamListApi(params);
      userStore.setTeamList(data);

      const savedTeamId = localStorage.getItem(STORAGE_KEY) || 'global';
      setTeamById(savedTeamId);
    } catch (error) {
      console.error('加载团队数据失败:', error);
      currentTeam.value = '全局视角';
    }
  };

  // 修改后的方法
  const loadCDexWorkUserInfo = async () => {
    try {
      const params = {
        userName: username.value,
      };
      const result: any = await getCDexWorkUserInfoApi(params);
      const createUserParam = result?.data?.createUserParam;
      // console.log("createUserParam", createUserParam);
      // 头像
      if (createUserParam?.avatarToken) {
        userStore.avatarToken = createUserParam.avatarToken;
      }
    } catch (err) {
      console.log('getCDexWorkUserInfoApi', err);
      // Message.error(t('navbar.user.info.load.failed'));
    }
  };

  const globalMode = computed({
    get: () => {
      return globalModeStore.getGlobalMode;
    },
    set: (mode) => {
      globalModeStore.changeGlobalMode(mode);
    },
  });

  // 跳转首页方法
  const toHomePage = () => {
    globalModeStore.changeGlobalMode('');
    router.push({
      name: 'homePage',
    });
  };
  const toApproveApply = () => {
    router.push({
      name: 'approveApply',
    });
    globalModeStore.changeGlobalMode('');
  };

  // 获取项目详情数据
  const getProjectDetail = async () => {
    const projectTempId = projectId.value;
    if (
      !projectTempId ||
      projectTempId === 'undefined' ||
      globalMode.value !== 'project'
    )
      return;
    console.log(projectTempId, 'getProjectDetail');
    console.log(projectTempId, 'getProjectDetail');
    const { data, status } = await queryProjectDetail({
      id: projectTempId,
    });
    console.log('获取项目详情');
    console.log(data, status);
    if (status) {
      projectCode.value = data.code;
    }
  };

  // 监听projectId的变化
  watch(
    () => projectId.value,
    (newValue) => {
      if (
        newValue &&
        newValue !== 'undefined' &&
        globalMode.value === 'project'
      ) {
        getProjectDetail();
      } else {
        projectCode.value = '';
      }
    },
    { immediate: true }
  );

  onMounted(() => {
    loadCDexWorkUserInfo();
    loadTeamList();
    document.addEventListener('click', handleOutsideClick);
  });

  onBeforeUnmount(() => {
    document.removeEventListener('click', handleOutsideClick);
  });

  const isTeamFlag: any = computed(() => {
    const routeList = ['/dashboard', '/home-page'];
    let flag = true;
    if (route.path !== '/schedule') {
      flag = !routeList.includes(route.path);
    } else
      flag = scheduleTab.value !== 'calendar'; // 日程页面 日历、会议事项新建不展示 会议事项列表页展示
    return flag;
  });

  const hoverAccountId = ref(null);
  const accounts = reactive<Account[]>([]);

  const loadCompanyList = async () => {
    try {
      const { data } = await getCompanyListBySelf();
      // 情况1: 如果数据是null或者数组的长度为0，保留私人空间
      if (!data || data?.length === 0) {
        accounts.splice(0, accounts.length, {
          id: '',
          type: 'personal',
          name: computed(() => t('account.switcher.personal')),
          userFullName: userFullname,
          icon: '个',
        });
        return;
      }
      // 情况2: 如果数据长度大于等于1，只展示企业数据
      const companyAccounts = data.map((company) => ({
        id: company.id,
        type: 'organization',
        name: company.companyName,
        userFullName: userFullname.value,
        icon: '企',
      }));
      accounts.splice(0, accounts.length, ...companyAccounts);
    } catch (error) {
      console.error('获取公司列表失败', error);
    }
  };

  const companyId = computed(() => {
    return userStore.companyId;
  });

  // 监听globalMode的变化
  watch(
    () => globalModeStore.getGlobalMode,
    (newValue) => {
      console.log('globalMode changed:', newValue);
    }
  );
</script>

<style scoped lang="less">
  :deep(.arco-divider-vertical) {
    border-left: 1px solid #d9d9d9;
  }
  .navbar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 72px;
    background-color: #f5f6fb;
    padding-left: 20px;
  }

  .left-side {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 166px;
    height: 50px;
    margin-right: 20px;
    padding-left:12px;
    border-radius:6px;
    &:hover{
      background-color:#E0E4EC;
    }
  }
  .logoTop {
    width: 42px;
    height: 42px;
  }

  .center-side {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    .center-side-left {
      border: 1px solid #c9cdd4;
      border-radius: 4px;
    }
    .center-side-right {
      display: flex;
      align-items: center;
      .body {
        padding: 0 18px;
      }
      .languageBox {
        width: 23px;
        height: 23px;
        margin: 0 10px;
        .nav-btn :hover {
          background: transparent;
        }
        &:hover {
          cursor: pointer;
        }
        // arco-btn arco-btn-secondary arco-btn-shape-circle arco-btn-size-medium arco-btn-status-normal arco-btn-only-icon nav-btn
      }
    }
    .return-home {
      color: #4e5969;
      font-size: 14px;
      span {
        margin-left: 4px;
      }
      cursor: pointer;
    }
    //max-width: 860px;
  }
  .mode-box {
    display: flex;
    align-items: center;
    // margin-right: 8px;
    .mode-logo {
      width: 32px;
      height: 32px;
      background-color: rgba(var(--primary-6));
      border-radius: 4px;
      font-size: 17px;
      color: #ffffff;
      line-height: 32px;
      display: flex;
      justify-content: center;
    }
    :deep(.mode-select) {
      background-color: rgba(var(--primary-1));
    }
    :deep(.project-name) {
      display: inline-block;
      min-width: 300px;
      max-width: 800px;
      width: calc(100% - 30px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: left;
    }
  }

  .g-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    img {
      margin-right: 8px;
    }
  }
  .prj-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(var(--primary-6));
    margin-right: 8px;
    margin-bottom: 1px;
  }

  .right-side {
    display: flex;
    font-size: 16px;
    list-style: none;
    padding: 0px;
    padding-left: 16px;
    margin-right: 16px;
    :deep(.locale-select) {
      border-radius: 20px;
    }
    li {
      display: flex;
      align-items: center;
      //padding: 0 10px;
    }

    a {
      color: var(--color-text-1);
      text-decoration: none;
    }
    .nav-btn {
      border-color: rgb(var(--gray-2));
      color: rgb(var(--gray-8));
      font-size: 16px;
    }
    .trigger-btn,
    .ref-btn {
      position: absolute;
      bottom: 14px;
    }
    .trigger-btn {
      margin-left: 14px;
    }
  }
  .user-name {
    padding-right: 8px;
    cursor: pointer;
    font-family: 'PingFang SC';
    font-weight: 500;
  }
  .trigger-box {
    display: flex;
    align-items: center;
  }
  .demo-arrow {
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
    padding: 10px;
    width: 350px;
    height: 400px;
    background-color: var(--color-bg-popup);
    border-radius: 4px;
  }
  :deep(.arco-select-dropdown-list-wrapper) {
    max-height: 300px !important;
  }

  .team-selector {
    position: relative;
    //margin: 0 16px;
    max-width: 848px;
    min-width: 348px;
    // margin-left: 16px;
    // margin-right: 4px;
    font-family: 'PingFang SC';

    .team-header {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 4px;
      height: 32px;
      font-family: Inter, '-apple-system', BlinkMacSystemFont, 'PingFang SC',
        'Hiragino Sans GB', 'noto sans', 'Microsoft YaHei', 'Helvetica Neue',
        Helvetica, Arial, sans-serif;

      &:hover {
        background-color: var(--color-fill-2);
      }

      .team-text {
        margin-right: 8px;
        font-size: 14px;
        color: #4e5969;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }

      .icon-rotate {
        transform: rotate(180deg);
        flex-shrink: 0;
      }
    }

    .team-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      //width: 220px;
      width: 348px;
      max-height: 300px;
      overflow-y: auto;
      background-color: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      border-radius: 8px;
      z-index: 1000;
      margin-top: 4px;

      .team-item {
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.4;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &:hover {
          background-color: var(--color-fill-2);
        }

        &-active {
          background-color: var(--color-fill-2);
        }
      }

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: var(--color-fill-3);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: var(--color-fill-1);
      }
    }
  }

  .dropdown-item {
    position: relative;
    width: 100%;

    .arco-space {
      width: 100%;
      padding: 8px 12px;
    }
  }

  .account-list {
    min-width: 300px;
  }

  :deep(.arco-dropdown-option) {
    padding: 0;
    margin: 4px 0;
    width: 100%;

    &:hover {
      background-color: transparent;
    }
  }
  :deep(.arco-dropdown-option-content) {
    width: 100%;
  }

  .account-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    width: 100%;

    &:hover {
      background-color: #f2f3f5;
    }

    .account-icon {
      width: 44px;
      height: 44px;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: white;
      margin-right: 8px;
      flex-shrink: 0;

      &.organization {
        background-color: #165dff;
      }

      &.personal {
        background-color: #00b42a;
      }
    }

    .account-info {
      flex: 1;
      max-width: 200px;
      overflow: hidden;

      .account-name {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #1d2129;
        line-height: 21px;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .account-user {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #4e5969;
        line-height: 21px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .check-icon {
      color: #165dff;
      font-size: 16px;
      margin-left: 12px;
      flex-shrink: 0;
    }
  }

  :deep(.arco-dropdown) {
    .arco-dropdown-menu {
      padding: 8px;
    }

    .arco-space {
      width: 100%;
      padding: 8px 12px;
    }
  }

  .icon-rotate {
    transform: rotate(-90deg);
    transition: transform 0.2s;
  }

  .marquee {
    position: absolute;
    top: calc(50% - 15px);
    left: 382px;
    width: 50%;
    height: 30px;
    // background-color: transparent;

    background-color: var(--color-danger-light-1);
    overflow: hidden;
    border-radius: 4px;
    z-index: 1;
  }

  .marquee-content {
    width: auto;
    white-space: nowrap;
    animation: marquee 18s linear infinite;
    color: rgb(var(--danger-6));
    font-size: 15px;
    line-height: 30px;
    background-color: transparent;
  }

  @keyframes marquee {
    0% {
      transform: translateX(15%);
    }
    100% {
      transform: translateX(-120%);
    }
  }
</style>
