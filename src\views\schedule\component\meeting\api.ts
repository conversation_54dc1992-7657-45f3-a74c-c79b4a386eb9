import axios from 'axios';

// 分页查询参数
export interface TeamSearchParams {
  pageNo: number;
  pageSize: number;
  projectId: string;
  name?: string;
}
// 获取会议列表数据
export function getMeetingData(params: any) {
  return axios.get('/cde-work/meetings/list', {
    params,
  });
}
// 获取会议详情数据
export function getMeetingDetail(id: any) {
  return axios.get(`/cde-work/meetings/${id}`, {});
}

// 新增评论
export function saveComment(data: any) {
  return axios.post('/cde-work/agenda/saveComment', data);
}

// 删除评论
export function deleteComment(data: any) {
  return axios.delete(
    `/cde-work/agenda/deleteCommentId/${data.commentId}?username=${data.username}`
  );
}

// 删除会议
export function removeMeeting(param: any) {
  return axios.delete(
    `/cde-work/meetings/${param.id}?rmOption=${param.rmOption}`
  );
}

// 创建会议
export function createMeeting(data: any) {
  return axios.post('/cde-work/meetings/save', data);
}

// 批量新增事项
export function saveBatchMaater(data: any) {
  return axios.post('/cde-work/agenda/saveBatch', data);
}

// 编辑事项
export function editMatter(data: any) {
  return axios.post('/cde-work/agenda/edit', data);
}

// 获取所属项目
export const getProjectList = (params: any) => {
  return axios.get('/cde-work/agenda/cdeProjectList', {
    params,
  });
};

// 生成会议通知
export function addMeetingNotify(meetingId: any) {
  return axios.post(`/cde-work/meetings/${meetingId}/notify`);
}

// 生成会议纪要
export function addMeetingMinutes(meetingId: any) {
  return axios.post(`/cde-work/meetings/${meetingId}/minutes`);
}

// 通过文件token获取文字
export const setFiletokenText = (params: any) => {
  return axios.get('/sys-storage/string/fileToken', {
    params,
  });
};

// 通过文件token获取到事项数据
export function getAiAnaly(data: any) {
  return axios.post(`/cde-work/agenda/meetAiAnalyzeByFileToken`, data);
}
// 文字转事项
export function meetAiAnalyze(data: any) {
  return axios.post(`/cde-work/agenda/meetAiAnalyze`, data);
}
// 添加会议附件
export function addAttachments(meetingId: any, data: any) {
  return axios.post(`/cde-work/meetings/${meetingId}/attachments`, data);
}
// 删除会议附件
export function removeAttachments(attachmentFileId: any, meetingId: any) {
  return axios.delete(
    `/cde-work/meetings/${meetingId}/attachments/${attachmentFileId}`
  );
}
// 获取项目下的人员
export const getProjectUsers = (params: any) => {
  return axios.get('/cde-collaboration/project/getUsers', {
    params,
  });
};
// 查询团队列表
export function queryTeamList(params: TeamSearchParams) {
  return axios.get('/cde-collaboration/team/list', {
    params,
  });
}
// 进入到项目日历，获取该项目的项目日历
export function getProjectPanel(params: any) {
  return axios.get('/cde-collaboration/schedule/project/panel', { params });
}

// 添加合并文件的信息
export function addMergaFile(data: any) {
  return axios.post('/cde-collaboration/file/save', data);
}
