<template>
  <div>
    <a-modal
      :visible="visible"
      :title="
        visibleType === 0
          ? $t('file-manage.upload-file')
          : $t('file-manage.upload-folders')
      "
      :unmount-on-close="true"
      :mask-closable="false"
      draggable
      :esc-to-close="false"
      width="420px"
      @cancel="handleCancel"
      @before-close="handleBeforeClose"
    >
      <chunk-upload
        :upload-files-or-folders="visibleType"
        :accept="acceptFileType"
        is-multiple
        :disabled="uploadFileStore.loading"
        :engine-percentages="enginePercentages"
        :is-start-upload="isStartUpload"
        @upload-error="handleUploadError"
        @line-change="handleLineChange"
      >
        <!-- :direction="visibleType === 0 ? '' : 'vertical'" -->
        <template #default>
          <a-upload
            :draggable="true"
            multiple
            :auto-upload="false"
            :show-file-list="false"
            :directory="visibleType !== 0"
            :file-list="aUploadFile"
            @change="onChange"
          >
            <template #upload-button>
              <div class="arco-upload-wrapper arco-upload-wrapper-type-picture">
                <span class="arco-upload arco-upload-draggable">
                  <div class="arco-upload-drag">
                    <svg
                      viewBox="0 0 48 48"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      stroke="currentColor"
                      class="arco-icon arco-icon-plus"
                      stroke-width="4"
                      stroke-linecap="butt"
                      stroke-linejoin="miter"
                    >
                      <path d="M5 24h38M24 5v38"></path>
                    </svg>
                    <div class="arco-upload-drag-text"></div>
                  </div>
                </span>
              </div>
            </template>
          </a-upload>
        </template>
      </chunk-upload>
      <template #footer>
        <div class="footer">
          <span>{{ $tc('file-manage.all-files', fileList.length) }}</span>
          <a-space>
            <a-button @click="handleCancel">{{
              $t('file-manage.cancel')
            }}</a-button>
            <a-button
              id="picker"
              type="primary"
              :disabled="fileList.length === 0"
              :loading="uploadFileStore.loading"
              @click="handleSubmit"
              >{{ $t('file-manage.confirm') }}</a-button
            >
          </a-space>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { storeToRefs } from 'pinia';
  import ChunkUpload from '@/components/chunk-upload/index.vue';

  import acceptFileType from '@/config/accept-file-types.json';
  import appleHidesFiles from '../json/apple-hides-files.json';
  import { useI18n } from 'vue-i18n';

  import useUploadFileStore from '@/store/modules/upload-file/index';
  import { useDebounceFn } from '@vueuse/core';
  import useFileStore from '@/store/modules/file/index';
  import { encode } from 'js-base64';
  import { uploadFolder } from '@/api/upload-file';

  const emits = defineEmits([
    'uploadComplete',
    'handleCancel',
    'uploadSingleSuccess',
    'startUpload',
  ]);
  const { t } = useI18n();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
    visibleType: {
      type: Number,
      default: 0,
      required: true,
    },
    selectedFolder: {
      type: Object,
      required: true,
    },
    modelFile: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  const fileStore = useFileStore();

  const uploadFileStore = useUploadFileStore();

  const { fileArr, loading, uploadFileList } = storeToRefs(uploadFileStore);

  const fileList = fileArr;
  const enginePercentages = ref<boolean[]>([]);
  const isStartUpload = ref<boolean>(false);

  /** 存储上传的文件格式属性 ext */
  // const fileExtCount: string[] = [];

  const aUploadFile = ref<any[]>([]);
  const handleFileUploadFn = (newFileList: any[]) => {
    aUploadFile.value = newFileList.slice();
    const file = newFileList[newFileList.length - 1];

    if (!acceptFileType.includes(file.name.split('.').pop())) {
      if (!appleHidesFiles.includes(file.name.split('.').pop())) {
        Message.error(`不能上传${file.name.split('.').pop()}文件`);
      }
      newFileList.pop();
    }
    uploadFileStore.handleFileArr(newFileList, props.visibleType);
    console.log(
      uploadFileStore.fileArr[0]?.name,
      '上传的文件名称',
      props.modelFile
    );
    // 检查文件名称是否一致
    if (
      uploadFileStore.fileArr[0]?.name !==
      (props.modelFile?.fileName || props.modelFile.name)
    ) {
      Message.error(t('cloud.file-name-mismatch'));
      uploadFileStore.fileArr.splice(0); // 清空数组
    }

    // 限制文件数量为1
    if (uploadFileStore.fileArr.length > 1) {
      Message.error('仅允许上传单个文件');
      uploadFileStore.fileArr.pop();
    }

    aUploadFile.value = [];
    console.log(uploadFileStore.fileArr, '文件列表');
  };
  const debounceHandleFileUploadFn = useDebounceFn((newFileList) => {
    handleFileUploadFn(newFileList);
  }, 100);
  const onChange = (newFileList: any) => {
    if (props.visibleType === 0) {
      debounceHandleFileUploadFn(newFileList);
    } else {
      handleFileUploadFn(newFileList);
    }
  };

  // 文件上传失败
  const handleUploadError = (failType: any, file: any, res: any) => {
    if (failType !== 1) {
      Message.info(t('file-manage.files-upload-failed', { name: file.name }));
    }
  };

  // 网络变化
  const handleLineChange = (isOnline: boolean) => {
    if (!isOnline) {
      Message.error(t('file-manage.network-disconnected'));
    }
  };

  let allFolders: any[] = [];
  const uploadSuccessCb = (file: any) => {
    const params: any = {
      fileToken: file.fileToken,
      name: file.name,
      size: file.size,
      description: file.description,
      projectId: props.selectedFolder.projectId,
      teamId: props.selectedFolder.teamId,
    };
    // 文件夹
    if (props.visibleType !== 0) {
      const pathString = file.webkitRelativePath;
      // 使用split方法以"/"为分隔符分割字符串
      const pathParts = pathString.split('/');

      // 获取倒数第二个元素
      const folderName = pathParts[pathParts.length - 2];
      // 主要用于上传文件夹时设置文件所处文件夹id
      allFolders.some(function (item1: any): boolean {
        if (item1.name === encode(folderName)) {
          params.folderId = item1.id;
          return true;
        }
        return false;
      });
    } else {
      // 文件
      params.folderId = props.selectedFolder.id;
    }
    emits('uploadSingleSuccess', params);
  };

  async function createFolder(selectedFolder: {
    path: string;
    projectId: string;
    teamId: string;
    type: string;
    parentId: string;
    id: string;
    idPath: string;
  }) {
    const folders: {
      oldPath: string;
      path: string;
    }[] = [];

    // 提取文件夹路径信息，生成文件夹结构
    fileArr.value.forEach((item: any) => {
      const pathString = item.file.webkitRelativePath;
      const pathParts = pathString.split('/');
      const newPathString: string = pathParts
        .slice(0, pathParts.length - 1)
        .join('/');
      const folderName = pathParts[pathParts.length - 2];

      folders.push({
        path: encode(`${selectedFolder.path}/${newPathString}`),
        folderName,
        oldPath: newPathString,
        projectId: selectedFolder.projectId,
        teamId: selectedFolder.teamId,
        type: selectedFolder.type,
      });
    });

    // 根文件夹数组
    const rootFolders: any[] = [];

    // 查找或创建父文件夹
    // eslint-disable-next-line no-shadow
    function findParentFolder(folders: any[], oldPath: string) {
      const parts = oldPath.split('/').slice(0, -1); // 去掉最后一部分
      let currentParent = folders;

      parts.forEach((part, index) => {
        // 查找当前部分文件夹是否存在
        let found = currentParent.find(
          (folder) => folder.name === encode(part)
        );
        if (!found) {
          // 如果没有找到，则创建文件夹
          found = {
            name: encode(part),
            path: encode(
              `${selectedFolder.path}/${parts.slice(index + 1).join('/')}`
            ),
            oldPath: parts.slice(index + 1).join('/'),
            teamId: selectedFolder.teamId,
            type: selectedFolder.type,
            projectId: selectedFolder.projectId,
            folderAllDtoList: [],
          };
          currentParent.push(found); // 加入当前父级
        }
        currentParent = found.folderAllDtoList; // 更新当前父文件夹的子文件夹列表
      });

      return currentParent;
    }

    // 遍历文件夹，构建目录结构
    folders.forEach((folder) => {
      const parts = folder.oldPath.split('/');
      const folderName = parts.pop(); // 获取文件夹名称
      const parentFolder = findParentFolder(rootFolders, folder.oldPath);

      // 创建文件夹（如果不存在）
      const existingFolder = parentFolder.find(
        (child) => child.name === encode(folderName!)
      );
      if (!existingFolder) {
        parentFolder.push({
          ...folder,
          name: encode(folderName!),
          folderAllDtoList: [],
        });
      }
    });

    // 设置根文件夹的parentId为选中的文件夹ID
    rootFolders[0].parentId = selectedFolder.id;
    rootFolders[0].idPath = selectedFolder.idPath;

    console.log('start=====>', fileArr.value);
    console.log('end=====>', rootFolders[0]);
    // 上传文件夹
    const result = await uploadFolder(rootFolders[0]);

    // 扁平化文件夹列表
    allFolders = uploadFileStore.flattenFolders(result.data);
  }

  // 上传按钮点击事件
  const handleSubmit = async () => {
    if (props.visibleType !== 0) await createFolder(props.selectedFolder);
    const files = fileArr.value.map((file: any) => {
      return {
        ...file,
        uploadSuccessCb,
      };
    });
    emits('startUpload');

    await uploadFileStore.handleUploadFile(files);
  };

  // 弹窗关闭前，清空数据
  const handleBeforeClose = () => {
    fileList.value = [];
    loading.value = false;
  };

  // 取消按钮点击事件
  function handleCancel() {
    emits('handleCancel');
  }

  // 触发上传完成事件
  watch(
    () => uploadFileList.value.length,
    (val) => {
      if (!val) {
        handleCancel();
        emits('uploadComplete');
      }
    }
  );
</script>

<style lang="less" scoped>
  /* 覆盖文件拖拽上传默认样式 */
  :deep(.arco-upload-drag) {
    padding: 40px 0;
    background-color: var(--color-fill-2);
    border-radius: var(--border-radius-medium);
    .arco-icon-plus {
      margin-bottom: 0;
    }
  }

  /* 拖动文件到拖动框内样式 */
  :deep(.webuploader-dnd-over) {
    .arco-upload-drag {
      border: 1px dashed rgb(var(--primary-6));
      svg {
        color: rgb(var(--primary-6));
      }
    }
  }

  /* 拖动框hover样式 */
  :deep(.webuploader-pick-hover) {
    .arco-upload-drag {
      border: 1px dashed var(--color-neutral-4);
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      color: var(--color-text-2);
    }

    /* button {
      border-radius: var(--border-radius-medium);
    } */
  }
</style>
