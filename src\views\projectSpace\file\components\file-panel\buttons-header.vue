<template>
  <div class="button-box">
    <a-dropdown
      v-if="!disabled"
      v-permission="`${currentFolder.teamId}_${$btn.file.upload}`"
      trigger="hover"
      hide-on-select
    >
      <a-button :disabled="disabled" type="primary" @click="upload(0)">
        <template #icon>
          <icon-upload />
        </template>
        <template #default>{{ $t('file-manage.upload') }}</template>
      </a-button>
      <template #content>
        <a-doption :value="0" @click="upload(0)">{{
          $t('file-manage.upload')
        }}</a-doption>
        <a-doption :value="1" @click="upload(1)">{{
          $t('file-manage.upload-folders')
        }}</a-doption>
      </template>
    </a-dropdown>
  </div>
</template>

<script lang="ts" setup>
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import useFileStore from '@/store/modules/file/index';
  import { computed } from 'vue';

  const fileStore = useFileStore();
  const projectStore = usePrjPermissionStore();
  const emits = defineEmits(['handleUpload']);
  const currentFolder = computed(() => fileStore.currentFolder);

  const props = defineProps({
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  function upload(uploadType: number) {
    emits('handleUpload', uploadType);
  }
</script>
