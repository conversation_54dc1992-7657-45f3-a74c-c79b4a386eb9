import CryptoJS from 'crypto-js';
import config from '@/config/xbase-config.json';
import initDXY from './utils/addScript';
import {
  modelViewToken,
  queryModelAttributes,
  getComponentProperty,
} from './api';
import { Marker } from './tools/marker';
import { MarkerType, ViewerType, ViewerParams } from './type';
import { model2d } from '@/utils/BIM-Engine/XBase/format';
import i18n from '@/locale/index';
import useModelToolsStore from '@/store/modules/model-viewer/index';
import { storeToRefs } from 'pinia';
import { Message } from '@arco-design/web-vue';
// 引入esmodule版本的模型树
import { TreeViewer } from './components/modal_tree/xcomponent';

const store = useModelToolsStore();
const { modelAssemblyActive, ModelAssemblyType } = storeToRefs(store);

// 平移所需要的坐标数据
let movePoint: any = {
  startPoint: null,
  endPoint: null,
  motionPath: null,
};

// 递归获取模型id
const getModelIds = async (data: any) => {
  const id: any = [];
  const getId = (value: any) => {
    value?.forEach((item: any) => {
      id.push(item.id);
      if (item.children?.length) getId(item.children);
    });
  };
  await getId(data);
  return id;
};

const projectIdMatch = window.location.href.match(/projectId=([^&]+)/);
const projectId = projectIdMatch ? projectIdMatch[1] : null;
const { locale } = i18n.global;
declare const DX: any;

/**
 * 获取viewer_token
 * @param fileId
 * @param viewerType
 * @returns
 */
const getViewerToken = async (fileId: string, viewerType: string) => {
  // model（模型）、component（构件）、asset（资产）、scene（场景）
  const token = await modelViewToken({
    file_id: fileId,
    viewer_type: viewerType,
  });
  return {
    Authorization: `Bearer ${token.data.token}`,
  };
};

export default class Viewer implements ViewerType {
  elementId: string;

  private fileId: string;

  private viewerType: string;

  private renderPath: string;

  private options: any;

  private toolBar: any;

  private app: any;

  private viewer: any;

  marker: MarkerType;

  constructor({
    elementId,
    fileId,
    viewerType,
    renderPath,
    toolBar,
  }: ViewerParams) {
    this.elementId = elementId || '';
    this.fileId = fileId || '';
    this.viewerType = viewerType || '';
    this.renderPath = renderPath || '';
    this.toolBar = toolBar || null;
    this.marker = new Marker(null);
  }

  async rander() {
    try {
      // 导入DX资源
      if (!('DX' in window)) await initDXY();
      // 初始化options
      this.options = new DX.DefaultConfigs();
      // 设置静态资源域的地址
      this.options.staticHost = `${window.origin}${config.staticHost}`;
      // 设置服务域的地址
      // this.options.serverHost = `${window.location.protocol}//${window.location.hostname}/x_base_api/viewer`;
      this.options.serverHost = `${window.origin}/x_base_api/viewer`;
      // 添加token
      this.options.accessToken = await getViewerToken(
        this.fileId,
        this.viewerType
      );
      // 渲染模式, 默认是融合版 HYBRID 即三角面数复杂则调用云渲染 三角面数少则调用本地渲染
      this.options.engine = DX.mode.HYBRID;
      if (model2d.includes(this.renderPath.split('.').slice(-1)[0])) {
        this.options.engine = DX.mode.TWOD;
      } else if (this.viewerType === 'canvas') {
        this.options.engine = DX.mode.WEBGL;
      }
      // 设置默认的toolbar功能
      // options.mainToolbar = [];

      this.app = new DX.Application(this.elementId);

      await this.app.init(this.options);
      if (this.toolBar) {
        if (this.toolBar.isToolbar) {
          this.app.viewer.setToolbarItems(this.toolBar.tools);
        } else {
          this.app.viewer.setToolbarItems([]);
        }
      } else {
        const { renderPath } = this;
        const componentTreeKey = 'COMPONENT_TREE';
        const toolbar = this.app.viewer.getToolbarItems();
        if (window.innerWidth > 768) {
          toolbar.splice(0, 0, componentTreeKey);
        }

        this.app.viewer.createCustomButton({
          key: componentTreeKey,
          hasActive: true,
          title: '构件树',
          update: false,
          className: 'xicon-member-tree',
          handle: (v: boolean) => {
            store.setModalTreeIsShow(v);
          },
        });

        this.app.viewer.setToolbarItems(toolbar);

        const componentTree = new TreeViewer(
          '.tree-container' || document.body,
          {
            previewToken: this.options.accessToken.Authorization,
            baseUrl: `${window.origin}/x_base_api`,
            renderPath,
            menuKey: componentTreeKey,
            dragAreaClass: 'contenter',
            app: this.app,
            onClose: () => {},
          }
        );

        /**
         * 构件树——————旧版本
         */
        // this.app.pluginManager.install(
        //   new DX.Plugins.ModelTreePlugin(this.app)
        // );
        // this.app.viewer.setToolbarItems([
        //   !this.options.mainToolbar.includes('show_model_tree')
        //     ? 'show_model_tree'
        //     : '',
        //   ...this.options.mainToolbar,
        // ]);
      }

      this.viewer = this.app.getViewer();

      await this.app.mainWindow.openFile(this.renderPath);
      this.viewer.setLanguage(
        locale === 'zh-CN' ? DX.lang.ZH_CN : DX.lang.EN_US
      );

      // 模型装配点
      const point: any = document.getElementById('point');

      // 显示/隐藏点位
      const pointshow = (value: any) => {
        point.style.display = value;
      };

      // 设置选中模型初始化
      const selectModelInit = (type: any) => {
        this.viewer.enableSelection(false); // 设置不允许选择构件
        store.setMotionPath('');

        // 旋转
        if (type === 'rotate') {
          this.app.execute(DX.Commands.ENABLE_SNAP, { toggled: false });
        } else if (type === 'translation') {
          pointshow('none');
          this.app.execute(DX.Commands.ENABLE_SNAP, { toggled: true });
        } else {
          this.app.execute(DX.Commands.ENABLE_SNAP, { toggled: false });
          this.viewer.enableSelection(true);
        }
      };

      // 模型装配-旋转-选中模型
      const rotateSelectModel = async (select: any, motionPath?: any) => {
        // 清空构件选中状态
        this.viewer.clearSelectedEntities();
        if (!select) return;
        const treeNode = this.viewer.getModelTreeNodeByPath(motionPath);
        if (!treeNode) return;
        const stack = treeNode.children.slice();
        this.viewer.enableSelection(true);
        const ids = await getModelIds(stack);
        this.viewer.selectEntities({ ids });
        this.viewer.enableSelection(false);
      };

      // 模型平移
      const translation = (data: any) => {
        this.viewer.translateModel(
          data.motionPath,
          data.startPoint,
          data.endPoint
        );
        // 点位重置
        movePoint.motionPath = null;
        movePoint.startPoint = null;
        movePoint.endPoint = null;
      };

      // 设置平移选中点位
      const updateStartPoint = (x: any, y: any) => {
        pointshow('block');
        point.style.left = `${x - 3}px`;
        point.style.top = `${y - 3}px`;
      };

      // 模型装配平移初始化配置
      const pointPicked = () => {
        // 平移模式下禁用构件选中功能
        this.viewer.enableSelection(false);
        // 默认开启捕捉,可捕捉点，线，面
        this.app.execute(DX.Commands.ENABLE_SNAP, { toggled: true });
        // 限制捕捉类型DX.Snap3DTypes.piont/line/face
        this.viewer.setSnapTypes([DX.Snap3DTypes.Point]);
        // 关闭页面左下角坐标轴
        this.viewer.enableAxes(false);
        // 开启时关闭viewcube
        this.viewer.enableViewCube(false);
      };

      this.viewer.on(DX.Events.SCENE_READY, function () {
        if (ModelAssemblyType.value === 'translation') {
          pointPicked();
        }
      });

      /* 解决3D测量和模型装配冲突 */
      const thatApp = this.app;
      this.app.commandManager.commands
        .get(DX.Commands.ENABLE_MEASURE)
        .on(DX.Events.COMMAND_TOGGLED, (data: any) => {
          if (data)
            thatApp.execute(DX.Commands.ENABLE_SNAP, { toggled: false });
        });

      // 监听选择构件
      this.viewer.on(DX.Events.POINT_PICKED, function (data: any) {
        selectModelInit(ModelAssemblyType.value);
        if (!data.type) {
          // 点击空白处
          pointshow('none');
          movePoint = {};
          return;
        }
        // 平移
        if (ModelAssemblyType.value === 'translation') {
          pointshow('block');
          if (!movePoint.motionPath) movePoint.motionPath = data.path;
          if (!data.clientPoint) return;
          // 设置初始点和结束点
          if (!movePoint.startPoint) {
            movePoint.startPoint = data.worldPosition;
            updateStartPoint(data.clientPoint.x, data.clientPoint.y);
            Message.info('请选择目标点');
          } else if (!movePoint.endPoint) {
            movePoint.endPoint = data.worldPosition;
            updateStartPoint(data.clientPoint.x, data.clientPoint.y);
          }
          // 平移
          if (movePoint.startPoint && movePoint.endPoint) {
            translation(movePoint);
            pointshow('none');
          }
        } else if (ModelAssemblyType.value === 'rotate') {
          pointshow('none');
          // 模型旋转
          rotateSelectModel(false);
          if (!modelAssemblyActive.value) return;
          const motionPath = data.path;
          store.setMotionPath(motionPath);

          // 旋转
          if (motionPath) {
            rotateSelectModel(true, data.path);
          }
        } else {
          pointshow('none');
        }
      });

      // 加载Marker工具
      this.marker = new Marker(this.viewer);
    } catch (e) {
      console.log(e);
    }
  }

  getModelId() {
    return this.fileId;
  }

  getCamera() {
    return this.viewer.getCamera();
  }

  setCamera(position: number[]) {
    this.viewer.setCamera(position);
  }

  reRender() {
    this.viewer.render();
  }

  modelTreeReady(func: () => void) {
    this.viewer.on(DX.Events.MODEL_TREE_READY, func);
  }

  setEntitiesColor(ids: any, custom = false) {
    let color: any = [];
    let states: any = [];
    if (!custom) {
      states = ids.map((item: string) => {
        return [item, 0];
      });
      color = [[130, 200, 150]];
      this.viewer.setEntitiesColor(states, color);
    } else {
      const idList = ids.map((item: any) => {
        return [
          CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(item[0])),
          item[1],
        ];
      });
      this.viewer.setEntitiesColor(idList);
    }
  }

  clearEntitiesColor(ids: string[]) {
    if (!Array.isArray(ids)) return;
    const idList = ids.map((item: string) => {
      return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(item));
    });
    this.viewer.clearEntitiesColor(idList);
  }

  // isBase64 :是否需要base64加密，默认为是
  selectEntities(id: string, isBase64 = true) {
    const param = {
      ids: isBase64
        ? [CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(id))]
        : [id],
      clear: true,
      goto: true,
    };
    console.log('[ param ] >', param);
    this.viewer.selectEntities(param);
  }

  gotoByIds(ids: string[]) {
    if (!Array.isArray(ids)) return;
    const idList = ids.map((item: string) => {
      return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(item));
    });
    this.viewer.gotoByIds(idList);
  }

  gotoByComponentIds(ids: string[]) {
    // renderPath: 当前预览的模型的路径
    // path: guid所在的模型路径
    // guid: 构件guid
    const suffix = this.renderPath?.substring(
      this.renderPath.lastIndexOf('.') + 1
    );
    let btoaGuid;
    if (suffix === 'asm') {
      const pathMapIndex = this.viewer?.engine?.pathMapIndex;
      const index = pathMapIndex.get(this.renderPath);
      btoaGuid =
        this.renderPath?.substring(this.renderPath.lastIndexOf('.') + 1) ===
        'ifc'
          ? btoa(`${index}.'${ids}'`)
          : btoa(`${index}.${ids}`);
    } else {
      btoaGuid = suffix === 'ifc' ? btoa(`'${ids}'`) : btoa(`${ids}`);
    }
    const res = this.viewer.getEntityChildrenIds(btoaGuid);
    this.viewer.gotoByIds(res); // 根据构件id集合将对应的构件实体缩放到视口中央
    this.viewer.clearEntitiesColor(); // 清除自定义的模型实体颜色
    this.viewer.setEntitiesColor([[res[0], 0]], [[110, 255, 110]]); // 设置模型实体颜色
  }

  isolateEntities(ids: string[]) {
    if (!Array.isArray(ids)) return;
    const idList = ids.map((item: string) => {
      return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(item));
    });
    this.viewer.isolateEntities(idList);
  }

  showAll() {
    this.viewer.showAll();
  }

  getEntitiesBBoxAsync(ids: string[]) {
    if (!Array.isArray(ids)) return null;
    const idList = ids.map((item: string) => {
      return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(item));
    });
    return this.viewer.getEntitiesBBoxAsync(idList);
  }

  gotoByBBox(bbox: []) {
    this.viewer.gotoByBBox(bbox);
  }

  toHome() {
    this.viewer.toHome();
  }

  getEntityChildrenIds(id: string) {
    return this.viewer?.getEntityChildrenIds(id);
  }

  hyalineEntities(ids: string[]) {
    this.viewer.hyalineEntities(ids);
  }

  entitySelectedEvent(func: (data: any) => void) {
    this.viewer.on(DX.Events.ENTITY_SELECTED, func);
  }

  entityDeselectedEvent(func: any) {
    this.viewer.on(DX.Events.ENTITY_DESELECTED, func);
  }

  searchEntityIdsByProps(searchList: any[]) {
    return new Promise((resolve) => {
      const promiseList: Promise<any>[] = [];
      searchList.forEach((item: any) => {
        promiseList.push(
          queryModelAttributes({
            page_num: 0,
            page_size: 100,
            path: this.renderPath,
            op: 'e',
            name: item.name,
            value: item.value,
            group_id: projectId,
          })
        );
      });
      Promise.all(promiseList).then((res) => {
        for (let i = 0; i < res.length; i++) {
          if (res[i]?.data?.count > 0) {
            const { categories } = res[i].data.pageItems[0];
            resolve(categories);
            break;
          }
        }
      });
    });
  }

  get2dViewInfo() {
    return this.viewer.getViewObjectsAsync();
  }

  // 模型装配-模型旋转
  rotateModel(data: any) {
    this.viewer.rotateModel(data.path, data.axis, data.angle);
  }

  // 设置是否可以可以使用构件选择功能
  setEnableSelection(value: boolean) {
    this.viewer.enableSelection(value);
  }

  // 清空选择构件
  clearSelectedEntities() {
    this.viewer.clearSelectedEntities();
  }

  // 监听选择构件
  modelPointPicked(func: () => void) {
    this.viewer.on(DX.Events.POINT_PICKED, func);
  }

  // 获取指定构件属性值
  async getSpecifiedComponentProperty(guids: string[]) {
    const params = {
      render_path: this.renderPath,
      guids,
      prop_group_name: 'Element_Specific',
      prop_name: 'Tag',
      group_id: projectId,
    };
    const res = await getComponentProperty(params);
    return res;
  }
}
