<template>
  <a-modal
    :visible="visible"
    :width="400"
    :title="t('knowledgenew.share')"
    title-align="start"
    :mask-closable="false"
    :unmount-on-close="true"
    :esc-to-close="false"
    class="share-dialog"
    @cancel="cancel"
  >
    <div class="base-card">
      <div class="card-top">
        <img
          v-if="data?.picUrl"
          :src="'/work/api/sys-storage/download_image?f8s=' + data.picUrl"
        />
        <DefaultCover v-else />
        <div class="text-container">
          <a-typography-paragraph
            :ellipsis="{
              rows: 1,
              showTooltip: true,
            }"
            class="text-title"
            >{{ data.name }}
          </a-typography-paragraph>
          <div class="text-desc">{{
            t('knowledgenew.created-by', { name: data.creatorName || '' })
          }}</div>
        </div>
      </div>
    </div>
    <template #footer>
      <a-button
        type="primary"
        size="large"
        long
        :loading="btnLoading"
        @click="copyLink"
        >{{ t('knowledgenew.copy-link') }}</a-button
      >
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { defineEmits, defineProps, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import useClipboard from 'vue-clipboard3';
  import { Message } from '@arco-design/web-vue';
  import { generateShareLink } from '../../api';
  import { GenerateShareLinkParams } from '../../types';
  import DefaultCover from '@/assets/images/knowledge-base/default-cover.svg';

  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  const emits = defineEmits(['update:visible']);

  const btnLoading = ref(false);

  const { toClipboard } = useClipboard();

  // 复制链接
  const copyLink = async () => {
    console.log(props.data, 79);
    const { data } = props;
    try {
      btnLoading.value = true;
      const params: GenerateShareLinkParams = {
        kbId: data.id,
      };
      const res = await generateShareLink(params);
      if (res.status) {
        const [, , kbId, visitCode] = res.data.split('/');
        const linkPath = `${window.location.origin}/work/share-knowledge?kbId=${kbId}&visitCode=${visitCode}`;
        await toClipboard(linkPath);
        Message.success(t('knowledgenew.copy-link-success'));
        emits('update:visible', false);
      }
    } catch (err) {
      console.error(err);
      Message.error(t('knowledgenew.copy-link-fail'));
    } finally {
      btnLoading.value = false;
    }
  };

  const cancel = () => {
    emits('update:visible', false);
  };
</script>

<style scoped lang="less">
  .base-card {
    padding: 16px;
    height: 92px;
    background-image: url('../../../../assets/images/knowledge-base/share-dialog-bg.png');
  }
  .card-top {
    display: flex;
    img,
    svg {
      margin-right: 12px;
      width: 60px;
      height: 60px;
      border-radius: 8px;
    }
  }

  .text-container {
    flex: 1;
    .text-title {
      margin: 7px 0 4px;
      height: 21px;
      font-weight: 500;
      font-size: 20px;
      color: #1d2129;
      line-height: 21px;
    }
    .text-desc {
      height: 21px;
      font-size: 14px;
      color: #4e5969;
      line-height: 21px;
    }
  }
</style>

<style lang="less">
  .share-dialog {
    .arco-modal-header {
      height: 52px;
    }
    .arco-modal-title {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
      line-height: 28px;
    }
    .arco-modal-body {
      padding: 20px;
    }
    .arco-btn-size-large {
      border-radius: 4px;
    }
  }
</style>
