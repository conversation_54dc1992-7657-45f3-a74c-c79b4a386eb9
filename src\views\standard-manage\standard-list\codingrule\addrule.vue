<template>
  <a-modal
    :visible="visible"
    :width="450"
    :title="title"
    :footer="!disabled"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    :unmount-on-close="true"
    title-align="start"
    @cancel="handleBeforeCancel"
    @before-ok="handleBeforeOk"
  >
    <a-form
      ref="formRef"
      :model="addRuleForm"
      :disabled="disabled"
      auto-label-width
      layout="vertical"
    >
      <a-form-item
        field="name"
        :label="$t('standard-setting.rule-name')"
        validate-trigger="input"
        :rules="[
          {
            required: true,
            message: $t('standard-setting.rule-name-required'),
          },
        ]"
      >
        <remove-spaces-input
          v-model="addRuleForm.name"
          :max-length="currentLocale === 'en-US' ? 200 : 50"
          :placeholder="$t('standard-setting.please-enter')"
        />
      </a-form-item>
      <a-form-item
        field="regularExpression"
        :label="$t('standard-setting.regular-expression')"
        validate-trigger="input"
        :rules="[
          {
            required: true,
            message: $t('standard-setting.expression-required'),
          },
        ]"
      >
        <remove-spaces-input
          v-model="addRuleForm.regularExpression"
          :placeholder="$t('standard-setting.please-enter')"
        />
      </a-form-item>
      <a-form-item
        field="description"
        :label="$t('standard-setting.instructions')"
        validate-trigger="input"
      >
        <a-textarea
          v-model="addRuleForm.description"
          :placeholder="$t('standard-setting.please-enter')"
        ></a-textarea>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { addRule, addRuleParams, editRule, AddRuleFormInter } from './api';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import { useUserStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';

  const { currentLocale } = useLocale();

  const { t } = useI18n();
  const formRef = ref<FormInstance>();
  const props = withDefaults(
    defineProps<{
      visible: boolean;
      title: string;
      disabled: boolean;
      form: AddRuleFormInter;
      actionType: string;
    }>(),
    {
      visible: false,
      title: '',
      disabled: true,
    }
  );

  const formatForm = (form: AddRuleFormInter) => {
    const { ...rest } = form;
    return {
      ...rest,
    };
  };
  const addRuleForm = ref<AddRuleFormInter>(formatForm(props.form));

  const emit = defineEmits(['update:visible', 'refresh']);

  const resetModal = ref(false);

  const handleBeforeCancel = () => {
    emit('update:visible', false);
    resetModal.value = true;
  };

  const userStore = useUserStore();
  const userId = getUserId() || '';
  const projectId = getLocalstorage(`last_project_${userId}`) || '';

  const handleBeforeOk = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      const params: addRuleParams = {
        name: addRuleForm.value.name,
        regularExpression: addRuleForm.value.regularExpression,
        description: addRuleForm.value.description,
        id: addRuleForm.value.id,
        source: 'CDE',
        groupId: userStore.admin === 0 ? '0' : projectId,
      };
      if (props.actionType === 'edit') {
        const saveRes: any = await editRule(params);
        if (saveRes.status) {
          Message.success(saveRes.message);
          handleBeforeCancel();
          emit('refresh');
        }
      } else {
        const saveRes: any = await addRule(params);
        if (saveRes.status) {
          Message.success(saveRes.message);
          handleBeforeCancel();
          emit('refresh');
        }
      }
    }
  };

  watch(
    () => props.form,
    (n) => {
      addRuleForm.value = formatForm(n);
    }
  );

  watch(
    () => resetModal.value === true,
    () => {
      addRuleForm.value.name = '';
      addRuleForm.value.regularExpression = '';
      addRuleForm.value.description = '';
      resetModal.value = false;
    }
  );
</script>

<script lang="ts">
  export default {
    name: 'Addrule',
  };
</script>

<style scoped lang="less">
  :deep(.arco-select-view),
  :deep(.arco-textarea-wrapper),
  :deep(.arco-input-wrapper),
  :deep(.arco-picker),
  :deep(.arco-input-tag),
  :deep(.arco-select-view-single),
  :deep(.arco-textarea),
  :deep(.arco-form-item-content-wrapper) {
    background-color: #fff;
    border-radius: 4px;
  }
  :deep(.arco-select-view),
  :deep(.arco-form-item-content-wrapper) {
    border: 1px solid #c9cdd4 !important;
  }
</style>
