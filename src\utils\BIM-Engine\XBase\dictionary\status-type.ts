// 模型后缀
const modelExtensions = [
  'skp',
  'obj',
  'stl',
  'glb',
  'fbx',
  'gltf',
  'stp',
  'step',
  'x_t',
  '3ds',
  'dae',
  'nwc',
  'igs',
  'cgr',
  'catpart',
  '3dxml',
  'p3d',
];

// 构建后缀
const componentExtensions = ['rfa'];

// 文件后缀
const documentExtensions = ['dwg'];

// 语义模型后缀
const semanticModelExtensions = ['ifc', 'dgn', 'rvt', 'nwd'];

const statusTypeMap = [
  {
    name: 'model',
    extensions: modelExtensions,
    statusMap: {
      '-7': '已终止',
      '-3': '上传成功',
      '-2': '转换失败',
      '0': '转换成功',
      '1': '转换中',
      '4': '排队中',
    },
  },
  {
    name: 'component',
    extensions: componentExtensions,
    statusMap: {
      '-7': '转换终止',
      '-3': '上传成功',
      '-1': '转换失败',
      '0': '转换成功',
      '1': '转换中',
      '2': '排队中',
    },
  },
  {
    name: 'document',
    extensions: documentExtensions,
    statusMap: {
      '-2': '转换终止后的失败',
      '-1': '转换失败',
      '0': '上传成功',
      '1': '排队中',
      '2': '转换中',
      '3': '转换成功',
    },
  },
  {
    name: 'semanticModel',
    extensions: semanticModelExtensions,
    statusMap: {
      '-7': '已终止',
      '-3': '待转换',
      '-2': '转换失败',
      '-1': '转换失败',
      '0': '转换成功',
      '1': '转换中',
      '4': '等待中',
    },
  },
  {
    name: 'collision', // 碰撞检查
    extensions: ['clash'],
    statusMap: {
      '0': '碰撞完成',
      '1': '碰撞检测中',
      '2': '碰撞失败',
    },
  },
];

export default statusTypeMap;
