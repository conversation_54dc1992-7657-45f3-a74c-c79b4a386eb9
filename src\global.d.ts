declare module 'sm-crypto';
declare module 'vuedraggable';
declare module 'spark-md5';
declare module '@arco-design/web-vue/lib/_components/auto-tooltip/auto-tooltip';
declare module 'chbim';

interface WwLoginParams {
  id: string;
  appid: string;
  agentid: string;
  redirect_uri: string;
  state?: string;
  href?: string;
  style?: string;
}

// 此处扩展Window类型
declare interface Window {
  WwLogin: (params: WwLoginParams) => void;
  _AMapSecurityConfig: any;
  AMapUI: any;
  DX: any;
}

declare namespace WebUploader {
  export type Uploader = any;
  export type File = any;
  export function create(value: any);
  export function Deferred();
  export const Uploader;
}

declare namespace JSX {
  interface IntrinsicElements {
    'a-sub-menu': any;
    'a-menu': any;
    'a-menu-item': any;
  }
}
interface GetPageRes<T> {
  list: T;
  total: number;
}
interface OperateRes<T> {
  list: T;
  total: number;
}
