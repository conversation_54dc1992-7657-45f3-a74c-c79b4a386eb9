<template>
  <div class="personal-base">
    <a-layout class="person-base-panel">
      <a-layout-sider
        :width="340"
        :resize-directions="['right']"
        class="file-tree-wrap"
      >
        <div class="tree-header">
          <TitleIcon style="margin-right: 8px" />
          <span class="title">{{ t('knowledgenew.personal-base') }}</span>
          <a-tooltip :content="t('knowledgenew.go-back')">
            <span>
              <BackIcon
                style="cursor: pointer"
                class="icon-hover-bg"
                @click="goHome"
              />
            </span>
          </a-tooltip>
        </div>
        <FileTree ref="fileTree" style="height: calc(100% - 60px)"></FileTree>
      </a-layout-sider>
      <a-layout-content class="content-panel">
        <AIPanel />
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import { useI18n } from 'vue-i18n';
  import { useKnowledgeBaseStore2 } from '@/store';
  import FileTree from './file-tree.vue';
  import AIPanel from './ai-panel.vue';
  import TitleIcon from '@/assets/images/knowledge-base/title-icon.svg';
  import BackIcon from '@/assets/images/knowledge-base/back-icon.svg';
  import { getBaseList } from '../../api';

  const { t } = useI18n();

  const router = useRouter();
  const route = useRoute();
  const { kbId } = route.params; // 当前知识库ID

  const knowledgeBaseStore2 = useKnowledgeBaseStore2();
  const { baseInfo } = storeToRefs(knowledgeBaseStore2);

  const fileTree = ref();

  // 返回首页
  const goHome = () => {
    router.push({
      name: 'knowledgeBase',
    });
  };

  // 查询知识库信息
  const getKnowledgeBaseInfo = async () => {
    try {
      const res = await getBaseList({
        pageParam: { pageNo: 1, pageSize: 1000 },
      });

      if (res.status) {
        res.data.list?.find((base: any) => {
          if (base.id === kbId) {
            knowledgeBaseStore2.setBaseInfo(base);
            return true;
          }
          return false;
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  onMounted(async () => {
    if (!baseInfo.value) {
      getKnowledgeBaseInfo();
    }
  });
</script>

<style scoped lang="less">
  .personal-base {
    flex: 1;
    overflow: hidden;
  }

  .tree-header {
    padding: 20px;
    height: 60px;
    display: flex;
    align-items: center;

    .title {
      flex: 1;
      height: 20px;
      font-weight: 500;
      font-size: 18px;
      color: #1d2129;
      line-height: 20px;
    }
  }

  .person-base-panel {
    border: 1px solid #d9d9d9;
    height: 100%;
    border-radius: 8px;
    .file-tree-wrap {
      border-radius: 8px;
      height: 100%;
      border-right: 1px solid #d9d9d9;
    }
    .content-panel {
      position: relative;
    }
  }

  .icon-hover-bg:hover {
    background: #e5e6eb;
    border-radius: 2px;
  }

  :deep(.arco-resizebox-trigger-icon-wrapper) {
    background-color: #eee;
  }
  :deep(.arco-layout-sider) {
    min-width: 340px;
    max-width: 600px;
  }

  :deep(.arco-btn-size-small) {
    padding: 0 8px;
    border-radius: 4px;
    color: #3366ff;
  }
</style>
