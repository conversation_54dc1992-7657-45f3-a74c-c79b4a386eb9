<template>
  <a-modal
    :visible="visible"
    width="600px"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    title-align="start"
    @cancel="cancel"
  >
    <template #title> {{ title }} </template>
    <template #footer>
      <a-button @click="cancel">{{ $t('standard-setting.cancel') }}</a-button>
      <a-button type="primary" @click="formSubmit">{{
        $t('standard-setting.save')
      }}</a-button>
    </template>
    <div class="content">
      <a-form ref="formRef" :model="form" auto-label-width layout="vertical">
        <a-form-item
          label-col-flex="100px"
          :label="$t('standard-setting.standard-chinese-name')"
          field="name"
          :rules="[
            {
              required: true,
              message: $t('standard-setting.please-enter'),
            },
          ]"
          :validate-trigger="['change', 'input']"
        >
          <remove-spaces-input
            v-model="form.name"
            :placeholder="$t('standard-setting.please-enter')"
            :max-length="50"
          />
        </a-form-item>
        <a-form-item
          label-col-flex="100px"
          :label="$t('standard-setting.standard-english-name')"
          :rules="[
            {
              required: false,
              message: $t('standard-setting.please-enter'),
            },
          ]"
          :validate-trigger="['change', 'input']"
        >
          <remove-spaces-input
            v-model="form.englishName"
            :placeholder="$t('standard-setting.please-enter')"
            :max-length="200"
          />
        </a-form-item>
        <a-form-item
          label-col-flex="100px"
          :label="$t('standard-setting.standard-number')"
          field="code"
          :rules="[
            {
              required: true,
              message: $t('standard-setting.please-enter'),
            },
          ]"
          :validate-trigger="['change', 'input']"
        >
          <remove-spaces-input
            v-model="form.code"
            :placeholder="$t('standard-setting.please-enter')"
            :max-length="currentLocale === 'en-US' ? 2010 : 50"
          />
        </a-form-item>
        <a-form-item
          label-col-flex="100px"
          field="message"
          :label="t('standard-setting.description')"
        >
          <a-textarea
            v-model="form.description"
            :placeholder="$t('standard-setting.please-enter')"
            :max-length="500"
            :auto-size="{
              minRows: 2,
            }"
          />
        </a-form-item>
        <a-form-item
          v-if="userStore.admin === 0"
          label-col-flex="100px"
          field="message"
          :label="$t('standard-setting.standard-permission')"
        >
          <a-select
            v-model="form.groupId"
            :placeholder="$t('standard-setting.please-enter')"
          >
            <a-option value="0">{{
              $t('standard-setting.system-standard')
            }}</a-option>
            <a-option v-show="admin !== 0" value="1">{{
              $t('standard-setting.project-standard')
            }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label-col-flex="100px"
          :label="$t('standard-setting.standard-type')"
          field="standardType"
          :rules="[
            {
              required: true,
              message: $t('standard-setting.please-enter'),
            },
          ]"
        >
          <a-radio-group
            v-model="form.standardType"
            @change="changeStandardType"
          >
            <a-radio
              v-for="item of standardTypeOption"
              :key="item.key"
              :value="item.key"
              >{{ item.label }}</a-radio
            >
          </a-radio-group>

          <!-- <a-checkbox-group
            v-model="form.standardType"
            direction="horizontal"
            @change="setPrecisionCheck"
          >
            <a-checkbox
              v-for="item of standardTypeOption"
              :key="item.code"
              :value="item.code"
              >{{ item.name }}</a-checkbox
            >
          </a-checkbox-group> -->
        </a-form-item>
        <a-form-item
          v-if="form.standardType === 1"
          label-col-flex="100px"
          field="accuracy"
          :rules="[
            {
              required: true,
              message: $t('please-select'),
            },
          ]"
          :label="$t('standard-setting.precision-range')"
        >
          <a-checkbox-group
            v-model="form.accuracy"
            direction="horizontal"
            @change="setPrecisionCheck"
          >
            <a-checkbox
              v-for="item of precisionResData"
              :key="item.code"
              :value="item.code"
              >{{ item.name }}</a-checkbox
            >
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { ref, reactive, computed } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import {
    standardList,
    standardParams,
    addStandard,
    standardData,
  } from '../api';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import { useUserStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';
  import useStandardManageStore from '@/store/modules/standard-manage/index';
  import { storeToRefs } from 'pinia';

  const { currentLocale } = useLocale();
  const store = useStandardManageStore();
  const admin = computed(() => userStore.admin);
  const { precisionResData } = storeToRefs(store);

  const setPrecisionCheck = () => {};
  store.setDictionaryData('stdPrecision');

  const { t } = useI18n();
  const title = ref(t('standard-setting.new'));
  const formRef = ref<FormInstance>();
  const visible = ref(false);

  const standardTypeOption = computed(() => [
    {
      key: 0,
      label: t('standard-setting.naming-standard'),
    },
    {
      key: 1,
      label: t('standard-setting.attribute-standard'),
    },
  ]);

  const alertType = ref('add');
  const form = reactive<standardData>({
    id: '',
    name: '',
    englishName: '',
    code: '',
    description: '',
    existStandardId: '',
    groupId: '0',
    attributeLink: '-',
    standardType: 0,
    xbaseStandardId: '',
    accuracy: '',
  });

  // 新增/编辑
  const alertShow = async (data: any, type: string) => {
    alertType.value = type;
    if (type === 'edit') {
      // 请求已有标准数据
      form.id = data.id || '';
      form.name = data.name || '';
      form.englishName = data.englishName || '';
      form.code = data.code || '';
      form.description = data.description || '';
      form.existStandardId = data.existStandardId || '';
      form.attributeLink = data.attributeLink || '-';
      form.standardType = data.standardType;
      form.xbaseStandardId = data.xbaseStandardId;
      form.accuracy =
        data.accuracy?.split(',').length > 0 ? data.accuracy?.split(',') : [];
      form.groupId = data.groupId === '0' ? '0' : '1';
      form.standardType = data.standardType || 0;

      title.value = t('standard-setting.edit');
    } else {
      form.id = '';
      form.name = '';
      form.englishName = '';
      form.code = '';
      form.description = '';
      form.existStandardId = '';
      form.attributeLink = '-';
      form.standardType = 0;
      form.xbaseStandardId = '';
      title.value = t('standard-setting.new');
    }
    visible.value = true;
  };
  defineExpose({
    alertShow,
  });

  const cancel = () => {
    formRef.value?.resetFields();
    visible.value = false;
  };

  const emits = defineEmits(['refresh']);

  const userStore = useUserStore();
  const userId = getUserId() || '';
  const projectId = getLocalstorage(`last_project_${userId}`) || '';

  // 修改类型
  const changeStandardType = () => {
    form.accuracy = null;
  };

  const formSubmit = async () => {
    try {
      const validationResponse = await formRef.value?.validate();
      console.log(validationResponse, 484848);
      const groupId = form.groupId === '0' ? '0' : projectId;
      if (form.standardType === 1 && !form.accuracy.length) {
        Message.info(t('standard-setting.select-accuracy'));
        return;
      }
      if (!validationResponse) {
        const params = {
          code: form.code,
          name: form.name,
          id: form.id,
          englishName: form.englishName,
          description: form.description,
          existStandardId: form.existStandardId,
          attributeLink: form.attributeLink,
          groupId,
          source: 'CDE',
          standardType: form.standardType,
          xbaseStandardId: form.xbaseStandardId,
          accuracy: form.standardType === 1 ? form.accuracy?.join(',') : '',
        };
        const saveResponse = await addStandard(params);

        if (saveResponse.status) {
          Message.success(t('standard-setting.success'));
          emits('refresh');
          cancel();
        }
      }
    } catch (error: any) {
      Message.error(error.message || error.msg);
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'CreateStandard',
    inheritAttrs: false,
  };
</script>

<style scoped lang="less">
  .arco-modal {
    width: 860px;
  }

  .title {
    position: relative;
    margin-bottom: 20px;

    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }

    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }

    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .file-list-wrap {
    margin-top: 16px;
  }
  :deep(.arco-textarea-wrapper),
  :deep(.arco-input-wrapper),
  :deep(.arco-picker),
  :deep(.arco-input-tag),
  :deep(.arco-select-view-single),
  :deep(.arco-textarea),
  :deep(.arco-form-item-content-wrapper) {
    background-color: #fff;
    border-radius: 4px;
  }
  :deep(.arco-textarea-wrapper),
  :deep(.arco-select),
  :deep(.arco-input-wrapper) {
    border: 1px solid #c9cdd4 !important;
  }
</style>
