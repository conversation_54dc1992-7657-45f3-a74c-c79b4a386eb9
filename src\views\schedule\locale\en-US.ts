export default {
  'schedule.calendar': 'Calendar',
  'schedule.gantt': 'Gantt',
  'schedule.matters': 'Matters',
  'schedule.meeting': 'Meeting',
  'schedule.new': 'New',
  'schedule.edit': 'Edit',
  'schedule.schedule': 'Schedule',
  'schedule.container': 'Schedule Container',
  'schedule.search': 'Search',
  'schedule.all': 'All',
  'schedule.createdByMe': 'Created by Me',
  'schedule.responsibleByMe': 'Responsible by Me',
  'schedule.notStarted': 'Not Started',
  'schedule.inProgress': 'In Progress',
  'schedule.completed': 'Completed',
  'schedule.closed': 'Closed',
  'schedule.noData': 'No Data',
  'schedule.delete': 'Delete',
  'schedule.delete.confirm': 'Are you sure you want to delete this item?',
  'schedule.delete.current': 'Current Schedule',
  'schedule.delete.currentAndAfter': 'Current and Following Schedules',
  'schedule.delete.all': 'All Schedules',
  'schedule.delete.success': 'Deleted Successfully',
  'schedule.delete.selectType': 'Please select repeat type',
  'schedule.status.success': 'Status set successfully',
  'schedule.status.notCreator':
    'You are not the creator, please contact the creator to modify the status',
  'schedule.status.notMeetingCreator':
    'You are not the meeting creator, please contact the creator to delete',
  'schedule.status.notMatterCreator':
    'You are not the matter creator, please contact the creator to delete',
  'schedule.status.completeSubMatters': 'Please complete all sub-items first',
  'schedule.focusMatters': 'Focus Matters',
  'schedule.noFocusMatters': 'No Focus Matters',
  'schedule.deadline': 'Deadline',
  'schedule.addCalendar': 'Add Calendar',
  'schedule.myCalendar': 'My Calendar',
  'schedule.projectCalendar': 'Project Calendar',
  'schedule.deleteCalendar.confirm':
    'Are you sure you want to delete this calendar?',
  'schedule.createMatter': 'Create Matter',
  'schedule.createMeeting': 'Create Meeting',
  'schedule.month': 'Month',
  'schedule.week': 'Week',
  'schedule.day': 'Day',
  'schedule.year': 'Year',
  'schedule.current': 'Current',
  'schedule.weekday.sunday': 'Sunday',
  'schedule.weekday.monday': 'Monday',
  'schedule.weekday.tuesday': 'Tuesday',
  'schedule.weekday.wednesday': 'Wednesday',
  'schedule.weekday.thursday': 'Thursday',
  'schedule.weekday.friday': 'Friday',
  'schedule.weekday.saturday': 'Saturday',
  'schedule.theFirstWeek': 'First Week',
  'schedule.inputTitle': 'Please enter a title',
  'schedule.basicInfo': 'Basic Information',
  'schedule.assignedTo': 'Assigned To',
  'schedule.selectAssignee': 'Please select an assignee',
  'schedule.matter.deadline': 'Deadline',
  'schedule.selectDeadline': 'Please select a deadline',
  'schedule.calendarBelong': 'Calendar Belonging',
  'schedule.selectCalendar': 'Please select a calendar',
  'schedule.description': 'Description',
  'schedule.placeholder': 'Please enter',
  'schedule.subTaskList': 'Sub-task List',
  'schedule.aiSubTaskList': 'AI-generated Sub-task List',
  'schedule.add': 'Add',
  'schedule.selectPerson': 'Please select person',
  'schedule.attachment': 'Attachment',
  'schedule.uploadAttachment': 'Upload Attachment',
  'schedule.comment': 'Comment',
  'schedule.noComment': 'No Comment',
  'schedule.placeholder.comment':
    'Please enter a comment and press Enter to send',
  'schedule.delete.comment': 'Are you sure you want to delete this comment?',
  'schedule.matter.createMatter': 'Create Matter',
  'schedule.matter.saveChanges': 'Save Changes',
  'schedule.meeting.startTime': 'Meeting Start Time',
  'schedule.meeting.endTime': 'Meeting End Time',
  'schedule.meeting.startTime.placeholder': 'Please select a start time',
  'schedule.meeting.endTime.placeholder': 'Please select an end time',
  'schedule.meeting.organizer': 'Organizer',
  'schedule.meeting.organizer.placeholder': 'Please select an organizer',
  'schedule.meeting.participant': 'Participant',
  'schedule.meeting.participant.placeholder': 'Please select participant',
  'schedule.meeting.team': 'Belonging Team',
  'schedule.meeting.team.placeholder': 'Please select a belonging team',
  'schedule.meeting.type': 'Meeting Type',
  'schedule.meeting.type.placeholder': 'Please select a meeting type',
  'schedule.meeting.location': 'Meeting Location',
  'schedule.meeting.location.placeholder': 'Please select a meeting location',
  'schedule.meeting.repeat': 'Repeat Type',
  'schedule.meeting.repeat.placeholder': 'Please select a repeat type',
  'schedule.meeting.endAt': 'End At',
  'schedule.meeting.endAt.placeholder': 'Please select an end time',
  'schedule.meeting.calendar': 'Belonging Calendar',
  'schedule.meeting.calendar.placeholder': 'Please select a belonging calendar',
  'schedule.meeting.download': 'Download',
  'schedule.meeting.preview': 'Preview',
  'schedule.meeting.ai': 'AI Empowerment',
  'schedule.calendar.add': 'Add New Schedule Panel',
  'schedule.calendar.name': 'Calendar Panel Name',
  'schedule.calendar.name.placeholder': 'Please enter a calendar panel name',
  'schedule.calendar.member':
    'Sync calendar with members ( After adding, members can participate in the creation and editing of schedules )',
  'schedule.calendar.member.placeholder': 'Please select members',
  'schedule.focus': 'Focus',
  'schedule.cancelFocus': 'Unfocus',
  'schedule.calendar.edit': 'Edit Successful',
  'schedule.calendar.create': 'Create Successful',
  'schedule.calendar.status.placeholder': 'Please select a status',
  'schedule.comment.success': 'Comment successful',
  'schedule.comment.placeholder': 'Please enter a comment',
  'schedule.deadline.error': 'Deadline cannot be earlier than the current time',
  'schedule.deadline.create.error':
    'Deadline cannot be earlier than the creation time',
  'schedule.subTaskList.placeholder': 'Please enter sub-task list',
  'schedule.meeting.repeat.type.none': 'No Repeat',
  'schedule.meeting.repeat.type.daily': 'Daily Repeat',
  'schedule.meeting.repeat.type.weekday': 'Weekday Repeat',
  'schedule.meeting.repeat.type.weekly': 'Weekly Repeat',
  'schedule.meeting.repeat.type.biweekly': 'Biweekly Repeat',
  'schedule.meeting.type.online': 'Online',
  'schedule.meeting.type.offline': 'Offline',
  'schedule.meeting.ai.generate': 'Generate Meeting Plan',
  'schedule.meeting.ai.upload': 'Upload Meeting Plan',
  'schedule.meeting.ai.summary': 'Generate Meeting Summary',
  'schedule.meeting.ai.upload.summary': 'Upload Meeting Summary',
  'schedule.meeting.ai.todo': 'Generate To-Do',
  'schedule.meeting.add.todo': 'Add To-Do',
  'schedule.meeting.add.todo.placeholder': 'Please enter a to-do item',
  'schedule.meeting.edit.repeat': 'Modify Recurring Meeting',
  'schedule.meeting.edit.current': 'Current Meeting',
  'schedule.meeting.edit.currentAndAfter': 'Current Meeting and After',
  'schedule.meeting.ai.generate.success': 'Meeting plan generated successfully',
  'schedule.meeting.ai.generate.error': 'Meeting plan generation failed',
  'schedule.meeting.placeholder': 'Please enter a to-do item',
  'schedule.meeting.time.placeholder':
    'End time cannot be earlier than start time',
  'schedule.meeting.ai.todo.error': 'Failed to parse, unable to generate to-do',
  'schedule.meeting.delete': 'Only the uploader can delete',
  'schedule.meeting.ai.summary.success':
    'Meeting summary generated successfully',
  'schedule.meeting.ai.summary.error': 'Meeting summary generation failed',
  'schedule.calendar.team': 'Belonging Team',
  'schedule.status.notMatterStatusPermission':
    'You do not have permission to modify the status of this item',
  'schedule.delete.todo': 'Are you sure you want to delete this to-do?',
  'schedule.delete.meeting': 'Are you sure you want to delete this meeting?',
  'schedule.delete.plan': 'Are you sure you want to delete this meeting plan?',
  'schedule.delete.attachment':
    'Are you sure you want to delete this meeting attachment?',
  'schedule.file.tips': 'Please save to view',
  'calendar.monthTitle': '{month}/{year}',
  'calendar.weekTitle': 'Week {week}, {month}/{year}',
  'calendar.dayTitle': '{month}/{day}/{year} {weekDay}',
  'calendar.weekday.sunday': 'Sun',
  'calendar.weekday.monday': 'Mon',
  'calendar.weekday.tuesday': 'Tue',
  'calendar.weekday.wednesday': 'Wed',
  'calendar.weekday.thursday': 'Thu',
  'calendar.weekday.friday': 'Fri',
  'calendar.weekday.saturday': 'Sat',
  'calendar.dayTitleSimple': '{month}/{day}/{year}',

  'calendar.push-to-Jiaojiantong': 'Whether to push to Jiaojiantong',
  'calendar.searchProject': 'Search',
  'calendar.tips': 'Check to display all your schedules within this project',
  'calendar.head-to-main-task': 'To master',
  'calendar.go-to-subevent': 'Go to subevent',
  'calendar.go-to-meeting': 'Go to meeting',
  'calendar.go-to': 'Go to',
  'calendar.save-as': 'Save as',
  'calendar.save-modifications':
    'The edited content on the current page has not been saved. May I ask if you want to save the modifications?',
  'calendar.go-to-meeting-detail': 'Go to meeting details',
  'schedule.project-plan': 'Project Plan',
};
