<!-- filepath: src/components/BaseModal.vue -->
<template>
  <a-modal
    v-model:visible="referenceModalVisible"
    :title="t('cloud.select-file')"
    width="1000px"
    height="800px"
    class="reference-dialog"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <ProjectCtl ref="projectCtlRef"></ProjectCtl>
    <div style="margin-top: 20px; max-height: 800px">
      <FilePanel></FilePanel>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { ref, defineProps, defineEmits, watch, toRefs } from 'vue';
  import i18n from '@/locale/index';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import ProjectCtl from '@/views/knowledge-base-new/components/projectCtl.vue';
  import FilePanel from '@/views/knowledge-base-new/components/file-panel.vue';
  import { relateFolder } from './api';

  const { t } = i18n.global;
  const knowledgeStore = useKnowledgeBaseNewStore();
  const { project, referenceModal } = storeToRefs(knowledgeStore);
  const {
    referenceModalVisible,
    tableData,
    selectedTableRowkeys,
    currentProjectId,
    currentFolder,
  } = toRefs(referenceModal.value);

  // 清空子组件的搜索条件
  const projectCtlRef = ref();
  // 写一个方法已经知道选中的selectedTableRowkeys集合，和tableData
  // 从里面筛选出两个数组，一个是文件id组成的数据，一个是文件夹id组成的数组
  // 通过有folderId是文件，没有是文件夹，然后区分出两个数组来
  function getSourceFileIdList() {
    const fileIdList: string[] = [];
    const folderIdList: string[] = [];

    tableData.value.forEach((item: any) => {
      if (selectedTableRowkeys.value.includes(item.id)) {
        if (item.folderId) {
          // 有 folderId 是文件
          fileIdList.push(item.id);
        } else {
          // 没有 folderId 是文件夹
          folderIdList.push(item.id);
        }
      }
    });

    return {
      fileIdList,
      folderIdList,
    };
  }
  const handleOk = async () => {
    const { fileIdList, folderIdList } = getSourceFileIdList();
    const params = {
      sourceFileIdList: fileIdList,
      sourceFolderIdList: folderIdList,
      sourceProjectId: currentProjectId.value,
      sourceTeamId: currentFolder.value.teamId,
    };
    console.log('referenceModal', currentFolder.value);
    const res = await relateFolder(params);
    if (res.status) {
      knowledgeStore.getProjectFiles();
    }
    knowledgeStore.setSelectedTableRowkeys([]);
    knowledgeStore.setReferenceModalVisible(false);
    knowledgeStore.setReferencereferenceModal();
  };

  function handleCancel() {
    knowledgeStore.setReferenceModalVisible(false);
    knowledgeStore.setReferencereferenceModal();
    knowledgeStore.setSelectedTableRowkeys([]);
    // 调用子组件的方法来清空搜索字段
    projectCtlRef.value?.clearSearch?.();
  }
</script>

<style scoped lang="less">
  .reference-modal {
    height: 800px;
  }
</style>
