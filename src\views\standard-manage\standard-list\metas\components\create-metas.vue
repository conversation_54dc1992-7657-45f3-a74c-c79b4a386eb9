<template>
  <a-modal
    :visible="visible"
    :ok-text="$t('standard-setting.ok')"
    :title="title"
    :on-before-ok="metasSubmit"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    :width="600"
    title-align="start"
    @cancel="cancel"
  >
    <div class="content">
      <a-form
        ref="metasFormRef"
        :model="metasForm"
        auto-label-width
        :disabled="openType === 'view'"
        layout="vertical"
      >
        <a-row :gutter="[20, 0]">
          <a-col :span="12">
            <a-form-item
              field="name"
              :label="$t('standard-setting.name')"
              :rules="[
                {
                  required: true,
                  message: $t('standard-setting.please-enter'),
                },
              ]"
              :validate-trigger="['change', 'input']"
            >
              <a-input
                v-model="metasForm.name"
                :placeholder="$t('standard-setting.please-enter')"
                :max-length="currentLocale === 'en-US' ? 200 : 50"
                show-word-limit
                @input="
                  {
                    metasForm.name = metasForm.name.replace(
                      /[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g,
                      ''
                    );
                  }
                "
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item field="type" :label="$t('standard-setting.type')">
              <a-select
                v-model="metasForm.type"
                :placeholder="$t('standard-setting.please-select')"
                @change="typeChange"
              >
                <a-option :value="0">{{
                  $t('standard-setting.date')
                }}</a-option>
                <a-option :value="1">{{
                  $t('standard-setting.text-field')
                }}</a-option>
                <a-option :value="2">{{
                  $t('standard-setting.dropdown-list')
                }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item
          field="description"
          :label="$t('standard-setting.description')"
        >
          <a-textarea
            v-model="metasForm.description"
            :placeholder="$t('standard-setting.please-enter')"
            allow-clear
            :max-length="255"
            show-word-limit
            :auto-size="{
              minRows: 2,
            }"
          />
        </a-form-item>
        <div v-if="metasForm.type !== 0">
          <a-row>
            <img
              :src="listIcon"
              alt=""
              style="width: 17px; height: 17px; margin-right: 5px"
            />
            <span class="subhead">{{
              metasForm.type == 2
                ? $t('standard-setting.property-value-setting')
                : metasForm.type == 1
                ? $t('standard-setting.rule-validation')
                : ''
            }}</span>
            <div
              v-if="openType !== 'view'"
              style="text-align: right; width: 458px"
            >
              <icon-plus
                v-if="metasForm.type === 2"
                style="cursor: pointer; color: rgb(51, 112, 255)"
                @click="iconClick"
              />
            </div>
          </a-row>
          <a-tree
            v-if="metasForm.type === 2"
            ref="dimensionTreeRef"
            :field-names="{ key: 'fieldKey' }"
            :data="treeData"
            block-node
          >
            <template #title="nodeData">
              <a-input-group>
                <a-input
                  v-model="nodeData.code"
                  :disabled="openType === 'view'"
                  :placeholder="
                    $t('standard-setting.please-enter-field') + 'code'
                  "
                  allow-clear
                  @input="
                    {
                      nodeData.code = nodeData.code.replace(/[^\w]/g, '');
                    }
                  "
                />
                <a-input
                  v-model="nodeData.name"
                  :disabled="openType === 'view'"
                  allow-clear
                  :placeholder="$t('standard-setting.please-enter-field-value')"
                  @input="
                    {
                      nodeData.name = nodeData.name.replace(
                        /[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g,
                        ''
                      );
                    }
                  "
                />
              </a-input-group>
            </template>
            <template #extra="nodeData">
              <a-space v-if="openType !== 'view'">
                <IconPlus
                  style="color: #3370ff"
                  @click="() => onAddClick(nodeData)"
                />
                <IconDelete
                  style="color: red"
                  @click="() => onDeleteClick(nodeData)"
                ></IconDelete>
              </a-space>
            </template>
          </a-tree>
          <a-row style="margin-top: 10px">
            <a-col v-if="metasForm.type == 1" :span="24">
              <a-form-item
                field="dimension"
                :label="$t('standard-setting.rule-name')"
              >
                <a-select
                  v-model="metasForm.ruleList"
                  :placeholder="$t('standard-setting.please-select')"
                  :options="allOptions.ruleOptions"
                  multiple
                >
                </a-select>
              </a-form-item>
            </a-col>
            <!-- <a-col v-if="metasForm.type == 2" :span="12">
              <a-form-item field="dimension" label="选择维表">
                <a-select
                  v-model="metasForm.dimensionId"
                  :options="allOptions.dimensionOptions"
                  placeholder="请选择维表"
                >
                </a-select>
              </a-form-item>
            </a-col> -->
          </a-row>
        </div>
        <div v-if="openType === 'add'">
          <img
            :src="listIcon"
            alt=""
            style="width: 17px; height: 17px; margin-right: 5px"
          />
          <span class="subhead">{{
            $t('standard-setting.naming-standards-set')
          }}</span>
          <a-row style="margin-top: 10px">
            <a-col :span="12">
              <a-checkbox
                v-model="metasForm.isAddStandard"
                style="margin-bottom: 10px; margin-left: -5px"
                >{{ $t('standard-setting.add-to-naming-standard') }}</a-checkbox
              >
            </a-col>
            <!-- <a-col v-show="metasForm.isAddStandard" :span="12">
              <a-checkbox
                v-model="metasForm.emptyStatus"
                style="margin-bottom: 10px; margin-left: -5px"
                >是否允许为空</a-checkbox
              >
            </a-col> -->
            <a-col v-show="metasForm.isAddStandard" :span="24">
              <a-select
                v-model="metasForm.standardId"
                :placeholder="$t('standard-setting.please-select')"
                :options="allOptions.standardOptions"
              >
              </a-select>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { watch, ref, reactive } from 'vue';
  import listIcon from '@/assets/images/standard/list.png';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import {
    addMetas,
    updateMetas,
    addDimensions,
    updateDimensions,
    queryDimensionsInfo,
  } from '../api';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import { useUserStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';

  const { currentLocale } = useLocale();

  const { t } = useI18n();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    openType: {
      type: String,
      default: '',
    },
    currentData: {
      type: Object,
      default() {
        return {};
      },
    },
    allOptions: {
      type: Object,
      default() {
        return {};
      },
    },
  });

  interface metasFormInter {
    id: string;
    name: string;
    type: number;
    description: string;
    dimensionId: string;
    ruleList: Array<string>;
    isAddStandard: boolean;
    // emptyStatus: boolean;
    standardId: string;
  }
  const metasForm = reactive<metasFormInter>({
    id: '',
    name: '',
    type: 0,
    description: '',
    dimensionId: '',
    ruleList: [],
    isAddStandard: true,
    // emptyStatus: false,
    standardId: '',
  });
  const metasFormRef = ref<FormInstance>();
  const emits = defineEmits(['update:visible', 'refresh']);
  const title = ref<string>('');

  const clearForm = () => {
    metasForm.id = '';
    metasForm.description = '';
    metasForm.dimensionId = '';
    metasForm.isAddStandard = true;
    // metasForm.emptyStatus = false;
    metasForm.name = '';
    metasForm.ruleList = [];
    metasForm.standardId = '';
    metasForm.type = 0;
  };
  const cancel = () => {
    metasFormRef.value?.resetFields();
    clearForm();
    emits('update:visible', false);
  };
  const typeChange = () => {
    metasForm.dimensionId = '';
    metasForm.ruleList = [];
  };

  const userStore = useUserStore();
  const userId = getUserId() || '';
  const projectId = getLocalstorage(`last_project_${userId}`) || '';

  const createDimensions = async () => {
    const params = {
      id: metasForm.dimensionId,
      groupId: userStore.admin === 0 ? '0' : projectId,
      name: metasForm.name,
      description: metasForm.description,
      fields: treeData.value,
    };
    let result;
    // 编辑时候从别的类型编辑到下拉列表的时候 dimensionId没有数据 这种情况下也需要创建维表
    if (props.openType === 'add' || !metasForm.dimensionId) {
      const saveRes: any = await addDimensions(params);
      // if (!saveRes?.status) {
      //   Message.error(saveRes?.message);
      // }
      result = saveRes.data;
    } else {
      await updateDimensions(params);
      result = metasForm.dimensionId;
    }
    return result;
  };
  const metasSubmit = async (done: any) => {
    if (props.openType === 'view') {
      cancel();
      return;
    }
    const res = await metasFormRef.value?.validate();
    if (!res) {
      // 优先创建/修改维表
      let dimensionId;
      if (metasForm.type === 2) {
        dimensionId = await createDimensions();
        if (!dimensionId) return;
      }
      // 创建/修改属性
      const params: object = {
        id: metasForm.id,
        // groupId: projectId.value,
        groupId: userStore.admin === 0 ? '0' : projectId,
        name: metasForm.name,
        type: metasForm.type,
        description: metasForm.description,
        dimensionId,
        ruleList: metasForm.ruleList,
      };
      let saveRes: any;
      if (props.openType === 'add') {
        if (metasForm.isAddStandard) {
          const addParams = {
            ...params,
            isAddStandard: metasForm.isAddStandard,
            // emptyStatus: metasForm.emptyStatus ? 1 : 0,
            standardId: metasForm.standardId,
          };
          saveRes = await addMetas(addParams);
        } else {
          saveRes = await addMetas(params);
        }
      } else if (props.openType === 'edit') {
        saveRes = await updateMetas(params);
      }
      if (saveRes.status) {
        Message.success(saveRes.message);
        emits('refresh');
        cancel();
      } else {
        // Message.error(saveRes.message);
      }
      done();
    }
  };

  let i = 1;
  const setFieldKey = (fieldList: any[]) => {
    fieldList.forEach((item) => {
      item.fieldKey = String(i++);
      if (item.children && item.children.length > 0) {
        setFieldKey(item.children);
      }
    });
  };

  const getDimensions = async (dimensionId: string) => {
    treeData.value = [];
    const res: any = await queryDimensionsInfo(dimensionId);
    if (res.status) {
      setFieldKey(res.data.fields);
      treeData.value = res.data.fields;
    }
  };

  watch(
    () => props.visible,
    (val) => {
      if (val) {
        if (props.openType === 'add') {
          title.value = t('standard-setting.new');
          treeData.value = [];
        } else {
          metasForm.id = props.currentData?.id;
          metasForm.name = props.currentData?.name;
          metasForm.type = props.currentData?.type;
          metasForm.description = props.currentData?.description;
          metasForm.isAddStandard = props.currentData?.isAddStandard;
          metasForm.dimensionId = props.currentData?.dimensionId;
          metasForm.ruleList = props.currentData?.ruleList.map((item: any) => {
            return item.id;
          });
          // metasForm.emptyStatus = props.currentData?.emptyStatus;
          metasForm.standardId = props.currentData?.standardId;
          title.value =
            props.openType === 'edit'
              ? t('standard-setting.edit')
              : t('standard-setting.view');
          if (props.currentData?.type === 2) {
            getDimensions(props.currentData?.dimensionId);
          }
        }
      }
    },
    { deep: true, immediate: true }
  );

  interface TreeData {
    fieldKey: string;
    code: string;
    name: string;
    children?: TreeData[];
  }
  const dimensionTreeRef = ref();
  const treeData = ref<TreeData[]>([]);

  const onAddClick = (nodeData: TreeData) => {
    dimensionTreeRef.value.expandNode(nodeData.fieldKey, true);
    const children = nodeData.children || [];
    children.push({
      name: '',
      code: '',
      fieldKey: String(i++),
    });
    nodeData.children = children;
    treeData.value = [...treeData.value];
  };

  const iconClick = () => {
    treeData.value.push({
      name: '',
      code: '',
      fieldKey: String(i++),
    });
  };

  const filterDel = (arr: TreeData[], fieldKey: string) => {
    arr.forEach((item, index) => {
      if (item.fieldKey === fieldKey) {
        arr.splice(index, 1);
      }
      if (item.children) {
        filterDel(item.children, fieldKey);
      }
    });
    return arr;
  };

  const onDeleteClick = (nodeData: TreeData) => {
    filterDel(treeData.value, nodeData.fieldKey);
  };
</script>

<script lang="ts">
  export default {
    name: 'CreateMetas',
    inheritAttrs: false,
  };
</script>

<style scoped lang="less">
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .file-list-wrap {
    margin-top: 16px;
  }
  .subhead {
    position: relative;
    top: -3px;
    font-size: 16px;
  }

  :deep .arco-tree-node-title:hover {
    background-color: white;
  }

  :deep(.arco-select-view),
  :deep(.arco-textarea-wrapper),
  :deep(.arco-input-wrapper),
  :deep(.arco-picker),
  :deep(.arco-input-tag),
  :deep(.arco-select-view-single),
  :deep(.arco-textarea),
  :deep(.arco-form-item-content-wrapper) {
    background-color: #fff;
    border-radius: 4px;
  }
  // :deep(.arco-form-item-content-wrapper),
  :deep(.arco-select-view),
  :deep(.arco-input-wrapper),
  :deep(.arco-select-view-single),
  :deep(.arco-textarea-wrapper),
  :deep(.arco-input-group .arco-input-wrapper) {
    border: 1px solid #c9cdd4 !important;
  }
</style>
