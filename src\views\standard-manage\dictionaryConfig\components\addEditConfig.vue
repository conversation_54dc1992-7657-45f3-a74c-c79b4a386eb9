<template>
  <div>
    <a-modal
      :visible="visible"
      :title="title"
      title-align="start"
      :unmount-on-close="true"
      :mask-closable="false"
      width="600px"
      @cancel="handleBeforeCancel"
      @ok="handleBeforeOk"
    >
      <a-spin style="width: 100%" :loading="loading">
        <a-form ref="formRef" :model="form" auto-label-width layout="vertical">
          <a-form-item
            field="name"
            :label="$t('dictionary-config.name')"
            validate-trigger="input"
            :rules="[
              {
                required: true,
                message: $t('dictionary-config.please-enter'),
              },
            ]"
          >
            <a-input
              v-model="form.name"
              :max-length="100"
              allow-clear
              show-word-limit
              :placeholder="$t('dictionary-config.please-enter')"
            />
          </a-form-item>

          <a-form-item :label="$t('dictionary-config.sort')">
            <a-input-number
              v-model="form.sortCode"
              :precision="0"
              :min="0"
              :max="999"
              :placeholder="
                $t('dictionary-config.please-enter-positive-integer')
              "
            />
          </a-form-item>

          <a-form-item
            :label="$t('dictionary-config.note')"
            validate-trigger="input"
          >
            <a-textarea
              v-model="form.description"
              :max-length="100"
              allow-clear
              show-word-limit
              :auto-size="{
                minRows: 3,
                maxRows: 3,
              }"
              :placeholder="$t('dictionary-config.please-enter')"
            ></a-textarea>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { onMounted, ref } from 'vue';
  import { addKeyValue, updateKeyValue } from '../api';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const title = ref(''); // 标题
  const loading: any = ref(false);

  const props: any = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      required: false,
    },
    type: {
      type: String,
      required: false,
    },
    configId: {
      type: String,
      required: false,
    },
  });

  const form = ref<any>({
    name: '',
  });

  const emit = defineEmits(['update:visible', 'refresh']);

  const handleBeforeCancel = () => {
    emit('update:visible', false);
  };

  const formRef = ref<any>(null);

  const handleBeforeOk = async () => {
    // if (form.value.sortCode > 999) {
    //   Message.info(t('dictionary-config.sorting-not-exceed-999'));
    //   return;
    // }
    const res = await formRef.value?.validate();
    if (!res) {
      loading.value = true;
      let params: any;
      try {
        // 新增
        if (props.type === 'add') {
          params = [
            {
              parentId: props.configId,
              content: form.value.name,
              sort: Number(form.value.sortCode) || 0,
              ext1: form.value.description || '',
              langCode: 'zh-CN',
            },
          ];
        } else {
          // 编辑
          params = [
            {
              parentId: props.configId,
              content: form.value.name,
              sort: Number(form.value.sortCode) || 0,
              ext1: form.value.description || '',
              id: form.value.id,
              langCode: 'zh-CN',
            },
          ];
        }
        const result: any =
          props.type === 'add'
            ? await addKeyValue(params)
            : await updateKeyValue(params);
        if (result.status) {
          Message.success(t('dictionary-config.success'));
          emit('refresh');
        }
      } catch (err) {
        console.log(err);
      } finally {
        loading.value = false;
      }
    }
  };

  onMounted(() => {
    console.log('[ props.data ] >', props.data);
    if (props.type === 'add') {
      title.value = t('dictionary-config.add');
      form.value = {};
    } else {
      title.value = t('dictionary-config.edit');
      form.value.name = props.data.contentMap?.['zh-CN'] || '';
      form.value.sortCode = props.data?.sort;
      form.value.description = props.data.ext1 || '';
      form.value.id = props.data.id;
    }
  });
</script>

<style lang="less" scoped>
  :deep(.arco-textarea-wrapper),
  :deep(.arco-input-wrapper),
  :deep(.arco-textarea),
  :deep(.arco-form-item-content-wrapper) {
    background-color: #fff;
    border-radius: 4px;
  }

  :deep(.arco-textarea-wrapper),
  :deep(.arco-input-wrapper) {
    border: 1px solid #c9cdd4 !important;
  }
</style>
