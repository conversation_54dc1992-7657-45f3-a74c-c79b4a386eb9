<!-- 选人组件 -->
<template>
  <a-select
    v-bind="$attrs"
    v-model="user"
    :options="options"
    :style="{ width }"
    :loading="loading"
    :multiple="multiple"
    :filter-option="false"
    :show-extra-options="showExtraOptions"
    allow-clear
    allow-search
    @search="handleSearch"
    @change="change"
    @clear="searchUsers('')"
    @focus="searchUsers('')"
  />
</template>

<script lang="ts" setup>
  import useLoading from '@/hooks/loading';
  import { ref, watch, computed, onMounted } from 'vue';
  import { getUserList, UserParams, searchUser } from '@/api/user';
  import { UserState } from '@/store/modules/user/types';
  import { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
  import { isArray } from '@/utils/is';
  import useConferenceStore from '@/store/modules/conference/index';

  const { loading, setLoading } = useLoading(false);
  const store = useConferenceStore();

  const props = defineProps({
    width: {
      type: String,
      default: '100%',
    },
    modelValue: {
      type: String,
      default: '',
    },
    usingName: {
      type: Boolean,
      default: false,
    },
    projectId: {
      type: String,
      default: undefined,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    showUserName: {
      type: Boolean,
      default: false,
    },
    showExtraOptions: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['update:modelValue', 'change']);

  const options = ref<SelectOptionData[]>([]);

  let originData: UserState[] = [];

  // 后续v-model的值改为对象
  const user = ref<string | string[]>(props.modelValue);
  const initDefaultValue = (v: string, flag: boolean) => {
    if (props.multiple) {
      user.value = v ? v.split(',') : [];
    } else {
      user.value = v;
    }
    const paras = isArray(user.value) ? user.value.join(',') : user.value;
    if (paras && flag) {
      const value = props.usingName ? { names: paras } : { ids: paras };
      getUserInfo(value);
    }
  };
  const getUserInfo = async (value: UserParams) => {
    try {
      const { data } = await getUserList(value);
      originData = data || [];
      // select组件存在bug：show-extra-options即使设置为true时，用于初始化展示的option再重新搜索时会变回只有value没有label的情况，因为label是接口异步获得的
      // 改为自己维护额外的options
      if (store.conferenceEditFlag === true) {
        store.conferenceEditFlag = false;
      } else {
        options.value = handlerOriginData(originData);
      }
      emit('change', getOthersInfo(user.value));
    } catch (error) {
      // handle error
    }
  };
  const selectedOptions = computed(() => {
    return options.value.filter((item) =>
      user.value.includes(item.value as string)
    );
  });
  let initFlag = true;
  watch(
    () => props.modelValue,
    (n) => {
      initDefaultValue(n, initFlag);
    },
    { immediate: true }
  );

  // fetch org data
  const searchUsers = async (searchVal: string) => {
    setLoading(true);
    try {
      const { data } = props.projectId
        ? await searchUser(searchVal, props.projectId)
        : await searchUser(searchVal);
      originData = data || [];
      options.value = handlerOriginData(originData);
    } catch (err) {
      // handle error
    } finally {
      setLoading(false);
    }
  };
  searchUsers('');

  const handlerOriginData = (data: UserState[]) => {
    const result = data.map((item) => {
      const val: SelectOptionData = {};
      const { name, userId, username, id } = item;
      // 接口兼容
      const idValue = id || userId;
      val.value = props.usingName ? username : idValue;
      val.label = props.showUserName ? `${username}(${name})` : name;
      val.data = item;
      return val;
    });
    // 自己维护额外的options,只有多选时需要加上
    const extraOptions = selectedOptions.value.filter((item) => {
      return !result.some((it) => it.value === item.value);
    });
    return props.multiple ? [...extraOptions, ...result] : result;
  };

  const flag = ref();
  const handleSearch = (val: string) => {
    flag.value = true;
    searchUsers(val);
  };

  const patchMulti = (v: string | string[]): string => {
    if (isArray(v)) {
      return v.join(',');
    }
    return v;
  };

  const change = (val: any) => {
    initFlag = false;
    emit('update:modelValue', patchMulti(val as string | string[]));
    emit('change', getOthersInfo(val as string | string[]));
    if (val.length < 1) {
      searchUsers('');
    } else if (val.length >= 1 && flag.value === true) {
      searchUsers('');
      flag.value = false;
    }
  };

  const getOthersInfo = (val: string | string[]) => {
    if (isArray(val)) {
      const res: UserState[] = [];
      val.forEach((v) => {
        // 多选需要采用options获取用户信息，因为每次重新搜索时originData会变成搜索的值,原来的用户取不到
        const item = options.value.find((it) => it.value === v);
        if (item) {
          res.push(item.data);
        }
      });
      return res;
    }
    // return options.value.find((it) => it.value === val);
    // 单选时选中的肯定是搜索的，且不需要考虑原来的值
    return originData.find((it) => {
      // 接口兼容
      const idValue = it.id || it.userId;
      return props.usingName ? it.username === val : idValue === val;
    });
  };
</script>

<script lang="ts">
  export default {
    name: 'UserSelector',
    inheritAttrs: false,
  };
</script>
