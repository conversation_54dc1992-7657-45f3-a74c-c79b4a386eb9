<template>
  <div class="container">
    <div class="user-item">
      <TableTitle :title="$t('userSetting.title.baseinfo')" />
      <UserPanel />
    </div>
    <div class="user-item Item">
      <div>
        <TableTitle
          style="padding: 0 0 20px 95px"
          :title="$t('userSetting.title.editPwd')" />
        <EditPwd
      /></div>
    </div>
    <div class="user-item Item">
      <div>
        <TableTitle
          style="padding: 0 0 20px 95px"
          :title="$t('userSetting.title.bind-Phone')" />
        <EditPhone
      /></div>
    </div>
    <div class="user-item Item">
      <div>
        <TableTitle
          style="padding: 0 0 20px 95px"
          :title="$t('userSetting.title.bind-Email')"
        />
        <EditEmail /> </div
    ></div>
  </div>
</template>

<script lang="ts" setup>
  import TableTitle from '@/components/table-title/index.vue';
  import UserPanel from './components/user-panel.vue';
  import EditPwd from './components/edit-pwd.vue';
  import EditPhone from './components/edit-phone.vue';
  import EditEmail from './components/edit-email.vue';
</script>

<script lang="ts">
  export default {
    name: 'Setting',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 0 44px 20px;
    height: 100%;
    overflow: auto;
  }
  .user-item {
    padding-top: 20px;
  }
  .Item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: -100px;
  }

  .wrapper {
    padding: 20px 0 0 20px;
    min-height: 580px;
    background-color: var(--color-bg-2);
    border-radius: 4px;
  }

  :deep(.section-title) {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 14px;
  }
</style>
