<template>
  <div style="margin: auto; width: 60%; padding: 20px">
    <a-form
      ref="formRef"
      :model="form3"
      layout="inline"
      :rules="rules"
      auto-label-width
    >
      <a-form-item
        field="name"
        :label="$t('model-collaboration.check-name')"
        validate-trigger="input"
        label-col-flex="90px"
        :rules="[
          {
            required: true,
            message: $t('model-collaboration.please-input'),
          },
          formRule.specialCharts,
        ]"
      >
        <!-- <remove-spaces-input
          v-model="form3.name"
          style="width: 335px"
          :max-length="currentLocale === 'en-US' ? 255 : 50"
          :placeholder="$t('model-collaboration.please-input')"
          allow-clear
          show-word-limit
        /> -->
        <a-input
          v-model="form3.name"
          style="width: 335px"
          :placeholder="$t('model-collaboration.please-input')"
          allow-clear
          :max-length="currentLocale === 'en-US' ? 255 : 50"
          show-word-limit
        />
      </a-form-item>
      <a-form-item
        field="teamId"
        :label="$t('model-collaboration.generateInTeam')"
        validate-trigger="change"
        label-col-flex="90px"
        :rules="[
          {
            required: true,
            message: $t('model-collaboration.selectTeam'),
          },
        ]"
      >
        <a-select
          v-model="form3.teamId"
          :field-names="{
            label: 'name',
            value: 'id',
          }"
          :style="{ width: '320px' }"
          :options="teamOptions"
          :placeholder="$t('model-collaboration.selectTeam')"
        />
      </a-form-item>

      <a-form-item
        field="type"
        validate-trigger="change"
        label-col-flex="90px"
        :rules="[
          {
            required: true,
            message: $t('model-collaboration.at-least-one'),
          },
        ]"
        :label="$t('model-collaboration.Collision-type')"
      >
        <a-checkbox-group
          v-model="form3.type"
          style="width: 220px"
          @change="checkTypeValue"
        >
          <a-checkbox :value="1">{{
            $t('model-collaboration.hard-collision')
          }}</a-checkbox>
          <a-checkbox :value="2">{{
            $t('model-collaboration.Gap-collision')
          }}</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <a-form-item
        field="tolerance"
        :label="$t('model-collaboration.Tolerance-collision')"
        validate-trigger="input"
        label-col-flex="90px"
        :rules="[
          {
            required: true,
            message: $t('model-collaboration.at-least-one'),
          },
        ]"
      >
        <a-input
          v-model="form3.tolerance"
          style="width: 335px"
          :placeholder="$t('model-collaboration.at-least-one')"
          allow-clear
          :disabled="!typeValue"
          @change="onTypeChange"
        >
          <template #append>mm</template>
        </a-input>
      </a-form-item>
      <a-typography-paragraph
        v-if="typeValue"
        :ellipsis="{
          rows: 2,
          showTooltip: true,
        }"
        style="margin: 0px 0px 0px 85px; color: var(--color-text-3)"
      >
        <icon-info-circle-fill style="color: burlywood" />
        <span>{{ $t('model-collaboration.tolerance-tip') }}</span>
      </a-typography-paragraph>
      <a-form-item
        :label="$t('model-collaboration.Checking-Rules')"
        style="margin-left: 12px"
        label-col-flex="78px"
      >
        <a-space direction="vertical">
          <a-checkbox
            v-model="form3.rule_model"
            :disabled="disabled || props.modelNumber === 2"
            >{{ $t('model-collaboration.forbid-same-model') }}
            <a-tooltip :content="$t('model-collaboration.vertical-tip')">
              <icon-info-circle-fill style="color: burlywood" />
            </a-tooltip>
          </a-checkbox>
          <a-checkbox v-model="form3.rule_storey" :disabled="disabled">{{
            $t('model-collaboration.forbid-same-layer')
          }}</a-checkbox>
          <a-checkbox v-model="form3.is_rule_component" :disabled="disabled">{{
            $t('model-collaboration.forbid-same-class')
          }}</a-checkbox>
          <a-form-item
            v-if="form3.is_rule_component"
            field="rule_component"
            :validate-trigger="['change', 'input']"
            :rules="[
              {
                required: true,
                message: $t('model-collaboration.at-least-one'),
              },
            ]"
          >
            <a-select
              v-model="form3.rule_component"
              :options="categories"
              style="width: 310px"
              :placeholder="$t('model-collaboration.Select-component-category')"
              allow-clear
              allow-search
              multiple
          /></a-form-item>
          <a-row>
            <a-checkbox
              v-model="form3.is_rule_group"
              :disabled="disabled"
              @change="ruleGroupCheck"
              >{{ $t('model-collaboration.forbid-collision') }}</a-checkbox
            >
            <icon-plus
              v-if="form3.is_rule_group"
              style="
                position: relative;
                cursor: pointer;
                color: rgb(51, 112, 255);
                top: 4px;
                left: 5px;
              "
              @click="iconClick"
            />
          </a-row>
          <a-list
            v-if="form3.is_rule_group"
            :bordered="false"
            :split="false"
            style="width: 410px"
            :data="form3.rule_group"
          >
            <template #item="{ item, index }">
              <a-list-item
                class="list-item"
                style="padding: 0"
                action-layout="vertical"
              >
                <a-form-item
                  :rules="[
                    {
                      required: true,
                      message: $t('model-collaboration.at-least-one'),
                    },
                  ]"
                  :merge-props="false"
                  style="margin-right: 0px"
                >
                  <a-space>
                    <a-form-item
                      :field="`rule_group[${index}].rule_group1`"
                      :validate-trigger="['change']"
                      :rules="[
                        {
                          required: true,
                          message: $t('model-collaboration.Select-first-type'),
                        },
                      ]"
                      no-style
                      style="margin-right: 0px"
                    >
                      <a-select
                        v-model="item.rule_group1"
                        :options="categories"
                        :style="{ width: '150px' }"
                        :placeholder="
                          $t('model-collaboration.Select-first-type')
                        "
                        allow-clear
                    /></a-form-item>
                    <a-form-item
                      :field="`rule_group[${index}].rule_group2`"
                      :validate-trigger="['change']"
                      :rules="[
                        {
                          required: true,
                          message: $t('model-collaboration.Select-second-type'),
                        },
                      ]"
                      no-style
                      style="margin-right: 0px"
                    >
                      <a-select
                        v-model="item.rule_group2"
                        :options="categories"
                        :style="{ width: '150px' }"
                        :placeholder="
                          $t('model-collaboration.Select-second-type')
                        "
                        allow-clear
                    /></a-form-item>
                  </a-space>
                </a-form-item>
                <template #actions>
                  <IconDelete
                    style="color: red; margin-left: 13px"
                    @click="() => onDeleteClick(item)"
                  ></IconDelete>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import {
    createXBaseModelClashNew,
    addMergaCrashCheck,
    clashList,
    queryTeamList,
  } from '../api';
  import { GetXBaseClashInfo } from '@/api/collision';
  import { Message } from '@arco-design/web-vue';
  import { useRoute } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import showErrorMessage from '@/api/msg';
  import useLocale from '@/hooks/locale';
  import { formRule } from '@/utils/index';

  const teamOptions = ref<any[]>([]);

  const { currentLocale } = useLocale();

  const { t } = useI18n();
  const route = useRoute();
  const props = defineProps({
    categories: {
      type: Array<any>,
      default: () => {
        return [];
      },
    },
    modelNumber: {
      type: Number,
      default: 1,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    stepData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const rules = {
    name: [
      {
        required: true,
        message: 'name is required',
      },
    ],
  };
  interface TreeData {
    fieldKey: string;
    rule_group1: string;
    rule_group2: string;
    children?: TreeData[];
  }
  interface FormState {
    name: string;
    type: number[];
    tolerance: string;
    rule_model: boolean;
    rule_storey: boolean;
    is_rule_component: boolean;
    rule_component: string[];
    is_rule_group: boolean;
    rule_group: TreeData[];
    teamId: string;
  }

  const form3 = ref<FormState>({
    name: '',
    type: [1],
    tolerance: '1',
    rule_model: false,
    rule_storey: false,
    is_rule_component: false,
    rule_component: [],
    is_rule_group: false,
    rule_group: [],
    teamId: '',
  });

  let i = 1;
  const typeValue = ref();
  const emit = defineEmits(['change', 'refresh', 'abnormal']);
  const checkTypeValue = (value: any) => {
    typeValue.value = value.includes(2);
  };
  const ruleGroupCheck = (val: any) => {
    if (val) {
      form3.value.rule_group.push({
        rule_group2: '',
        rule_group1: '',
        fieldKey: String(i++),
      });
    } else {
      form3.value.rule_group = [];
    }
  };
  const onTypeChange = (val: string) => {
    if (val) {
      // 只能输入数字，且不能是0开头
      val = val.replace(/[^\d]/g, '').replace(/^0{1,}/g, '');
      // 转千分位
      val = Number(val).toLocaleString();
    }
    if (val === '0') {
      val = '';
    }
    form3.value.tolerance = val;
  };

  const iconClick = (event: Event) => {
    if (form3.value.rule_group.length >= 5) {
      event.preventDefault();
    } else {
      form3.value.rule_group.push({
        rule_group2: '',
        rule_group1: '',
        fieldKey: String(i++),
      });
    }
  };

  const filterDel = (arr: TreeData[], fieldKey: string) => {
    arr.forEach((item, index) => {
      if (item.fieldKey === fieldKey) {
        arr.splice(index, 1);
      }
      if (item.children) {
        filterDel(item.children, fieldKey);
      }
    });
    return arr;
  };

  // 扁平化数组
  const flattenTeams = function (arr: any[]): any[] {
    const result: any[] = []; // 递归函数
    function traverse(item: any) {
      if (Array.isArray(item) && item.length) {
        // 如果teams是数组，遍历它
        item.forEach((item2: any) => {
          result.push(item2);
          if (item2.teams) {
            // 确保item有teams属性
            traverse(item.teams);
          }
        });
      } // 如果到达没有teams属性的对象或teams为空数组，则不执行任何操作
    } // 对输入数组的每个元素执行递归遍历

    arr.forEach((item) => {
      // 确保item有teams属性
      result.push(item);
      traverse(item.teams);
    });

    return result;
  };

  // 获取团队数据
  const getTeamList = async () => {
    const params: any = {
      pageNo: 1,
      pageSize: 10000,
      projectId: route?.params.projectId,
    };
    const res = await queryTeamList(params);
    if (res.status) {
      const arr = flattenTeams(res.data.list);
      teamOptions.value = arr;
    }
  };

  getTeamList();

  // 根据大象云报错code提示
  const showErrorMessageHandle = (code: string) => {
    emit('abnormal');
    const text = showErrorMessage(code);
    if (text !== 'undefined') Message.error(showErrorMessage(code));
  };

  const onDeleteClick = (nodeData: TreeData) => {
    filterDel(form3.value.rule_group, nodeData.fieldKey);
  };

  const formRef = ref<FormInstance>();

  // 查询模型id
  const getModelId = async (clashId: any) => {
    let result: any = null;
    const param = {
      page_num: 1,
      page_size: 1000,
      group_id: route.query.projectId,
    };
    try {
      const data = (await clashList(param)).data.list;
      result = data.find((item: any) => item.clash_id === clashId);
    } catch (error: any) {
      showErrorMessageHandle(error.data.code);
    }
    return result?.model_id || null;
  };

  // 获取碰撞检测数据
  const getClashInfo = async (clashId: any) => {
    let allres: any = null;
    const params = {
      clash_id: clashId,
      page_num: 1,
      page_size: 100,
      type: 3,
      group_id: route.query.projectId,
    };
    try {
      allres = await GetXBaseClashInfo({ ...params });
    } catch (error: any) {
      showErrorMessageHandle(error.data.code);
    }
    return allres.data.render_path;
  };

  // 判断语义模型是否转换成功
  const semanticModelStatus = (arr: any) => {
    let modelStatus = true;
    arr.forEach((item: any) => {
      if (Object.keys(item).length !== 0) {
        if (item.status !== 0) {
          Message.error(
            `${item.name}${t(
              'model-collaboration.conversion-was-not-successful-no-clash'
            )}`
          );
          modelStatus = false;
          emit('abnormal');
        }
      }
    });
    return modelStatus;
  };

  const clashRes: any = ref();

  // 创建碰撞检测
  const createXBaseModelClashHandle = async () => {
    const ruleGroup: any = [];
    const ruleGroupParam: any = {
      group1: [],
      group2: [],
    };
    if (form3.value.is_rule_group) {
      form3.value.rule_group.forEach((item: any) => {
        ruleGroupParam.group1.push(item.rule_group1);
        ruleGroupParam.group2.push(item.rule_group2);
      });
      ruleGroup[0] = ruleGroupParam.group1.join(':');
      ruleGroup[1] = ruleGroupParam.group2.join(':');
    }
    const params = {
      name: form3.value.name.trim(),
      type: form3.value.type.length === 2 ? 3 : form3.value.type[0],
      tolerance: Number(form3.value.tolerance),
      rule_model: form3.value.rule_model,
      rule_storey: form3.value.rule_storey,
      rule_component: form3.value.rule_component,
      rule_group: ruleGroup,
      model_list:
        props.modelNumber === 2
          ? props.stepData.modelInfo
          : [props.stepData.modelInfo[0]],
      group_id: route.params.projectId,
    };
    try {
      clashRes.value = await createXBaseModelClashNew(params);
    } catch (error: any) {
      showErrorMessageHandle(error.data.code);
      emit('abnormal');
    }
  };

  // 创建碰撞检测列表
  const addMergaCrashCheckhandle = async (
    clashModelId: any,
    renderPath: any
  ) => {
    const cdeParams = {
      name: form3.value.name.trim(),
      projectId: route.params.projectId,
      files:
        props.modelNumber === 2
          ? props.stepData.files
          : [props.stepData.files[0]],
      graphicEngineInfo: JSON.stringify({
        clashId: clashRes.value.data.clash_id,
        clashModelId:
          props.modelNumber === 2
            ? clashModelId
            : props.stepData.modelInfo[0].model_id,
        renderPath: props.modelNumber === 2 ? renderPath : null,
        modelInfo:
          props.modelNumber === 2
            ? [
                {
                  semantic_model_id: props.stepData.modelInfo[0].model_id,
                  render_path: props.stepData.modelInfo[1].render_path,
                },
                {
                  semantic_model_id: props.stepData.modelInfo[1].model_id,
                  render_path: props.stepData.modelInfo[1].render_path,
                },
              ]
            : {
                semantic_model_id: props.stepData.modelInfo[0].model_id,
                render_path: props.stepData.modelInfo[0].render_path,
              },
      }),
      teamId: form3.value.teamId,
    };
    try {
      const res: any = await addMergaCrashCheck(cdeParams);
      if (res.status) {
        Message.success(t('model-collaboration.success'));
        emit('refresh');
      }
    } catch (error: any) {
      showErrorMessageHandle(error.data.code);
      emit('abnormal');
    }
  };

  const submit = async () => {
    const res = await formRef.value?.validate();
    // // 生成团队校验
    // if (!teamId.value) {
    //   Message.error(t('model-collaboration.selectTeam'));
    //   emit('abnormal');
    //   return;
    // }
    // 若为为dgn文件并且选择的是两个模型则禁止碰撞
    if (props.disabled && props.modelNumber === 2) {
      Message.error(t('model-collaboration.dgn-files-are-not-supported'));
      emit('abnormal');
      return;
    }
    // 判断模型状态是否转换成功
    const status = await semanticModelStatus(props.stepData.modelInfo);
    if (!status) {
      emit('abnormal');
      return;
    }
    if (!res) {
      // 1.发起碰撞检测
      await createXBaseModelClashHandle();
      if (!clashRes.value.data.clash_id) return;
      // 2.获取碰撞检测结果的modelId和path（用于预览）
      const clashModelId = await getModelId(clashRes.value.data.clash_id);
      const renderPath = await getClashInfo(clashRes.value.data.clash_id);
      if (clashModelId && renderPath)
        // 3.创建碰撞列表
        await addMergaCrashCheckhandle(clashModelId, renderPath);
    } else {
      emit('abnormal');
    }
  };

  watch(
    () => form3.value.name,
    () => {
      if (form3.value.name === '') {
        emit('change', true);
      } else {
        emit('change', false);
      }
    }
  );
  watch(
    () => props.modelNumber,
    (val: any) => {
      if (val === 2) {
        form3.value.rule_model = true;
      } else {
        form3.value.rule_model = false;
      }
    }
  );

  defineExpose({
    submit,
  });
</script>

<style scoped lang="less">
  :deep .arco-tree-node-title:hover {
    background-color: white;
  }
  :deep(.arco-list-item-content) {
    display: inline-block;
  }
  :deep(.arco-list-item-action) {
    display: inline-block;
  }
  :deep(.arco-space-item .arco-select-view-single) {
    width: 168px !important;
  }
  :deep(.arco-space-vertical > .arco-space-item) {
    width: 250px;
  }
</style>
