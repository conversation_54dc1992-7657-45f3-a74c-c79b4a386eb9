<template>
  <a-breadcrumb :max-count="4" v-if="attachPathList.length > 1">
    <template #separator>
      <icon-right />
    </template>
    <a-breadcrumb-item v-for="(item, index) in attachPathList" :key="item.id">
      <a-tooltip :content="item.name" mini>
        <div class="breadcrumb-item" @click="handleClick(item, index)">{{
          item.name
        }}</div>
      </a-tooltip>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import useFileStore from '@/store/modules/file/index';
  import { defineEmits } from 'vue';

  const fileStore = useFileStore();

  const { attachPathList } = storeToRefs(fileStore);

  const emits = defineEmits(['handleClick']);

  function handleClick(item: { name: string; id: string }, index: number) {
    const list = attachPathList.value.slice(0, index + 1);
    emits('handleClick', list);
    fileStore.setAttachPathList(list);
  }
</script>

<style lang="less" scoped>
  .breadcrumb-item {
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 200px;
  }
  :deep(.arco-breadcrumb-item:last-child) {
    font-weight: 600;
  }
  :deep(.arco-breadcrumb-item-separator) {
    margin: 0;
  }
</style>
