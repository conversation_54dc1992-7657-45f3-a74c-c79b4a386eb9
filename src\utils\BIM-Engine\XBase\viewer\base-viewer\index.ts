import initDXY from '@/utils/xbase/utils/addScript';
import CryptoJS from 'crypto-js';
import i18n from '@/locale/index';
import {
  iconTypeXbase as XBaseIssueIcon,
  MarkerConfig,
} from '@/utils/xbase/tools/marker';
import { getComponentProperty, queryModelAttributes } from '@/utils/xbase/api';
import { nextTick } from 'vue';

declare const DX: any;
declare const OBV: any;
const { locale } = i18n.global;
export default class BaseViewer {
  private app: any;

  private viewer: any;

  private options: any;

  private elementId: any;

  private callback: any;

  private markerIds: string[] = [];

  private markerAcc = 0;

  private boundXbaseModelClick: (e: MouseEvent) => void; // 存储绑定后的函数

  constructor(elementId: any) {
    this.elementId = elementId;
    // 预先绑定 this，并存储引用
    this.boundXbaseModelClick = this.xbaseModelClick.bind(this);
  }

  // eslint-disable-next-line class-methods-use-this
  async render(options: any) {
    try {
      if (!('DX' in window)) await initDXY();
      this.options = new DX.DefaultConfigs();
      // 设置静态资源域的地址
      this.options.staticHost = options.staticHost;
      // 设置服务域的地址
      this.options.serverHost = options.serverHost;
      // 添加token
      this.options.accessToken = options.accessToken;
      // 渲染模式, 默认是 HYBRID
      this.options.engine =
        options.engine === '2d' ? DX.mode.TWOD : DX.mode.HYBRID;
      this.app = new DX.Application(this.elementId);
      await this.app.init(this.options);
      this.viewer = this.app.getViewer();
    } catch (e) {
      if (e) console.log(e.toString());
    }
    return this.getViewer();
  }

  getViewer(): any {
    return this.viewer;
  }

  getApp(): any {
    return this.app;
  }

  async openFile(filePath: string) {
    await this.app.mainWindow.openFile(filePath);
    // todo 需要大象云修改
    this.viewer.setLanguage(locale === 'zh-CN' ? DX.lang.ZH_CN : DX.lang.EN_US);
  }

  // getModelId() {
  //   return this.fileId;
  // }

  getCamera() {
    return this.viewer.getCamera();
  }

  setCamera(position: number[]) {
    this.viewer.setCamera(position);
  }

  reRender() {
    this.viewer.render();
  }

  modelTreeReady(func: () => void) {
    this.viewer.on(DX.Events.MODEL_TREE_READY, func);
  }

  setEntitiesColor(ids: any, custom = false) {
    let color: any = [];
    let states: any = [];
    if (!custom) {
      states = ids.map((item: string) => {
        return [item, 0];
      });
      color = [[130, 200, 150]];
      this.viewer.setEntitiesColor(states, color);
    } else {
      const idList = ids.map((item: any) => {
        return [
          CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(item[0])),
          item[1],
        ];
      });
      this.viewer.setEntitiesColor(idList);
    }
  }

  clearEntitiesColor(ids: string[]) {
    if (!Array.isArray(ids)) return;
    const idList = ids.map((item: string) => {
      return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(item));
    });
    this.viewer.clearEntitiesColor(idList);
  }

  // isBase64 :是否需要base64加密，默认为是
  selectEntities(id: string, isBase64 = true) {
    const param = {
      ids: [id],
      clear: true,
      goto: true,
    };
    this.viewer.selectEntities(param);
  }

  gotoByIds(ids: string[]) {
    if (!Array.isArray(ids)) return;
    const idList = ids.map((item: string) => {
      return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(item));
    });
    this.viewer.gotoByIds(idList);
  }

  // gotoByComponentIds(ids: string[]) {
  //   // renderPath: 当前预览的模型的路径
  //   // path: guid所在的模型路径
  //   // guid: 构件guid
  //   const suffix = this.renderPath?.substring(
  //     this.renderPath.lastIndexOf('.') + 1
  //   );
  //   let btoaGuid;
  //   if (suffix === 'asm') {
  //     const pathMapIndex = this.viewer?.engine?.pathMapIndex;
  //     const index = pathMapIndex.get(this.renderPath);
  //     btoaGuid =
  //       this.renderPath?.substring(this.renderPath.lastIndexOf('.') + 1) ===
  //       'ifc'
  //         ? btoa(`${index}.'${ids}'`)
  //         : btoa(`${index}.${ids}`);
  //   } else {
  //     btoaGuid = suffix === 'ifc' ? btoa(`'${ids}'`) : btoa(`${ids}`);
  //   }
  //   const res = this.viewer.getEntityChildrenIds(btoaGuid);
  //   this.viewer.gotoByIds(res); // 根据构件id集合将对应的构件实体缩放到视口中央
  //   this.viewer.clearEntitiesColor(); // 清除自定义的模型实体颜色
  //   this.viewer.setEntitiesColor([[res[0], 0]], [[110, 255, 110]]); // 设置模型实体颜色
  // }

  isolateEntities(ids: string[]) {
    if (!Array.isArray(ids)) return;
    const idList = ids.map((item: string) => {
      return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(item));
    });
    this.viewer.isolateEntities(idList);
  }

  showAll() {
    this.viewer.showAll();
  }

  getEntitiesBBoxAsync(ids: string[]) {
    if (!Array.isArray(ids)) return null;
    return this.viewer.getEntitiesBBoxAsync(ids);
  }

  gotoByBBox(bbox: []) {
    this.viewer.gotoByBBox(bbox);
  }

  toHome() {
    this.viewer.toHome();
  }

  getEntityChildrenIds(id: string) {
    return this.viewer?.getEntityChildrenIds(id);
  }

  hyalineEntities(ids: string[]) {
    this.viewer.hyalineEntities(ids);
  }

  entitySelectedEvent(func: (data: any) => void) {
    this.viewer.on(DX.Events.ENTITY_SELECTED, func);
  }

  entityDeselectedEvent(func: any) {
    this.viewer.on(DX.Events.ENTITY_DESELECTED, func);
  }

  // searchEntityIdsByProps(searchList: any[]) {
  //   return new Promise((resolve) => {
  //     const promiseList: Promise<any>[] = [];
  //     searchList.forEach((item: any) => {
  //       promiseList.push(
  //         queryModelAttributes({
  //           page_num: 0,
  //           page_size: 100,
  //           path: this.renderPath,
  //           op: 'e',
  //           name: item.name,
  //           value: item.value,
  //           group_id: projectId,
  //         })
  //       );
  //     });
  //     Promise.all(promiseList).then((res) => {
  //       for (let i = 0; i < res.length; i++) {
  //         if (res[i]?.data?.count > 0) {
  //           const { categories } = res[i].data.pageItems[0];
  //           resolve(categories);
  //           break;
  //         }
  //       }
  //     });
  //   });
  // }

  get2dViewInfo() {
    return this.viewer.getViewObjectsAsync();
  }

  // 清空选择构件
  clearSelectedEntities() {
    this.viewer.clearSelectedEntities();
  }

  // 模型装配-模型旋转
  rotateModel(data: any) {
    this.viewer.rotateModel(data.path, data.axis, data.angle);
  }

  // 模型装配-模型平移
  translateModel(data: any) {
    this.viewer.translateModel(data.motionPath, data.startPoint, data.endPoint);
  }

  // 设置是否可以可以使用构件选择功能
  setEnableSelection(value: boolean) {
    this.viewer.enableSelection(value);
  }

  // 模型平移初始化(true为开启点位捕捉  false关闭点位捕捉)
  pointPicked(val: any): any {
    // 是否允许选择构件
    this.setEnableSelection(!val);
    // 默认开启捕捉,可捕捉点，线，面
    this.app.execute(DX.Commands.ENABLE_SNAP, { toggled: val });
    // 限制捕捉类型DX.Snap3DTypes.piont/line/face
    this.viewer.setSnapTypes([DX.Snap3DTypes.Point]);
    // 关闭页面左下角坐标轴
    this.viewer.enableAxes(!val);
    // 开启时关闭viewcube
    this.viewer.enableViewCube(!val);
  }

  // 递归获取模型id
  async getModelIds(data: any) {
    console.log(this);
    const id: any = [];
    const getId = (value: any) => {
      value?.forEach((item: any) => {
        id.push(item.id);
        if (item.children?.length) getId(item.children);
      });
    };
    await getId(data);
    return id;
  }

  // 模型旋转-选择构件
  async rotateSelectModel(motionPath: any, lastMotionPath: any) {
    this.clearSelectedEntities();
    this.app.execute(DX.Commands.ENABLE_SNAP, { toggled: false });
    if (motionPath) {
      this.viewer.addAxes({ path: motionPath });
      this.viewer.removeModelBoxHelper(lastMotionPath); // 清除上一个选择模型
      this.viewer.createModelBoxHelper(motionPath);
      lastMotionPath = motionPath;

      // 模型旋转选中模型老代码
      // const treeNode = this.viewer.getModelTreeNodeByPath(motionPath);
      // if (!treeNode) return;
      // const stack = treeNode.children.slice();
      // this.setEnableSelection(true);
      // const ids = await this.getModelIds(stack);
      // this.viewer.selectEntities({ ids });
      // this.setEnableSelection(false);
    } else {
      this.viewer.addAxes({ path: '' });
      this.viewer.removeModelBoxHelper(lastMotionPath); // 清除上一个选择模型
    }
  }

  // 监听模型点位点击
  modelPointPickedListen(func: () => void) {
    this.app.execute(DX.Commands.ENABLE_SNAP, { toggled: false });
    this.viewer.on(DX.Events.POINT_PICKED, func);
  }

  searchEntityIdsByProps(searchList: any[], projectId: string) {
    return new Promise((resolve) => {
      const promiseList: Promise<any>[] = [];
      searchList.forEach((item: any) => {
        promiseList.push(
          // queryModelAttributes({
          //   page_num: 0,
          //   page_size: 100,
          //   path: this.options.path.slice(0, -4),
          //   op: 'e',
          //   name: item.name,
          //   value: item.value,
          //   group_id: projectId,
          // })
          queryModelAttributes({
            render_path: this.options.path.slice(0, -4),
            guids: item.value,
            prop_name: item.name,
            group_id: projectId,
          })
        );
      });
      Promise.all(promiseList).then((res) => {
        for (let i = 0; i < res.length; i++) {
          if (res[i]?.data?.count > 0) {
            const { categories } = res[i].data.pageItems[0];
            resolve(categories);
            break;
          }
        }
      });
    });
  }

  async searchEntityId(elementId: string) {
    let result = '';
    const res = await this.viewer?.searchProperties([
      { name: 'Element ID', value: elementId },
      { name: 'ID', value: elementId },
    ]);
    if (res.length) {
      result = res[0].dbId;
    }
    return result;
  }

  setObjectsColor(nodeInfo: any, custom?: boolean) {
    if (typeof nodeInfo !== 'object' || nodeInfo === null) return;
    if (custom) {
      this.viewer?.setEntitiesColor(nodeInfo, true);
    } else {
      const states = [
        [nodeInfo.id || nodeInfo.selectionIds[0], [51, 102, 255]],
      ];
      console.log('[ 标记颜色 ] >', states);
      try {
        this.viewer?.clearEntitiesColor(); // 清除所有自定义构件颜色
      } catch (error) {
      } finally {
        this.viewer?.setEntitiesColor(states);
      }
    }
  }

  clearObjectsColor(ids?: string[]) {
    this.viewer?.clearEntitiesColor(ids as string[]);
  }

  getViewMessage() {
    // return this.modelEngine === BIMBase
    //   ? this.bimBaseViewer?.getViewPointInfo()
    //   : this.xbaseViewer?.getCamera();
    return this.viewer?.getCamera();
  }

  clearMarker(ids?: string[]) {
    if (!this.viewer) return;
    if (ids) {
      this.viewer.removeMarker(ids);
      ids.forEach((item: string) => {
        const index = this.markerIds.findIndex((val: string) => val === item);
        if (index !== -1) this.markerIds.splice(index, 1);
      });
    } else if (this.markerIds.length) {
      console.log('[ 移除三维标签 markerIds ] >', this.markerIds);
      this.viewer.removeMarker(this.markerIds);
      this.markerIds = [];
      this.markerAcc = 0;
    }
  }

  // 通过二维坐标添加三维标签
  async addMarker(pageX: number, pageY: number, config?: MarkerConfig) {
    if (!this.viewer) return null;
    const data = await this.viewer.pickPointAsync([pageX, pageY]);
    if (data.point !== null) {
      const position = data.result.slice(0, 3);
      // 添加标签
      this.viewer.createMarker([
        {
          id: `${this.markerAcc}`,
          name: ' ',
          size: [36, 60],
          icon: config?.icon,
          position,
          ...config,
        },
      ]);
      this.markerIds.push(config?.id || `${this.markerAcc++}`);
      return { id: data.id, position: data.result.slice(0, 3) };
    }
    return null;
  }

  async xbaseModelClick(e: any) {
    this.clearMarker(['issue']);
    const data: any = await this.addMarker(e.pageX, e.pageY, {
      id: 'issue',
      name: ' ',
      size: [36, 60],
      icon: XBaseIssueIcon.unsolvedImg,
    });
    if (this.callback) this.callback(data);
  }

  // 通过三维坐标直接添加三维标签
  create3DMarker(position: number[]) {
    // 添加标签
    this.viewer.createMarker([
      {
        id: `${++this.markerAcc}`,
        name: ' ',
        size: [36, 60],
        icon: XBaseIssueIcon.unsolvedImg,
        position,
      },
    ]);
    this.markerIds.push(`${this.markerAcc}`);
  }

  listenToAddMarker(func: (data: any) => void) {
    this.callback = func;
    const doc = document.getElementById(this.elementId as string) as Element;
    doc.addEventListener('click', this.boundXbaseModelClick); // 使用预先绑定的函数
  }

  stopListenToAddMarker() {
    const doc = document.getElementById(this.elementId as string) as Element;
    doc.removeEventListener('click', this.boundXbaseModelClick); // 移除同一个函数
  }

  // 获取指定构件属性值
  async getSpecifiedComponentProperty(guids: string[], projectId: string) {
    const params = {
      render_path: this.options.path.slice(0, -4),
      guids,
      prop_group_name: 'Element_Specific',
      prop_name: 'Tag',
      group_id: projectId,
    };
    const res = await getComponentProperty(params);
    return res;
  }

  /**
   * 根据addMarker的回调获取要存在数据库的elementId
   * @param data 回调的入参
   * @returns elementId
   */
  async getElementIdByMarkData(data: any) {
    let elementId = '';
    const type = localStorage.getItem('model_type');
    if (type === 'rvt') {
      const arr = OBV.Base64UrlSafe.decode(data.id);
      const guid = arr.slice(1, -1); // 移除第一个和最后一个字符;
      const res = await this.getSpecifiedComponentProperty(
        [guid],
        data.projectId
      );
      elementId = res.data?.element_list[0]?.list[0]?.value;
    }
    return elementId;
  }

  // 碰撞检测定位构件 设置构件颜色
  collisionGotoByComponentIds(ids: any, color: any) {
    this.viewer.gotoByIds(ids); // 构件定位
    this.viewer.clearEntitiesColor(); // 清除自定义的模型实体颜色
    const states = [
      [ids[0], 0],
      [ids[1], 1],
    ];
    this.viewer.setEntitiesColorOverride(states, color); // 设置构件颜色
    this.viewer.isolateEntities(ids);
  }

  // 获取模型装配的所有模型path
  getPathMapIndex() {
    return Array.from(this.viewer?.engine?.pathMapIndex);
  }
}
