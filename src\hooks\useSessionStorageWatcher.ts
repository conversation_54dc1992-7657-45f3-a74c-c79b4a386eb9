export default function useSessionStorageWatcher(key: string, callback: any) {
  let originalValue = sessionStorage.getItem(key);

  // 处理变化的函数
  const handleStorageChange = () => {
    const newValue = sessionStorage.getItem(key);

    if (newValue !== originalValue) {
      const oldValue = originalValue;
      originalValue = newValue;
      callback(newValue, oldValue);
    }
  };

  // 初始化监听
  const startWatching = () => {
    // 立即检查初始值
    originalValue = sessionStorage.getItem(key);

    // 监听自定义事件（当前窗口）
    window.addEventListener('cdex-session-storage-change', handleStorageChange);
  };

  // 清理监听
  const stopWatching = () => {
    window.removeEventListener(
      'cdex-session-storage-change',
      handleStorageChange
    );
  };

  return {
    startWatching,
    stopWatching,
  };
}
