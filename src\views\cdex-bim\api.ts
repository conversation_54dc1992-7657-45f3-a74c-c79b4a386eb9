
import axios from 'axios';
import { getUserName } from '@/utils/auth'
import qs from 'query-string';

// 查询用户最近文件
export function getLoginCode() {
  return axios.post('/bimapi/sys-auth/oauth/code', {
    redirect_uri: 'https://cdex.ccccltd.cn/',
    username: getUserName(),
    client_id: 'fusion',
    tenantId: '100000'
  }, {
    baseURL: ''
  });
}
export function getLoginToken(code: any) {
  const params = {
    code,
    redirect_uri: 'https://cdex.ccccltd.cn/',
    grant_type: 'authorization_code'
  };
  return axios.post('/bimapi/sys-auth/oauth/token', qs.stringify(params), {
    baseURL: ''
  });
}

export default null;
