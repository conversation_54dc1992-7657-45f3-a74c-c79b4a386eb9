<template>
  <a-modal
    :visible="visible"
    :title="title"
    :width="676"
    :footer="!disabled"
    :unmount-on-close="true"
    :ok-loading="loading"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
    @open="init"
  >
    <a-form ref="memberRef" :model="formData" :disabled="disabled">
      <a-row :gutter="16">
        <a-col flex="320px">
          <a-form-item
            field="userNameKey"
            :label="$t('prjMember.column.name')"
            label-col-flex="68px"
            validate-trigger="input"
            :rules="[
              {
                required: true,
                message: $t('prjMember.name-required'),
              },
            ]"
          >
            <remove-spaces-input
              v-model="formData.name"
              :placeholder="$t('prjMember.please-enter-phone')"
            />
          </a-form-item>
        </a-col>
        <a-col flex="320px">
          <a-form-item
            field="phone"
            :label="$t('prjMember.column.phone')"
            label-col-flex="68px"
            validate-trigger="input"
            :disabled="disabled || formData.accountState == 1"
            :rules="[
              {
                required: true,
                message: $t('prjMember.phone-required'),
              },
              {
                match: /^(\+\d{1,3})?\d{7,13}$/, //正则替换  *匹配大陆港澳台
                message: $t('login.form.telInvalid'),
              },
            ]"
          >
            <remove-spaces-input
              v-model="formData.phone"
              :placeholder="$t('prjMember.please-enter-phone')"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col flex="320px">
          <a-form-item
            field="email"
            :label="$t('prjMember.column.email')"
            validate-trigger="input"
            :rules="[
              {
                required: true,
                message: $t('prjMember.email-required'),
              },
              {
                match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, //邮箱宽松正则替换
                message: $t('userSetting.form.email.error'),
              },
            ]"
            label-col-flex="68px"
          >
            <remove-spaces-input
              v-model="formData.email"
              :placeholder="$t('prjMember.please-enter-email')"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { MemberRecord, addMember, updateMember } from '@/api/member';
  import useLoading from '@/hooks/loading';
  // 导入搜索用户下拉框接口
  import { searchUser } from '@/api/user';

  // 用户列表改下拉框新增
  type userType = {
    id?: string;
    name?: string;
    phone?: string;
    email?: string;
    userId?: string;
  };
  const searchLoading = ref(false);
  const userList = ref<userType[]>([]);

  const getAllUserList = async (searchValue = '') => {
    searchLoading.value = true;
    const { data } = await searchUser(searchValue);
    if (data.length) {
      userList.value = data;
    } else {
      userList.value = [];
    }

    searchLoading.value = false;
  };

  const init = () => {
    userList.value = [];
  };

  interface MemberRecordExt extends Partial<MemberRecord> {
    moduleVisible?: string[];
    userNameKey?: string;
  }
  const { t } = useI18n();
  const { loading, setLoading } = useLoading(false);

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    userId: {
      type: String,
      default: '',
    },
    form: {
      type: Object,
      default() {
        return {
          id: '',
          userId: '',
          name: '',
          email: '',
          phone: '',
          role: '',
          // projectAdmin: 0,
          moduleVisible: '0,1',
        };
      },
    },
  });
  const emit = defineEmits(['update:visible', 'refresh']);

  const formatForm = (form: MemberRecord): MemberRecordExt => {
    const { moduleVisible, ...rest } = form;
    // 默认moduleVisible为所有
    const moduleVisibleArray: string[] = ['0', '1'];
    // if (!isArray(moduleVisible)) {
    //   moduleVisibleArray = moduleVisible?.split(',') || [];
    // }
    return {
      ...rest,
      userNameKey: form.name,
      moduleVisible: moduleVisibleArray,
    };
  };
  const formData = ref<MemberRecordExt>(formatForm(props.form));
  watch(
    () => props.visible,
    (n) => {
      formData.value = formatForm(props.form || {});
    }
  );

  const selectedUser = ref<userType>({
    id: '',
    name: '',
    email: '',
    phone: '',
  });

  // 选择名字之后手机和邮箱回显
  watch(
    () => formData.value.userId,
    (newVal) => {
      if (newVal && !props.form.name) {
        selectedUser.value = {
          ...userList.value.find((item) => item.id === newVal),
        };
        formData.value.phone = selectedUser.value.phone;
        formData.value.email = selectedUser.value.email;
      }
    }
  );

  const memberRef = ref<FormInstance>();
  const handleBeforeOk = async (done: any) => {
    const res = await memberRef.value?.validate();
    if (!res) {
      setLoading(true);
      let flg = false;
      let msg = t('prjMember.success');
      if (props.title === t('prjMember.edit.title')) {
        flg = await editMember();
        msg = t('prjMember.success');
      } else if (props.title === t('prjMember.add.title')) {
        flg = await addOneMember();
      } else {
        // 查看无按钮
      }
      if (flg) {
        Message.success(msg);
        emit('update:visible', false);
        emit('refresh');
      }
      setLoading(false);
      done();
    }
  };
  const editMember = async () => {
    try {
      const { moduleVisible, userId, ...rest } = formData.value;
      const moduleVisibleString = moduleVisible?.join(',') || '';
      const res = await updateMember({
        ...rest,
        // 用传进来的用户id,避免被修改过
        userId: props.form.userId,
        moduleVisible: moduleVisibleString,
      });
      // 更新用户名单
      getAllUserList();
      return !!res.status;
    } catch (error) {
      return false;
    }
  };
  const addOneMember = async () => {
    try {
      const { moduleVisible, userId, ...rest } = formData.value;
      const moduleVisibleString = moduleVisible?.join(',') || '';
      const res = await addMember({
        ...rest,
        registerUrl: `${window.location.origin}/register-user`,
        moduleVisible: moduleVisibleString,
      });
      // 更新用户名单
      getAllUserList();
      return !!res.status;
    } catch (error) {
      return false;
    }
  };
  const handleCancel = () => {
    emit('update:visible', false);
  };
  // const getUserInfo = (val: any) => {
  //   // 此处有值表示选择，undefined表示新加或数据为空
  //   if (val) {
  //     // 赋值姓名，手机和邮箱
  //     formData.value.name = val.name;
  //     formData.value.email = val.email;
  //     formData.value.phone = val.phone;
  //     // 清除校验
  //     memberRef.value?.clearValidate(['name', 'email', 'phone']);
  //   }
  //   // else {
  //   //   formData.value.name = '';
  //   //   formData.value.email = '';
  //   //   formData.value.phone = '';
  //   // }
  // };
</script>

<script lang="ts">
  export default {
    name: 'AddMember',
  };
</script>

<style scoped lang="less">
  .user-item {
    width: 190px;
    //border: 1px solid red;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
