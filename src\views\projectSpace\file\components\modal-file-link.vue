<template>
  <a-modal
    :visible="show"
    width="700px"
    @before-open="onBeforeOpen"
    @before-close="onBeforeClose"
    @cancel="handleCancel"
  >
    <template #title> {{ $t('file-manage.share') }} </template>
    <div class="content-wrap">
      <a-row>
        <a-col :span="18" class="table_view">
          <table>
            <tr>
              <th class="link-title">{{
                $t('file-manage.file-share-file-link')
              }}</th>
              <a-tooltip :content="form.link">
                <td>{{ form.link }}</td>
              </a-tooltip>
            </tr>
            <tr v-if="form.code !== ''">
              <th class="link-title">{{ $t('file-manage.file-code') }}</th>
              <td>{{ form.code }}</td>
            </tr>
          </table>
        </a-col>
        <a-col :span="6">
          <img v-if="qrcodeSrc" :src="qrcodeSrc" class="img_view" />
        </a-col>
      </a-row>
    </div>
    <template #footer>
      <div>
        <a-button @click="handleCancel" style="margin-right: 12px">{{
          $t('file-manage.close')
        }}</a-button>
        <a-button id="copyBtn" style="margin-right: 10px" @click="handleCtrl">
          {{
            form.code === ''
              ? $t('file-manage.copy-link')
              : $t('file-manage.copy-link-and-code')
          }}
        </a-button>
        <!-- <a-button
          id="copyAndCloseBtn"
          type="primary"
          style="margin-left: 10px; margin-right: 6px"
          @click="handleConfirm"
          >{{ $t('file-manage.confirm') }}</a-button
        > -->
      </div>
    </template>
    <div class="line"></div>
  </a-modal>
</template>

<script setup lang="ts">
  import { defineProps, ref, toRefs } from 'vue';
  import { copy } from '../utils';
  import { useI18n } from 'vue-i18n';
  // @ts-ignore
  import QRcode from 'qrcode';

  const { t } = useI18n();

  const props = defineProps({
    show: {
      type: Boolean,
      required: true,
    },
    shareLink: {
      type: Object,
      required: true,
    },
  });
  const emits = defineEmits(['update:show']);

  const { shareLink } = toRefs(props);

  const form = ref({ link: '', code: '' });
  const qrcodeSrc = ref('');

  // 关闭弹窗
  const handleCancel = () => {
    emits('update:show', false);
  };

  // 复制链接
  const writeTextFn = (obj: string) => {
    const text = form.value.code
      ? `${form.value.link}\n提取码：${form.value.code}`
      : `${form.value.link}`;
    copy(obj, text, t('file-manage.successfully-copied'));
  };

  // 复制链接并关闭弹窗
  const handleConfirm = () => {
    writeTextFn('#copyAndCloseBtn');
    handleCancel();
  };

  // 复制链接及提取码
  const handleCtrl = () => {
    writeTextFn('#copyBtn');
  };

  // 对话框打开前触发
  const onBeforeOpen = async () => {
    const link = `${window.location.origin}/work/share-download?uuid=${shareLink.value.uuid}`;
    const linkWithMobile = `${window.location.origin}/work/share-mobile?uuid=${shareLink.value.uuid}`;
    form.value.link = link;
    form.value.code = shareLink.value.extractedCode as string;

    // 生成二维码并转为图片地址
    try {
      QRcode.toDataURL(linkWithMobile, { width: 94 }).then((res: string) => {
        qrcodeSrc.value = res;
      });
    } catch (err) {
      console.error('生成二维码失败:', err);
    }
  };

  // 对话框关闭前触发
  const onBeforeClose = () => {
    Object.assign(form.value, { link: '', code: '' });
    qrcodeSrc.value = '';
  };
</script>

<style scoped lang="less">
  .link-title {
    color: #86909c;
    background-color: #f7f8fa !important;
  }
  .content-wrap {
    height: 100px;
    padding-left: 30px;
    box-sizing: border-box;
  }
  .btn_view {
    :deep(.arco-form-item-content-flex) {
      display: flex;
      justify-content: end;
    }
  }
  .line {
    width: 1px;
    height: 80px;
    background-color: #e5e6eb;
    position: absolute;
    top: 33px;
    left: 522px;
  }
  .form_item_link {
    :deep(.arco-form-item-content-flex) {
      justify-content: end;
    }
  }
  .table_view {
    table {
      width: 440px;
      border-collapse: collapse;
    }
    th,
    td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
      max-width: 200px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    th {
      background-color: #f2f2f2;
    }
  }
  .img_view {
    width: 97px;
    height: 97px;
    border: 1px solid #e5e6eb;
    display: block;
    margin-left: 28px;
  }
  :deep(.arco-row-justify-start) {
    align-items: center !important;
  }
</style>
