import { RemoteDataCommon } from '@/types/global';

export type RoleType = '' | '*' | 'admin' | 'user';
// 0 系统管理员 -1 普通成员 1 项目创建员 2 停用的项目创建员 3 单位管理员
export type AdminType = 0 | 1 | -1 | 3;

export interface UserState extends RemoteDataCommon {
  userId?: string;
  id?: string;
  username?: string;
  userName?: string;
  userFullname?: string;
  name?: string;
  phone?: string;
  email?: string;
  userNo?: string;
  accountState?: number;
  avatarToken?: string;
  fid?: number;
  admin: AdminType;
  role: RoleType;
  color?: string;
  standardTab: string;
  orgNo?: number;
  orgName?: string;
  pathName?: string;
  pathNo?: any;
  tenantId?: string;
  projectList?: any;
}
