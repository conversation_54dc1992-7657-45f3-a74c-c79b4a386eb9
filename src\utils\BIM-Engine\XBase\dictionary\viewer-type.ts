/*
 * by: chenming
 * date: 2024.09.06
 * desc: 预览时，不同viewerType对应的文件拓展名
 * */
// model（模型）、semanticModel（语义模型）、component（构件）、asset（资产）、scene（场景）、document(文件)
const viewerTypeMap = {
  model: [
    'skp',
    'obj',
    'stl',
    'glb',
    'fbx',
    'gltf',
    'stp',
    'step',
    'x_t',
    '3ds',
    'dae',
    'nwd',
  ], // dwg上传时走文件上传，预览时用模型预览
  component: ['rfa'],
  // semanticModel: ['rvt', 'dgn', 'ifc'], // 语义模型会根据是否有语义模型ID判断，此处无需处理
  document: ['pdf', 'doc', 'docx', 'xlsx', 'pptx', 'wps', 'csv', 'dwg'],
};

export default viewerTypeMap;
