<template>
  <a-modal
    :unmount-on-close="true"
    :visible="visible"
    :title="title"
    width="850px"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="cancel"
  >
    <template #footer>
      <div class="add-dialog-footer">
        <div class="add-dialog-footer-left" v-if="!showMarquee.includes(projectCode)">
          <span class="add-dialog-footer-label">{{ $t('design.sync-push-to-jjt') }}</span>
          <a-checkbox
            v-model="formData.pushToReviewer"
            style="margin-right: 8px;"
          >{{ $t('design.approver') }}</a-checkbox>
          <a-checkbox
            v-if="props.title !== t('design.delivery')"
            v-model="formData.pushToRecipient"
          >{{ $t('design.recipient') }}</a-checkbox>
        </div>
        <div class="add-dialog-footer-right">
          <a-button
            v-if="props.initialData.id"
            type="primary"
            status="danger"
            @click="RemoveData()"
            class="footer-btn"
          >{{ $t('design.remove') }}</a-button>
          <a-button @click="cancel" class="footer-btn">{{ $t('design.cancel') }}</a-button>
          <!-- <a-button
            v-if="teamPermission > 1"
            :loading="submitBtnLoading"
            @click="submitData(true)"
            class="footer-btn"
          >{{ $t('design.save') }}</a-button> -->
          <a-button
            v-if="teamPermission > 1"
            type="primary"
            :loading="submitBtnLoading"
            @click="submitData(false)"
            class="footer-btn"
          >{{ $t('design.submit') }}</a-button>
        </div>
      </div>
    </template>
    <a-spin style="width: 100%" :loading="loading">
      <div class="content">
        <a-form
          ref="formRef"
          :model="formData"
          label-align="right"
          :rules="rules"
        >
          <a-row>
            <a-col :span="12">
              <a-form-item
                field="processId"
                :label="$t('design.approve-workflow')"
                label-col-flex="90px"
                :validate-trigger="['change', 'input']"
              >
                <a-select
                  v-model="formData.processId"
                  :placeholder="$t('design.please-choose')"
                >
                  <a-option
                    v-for="template in processList"
                    :key="`${template.id}-${template.name}`"
                    :value="template.id"
                    :label="template.name"
                  ></a-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item
                field="name"
                :label="`${title}${$t('design.name')}`"
                :tooltip="title === '共享' ? $t('design.share-name-tip') : ''"
                label-col-flex="110px"
                :validate-trigger="['change', 'input']"
              >
                <a-input
                  v-model="formData.name"
                  :placeholder="$t('design.please-choose')"
                  :max-length="currentLocale === 'en-US' ? 255 : 100"
                  show-word-limit
                />
                <!-- <a-input
                  v-model="formData.name"
                  :placeholder="$t('design.please-choose')"
                /> -->
              </a-form-item>
            </a-col>
          </a-row>

          <a-row v-if="title === $t('design.share')">
            <a-col :span="12">
              <a-form-item
                field="userNames"
                :label="$t('design.recipients')"
                :tooltip="$t('design-recipients-tip')"
                label-col-flex="90px"
                :validate-trigger="['change', 'input']"
              >
                <a-tree-select
                  v-model="formData.userNames"
                  :field-names="{
                    key: 'userName',
                    title: 'name',
                    children: 'users',
                  }"
                  :allow-search="true"
                  :allow-clear="true"
                  :multiple="true"
                  :tree-checkable="true"
                  :tree-checked-strategy="'child'"
                  :data="teamList"
                  :placeholder="$t('design.please-choose')"
                  :filter-tree-node="filterTreeNode"
                ></a-tree-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-divider />
          <a-row>
            <a-col :span="24">
              <a-form-item hide-label>
                <div class="file-list" style="width: 100%">
                  <div class="title">
                    <div class="text">
                      <img
                        src="@/assets/images/design/<EMAIL>"
                        alt=""
                        style="width: 17px; height: 17px"
                      />
                      <span class="text-font">{{
                        $tc('design.file-required', { name: title })
                      }}</span>
                    </div>
                    <div class="file-count">
                      <span>{{
                        $tc('design.file-counts', fileCounts || 0)
                      }}</span>
                      <a-button
                        v-if="teamPermission > 1"
                        type="text"
                        @click="treeFolderVisible = true"
                      >
                        {{ $t('design.add-file') }}
                      </a-button>
                    </div>
                  </div>
                  <div class="file-list-wrap">
                    <FileCollapse
                      v-show="fileList?.length"
                      v-model:files="fileList"
                    ></FileCollapse>
                  </div>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="title === $t('design.delivery')">
            <a-col :span="12">
              <a-form-item
                field="milestoneId"
                :label="$t('design.milestone')"
                label-col-flex="80px"
                :validate-trigger="['change', 'input']"
              >
                <a-select
                  v-model="formData.milestoneId"
                  :placeholder="$t('design.please-choose')"
                >
                  <a-option
                    v-for="template in milestoneList"
                    :key="`${template.id}-${template.name}`"
                    :value="template.id"
                    :label="template.name"
                  ></a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-divider v-if="title === $t('design.delivery')" />

          <a-row v-if="title === $t('design.delivery')">
            <span>{{ $t('design.to-third-system') }}</span>
            <a-space
              style="margin-left: 80px"
              direction="vertical"
              size="large"
            >
              <a-radio-group v-model="systemCheck">
                <a-radio value="0">{{ $t('design.yes') }}</a-radio>
                <a-radio value="1">{{
                  $t('design.no')
                }}</a-radio></a-radio-group
              ></a-space
            ></a-row
          >

          <a-row
            v-if="title === $t('design.delivery')"
            style="padding-top: 20px"
          >
            <a-col v-if="systemCheck === '0'" :span="12">
              <a-form-item
                field="thirdPartySystem"
                :label="$t('design.system-name')"
                label-col-flex="90px"
                :validate-trigger="['change', 'input']"
              >
                <a-select
                  v-model="formData.thirdPartySystem"
                  :placeholder="$t('design.please-choose')"
                  allow-clear
                >
                  <a-option
                    :value="1"
                    :label="$t('design.data-asset')"
                  ></a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col v-if="systemCheck === '0'" :span="12">
              <a-form-item
                field="recipient"
                :label="$tc('design.recipients')"
                label-col-flex="90px"
                :validate-trigger="['change', 'input']"
              >
                <a-select
                  v-model="formData.recipient"
                  :placeholder="$t('design.please-choose')"
                  allow-clear
                  allow-search
                >
                  <a-option
                    v-for="item in recipient"
                    :key="`${item.id}-${item.userFullname}`"
                    :value="`${[item.id, item.userFullname, item.userName]}`"
                    :label="item.userFullname"
                  ></a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row v-if="title === $t('design.delivery')">
            <span>{{ $t('design.project-information') }}</span>
            <a-space
              style="margin-left: 150px"
              direction="vertical"
              size="large"
            >
              <a-radio-group v-model="thirdPartyIncloudProject">
                <a-radio value="1">{{ $t('design.yes') }}</a-radio>
                <a-radio value="0">{{
                  $t('design.no')
                }}</a-radio></a-radio-group
              ></a-space
            ></a-row
          >

          <a-row
            v-if="title === $t('design.delivery')"
            style="padding-top: 20px"
          >
            <a-col :span="12">
              <a-form-item
                v-if="thirdPartyIncloudProject === '1'"
                field="projectName"
                :label="$t('design.project-name')"
                label-col-flex="90px"
                :disabled="true"
                :validate-trigger="['change', 'input']"
              >
                <a-input
                  v-model="projectInfo.name"
                  :placeholder="$t('design.please-enter')"
                />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item
                v-if="thirdPartyIncloudProject === '1'"
                field="code"
                :label="$t('design.project-code')"
                label-col-flex="90px"
                :disabled="true"
                :validate-trigger="['change', 'input']"
              >
                <a-input
                  v-model="projectInfo.code"
                  :placeholder="$t('design.please-enter')"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row v-if="title === $t('design.delivery')">
            <a-col :span="12">
              <a-form-item
                v-if="thirdPartyIncloudProject === '1'"
                field="planData"
                :label="$t('design.project-date')"
                label-col-flex="90px"
                :disabled="true"
                :validate-trigger="['change', 'input']"
              >
                <a-range-picker v-model="planData" />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item
                v-if="thirdPartyIncloudProject === '1'"
                field="type"
                :label="$t('design.project-type')"
                label-col-flex="90px"
                :disabled="true"
                :validate-trigger="['change', 'input']"
              >
                <a-input
                  v-model="projectInfoType"
                  :placeholder="$t('design.please-enter')"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row v-if="title === $t('design.delivery')">
            <a-col :span="24">
              <a-form-item
                v-if="thirdPartyIncloudProject === '1'"
                field="description"
                :label="$t('design.project-description')"
                label-col-flex="90px"
                :disabled="true"
                :validate-trigger="['change', 'input']"
              >
                <a-textarea
                  v-model="projectInfo.description"
                  :placeholder="$t('design.please-enter')"
                  show-word-limit
                  :auto-size="{
                    minRows: 2,
                  }"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <TreeFolder
          v-model:visible="treeFolderVisible"
          :title="$t('design.add-file')"
          :ok-function="fileChange"
          :checked-data="fileIdList"
          :team-ids="treeFolderConfig.teamIds"
          :show-module="treeFolderConfig.module"
        ></TreeFolder>
        <TreeFolder
          v-if="initialData.id"
          v-model:visible="calculateModal"
          :ok-function="calculateFileChange"
          :team-ids="treeFolderConfig.teamIds"
          :show-module="treeFolderConfig.module"
          :checked-data="initFileList"
          :is-calculate="true"
        ></TreeFolder>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch, h } from 'vue';
  import { useI18n } from 'vue-i18n';
  import FileCollapse from './file-collapse/index.vue';
  import TreeFolder from '@/components/tree-folder/index.vue';
  import { useRoute } from 'vue-router';
  import router from '@/router';
  import {
    Message,
    Notification,
    Button,
    Modal,
    FormInstance,
  } from '@arco-design/web-vue';
  import {
    getProcess,
    addDeliver,
    saveDeliver,
    addShare,
    saveShare,
    submitShare,
    submitDelivery,
    removeShare,
  } from './api';
  import useLoading from '@/hooks/loading';
  // import useFileManageStore from '@/store/modules/file-manage/index';
  // import { useGlobalModeStore } from '@/store';
  import {
    ProjectRecord,
    queryProjectDetail,
    queryTeamListAll,
    getDirectoryFileCounts,
    getDirectoryFileIds,
    queryMilestoneList,
    queryThirdRecipient,
  } from '../api';
  import useLocale from '@/hooks/locale';
  import { formRule } from '@/utils/index';

  const { t } = useI18n();
  // 国际化类型
  const { currentLocale } = useLocale();
  // const globalModeStore = useGlobalModeStore();
  // const store = useFileManageStore();

  interface ProcessObj {
    id?: string | number;
    name?: string;
  }

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    files: {
      type: Array,
      default() {
        return [];
      },
    },
    treeFolderConfig: {
      type: Object,
      default: () => {
        // empty
      },
    },
    initialData: {
      type: Object,
      default: () => {
        // empty
      },
    },
    teamPermission: {
      type: Number,
      default: 0,
    },
    currentTeamId: {
      type: String,
      default() {
        return '';
      },
    },
  });

  const systemCheck = ref('1');
  const thirdPartyIncloudProject = ref('0');
  const submitBtnLoading = ref(false);
  const emits = defineEmits(['update:visible', 'submit', 'refreshTeamData']);
  const route = useRoute();
  const processList = ref<ProcessObj[]>([]);
  const getProcessList = () => {
    const params = {
      pageNo: 1,
      pageSize: 100000,
      projectId: route.params.projectId,
      teamId: props.currentTeamId,
    };
    getProcess(params)
      .then((res: any) => {
        if (res.code === 8000000) {
          processList.value = res.data.list || [];
        }
      })
      .catch((e) => {
        if (e) {
          processList.value = [];
        }
      });
  };

  const milestoneList = ref<ProcessObj[]>([]);
  const getMilestoneList = () => {
    queryMilestoneList(route.params.projectId as string)
      .then((res: any) => {
        if (res.status) {
          milestoneList.value = res.data.list || [];
        }
      })
      .catch((e) => {
        if (e) {
          milestoneList.value = [];
        }
      });
  };

  const formRef = ref<FormInstance>();
  const formData = ref({
    name: '',
    processId: '',
    milestoneId: '',
    userNames: [],
    thirdPartySystem: '',
    recipient: '',
    pushToReviewer: false,
    pushToRecipient: false,
  });

  const rules = computed(() => ({
    name: [
      { required: true, message: t('design.enter-name') },
      formRule.specialCharts,
    ],
    processId: [
      {
        required: true,
        message: t('design.select-process'),
      },
    ],
    milestoneId: [
      {
        required: true,
        message: t('design.select-milestone'),
      },
    ],
    userNames: [
      {
        required: false,
        message: t('design.select-recipient'),
      },
    ],
    thirdPartySystem: [
      {
        required: true,
        message: t('design.select-system'),
      },
    ],
  }));
  const fileList = ref([]);

  const fileCounts = computed(() => {
    let counts = 0;
    fileList?.value?.forEach((file) => {
      counts += getDirectoryFileCounts(file);
    });
    return counts;
  });

  const getIdByFileList = (fileList2: any[]) => {
    const ids: any[] = [];
    fileList2?.forEach((item) => {
      if (item.abandon !== 1) {
        if ('folderId' in item) {
          ids.push(item.id);
        }
        if (item.children) {
          ids.push(...getIdByFileList(item.children));
        }
      }
    });
    return ids;
  };

  const fileIdList = computed(() => {
    const result = getIdByFileList(fileList.value);
    return result;
  });

  const fileIds = computed(() => {
    const ids: any[] = getDirectoryFileIds(fileList.value);
    return ids;
  });
  const treeFolderVisible = ref(false);
  // 递归函数去除 abandon = 1 的文件
  function filterData(node: any) {
    if (node.children) {
      // 处理子节点
      node.children = node.children.filter((child: any) => {
        // 只保留 abandon 不为 1 的子节点
        if (child.abandon !== 1) {
          // 递归处理子节点
          filterData(child);
          return true; // 保留这个节点
        }
        return false; // 过滤掉
      });
    }
    if (node.files) {
      // 处理文件
      node.files = node.files.filter((file: any) => file.abandon !== 1);
    }
  }
  const fileChange = async (data: () => Promise<any>) => {
    const files = await data();
    // 执行函数
    files.forEach((item: any) => filterData(item));
    fileList.value = files;
  };

  const cancel = () => {
    fileList.value = [];
    emits('update:visible', false);
  };

  const handleNotificationError = (msg: string) => {
    const id = `${Date.now()}`;
    Notification.error({
      id,
      title: t('design.attention'),
      content: msg,
      duration: 0,
      closable: true,
    });
  };

  const submitData = async (isSave = false) => {
    submitBtnLoading.value = true;
    const res = await formRef.value?.validate();
    if (!res) {
      let params: any = {};
      const userNameList = formData.value.userNames.map((item: any) => {
        return item.split('_')[0];
      });
      if (props.initialData.id) {
        params = {
          ...props.initialData,
          name: formData.value.name,
          fileIds: fileIds.value,
          fileCount: fileCounts.value,
          standardId: 0,
          userNames: userNameList.join(','),
        };
      } else {
        params = {
          name: formData.value.name,
          cdeProcessId: formData.value.processId,
          fileIds: fileIds.value,
          projectId: route.params.projectId,
          fileCount: fileCounts.value,
          teamId: props.treeFolderConfig.teamId,
          standardId: 0,
          userNames: userNameList.join(','),
        };
      }
      if (!params.fileCount) {
        Message.error(t('design.select-least'));
        submitBtnLoading.value = false;
      } else if (props.title === t('design.share')) {
        let api: any = null;
        if (isSave) {
          api = saveShare;
          submitBtnLoading.value = true;
        } else if (props.initialData.id) {
          params.id = props.initialData.id;
          api = submitShare;
        } else {
          api = addShare;
          submitBtnLoading.value = true;
          params.pushToReviewer = formData.value.pushToReviewer;
          params.pushToRecipient = formData.value.pushToRecipient;
        }
        api(params)
          .then((result: any) => {
            if (result.status) {
              if (result.data === 'MODEL_CHECKING') {
                // 校验中
                Notification.info({
                  title: t('file-manage.checking'),
                  content: t('design.in-examination-verification'),
                });
              } else {
                Notification.info({
                  title: t('design.succeeded'),
                  content: t('design.success-verification'),
                });
              }
              emits('submit', true);
              emits('refreshTeamData');
              cancel();
            } else {
              handleNotificationError(result.message);
              cancel();
            }
            submitBtnLoading.value = false;
          })
          .catch(() => {
            submitBtnLoading.value = false;
          });
      } else {
        let api = null;
        let deliverP = {};

        if (isSave) {
          api = saveDeliver;
          submitBtnLoading.value = true;
          if (formData.value.recipient.length > 0) {
            deliverP = {
              ...params,
              milestoneId: formData.value.milestoneId,
              thirdPartySystem: formData.value.thirdPartySystem,
              thirdPartyUserId: formData.value.recipient.split(',')[0],
              thirdPartyUserFullname: formData.value.recipient.split(',')[1],
              thirdPartyUserName: formData.value.recipient.split(',')[2],
              thirdPartyIncloudProject: thirdPartyIncloudProject.value,
              pushToReviewer: formData.value.pushToReviewer,
            };
            if (props.title === t('design.share')) {
              deliverP.pushToRecipient = formData.value.pushToRecipient;
            }
          } else {
            deliverP = {
              ...params,
              milestoneId: formData.value.milestoneId,
              thirdPartySystem: formData.value.thirdPartySystem,
              thirdPartyUserId: '',
              thirdPartyUserFullname: '',
              thirdPartyUserName: '',
              thirdPartyIncloudProject: thirdPartyIncloudProject.value,
              pushToReviewer: formData.value.pushToReviewer,
            };
            if (props.title === t('design.share')) {
              deliverP.pushToRecipient = formData.value.pushToRecipient;
            }
          }
        } else if (props.initialData.id) {
          params.id = props.initialData.id;
          deliverP = params;
          api = submitDelivery;
        } else {
          api = addDeliver;
          if (formData.value.recipient.length > 0) {
            deliverP = {
              ...params,
              milestoneId: formData.value.milestoneId,
              thirdPartySystem: formData.value.thirdPartySystem,
              thirdPartyUserId: formData.value.recipient.split(',')[0],
              thirdPartyUserFullname: formData.value.recipient.split(',')[1],
              thirdPartyUserName: formData.value.recipient.split(',')[2],
              thirdPartyIncloudProject: thirdPartyIncloudProject.value,
              pushToReviewer: formData.value.pushToReviewer,
            };
            if (props.title === t('design.share')) {
              deliverP.pushToRecipient = formData.value.pushToRecipient;
            }
          } else {
            deliverP = {
              ...params,
              milestoneId: formData.value.milestoneId,
              thirdPartySystem: formData.value.thirdPartySystem,
              thirdPartyUserId: '',
              thirdPartyUserFullname: '',
              thirdPartyUserName: '',
              thirdPartyIncloudProject: thirdPartyIncloudProject.value,
              pushToReviewer: formData.value.pushToReviewer,
            };
            if (props.title === t('design.share')) {
              deliverP.pushToRecipient = formData.value.pushToRecipient;
            }
          }
          submitBtnLoading.value = true;
        }
        api(deliverP)
          .then((result: any) => {
            if (result.code === 8000000) {
              Notification.info({
                title: t('design.succeeded'),
                content: t('design.success-verification'),
              });
              emits('submit', true);
              emits('refreshTeamData');
              cancel();
            } else {
              handleNotificationError(result.message);
              cancel();
            }
            submitBtnLoading.value = false;
          })
          .catch(() => {
            submitBtnLoading.value = false;
          });
      }
    } else {
      submitBtnLoading.value = false;
    }
  };
  const RemoveData = async (isSave = false) => {
    console.log(formData.value);
    const id = props.initialData.id || '';
    Modal.warning({
      title: t('design-delete-tip'),
      content: t('design-delete-tip'),
      closable: true,
      hideCancel: false,
      onOk: async () => {
        let params = {};
        if (props.title === t('design.share')) {
          params = {
            id,
            type: 'collaborate',
          };
        } else {
          params = {
            id,
            type: 'delivery',
          };
        }
        await removeShare(params).then((result: any) => {
          if (result.code === 8000000) {
            Notification.success({
              title: t('design.succeeded'),
              content: t('design.deleteSuccessfully'),
            });
            emits('submit', true);
            emits('refreshTeamData');
            cancel();
          } else {
            Notification.warning({
              title: t('design.deleteFailed'),
              content: t('design.deleteFailed'),
            });
            cancel();
          }
        });
      },
    });
  };

  // 保存共享包回显
  const initFileList = computed(() => {
    let result = [];
    if (props.initialData.fileList) {
      result = props.initialData.fileList.map((item: any) => item.fileId);
    }
    return result;
  });
  const calculateModal = ref(false);
  const loading = ref(false);
  const calculateFileChange = async (data: () => Promise<any>) => {
    const files = await data();
    fileList.value = files;
    calculateModal.value = false;
    loading.value = false;
  };

  // 查询收件人
  const { setLoading } = useLoading(true);
  const teamList = ref();
  const getMemberList = () => {
    queryTeamListAll(route.params.projectId)
      .then((res) => {
        teamList.value = res.data.teams || [];

        teamList.value.forEach((item: any) => {
          item.userName = item.id;
          item.disabled = true;

          item.users.forEach((ele: any) => {
            ele.userName = `${ele.userName}_${ele.teamId?.toString()}`;
          });
        });
        console.log(teamList.value);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const init = () => {
    if (props.initialData.id) {
      loading.value = true;
      calculateModal.value = true;
    }
    formData.value = {
      name: props.initialData.name || '',
      processId: props.initialData.cdeProcessId || '',
      milestoneId: props.initialData.milestoneId || '',
      userNames: props.initialData.userNames?.split(',') || [],
      thirdPartySystem: props.initialData.thirdPartySystem || '',
      recipient: props.initialData.thirdPartyUserFullname || [],
      pushToReviewer: typeof props.initialData.pushToReviewer !== 'undefined' ? Boolean(props.initialData.pushToReviewer) : false,
      pushToRecipient: typeof props.initialData.pushToRecipient !== 'undefined' ? Boolean(props.initialData.pushToRecipient) : false,
    };
    if (props.initialData.thirdPartySystem) {
      systemCheck.value = '0';
    } else {
      systemCheck.value = '1';
    }
    if (props.initialData.thirdPartyIncloudProject === 0) {
      thirdPartyIncloudProject.value = `${props.initialData.thirdPartyIncloudProject}`;
    } else {
      thirdPartyIncloudProject.value = '0';
    }
    if (Array.isArray(props.files) && props.files?.length) {
      fileList.value = JSON.parse(JSON.stringify(props.files));
    }
    getProcessList();
    getMilestoneList();
    getMemberList();
  };
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        init();
        if (props.title === t('design.delivery')) {
          formData.value.pushToRecipient = false;
        }
      }
    }
  );

  watch(
    () => systemCheck.value,
    () => {
      if (systemCheck.value === '1') {
        formData.value.thirdPartySystem = '';
        formData.value.recipient = '';
      }
    }
  );

  const projectId = computed(() => {
    return String(route?.params?.projectId);
  });

  const emptyProject: Partial<ProjectRecord> = {
    id: projectId.value,
    name: '',
    code: '',
    type: 0,
    protemId: '',
    planStart: '',
    planEnd: '',
    description: '',
  };

  const projectInfo = ref<Partial<ProjectRecord>>(emptyProject);

  // 获取第三方系统收件人
  const recipient = ref();
  const getThirdRecipient = () => {
    const params = {};
    queryThirdRecipient(params)
      .then((res) => {
        recipient.value = res.data || [];
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // 添加projectCode变量和showMarquee数组
  const projectCode = ref('');
  const showMarquee = ['CDex001'];
  
  // 查询项目详细信息
  const planData = ref();
  const projectInfoType = ref();
  const getProjectDetail = () => {
    const params = {
      id: projectId.value,
    };
    queryProjectDetail(params)
      .then((res) => {
        projectInfo.value = res.data;
        projectCode.value = res.data.code;
        planData.value = [
          projectInfo.value.planStart,
          projectInfo.value.planEnd,
        ];
        switch (projectInfo.value.type) {
          case 1:
            projectInfoType.value = t('design.highway-engineering');
            break;
          case 2:
            projectInfoType.value = t('design.urban-road-engineering');
            break;
          case 3:
            projectInfoType.value = t('design.airport-runway-engineering');
            break;
          case 4:
            projectInfoType.value = t('design.traffic-engineering');
            break;
          case 5:
            projectInfoType.value = t('design.bridge-engineering');
            break;
          case 6:
            projectInfoType.value = t('design.culvert-engineering');
            break;
          case 7:
            projectInfoType.value = t('design.port-engineering');
            break;
          case 8:
            projectInfoType.value = t('design.navigation-engineering');
            break;
          case 9:
            projectInfoType.value = t('design.tunneling-engineering');
            break;
          default:
            // 如果没有匹配的case，可以执行此默认操作，例如：
            projectInfoType.value = '';
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // getThirdRecipient();
  getProjectDetail();

  watch(
    () => systemCheck.value,
    (val) => {
      if (val === '0') {
        getThirdRecipient();
      }
    }
  );

  const filterTreeNode = (searchValue: string, nodeData: any) => {
    return nodeData.name.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  };
</script>

<style scoped lang="less">
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .file-list-wrap {
    margin-top: 16px;
    min-height: 20px;
  }

  .add-dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .add-dialog-footer-left {
    display: flex;
    align-items: center;
  }
  .add-dialog-footer-label {
    margin-right: 8px;
  }
  .add-dialog-footer-right {
    display: flex;
    align-items: center;
  }
  .footer-btn {
    margin-right: 12px;
  }
  .footer-btn:last-child {
    margin-right: 0;
  }
</style>
