<template>
  <div class="container" :class="{ disabled: disabled }">
    <!-- 上传按钮 -->
    <div id="picker">
      <div class="shadow"></div>
      <slot></slot>
    </div>
    <!-- :echo-file-arr="echoFileArr" -->
    <upload-list
      v-if="uploadFileStore.fileArr.length"
      :file-arr="uploadFileStore.fileArr"
      :disabled="disabled"
    ></upload-list>
  </div>
</template>

<script lang="ts" setup>
  // import useUploadFileStore from '@/store/modules/upload-file/index';
  import useUploadFileStore from '@/store/modules/upload-file/index';

  import { ref, onMounted } from 'vue';

  import UploadList from './upload-list.vue';

  const uploadFileStore = useUploadFileStore();
  const disabled = ref(false);
  // const uploadfileArr = uploadFileStore.uploadfileArr;

  // const { fileArr } = uploadFileStore;

  // // 修改暂存的信息
  // const amendInfo = (fileObj) => {
  //   uploadFileStore.fileArr.value.some((file, index) => {
  //     if (file.id === fileObj.id) {
  //       uploadFileStore.fileArr.value.splice(index, 1);
  //       return true;
  //     }
  //     return false;
  //   });
  // };
  // const getFileKey = (fileObj) => {
  //   console.log(fileObj);
  //   return 1;
  // };

  onMounted(() => {
    console.log(1111);
  });
</script>

<style lang="less" scoped>
  .container {
    position: relative;
    .shadow {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 1;
      display: none;
    }
  }

  .disabled {
    .shadow {
      display: block;
      cursor: not-allowed;
    }
  }

  :deep(.webuploader-container) {
    width: 100%;
    position: relative;
    display: inline-block;
  }

  :deep(.webuploader-element-invisible) {
    display: none;
  }
</style>
