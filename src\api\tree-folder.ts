import axios from 'axios';
import { Paging } from '@/types/global';
import { TreeNodeData } from '@arco-design/web-vue';

export interface FolderMessage {
  id?: string;
  name?: string;
  englishName?: string;
  idPath?: string;
  path?: string;
  englishPath?: string;
  title?: string;
  projectId?: string;
  teamId?: string;
  userId?: string;
  parentId?: number;
  childId?: string;
  children?: FolderMessage[];
  isFileOrFolder?: number;
  type?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  sysType?: number;
}

export interface FileMessage {
  id?: string;
  name?: string;
  projectId?: string;
  teamId?: string;
  folderId?: string;
  fileToken?: string;
  description?: string;
  size?: string;
  type?: string;
  version?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  isLocked?: boolean;
  status?: number;
}

export interface TableDataMessage {
  createBy: string;
  createDate: string;
  deleteFlag: number;
  fileMeta: null;
  id: string;
  isLocked: false;
  name: string;
  parentId: string;
  path: string;
  projectId: string;
  sysType: number;
  teamId: string;
  type: string;
  updateBy: string;
  updateDate: string;
}

export interface FileAndFolderNodeMessage
  extends TreeNodeData,
    FileMessage,
    FolderMessage {
  /** 0:folder 1:file */
  isFileOrFolder?: number;
  children?: FileAndFolderNodeMessage[];
  sysType?: number;
  key?: string;
}

export interface FileAndFolderMessage extends FileMessage, FolderMessage {
  /** 0:folder 1:file */
  isFileOrFolder?: number;
}

export function getFolderList(id?: number) {
  return axios.get<Paging<FolderMessage>>('/cde-collaboration/folder/list', {
    params: {
      id,
    },
  });
}

export function getChildFolderList(
  projectId?: string,
  teamId?: string,
  type?: string,
  parentId = '0'
) {
  return axios.get<Paging<FolderMessage>>(
    '/cde-collaboration/folder/children',
    {
      params: {
        pageNo: 1,
        projectId,
        teamId: type === 'PUBLISHED' ? undefined : teamId,
        type,
        parentId,
        pageSize: 9999,
      },
    }
  );
}

/// cde-collaboration/agenda/folderTeamsByUserId
export function getFolderTeamsByUserId(params: any) {
  return axios.get('/cde-collaboration/agenda/folderTeamsByUserId', { params });
}

export function getFileList(folderId?: string, fileName?: string) {
  if (folderId === undefined) return null;
  return axios.get<Paging<FileMessage>>('/cde-collaboration/file/list', {
    params: {
      folderId,
      fileName,
      pageSize: 9999,
    },
  });
}

export function getChildrenAll(parentId: string) {
  return axios.get('/cde-collaboration/folder/childrenAll', {
    params: {
      parentId,
    },
  });
}
