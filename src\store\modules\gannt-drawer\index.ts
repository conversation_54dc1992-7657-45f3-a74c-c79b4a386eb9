import { defineStore } from 'pinia';
import { drawerParams } from './type';
import { agendaDetail } from '@/views/create-schedule/api';
import { getMeetingDetail } from '@/views/schedule/component/meeting/api';

const usegGanntDrawerStore = defineStore('ganntDrawer', {
  state: (): drawerParams => {
    return {
      drawerData: {}, // 事项/会议数据
    };
  },
  actions: {
    // 设置事项数据（通过id查询事项详情数据）
    async setDrawerData(val: any) {
      if (val.type === 'meeting') {
        const { data } = await getMeetingDetail(val.id);
        this.drawerData = data;
      } else {
        const { data } = await agendaDetail(val.id);
        data.status = data.agendaStatus; // 为使用统一字段
        this.drawerData = data;
      }
    },
  },
});

export default usegGanntDrawerStore;
