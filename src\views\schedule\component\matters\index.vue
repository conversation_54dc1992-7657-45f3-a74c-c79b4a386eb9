<template>
  <div class="matter-container">
    <!-- 左侧列表区域 -->
    <MatterList
      ref="matterListRef"
      class="matter-left"
      type="matter"
      :edit-type="type"
      @select-data="selectData"
      @on-create="handleCreate"
    />
    <!-- 右侧编辑区域 -->
    <div v-if="type == 'edit' && !matterData.title" class="noData">
      <img :src="scheduleBgImg" alt="" />
      <div>暂无内容</div></div
    >
    <div v-else class="matter-right">
      <div class="content">
        <!-- 搜索框 -->
        <div class="describe-box">
          <a-input
            v-model="title"
            placeholder="请输入标题"
            class="title-input"
            :disabled="type !== 'new' && !isRelatedToUser"
          >
            <template
              v-if="matterData.parentScheduleDetailId || matterData.meetingId"
              #append
            >
              <a-button
                v-show="matterData.parentScheduleDetailId"
                type="outline"
                @click="goToMainMatter(matterData)"
                >{{ $t('calendar.head-to-main-task') }}</a-button
              >

              <a-button
                v-show="matterData.meetingId"
                type="outline"
                class="go-to-meeting-detail"
                @click="goToMeeting(matterData)"
                >{{ $t('calendar.go-to-meeting-detail') }}</a-button
              >
            </template>
          </a-input>
        </div>

        <CategoryMatter
          ref="matterRef"
          :key="type"
          :title="title"
          :type="type"
          :data="type === 'edit' ? matterData : {}"
          @update:title="title = $event"
          @refresh="refreshHandle"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import MatterList from '@/views/schedule/component/scheduleList.vue';
  import { computed, nextTick, ref, watch } from 'vue';
  import CategoryMatter from '@/views/create-schedule/component/category-matter.vue';
  import scheduleBgImg from '@/assets/images/schedule/schedule-bg.png';
  import { cloneDeep } from 'lodash';
  import { useUserStore } from '@/store';

  const userStore = useUserStore();
  const userName = computed(() => userStore.username);

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
    types: {
      type: String,
      default: 'edit', // 设置默认值
    },
  });

  const matterRef = ref<any>(null);
  const title = ref('');
  const type = ref('edit'); // 默认是编辑模式
  const matterData: any = ref({});

  const isRelatedToUser = ref<boolean>(false);
  // 判断用户的查看权限
  const checkPermission = async (data: any) => {
    const detail = cloneDeep(data);

    // 判断 createBy 是否为该用户
    const isCreateByUser = detail.createBy === userName.value;

    // 判断 chargePersonId 中是否包含该用户
    const chargeList = (detail.chargePersonId || '').split(',');
    const isChargePerson = chargeList.includes(userName.value);

    // 合并判断
    isRelatedToUser.value = isCreateByUser || isChargePerson;
  };

  const selectData = async (val: any) => {
    type.value = 'edit';
    if (type.value === 'edit') {
      matterData.value = {};
      await nextTick();
      matterData.value = val;
      checkPermission(val);
      title.value = val?.title;
    }
  };

  const matterListRef = ref<any>(null);

  // 刷新数据
  const refreshHandle = (id: any) => {
    console.log('触发了这里70', id);
    if (id === 'new') {
      matterListRef.value.init();
      type.value = 'edit'; // 设置为编辑模式
      return;
    }
    matterListRef.value.init(id);
  };
  const handleCreate = (data: { type: string; editType: string }) => {
    console.log('触发了这里80');
    console.log('接收到的参数:', data);
    if (data.type === 'matter' && data.editType === 'new') {
      type.value = 'new'; // 设置为创建模式
      matterData.value = {}; // 清空数据
      title.value = ''; // 清空标题
    } else {
      type.value = 'edit'; // 设置为编辑模式
    }
  };

  /**
   * 前往主事项
   * @param id 事项详情id
   */
  const goToMainMatter = (data: any) => {
    matterRef.value.beforeJumpMatter(data, 'main');
  };

  /**
   * 前往会议
   * @param id 会议详情id
   */
  const goToMeeting = (data: any) => {
    matterRef.value.beforeJumpMeeting(data);
  };

  // 监听有id则回显  用于日程的编辑跳转到对应的数据编辑
  watch(
    () => props.id,
    (val) => {
      console.log('监听到的props.id91:', val);
      setTimeout(() => {
        matterListRef.value.init(val);
      }, 100);
    },
    { immediate: true }
  );
  watch(
    () => props.types,
    (newValue) => {
      console.log('监听到的类型100:', newValue);
      if (newValue === 'new') {
        type.value = 'new'; // 切换为创建模式
        matterData.value = {}; // 清空数据
        title.value = ''; // 清空标题
      } else {
        type.value = 'edit'; // 切换为编辑模式
      }
    },
    { immediate: true } // 初始化时立即执行
  );
  watch(
    title,
    (newValue, oldValue) => {
      console.log('title 发生变化:');
      console.log('旧值:', oldValue);
      console.log('新值:', newValue);
    },
    { immediate: true } // 初始化时立即执行一次
  );
</script>

<script lang="ts">
  export default {
    name: 'Matter',
  };
</script>

<style lang="less" scoped>
  .matter-container {
    display: flex;
    height: 100%;
    overflow: auto;

    .matter-left {
      width: calc(30% - 1px);
      height: 100%;
      background-color: #fff;
      overflow-y: auto;
    }
    ::-webkit-scrollbar {
      width: 8px; /* 滚动条宽度 */
    }
    ::-webkit-scrollbar-thumb {
      background-color: #eeeeee; /* 滚动条滑块颜色 */
      border-radius: 8px; /* 滑块圆角 */
    }

    .matter-right {
      width: 70%;
      height: 100%;
      border: 1px solid #d9d9d9;
      border-bottom-right-radius: 8px;
      border-top-right-radius: 8px;
      background-color: #fff;
      overflow-y: auto;
      .describe-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        border-bottom: 1px solid #ddd;
        .title-input {
          font-size: 20px;
          background-color: #fff;
          color: #1d2129;
          border: none !important;
          height: 60px;
          line-height: 60px;

          :deep(.arco-input) {
            font-size: 20px;
            color: #1d2129;
          }
          :deep(.arco-input-wrapper) {
            background-color: #fff;
            border: 0 !important;
          }

          .arco-input-wrapper {
            padding: 0 20px 0 20px;
          }

          :deep(.arco-input-append) {
            background-color: #fff;
            margin-right: 16px;
            border: 0;
          }

          .go-to-meeting-detail {
            color: #ff6b00;
            border: 1px solid #ff6b00;
          }
        }
      }
    }
    .noData {
      // border-radius: 8px;
      height: 100%;
      // width: calc(47% - 20px);
      width: 70%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #d9d9d9;
      flex-direction: column;
      img {
        display: block;
        width: 140px;
        height: 140px;
      }
      div {
        margin-top: 16px;
        color: #4e5969;
      }
    }
  }
</style>
