<template>
  <a-modal
    :visible="visible"
    :title="$t('standard-attribute.binding-standard')"
    title-align="start"
    :unmount-on-close="true"
    :mask-closable="false"
    @cancel="cancel"
    @ok="submitHandle"
  >
    <a-form ref="formRef" :model="form" auto-label-width layout="vertical">
      <a-form-item
        field="standardName"
        :label="$t('standard-attribute.standard-name')"
        :validate-trigger="['change', 'input']"
        :rules="[
          {
            required: true,
            message: $t('standard-attribute.please-select-standard-name'),
          },
        ]"
      >
        <a-select
          v-model="form.standardName"
          :placeholder="$t('standard-attribute.please-select')"
          allow-search
          @change="standardNameChange"
        >
          <a-option
            v-for="item in standardName"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          />
        </a-select>
      </a-form-item>

      <a-form-item
        field="scene"
        :label="$t('standard-attribute.attribute-standard')"
        :validate-trigger="['change', 'input']"
        :rules="[
          {
            required: true,
            message: $t('standard-attribute.please-select-attribute-criteria'),
          },
        ]"
      >
        <a-tree-select
          v-model="form.scene"
          :field-names="{
            key: 'id',
            title: 'name',
            children: 'childList',
          }"
          allow-search
          :filter-tree-node="filterTreeNode"
          :data="attributeStandard"
          :placeholder="$t('standard-attribute.please-select')"
        >
        </a-tree-select>
      </a-form-item>
      <a-form-item
        field="precision"
        :label="$t('standard-attribute.fineness')"
        :validate-trigger="['change', 'input']"
        :rules="[
          {
            required: true,
            message: $t('standard-attribute.please-select-fineness'),
          },
        ]"
      >
        <a-select
          v-model="form.precision"
          :placeholder="$t('standard-attribute.please-select')"
          allow-search
        >
          <a-option
            v-for="item of precision"
            :key="item.code"
            :value="item.code"
            :label="item.contentMap?.['zh-CN']"
          />
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import {
    getStandardName,
    getAttributeStandard,
    bindStandardBySingleFile,
    bindStandardByMultiFolder,
    bindStandardByMultiFiles,
    getPrecision,
  } from '../../api';
  import { getUserId } from '@/utils/auth';
  import { cloneDeep } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { useI18n } from 'vue-i18n';

  const props: any = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    // 绑定类型
    type: {
      type: String,
      required: false,
    },
    // 单个绑定文件/文件夹id
    singleData: {
      type: Object,
      required: false,
    },
    //  批量绑定文件/文件夹id
    multiIds: {
      type: Object,
      required: false,
    },
  });

  const { t } = useI18n();

  const userId = getUserId() || '';
  const projectId = getLocalstorage(`last_project_${userId}`) || '';

  const emits = defineEmits(['update:visible', 'refresh']);

  const form: any = ref({});

  const cancel = () => {
    emits('update:visible', false);
  };

  const filterTreeNode = (searchValue: string, nodeData: any) => {
    return nodeData.name.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  };

  // 单个文件绑定标准
  const bindSingleFile = async () => {
    try {
      const params = {
        standardId: form.value.standardName, // 标准名称
        classification: form.value.scene, // 属性标准
        accuracy: form.value.precision, // 精细度
        fileId: props.singleData.id,
      };
      bindStandardBySingleFile(params).then((res) => {
        if (res.status) {
          Message.success(t('standard-attribute.success'));
        }
        emits('update:visible', false);
      });
    } catch (error) {
      console.log(error);
    }
  };

  // 批量文件绑定标准
  const bindMultiFiles = async () => {
    try {
      const params = {
        standardId: form.value.standardName, // 标准名称
        classification: form.value.scene, // 属性标准
        accuracy: form.value.precision, // 精细度
        fileIds: props.multiIds.fileIds,
        type: 1,
      };
      console.log('[ params ] >', params);
      bindStandardByMultiFiles(params).then((res) => {
        if (res.status) {
          Message.success(t('standard-attribute.success'));
        }
        emits('update:visible', false);
      });
    } catch (error) {
      console.log(error);
    }
  };

  // 批量文件夹绑定标准
  const bindMultiFolder = async () => {
    try {
      const params = {
        standardId: form.value.standardName,
        classification: form.value.scene,
        accuracy: form.value.precision,
        folderIds:
          props.type === 'singleFolder'
            ? [props.singleData.id]
            : props.multiIds.folderIds,
      };
      bindStandardByMultiFolder(params).then((res) => {
        if (res.status) {
          Message.success(t('standard-attribute.success'));
        }
        emits('update:visible', false);
      });
    } catch (error) {
      console.log(error);
    }
  };

  // 批量文件及文件夹绑定标准
  const bindMultiFilesAndFolders = async () => {
    try {
      const params = {
        standardId: form.value.standardName,
        classification: form.value.scene,
        accuracy: form.value.precision,
      };
      // 并发调用两个接口
      const [folderRes, fileRes]: any = await Promise.all([
        bindStandardByMultiFolder({
          ...params,
          folderIds: props.multiIds.folderIds,
        }),
        bindStandardByMultiFiles({
          ...params,
          fileIds: props.multiIds.fileIds,
          type: 1,
        }),
      ]);
      // 判断两个请求的 status 是否都为 true
      if (folderRes?.status === true && fileRes?.status === true) {
        Message.success(t('standard-attribute.success'));
      }
      emits('update:visible', false);
    } catch (error) {
      console.log(error);
    }
  };

  // 提交绑定标准
  const formRef: any = ref(null);

  const submitHandle = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      if (props.type === 'singleFile') {
        bindSingleFile();
      } else if (
        props.type === 'singleFolder' ||
        props.type === 'multiFolders'
      ) {
        bindMultiFolder();
      } else if (props.type === 'multiFiles') {
        bindMultiFiles();
      } else if (props.type === 'multiFilesAndFolders') {
        bindMultiFilesAndFolders();
      }
      emits('refresh');
    }
  };
  const userStore = useUserStore();

  // 获取标准名称
  const standardName = ref();
  const queryStandardName = () => {
    try {
      const params = {
        pageNo: 1,
        pageSize: 9999,
        groupId: userStore.admin === 0 ? '0' : projectId,
      };
      getStandardName(params).then((res) => {
        const list = res.data?.list || [];
        const filteredData = list.filter(
          (item: { standardType: number }) => item.standardType === 1
        );
        standardName.value = cloneDeep(filteredData);
      });
    } catch (error) {
      console.log(error);
    }
  };

  // 获取属性标准
  const attributeStandard = ref();
  const queryAttributeStandard = (value?: any) => {
    try {
      const params = {
        standardId: form.value.standardName,
      };
      getAttributeStandard(params).then((res) => {
        const data = res?.data?.childList || [];
        attributeStandard.value = cloneDeep(data);
      });
    } catch (error) {
      console.log(error);
    }
  };

  // 获取精细度
  const precision = ref();

  // 递归提取 content 属性
  const extractContentRecursive = (list: any[]) => {
    const result: any[] = [];
    const traverse = (nodes: any[]) => {
      nodes.forEach((node: { content: any; children: any }) => {
        if (node.content) {
          result.push(node.content);
        }
        if (Array.isArray(node.children)) {
          traverse(node.children);
        }
      });
    };

    traverse(list);
    return result;
  };

  const finenessList = ref(); // 精细度列表
  // 获取精细度列表
  const getFinenessList = async () => {
    try {
      const params = {
        code: 'stdPrecision',
        langCode: 'zh-CN',
      };
      const res = await getPrecision(params);

      if (res.status && Array.isArray(res.data) && res.data.length > 0) {
        const root = res.data[0];
        const firstLevelContents = root.children?.map((item) => item.content);
        finenessList.value = [...firstLevelContents];
      }
    } catch (err) {
      console.log(err);
    }
  };

  // 处理精细度列表
  const handleFinenessList = () => {
    if (Array.isArray(finenessList.value) && finenessList.value.length > 0) {
      const targetItem = standardName.value.find(
        (item: { id: string }) => item.id === form.value.standardName
      );
      const selectedAccuracy = targetItem ? targetItem.accuracy : '';

      precision.value = selectedAccuracy
        ? finenessList.value.filter((item) =>
            selectedAccuracy.split(',').includes(item.code)
          )
        : [];
    }
  };

  // 标准名称选择事件
  const standardNameChange = (value: any) => {
    queryAttributeStandard(); // 获取属性标准
    form.value.scene = ''; // 重置属性标准表单
    form.value.precision = ''; // 重置精细度表单
    handleFinenessList(); // 处理精细度列表
  };

  onMounted(() => {
    queryStandardName(); // 获取标准名称
    getFinenessList(); // 获取精细度列表
  });
</script>

<script lang="ts">
  export default {
    name: 'InitiateValidation',
    inheritAttrs: false,
  };
</script>

<style lang="less" scoped></style>
