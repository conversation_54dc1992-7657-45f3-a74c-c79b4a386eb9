<template>
  <div class="process-list">
    <!-- 新增标题栏 -->
    <a-row class="title-bar" justify="space-between" align="center">
      <a-col :span="12">
        <div class="title-left">
          <img
            src="@/assets/images/table-title.png"
            class="title-img icon"
            alt=""
            style="width: 20px; height: 20px"
          />
          <span class="title-text">{{
            $t('project-setting.process-list')
          }}</span>
        </div>
      </a-col>
      <a-col :span="12" style="text-align: right">
        <a-space :size="16">
          <a-input
            v-model="searchParams.name"
            :placeholder="$t('project-setting.please-enter-name')"
            :style="{ width: '240px' }"
            allow-clear
            @press-enter="getList"
            @clear="handleClear"
          />
          <a-button
            v-permission="`${canManageTeamId}_${$btn.project.addProcess}`"
            type="primary"
            @click="addProcess"
          >
            <template #icon>
              <icon-plus />
            </template>
            {{ $t('project-setting.add-process-template') }}
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <div class="table-wrap">
      <a-table
        :data="projectList"
        stripe
        row-key="id"
        :bordered="false"
        :scroll="{ x: '100%', y: '100%' }"
        :pagination="pageConfig"
        @page-change="pageChange"
        @page-size-change="pageSizeChange"
      >
        <template #columns>
          <a-table-column :title="$t('project-setting.index')" :width="60">
            <template #cell="{ rowIndex }">
              {{
                (pageConfig.pageSize ? pageConfig.pageSize : 10) *
                  ((pageConfig.current ? pageConfig.current : 1) - 1) +
                (rowIndex + 1)
              }}
            </template>
          </a-table-column>
          <a-table-column :title="$t('project-setting.process-template-name')">
            <template #cell="{ record }">
              <span class="name" @click="showRow(record)">{{
                record.name
              }}</span>
            </template>
          </a-table-column>
          <a-table-column :title="$t('project-setting.team-name')">
            <template #cell="{ record }">
              <span>{{
                record.teamId !== 0
                  ? record.teamName
                  : $t('project-setting.general')
              }}</span>
            </template>
          </a-table-column>
          <a-table-column :title="$t('project-setting.process-status')">
            <template #cell="{ record }">
              <a-tag v-if="record.deployStatus === 1" color="green">{{
                $t('project-setting.published')
              }}</a-tag>
              <a-tag v-if="record.deployStatus === 0">{{
                $t('project-setting.unpublished')
              }}</a-tag>
            </template>
          </a-table-column>
          <a-table-column :title="$t('project-setting.node-number')">
            <template #cell="{ record }">
              {{
                (record.processUser
                  ? JSON.parse(record.processUser).length
                  : 0) || 0
              }}
            </template>
          </a-table-column>
          <a-table-column
            :title="$t('project-setting.process-template-description')"
            data-index="describe"
          ></a-table-column>
          <a-table-column
            :title="$t('project-setting.updater')"
            data-index="creater"
          ></a-table-column>
          <a-table-column
            :title="$t('project-setting.update-time')"
            data-index="updateDate"
            :width="180"
          ></a-table-column>
          <a-table-column
            :title="$t('project-setting.operation')"
            align="center"
            :width="220"
          >
            <template #cell="{ record }">
              <div class="operator">
                <a-popconfirm
                  v-if="record.deployStatus !== 1"
                  :content="$t('project-setting.is-release-process-templatee')"
                  @ok="publishRow(record)"
                >
                  <a-button
                    :disabled="record.deployStatus === 1"
                    type="text"
                    size="small"
                    >{{ $t('project-setting.release') }}</a-button
                  >
                </a-popconfirm>
                <a-button
                  v-permission="`${record.teamId}_${$btn.project.editProcess}`"
                  type="text"
                  size="small"
                  @click="editRow(record)"
                  >{{ $t('project-setting.edit') }}</a-button
                >
                <a-popconfirm
                  :content="$t('project-setting.is-delete-process-template')"
                  type="error"
                  @ok="delRow(record)"
                >
                  <a-button
                    v-permission="
                      `${record.teamId}_${$btn.project.deleteProcess}`
                    "
                    type="text"
                    status="danger"
                    size="small"
                    >{{ $t('project-setting.delete') }}</a-button
                  >
                </a-popconfirm>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <div class="components">
      <CreateProcess
        v-model:visible="createDialogVisible"
        :type="dialogType"
        :default-process="currentProcess"
        @submit="getList"
        @update="getList"
      ></CreateProcess>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref } from 'vue';
  import { PaginationProps, Message } from '@arco-design/web-vue';
  import { IconPlus } from '@arco-design/web-vue/es/icon';
  import { useRoute } from 'vue-router';
  import CreateProcess from './create-process.vue';
  import { getProcessList, deployProcess, delProcess } from './api';
  import { usePrjPermissionStore } from '@/store/index';

  const projectStore = usePrjPermissionStore();

  const route = useRoute();
  const searchParams = reactive({
    name: '',
  });

  const canManageTeamId = computed(() => {
    const canManageTeams = projectStore.teamList.filter(
      (item) => item.role === 5
    );
    if (canManageTeams.length) {
      return canManageTeams[0].id;
    } else {
      return '';
    }
  });
  const projectList = ref<object[]>([]);
  // for (let i = 0; i < 50; i += 1) {
  //   projectList.value.push({
  //     id: i,
  //     name: '测试项目',
  //     code: 111,
  //     type: '隧道',
  //     createTime: '2023-05-06',
  //     creator: 'chenming',
  //   });
  // }
  const pageConfig: PaginationProps = reactive({
    showTotal: true,
    showMore: false,
    showJumper: true,
    showPageSize: true,
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    total: 100,
  });

  const getList = () => {
    const params = {
      pageNo: pageConfig.current || 1,
      pageSize: pageConfig.pageSize || 20,
      projectId: route.params.projectId,
      name: searchParams.name || '',
    };
    getProcessList(params)
      .then((res: any) => {
        if (res.code === 8000000) {
          projectList.value = res.data.list || [];
          pageConfig.total = res.data.total || projectList.value.length || 0;
        } else {
          projectList.value = [];
        }
      })
      .catch((e) => {
        if (e) {
          projectList.value = [];
        }
      });
  };

  // Function to clear search and refresh list
  const handleClear = () => {
    searchParams.name = '';
    getList();
  };

  const createDialogVisible = ref(false);
  const dialogType = ref(0);
  const currentProcess = ref({});
  const editRow = (row: any) => {
    dialogType.value = 1;
    currentProcess.value = row;
    createDialogVisible.value = true;
  };
  const showRow = (row: any) => {
    dialogType.value = 2;
    currentProcess.value = row;
    createDialogVisible.value = true;
  };
  const addProcess = () => {
    dialogType.value = 0;
    currentProcess.value = {};
    createDialogVisible.value = true;
  };
  const delRow = (row: any) => {
    delProcess(row.id).then((res: any) => {
      if (res.code === 8000000) {
        Message.success('删除成功！');
        getList();
      }
    });
  };
  // 部署流程
  const publishRow = (row: any) => {
    const params = {
      modelId: row.modelId,
      modelKey: row.modelKey,
    };
    if (!params.modelKey || !params.modelId) {
      Message.error('modelKey 或 modelId 缺失！');
    } else {
      deployProcess(params).then((res: any) => {
        if (res.code === 8000000) {
          Message.success('发布成功！');
          getList();
        }
      });
    }
  };
  const pageSizeChange = (size: number): void => {
    pageConfig.pageSize = size;
    getList();
  };
  const pageChange = (current: number): void => {
    pageConfig.current = current;
    getList();
  };

  const init = async () => {
    getList();
  };
  onMounted(() => {
    init();
  });
</script>

<style scoped lang="less">
  // 容器样式
  .process-list {
    height: calc(100vh - 200px);
  }
  :deep(.arco-input-wrapper) {
    background-color: transparent !important;
    border-radius: 4px;
    border: 1px solid #c9cdd4;

    &:focus,
    &:hover {
      border-color: rgb(var(--primary-6));
    }
  }

  // 标题区域样式
  .title-left {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: bold;
    color: var(--color-text-1);
    line-height: 21px;

    .icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }

  // 表格容器
  .content {
    margin-top: 24px;
    height: 100%;

    .table-wrap {
      margin-top: 18px;
      height: calc(100% - 50px);
      position: relative;

      // 表格内容区域
      :deep(.arco-table-wrapper) {
        background-color: transparent;
        height: calc(100% - 64px);
        overflow: auto;
        &:focus,
        &:hover {
          border-color: rgb(var(--primary-6));
        }
      }

      // 分页器定位
      :deep(.arco-table-pagination) {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 100%;
        background: #fff;
        padding: 16px;
      }
    }
  }

  // 通用文字样式
  .name {
    color: rgb(var(--arcoblue-6));
    cursor: pointer;
  }
</style>
