<template>
  <a-form
    ref="loginForm"
    :model="userInfo"
    class="login-form"
    layout="vertical"
    @submit="handleSubmit"
  >
    <a-form-item hide-label>
      <div class="company-invite-text">
        {{
          $t('login.form.invite.companyConfirm', {
            companyName: userInfo.companyName,
          })
        }}
      </div>
    </a-form-item>

    <a-form-item
      field="phone"
      :rules="[{ required: true, message: $t('login.form.telRequired') }]"
      :validate-trigger="['change', 'blur']"
      hide-label
    >
      <a-input
        v-model="userInfo.phone"
        disabled
        :placeholder="$t('login.form.telPlaceholder')"
      >
        <template #prefix>
          <icon-user :size="20" />
        </template>
      </a-input>
    </a-form-item>

    <a-form-item
      field="password"
      :rules="[{ required: true, message: $t('login.form.password.errMsg') }]"
      :validate-trigger="['change', 'blur']"
      hide-label
    >
      <a-input-password
        v-model="userInfo.password"
        :placeholder="$t('login.form.password.placeholder')"
        allow-clear
      >
        <template #prefix>
          <icon-lock :size="20" />
        </template>
      </a-input-password>
    </a-form-item>

    <a-form-item
      v-if="checked"
      field="captcha_code"
      :rules="[{ required: true, message: $t('login.form.captchaRequired') }]"
      :validate-trigger="['change', 'blur']"
      hide-label
    >
      <a-input
        v-model="userInfo.captcha_code"
        :placeholder="$t('login.form.captchaPlaceholder')"
        :maxlegth="50"
      >
        <template #prefix>
          <icon-safe :size="20" />
        </template>
        <template #append>
          <a-button type="text" :loading="smsLoading" @click="getSMSCaptcha">
            <span v-if="countDown === -2" class="captcha-word">{{
              $t('login.form.sendCaptcha')
            }}</span>
            <span v-else-if="countDown === -1" class="captcha-word">{{
              $t('login.form.regainCaptcha')
            }}</span>
            <span v-else class="captcha-word">{{ `${countDown}s` }}</span>
          </a-button>
        </template>
      </a-input>
    </a-form-item>

    <a-space :size="16" direction="vertical">
      <a-button
        type="primary"
        html-type="submit"
        long
        :loading="loading"
        style="
          font-size: 16px;
          height: 48px;
          border-radius: 8px;
          margin-top: 32px;
        "
      >
        {{ $t('login.form.joinCompany') }}
      </a-button>
      <div
        class="cancel-text"
        @click="emit('changeLogin', LoginMethods.password)"
      >
        {{ $t('login.form.cancel') }}
      </div>
    </a-space>
  </a-form>
  <AccountSwitcherModal
    v-model:showModal="showModal"
    :should-load="true"
    @select="handleAccountSelected"
  />
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import useLoading from '@/hooks/loading';
  import pwdEncrypt from '@/utils/encryption/pwd';
  import checkPassWordFormat from '@/utils/password-validation';
  import LoginMethods from '../constant';
  import { joinCompanyInfo } from '../api';
  import { companyJoinCompanyAndOrg } from '@/views/user-center/api';
  import { getSms, loginDualVerification } from '@/api/user';
  import { useUserStore, useGlobalModeStore } from '@/store';
  import { dotToSlash } from '@/utils/index';
  import AccountSwitcherModal from '@/components/AccountSwitcherModal/AccountSwitcherModal.vue';

  const props = defineProps<{
    invitationId?: string;
  }>();

  const emit = defineEmits<{
    (e: 'changeLogin', method: LoginMethods): void;
  }>();
  const router = useRouter();
  const { t } = useI18n();
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();
  const globalModeStore = useGlobalModeStore();

  const userInfo = reactive({
    phone: '',
    userName: '',
    companyId: '',
    companyName: '',
    companyUserType: '',
    orgNo: [],
    password: '',
    captcha_code: '',
  });

  // 验证码相关状态
  const countDown = ref(-2);
  const smsLoading = ref(false);
  const captchaKey = ref('');
  const checked = ref(false);
  const showModal = ref(false);

  // 获取邀请信息
  const getInvitationInfo = async () => {
    try {
      const { status, data } = await joinCompanyInfo({
        invitationId: props.invitationId,
      });
      if (status) {
        userInfo.phone = data.phone;
        userInfo.userName = data.userName;
        userInfo.companyId = data.companyId;
        userInfo.companyName = data.companyName;
        userInfo.companyUserType = data.companyUserType;
        userInfo.orgNo = data.orgNo;
      }
    } catch (err) {
      if (typeof err === 'string') {
        Message.error(err);
      }
    }
  };

  // 更新验证码倒计时
  const updateCountDown = () => {
    countDown.value = 60;
    const counter = setInterval(() => {
      if (countDown.value === 0) {
        clearInterval(counter);
        countDown.value = -1;
      } else {
        countDown.value--;
      }
    }, 1000);
  };

  // 获取验证码
  const getSMSCaptcha = async () => {
    if (countDown.value >= 0) {
      Message.warning(t('login.form.captchaHoldOn'));
      return;
    }

    try {
      const res = await getSms(userInfo.phone);
      captchaKey.value = res.data;
      updateCountDown();
    } catch (err) {
      if (typeof err === 'string') {
        Message.error(err);
      }
    }
  };

  // 验证账号密码
  const checkPassword = async (values: Record<string, any>) => {
    try {
      const res: any = await loginDualVerification({
        username: values.userName,
        password: pwdEncrypt(values.password),
      });
      if (res.status && res.data) {
        checked.value = true;
        userInfo.phone = res.data.phone;
        await getSMSCaptcha();
      }
    } catch (error: any) {
      if (error.data?.code === -8020200) {
        Message.error(t('login.form.password-risk'));
      }
    }
  };

  // 处理账号选择
  const handleAccountSelected = ({
    account,
    isSameScene,
  }: {
    account: any;
    isSameScene: boolean;
  }) => {
    const { redirect, ...othersQuery } = router.currentRoute.value.query;
    if (!account.id) {
      globalModeStore.changeGlobalMode('work');
      router
        .push({
          path: '/dashboard',
          query: {
            ...othersQuery,
          },
        })
        .then(() => {
          showModal.value = false;
        })
        .catch((err) => {
          console.error('路由跳转失败', err);
          showModal.value = false;
          Message.error(t('login.navigation.failed'));
        });
    } else {
      const targetPath = isSameScene
        ? dotToSlash(redirect as string) || '/home-page'
        : '/home-page';

      router
        .push({
          path: targetPath,
          query: {
            ...othersQuery,
          },
        })
        .then(() => {
          showModal.value = false;
        })
        .catch((err) => {
          console.error('路由跳转失败', err);
          showModal.value = false;
          Message.error(t('login.navigation.failed'));
        });
    }
  };

  // 表单提交
  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (loading.value) return;
    if (!errors) {
      setLoading(true);
      try {
        // 验证密码格式
        if (!checkPassWordFormat(values.password)) {
          Message.error(t('userSetting.password-rule-error'));
          return;
        }

        if (!checked.value) {
          await checkPassword(values);
          return;
        }

        // 登录
        const loginData = {
          username: userInfo.phone,
          captcha_key: captchaKey.value,
          captcha_code: values.captcha_code,
          grant_type: 'sms_captcha',
          scope: 'all',
        };
        const res = await userStore.login(loginData);
        userStore.addToProject();

        // 加入公司
        const joinCompanyData = {
          companyId: userInfo.companyId,
          companyUserType: userInfo.companyUserType,
          orgNoList: userInfo.orgNo,
          companyMembers: [userInfo.userName],
        };
        await companyJoinCompanyAndOrg(joinCompanyData);

        // 打开账号切换弹窗
        showModal.value = true;
        Message.success(res.message);
      } catch (err) {
        if (typeof err === 'string') {
          Message.error(err);
        }
      } finally {
        setLoading(false);
      }
    }
  };

  onMounted(() => {
    if (props.invitationId) {
      getInvitationInfo();
    }
  });
</script>

<style lang="less" scoped>
  :deep(.arco-btn-size-large) {
    height: 48px;
    border-radius: 8px;
  }
  :deep(.arco-input-wrapper) {
    height: 40px;
    border: 1px solid #c9cdd4;
    background-color: #ffffff;
    border-radius: 8px !important;
  }
  .login-form {
    height: 340px;
    margin-top: 16px;
  }
  .company-invite-text {
    width: 100%;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #3366ff;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    background: #e8f2ff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #3366ff;
    padding: 10px; /* 添加一些内边距，使文本不紧贴边框 */
  }
  .cancel-text {
    width: 100%;
    cursor: pointer;
    height: 32px;
    font-family: Source Han Sans CN, Source Han Sans CN;

    font-weight: 400;
    font-size: 16px;
    color: #3366ff;
    line-height: 32px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
</style>

<script lang="ts">
  export default {
    name: 'InviteForm',
  };
</script>
