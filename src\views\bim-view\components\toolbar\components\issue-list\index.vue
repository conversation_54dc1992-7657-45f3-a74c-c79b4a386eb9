<template>
  <div class="issue-list-box">
    <a-card
      ref="cardList"
      :title="
        meetingId
          ? $t('model-viewer.meetingIssueListTitle')
          : $t('model-viewer.modelIssueListTitle')
      "
      class="list-card"
    >
      <template #extra>
        <icon-close class="icon-close" @click="closeList" />
      </template>
      <div class="issue-filter-box">
        <div class="header">
          <icon-search style="margin-right: 8px" />
          <a-input
            v-model="filterForm.title"
            :placeholder="$t('model-viewer.issueFilterTitle')"
          />
          <icon-filter
            style="margin-left: 16px"
            @click="isShowFilter = !isShowFilter"
          />
        </div>
        <div v-show="isShowFilter" class="body">
          <a-form :model="filterForm" :style="{ width: '360px' }">
            <a-form-item field="name" :label="$t('model-viewer.issueStatus')">
              <a-select
                v-model="filterForm.state"
                :style="{ width: '320px' }"
                :placeholder="$t('model-viewer.selectAll')"
              >
                <a-option :value="-1">{{
                  $t('model-viewer.selectAll')
                }}</a-option>
                <a-option :value="0">{{
                  $t('model-viewer.unresolved')
                }}</a-option>
                <a-option :value="1">{{
                  $t('model-viewer.resolved')
                }}</a-option>
                <a-option :value="2">{{
                  $t('model-viewer.inProgress')
                }}</a-option>
              </a-select>
            </a-form-item>

            <a-form-item
              style="padding-top: 10px"
              field="createUser"
              :label="$t('model-viewer.createUser')"
            >
              <a-select
                v-model="filterForm.createUser"
                :style="{ width: '320px' }"
                :default-value="'0'"
                :placeholder="$t('model-viewer.searchPlaceholder')"
              >
                <a-option :value="'all'">{{
                  $t('model-viewer.allUsers')
                }}</a-option>
                <a-option
                  v-for="item in dataSource"
                  :key="`${item[1]}-${item[0]}`"
                  :value="item[1]"
                  :label="item[0]"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-form>
        </div>
      </div>
      <div class="issue-card-box">
        <issue-card
          v-for="item in issueList"
          :id="item.id"
          :key="item.id"
          :shadow="item.id === shadowKey"
          :issue-detail="item"
          :viewer="viewer"
          :issue-reply-model="issueReplyModel"
          v-bind="$attrs"
          @set-issue-reply-model="setIssueReplyModel"
          @set-shadow-key="shadowKey = item.id"
          @show-reply="showReply"
          @set-reply-issue="(val:IssuesDetail)=>(replyIssue=val)"
          @set-issue-reply-data="setIssueReplyData"
        ></issue-card>
      </div>
      <template #actions>
        <div class="footer">
          <a-button
            type="outline"
            style="margin-right: 10px"
            @click="GenerateIssueXML(issueList)"
            >{{ $t('model-viewer.issueXMLDownload') }}</a-button
          >
          <a-button type="primary" @click="showCreateIssue">{{
            $t('model-viewer.createIssue')
          }}</a-button>
        </div>
      </template>
    </a-card>
    <create-issue
      v-show="createIssueToolActive && route.name === 'bimView'"
      :project-id="projectId"
      :file-id="modelId"
      :viewer="viewer"
      @issue-list-reload="init"
      @close="close"
    ></create-issue>
    <issue-reply
      v-if="issueReplyActive && replyIssue"
      :current-issue="replyIssue"
      :issue-reply-model="issueReplyModel"
      :issue-reply-data="issueReplyData"
      @issue-list-reload="init"
      @close="close"
    ></issue-reply>
  </div>
</template>

<script lang="ts" setup>
  import {
    ref,
    reactive,
    watch,
    onUnmounted,
    defineEmits,
    nextTick,
  } from 'vue';
  import { useRoute } from 'vue-router';
  // import useModelToolsStore from '@/store/modules/model-viewer/index';
  // import { getViewer } from '@/utils/modelViewer/index';
  import { useDebounceFn } from '@vueuse/core';
  import issueCard from './issue-card.vue';
  import createIssue from '../create-issue/index.vue';
  import IssueReply from './issue-reply.vue';
  import { queryissuesList, IssuesDetail } from './api';
  import { GenerateIssueXML } from '@/components/bcf/index';
  // import { storeToRefs } from 'pinia';
  import useModelToolsStore from '@/store/modules/model-viewer/index';

  const store = useModelToolsStore();
  const emits = defineEmits(['close']);
  const route = useRoute();
  // const toolStore = storeToRefs(useModelToolsStore());
  // const modelToolsStore = useModelToolsStore();
  // const props = defineProps<{ projectId: string }>();
  const meetingId = route.query.conferenceId as string;
  const projectId = route.query.projectId as string;
  const replyIssue = ref<IssuesDetail>();
  const issueList = ref<IssuesDetail[]>([]);
  const filterForm = reactive({
    title: '',
    state: -1,
    createUser: 'all',
  });

  const isShowFilter = ref<boolean>(false);
  const shadowKey = ref<string>();

  const props = defineProps({
    viewer: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  const createIssueToolActive = ref(false);
  const issueReplyActive = ref(false);
  const issueReplyModel = ref('view');
  const issueReplyData = ref<any>({});
  const setIssueReplyData = (value: any) => {
    issueReplyData.value = value;
  };
  const setIssueReplyModel = (mode: any) => {
    issueReplyModel.value = mode;
  };
  // 大象云实例
  console.log(props.viewer);

  const closeList = () => {
    try {
      props.viewer?.clearMarker(); // 移除所有标签
    } catch (error) {}
    // props.viewer?.stopListenToAddMarker();
    // modelToolsStore.setIssueModal(false, '');
    // modelToolsStore.setCreateIssueModal(false);
    createIssueToolActive.value = true;
    emits('close');
  };

  const cardList = ref<any>();
  const changeShadowKey = (value: string) => {
    shadowKey.value = value;
    document.getElementById(value)?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'center',
    });
  };

  const locate = (value: string) => {
    const index = issueList.value.findIndex((item: IssuesDetail) => {
      return item.id === value;
    });
    shadowKey.value = value;
    document.getElementById(value)?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'center',
    });
    const { viewPoint } = JSON.parse(
      issueList.value[index].issueFileItems[0].dimensionString
    );
    props.viewer?.setViewMessage(viewPoint);
  };

  const drawIssueIcon = (issues: IssuesDetail[]) => {
    console.log('[ issues ] >', issues);
    const result: any[] = []; // 存放所有三维坐标点
    issues.forEach((item) => {
      if (item.issueFileItems && item.issueFileItems.length > 0) {
        item.issueFileItems.forEach((fileItem) => {
          if (fileItem.dimensionString) {
            try {
              const dimension = JSON.parse(fileItem.dimensionString);
              console.log('[ dimension ] >', dimension);
              if (dimension.nodeInfo) {
                const { position, point } = dimension.nodeInfo;
                let x; // x坐标
                let y; // y坐标
                let z; // z坐标
                if (position) {
                  [x, y, z] = position;
                } else if (point) {
                  x = point.x;
                  y = point.y;
                  z = point.z;
                }
                result.push({ x, y, z });
              }
            } catch (e) {
              console.error('Failed to parse dimensionString:', e);
            }
          }
        });
      }
    });
    result.forEach(async (position) => {
      try {
        props.viewer?.create3DMarker([position.x, position.y, position.z]);
      } catch (error) {}
    });

    // props.viewer?.clearMarker();
    // props.viewer?.addMarker(issues);
    // props.viewer?.resize();
    // props.viewer?.onMarkerClick(issues, changeShadowKey);
  };

  const dataList = ref<any>([]);
  const dataSource = ref<any>([]);
  const createrData = ref<any>([]);
  const createByData = ref<any>([]);

  const init = useDebounceFn(async () => {
    const { idStr }: any = route.query;
    try {
      createIssueToolActive.value = false;
      const res = await queryissuesList({
        fileId: idStr,
        projectId,
        title: filterForm.title,
        state: filterForm.state === -1 ? undefined : filterForm.state,
        pageNo: 1,
        pageSize: 9999,
        createUser: filterForm.createUser,
      });
      if (res.status) {
        // console.log('[ 模型问题列表 ] >', res);
        // 问题图片url拼接
        res.data.list.forEach((item: IssuesDetail) => {
          item.picUrl = item.issueFileItems[0]
            ? `/api/sys-storage/download_image?f8s=${item.issueFileItems[0].picToken}`
            : '';
        });
        if (route.query.conferenceId) {
          issueList.value = res.data.list.filter(
            (item: IssuesDetail) => item.mettingId === route.query.conferenceId
          );
          dataList.value = issueList.value;
        } else {
          issueList.value = res.data.list;
          dataList.value = res.data.list;
        }

        // 画issue点
        await drawIssueIcon(issueList.value);
        // if (modelToolsStore.issueId) locate(modelToolsStore.issueId);

        // 查询创建人
        dataSource.value = [];
        dataList.value.forEach((item: any) => {
          createrData.value.push(item.creater);
          createrData.value = Array.from(new Set(createrData.value));
          createByData.value.push(item.createBy);
          createByData.value = Array.from(new Set(createByData.value));
        });
        for (let i = 0; i < createrData.value.length; i++) {
          dataSource.value.push([createrData.value[i], createByData.value[i]]);
        }
      }
    } catch (e) {
      // props.viewer?.resize();
    }
  }, 500);

  init();

  watch(filterForm, () => {
    init();
  });
  const close = () => {
    createIssueToolActive.value = false;
    issueReplyActive.value = false;
  };

  /**
   * 设置鼠标样式
   */
  const setMouseStyle = () => {
    const element = document.getElementById('Xbase-viewer');
    if (element) {
      element.style.cursor = 'crosshair';
    }
  };

  const showCreateIssue = () => {
    // modelToolsStore.setCreateIssueModal(true);
    // modelToolsStore.setIssueReplyModal(false);
    createIssueToolActive.value = true;
    issueReplyActive.value = false;
    store.setAlertShow(true);
    setMouseStyle();
  };
  const showReply = () => {
    createIssueToolActive.value = false;
    issueReplyActive.value = true;
  };
  const { idStr }: any = route.query;
  const modelId = ref(idStr);
  onUnmounted(() => {
    // props.viewer?.resize();
    // props.viewer?.clearObjectsColor();
  });

  defineExpose({
    init,
  });
</script>

<style lang="less" scoped>
  @media (max-width: 600px) {
    .issue-list-box {
      width: 100%;
      height: 62vh;
      z-index: 100;
      position: absolute;
      bottom: 0;
      overflow: hidden;
      box-shadow: 0 0 8px rgb(0 0 0 / 15%);
      flex-shrink: 0;
    }
  }
  @media (min-width: 601px) {
    .issue-list-box {
      width: 400px;
      overflow: hidden;
      box-shadow: 0 0 8px rgb(0 0 0 / 15%);
      flex-shrink: 0;
    }
  }
  .issue-list-box {
    // width: 400px;
    // overflow: hidden;
    // box-shadow: 0 0 8px rgb(0 0 0 / 15%);
    // flex-shrink: 0;
    //border: 1px solid red;
    height: 100%;
    .list-card {
      height: 100%;
      border-top: 0;
      border-bottom: 0;
    }
    .issue-filter-box {
      width: 100%;
      font-size: 20px;
      border-bottom: 1px solid var(--color-neutral-3);
      svg {
        cursor: pointer;
      }
      .header {
        display: flex;
        align-items: center;
        padding: 8px 12px;
      }
      .body {
        padding: 8px 12px;
        :deep(.arco-form-item) {
          margin-bottom: 0;
        }
      }
    }
    .icon-close {
      cursor: pointer;
    }
    .issue-card-box {
      height: 100%;
      overflow-y: scroll;
      padding: 16px 0 16px 12px;
      flex: 1;
    }
    :deep(.list-card > .arco-card-body) {
      height: calc(100% - 46px);
      padding: 0;
      display: flex;
      flex-direction: column;
    }
    :deep(.arco-card + .arco-card) {
      margin-top: 12px;
    }
    :deep(.arco-card-actions) {
      border-top: 1px solid var(--color-neutral-3);
      margin-top: 0;
      padding: 8px 12px;
    }
  }
</style>
