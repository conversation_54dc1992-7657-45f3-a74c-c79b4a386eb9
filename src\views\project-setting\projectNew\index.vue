<template>
  <div class="container">
    <a-row justify="space-between" align="center" class="title-bar title">
      <a-col :span="5">
        <div class="title-left">
          <img class="icon" src="@/assets/images/table-title.png" />
          <span v-if="formData.isTemplate + '' === '0'">{{
            $t('project-setting.project-information')
          }}</span>
          <span v-else>{{
            $t('project-setting.project-template-information')
          }}</span>
        </div>
      </a-col>
      <a-col v-if="userAdmin === '0' || isPrjAdmin === 1" :span="8" class="btn">
        <a-button
          v-if="isReadonly"
          v-permission="$btn.project.editProject"
          type="primary"
          @click="changeReadonly(false)"
        >
          {{ $t('project-setting.edit') }}</a-button
        >
        <a-space v-else>
          <a-button type="outline" @click="handelCancel">{{
            $t('project-setting.cancel')
          }}</a-button>
          <a-button type="primary" @click="handleOk">{{
            $t('project-setting.ok')
          }}</a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-form
      ref="projectRef"
      :model="formData"
      :disabled="isReadonly"
      :style="{ width: '100%' }"
      auto-label-width
      layout="vertical"
    >
      <div class="top-box">
        <a-row :gutter="20" justify="start">
          <a-col :span="8">
            <a-form-item
              field="name"
              :label="
                formData.isTemplate + '' === '0'
                  ? $t('project-setting.project-name')
                  : $t('project-setting.template-name')
              "
              label-col-flex="auto"
              :rules="[
                {
                  required: true,
                  message: $t('project-setting.project-name-errMsg'),
                },
                {
                  maxLength: 255,
                  message: $t('project-setting.project-name-length-errMsg'),
                },
              ]"
              validate-trigger="input"
            >
              <remove-spaces-input
                v-model="formData.name"
                :placeholder="isReadonly ? '' : $t('please-enter')"
                :max-length="currentLocale === 'en-US' ? 255 : 100"
                show-word-limit
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              field="code"
              :label="$t('project-setting.project-code')"
              label-col-flex="auto"
              :rules="[
                {
                  required: true,
                  message: $t('project-setting.project-code-errMsg'),
                },
                {
                  match: /^[A-Za-z0-9]+$/,
                  message: $t(
                    'project-setting.project-code-english-number-errMsg'
                  ),
                },
                {
                  maxLength: 255,
                  message: $t('project-setting.project-name-length-errMsg'),
                },
              ]"
              validate-trigger="input"
            >
              <a-input
                v-model="formData.code"
                :placeholder="isReadonly ? '' : $t('please-enter')"
                :max-length="currentLocale === 'en-US' ? 255 : 100"
                show-word-limit
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item
              field="position"
              :label="$t('project-setting.project-location')"
              label-col-flex="auto"
              :rules="[
                {
                  required: true,
                  message: $t('project-setting.project-location-errMsg'),
                },
              ]"
              validate-trigger="input"
            >
              <a-input
                v-model="formData.position"
                :placeholder="isReadonly ? '' : $t('please-enter')"
                :max-length="currentLocale === 'en-US' ? 255 : 100"
                show-word-limit
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="20" justify="start">
          <a-col :span="8">
            <a-form-item
              field="type"
              :label="$t('project-setting.project-type')"
              validate-trigger="input"
              label-col-flex="auto"
            >
              <a-select
                v-model="formData.type"
                :show-extra-options="false"
                :fallback-option="false"
                :placeholder="isReadonly ? '' : $t('please-select')"
              >
                <a-option
                  v-for="type in projectTypes"
                  :key="type.value + type.label"
                  :value="type.value"
                  :label="type.label"
                ></a-option
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label-col-flex="auto"
              field="rangeDate"
              :label="$t('project-setting.project-time')"
              validate-trigger="input"
            >
              <a-range-picker
                v-model="formData.rangeDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="
                  isReadonly
                    ? ['', '']
                    : [$t('please-select'), $t('please-select')]
                "
                @change="handleDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label-col-flex="auto"
              field="participaUnit"
              :label="$t('dashboard.participating-units')"
              :validate-trigger="['change', 'input']"
            >
              <a-input
                v-model="formData.participaUnit"
                :placeholder="isReadonly ? '' : $t('please-enter')"
                :max-length="currentLocale === 'en-US' ? 255 : 100"
                show-word-limit
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="20" justify="start">
          <a-col :span="8">
            <a-form-item
              label-col-flex="auto"
              field="projectPhase"
              :label="$t('dashboard.project-phase')"
              :rules="[
                {
                  required: true,
                  message: $t('dashboard.project-phase-errMsg'),
                },
              ]"
            >
              <a-select
                v-model="formData.projectPhase"
                :show-extra-options="false"
                :fallback-option="false"
                :placeholder="isReadonly ? '' : $t('dashboard.please-select')"
                allow-clear
              >
                <a-option
                  v-for="item in projectPhase"
                  :key="`${item?.value}_${item?.label}`"
                  :value="item?.value"
                  :label="item?.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label-col-flex="auto"
              field="projectStatus"
              :label="$t('dashboard.project-status')"
            >
              <a-select
                v-model="formData.projectStatus"
                :show-extra-options="false"
                :fallback-option="false"
                :placeholder="isReadonly ? '' : $t('dashboard.please-select')"
                allow-clear
              >
                <a-option
                  v-for="item in projectStatus"
                  :key="`${item?.value}_${item?.label}`"
                  :value="item?.value"
                  :label="item?.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label-col-flex="auto"
              field="projectProperties"
              :label="$t('dashboard.project-properties')"
              :rules="[
                {
                  required: true,
                  message: $t('dashboard.project-pro-errMsg'),
                },
              ]"
            >
              <a-select
                v-model="formData.projectProperties"
                :show-extra-options="false"
                :fallback-option="false"
                :placeholder="isReadonly ? '' : $t('dashboard.please-select')"
                allow-clear
              >
                <a-option
                  v-for="item in projectProperties"
                  :key="`${item?.value}_${item?.label}`"
                  :value="item?.value"
                  :label="item?.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="20" justify="start">
          <a-col :span="8">
            <a-form-item
              label-col-flex="auto"
              field="application"
              :label="$t('project-setting.integrated-product-application')"
              validate-trigger="input"
              style="position: relative"
            >
              <a-tooltip position="bottom" background-color="#ccc">
                <template #content>
                  <div class="custom-wrap">
                    <ul class="custom-ul">
                      <li
                        v-for="item in formData.application"
                        :key="item"
                        class="custom-list"
                        >{{ selectIdtoLabel(item) }}</li
                      >
                    </ul>
                  </div>
                </template>
                <button
                  v-if="isReadonly && formData.application.length > 1"
                  class="expand-button"
                  >{{ $t('project-setting.application-expand') }}</button
                ></a-tooltip
              >
              <a-select
                v-model="formData.application"
                :placeholder="isReadonly ? '' : $t('dashboard.please-select')"
                multiple
                :max-tag-count="1"
                :show-extra-options="false"
                :fallback-option="false"
                allow-search
                allow-clear
              >
                <a-option
                  v-for="item in integratedProductApplication"
                  :key="`${item?.value}_${item?.label}`"
                  :value="item?.value"
                  :label="item?.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <!-- 暂时隐藏项目模板 -->
          <!-- <a-col v-show="formData.isTemplate === 0" :span="8">
          <a-form-item
            field="protemName"
            :label="$t('project-setting.project-template')"
            validate-trigger="input"
            disabled
          >
            <a-input
              v-model="formData.protemName"
              :placeholder="$t('please-enter')"
            />
          </a-form-item>
        </a-col> -->
        </a-row>
        <a-row justify="start">
          <a-col :span="24">
            <a-form-item
              label-col-flex="auto"
              field="description"
              :label="$t('project-setting.project-overview')"
              validate-trigger="input"
            >
              <a-textarea
                v-model="formData.description"
                :auto-size="{
                  minRows: 2,
                  maxRows: 5,
                }"
                :placeholder="isReadonly ? '' : $t('please-enter')"
                :max-length="currentLocale === 'en-US' ? 2000 : 1000"
                show-word-limit
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      <list-title
        :title-text="$t('project-setting.design-information')"
        :show-button="false"
        class="design-title title"
      />

      <a-row :gutter="20" justify="start">
        <a-col :span="8">
          <a-form-item
            field="modelEngine"
            :label="$t('project-setting.model-engine')"
            label-col-flex="auto"
            :rules="[
              {
                required: true,
                message: $t('project-setting.model-engine-errMsg'),
              },
            ]"
            validate-trigger="input"
            :disabled="true"
          >
            <a-select
              v-model="formData.modelEngine"
              :show-extra-options="false"
              :fallback-option="false"
              :placeholder="isReadonly ? '' : $t('please-select')"
              :label="$t('project-setting.model-engine-errMsg')"
            >
              <a-option
                value="XBase"
                :label="$t('project-setting.X-Base-model-engine')"
              ></a-option>
              <!-- 护网结束前隐藏构力引擎选项 -->
              <!-- <a-option
                value="BimBase"
                :label="$t('project-setting.BIM-Base-model-engine')"
              ></a-option> -->
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            field="roadLevel"
            :label="$t('project-setting.road-level')"
            validate-trigger="input"
            label-col-flex="auto"
          >
            <a-select
              v-model="formData.roadLevel"
              :show-extra-options="false"
              :fallback-option="false"
              :placeholder="isReadonly ? '' : $t('please-select')"
            >
              <a-option
                v-for="type in projectRoadlevel"
                :key="type.value + type.label"
                :value="type.value"
                :label="type.label"
              ></a-option
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label-col-flex="auto"
            field="coordinate"
            :label="$t('project-setting.coordinate-system')"
            validate-trigger="input"
          >
            <a-select
              v-model="formData.coordinate"
              :show-extra-options="false"
              :fallback-option="false"
              :placeholder="isReadonly ? '' : $t('please-select')"
            >
              <a-option
                v-for="coordinate in projectCoordinate"
                :key="coordinate.value + coordinate.label"
                :value="coordinate.value"
                :label="coordinate.label"
              ></a-option
            ></a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="20" justify="start">
        <a-col :span="8">
          <a-form-item
            label-col-flex="auto"
            field="elevation"
            :label="$t('project-setting.elevation-datum')"
            validate-trigger="input"
          >
            <a-select
              v-model="formData.elevation"
              :show-extra-options="false"
              :fallback-option="false"
              :placeholder="isReadonly ? '' : $t('please-select')"
            >
              <a-option
                v-for="elevation in projectElevation"
                :key="elevation.value + elevation.label"
                :value="elevation.value"
                :label="elevation.label"
              ></a-option
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label-col-flex="auto"
            field="loadDesign"
            :label="$t('project-setting.design-load')"
            validate-trigger="input"
          >
            <a-select
              v-model="formData.loadDesign"
              :show-extra-options="false"
              :fallback-option="false"
              :placeholder="isReadonly ? '' : $t('please-select')"
            >
              <a-option
                v-for="type in projectDesignLoad"
                :key="type.value + type.label"
                :value="type.value"
                :label="type.label"
              ></a-option
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label-col-flex="auto"
            field="safetyLevel"
            :label="$t('project-setting.safety-level')"
            validate-trigger="input"
          >
            <a-select
              v-model="formData.safetyLevel"
              :show-extra-options="false"
              :fallback-option="false"
              :placeholder="isReadonly ? '' : $t('please-select')"
            >
              <a-option
                v-for="type in projectSecurityLevel"
                :key="type.value + type.label"
                :value="type.value"
                :label="type.label"
              ></a-option
            ></a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="20" justify="start">
        <a-col :span="8">
          <a-form-item
            label-col-flex="auto"
            field="environmentType"
            :label="$t('project-setting.environmental-category')"
            validate-trigger="input"
          >
            <a-select
              v-model="formData.environmentType"
              :show-extra-options="false"
              :fallback-option="false"
              :placeholder="isReadonly ? '' : $t('please-select')"
            >
              <a-option
                v-for="environmentType in projectEnvironmentType"
                :key="environmentType.value + environmentType.label"
                :value="environmentType.value"
                :label="environmentType.label"
              ></a-option
            ></a-select>
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item
            label-col-flex="auto"
            field="magnitude"
            :label="$t('project-setting.seismic-grade')"
            validate-trigger="input"
          >
            <a-select
              v-model="formData.magnitude"
              :show-extra-options="false"
              :fallback-option="false"
              :placeholder="isReadonly ? '' : $t('please-select')"
            >
              <a-option
                v-for="magnitude in projectMagnitude"
                :key="magnitude.value + magnitude.label"
                :value="magnitude.value"
                :label="magnitude.label"
              ></a-option
            ></a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import ListTitle from '@/components/list-title/index.vue';
  import { ref, onMounted, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { ProjectRecord, queryProjectDetail, updateProject } from './api';
  import {
    projectPhase,
    projectStatus,
    projectTypes,
    projectCoordinate,
    projectElevation,
    projectEnvironmentType,
    projectMagnitude,
    projectRoadlevel,
    projectDesignLoad,
    projectSecurityLevel,
    projectProperties,
    integratedProductApplication,
  } from '@/directionary/project';
  import { useI18n } from 'vue-i18n';
  import useProjectStore from '@/store/modules/project/index';
  import useLocale from '@/hooks/locale';
  import removeSpacesInput from '@/components/removeSpacesInput/index.vue';
  import { usePrjPermissionStore } from '@/store/index';

  const { currentLocale } = useLocale();

  // 是否只读
  const isReadonly = ref(true);
  const route = useRoute();
  const { t } = useI18n();
  const projectState = useProjectStore();

  const projectPermissionStore = usePrjPermissionStore();
  const userAdmin = localStorage.getItem('userAdmin'); // 0 系统管理员 -1 系统普通成员
  const isPrjAdmin = projectPermissionStore.projectAdmin; // 0 项目普通成员 1 项目管理员

  // 项目id
  const projectId = computed(() => {
    return String(route?.params?.projectId);
  });
  const emptyProject: ProjectRecord = {
    id: projectId.value,
    name: '',
    modelEngine: '',
    code: '',
    type: '',
    protemId: '',
    protemName: '',
    planStart: '',
    planEnd: '',
    description: '',
    rangeDate: [],
    isTemplate: '',
    position: '',
    coordinate: '',
    elevation: '',
    environmentType: '',
    magnitude: '',
    roadLevel: '',
    loadDesign: '',
    safetyLevel: '',
    projectStatus: '',
    projectPhase: '',
    participaUnit: '',
    projectProperties: '',
    application: [],
  };
  const formData = ref<ProjectRecord>(emptyProject);
  const tempFormData = ref<ProjectRecord>(emptyProject);
  const projectRef = ref<FormInstance>();

  // 编辑按钮点击事件
  // 切换表单只读状态
  const changeReadonly = (readonly: boolean) => {
    isReadonly.value = readonly;
  };

  // 处理日期范围选择变化
  const handleDateChange = (time: any) => {
    if (time && time.length === 2) {
      [formData.value.planStart, formData.value.planEnd] = time;
    } else {
      formData.value.planStart = '';
      formData.value.planEnd = '';
    }
  };

  // 将应用ID转换为显示标签
  const selectIdtoLabel = (val: string) => {
    const transitonLabel = integratedProductApplication.value.find(
      (item) => item.value === val
    );
    return transitonLabel?.label;
  };

  // 获取项目详情数据
  const getProjectDetail = async () => {
    console.log('2333333333333333333333333333333333333');
    const projectTempId = projectId.value;
    const res = await queryProjectDetail({
      // id: projectId.value,
      id: projectTempId,
    });
    let result = false;
    if (res.status) {
      const { data } = res;
      formData.value = { ...data };
      formData.value.application =
        data.application === null
          ? []
          : data.application?.split(',').filter((item: string) => item !== '');
      formData.value.rangeDate = [data.planStart, data.planEnd];
      tempFormData.value = { ...data };
      result = true;
    } else {
      Message.error(res.message);
    }
    return result;
  };

  // 取消编辑操作
  const handelCancel = async () => {
    await projectRef.value?.clearValidate();
    changeReadonly(true);
    getProjectDetail();
  };

  // 提交表单数据
  const handleOk = async () => {
    const res = await projectRef.value?.validate();
    if (!res) {
      const projectTempId = projectId.value;
      const params = {
        ...formData.value,
        id: projectTempId,
        application: formData.value.application?.toString() || '',
      };
      delete params.rangeDate;
      const ress = await updateProject(params);
      if (ress.status) {
        projectState.setProjectList({
          pageNo: 1,
          pageSize: 1000000,
          projectType: 0,
        });
        const resd = await getProjectDetail();
        if (resd) {
          Message.success(t('edit-successful'));
        }
        isReadonly.value = true;
      } else {
        Message.error(ress.message);
      }
    }
  };
  onMounted(() => {
    getProjectDetail();
  });
</script>

<style lang="less" scoped>
  :deep(.arco-input-wrapper),
  :deep(.arco-textarea-wrapper),
  :deep(.arco-select-view),
  :deep(.arco-picker) {
    background-color: transparent !important;
    border-radius: 4px;
    border: 1px solid #c9cdd4;

    &:focus,
    &:hover {
      border-color: rgb(var(--primary-6));
    }
  }

  :deep(.arco-input-wrapper),
  :deep(.arco-textarea-wrapper) {
    &:focus,
    &:hover {
      background-color: transparent !important;
    }
  }
  :deep(.arco-picker-input-active input) {
    background-color: transparent !important;
  }

  :deep(.arco-select-dropdown),
  :deep(.arco-picker-dropdown) {
    background-color: var(--color-bg-popup) !important;
  }

  :deep(.arco-select-option),
  :deep(.arco-picker-cell) {
    background-color: transparent !important;

    &:hover {
      background-color: var(--color-fill-2) !important;
    }
  }

  :deep(.arco-picker-cell-selected) {
    background-color: rgb(var(--primary-6)) !important;
    color: #fff !important;
  }

  .custom-wrap {
    max-height: 210px;
    overflow: auto;
    .custom-ul {
      list-style: none;
      font-size: 14px;
      padding: 0;
      // margin: 4px 0;
      margin: 0;
      .custom-list {
        // padding: 0 12px;
        white-space: nowrap;
        padding: 0 0;
        box-sizing: border-box;
        width: 100%;
        height: 30px;
        line-height: 30px;
        color: #1d2129;
      }
    }
  }
  /* 设置滚动条的宽度 */
  ::-webkit-scrollbar {
    width: 6px; /* 水平滚动条的高度 */
    height: 6px; /* 垂直滚动条的宽度 */
  }
  .expand-button {
    background: transparent;
    position: absolute;
    right: 0;
    color: #165dff;
    z-index: 100;
    font-size: 12px;
    border: 0px;
    cursor: pointer;
  }
  .container {
    height: calc(100vh - 156px);
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
  }
  .title {
    width: 100%;
    margin-bottom: 16px;
  }
  .design-title {
    margin-top: 20px;
  }
  :deep(.arco-row-nowrap) {
    margin-bottom: 12px;
  }
  :deep(.arco-picker-range) {
    width: 100%;
  }
  .title-bar {
    margin-bottom: 16px;

    :deep(button) {
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    }

    .title-left {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      color: #1d2129;
      line-height: 21px;

      .icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
    }

    .btn {
      text-align: right;
    }
  }
  .top-box {
    border-bottom: 1px solid #e5e6eb;
  }
</style>
