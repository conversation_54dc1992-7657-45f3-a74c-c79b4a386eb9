<template>
  <a-modal
    :visible="visible"
    :width="420"
    :title="
      type === 'add'
        ? t('knowledgenew.create-title')
        : t('knowledgenew.edit-title')
    "
    title-align="start"
    :ok-text="
      type === 'add' ? t('knowledgenew.create-btn') : t('knowledgenew.ok-text')
    "
    :mask-closable="false"
    :unmount-on-close="true"
    :ok-loading="btnLoading"
    :esc-to-close="false"
    class="create-base-dialog"
    @before-open="initData"
    @cancel="cancel"
    @ok="submitData"
  >
    <a-form ref="formRef" :model="formData" layout="vertical" :rules="rules">
      <a-form-item field="name" :label="t('knowledgenew.name-label')">
        <a-input
          v-model="formData.name"
          :placeholder="t('knowledgenew.input-placeholder')"
          :max-length="256"
        />
      </a-form-item>
      <a-form-item field="picUrl" :label="t('knowledgenew.cover-label')">
        <a-upload
          action="/"
          :auto-upload="false"
          :file-list="picFile ? [picFile] : []"
          :show-file-list="false"
          accept="image/png, image/jpeg, image/jpg"
          @before-upload="handleBeforeUpload"
          @change="handleChange"
          @progress="handleProgress"
        >
          <template #upload-button>
            <!-- 展示图片 -->
            <div v-if="picFile && picFile.url" class="arco-upload-list-picture">
              <img :src="picFile.url" :size="80" />
              <div class="arco-upload-list-picture-mask">
                <IconEdit />
              </div>
              <a-progress
                v-if="picFile.status === 'uploading' && picFile.percent < 100"
                :percent="picFile.percent"
                type="circle"
                size="mini"
                :style="{
                  position: 'absolute',
                  left: '50%',
                  top: '50%',
                  transform: 'translateX(-50%) translateY(-50%)',
                }"
              />
            </div>
            <!-- 上传 -->
            <div v-else class="arco-upload-picture-card">
              <div class="arco-upload-picture-card-text">
                <IconPlus />
                <div style="margin-top: 10px">{{
                  t('knowledgenew.upload-text')
                }}</div>
              </div>
            </div>
          </template>
        </a-upload>
      </a-form-item>
      <a-form-item
        field="description"
        :label="t('knowledgenew.description-label')"
      >
        <a-textarea
          v-model="formData.description"
          :placeholder="t('knowledgenew.input-placeholder')"
          :max-length="150"
          allow-clear
        />
      </a-form-item>
      <a-divider :margin="0" style="margin-bottom: 20px" />

      <div class="question-label">
        <div>{{ t('knowledgenew.set-recommended-question') }}</div>
        <a-button
          type="text"
          size="small"
          :disabled="formData.recommendedQuestions.length === 3"
          class="add-btn"
          @click="addQuestionInput"
          >{{ t('knowledgenew.add-question') }}</a-button
        >
      </div>

      <div
        v-for="(item, index) in formData.recommendedQuestions"
        :key="index"
        class="input-container"
      >
        <a-input
          v-model="formData.recommendedQuestions[index]"
          :placeholder="t('knowledgenew.input-placeholder')"
        >
          <template #suffix>
            <icon-delete
              :size="16"
              class="delete-icon hidden-delete-icon"
              :class="
                formData.recommendedQuestions.length === 1
                  ? 'formdisable-delete-icon'
                  : ''
              "
              @click="deleteQuestion(index)"
            />
          </template>
        </a-input>
      </div>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { defineEmits, defineProps, ref, PropType } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { uploadFile, createBase, updateBase } from '../../api';
  import { KnowledgeBaseRecord, CreateOrUpdateBaseParams } from '../../types';
  import { Message } from '@arco-design/web-vue';

  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object as PropType<KnowledgeBaseRecord>,
      default() {
        return {};
      },
    },
    type: {
      type: String,
      default: 'add',
    },
  });
  const emits = defineEmits(['update:visible', 'submit']);

  const btnLoading = ref(false);

  const emptyData: KnowledgeBaseRecord = {
    name: '',
    description: '',
    picUrl: '',
    recommendedQuestions: [''],
    view: true,
    upload: true,
    share: true,
    edit: false,
  };

  const formData = ref({ ...emptyData, recommendedQuestions: [''] });

  const rules = ref({
    name: [
      {
        required: true,
        message: t('knowledgenew.name-required'),
      },
    ],
  });

  // 封面文件
  const picFile = ref();

  // 表单初始化
  const initData = () => {
    if (props.type !== 'add') {
      formData.value = JSON.parse(
        JSON.stringify(props.data)
      ) as KnowledgeBaseRecord;
      if (formData.value.recommendedQuestions.length === 0) {
        formData.value.recommendedQuestions = [''];
      }
      if (formData.value.picUrl) {
        // picFile.file不赋值
        picFile.value = {
          uid: new Date().getTime(),
          percent: 100,
          status: 'success',
          url: `/work/api/sys-storage/download_image?f8s=${formData.value.picUrl}`,
        };
      }
    } else {
      formData.value = { ...emptyData, recommendedQuestions: [''] };
    }
  };

  // 上传
  const handleBeforeUpload = (file: File) => {
    const validTypes = ['image/jpeg', 'image/png'];
    const isImage = validTypes.includes(file.type);
    if (!isImage) {
      Message.info(t('knowledgenew.upload-image-type-error'));
    }
    return isImage;
  };
  const handleChange = async (_: any, currentFile: any) => {
    console.log(currentFile);
    picFile.value = {
      ...currentFile,
    };
  };
  const handleProgress = (currentFile: any) => {
    console.log('上传进度', currentFile);
    picFile.value = currentFile;
  };

  // 添加问题
  const addQuestionInput = () => {
    if (formData.value.recommendedQuestions.length < 3) {
      formData.value.recommendedQuestions.push('');
    } else {
      Message.info(t('knowledgenew.max-question-limit'));
    }
  };

  // 删除问题
  const deleteQuestion = (index: number) => {
    if (formData.value.recommendedQuestions.length > 1) {
      formData.value.recommendedQuestions.splice(index, 1);
    }
  };

  // 提交数据
  const formRef = ref<FormInstance>();
  const submitData = async () => {
    const res = await formRef.value?.validate();
    if (res) {
      return;
    }

    try {
      btnLoading.value = true;
      if (picFile.value && picFile.value.file) {
        const uploadFormData = new FormData();
        uploadFormData.append('file', picFile.value?.file);
        const uploadRes = await uploadFile(uploadFormData);

        if (uploadRes.status) {
          formData.value.picUrl = uploadRes.data.fileToken;
        } else {
          Message.error(t('knowledgenew.upload-image-fail'));
        }
      }

      // 处理推荐问题
      const newQuestions = formData.value.recommendedQuestions
        .filter((item) => {
          return item.trim();
        })
        .map((item) => {
          return item.trim();
        });

      if (props.type === 'add') {
        // 创建知识库
        const params: CreateOrUpdateBaseParams = {
          ...formData.value,
          recommendedQuestions: newQuestions,
        };
        const submitRes = await createBase(params);
        if (submitRes.status) {
          Message.success(t('knowledgenew.create-success'));
          emits('update:visible', false);
          emits('submit'); // 触发父组件查询
        }
      } else {
        // 编辑知识库
        // 若修改了提问问题，需要更新ragAgent
        const originalQuestionsStr = JSON.stringify(
          props.data.recommendedQuestions
        );
        const newQuestionsStr = JSON.stringify(newQuestions);
        let updateRAGAgent = false;
        if (originalQuestionsStr !== newQuestionsStr) {
          updateRAGAgent = true;
        }
        const params: CreateOrUpdateBaseParams = {
          id: formData.value.id,
          description: formData.value.description,
          name: formData.value.name,
          picUrl: formData.value.picUrl,
          recommendedQuestions: newQuestions,
          edit: formData.value.edit,
          share: formData.value.share,
          upload: formData.value.upload,
          view: formData.value.view,
          updateRAGAgent,
        };
        const submitRes = await updateBase(params);
        if (submitRes.status) {
          Message.success(t('knowledgenew.edit-success'));
          emits('update:visible', false);
          emits('submit', updateRAGAgent); // 触发父组件查询
        }
      }
      btnLoading.value = false;
    } catch (err) {
      btnLoading.value = false;
      console.error(err);
    }
  };

  const cancel = () => {
    emits('update:visible', false);
  };
</script>

<style scoped lang="less">
  .question-label {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    color: var(--color-text-2);
  }
  .add-btn {
    border: none;
    height: auto;
    padding: 0 4px;
  }

  .input-container {
    margin-bottom: 20px;
  }

  /* 默认隐藏删除按钮 */
  .hidden-delete-icon {
    opacity: 0;
    transition: opacity 0.2s;
  }

  /* 输入框悬浮时显示删除按钮 */
  .arco-input-wrapper:hover .hidden-delete-icon {
    opacity: 1;
  }

  .delete-icon {
    margin-left: 10px;
    color: #4e5969;
    cursor: pointer;
  }

  .delete-icon:hover {
    background-color: #f2f3f5;
    border-radius: 2px;
  }
  .formdisable-delete-icon {
    cursor: not-allowed;
  }

  // 上传图片默认样式覆盖
  .arco-upload-picture-card {
    height: 88px;
    width: 88px;
    background: rgba(242, 243, 245, 0.8);
    border-radius: 8px;
    color: #86909c;
  }
  .arco-upload-list-picture {
    margin: 0;
    height: 88px;
    width: 88px;
    border-radius: 8px;
  }
  .arco-upload-list-picture-mask {
    line-height: 88px;
  }
</style>

<style lang="less">
  .create-base-dialog {
    .arco-modal-header {
      height: 52px;
    }
    .arco-modal-title {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
      line-height: 28px;
    }
    .arco-modal-body {
      padding: 16px 20px 0;
    }
  }
</style>
