<template>
  <div
    v-if="tableData.length > 0 && checkedTableRows.length > 0"
    style="display: flex"
  >
    <div v-if="isDownloadable" class="button-box">
      <a-tooltip :content="$t('file-manage.download')" mini>
        <icon-download @click="handleDownload" />
      </a-tooltip>
    </div>

    <div v-if="isShareable" class="button-box">
      <a-tooltip :content="$t('file-manage.share')" mini>
        <icon-share-alt @click="handleShare" :disabled="btnLoading" />
      </a-tooltip>
    </div>

    <div v-if="isMoveable" class="button-box">
      <a-tooltip :content="$t('file-manage.move')" mini>
        <icon-share-internal @click="handleMove" />
      </a-tooltip>
    </div>

    <div v-if="isdeleteable" class="button-box">
      <a-popconfirm
        content="确认删除这些文件/文件夹吗"
        type="info"
        position="left"
        @ok="handleDelete('delete-folder')"
      >
        <a-tooltip :content="$t('file-manage.delete')" mini>
          <icon-delete />
        </a-tooltip>
      </a-popconfirm>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, inject, Ref, ref, watch } from 'vue';
  import { storeToRefs } from 'pinia';
  import useFileStore from '@/store/modules/file/index';

  import { Notification } from '@arco-design/web-vue';
  import usePrjPermissionStore from '@/store/modules/project-permission';

  import { downloadSource } from '../../hooks/events';
  import { deleteApi, getFileChildrenAll } from '../../api';
  import { useI18n } from 'vue-i18n';
  import { filterSysTreeData, isSysFolder, transformData } from '../../utils';

  const emits = defineEmits(['handleDownload', 'expendFolder', 'handleMove']);
  const { t } = useI18n();

  const fileStore = useFileStore();
  const { currentFolder, hiddenSlot, tableData, selectedTableRowkeys } =
    storeToRefs(fileStore);

  const prjStore = usePrjPermissionStore();
  const currentFolderRole = computed(() => {
    const currentTeam = (prjStore.teamList as any[]).filter((item) => {
      return item.id === currentFolder.value.teamId;
    })[0];
    return currentTeam ? currentTeam.role : 5;
  });

  const checkedTableRows = computed(() =>
    tableData.value.filter(
      (row) =>
        selectedTableRowkeys.value.findIndex((key) => key === row.id) !== -1
    )
  );

  const isDownloadable = ref(true);
  const isMoveable = ref(true);
  const isdeleteable = ref(true);
  const isShareable = ref(true);

  watch(
    () => checkedTableRows.value.length,
    () => {
      isDownloadable.value = true;
      isMoveable.value = true;
      isdeleteable.value = true;
      isShareable.value = true;

      checkedTableRows.value.forEach((row) => {
        if (isSysFolder(row.sysType) || currentFolderRole.value < 2) {
          isDownloadable.value = false;
          isdeleteable.value = false;
          isMoveable.value = false;
          isShareable.value = false;
        }
        if (row.type !== 'WIP' || row.parentId === 0) {
          isdeleteable.value = false;
          isMoveable.value = false;
          isShareable.value = false;
        }
      });
    }
  );

  function handleDownload() {
    emits('handleDownload');
    checkedTableRows.value.forEach((row) => {
      downloadSource(row);
    });
  }

  function handleMove() {
    const fileIds: string[] = [];
    const folderIds: string[] = [];
    checkedTableRows.value.forEach((row: any) => {
      if (row.folderId) {
        fileIds.push(row.id);
      } else {
        folderIds.push(row.id);
      }
    });
    fileStore.setMoveIds(fileIds, folderIds);

    emits('handleMove');
  }

  function handleDelete(event: string) {
    const params: {
      folderIds: string[];
      fileIds: string[];
      targetTeamId?: string;
    } = {
      folderIds: [],
      fileIds: [],
      targetTeamId: checkedTableRows.value[0].teamId,
    };
    checkedTableRows.value.forEach((row: any) => {
      if (row.folderId) {
        params.fileIds.push(row.id);
      } else {
        params.folderIds.push(row.id);
      }
    });

    deleteApi(params)
      .then((res: any) => {
        if (res.code === 8000000) {
          Notification.success({
            id: 'delete',
            title: 'Success',
            content: t('file-manage.success'),
          });
          emits('expendFolder', currentFolder.value);
        }
      })
      .catch((error) => {
        console.error('请求错误', error);
      });
  }

  const shareModalData = inject<Ref<Record<string, any>>>('shareModalData');
  const btnLoading = ref(false);

  const handleShare = async (record: any) => {
    btnLoading.value = true;
    const shareData: any = [];
    Promise.all(
      checkedTableRows.value
        .filter((row) => !row.folderId)
        .map((item: any) => {
          return getFileChildrenAll(item.id);
        })
    ).then((resList) => {
      resList.forEach((res) => {
        const treeData = transformData(res.data).shareLinkDtoList[0];

        treeData.shareLinkDtoList = filterSysTreeData(
          treeData.shareLinkDtoList
        );
        shareData.push(treeData);
      });
      checkedTableRows.value
        .filter((row) => row.folderId)
        .forEach((item) => {
          shareData.push({
            fileId: item.id,
            fileName: item.name,
            fileToken: item.fileToken,
            fileType: 1,
            size: item.size,
            version: item.version,
            description: item.description || '',
          });
        });

      btnLoading.value = false;
      shareModalData!.value = {
        show: true,
        shareData,
        shareType: 'table',
      };
    });
  };
</script>

<style lang="less" scoped>
  .button-box {
    height: 32px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-shrink: 0;
    background-color: rgba(232, 242, 255, 0.6);
    padding: 0 10px;
    transition: background-color 0.3s linear;
    svg + svg {
      width: 16px;
      height: 16px;
      margin-left: 16px;
    }
    svg {
      cursor: pointer;
      color: black;
    }
    svg:hover {
      color: rgb(var(--arcoblue-5));
    }
    .icon-approve {
      fill: black;
      outline: none;
    }
    .icon-approve:hover {
      fill: rgb(var(--arcoblue-5));
    }
  }
  .button-box:hover {
    background-color: rgba(232, 242, 255, 0.8);
  }
  .arco-btn + .arco-btn {
    margin-left: 8px;
  }
</style>
