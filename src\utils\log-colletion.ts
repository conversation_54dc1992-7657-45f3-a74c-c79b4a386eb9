import {getLocalstorage, getSessionStorage,setSessionStorage} from './localstorage'
import axios from "axios";

function getZJUnit(){
  return axios.get('/sys-system/dictionary/detail/list', {
    params: {
      code: 'zjunit'
    }
  })
}

export const setLogInfo = (name: string, orgName: string) => {
  setSessionStorage('name', name || '')
  setSessionStorage('orgName', orgName || '')
};

export const initLogCollectionSDK = async (userName: string, name: string, orgList: any) => {
  // 埋点日志上报
  try{
    // @ts-ignore
    if(window.xkCollectionSdk){
      const orgFullPathName: any = orgList.length ? orgList[0]?.pathName : '';  // 用户部门全路径
      const orgName: any = orgList.length ? orgList[0]?.entName : ''; // 用户部门名称
      const units = await getZJUnit()
      const unitList = units.data?.zjunit?.map((e:any)=>{
        return e['zh-CN']
      })
      let matchOrg = '';
      if(orgFullPathName && unitList.length){
        [...unitList, '信科集团'].forEach((e: any) => {
          if (orgFullPathName.indexOf(e) >= 0) matchOrg = e;
        })
      }
      // @ts-ignore
      window.xkCollectionSdk.RunXkCollectionSdk({
        $custromData: {
          $_user_id: userName || '',
          $_user_name: name || '',
          $_app_key: 'cdesecretkey',
          $_frontend_version: 'v1.0.',
          $_org_name: matchOrg || orgName || '',
          $_org_full_name: orgFullPathName || '',
        },
      });
    }
  }catch(e){
    if(e) console.log(e.toString());
  }
}

export default null;