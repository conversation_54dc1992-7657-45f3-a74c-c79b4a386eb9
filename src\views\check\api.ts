/*
 * by: chenming
 * date: 2023-5-11
 *
 * */
import axios from 'axios';
// 计算文件夹内文件的数量
export const getDirectoryFileCounts = (directory: any) => {
  let total = 0;
  if (directory.children?.length) {
    directory.children.forEach((item: any) => {
      if (item.isFileOrFolder === 1) {
        total += 1;
      } else {
        total += getDirectoryFileCounts(item);
      }
    });
  }
  return total;
};

export const getDirectoryFileIds = (directory: any[]): any[] => {
  const total: any[] = [];
  if (directory.length) {
    directory.forEach((e) => {
      if (e.isFileOrFolder === 1 && e.abandon !== 1) {
        total.push(e.id);
        // console.log('id: ', e.id);
      } else if (e.isFileOrFolder === 0 && e.children.length) {
        const childIds = getDirectoryFileIds(
          e.children.filter((child: any) => child.abandon !== 1)
        );
        total.push(...childIds);
      }
    });
  }

  return total;
};

export const getCheckList = (params: any) => {
  return axios.get('/cde-collaboration/review/list', { params });
};
export default null;
