import axios from 'axios';

// 获取标准数据
export function getStandardList(data: any) {
  return axios.post<any>(
    `/asset-system/bimStandard/listClassByCondition`,
    data
  );
}

// 获取分类树数据
export function getStandardTree(data: any) {
  return axios.post<any>(`/asset-system/bimStandard/classTree`, data);
}

// 新增分类树
export function addTree(data: any) {
  return axios.post<any>(`/asset-system/bimStandard/createClass`, data);
}

// 更新分类树数据
export function editTree(data: any) {
  return axios.post<any>(`/asset-system/bimStandard/updateClass`, data);
}

// 删除分类树数据
export function delTree(data: any) {
  return axios.post<any>(`/asset-system/bimStandard/batchDeleteClass`, data);
}
