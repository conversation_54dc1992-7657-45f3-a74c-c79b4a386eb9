import axios from 'axios';

export const getProcess = (data: any) => {
  return axios.get('/cde-collaboration/process/list', { params: data });
};

export const addShare = (params: any) => {
  return axios.post('/cde-collaboration/collaborate/save-submit', params);
};

export const saveShare = (params: any) => {
  return axios.post('/cde-collaboration/collaborate/save', params);
};

export const submitShare = (params: any) => {
  return axios.post('/cde-collaboration/collaborate/submit', params);
};

export const addDeliver = (params: any) => {
  return axios.post('/cde-collaboration/delivery/save-submit', params);
};

export const saveDeliver = (params: any) => {
  return axios.post('/cde-collaboration/delivery/save', params);
};

export const submitDelivery = (params: any) => {
  return axios.post('/cde-collaboration/delivery/submit', params);
};
// 删除共享数据
export function removeShare(params: any) {
  return axios.delete('/cde-collaboration/collaborate/remove', {
    params,
  });
}
