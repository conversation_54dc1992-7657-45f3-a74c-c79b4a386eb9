<template>
  <div class="pageContainer">
    <commonTabs :tabs="[]"></commonTabs>
    <div class="stardard-sel">
      <a-select
        v-model="nowStandardIdNew"
        style="max-width: 500px"
        :placeholder="$t('please-select')"
        @change="selStandardHandle"
      >
        <a-option
          v-for="type in standardListData"
          :key="type.id"
          :value="type.id"
          :label="type.name"
        ></a-option>
      </a-select>
    </div>
    <div class="container-body">
      <div class="containerLeft">
        <div class="leftCont">
          <standard-tree
            ref="treeRef"
            :action-type="false"
            @select="selecHandle"
          ></standard-tree>
        </div>
      </div>
      <div class="containerRight">
        <table-title
          :title="$t('standard.attribute-feature-standard')"
        ></table-title>

        <a-row style="margin-top: 20px">
          <a-col :span="8">
            <a-form-item
              field="name"
              :label="$t('standard.name')"
              content-class="item"
            >
              <a-input
                v-model="searchParams.name"
                style="width: 250px"
                :placeholder="$t('please-enter')"
                @change="searchHanlde"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="name"
              :label="$t('standard.information-category')"
              content-class="item"
              style="margin-left: 18px"
            >
              <a-select
                v-model="searchParams.infoCategoryName"
                :placeholder="$t('please-select')"
                style="width: 250px"
                allow-clear
                @change="searchHanlde"
              >
                <a-option
                  v-for="item of infoCategory"
                  :key="item.name"
                  :value="item.name"
                  >{{ item.name }}</a-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-space style="float: right">
              <a-button type="outline" @click="searchHanlde"
                ><icon-search /> &nbsp;{{ $t('list.options.btn.search') }}
              </a-button>
              <a-button type="outline" @click="reset">
                <icon-refresh />&nbsp;{{ $t('list.options.btn.reset') }}
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <a-divider margin="0px 0 20px" />
        <div class="btns">
          <a-space>
            <a-button
              type="primary"
              class="button"
              :disabled="!treeDataList?.length || isSystemStandard"
              @click="addHandle"
            >
              <template #icon> <icon-plus /> </template
              >{{ $t('list.options.btn.add') }}
            </a-button>
            <a-popconfirm
              :content="$t('table.opt.sure.delete')"
              type="warning"
              position="left"
              @ok="batchDelete"
            >
              <a-button
                type="outline"
                :disabled="isBatchDetlete"
                status="danger"
                ><template #icon> <icon-delete /> </template
                >{{ $t('table.opt.delete') }}
              </a-button>
            </a-popconfirm>
          </a-space>
        </div>
        <div class="content">
          <div class="table-wrap">
            <a-table
              v-model:selected-keys="selectedKeys"
              :default-expanded-keys="expandedRowKeys"
              :default-expand-all-rows="true"
              :loading="loading"
              row-key="id"
              :scroll="scroll"
              :columns="columns"
              :data="tableData"
              :scrollbar="true"
              :row-selection="{
                type: 'checkbox',
                showCheckedAll: true,
                onlyCurrent: true,
              }"
              @selection-change="selectionChange"
            >
              <template #unitCode="{ record }">
                {{ getUnitCodeText(record.unitCode) }}
              </template>
              <template #example="{ record }">
                <a-tooltip :content="record.example">
                  <span class="example">{{ record.example }}</span>
                </a-tooltip>
              </template>

              <template #valueTypeCode="{ record }">
                <a-tag
                  v-if="record.valueTypeCode"
                  :color="getBgColor(record.infoTypeCode)"
                >
                  <span :style="{ color: getSpanColor(record.infoTypeCode) }">{{
                    getvalueTypeText(record.valueTypeCode)
                  }}</span>
                </a-tag>
              </template>

              <template #action="{ record }">
                <a-space v-if="record.classId">
                  <a-button
                    type="text"
                    size="small"
                    :disabled="isSystemStandard"
                    @click="editHandle(record)"
                  >
                    {{ $t('table.opt.edit') }}
                  </a-button>

                  <a-popconfirm
                    :content="$t('table.opt.sure.delete')"
                    position="left"
                    @ok="delHandle(record)"
                  >
                    <a-button
                      type="text"
                      size="small"
                      status="danger"
                      :disabled="isSystemStandard"
                    >
                      {{ $t('table.opt.delete') }}
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </a-table>
            <div class="tips">{{ $t('standard.tips') }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <add-node
    v-if="addAttrVisible"
    v-model:visible="addAttrVisible"
    :type="actionType"
    :data="attrData"
    @refresh="refreshTable"
  ></add-node>
</template>

<script lang="ts" setup>
  import { onMounted, ref, computed, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import addNode from './components/addNode.vue';
  import standardTree from '@/components/standard/standardTree/index.vue';
  import commonTabs from '@/components/common-tabs/index.vue';
  import useStandardManageStore from '@/store/modules/standard-manage/index';
  import { storeToRefs } from 'pinia';
  import TableTitle from '@/components/table-title/index.vue';
  import { useI18n } from 'vue-i18n';

  import { getGroupList, delAttribute } from './api';
  import { getStandardTree } from '@/views/standard-manage/classCodeStandard/api';
  import { useUserStore } from '@/store';

  const userStore = useUserStore();
  const admin = computed(() => userStore.admin);
  const { t } = useI18n();

  const store = useStandardManageStore();
  const {
    standardListData,
    unit,
    dataType,
    infoCategory,
    treeSelectedKey,
    nowStandardId,
    treeDataList,
    standPrecision,
  } = storeToRefs(store);

  const nowStandardIdNew: any = ref('');

  // 精度文案转换
  const getModelAccuText = (value: any) => {
    let text = '';
    if (value === 1) text = '▲';
    if (value === 2) text = '△';
    if (value === 3) text = '○';
    if (value === 0) text = '/';
    return text;
  };

  const expandedRowKeys: any = ref([]);

  const searchParams: any = ref({
    pageNo: 1,
    pageSize: 20,
    name: '',
    scenarioId: '',
  });

  const columns0: any = computed<any[]>(() => [
    {
      title: t('standard.name'),
      dataIndex: 'name',
      align: 'left',
      fixed: 'left',
    },
    {
      title: t('standard.unit'),
      dataIndex: 'unitCode',
      align: 'left',
      slotName: 'unitCode',
    },
    {
      title: t('standard.type'),
      dataIndex: 'valueTypeCode',
      slotName: 'valueTypeCode',
      align: 'left',
    },
    {
      title: t('standard.examples-value'),
      dataIndex: 'example',
      slotName: 'example',
      align: 'left',
    },
    {
      title: t('standard.ifc-type'),
      dataIndex: 'ifcType',
      slotName: 'ifcType',
      align: 'left',
    },
  ]);

  const columns: any = ref([]);

  const loading: any = ref(false);

  const addAttrVisible: any = ref(false);
  const actionType: any = ref('add');
  const attrData: any = ref({});

  // 最近文件列表部分
  const tableData = ref<object[]>([]);
  const tableDataAll = ref<object[]>([]);
  // const tableHeight = ref(window.innerHeight - 590);
  const scroll = {
    y: 'calc(100vh - 410px)',
  };

  const isSystemStandard = ref<boolean>(false); // 是否是系统标准

  /**
   * 添加 disabled属性
   */
  const addDisabledProperty = (data) => {
    return data.map((item) => {
      // 1. 给当前层添加 disabled: true
      const newItem = { ...item, disabled: true };

      // 2. 如果存在 children，递归处理
      if (newItem.children && newItem.children.length > 0) {
        newItem.children = addDisabledProperty(newItem.children);
      }

      return newItem;
    });
  };

  const treeRef: any = ref(null);

  const selecHandle = async () => {
    if (!treeSelectedKey.value) return;
    loading.value = true;
    const param: any = {
      classId: treeSelectedKey.value,
    };

    // 获取表格数据
    const { data }: any = await getGroupList(param);

    // 表格数据处理
    data?.forEach((item: any, index: any) => {
      item.id = `attr${index}`;
      expandedRowKeys.value.push(item?.id || '');
      item.children = item.attributeList || [];
      item.name = item.infoType;
    });
    // 精度展示处理
    data?.forEach((item: any) => {
      item.children.forEach((child: any) => {
        if (child.precisionList?.length) {
          child.precisionList?.forEach((pre: any) => {
            child[pre.code] = getModelAccuText(pre.requirement);
          });
        }
      });
    });
    tableData.value = data;
    tableDataAll.value = data;
    loading.value = false;
    if (isSystemStandard.value) {
      // 执行递归添加 disabled: true
      tableData.value = addDisabledProperty(tableData.value);
    }
  };

  // 查询
  const searchHanlde = async () => {
    const { infoCategoryName, name } = searchParams.value as any;

    // 先按类别筛选
    let filteredData = tableDataAll.value;
    if (infoCategoryName) {
      filteredData = tableDataAll.value.filter(
        (item: any) => item.name === infoCategoryName
      );
    }

    // 再按名称筛选
    if (name) {
      filteredData = filteredData.map((item: any) => {
        const matchedAttributes = item.attributeList.filter((attribute: any) =>
          attribute.name.includes(name)
        );
        return {
          ...item,
          children: matchedAttributes,
        };
      });
    }

    // 过滤掉父节点下无子集的数据
    tableData.value = filteredData.filter((item: any) => {
      return item.children && item.children?.length > 0;
    });
  };

  // 配置对象
  const typeConfig = {
    stdType_1: { bgColor: '#E8F2FF', spanColor: '#3366FF' },
    stdType_2: { bgColor: '#F5E8FF', spanColor: '#722ED1' },
    stdType_3: { bgColor: '#E8F2FF', spanColor: '#3366FF' },
    stdType_4: { bgColor: '#E8FFFB', spanColor: '#0FC6C2' },
  };

  // 背景颜色
  const getBgColor = (code: string) => {
    return typeConfig[code]?.bgColor || '#E8F2FF';
  };

  // 字体颜色
  const getSpanColor = (code: string) => {
    return typeConfig[code]?.spanColor || '#3366FF';
  };

  // 类型
  const getvalueTypeText = (code: any) => {
    const data = dataType.value.filter((item: any) => item.code === code);
    return data[0]?.name;
  };
  // 获取单位文案
  const getUnitCodeText = (code: any) => {
    let result = '';
    unit.value.forEach((item: any) => {
      item.children?.forEach((val: any) => {
        if (code === val.code) {
          result = val.name;
        }
      });
    });
    return result;
  };

  const accuracyColumns: any = ref([]);

  // 获取树数据
  const getTreeData = async () => {
    tableData.value = [];
    if (nowStandardIdNew.value) {
      store.setTreeLoading(true);
      const { data }: any = await getStandardTree({
        standardId: nowStandardIdNew.value,
      });
      const setTreeSelectList: any = [
        {
          name: data.name,
          id: data.id,
          childList: data.childList,
        },
      ];
      store.setTreeLoading(false);
      await store.setTreeData(data.childList || []);

      // 动态设置Columns
      accuracyColumns.value = [];
      standPrecision.value.forEach((item: any) => {
        accuracyColumns.value.push({
          title: item.name,
          dataIndex: item.code,
          align: 'left',
        });
      });
      columns.value = [
        ...columns0.value,
        ...accuracyColumns.value,
        {
          title: t('standard.operation'),
          dataIndex: 'action',
          slotName: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
        },
      ];
      // 设置表格数据（第一个树节点对应的属性数据）
      if (data?.childList?.length) {
        await store.setTreeSelectedKey(data.childList[0]?.id);
        store.setSelTreeData(data.childList[0]);
        await store.setTreeSelectList(setTreeSelectList);
        selecHandle();
      }
    }
  };

  // 切换标准
  const selStandardHandle = (val: any) => {
    // store.setTableDataNew([]); // 清空表格数据
    store.setNowStand(val); // 设置当前选择的标准
    store.setTreeData([]); // 清空树数据
    tableData.value = []; // 清空属性表格数据
    // store.setTreeSelectedKey(''); // 重置选择的树节点
    getTreeData();
    // 获取标准精度范围
    store.setStandPrecision();
  };

  const getDictionaryList = async () => {
    // 获取字典数据
    store.setDictionaryData('stdUnit');
    store.setDictionaryData('stdValueType');
    store.setDictionaryData('stdType');
  };

  // 编辑成功后刷新当前标准树下的属性数据
  const refreshTable = () => {
    selecHandle();
  };

  const reset = () => {
    searchParams.value = {
      pageNo: 1,
      pageSize: 20,
      name: '',
      infoCategoryName: null,
    };
    selecHandle();
  };

  const selectedKeys: any = ref([]);

  // 新增
  const addHandle = () => {
    actionType.value = 'add';
    addAttrVisible.value = true;
  };

  // 编辑
  const editHandle = async (record: any) => {
    actionType.value = 'edit';
    attrData.value = record;
    addAttrVisible.value = true;
  };

  // 删除
  const delHandle = async (record: any) => {
    try {
      await delAttribute({ idList: [record.id] });
      Message.success(t('delete-successful'));
      refreshTable();
    } catch (err) {
      console.log(err);
    }
  };

  const isBatchDetlete: any = ref(true);
  const selectionIds: any = ref([]);

  // 批量删除
  const batchDelete = async () => {
    await delAttribute({ idList: selectionIds.value });
    Message.success(t('delete-successful'));
    refreshTable();
  };

  // 获取已选择表格行数据
  const selectionChange = (keys: any) => {
    const key = keys.filter((item: any) => !item.startsWith('attr'));
    selectionIds.value = key;
    if (selectionIds.value?.length > 0) isBatchDetlete.value = false;
    else isBatchDetlete.value = true;
  };

  onMounted(async () => {
    store.setTreeData([]); // 树数据清空
    // 1.获取精度数据
    await store.setDictionaryData('stdPrecision');
    // 2.获取标准下拉数据
    await store.setStandardList();
    const hasStandardId = standardListData.value.some(
      (item: any) => item.id === nowStandardId.value
    );
    // 缓存中的标准id如果在标准列表数据中 则默认展示此标准  若没有 默认展示第一个
    nowStandardIdNew.value = hasStandardId
      ? nowStandardId.value
      : standardListData.value[0]?.id;

    getTreeData();
    columns.value = JSON.parse(JSON.stringify(columns0.value));
    getDictionaryList();
  });

  // watch(
  //   () => treeDatalist.value,
  //   () => {
  //     // store.setTreeData(treeDatalist.value);
  //     store.setSelTreeData(treeDatalist.value[0]);
  //     setTimeout(() => {
  //       getTreeData();
  //     }, 100);
  //   }
  // );

  watch(
    () => nowStandardIdNew.value,
    (val) => {
      if (val && admin.value !== 0 && standardListData.value.length > 0) {
        const ids: string[] = standardListData.value
          .filter((item) => item.groupId === '0') // 筛选 groupId 为 "0" 的元素
          .map((item) => item.id); // 提取 id 组成新数组
        isSystemStandard.value = ids.includes(val);
      }
    }
  );
</script>

<style scoped lang="less">
  .pageContainer {
    position: relative;
    padding: 16px 20px;
    .container-body {
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      > div {
        background-color: var(--color-bg-1);
      }
      .containerLeft {
        width: 300px;
        overflow: hidden;
        .content-title {
          div {
            display: flex;
            align-items: center;
            justify-content: left;
            height: 50px;

            .title-text {
              margin-left: 8px;
              font-weight: bold;
              font-size: 16px;
            }
          }
        }

        .leftList {
          div {
            padding: 4px 0 4px 20px;
            font-size: 16px;
            line-height: 30px;
            cursor: pointer;
          }

          .active {
            color: #fff;
            background-color: rgb(var(--link-6));
          }
        }
      }
      .containerRight {
        flex: 1;
        padding: 20px;
        border-left: 2px solid #efefef;
        .content {
          margin-top: 24px;

          .content-title {
            position: relative;
            margin-top: 16px;

            .btns {
              position: absolute;
              top: 0;
              right: 0;
            }

            .title-img {
              width: 20px;
              height: 20px;
            }

            .title-text {
              position: absolute;
              top: 0;
              left: 20px;
              display: inline-block;
              margin-left: 8px;
              color: #1d2129;
              // color: var(--color-text-1);
              font-weight: 600;
              font-size: 18px;
              // font-family: 'Source Han Sans CN-Medium', 'Source Han Sans CN', serif;
              line-height: 21px;
            }
          }

          .table-wrap {
            height: calc(100% - 250px);
            font-size: 14px;
            margin-top: 18px;
          }
        }

        .tips {
          margin-top: 16px;
          color: #86909c;
        }
      }
    }
  }
  :deep(.arco-form-item-label) {
    width: auto;
    // text-align: left;
  }
  :deep(.arco-select) {
    border: 1px solid #c9cdd4 !important;
    background-color: #fff;
  }

  .stardard-sel {
    position: absolute;
    top: 14px;
    left: 60px;
    :deep(.arco-select) {
      border: 1px solid transparent !important;
    }
    :deep(.arco-select-view-value) {
      font-size: 16px !important;
    }

    :deep(.arco-select-view-value) {
      min-width: 200px;
    }
    :deep(.arco-select-view-value),
    :deep(.arco-select-view-icon) {
      color: #3366ff;
      font-size: 16px;
      font-weight: 500;
    }
    :deep(.arco-select-view-single) {
      max-width: 500px;
    }
    :deep(.arco-select-view-value) {
      display: inline-block;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .example {
    display: inline-block;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }

  :deep(.arco-table-td .arco-btn) {
    padding: 0;
  }
  .leftCont {
    // height: 100%;
    height: calc(100vh - 170px);
  }
</style>
