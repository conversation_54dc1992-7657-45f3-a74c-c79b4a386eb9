import axios from 'axios';

export interface OrgContent {
  id?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  portalId?: string;
  name?: string;
  entName?: string;
  orgNo?: number;
  parentNo?: string;
  parentName?: string;
  pathNo?: string;
  pathName?: string;
  sort?: number;
  type?: number;
  orgType?: any;
  tenantId?: number;
  ext1?: any;
  ext2?: any;
  ext3?: any;
  ext4?: any;
  ext5?: any;
}

export interface OrgRecord {
  id: string;
  name: string | undefined;
  entName: string | undefined;
  parentId: string;
  content?: OrgContent;
  orgType?: string;
  children?: OrgRecord[];
}

export interface SelectDept extends OrgRecord {
  operator?: 'add' | 'edit' | 'addSub';
  parentName?: string;
  orgType?: string;
  parentNo?: string | number;
  pathName?: string;
  pathNo?: string | number;
}

export interface OrgServiceRecord {
  id: string;
  parentId: string;
  content: OrgContent;
  children?: OrgServiceRecord[];
}

export interface AddOrgParams {
  name: string | undefined;
  entName: string | undefined;
  parentName: string | undefined;
  parentNo?: string;
  pathName: string;
  pathNo?: string;
}

// 根据门户id获取组织架构树, TODO: 通用化接口
export function getOrgs(
  params: any,
  defaultUrl = '/cde-collaboration/sys-user/orgs/tree'
) {
  return axios({
    url: defaultUrl,
    method: 'get',
    params,
  });
}

// 新增部门
export function addOrgs(data: OrgContent) {
  return axios({
    url: '/sys-user/org',
    method: 'post',
    data,
  });
}
// 修改部门
export function modifyOrg(data: OrgContent) {
  return axios({
    url: '/sys-user/org',
    method: 'put',
    data,
  });
}
// 删除部门
export function deleteOrg(id: string) {
  return axios.delete('/sys-user/org', { params: { id } });
}

// 获取部门数据
export function getDepartmentList(params: any) {
  return axios.get('/sys-user/orgs/child', {
    params,
  });
}
