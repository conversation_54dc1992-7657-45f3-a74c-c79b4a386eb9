import { AppRouteRecordRaw } from '../types';

const FILE: AppRouteRecordRaw = {
  path: 'task',
  name: 'task',
  component: () => import('@/views/projectSpace/task/index.vue'),
  props: (route: any) => ({
    type: route.query.type
  }),
  meta: {
    locale: 'menu.cdex.approval',
    requiresAuth: true,
    icon: 'icon-stamp',
    order: 3,
    showAI: true,
    globalMode: ['project'],
  },
};

export default FILE;
