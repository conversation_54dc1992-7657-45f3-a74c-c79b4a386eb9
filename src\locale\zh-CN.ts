const utilsModules = import.meta.webpackContext('@/utils', {
  recursive: true,
  regExp: /zh-CN\.ts$/,
});
const apiModules = import.meta.webpackContext('@/api', {
  recursive: true,
  regExp: /zh-CN\.ts$/,
});
const directionaryModules = import.meta.webpackContext('@/directionary', {
  recursive: true,
  regExp: /zh-CN\.ts$/,
});

const viewModules = import.meta.webpackContext('@/views', {
  recursive: true,
  regExp: /zh-CN\.ts$/,
});

const componentModules = import.meta.webpackContext('@/components', {
  recursive: true,
  regExp: /zh-CN\.ts$/,
});

const globleModules = import.meta.webpackContext('./zh-CN', {
  recursive: false,
  regExp: /\.ts$/,
});

function formatModules(_modules: any) {
  let result = {};
  // eslint-disable-next-line no-restricted-syntax
  for (const path of _modules.keys()) {
    const mod = _modules(path);
    // eslint-disable-next-line no-unused-expressions
    mod.default ? (result = { ...result, ...mod.default }) : '';
  }
  return result;
}
const locale = {
  ...formatModules(viewModules),
  ...formatModules(componentModules),
  ...formatModules(globleModules),
  ...formatModules(utilsModules),
  ...formatModules(apiModules),
  ...formatModules(directionaryModules),
};
export default {
  'menu.result': '结果页',
  'menu.feedback.requirements': '需求反馈',
  'menu.exception': '异常页',
  'menu.project-member': '团队成员',
  'menu.project-setting': '项目设置',
  'menu.setting': '设置',
  'menu.project-component': '构件',
  'menu.merge-model': '合模',
  'menu.mold-application': '合模应用',
  'menu.conference': '会议',
  'menu.component': '构件',
  'menu.matters': '事项',

  'menu.standard-manage': '标准管理',
  'menu.standard-list': '标准列表',
  'menu.class-code-standard': '分类编码标准',
  'menu.attr-features-standard': '属性特征标准',
  'menu.dictionary-onfig': '字典配置',
  'menu.attr-check': '属性校验',
  'menu.named-check': '命名校验',

  'menu.issues': '问题沟通',
  'menu.check': '审阅',
  'menu.files': '文件',
  'menu.enclosure': '附函',
  'menu.design': '主页',
  'menu.task': '任务',
  'menu.org': '组织机构',
  'menu.user-manage': '用户管理',
  'menu.dashboard': '工作台',
  'menu.knowledgeBase': '知识库',
  'menu.schedule': '日程',
  'menu.portalCenter': '门户中心',
  'menu.typeManagement': '类型管理',
  'menu.documentManagement': '文档管理',
  'menu.videoGroupManagement': '视频组管理',
  'menu.videoManagement': '视频管理',
  'menu.bannerAdvertisingImage': '横幅广告图片',
  'menu.announcementSettings': '公告设置',
  'menu.feedbackCollection': '反馈收集',
  'menu.temporary-storage': '临时存储',
  'menu.cdex.bim': '项目空间',
  'menu.cdex.file': '项目文件',
  'menu.cdex.approval': '审批流程',
  'menu.cdex.home': '项目主页',
  'menu.cdex.meeting': '会议事项',
  'menu.createSchedule': '创建日程',
  'layout.switch.to.project': '切换至项目空间',
  'layout.switch.to.work': '切换至个人空间',
  'layout.navigation': '导航',
  'layout.ai.assistant': 'AI助手',
  'navbar.action.locale': '切换为中文',
  'navbar.logo.title': 'CDex',
  'navbar.logo.enter': '请输入',
  'navbar.logo.to': '回到首页',
  'navbar.mode.design': '协同办公',
  'navbar.mode.design.mini': '协',
  'navbar.mode.file': '文件',
  'navbar.mode.file.mini': '文',
  'navbar.mode.user': '用户中心',
  'navbar.mode.portal': '门户中心',
  'navbar.mode.user.mini': '用',
  'navbar.mode.portal.mini': '门',
  'navbar.mode.component.library': '构件库',
  'navbar.global.view': '全局视角',
  'navbar.docs': '文档',
  'ai.new.chat': '新增对话',
  'ai.search': '查询',
  'ai.confirm.delete': '确认删除吗？',
  'ai.delete.success': '删除成功！',
  'ai.rename.success': '重命名成功！',
  'global.search.title': '全局搜索',
  'global.search.placeholder': '请输入',
  'global.search.results': '搜索结果',
  'global.search.schedule': '日程',
  'global.search.knowledge': '知识库',
  'account.switcher.back': '返回',
  'account.switcher.title': '你可以切换以下账号',
  'account.switcher.description':
    '该用户已绑定多个账号，你可以进入以下任一企业或组织',
  'account.switcher.personal': '私人空间',
  'account.switcher.loading': '正在加载...',
  // Common Locale
  'list.options.btn.search': '查询',
  'list.options.btn.clear': '清空',
  'list.options.btn.reset': '重置',
  'list.options.btn.add': '添加',
  'list.options.btn.new': '新增',
  'list.options.btn.edit': '编辑',
  'list.options.btn.delete': '删除',
  'table.yes': '是',
  'table.no': '否',
  'table.opt.preview': '预览',
  'table.opt.details': '详情',
  'table.opt.setting': '设置',
  'table.opt.edit': '编辑',
  'table.opt.delete': '删除',
  'table.opt.sure.delete': '确定删除?',
  'table.opt.remove': '移除',
  'please-enter': '请输入',
  'please-enter-name-email-phone-to-search':
    '请输入姓名、账号、邮箱或手机号进行查询',
  'please-select': '请选择',
  'delete-successful': '删除成功',
  'delete-failed': '删除失败',
  'create-successful': '创建成功',
  'edit-successful': '编辑成功',
  'zip': '压缩包',
  'super-administrator': '超级管理员',
  'administrator': '管理员',
  'ordinary-members': '普通成员',
  'not-allowed-special-characters': '名称不能包含下列任何字符：',
  'login.register': '注册',
  'login.complete.info': '完善信息',
  'login.form.mobileCaptcha': '手机验证码',
  'login.form.emailCaptcha': '邮箱验证码',
  'login.form.password': '密码',
  'login.form.confirmPassword': '确认密码',
  'login.form.confirmPasswordRequired': '请确认密码',
  'home.chat.general-dialog': '通用对话-DeepSeek',
  'home.last-seven-days': '近七天',
  'home.no-data': '暂无数据',
  'user.transfer.name': '姓名',
  'user.transfer.account': '账号',
  'user.transfer.phone': '手机号',
  'user.transfer.email': '邮箱',
  'user.transfer.operate': '操作',
  'user.transfer.userList': '用户列表',
  'user.transfer.add.user': '添加用户',
  'user.transfer.search.placeholder': '请输入姓名或者手机号进行查找',
  'user.transfer.remove.confirm': '确认删除该项?',
  'user.transfer.remove': '移除',
  'user.transfer.loading': '加载中，请稍后...',
  'user.transfer.no.data': '暂无数据',
  'user.transfer.no.org': '数据发送错误，请重试',
  'user.transfer.no.user': '未选择用户',
  'user.transfer.all.user.list': '全部用户列表',
  'user.transfer.add.user.list': '添加用户列表',
  'user.transfer.confirm': '确认',
  'user.transfer.cancel': '取消',
  'selectMembers.invite': '邀请成员',
  'selectMembers.search': '搜索',
  'selectMembers.contacts': '联系人',
  'selectMembers.selected': '已选',
  'selectMembers.person': '人',
  'selectMembers.selectAll': '全选',
  'selectMembers.organization': '组织架构',
  'selectMembers.group': '群组',
  'selectMembers.close': '关闭',
  'selectMembers.root': '根节点',
  'selectMembers.team': '群组',
  'navbar.marquee.text':
    '当前项目为示例项目，为了保障您的项目数据安全，请勿在此上传敏感数据，当日所有上传的数据将在次日凌晨3点30分清空，请您知悉！',
  'task.sync-push-to-jjt': '同步推送至交建通',
  'task.submitter': '提交人',
  ...locale,
};
