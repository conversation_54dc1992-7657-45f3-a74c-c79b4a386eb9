<template>
  <div class="login-form-wrapper">
    <a-tabs v-model:active-key="activeKey">
      <a-tab-pane
        v-if="secondFA"
        key="3"
        :title="$t('login.form.login.account')"
      >
        <DualVerificationForm @change-login="changeLogin" />
      </a-tab-pane>
      <a-tab-pane
        v-if="!secondFA"
        key="1"
        :title="$t('login.form.login.account')"
      >
        <AccountForm @change-login="changeLogin" />
      </a-tab-pane>
     <a-tab-pane :title="$t('login.tab.jjt')" key="2">
       <ScanCode  v-if="activeKey === '2'"></ScanCode>
     </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import LoginMethods from '../constant';
  import AccountForm from './account-form.vue';
  import DualVerificationForm from './dual-verification-form.vue';
  import ScanCode from "@/views/login/components/scan-code.vue";
  import { getClientInfo } from '../api';

  const emit = defineEmits(['changeLogin']);

  const secondFA = ref(false);
  const activeKey = ref('1');
  const init = async () => {
    try {
      const res: any = await getClientInfo();
      const { additionalInformation } = res.data;
      const information = JSON.parse(additionalInformation);
      console.log('additionalInformation: ', information.loginStrategy)
      secondFA.value = information.loginStrategy.secondFA;
      activeKey.value = secondFA.value ? '3' : '1';
    } catch (err) {
      console.error(err);
    }
  };

  init();

  const changeLogin = (method: LoginMethods) => {
    emit('changeLogin', method);
  };
</script>

<style lang="less" scoped>

  .login-form {
    &-wrapper {
      width: 369px;
      padding-left: 16px;
    }
  }
  .third-login {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: var(--color-text-2);
    cursor: pointer;
    margin: 32px auto 0;
    width: 160px;
    &:hover {
      color: rgb(var(--primary-6));
    }
  }
  .cccc-image {
    width: 28px;
    height: 28px;
    margin-right: 8px;
  }
</style>
