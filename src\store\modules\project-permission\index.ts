import { defineStore } from 'pinia';
import { Message } from '@arco-design/web-vue';
import { getUserTeamsInPrj } from '@/api/project';
import { PrjPermissionState, TeamItem } from './types';
import i18n from '@/locale/index';

const { t } = i18n.global;

const usePrjPermissionStore = defineStore('prjPermission', {
  state: (): PrjPermissionState => ({
    id: '',
    userId: '',
    name: '',
    projectAdmin: 0,
    moduleVisible: '0,1',
    teamList: [],
    modelEngine: 'XBase',
  }),

  getters: {
    isPrjAdmin(state: PrjPermissionState) {
      return !!state.projectAdmin;
    },
    modeList() {
      // // 协同设计和文件模块暂时全部放开，不走接口
      // const moduleVisibleArray = '0,1,2'.split(',') || [];
      // // const moduleVisibleArray = state.moduleVisible?.split(',') || [];
      // const moduleMap = [
      //   { label: '文件', value: 'file', other: '文' },
      //   { label: '协同设计', value: 'design', other: '协' },
      //   { label: '模型协调', value: 'coordination', other: '模' },
      // ];
      const moduleVisibleArray = '0,1'.split(',') || [];
      const moduleMap = [
        {
          label: t('navbar.mode.file'),
          value: 'file',
          other: t('navbar.mode.file.mini'),
        },
        {
          label: t('navbar.mode.design'),
          value: 'design',
          other: t('navbar.mode.design.mini'),
        },
      ];
      return moduleVisibleArray.map((item) => moduleMap[Number(item)]);
    },
  },

  actions: {
    // Reset user's information
    resetInfo() {
      this.$reset();
    },
    setModelEngine(modelEngine: string) {
      this.modelEngine = modelEngine;
    },
    setTeamList(list: TeamItem[]) {
      this.teamList = list || [];
    },
    // Set permission list
    async setPermission(projectId: string) {
      try {
        const res = await getUserTeamsInPrj(projectId);
        if (res.status && res.data?.[0]) {
          res.data[0].modelEngine = res.data[0].modelEngine || 'XBase';
          this.$patch(res.data[0]);
        } else {
          this.$reset();
        }
        this.teamList = res.data[0].teamList || [];
      } catch (err) {
        if (typeof err === 'string') {
          Message.error(err);
        }
      }
    },
  },
});

export default usePrjPermissionStore;
