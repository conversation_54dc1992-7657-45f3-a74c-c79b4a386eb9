export default {
  'menu.setup': '系统设置',
  'setup.menu.personalInfo': '个人信息',
  'setup.menu.securitySettings': '安全设置',
  'setup.menu.memberManagement': '成员管理',
  'setup.menu.groupManagement': '群组管理',
  'setup.menu.groupMember': '群组成员',
  'setup.menu.organization': '组织机构',
  'setup.menu.user': '用户管理',
  'setup.menu.role': '角色管理',
  'setup.menu.permission': '权限管理',
  'setup.logout': '退出登录',
  'setup.frequent.request.message': '操作过于频繁，请稍后再试。',
  'menu.user.setting': '用户设置',
  'userSetting.title.baseinfo': '基本资料',
  'userSetting.title.editPwd': '修改密码',
  'userSetting.title.editPhone': '更换手机号',
  'userSetting.title.editEmail': '更换邮箱',
  'userSetting.title.bind-Phone': '绑定手机设置',
  'userSetting.title.bind-Email': '绑定邮箱',
  'userSetting.form.oldPwd': '当前密码',
  'userSetting.form.oldPwd.required': '当前密码必填',
  'userSetting.form.newPwd': '新密码',
  'userSetting.form.newPwd.required': '新密码必填',
  'userSetting.form.enterPwd': '确认密码',
  'userSetting.form.enterPwd.required': '确认密码必填',
  'userSetting.form.placeholder.common': '请输入',
  'userSetting.save': '保存',
  'userSetting.reset': '重置',
  'userSetting.password.edit.error': '两次密码输入不一致',
  'userSetting.password.edit.success': '密码修改成功',
  'userSetting.form.phone': '手机号',
  'userSetting.form.phone-new': '新手机号',
  'userSetting.form.captcha': '验证码',
  'userSetting.phone.edit.success': '手机号修改成功',
  'userSetting.form.email': '邮箱',
  'userSetting.form.email.required': '邮箱必填',
  'userSetting.form.email.error': '邮箱格式不正确',
  'userSetting.email.edit.success': '邮箱修改成功',
  'userSetting.email.equity': '产品权限申请',
  'userSetting.password-validation': '密码必须至少包含一个小写字母、一个大写字母、一个数字、一个特殊字符（如$%?^()=+,.;:等），并且长度至少为8个字符',
  'userSetting.password-rule-error': '密码不符合规则',

  // 成员管理
  'member.management.title': '成员管理',
  'member.management.add': '添加成员',
  'member.management.name': '姓名',
  'member.management.account': '账号',
  'member.management.role': '角色',
  'member.management.department': '部门',
  'member.management.status': '状态',
  'member.management.operation': '操作',
  'member.management.edit': '编辑',
  'member.management.delete': '删除',

  // 安全设置
  'security.settings.title': '安全设置',
  'security.settings.password': '修改密码',
  'security.settings.oldPassword': '原密码',
  'security.settings.newPassword': '新密码',
  'security.settings.confirmPassword': '确认密码',
  'security.settings.phone': '绑定手机',
  'security.settings.email': '绑定邮箱',
  'security.settings.twoFactor': '两步验证',
  'security.settings.loginHistory': '登录历史',

  // 成员管理新增
  'member.management.group': '群组',
  'member.management.create': '新建',
  'member.management.members': '成员',
  'member.management.invite': '邀请更多成员',
  'member.management.organization': '内部组织',
  'member.management.external': '外部用户',
  'member.management.remove': '移出',
  'member.management.confirm.remove': '确认移出',
  'member.management.confirm.remove.message': '确定将{name}移出群组?',
  'member.management.confirm.delete': '确认删除',
  'member.management.confirm.delete.message': '确定删除群组 {teamName} ?',
  'member.management.add.group': '添加新群组',
  'member.management.edit.group': '修改群组',
  'member.management.group.name': '群组名称',
  'member.management.group.name.required': '群组名称必须填写',
  'member.management.group.name.placeholder': '请输入群组名称',
  'member.management.group.save.user.failed': '请先创建群组!',
  'member.management.selected.members': '已选择成员',
  'member.management.avatar': '头像',
  'member.management.email': '邮箱',
  'member.management.organizationType': '组织类型',
  'member.management.index': '序号',
  'member.management.user': '用户',
  'common.cancel': '取消',
  'common.confirm': '确定',

  // 个人信息
  'personal.info.title': '个人信息',
  'personal.info.fullname': '全名',
  'personal.info.fullname.placeholder': '请输入你的全名',
  'personal.info.role': '角色',
  'personal.info.role.placeholder': '请选择你的角色',
  'personal.info.email': '电子邮件',
  'personal.info.email.placeholder': '请输入你的邮箱',
  'personal.info.phone': '电话',
  'personal.info.phone.placeholder': '请输入你的手机号码',
  'personal.info.language': '语言',
  'personal.info.language.placeholder': '请选择你要使用的语言',
  'personal.info.language.zh': '简体中文',
  'personal.info.language.en': '英文',
  'personal.info.account': '交建通账号',
  'personal.info.account.placeholder': '请绑定你的交建通账号',
  'personal.info.account.unbind': '暂未绑定交建通账号',
  'personal.info.reset': '重置',
  'personal.info.save': '保存设置',
  'personal.info.role.required': '请选择角色类型！',
  'personal.info.role.load.failed': '角色列表加载失败',
  'personal.info.avatar.upload.failed': '头像上传失败',
  'personal.info.update.failed': '个人信息更新失败',
  'personal.info.submit.failed': '提交失败，请稍后重试',
  'personal.info.avatar.format.error':
    '请上传正确的图片格式（支持 jpg、jpeg、png）',

  // 登录表单相关错误提示
  'login.form.captchaSent': '验证码发送成功',
  'login.form.captchaFailed': '验证码发送失败',
  'login.form.captchaHoldOn': '请稍后再试',

  // 成员管理相关错误提示
  'member.management.group.load.failed': '群组列表加载失败',
  'member.management.members.load.failed': '成员列表加载失败',
  'member.management.save.failed': '保存失败',
  'member.management.data.load.failed': '数据加载失败',
  'member.management.group.create.failed': '群组创建失败',
  'member.management.group.update.failed': '群组更新失败',
  'member.management.remove.success': '已成功移出群组',
  'member.management.delete.success': '已成功删除群组',
  'member.management.remove.failed': '移出成员失败',

  // 安全设置相关错误提示
  'userSetting.password.edit.failed': '密码修改失败',

  'userSetting.jiao-jian-authority': '交建通权限',
  'userSetting.project-members': '允许同项目成员向我发送通知',
  'userSetting.all-members': '允许所有人向我发送通知',
  'userSetting.specified-user': '允许指定用户向我发送通知',
};
