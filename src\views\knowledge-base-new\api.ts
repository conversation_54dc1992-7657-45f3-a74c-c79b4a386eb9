import axios from 'axios';
import {
  QueryBaseListParams,
  CreateOrUpdateBaseParams,
  GenerateShareLinkParams,
  QueryBaseMemberListParams,
  QueryFolderListParams,
  SearchFileParams,
  CreateFolderParams,
  RenameFolderParams,
  RenameFileParams,
  DeleteFolderParams,
  SaveFileParams,
  DeleteFileParams,
  ImportFileParams,
  // KnowledgeBaseUsageParams,
} from './types';

// 查看知识库列表
function getBaseList(data: QueryBaseListParams) {
  return axios.post('/cde-collaboration/knowledgeBase/listBases', data);
}

// 创建知识库
function createBase(data: CreateOrUpdateBaseParams) {
  return axios.post('/cde-collaboration/knowledgeBase/createBase', data);
}

// 更新知识库
function updateBase(data: CreateOrUpdateBaseParams) {
  return axios.post('/cde-collaboration/knowledgeBase/updateBase', data);
}

// 删除知识库
function deleteBase(id: string) {
  return axios.post(`/cde-collaboration/knowledgeBase/deleteBase?id=${id}`);
}

// 移除知识库成员
function removeMember(data: { kbId: string; userNames: string[] }) {
  return axios.post('/cde-collaboration/knowledgeBase/removeMember', data);
}

// 生成分享链接
function generateShareLink(data: GenerateShareLinkParams) {
  return axios.post('/cde-collaboration/knowledgeBase/generateShareLink', data);
}
// 查看分享信息
function getShareInfoById(kbId: string, visitCode: string) {
  return axios.get(
    `/cde-collaboration/knowledgeBase/preview/${kbId}/${visitCode}`
  );
}
// 加入共享知识库
function joinKnowledgeByshared(data: any) {
  return axios.post(`/cde-collaboration/knowledgeBase/join`, data);
}

// 查看知识库成员列表
function getBaseMemberList(data: QueryBaseMemberListParams) {
  return axios.post('/cde-collaboration/knowledgeBase/queryMembers', data);
}

function uploadFile(data: any) {
  return axios({
    url: '/sys-storage/upload',
    method: 'post',
    data,
    timeout: 0,
  });
}

// 查询知识库内容
function getFolderList(data: QueryFolderListParams) {
  return axios.post('/cde-collaboration/knowledgeBase/listFolders', data);
}

// 新建文件夹
function createFolder(data: CreateFolderParams) {
  return axios.post('/cde-collaboration/knowledgeBase/createFolder', data);
}

// 重命名文件夹
function renameFolder(data: RenameFolderParams) {
  return axios.post('/cde-collaboration/knowledgeBase/renameFolder', data);
}

// 重命名文件
function renameFile(data: RenameFileParams) {
  return axios.post('/cde-collaboration/knowledgeBase/renameFile', data);
}

// 删除文件夹
function deleteFolder(data: DeleteFolderParams) {
  return axios.post('/cde-collaboration/knowledgeBase/deleteFolder', data);
}

// （上传文件后）保存文件
function saveFile(data: SaveFileParams) {
  return axios.post('/cde-collaboration/knowledgeBase/saveFile', data);
}

// 删除文件
function deleteFile(data: DeleteFileParams) {
  return axios.post('/cde-collaboration/knowledgeBase/deleteFile', data);
}

// // 查询知识库使用情况
// function getKnowledgeBaseUsage(data: KnowledgeBaseUsageParams) {
//   return axios.post('/cde-work/knowledgeBase/queryBaseFileCnt', data);
// }

// // 查看当前文件夹的完整路径
// function getFolderPath(data: QueryFolderListParams) {
//   return axios.post('/cde-work/knowledgeBase/queryFolderPath', data);
// }

// 移动
function batchMoveFile(data: any) {
  return axios.post('/cde-collaboration/knowledgeBase/batchMoveFile', data);
}

// 搜索文件列表
function getSearchFile(data: SearchFileParams) {
  return axios.post('/cde-work/knowledgeBase/searchFile', data);
}

// 导入文件
function importProjectFile(data: ImportFileParams) {
  return axios.post('/cde-collaboration/knowledgeBase/importProjectFile', data);
}

// 从项目文件导入，搜索接口
function getProjectSearchFile(data: any) {
  return axios.post('/cde-collaboration/netdisk/project/searchFile', data);
}

export {
  getBaseList,
  createBase,
  updateBase,
  deleteBase,
  removeMember,
  generateShareLink,
  getShareInfoById,
  joinKnowledgeByshared,
  getBaseMemberList,
  uploadFile,
  getFolderList,
  createFolder,
  renameFolder,
  renameFile,
  deleteFolder,
  saveFile,
  deleteFile,
  // getKnowledgeBaseUsage,
  // getFolderPath,
  batchMoveFile,
  getSearchFile,
  importProjectFile,
  getProjectSearchFile,
};
