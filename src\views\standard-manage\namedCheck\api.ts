import axios from 'axios';

// 文件及文件夹校验
export function batchCheck(data: any) {
  return axios.post('/cde-collaboration/standardCheck/check-file-name', data);
}

// 文件及文件夹校验
export function getNameStandardList(params: any) {
  return axios.get('/cde-collaboration/bimFileCheckout/listFileForNameStd', {
    params,
  });
}

// 标准命名重命名
export function renameStandard(data: any) {
  return axios.post(
    `/cde-collaboration/file/updateFileName/forStd?id=${data.id}&standardName=${data.standardName}&type=${data.type}`,
    data
  );
}
