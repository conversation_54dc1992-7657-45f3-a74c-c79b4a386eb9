<template>
  <div class="project-card">
    <!-- 文件/文件夹卡片 -->
    <div
      v-if="currentFolder.id === 'project' && !tableData.length"
      class="empty-folder"
    >
      <!-- 数据为空 -->
      <emptyFolderIcon style="width: 140px; height: 140px" />
      <div class="empty-folder-text">{{ t('knowledgenew.empty-content') }}</div>
      <a-button type="primary" @click="handleReference">{{
        t('cloud.import1')
      }}</a-button>
    </div>
    <div v-else class="card">
      <div v-if="currentFolder.id === 'project'" class="card-page">
        <div v-for="record in tableData" :key="record.projectId">
          <div class="project-title">
            <titleLogo class="title-image" />
            <div>{{ record.projectName }}</div>
          </div>
          <div class="card-list">
            <div
              v-for="item in record.folderVOList"
              :key="item.folderId || item.fileId"
              class="file-card"
              :class="{ selected: item.selected }"
            >
              <div>
                <div
                  class="file-card-header hover-show"
                  :class="{ 'always-show': item.selected }"
                >
                  <a-checkbox v-model="item.selected" class="folder-checkbox" />
                  <ProjectActionDropdown
                    :item="item"
                    :deleteBtn="true"
                    :project-id="record.projectId"
                    @action="handleAction"
                  ></ProjectActionDropdown>
                </div>
                <div class="icon-name">
                  <div @click="() => debouncedSelect(record, item)">
                    <file-image
                      :file-name="
                        item.type === 'folder' ? item.folderName : item.fileName
                      "
                      :is-sysFile="false"
                      :is-file="item.type !== 'folder'"
                      class="folder-image"
                    />
                  </div>

                  <a-tooltip
                    v-if="!item.isAdd && !item.isEdit"
                    :content="
                      (item.type === 'folder'
                        ? item.folderName
                        : item.fileName) || undefined
                    "
                  >
                    <div
                      class="file-name"
                      @click="() => debouncedSelect(record, item)"
                      >{{
                        item.type === 'folder' ? item.folderName : item.fileName
                      }}</div
                    >
                  </a-tooltip>
                  <div v-if="item.isEdit" class="file-name">
                    <a-input
                      ref="editInputRef"
                      class="folder-input"
                      v-model="item.editName"
                      style="width: 95%"
                      @blur="throttledRename(item)"
                      @keydown.enter="throttledRename(item)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ProjectCardChildren v-else />
    </div>
  </div>
  <UploadModal
    :visible="uploadModel.visible"
    :visible-type="uploadModel.type"
    :selected-folder="uploadModel.selectedFolder"
    :model-file="uploadModel.modelFile"
    @upload-single-success="singleFileSuccessCallback"
    @handle-cancel="uploadModel.visible = false"
    @upload-complete="finishUpLoad"
    @start-upload="startUpload"
  />
  <TransmitPanel
    v-model:visible="TransmitPanelVisible"
    :position="{
      top: 120,
      right: 60,
    }"
    :transmit-type="transmitType"
  />
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import { ref, toRefs, defineEmits, nextTick, watch } from 'vue';
  import i18n from '@/locale/index';
  import { useDebounceFn, useThrottleFn } from '@vueuse/core';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import titleLogo from '@/assets/images/knowledge-base/title-logo.svg';
  import UploadModal from './upload-modal.vue';
  import { addMergaFile } from '@/api/upload-file';
  import TransmitPanel from '@/views/projectSpace/file/components/transmit-panel/index.vue';
  import { finishTaskParams } from '@/store/modules/upload-file/types';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import modelViewBim from '@/utils/common/view';
  import useFolderActions from '../composables/useFolderActions';
  import ProjectActionDropdown from './project-action-dropdown.vue';
  import ProjectCardChildren from './project-card-children.vue';
  import emptyFolderIcon from '@/assets/images/knowledge-base/empty-folder1.svg';

  const { t } = i18n.global;
  const emit = defineEmits(['refreshFolder', 'handleList']);
  const { renameFileOrFolder, setTableDataEditType } = useFolderActions();
  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { project } = storeToRefs(knowledgeBaseNewStore);
  const { currentFolder, tableData } = toRefs(project.value);
  const uploadModel = ref({ visible: false, type: 0, selectedFolder: {} });
  const TransmitPanelVisible = ref(false);
  const transmitType = ref('upload');
  function handleUpload(visibleType: number, item: any = {}) {
    uploadModel.value = {
      type: visibleType,
      visible: true,
      selectedFolder: {
        projectId: item.projectId || '',
        id: item.folderId || '',
      },
      modelFile: item,
    };
  }
  async function singleFileSuccessCallback(params: finishTaskParams) {
    await addMergaFile(params)
      .catch((err) => {
        // this.changeFileArrStatus(item, 1);
      })
      .then((res: any) => {
        // 事项中成功需要给出结果
        // this.mattersSaveList.push(res.data);
        emit('refreshFolder');
        console.log('上传接口');
      })
      .finally(() => {});
  }
  // 上传完成通知列表刷新
  const finishUpLoad = () => {
    console.log('上传完成');
    knowledgeBaseNewStore.getProjectFiles();
  };
  const startUpload = () => {
    transmitType.value = 'upload';
    TransmitPanelVisible.value = true;
    uploadModel.value.visible = false;
  };
  const hiddenSlot = ref(5);
  // 算一下是不是系统文件夹下的文件
  function updateHiddenSlot(folder: any) {
    if (folder && folder.id !== 'project' && folder.sysType) {
      hiddenSlot.value = Math.min(Math.max(folder.sysType, 1), 4) || 5;
    } else {
      hiddenSlot.value = 5;
    }
  }
  // 点击进入下一文件夹
  const onSelect = (record: any, item: any) => {
    console.log(item, '我点击的文件夹');
    // 如果点击文件夹，进入下一文件夹，如果点击文件，则进入预览文件
    if (item.type === 'folder') {
      // 文件夹点击事件，进入下一层
      // 在这里需要把文件拍平
      const newItem = {
        ...item,
        projectId: record.projectId,
        projectName: record.projectName,
      };
      console.log(newItem, 'newItem');
      console.log(item, 'item');
      knowledgeBaseNewStore.setProjectCurrentFolder(newItem);
      knowledgeBaseNewStore.pushProjectBreadcrumb(newItem);
      knowledgeBaseNewStore.getPersonalFolder('project');
      knowledgeBaseNewStore.getfiles('project');
      return;
    }
    updateHiddenSlot(currentFolder.value);
    const needParams: any = {
      noIssue: [1, 2, 3, 4].includes(hiddenSlot.value),
    };
    if (item.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: item.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      Object.assign(needParams, params);
    }
    item.name = item.type === 'folder' ? item.folderName : item.fileName;
    modelViewBim(item, record.projectId, needParams);
  };
  // 防抖延迟 300ms 执行 onSelect 函数
  const debouncedSelect = useDebounceFn((record: any, item: any) => {
    // 这里是点击时要执行的操作
    onSelect(record, item);
  }, 300);
  const addInputRef = ref(null);
  const editInputRef = ref(null);
  // 添加文件夹
  const focusInput = () => {
    nextTick(() => {
      const inputs = addInputRef.value;
      console.log(inputs, 'inputs111');
      // 在 v-for 中 ref 会生成一个数组
      if (inputs && inputs.length) {
        inputs[inputs.length - 1].focus();
      }
    });
  };
  const refreshData = () => {
    // knowledgeBaseNewStore.getProjectFiles();
  };
  // 重命名
  const throttledRename = useThrottleFn(async (item: any) => {
    await renameFileOrFolder(item, item.editName);
  }, 1000);

  const handleRename = (item: any) => {
    // 重命名

    nextTick(() => {
      const inputEdits = editInputRef.value;
      // 在 v-for 中 ref 会生成一个数组
      if (inputEdits && inputEdits.length) {
        inputEdits[inputEdits.length - 1].focus();
      }
    });
  };
  // dropdown事件委托
  const handleAction = (record: any) => {
    switch (record.type) {
      case 'rename':
        // 找到原始数据，把isEdit设置为true
        setTableDataEditType(record.item, true);
        handleRename(record.item);
        break;
      case 'delete':
        // 取消引用
        knowledgeBaseNewStore.deleteProjectItems([record.item], t);
        break;
      case 'popupVisible':
        record.item.selected = record.value;
        break;
      case 'replace':
        // 替换文件
        handleUpload(0, record.item);
        break;
      default:
    }
  };
  // 引用
  const handleReference = () => {
    knowledgeBaseNewStore.setReferenceModalVisible(true);
  };
  defineExpose({ focusInput });
</script>

<style scoped lang="less">
  .card-page {
    position: absolute;
    height: calc(100% - 82px);
    width: 100%;
    overflow: auto;
  }
  .project-card {
    height: calc(100% - 64px);
    width: 100%;
  }
  .card {
    width: 100%;
    height: 100%;
  }
  :deep(.has-pointer) {
    cursor: pointer;
  }
  .card-list {
    height: calc(100% - 64px);
    overflow: auto;
    display: flex;
    flex-wrap: wrap; // 允许换行
    gap: 20px;
    align-content: flex-start; /* 关键 */
    .file-card {
      width: 133px;
      height: 130px;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 0;
      &:hover {
        background: #e8f2ff;
        border: 1px solid #d9d9d9;
        .hover-show {
          visibility: visible;
          opacity: 1;
        }
      }
      .hover-show.always-show {
        visibility: visible;
        opacity: 1;
      }
      .hover-show {
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      .file-card-header {
        width: 133px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 8px 0 8px;
      }
      .folder-image {
        width: 60px;
        height: 60px;
      }
      .file-icon {
        width: 60px;
        height: 60px;
        margin-top: 22px;
        background: rgba(242, 243, 245, 0.8);
        border-radius: 8px 8px 8px 8px;
        border: 1px dashed #e5e6eb;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 13px;

        .upload-icon {
          width: 20px;
          height: 18px;
        }
      }
      .file-name {
        font-size: 14px;
        color: #1d2129;
        line-height: 22px;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏超出容器的内容 */
        text-overflow: ellipsis;
        max-width: 133px;
        width: 100%;
        display: block;
        word-break: keep-all;
        word-break: break-word;
        text-align: center;
        margin-top: 8px;
      }
    }
    .icon-name {
      display: flex;
      flex-direction: column; /* 上下排列 */
      align-items: center; /* 水平居中 */
    }
    .icon-name > div {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    .selected {
      background: #e8f2ff;
      border: 1px solid #d9d9d9;
    }
  }
  .project-title {
    font-size: 18px;
    color: #1d2129;
    font-weight: 500;
    display: flex;
    align-items: center;
    margin: 16px 0;
    .title-image {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
  .empty-folder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; /* 垂直居中 */
    .empty-folder-text {
      font-size: 14px;
      color: #4e5969;
      margin: 24px 0 16px 0;
    }
  }
  .folder-input {
    height: 22px;
  }
</style>
