<template>
  <div class="container">
    <span
      v-for="count in totalCount"
      :key="count"
      class="icon"
      :class="{ 'active-icon': count <= activeCount }"
    ></span>
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    activeCount: {
      type: Number,
      default: 1,
    },
    totalCount: {
      type: Number,
      default: 5,
    },
  });
</script>

<style lang="less" scoped>
  .container {
    display: flex;
    align-items: center;
    margin-left: 13px;
    line-height: 32px;
  }

  .icon {
    width: 40px;
    height: 4px;
    margin-right: 8px;
    border-radius: 12px;
    background: #d9d9d9;
  }

  .active-icon {
    background: #3366ff;
  }
</style>
