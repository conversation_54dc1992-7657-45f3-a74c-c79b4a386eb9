
const colWidthMap = {
  day: 50,
  week: 150,
  month: 500,
  quarter: 500
}
export const scalesTypeMap = {
  day: [
    {
      unit: 'month',
      step: 1,
      format(date) {
        return `${date?.startDate?.getFullYear()} 年 ${date.dateIndex} 月`;
      },
      style: {
        fontSize: 16,
        color: '#1D2129',
        textAlign: 'center',
        textBaseline: 'bottom',
        fontWeight: 500,
        backgroundColor: '#E5E5E5',
        textStick: true
        // padding: [0, 30, 0, 20]
      }
    },
    {
      unit: 'day',
      step: 1,
      format(date) {
        return `${date?.startDate?.getDate()}`;
      },
      style: {
        fontSize: 16,
        color: '#1D2129',
        textAlign: 'center',
        textBaseline: 'bottom',
        fontWeight: 500,
        backgroundColor: '#E5E5E5',
        textStick: true,
        padding: 10
      }
    },
    // {
    //   unit: 'hour',
    //   step: 1,
    //   startOfWeek: 'sunday',
    //   format(date) {
    //     return `${date.dateIndex}`;
    //   },
    //   style: {
    //     fontSize: 16,
    //     color: '#1D2129',
    //     textAlign: 'center',
    //     textBaseline: 'bottom',
    //     fontWeight: 500,
    //     backgroundColor: '#E5E5E5',
    //     textStick: true
    //     // padding: [0, 30, 0, 20]
    //   }
    // },
  ], // 天
  week: [
    {
      unit: 'month',
      step: 1,
      format(date) {
        return `${date?.startDate?.getFullYear()} 年 ${date.dateIndex} 月`;
      },
      style: {
        fontSize: 16,
        color: '#1D2129',
        textAlign: 'center',
        textBaseline: 'bottom',
        fontWeight: 500,
        backgroundColor: '#E5E5E5',
        textStick: true
        // padding: [0, 30, 0, 20]
      }
    },
    {
      unit: 'week',
      step: 1,
      startOfWeek: 'sunday',
      format(date) {
        return `${date?.startDate?.getDate()}-${date?.endDate?.getDate()} (${date.dateIndex}周)`;
      },
      style: {
        fontSize: 16,
        color: '#1D2129',
        textAlign: 'center',
        textBaseline: 'bottom',
        fontWeight: 500,
        backgroundColor: '#E5E5E5',
        textStick: true
        // padding: [0, 30, 0, 20]
      }
    },
  ], // 周
  month: [
    {
      unit: 'year',
      step: 1,
      format(date) {
        return `${date.dateIndex}年`;
      },
      style: {
        fontSize: 16,
        color: '#1D2129',
        textAlign: 'center',
        textBaseline: 'bottom',
        fontWeight: 500,
        backgroundColor: '#E5E5E5',
        textStick: true
        // padding: [0, 30, 0, 20]
      }
    },
    {
      unit: 'month',
      step: 1,
      format(date) {
        return `${date.dateIndex} 月`;
      },
      style: {
        fontSize: 18,
        color: '#1D2129',
        textAlign: 'center',
        textBaseline: 'bottom',
        fontWeight: 500,
        backgroundColor: '#E5E5E5',
        textStick: true
        // padding: [0, 30, 0, 20]
      }
    },
  ], // 月
  quarter: [
    {
      unit: 'quarter',
      step: 1,
      format(date) {
        return `${date?.startDate?.getFullYear()} 年 ${date.dateIndex} 季度`;
      },
      style: {
        fontSize: 18,
        color: '#1D2129',
        textAlign: 'center',
        textBaseline: 'bottom',
        fontWeight: 500,
        backgroundColor: '#E5E5E5',
        textStick: true
        // padding: [0, 30, 0, 20]
      }
    },
    {
      unit: 'month',
      step: 1,
      format(date) {
        return `${date.dateIndex} 月`;
      },
      style: {
        fontSize: 16,
        color: '#1D2129',
        textAlign: 'center',
        textBaseline: 'bottom',
        fontWeight: 500,
        backgroundColor: '#E5E5E5',
        textStick: true
        // padding: [0, 30, 0, 20]
      }
    },
  ], // 季度
}
export const genMarkLine = () => {
  return {
    date: new Date().toLocaleDateString(),
    scrollToMarkLine: true,
    position: 'middle',
    style: {
      lineColor: '#FCA800',
      lineWidth: 1
    },
    content: '今天',
    contentStyle: {
      color: 'white'
    },
  }
};

// 计算甘特图最小列的列宽
const computeColWidth = (recordList: any, scalesType: any = 'day') => {
  let colWidth = colWidthMap[scalesType];
  let minDate = '';
  let maxDate = '';
  try {
    let gutter:any = '';
    let colNum = 0;
    // eslint-disable-next-line no-inner-declarations
    function getMinAndMaxTime (list){
      list.forEach((item)=>{
        if(!minDate) minDate = item.start
        if(item.start < minDate && minDate){
          minDate = item.start;
        }
        if(!maxDate) maxDate = item.end;
        if(item.end > maxDate && maxDate){
          maxDate = item.end;
        }
      })
    }
    if(recordList.length) getMinAndMaxTime(recordList)
    // eslint-disable-next-line default-case
    const hourTime = 1000 * 60 * 60;
    const dayTime = hourTime * 24;
    switch (scalesType){
      case 'day':
        gutter = dayTime;
        break;
      case 'week':
        gutter = dayTime * 7;
        break;
      case 'month':
        gutter = dayTime * 30;
        break;
      case 'quarter':
        gutter = dayTime * 30;
        break;
      default:
        gutter = dayTime * 7;
        break;
    }
    colNum = Math.ceil((new Date(maxDate).getTime() - new Date(minDate).getTime())/gutter)
    const elWidth:any = document.getElementById('schedule-gantt')?.getClientRects()[0]?.width;
    const result = Math.ceil(elWidth/(colNum > 0 ? colNum : 1))
    colWidth = result > colWidth ? result : colWidth;
    // console.log('colNum: ', colNum, 'elWidth: ',  elWidth, 'colWidth: ', colWidth)
  }catch (e){
    console.log(e.toString())
  }
  return {colWidth, minDate, maxDate};
}

// 计算有效的links
const parseLinks = (records:any, links:any) => {
  const finalLinks:any = [];
  JSON.parse(JSON.stringify(links)).forEach((link:any)=>{
    const formIsExit = records.find((e:any)=>{
      return link.linkedFromTaskKey === e.id;
    })
    const toIsExit = records.find((e:any)=>{
      return link.linkedToTaskKey === e.id;
    })
    if(toIsExit && formIsExit) finalLinks.push(link);
  })
  return finalLinks;
}

// 生成甘特图所需配置
const genOptions = (recordList: any, scalesType = 'week', links:any = []) => {
  const records:any = recordList || [];
  const finalLinks:any = parseLinks(records, links) || [];
  const columns = [
    {
      field: 'name',
      title: 'name',
      width: 'auto',
      sort: true,
      tree: true,
      editor: 'input',
      hide: false
    },
    {
      field: 'progress',
      title: 'progress',
      width: 'auto',
      sort: true,
      tree: true,
      editor: 'input',
      hide: true
    }
  ];
  const { colWidth, minDate, maxDate } = computeColWidth(recordList, scalesType);
  // @ts-ignore
  const option = {
    minDate: new Date(new Date(minDate).getTime() - 7*24*60*60*1000).toLocaleDateString(),
    maxDate: new Date(new Date(maxDate).getTime() + 7*24*60*60*1000).toLocaleDateString(),
    // overscrollBehavior: 'none',
    records,
    taskListTable: {
      columns,
      tableWidth: 0,
      // minTableWidth: 100,
      // maxTableWidth: 100,
      theme: {
        headerStyle: {
          borderColor: '#e1e4e8',
          borderLineWidth: 1,
          fontSize: 18,
          fontWeight: 'bold',
          color: 'red',
          bgColor: '#EEF1F5'
        },
        bodyStyle: {
          borderColor: '#e1e4e8',
          borderLineWidth: [0, 1, 0, 1],
          fontSize: 16,
          color: '#4D4D4D',
          bgColor: '#FFF'
        }
      },
      // rightFrozenColCount: 1
    },
    frame: {
      outerFrameStyle: {
        borderLineWidth: 1,
        borderColor: '#e1e4e8',
        cornerRadius: 0
      },
      verticalSplitLine: {
        "lineColor": "#e1e4e8",
        "lineWidth": 1
      },
      horizontalSplitLine: {
        "lineColor": "#e1e4e8",
        "lineWidth": 1
      },
      verticalSplitLineMoveable: false,
      verticalSplitLineHighlight: {
        lineColor: 'green',
        lineWidth: 0
      }
    },
    grid: {
      verticalLine: {
        lineWidth: 1,
        lineColor: '#e1e4e8'
      },
      horizontalLine: {
        lineWidth: 0,
        lineColor: '#e1e4e8'
      }
    },
    headerRowHeight: 40,
    rowHeight: 40,
    taskBar: {
      startDateField: 'start',
      endDateField: 'end',
      progressField: 'progress',
      resizable: true,
      moveable: false,
      // hoverBarStyle: {
      //   barOverlayColor: 'rgba(99, 144, 0, 0.4)'
      // },
      labelText:  '{progress}%',
      labelTextStyle: {
        fontFamily: 'Arial',
        fontSize: 12,
        textAlign: 'left',
        textOverflow: 'ellipsis',
        color: 'white'
      },
      barStyle: (args: any) => {
        const { taskRecord, progress } = args;
        if(taskRecord.hasChildren) {
          return {
            width: 22,
            /** 任务条的颜色 */
            barColor: '#C4DBFF',
            /** 已完成部分任务条的颜色 */
            completedBarColor: '#3C7EFF',
            /** 任务条的圆角 */
            cornerRadius: 4,
            /** 任务条的边框 */
            // borderLineWidth: 1,
            /** 边框颜色 */
            // borderColor: 'black'
          }
        }
        if(taskRecord.isCount){
          return {
            width: 8,
            /** 任务条的颜色 */
            barColor: '#E5E6EB',
            /** 已完成部分任务条的颜色 */
            completedBarColor: '#C9CDD4',
            /** 任务条的圆角 */
            cornerRadius: 2,
            /** 任务条的边框 */
            // borderLineWidth: 1,
            /** 边框颜色 */
            // borderColor: 'black'
          }
        }
        return {
          width: 22,
          /** 任务条的颜色 */
          barColor: '#BEEFEB',
          /** 已完成部分任务条的颜色 */
          completedBarColor: '#5CD6CE',
          /** 任务条的圆角 */
          cornerRadius: 4,
          /** 任务条的边框 */
          // borderLineWidth: 1,
          /** 边框颜色 */
          // borderColor: 'black'
        }
      },
      // barStyle: {
      //   width: 22,
      //   /** 任务条的颜色 */
      //   barColor: '#C4DBFF',
      //   /** 已完成部分任务条的颜色 */
      //   completedBarColor: '#3C7EFF',
      //   /** 任务条的圆角 */
      //   cornerRadius: 4,
      //   /** 任务条的边框 */
      //   // borderLineWidth: 1,
      //   /** 边框颜色 */
      //   // borderColor: 'black'
      // },
      milestoneStyle: {
        fillColor: '#FF8D7D',          // 里程碑填充颜色
        borderColor: '#FF8D7D',        // 里程碑边框颜色
        borderLineWidth: 1,            // 边框宽度
        width: 16,                     // 里程碑大小
        cornerRadius: 0,               // 圆角半径
        textOrient: 'right'            // 文本位置
      }
    },
    timelineHeader: {
      colWidth,
      backgroundColor: '#F2F3F5',
      horizontalLine: {
        lineWidth: 1,
        lineColor: '#D9D9D9'
      },
      verticalLine: {
        lineWidth: 1,
        lineColor: '#D9D9D9'
      },
      scales: scalesTypeMap[scalesType]
    },
    markLine: [
      genMarkLine()
    ],
    scrollStyle: {
      scrollRailColor: '#F2F3F5',
      visible: 'scrolling',
      width: 8,
      scrollSliderCornerRadius: 2,
      scrollSliderColor: '#D9D9D9'
    },
    dependency: {
      links: finalLinks,
      linkCreatable: false,
      linkDeletable: false,
      // 依赖线基本样式
      linkLineStyle: {
        lineColor: '#8FA2E8',
        lineWidth: 1,
        // lineDash: [4, 2]
      },
      // 选中时的样式
      linkSelectedLineStyle: {
        lineColor: '#1890ff',
        lineWidth: 2,
        shadowBlur: 4,
        shadowColor: 'rgba(24, 144, 255, 0.5)'
      },
      // 创建点样式
      linkCreatePointStyle: {
        strokeColor: '#8c8c8c',
        strokeWidth: 1,
        fillColor: '#fff',
        radius: 5
      },
      // 创建中点样式
      linkCreatingPointStyle: {
        strokeColor: '#1890ff',
        strokeWidth: 2,
        fillColor: '#fff',
        radius: 6
      },
      // 创建中线样式
      linkCreatingLineStyle: {
        lineColor: '#1890ff',
        lineWidth: 2,
        lineDash: [4, 2]
      }
    }
  };
  return option;
}
export default genOptions;