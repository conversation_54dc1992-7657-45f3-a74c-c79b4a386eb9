const viewpoint = ` <?xml version="1.0" encoding="UTF-8"?>
<VisualizationInfo Guid="e9d2a979-503b-440d-a625-1af7df0fd9a6">
	<Components>
		<ViewSetupHints SpacesVisible="false" SpaceBoundariesVisible="false" OpeningsVisible="false" />
		<Visibility DefaultVisibility="true" />
	</Components>
	<PerspectiveCamera>
		<CameraViewPoint>
			<X>171.93847173970158</X>
			<Y>11.164093624465274</Y>
			<Z>13.772423784414871</Z>
		</CameraViewPoint>
		<CameraDirection>
			<X>-0.015150753602966803</X>
			<Y>0.62082327421927364</Y>
			<Z>-0.78380413168911189</Z>
		</CameraDirection>
		<CameraUpVector>
			<X>-0.019137721849101837</X>
			<Y>0.78357045920889856</Y>
			<Z>0.62100811835078473</Z>
		</CameraUpVector>
		<FieldOfView>60</FieldOfView>
	</PerspectiveCamera>
</VisualizationInfo>`;

export default viewpoint;
