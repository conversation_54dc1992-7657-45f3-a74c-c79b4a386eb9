<template>
  <div class="list-page">
    <div class="list">
      <a-spin ref="spin" :loading="tableLoading" class="folder-table-panel">
        <a-table
          ref="folderTable"
          :columns="columns"
          :data="
            currentFolder.id === 'project'
              ? projectAllItems
              : folderChildrenDatas
          "
          :scroll="{ x: '100%', y: 'calc(100% - 16px)' }"
          :bordered="false"
          :row-selection="{
            type: 'checkbox',
            showCheckedAll: true,
            onlyCurrent: true,
          }"
          :pagination="false"
          row-key="id"
          @selection-change="selectionChange"
        >
          <template #name="{ record }">
            <div class="table-name">
              <file-image
                :file-name="
                  record.folderName || record.fileName || record.name || ''
                "
                :is-sysFile="isSysFolder(record.sysType)"
                :is-file="
                  currentFolder.id === 'project'
                    ? record.type === 'file'
                    : !!record.folderId
                "
                style="margin-right: 8px"
              />
              <a-tooltip
                v-if="!record.isAdd && !record.isEdit"
                :content="record.name"
              >
                <span
                  style="
                    display: inline-block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  "
                  class="file-name"
                  @click="debouncedSelect(record)"
                  >{{
                    record.folderName || record.fileName || record.name || ''
                  }}</span
                >
              </a-tooltip>

              <a-input
                v-else-if="record.isAdd"
                ref="addInputRef"
                v-model="record.name"
                style="width: 95%"
                @blur="() => emit('handleList', 'add', record)"
                @keydown.enter="() => emit('handleList', 'add', record)"
              />
              <a-input
                v-else-if="record.isEdit"
                ref="editInputRef"
                v-model="record.editName"
                style="width: 95%"
                @blur="throttledRename(record)"
                @keydown.enter="throttledRename(record)"
              />
            </div>
          </template>
          <template #projectName="{ record }">
            <a-tooltip :content="record.projectName || ''">
              <span
                style="
                  display: inline-block;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
                class="file-name"
                >{{ record.projectName || '' }}</span
              >
            </a-tooltip>
          </template>
          <template #version="{ record }">
            <a-tag v-if="record.version" class="tag" bordered color="cyan"
              >V{{ record.version }}</a-tag
            >
          </template>
          <!-- 转换状态 -->
          <!-- <template #status="{ record }">
            <span>{{
              record.fileToken &&
              !isPicFile(record) &&
              isRequiredConversion(record)
                ? formatStatus(record.status, record.isCombination, record)
                : ''
            }}</span>
          </template> -->
          <template #updateDate="{ record }">
            {{ record.updateDate || '' }}
          </template>
          <template #size="{ record }">
            {{ record.size ? getFileSize(record.size) : '' }}
          </template>

          <template #optional="{ record }">
            <div class="flex-row">
              <a-button
                type="text"
                size="small"
                @click="handleSelect('pathNavigation', record)"
              >
                {{ t('cloud.path-navigation') }}
              </a-button>
              <!-- <a-button
                v-if="!isSysFolder(record.sysType)"
                type="text"
                size="small"
                @click="handleSelect('rename', record)"
              >
                重命名
              </a-button> -->
              <!-- <a-button
                v-if="isWpsFile(record.name)"
                type="text"
                size="small"
                @click="handleSelect('edit', record)"
              >
                编辑
              </a-button>
              <a-button
                v-if="record.editFileType"
                type="text"
                size="small"
                @click="handleSelect('replace', record)"
              >
                替换
              </a-button> -->
              <a-popconfirm
                v-if="currentFolder.id === 'project'"
                :content="t('cloud.cancel-one-import-tips')"
                type="info"
                position="left"
                @ok="() => handleSelect('delete', record)"
              >
                <a-button type="text" size="small">{{
                  t('cloud.cancel-import')
                }}</a-button>
              </a-popconfirm>
              <TableOptBtns
                v-if="!isSysFolder(record.sysType)"
                style="margin-left: 10px"
                :item="record"
                :project-id="record.projectId"
                @action="handleAction"
              />
            </div>
          </template>
        </a-table>
      </a-spin>
    </div>
  </div>
  <UploadModal
    :visible="uploadModel.visible"
    :visible-type="uploadModel.type"
    :selected-folder="uploadModel.selectedFolder"
    :model-file="uploadModel.modelFile"
    @upload-single-success="singleFileSuccessCallback"
    @handle-cancel="uploadModel.visible = false"
    @upload-complete="finishUpLoad"
    @start-upload="startUpload"
  />
  <TransmitPanel
    v-model:visible="TransmitPanelVisible"
    :position="{
      top: 120,
      right: 60,
    }"
    :transmit-type="transmitType"
  />
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import { ref, computed, toRefs, defineEmits, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { isWpsFile } from './utils';
  import { isSysFolder } from '@/views/projectSpace/file/utils';
  import { wpsViewHandle } from '@/hooks/wps';
  import i18n from '@/locale/index';
  import { useDebounceFn, useThrottleFn } from '@vueuse/core';
  import { Message } from '@arco-design/web-vue';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import useFileStore from '@/store/modules/file/index';
  import { getFileSize } from '@/utils/file';
  import { getUserId } from '@/utils/auth';
  import modelViewBim from '@/utils/common/view';
  import { setLocalstorage } from '@/utils/localstorage';
  import { reConvertApi } from '@/views/projectSpace/file/api';
  import { addMergaFile } from '@/api/upload-file';
  import { storeCurrentProjectId } from '@/api/storage-project';
  import UploadModal from './upload-modal.vue';
  import useFolderActions from '../composables/useFolderActions';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import TransmitPanel from '@/views/projectSpace/file/components/transmit-panel/index.vue';
  import TableOptBtns from './table-opt-btns.vue';

  const { t } = i18n.global;
  const emit = defineEmits(['refreshFolder', 'handleList']);
  const { renameFileOrFolder, setTableDataEditType } = useFolderActions();
  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { project, projectAllItems, folderChildrenDatas } = storeToRefs(
    knowledgeBaseNewStore
  );
  const { currentFolder, tableLoading } = toRefs(project.value);
  const fileStore = useFileStore();
  const userId = getUserId() || '';
  const router: any = useRouter();
  const columns = computed(() => {
    return [
      {
        title: t('file-manage.name'),
        dataIndex: 'name',
        slotName: 'name',
        // sortable: {
        //   sortDirections: ['ascend', 'descend'],
        // },
        width: 260,
      },
      {
        title: t('cloud.relation-project'),
        width: 260,
        dataIndex: 'projectName',
        slotName: 'projectName',
      },
      {
        title: t('file-manage.size'),
        width: 90,
        dataIndex: 'size',
        slotName: 'size',
      },
      // {
      //   title: t('file-manage.transition-status'),
      //   dataIndex: 'status',
      //   slotName: 'status',
      //   width: 140,
      // },
      {
        title: t('file-manage.version'),
        dataIndex: 'version',
        slotName: 'version',
        width: 120,
      },
      {
        title: t('file-manage.update-date'),
        width: 180,
        dataIndex: 'updateDate',
        slotName: 'updateDate',
      },
      {
        title: t('file-manage.operation'),
        slotName: 'optional',
        titleSlotName: 'optionalTitle',
        width: 220,
      },
    ];
  });
  function selectionChange(rowkeys: string[]) {
    knowledgeBaseNewStore.setProjectSelectedByRowKeys(rowkeys);
    //
    // fileStore.setSelectedTableRowkeys(rowkeys);
  }
  const addInputRef = ref(null);
  const editInputRef = ref(null);
  const hiddenSlot = ref(5);
  // 算一下是不是系统文件夹下的文件
  function updateHiddenSlot(folder: any) {
    if (folder && folder.id !== 'project' && folder.sysType) {
      hiddenSlot.value = Math.min(Math.max(folder.sysType, 1), 4) || 5;
    } else {
      hiddenSlot.value = 5;
    }
  }
  // 点击进入下一文件夹
  const onSelect = (item: any) => {
    // 如果点击文件夹，进入下一文件夹，如果点击文件，则进入预览文件
    if (item.type === 'folder' || !item.folderId) {
      console.log('项目列表点击文件夹', item);
      // 文件夹点击事件，进入下一层
      knowledgeBaseNewStore.setProjectCurrentFolder(item);
      knowledgeBaseNewStore.pushProjectBreadcrumb(item);
      knowledgeBaseNewStore.getPersonalFolder('project');
      knowledgeBaseNewStore.getfiles('project');
      return;
    }
    updateHiddenSlot(currentFolder.value);
    const needParams: any = {
      noIssue: [1, 2, 3, 4].includes(hiddenSlot.value),
    };
    if (item.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: item.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      Object.assign(needParams, params);
    }
    console.log(item, '需要预览的文件');
    modelViewBim(item, item.projectId as string, needParams);
  };
  // 防抖延迟 300ms 执行 onSelect 函数
  const debouncedSelect = useDebounceFn((item: any) => {
    // 这里是点击时要执行的操作
    onSelect(item);
  }, 300);
  // 添加文件夹
  const focusInput = () => {
    nextTick(() => {
      const inputs = addInputRef.value;
      if (inputs) {
        inputs.focus();
      }
    });
  };
  // 重命名
  const throttledRename = useThrottleFn(async (item: any) => {
    await renameFileOrFolder(item, item.editName);
  }, 1000);
  const handleRename = (item: any) => {
    // 重命名

    nextTick(() => {
      const inputEdits = editInputRef.value;
      // 在 v-for 中 ref 会生成一个数组
      if (inputEdits && inputEdits.length) {
        inputEdits[inputEdits.length - 1].focus();
      }
    });
  };

  // 替换
  const uploadModel = ref({ visible: false, type: 0, selectedFolder: {} });
  const TransmitPanelVisible = ref(false);
  const transmitType = ref('upload');
  function handleUpload(visibleType: number, item: any = {}) {
    uploadModel.value = {
      type: visibleType,
      visible: true,
      selectedFolder: {
        projectId: item.projectId || '',
        id: item.folderId,
      },
      modelFile: item,
    };
  }
  async function singleFileSuccessCallback(params: finishTaskParams) {
    await addMergaFile(params)
      .catch((err) => {
        // this.changeFileArrStatus(item, 1);
      })
      .then((res: any) => {
        // 事项中成功需要给出结果
        // this.mattersSaveList.push(res.data);
        emit('refreshFolder');
        console.log('上传接口');
      })
      .finally(() => {});
  }
  // 上传完成通知列表刷新
  const finishUpLoad = () => {
    console.log('上传完成');
    if (currentFolder.value.id === 'project') {
      knowledgeBaseNewStore.getProjectFiles();
    } else {
      knowledgeBaseNewStore.getfiles('project');
    }
  };
  const startUpload = () => {
    transmitType.value = 'upload';
    TransmitPanelVisible.value = true;
    uploadModel.value.visible = false;
  };
  const handleSelect = (type: any, record: any) => {
    switch (type) {
      case 'pathNavigation':
        // 路径定位，跳转到项目文件
        // 需要判断是顶层文件夹，还是子文件夹，顶层文件夹每一项里面有idPath,子文件夹接口文件不会返回idPath，需要取当前文件夹的idPath
        // 存储项目文件页面需要的路径
        console.log('点击路径定位的文件信息', record);
        fileStore.setCurrentIdPath(
          record?.idPath || currentFolder.value.idPath || ''
        );
        setLocalstorage(`last_project_${userId}`, record.projectId);
        storeCurrentProjectId(record.projectId);
        // 跳转到项目文件
        router.push({
          name: 'file',
          params: {
            projectId: record.projectId,
          },
        });
        break;
      case 'rename':
        // 找到原始数据，把isEdit设置为true
        setTableDataEditType(record, true);

        handleRename(record);
        break;
      case 'delete':
        // 取消引用
        knowledgeBaseNewStore.deleteProjectItems([record], t);
        break;
      case 'edit':
        wpsViewHandle(record, 'edit', 'admin');
        break;
      case 'replace':
        // 替换文件
        handleUpload(0, record);
        break;
      default:
    }
  };
  // dropdown事件委托
  const handleAction = (record: any) => {
    switch (record.type) {
      case 'replace':
        // 替换文件
        handleSelect('replace', record.item);
        break;
      case 'edit':
        // 编辑
        handleSelect('edit', record.item);
        break;
      case 'rename':
        // 重命名
        handleSelect('rename', record.item);
        break;
      default:
    }
  };
  defineExpose({ focusInput });
</script>

<style scoped lang="less">
  .list-page {
    position: absolute;
    height: calc(100% - 16px);
    width: 100%;
    overflow: auto;
  }

  .list {
    height: calc(100% - 64px);
    overflow: auto;
  }
  :deep(.arco-table-content .arco-scrollbar:nth-child(2)) {
    height: 100%;
  }
  :deep(.arco-table-header + .arco-scrollbar-track-direction-horizontal) {
    display: none;
  }
  .folder-table-panel {
    width: 100%;
    height: 100%;
    padding: 0 12px;
  }
  :deep(.arco-table-container) {
    height: calc(100% - 40px);
  }
  .file-name {
    cursor: pointer;
    transition: color 0.2s;
  }
  .file-name:hover {
    color: rgb(22, 93, 255);
  }
  .table-name {
    display: flex;
    align-items: center;
  }
  .flex-row {
    display: flex;
    align-items: center;
  }
  .flex-row :deep(.arco-btn) {
    padding-left: 0;
    padding-right: 8px;
  }
</style>
