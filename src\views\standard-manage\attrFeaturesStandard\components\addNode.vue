<template>
  <div>
    <a-modal
      v-if="visible"
      :visible="visible"
      :title="
        type === 'edit' ? t('list.options.btn.edit') : t('list.options.btn.new')
      "
      title-align="start"
      :unmount-on-close="true"
      :mask-closable="false"
      width="800px"
      @cancel="handleBeforeCancel"
      @ok="handleBeforeOk"
    >
      <a-spin style="width: 100%" :loading="loading">
        <a-form ref="formRef" :model="form" auto-label-width layout="vertical">
          <a-row>
            <a-col :span="24" class="title-name">{{
              $t('standard.basic-information')
            }}</a-col>
          </a-row>
          <a-row :gutter="[20, 0]">
            <a-col :span="12"
              ><a-form-item
                field="name"
                :label="$t('standard.name')"
                validate-trigger="input"
                :rules="[
                  {
                    required: true,
                    message: $t('please-enter'),
                  },
                ]"
              >
                <a-input
                  v-model="form.name"
                  :placeholder="$t('please-enter')"
                /> </a-form-item
            ></a-col>
            <a-col :span="12"
              ><a-form-item
                field="infoTypeCode"
                :label="$t('standard.information-category')"
                :rules="[
                  {
                    required: true,
                    message: $t('please-select'),
                  },
                ]"
              >
                <a-select
                  v-model="form.infoTypeCode"
                  :placeholder="$t('please-select')"
                >
                  <a-option
                    v-for="value of infoCategory"
                    :key="value.code"
                    :value="value.code"
                    >{{ value.name }}</a-option
                  >
                </a-select>
              </a-form-item></a-col
            >
            <a-col :span="12"
              ><a-form-item :label="$t('standard.ifc-type')">
                <a-input
                  v-model="form.ifcType"
                  :placeholder="$t('please-enter')"
                /> </a-form-item
            ></a-col>
          </a-row>

          <a-row :gutter="[20, 0]">
            <a-col :span="24" class="title-name">{{
              $t('standard.precision-range')
            }}</a-col>
            <a-col v-if="standPrecision?.length" :span="24">
              <a-form-item label="">
                <a-checkbox-group
                  v-if="standPrecision?.length"
                  v-model="form.nodeId"
                  direction="horizontal"
                  @change="setPrecisionCheck"
                >
                  <a-checkbox
                    v-for="item of standPrecision"
                    :key="item.code"
                    :value="item.code"
                    >{{ item.name }}</a-checkbox
                  >
                </a-checkbox-group>
                <!-- <a-empty v-else description="暂无精度数据" /> -->
              </a-form-item>
            </a-col>
            <a-col
              v-for="(item, index) in precisionList"
              :key="index"
              :span="12"
              ><a-form-item :label="item['label']">
                <a-select
                  v-model="item[Object.keys(item)[0]]"
                  :placeholder="$t('please-select')"
                  @change="changePrecision"
                >
                  <a-option
                    v-for="value of precisionData"
                    :key="value.code"
                    :value="value.code"
                    >{{ value.name }}</a-option
                  >
                </a-select>
              </a-form-item></a-col
            >
          </a-row>
          <a-row :gutter="[20, 0]">
            <a-col :span="12"
              ><a-form-item
                field="valueTypeCode"
                :label="$t('standard.type')"
                :rules="[
                  {
                    required: true,
                    message: $t('please-select'),
                  },
                ]"
              >
                <a-select
                  v-model="form.valueTypeCode"
                  :placeholder="$t('please-select')"
                >
                  <a-option
                    v-for="value of dataType"
                    :key="value.code"
                    :value="value.code"
                    >{{ value.name }}</a-option
                  >
                </a-select>
              </a-form-item></a-col
            >
            <a-col :span="12"
              ><a-form-item :label="$t('standard.unit')">
                <a-tree-select
                  v-model="form.unitCode"
                  :field-names="{
                    title: 'name',
                    children: 'children',
                    key: 'code',
                  }"
                  :data="unit"
                  :placeholder="$t('please-select')"
                ></a-tree-select> </a-form-item
            ></a-col>
            <a-col :span="12"
              ><a-form-item :label="$t('standard.preset-value')">
                <a-input
                  v-model="form.example"
                  :placeholder="$t('please-enter')"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12" class="pad20 multi-box">
              <a-button
                v-if="exampleType === '1'"
                type="primary"
                @click="addAttr"
                >{{ $t('standard.add') }}</a-button
              >
              <a-radio-group v-model="exampleType">
                <a-radio value="0">{{ $t('standard.single-value') }}</a-radio>
                <a-radio value="1">{{ $t('standard.multi-value') }}</a-radio>
              </a-radio-group>
            </a-col>
            <a-col
              v-if="exampleType === '1' && tags?.length"
              :span="24"
              class="tagBox"
            >
              <a-space>
                <a-tag
                  v-for="tag of tags"
                  :key="tag"
                  color="blue"
                  closable
                  @close="handleRemove(tag)"
                >
                  {{ tag }}
                </a-tag>
              </a-space>
            </a-col>
            <a-col :span="12">
              <a-form-item
                :label="$t('standard.description')"
                validate-trigger="input"
              >
                <a-textarea
                  v-model="form.description"
                  :placeholder="$t('please-enter')"
                ></a-textarea> </a-form-item></a-col
          ></a-row>
          <a-row> </a-row>
        </a-form>
      </a-spin>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { ref, watch } from 'vue';
  import useStandardManageStore from '@/store/modules/standard-manage/index';
  import { storeToRefs } from 'pinia';
  import { saveAttribute, editAttribute } from '../api';
  import cloneDeep from 'lodash/cloneDeep';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const store = useStandardManageStore();
  const {
    standPrecision,
    selTreeData,
    precisionData,
    infoCategory,
    unit,
    dataType,
  } = storeToRefs(store);

  const precisionList: any = ref([]);
  const precisionListAll: any = ref([]);

  // 勾选精度设置表单京都项
  const setPrecisionCheck = () => {
    precisionList.value = [];

    // 根据勾选的精度  添加对应的select选择框
    precisionList.value = form.value.nodeId.map((key: any) => {
      const item = standPrecision.value.find((item: any) => item.code === key);
      return {
        [key]: '', // 动态键，其值为空字符串
        label: item ? item.name : '', // 如果找到对应的对象，使用其 name 作为 label，否则为空字符串
      };
    });

    // 记住所选值
    const a = precisionListData.value;
    const b = precisionList.value;

    const aMap = new Map();
    a?.forEach((item: any) => {
      Object.keys(item).forEach((key) => {
        aMap.set(key, item[key]);
      });
    });

    b?.forEach((item: any) => {
      const firstKey = Object.keys(item)[0];
      if (aMap.has(firstKey)) {
        item[firstKey] = aMap.get(firstKey);
      }
    });

    precisionList.value = b;
  };

  const precisionListData = ref();
  const changePrecision = () => {
    console.log(precisionList, 456);
    precisionListData.value = cloneDeep(precisionList.value);
  };

  // 获取所有精度数据
  const setPrecisionList = () => {
    standPrecision.value.forEach((key: any, index: any) => {
      precisionListAll.value.push({
        [key]: '',
        label: standPrecision.value[index].name,
      });
    });
  };

  store.setDictionaryData('stdPrecision');

  const loading: any = ref(false);

  const props: any = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      required: false,
    },
    type: {
      type: String,
      required: false,
    },
  });

  const form = ref<any>({
    name: '',
    example: '',
  });

  const emit = defineEmits(['update:visible', 'refresh']);

  const handleBeforeCancel = () => {
    emit('update:visible', false);
  };

  const formRef = ref(null);

  const exampleType: any = ref('0');
  const tags: any = ref([]);

  const handleBeforeOk = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      let flag = true;
      precisionList.value.forEach((obj: any) => {
        const firstKey = Object.keys(obj)[0];
        const firstValue = obj[firstKey];
        if (firstValue === '') {
          flag = false;
        }
      });

      if (!flag) {
        Message.info(t('standard.please-fill-accuracy'));
        return;
      }
      // 预设值处理
      let example: any = '';
      if (exampleType.value === '0') example = form.value.example;
      else example = tags.value.join('|');

      const precision: any = {};

      // 设置precision参数
      precisionList.value.forEach((obj: any) => {
        const firstKey = Object.keys(obj)[0];
        const firstValue = obj[firstKey];
        precision[firstKey] = firstValue;
      });

      const param: any = ref({
        classId: selTreeData?.value?.id,
        description: form.value.description,
        example,
        ifcType: form.value.ifcType,
        infoTypeCode: form.value.infoTypeCode,
        multiValue: exampleType.value === '1',
        precision,
        dataType: form.value.dataType,
        name: form.value.name,
        unitCode: form.value.unitCode,
        valueTypeCode: form.value.valueTypeCode,
      });

      loading.value = true;
      let resData: any = null;
      try {
        // 新增
        if (props.type === 'add') {
          resData = await saveAttribute(param.value);
        } else {
          if (form.value?.id) param.value.id = form.value.id;
          // 编辑
          resData = await editAttribute(param.value);
        }
        if (resData) {
          Message.success(
            props.type === 'add' ? t('create-successful') : t('edit-successful')
          );
          emit('refresh');
          emit('update:visible', false);
        }
      } catch (err) {
        console.log(err);
      } finally {
        loading.value = false;
      }
    }
  };

  watch(
    () => props.visible,
    async () => {
      if (props.type === 'add') {
        form.value = {};
        form.value.nodeId = [];

        // 添加时精度默认全选
        standPrecision.value.forEach((item: any) => {
          form.value.nodeId.push(item.code);
        });
        setPrecisionCheck();
      } else {
        // 基础信息回显
        form.value = props.data;
        await setPrecisionList();

        // ---------------精度回显--------------------------
        precisionList.value = [];
        // 获取回显数据中的precision 添加key的数组
        const keyArr: any = [];
        props.data.precisionList?.forEach((item: any) => {
          keyArr.push(item.code);
        });

        // 从总数据中筛选出回显需要的数据
        standPrecision.value.forEach((item: any) => {
          if (keyArr.includes(item.code)) {
            precisionList.value.push(item);
          }
        });

        // 复选框回显
        form.value.nodeId = keyArr;
        // 根据复选框设置
        setPrecisionCheck();

        // 添加下拉精度的值
        props.data.precisionList?.forEach((item: any, index: any) => {
          precisionList.value[index][item.code] = item.requirement;
        });

        // 记住选项值所需
        precisionListData.value = precisionList.value;

        // ---------------精度结束--------------------------

        // // 预设值回显展示
        if (form.value.multiValue) {
          tags.value = props.data.example.split('|');
          exampleType.value = '1';
          form.value.example = '';
        } else {
          exampleType.value = '0';
        }
      }
    },
    {
      immediate: true,
    }
  );

  const handleRemove = (key: any) => {
    tags.value = tags.value.filter((tag: any) => tag !== key);
  };

  // 添加
  const addAttr = () => {
    if (form.value.example) {
      tags.value.push(form.value.example);
      form.value.example = '';
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'AddNode',
  };
</script>

<style lang="less" scoped>
  .appMapLine {
    display: block;
    margin-bottom: 4px;
    padding: 0 8px;
    border-left: 5px solid #1890ff;
  }

  .pad20 {
    padding-left: 20px;
  }

  .tagBox {
    width: 100%;
    padding-bottom: 20px;
    padding-left: 72px;
    word-wrap: break-word;
    :deep(.arco-space) {
      flex-wrap: wrap;
      .arco-space-item {
        margin-bottom: 8px;
      }
    }
  }
  .title-name {
    font-size: 16px;
    margin-bottom: 8px;
  }
  .multi-box {
    padding-top: 32px;
  }
  :deep(.arco-select),
  :deep(.arco-textarea),
  :deep(.arco-input-wrapper),
  :deep(.arco-select-view-single) {
    border: 1px solid #c9cdd4 !important;
    background-color: #fff;
    // border-radius: 8px;
  }
</style>
