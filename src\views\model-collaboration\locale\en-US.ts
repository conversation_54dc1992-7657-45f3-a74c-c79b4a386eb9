export default {
  'model-collaboration.total': 'Total',
  'model-collaboration.cancel': 'Cancel',
  'model-collaboration.selectTeam': 'Select Team',
  'model-collaboration.description': 'Description',
  'model-collaboration.file': 'File',
  'model-collaboration.totalFiles': 'Total Files: ',
  'model-collaboration.addFiles': 'Add Files',
  'model-collaboration.please-input': 'Please Input',

  'model-collaboration.search': 'Search',
  'model-collaboration.clear': 'Clear',
  'model-collaboration.create': 'Create',
  'model-collaboration.name': 'Name',
  'model-collaboration.modelSource': 'Model Source',
  'model-collaboration.creator': 'Creator',
  'model-collaboration.createTime': 'Create Time',
  'model-collaboration.merge-models': 'Merge Models',
  'model-collaboration.collision-check': 'Collision Detection',
  'model-collaboration.check-name': 'Check Name',
  'model-collaboration.modelFile': 'Model File',
  'model-collaboration.singleModelFile': 'single ModelFile',
  'model-collaboration.twoModelFile': 'two ModelFile',
  'model-collaboration.modelA': 'modelA',
  'model-collaboration.modelB': 'modelB',
  'model-collaboration.selectFile': 'Select File',
  'model-collaboration.fileName': 'File Name',
  'model-collaboration.version': 'Version',
  'model-collaboration.generateInTeam': 'Generate in Team',
  'model-collaboration.please-select': 'Please Select',

  'model-collaboration.enter': 'Enter',
  'model-collaboration.collisionDetection': 'Collision Detection',
  'model-collaboration.createNewCollisionDetection':
    'Create New Collision Detection',
  'model-collaboration.index': 'Index',

  'model-collaboration.model': 'Model',
  'model-collaboration.check': 'Check',
  'model-collaboration.create-merge-file': 'Create Merge File',
  'model-collaboration.merge-file-name': 'Merge File Name',
  'model-collaboration.enter-title': 'Please enter title',
  'model-collaboration.generate-in-team': 'Generate in Team',
  'model-collaboration.select-team': 'Please select team',
  'model-collaboration.file-count': 'files',
  'model-collaboration.add-file': 'Add File',
  'model-collaboration.create-success': 'Create successfully',
  'model-collaboration.select-file': 'Please select file',
  'model-collaboration.select-least-2-models':
    'Please select at least two 3D models',
  'model-collaboration.select-3d-model':
    '"{{ modelName }}" cannot be merged. Please select a 3D model',
  'model-collaboration.model-cannot-merge':
    '"{{ modelName }}" cannot be merged. Please select or upload again',

  'model-collaboration.files-source': 'Files Source',
  'model-collaboration.creation-time': 'Creation Time',
  'model-collaboration.team': 'Team',
  'model-collaboration.add': 'Add',
  'model-collaboration.success': 'Success',
  'model-collaboration.conversion-was-not-successful':
    'The conversion was not successful and the mold cannot be closed',
  'model-collaboration.not-support-merge-model':
    'The combination of semantic model and non-semantic model files is not supported',
  'model-collaboration.select-multi-model':
    'Please select at least two 3D model files',
  'model-collaboration.unable-select3d':
    'Unable to merge, please select 3D model file',
  'model-collaboration.unable-reselect':
    'Unable to merge, please re-select or upload',
  'model-collaboration.fileNameRequired': 'Input Name',

  'model-collaboration.back': 'Back',
  'model-collaboration.next': 'Next',
  'model-collaboration.confirm': 'Confirm',
  'model-collaboration.first-step': 'First Step',
  'model-collaboration.second-step': 'Second Step',
  'model-collaboration.third-step': 'Third Step',
  'model-collaboration.select-model': 'Select Model',
  'model-collaboration.select-range': 'Select Range',
  'model-collaboration.set-up-parameters': 'Set Up Parameters',
  'model-collaboration.model-tree-presentation': 'Model Tree Presentation',
  'model-collaboration.generated-position': 'Generated Position',
  'model-collaboration.unable-collision':
    'This model cannot be collision checked, please re-select and upload',
  'model-collaboration.at-least-one': 'Please select at least one',
  'model-collaboration.Collision-type': 'Collision Type',
  'model-collaboration.hard-collision': 'Hard Collision',
  'model-collaboration.Gap-collision': 'Gap Collision',
  'model-collaboration.tolerance-tip':
    'Tip: When the object spacing is less than or equal to (≤) the collision tolerance, the gap collision is reported. Only a number can be entered, and it cannot start with 0',
  'model-collaboration.Tolerance-collision': 'Tolerance of collision',
  'model-collaboration.Checking-Rules': 'Checking Rules',
  'model-collaboration.forbid-same-model':
    'The components within the same model are forbidden to participate in collisions',
  'model-collaboration.vertical-tip':
    'When checked: "single model inside" and "original model inside the table file" will not collide, only the collision between the original models in the table file',
  'model-collaboration.forbid-same-layer':
    'The components in the same layer are forbidden to participate in the collision',
  'model-collaboration.forbid-same-class':
    'A certain class of components is prohibited from participating in the collision',
  'model-collaboration.Select-component-category':
    'Select a component category',
  'model-collaboration.forbid-collision':
    'Collision between two types of components is prohibited',
  'model-collaboration.Select-first-type': 'Select the first type of component',
  'model-collaboration.Select-second-type':
    'Select the second type of component',
  'model-collaboration.Validation-failure': 'Validation Failure',
  'model-collaboration.conversion-was-not-successful-no-clash':
    'Conversion was not successful, no clash detection possible',
  'model-collaboration.dgn-files-are-not-supported':
    'DGN files are not supported for model collision',
  'model-collaboration.not-a-semantic-model':
    'Not a semantic model, clash detection not possible',
  'model-collaboration.operate': 'Operate',
  'model-collaboration.delete': 'Delete',
  'model-collaboration.delete-title': 'Confirm Deletion',
  'model-collaboration.delete-content': 'Confirm to delete this file?',
};
