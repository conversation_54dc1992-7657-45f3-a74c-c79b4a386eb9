<template>
  <div class="standard">
    <a-button type="outline" style="margin: 20px 0 0 0" @click="toUrl"
      ><template #icon> <icon-arrow-left /> </template
      >{{ $t('standard-setting.back') }}</a-button
    >
    <a-divider margin="20px 0" />

    <a-card class="general-card">
      <a-row class="standard-search" style="margin-bottom: 16px">
        <!-- <table-title
            :title="$t('standard-setting.standard-information')"
          ></table-title>
          <a-button type="text" @click="standardFormEdit"
            ><icon-edit />{{ $t('standard-setting.edit') }}</a-button
          > -->

        <a-col :span="17" class="card-title">
          <table-title
            style="display: inline-block; width: 100px"
            :title="$t('standard-setting.standard-information')"
          ></table-title>
          <a-button type="text" @click="standardFormEdit"
            ><icon-edit />{{ $t('standard-setting.edit') }}</a-button
          >
        </a-col>
      </a-row>

      <a-form
        ref="enclosureFormRef"
        :model="standardForm"
        disabled
        layout="vertical"
      >
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item
              label-col-flex="100px"
              :label="$t('standard-setting.standard-chinese-name')"
            >
              <a-input
                v-model="standardForm.name"
                :placeholder="$t('standard-setting.please-enter')"
                :max-length="50"
                show-word-limit
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label-col-flex="100px"
              :label="$t('standard-setting.standard-english-name')"
            >
              <a-input
                v-model="standardForm.englishName"
                :placeholder="$t('standard-setting.please-enter')"
                :max-length="200"
                show-word-limit
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label-col-flex="100px"
              :label="$t('standard-setting.standard-number')"
            >
              <a-input
                v-model="standardForm.code"
                :placeholder="$t('standard-setting.please-enter')"
                :max-length="currentLocale === 'en-US' ? 200 : 50"
                show-word-limit
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item
              label-col-flex="100px"
              field="message"
              :label="$t('standard-setting.description')"
            >
              <a-textarea
                v-model="standardForm.description"
                :placeholder="$t('standard-setting.please-enter')"
                :max-length="500"
                show-word-limit
                :auto-size="{
                  minRows: 2,
                }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <div>
        <a-row style="margin-bottom: 16px">
          <a-col :span="12" class="card-title">
            <table-title
              :title="$t('standard-setting.naming-standards-set')"
            ></table-title>
            <!-- <img src="@/assets/images/dashboard/<EMAIL>" />
            <span> {{ $t('standard-setting.standards-set') }} </span> -->
          </a-col>
          <a-col :span="12" style="text-align: right">
            <span style="margin-right: 16px">{{
              $t('standard-setting.delimiter')
            }}</span>
            <a-select
              v-model="standardType"
              style="width: 100px"
              class="mode-select"
              @change="changeType"
            >
              <a-option
                v-for="item of standardTypeOption"
                :key="item.code"
                :value="item.code"
                :label="item.name"
              />
            </a-select>
          </a-col>
        </a-row>

        <a-row style="margin-bottom: 16px" :gutter="24">
          <a-col :span="24">
            <div class="preview">
              <span v-for="(item, index) in AttributeList" :key="index">
                {{ item.name }}
                <span v-if="index < AttributeList.length - 1">{{
                  standardType
                }}</span></span
              >
            </div>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="24">
            <div class="attrbox">
              <div class="attrSpan" @mouseleave="tapIndex = -1">
                <draggable
                  v-if="AttributeList.length > 0"
                  v-model="AttributeList"
                  item-key="id"
                  @sort="sort"
                >
                  <template #item="{ element, index }">
                    <span
                      :key="element.id"
                      :class="{ active: index === isActive }"
                      :value="element.id"
                      @mouseover="tapIndex = index"
                      @click="selAttribute(element.id, index)"
                      >{{ element.name }}
                      <!--  :class="{ show: tapIndex === index }" -->
                      <img
                        style="margin-left: 10px"
                        class="show"
                        src="@/assets/images/standard/tips.png"
                        @click.stop="removeAttr(element)"
                      />
                    </span>
                  </template>
                </draggable>
              </div>
              <a-button type="text" @click="addAttribute">
                <template #icon>
                  <icon-plus-circle />
                </template>
                {{ $t('standard-setting.add') }}
              </a-button>
            </div>
          </a-col>
        </a-row>
        <a-row v-if="AttributeList.length > 0" :span="24">
          <a-form
            ref="enclosureFormRef"
            :model="attributeForm"
            disabled
            layout="vertical"
          >
            <a-row :gutter="24" justify="start">
              <a-col :span="12">
                <div class="title">
                  <div class="text">
                    <span class="text-title">{{
                      $t('standard-setting.property-details')
                    }}</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="12" style="text-align: right">
                <!-- <a-button type="text" @click="edit(attributeForm)">编辑</a-button> -->
                <span style="margin-right: 16px">{{
                  $t('standard-setting.attribute-display-type')
                }}</span>
                <a-select
                  v-model="attrShowTypeValue"
                  style="width: 100px"
                  class="mode-select"
                  @change="changeAttrType"
                >
                  <a-option
                    v-for="item of attrShowType"
                    :key="item.code"
                    :value="item.code"
                    :label="item.name"
                  />
                </a-select>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item
                  label-col-flex="100px"
                  :label="$t('standard-setting.name')"
                >
                  <a-input
                    v-model="attributeForm.name"
                    :placeholder="$t('standard-setting.please-enter')"
                    :max-length="20"
                    show-word-limit
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label-col-flex="100px"
                  :label="$t('standard-setting.type')"
                >
                  <a-select v-model="attributeForm.type" class="mode-select">
                    <a-option
                      v-for="item of attrTypeOption"
                      :key="item.code"
                      :value="item.code"
                      :label="item.name"
                    />
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <!-- <a-row :gutter="24">
            <a-col :span="24">
              <div class="title">
                <div class="text">
                  <span class="text-title">属性值设置</span>
                </div>
              </div>
            </a-col>
          </a-row> -->
            <a-row :gutter="24">
              <a-col v-if="attributeForm.type == 1" :span="12">
                <a-form-item
                  label-col-flex="100px"
                  field="dimension"
                  :label="$t('standard-setting.rule-name')"
                >
                  <a-select
                    v-model="attributeForm.ruleListId"
                    multiple
                    class="mode-select"
                  >
                    <a-option
                      v-for="item of attributeForm.ruleList"
                      :key="item.id"
                      :value="item.id"
                      :label="item.name"
                    />
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col v-if="attributeForm.type == 2" :span="12">
                <a-form-item
                  label-col-flex="100px"
                  field="dimension"
                  :label="$t('standard-setting.dimension-name')"
                >
                  <a-select
                    v-model="attributeForm.dimensionId"
                    class="mode-select"
                  >
                    <a-option
                      v-for="item of allOptions.dimensionOptions"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    />
                  </a-select>
                </a-form-item>
              </a-col>
              <!-- <a-col :span="12">
              <a-form-item label-col-flex="100px" label="是否为空">
                <a-select
                  v-model="attributeForm.emptyStatus"
                  class="mode-select"
                >
                  <a-option
                    v-for="item of emptyStatusOption"
                    :key="item.code"
                    :value="item.code"
                    :label="item.name"
                  />
                </a-select>
              </a-form-item> 
            </a-col>-->
            </a-row>
          </a-form>
        </a-row>
      </div>
    </a-card>
    <!-- 绑定属性 -->
    <CreateAttribute ref="createAttributeRef" @refresh="updateData">
    </CreateAttribute>

    <!-- 编辑标准 -->
    <CreateStandard
      ref="editStandardRef"
      @refresh="updateData"
    ></CreateStandard>
  </div>
</template>

<script lang="ts" setup>
  import draggable from 'vuedraggable';
  import { useRoute, useRouter } from 'vue-router';
  import { ref, reactive, computed, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import CreateStandard from '@/views/standard-manage/standard-list/standard/components/create-standard.vue';
  import CreateAttribute from './components/create-attribute.vue';
  import useLoading from '@/hooks/loading';

  import {
    queryDimensionList,
    queryRuleList,
  } from '@/views/standard-manage/standard-list/metas/api';
  import { addStandard } from '@/views/standard-manage/standard-list/standard/api';
  import {
    standarDetailList,
    standardList,
    standardParams,
    getAttribute,
    getAttributeDetail,
    standardFormInter,
    removeAttribute,
    attributeSort,
    saveAttribute,
  } from './api';
  import { useI18n } from 'vue-i18n';
  import TableTitle from '@/components/table-title/index.vue';
  import useLocale from '@/hooks/locale';
  import { useUserStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';

  const { currentLocale } = useLocale();

  const { t } = useI18n();
  const router = useRouter();
  const editStandardRef = ref<any>(null);
  const createAttributeRef = ref<any>(null);
  const route = useRoute();
  const { setLoading } = useLoading();

  // 建筑工程标准类型
  const standardTypeOption = ref([
    {
      name: '-',
      code: '-',
    },
    {
      name: '_',
      code: '_',
    },
  ]);

  // 属性显示标准
  const attrShowType = computed(() => {
    const data = [
      {
        name: t('standard-setting.code'),
        code: 0,
      },
      {
        name: t('standard-setting.name'),
        code: 1,
      },
    ];
    return data;
  });

  // 属性类型下拉数据
  const attrTypeOption = computed(() => [
    {
      code: 0,
      name: t('standard-setting.date'),
    },
    {
      code: 1,
      name: t('standard-setting.text-field'),
    },
    {
      code: 2,
      name: t('standard-setting.dropdown-list'),
    },
    {
      code: 4,
      name: t('standard-setting.system-attribute'),
    },
  ]);
  const standardForm = reactive<standardFormInter>({
    name: '',
    englishName: '',
    code: '',
    description: '',
    existStandardId: '-',
    groupId: '',
    id: '',
    attributeLink: '',
    xbaseStandardId: '',
    standardType: 2,
    xbaseStandardStatus: 0,
    xbaseStandardName: '',
    xbaseCollectionId: '',
  });
  // 详情数据
  const detailData = async () => {
    setLoading(true);
    try {
      const detailId = route.query.id || '';
      const { data } = await standarDetailList(detailId);
      if (data) {
        standardForm.standardType = data.standardType;
        standardForm.xbaseStandardId = data.xbaseStandardId;
        standardForm.xbaseStandardName = data.xbaseStandardName;
        standardForm.xbaseCollectionId = data.xbaseCollectionId;
        standardForm.code = data.code;
        standardForm.name = data.name;
        standardForm.englishName = data.englishName;
        standardForm.description = data.description;
        standardForm.existStandardId = data.existStandardId;
        standardForm.attributeLink = data.attributeLink || '-';
        standardForm.id = data.id;
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  // 获取属性数据
  const getattributeData = async (
    params: standardParams = {
      standardId: route.query?.id?.toString(),
    }
  ) => {
    setLoading(true);
    try {
      const { data } = await getAttribute(params);
      AttributeList.value = data;
      // 默认点击第一个属性
      const firstId = ref(AttributeList.value[0]?.id);
      selAttribute(firstId.value, 0);
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  // 编辑标准信息
  const standardFormEdit = () => {
    editStandardRef.value.alertShow(standardForm, 'edit');
  };

  // 查询
  const search = () => {
    detailData();
  };

  interface useInfoModelForm {
    name: string;
    type: number;
    dimensionId: string;
    ruleList: any;
    ruleListId: any;
    id: string;
    sort: string;
    emptyStatus: string;
  }

  const attributeForm = reactive<useInfoModelForm>({
    name: '',
    type: null,
    dimensionId: '',
    ruleList: [],
    ruleListId: [],
    id: '',
    sort: '',
    emptyStatus: '0',
  });

  const AttributeList = ref<any>([]);

  // 选择属性
  const isActive = ref(-1);
  const tapIndex = ref(-1);
  const selAttribute = async (id: string, index: any) => {
    isActive.value = index;
    setLoading(true);
    try {
      if (id) {
        const { data } = await getAttributeDetail(
          id,
          route.query?.id?.toString()
        );
        attributeForm.name = data.name;
        attributeForm.type = data.type;
        attributeForm.emptyStatus = data.emptyStatus ? '1' : '0';
        attributeForm.dimensionId = data.dimensionId;
        attributeForm.id = data.id;
        attributeForm.sort = data.sort;
        attributeForm.ruleList = data.ruleList;
        attrShowTypeValue.value = data.viewStatus;
        attributeForm.ruleListId = data.ruleList.map((item: any) => {
          return item.id;
        });
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };
  // 绑定属性
  const addAttribute = () => {
    createAttributeRef.value.alertShow();
  };
  // 属性排序
  const sort = (e: any) => {
    const target = AttributeList.value[e.newIndex];
    const params = {
      attributeId: target.id,
      emptyStatus: target.emptyStatus ? 1 : 0,
      standardId: route.query.id,
      newSort: e.newIndex + 1,
    };
    attributeSort(params);
    selAttribute(AttributeList.value[0].id, 0);
  };

  // 属性移除
  const removeAttr = async (item: any) => {
    const params: any = {
      attributeId: item.id,
      emptyStatus: item.emptyStatus || 0,
      standardId: route.query.id,
      newSort: 0,
    };

    const saveRes = await removeAttribute(params);
    if (saveRes.status) {
      Message.success(saveRes.message);
      getattributeData();
    } else {
      Message.error(saveRes.message);
    }
  };

  // 属性详情数据
  const standardType = ref('-');
  const attrShowTypeValue = ref('');
  const allOptions = reactive<any>({
    dimensionOptions: [],
    ruleOptions: [],
    standardOptions: [],
  });

  const userStore = useUserStore();
  const userId = getUserId() || '';
  const projectId = getLocalstorage(`last_project_${userId}`) || '';

  // 属性详情回显下拉数据
  const initAddInfo = () => {
    const params = {
      // groupId: route.params.projectId.toString(),
      groupId: userStore.admin === 0 ? '0' : projectId,
      pageNo: 1,
      pageSize: 10000,
      name: '',
      source: 'CDE',
    };
    queryDimensionList(params).then((res) => {
      if (res.status) {
        allOptions.dimensionOptions = res.data.list.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
    queryRuleList(params).then((res) => {
      if (res.status) {
        allOptions.ruleOptions = res.data.list.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
    standardList(params).then((res) => {
      if (res.status) {
        allOptions.standardOptions = res.data.list.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
  };
  initAddInfo();

  const updateData = () => {
    search();
    getattributeData();
  };

  // 改变分隔符
  const changeType = async () => {
    const params: any = {
      code: standardForm.code,
      name: standardForm.name,
      id: standardForm?.id,
      englishName: standardForm.englishName,
      description: standardForm.description,
      existStandardId: standardForm.existStandardId,
      groupId: userStore.admin === 0 ? '0' : projectId,
      attributeLink: standardType.value,
    };
    const saveRes: any = await addStandard(params);
    if (saveRes.status) {
      Message.success(saveRes.message);
    } else {
      Message.error(saveRes.message);
    }
  };

  const changeAttrType = async () => {
    const params = {
      attributeId: attributeForm.id,
      emptyStatus: attributeForm.emptyStatus,
      newSort: attributeForm.sort,
      standardId: route.query.id,
      viewStatus: attrShowTypeValue.value,
    };
    const saveRes: any = await saveAttribute(params);
    if (saveRes.status) {
      Message.success(saveRes.message);
    } else Message.error(saveRes.message);
  };

  onMounted(async () => {
    await detailData();
    if (standardForm.standardType === 0) {
      getattributeData();
    }
  });

  const toUrl = () => {
    router.go(-1);
  };
</script>

<script lang="ts">
  export default {
    name: 'Standard',
  };
</script>

<style scoped lang="less">
  .standard {
    padding: 0 20px;
    overflow-y: auto;
    .card-title {
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;

      img {
        position: relative;
        top: 3px;
        height: 20px;
      }
    }

    .search-title {
      height: 22px;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #1d2129;
      line-height: 22px;
    }
  }

  .list-empty {
    display: flex;
    align-items: center;

    .empty-box {
      width: 100%;
      text-align: center;
      height: 400px;

      .text {
        width: 100%;
        text-align: center;
        margin-top: -80px;
      }
    }
  }

  :deep(.arco-table-tr) {
    cursor: pointer;
  }

  :deep(.arco-card-header) {
    height: auto;
    padding: 20px 20px 0px 20px;
    border: none;
  }

  :deep(.arco-card-bordered) {
    border: none;
  }

  :deep(.arco-space-item) {
    cursor: pointer;
  }

  :deep(.arco-scrollbar-thumb-bar) {
    width: 0;
  }

  .previewBox {
    padding-left: 100px;
    box-sizing: border-box;
  }

  .preview {
    border: 1px solid #d9d9d9;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    color: #1d2129;
    font-weight: 500;
    height: 150px;
    margin-bottom: 16px;
  }

  .title {
    margin-bottom: 16px;
  }

  .text-font {
    font-size: 16px;
    font-weight: 600;
    color: #1d2129;
  }

  .text-title {
    font-size: 16px;
    color: #1d2129;
  }

  .attrbox {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #d9d9d9;
    margin-bottom: 20px;
    height: 30px;
  }

  .attrSpan {
    height: 30px;

    span {
      display: inline-block;
      margin-right: 36px;
      position: relative;
      height: 30px;
      cursor: pointer;

      img {
        position: absolute;
        right: -20px;
        top: 0px;
        display: none;
      }

      .show {
        display: block;
      }
    }

    .active {
      color: #3366ff;
      border-bottom: 3px solid #3366ff;
    }
  }

  .pl100 {
    padding-left: 100px !important;
  }
  .bindText {
    font-size: 18px;
    font-weight: 600;
    color: #3366ff;
  }
  /deep/ .arco-form-item-label-col > .arco-form-item-label {
    font-weight: normal;
  }

  .tipsText {
    padding-left: 25px;
    div {
      margin-top: 18px;
      color: #1d2129;
      font-size: 14px;
      .block {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: #ffffff;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 2px solid #e5e6eb;
      }
    }
  }
  :deep(.arco-textarea-wrapper) {
    border-radius: 8px;
  }
  :deep(.arco-picker),
  :deep(.arco-input-tag),
  :deep(.arco-select-view-single),
  :deep(.arco-textarea),
  :deep(.arco-form-item-content-wrapper) {
    background-color: #fff;
    border-radius: 8px;
  }
  :deep(.arco-select) {
    border: 1px solid #c9cdd4 !important;
    border-radius: 8px;
  }
  :deep(.arco-card-body) {
    padding: 20px 0;
  }
</style>
