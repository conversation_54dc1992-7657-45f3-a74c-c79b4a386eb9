<template>
  <div class="navbar">
    <div class="header">
      <a-tooltip :content="$t('navbar.logo.to')">
        <a-space style="cursor: pointer" @click="toDashboard">
          <img alt="logo" :src="logoTop" />
          <span class="header-title">
            {{ $t('navbar.logo.title') }}
          </span>
        </a-space>
      </a-tooltip>
    </div>
    <a-space
      direction="vertical"
      fill
      :size="isExist ? 32 : 24"
      style="justify-content: center; align-items: center; height: 100vh"
    >
      <template v-if="isExist">
        <div class="title">{{ shareInfo.inviterName }}邀请您加入知识库</div>
        <div class="base-card">
          <div class="card-top">
            <img
              v-if="shareInfo?.picUrl"
              :src="
                '/work/api/sys-storage/download_image?f8s=' + shareInfo.picUrl
              "
            />
            <DefaultCover v-else />
            <div v-if="shareInfo.id" class="info">
              <a-typography-paragraph
                :ellipsis="{
                  width: 200,
                  rows: 1,
                  showTooltip: true,
                }"
                class="info-name"
                >{{ shareInfo.name }}
              </a-typography-paragraph>
              <div class="info-desc">{{
                shareInfo.creatorName +
                '创建 / ' +
                (shareInfo.memberCnt || 0) +
                '人已加入'
              }}</div>
            </div>
          </div>
          <a-space>
            <a-typography-paragraph
              :ellipsis="{
                rows: 2,
                showTooltip: true,
              }"
              class="info-desc detail"
              >{{ shareInfo.description }}
            </a-typography-paragraph>
          </a-space>
          <a-divider></a-divider>
          <a-space fill style="justify-content: center">
            <a-button
              type="primary"
              :disabled="!shareInfo.id"
              style="width: 360px; padding: 17px"
              @click="joinKnowledge"
              >加入知识库</a-button
            >
          </a-space>
        </div>
      </template>
      <template v-else-if="loaded">
        <ShareNotExist />
        <span class="not-exist">分享链接不存在</span>
      </template>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import { getShareInfoById, joinKnowledgeByshared } from '../api';
  import { ref } from 'vue';
  import logoTop from '@/assets/images/logo-top.png';
  import DefaultCover from '@/assets/images/knowledge-base/default-cover.svg';
  import ShareNotExist from '@/assets/images/knowledge-base/share-not-exist.svg';

  const router = useRouter();
  const shareInfo = ref<any>({});
  const loaded = ref(false); // 是否已加载完成
  const isExist = ref(false); // 分享链接是否存在

  const { kbId, visitCode } = router.currentRoute.value.query as Record<
    string,
    string
  >;
  async function getSharedInfo() {
    loaded.value = false;
    const res = await getShareInfoById(kbId as string, visitCode as string);
    loaded.value = true;
    if (res.status) {
      shareInfo.value = res.data;
      isExist.value = true;
    } else {
      // 分享链接不存在
      isExist.value = false;
    }
  }
  getSharedInfo();

  async function joinKnowledge() {
    const res = await joinKnowledgeByshared({ kbId, visitCode });
    if (res.status) {
      const { folderVO } = res.data;
      router.push({
        name: 'sharedBase',
        params: { kbId, rootFolderId: folderVO.id },
      });
    }
  }

  // 回到主页
  const toDashboard = () => {
    const url = router.resolve({
      path: '/dashboard',
    });
    // 打开新窗口
    window.open(url.href);
  };
</script>

<style scoped lang="less">
  .navbar {
    height: 100vh;
    background-image: url('../../../assets/images/knowledge-base/share-page-bg.png');
    background-repeat: no-repeat; /* 不重复 */
    background-size: cover; /* 自适应覆盖整个容器 */
    background-position: center; /* 居中显示 */
    background-attachment: fixed; /* 可选：固定背景不随滚动 */
  }

  .header {
    position: fixed;
    height: 106px;
    padding: 30px;

    img {
      width: 46px;
      height: 46px;
    }

    .header-title {
      height: 33px;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 30px;
      color: #1d2129;
      line-height: 32px;
      letter-spacing: 2px;
    }
  }

  .title-img {
    width: 25px;
    position: relative;
    top: 5px;
    margin-right: 5px;
  }
  .title {
    font-family: PingFang SC-Medium;
    font-weight: 500;
    color: #1d2129;
    font-size: 28px;
    line-height: 44px;
  }
  .base-card {
    width: 458px;
    height: 235px;
    padding: 24px 24px 20px;
    background-image: url('../../../assets/images/knowledge-base/share-card-img.png');

    .info-name {
      font-family: PingFang SC-Medium;
      color: #1d2129;
      font-size: 20px;
      font-weight: 500;
      line-height: 21px;
    }
    .info-desc {
      color: #4e5969;
      font-size: 14px;
      font-family: PingFang SC, PingFang SC;
      line-height: 21px;
    }
  }
  .card-top {
    display: flex;
    img,
    svg {
      margin-right: 12px;
      width: 60px;
      height: 60px;
      border-radius: 8px;
    }

    .info {
      flex: 1;
      padding-top: 6px;
    }
  }
  .detail {
    padding: 12px 0;
    width: 410px;
    height: 42px;
  }

  .not-exist {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 20px;
    color: #4e5969;
    line-height: 29px;
  }

  :deep(div.arco-typography, p.arco-typography) {
    margin-bottom: 10px;
  }

  :deep(.arco-btn) {
    border-radius: 8px !important;
  }
</style>
