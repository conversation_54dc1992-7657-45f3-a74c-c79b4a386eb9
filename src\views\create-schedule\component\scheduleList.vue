<template>
  <div
    ><div class="schedule-list">
      <div v-if="!props.data?.length" class="noData"><a-empty /></div>
      <div class="schedule-body">
        <div
          v-for="item in props.data"
          :key="item.id"
          class="schedule-block"
          :class="{ active: item.scheduleStatus === '2' }"
        >
          <div>
            <div class="schedule-block-time">
              <span>{{ dayjs(item.planStartTime).format('HH:mm') }}</span> -
              <span>{{ dayjs(item.planEndTime).format('HH:mm') }}</span>
            </div>
            <div
              :class="
                item.type === 'meeting'
                  ? 'schedule-block-type1'
                  : 'schedule-block-type2'
              "
              class="schedule-block-type"
            >
              <span>
                <span
                  class="scheduleIcon"
                  :class="
                    item.type === 'meeting' ? 'scheduleIcon1' : 'scheduleIcon2'
                  "
                ></span>
                {{ item.type === 'meeting' ? '会议' : '事项' }}
              </span>
            </div>
          </div>

          <div class="schedule-block-cont">
            <div class="schedule-block-title">{{ item.title }}</div>
            <div class="schedule-block-described">{{ item.content }}</div>
          </div>
          <div class="schedule-list-action"
            ><a-dropdown class="custom-dropdown" @select="handleSelect">
              <icon-more class="icon-more" />
              <template #content>
                <a-doption @click="editHandle(item)"
                  ><a-button type="text">编辑</a-button></a-doption
                >
                <a-doption @click="deleteHandle(item)"
                  ><a-button type="text">删除</a-button></a-doption
                >
              </template>
            </a-dropdown>
          </div>
        </div>
      </div>
    </div>

    <a-modal
      :visible="delVidible"
      @ok="deleteSaveHandle"
      @cancel="handleCancel"
    >
      <template #title> 删除 </template>
      <div>
        <a-radio-group
          v-if="nowRecord?.type === 'meeting'"
          v-model="repeatedVal"
          direction="vertical"
        >
          <a-radio value="0">当前日程</a-radio>
          <a-radio value="1">当前及之后日程</a-radio>
          <a-radio value="2">所有日程</a-radio>
        </a-radio-group>
        <div v-else>{{ `${deleteMatterMsg || ''} 确认删除该事项吗？` }}</div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
  import { ref, inject } from 'vue';
  import { removeschedule } from '@/views/schedule/component/calendar/api';

  import { Message } from '@arco-design/web-vue';
  import { deleteMatter, deleteMatterFlag } from '../api';
  import dayjs from 'dayjs';

  const nowRecord = ref(null); // 当前选择的数据

  const emits = defineEmits(['refresh', 'jump']);

  const grandparentMethod = inject('grandparentMethod');
  const addToEdit = inject('addToEdit');
  // 新增 传递到日程首页

  // 编辑
  const editHandle = (item) => {
    // 事项的主键id为id  会议主键id为scheduleSubId  scheduleSubId为日程列表的id 故将id都统一到scheduleSubId中
    item.scheduleSubId = item.type === 'meeting' ? item.scheduleSubId : item.id;
    if (addToEdit) addToEdit(item);
  };

  const delVidible = ref(false);
  const repeatedVal = ref('0');
  const recordType = ref('');

  // 取消删除
  const handleCancel = () => {
    delVidible.value = false;
  };

  // 删除事项
  const deleteMatterList = async () => {
    const params = {
      scheduleDetailId: nowRecord.value.id,
    };
    try {
      const res = await deleteMatter(params);
      if (res.status) {
        Message.success('删除成功');
        emits('refresh');
        delVidible.value = false;
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 删除确认框
  const deleteSaveHandle = async () => {
    if (nowRecord.value.type === 'meeting') {
      if (!repeatedVal.value) {
        Message.info('请选择重复类型');
        return;
      }
      const param = {
        scheduleDetailId: nowRecord.value.id,
        rmOption: repeatedVal.value,
      };
      try {
        const res = await removeschedule(param);
        if (res.status) {
          Message.success('删除成功');
          emits('refresh');
        }
      } catch (error) {
        console.log(error);
      } finally {
        delVidible.value = false;
      }
    } else {
      deleteMatterList();
    }
  };

  const deleteMatterMsg = ref('');
  // 删除
  const deleteHandle = async (item) => {
    if (item.type === 'item') {
      const params = {
        scheduleDetailId: item.id,
      };
      try {
        const res = await deleteMatterFlag(params);
        if (res.status) {
          const [firstError] = res.data.err || []; // 使用数组解构，并处理 err 可能为 undefined 的情况
          deleteMatterMsg.value = firstError;
          console.log('[ firstError ] >', firstError);
        }
      } catch (error) {
        console.log(error);
      }
    }
    nowRecord.value = item;
    recordType.value = item.type;
    delVidible.value = true;
  };

  const props = defineProps({
    data: {
      type: String,
      required: false,
    },
  });
</script>

<style lang="less" scoped>
  .schedule-list {
    height: 100%;
    // padding: 0 20px;
    background-color: #fff;
    border-radius: 1px solid #efefef;
    position: relative;

    .noData {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .schedule-body {
      height: 100%;
      width: 100%;
      overflow: auto;
      position: absolute;
      padding: 0 20px;
      top: 0;
      left: 0;
      :deep(.arco-checkbox-icon) {
        border-radius: 50%;
        // position: absolute;
        // left: 20px;
        // top: 50px;
      }
      :deep(.arco-checkbox) {
        // display: block;
        flex: 1;
      }
      // :deep(.arco-checkbox-checked) {
      //   color: #1d2129 !important;
      //   .schedule-block-title {
      //     color: #1d2129 !important;
      //   }
      // }
      .schedule-block {
        padding: 16px 0 20px 0;
        display: flex;
        align-items: top;
        border-bottom: 1px solid #efefef;
        &:hover {
          :deep(.arco-icon-hover) {
            display: block;
          }
        }
        div {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1; /* 限制显示的行数 */
          -webkit-box-orient: vertical;
        }
        .schedule-block-time {
          width: 130px;
          font-size: 20px;
          color: #1d2129;
          margin-bottom: 8px;
          font-family: Source Han Sans CN, Source Han Sans CN;
        }
        .schedule-block-type {
          display: inline-block;
          // width: 64px;
          height: 24px;
          padding: 0 8px;
          line-height: 24px;
          border-radius: 8px;
          text-align: center;
          .scheduleIcon {
            display: inline-block;
            background-image: url(@/assets/images/schedule/icon-meeting.png);
            width: 16px;
            height: 16px;
            vertical-align: middle;
            background-size: 100% 100%;
            font-size: 14px;
          }
          .scheduleIcon1 {
            background-image: url(@/assets/images/schedule/icon-meeting1.png);
          }
          .scheduleIcon2 {
            background-image: url(@/assets/images/schedule/icon-matter1.png);
          }
          img {
            width: 20px;
            // margin-right: 2px;
          }
        }
        .schedule-block-type1 {
          background-color: #fff3e8;
          color: #f77234;
        }
        .schedule-block-type2 {
          background-color: #e8f7ff;
          color: #3491fa;
        }
        .schedule-block-cont {
          flex: 1;
          padding: 0 20px;

          .schedule-block-title {
            font-weight: 500;
            font-size: 16px;
            // margin: 8px 0;
            color: #1d2129;
            margin-bottom: 10px;
            font-family: Source Han Sans CN;
            margin-top: 2px;
          }
          .schedule-block-described {
            color: #86909c;
            font-size: 14px;
          }
        }

        .icon-more {
          cursor: pointer;
        }
        // .arco-checkbox-checked,
        .schedule-block-active {
          .schedule-block-time {
            div {
              color: #c9cdd4 !important;
            }
          }
          .schedule-block-cont {
            .schedule-block-title {
              color: #86909c;
            }

            .schedule-block-type {
              background-color: #e5e6eb;
              color: #86909c;
            }
            .schedule-block-described {
              color: #c9cdd4;
            }
            .schedule-block-type {
              .scheduleIcon1 {
                background-image: url(@/assets/images/schedule/icon-meeting2.png);
              }
              .scheduleIcon2 {
                background-image: url(@/assets/images/schedule/icon-matter2.png);
              }
            }
          }
        }
        .schedule-list-action {
          cursor: pointer;
        }
      }
    }
  }
  :deep(.arco-divider-horizontal) {
    margin: 0 !important;
  }
  // 已完成数据状态
  .active {
    .schedule-block-time div,
    .schedule-block-title,
    .schedule-block-described,
    .schedule-block-time span {
      color: #86909c !important;
    }
    .schedule-block-type {
      background-color: #e5e6eb !important;
      color: #86909c !important;
      .scheduleIcon1 {
        background-image: url(@/assets/images/schedule/icon-meeting2.png) !important;
      }
      .scheduleIcon2 {
        background-image: url(@/assets/images/schedule/icon-matter2.png) !important;
      }
    }
  }
</style>

<style>
  .custom-dropdown .arco-dropdown .arco-btn {
    color: #000;
  }
</style>
