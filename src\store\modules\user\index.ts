import { defineStore } from 'pinia';
import {
  login as userLogin,
  submitCode,
  logout as userLogout,
  BIMBaseLogin,
  getUserInfo,
  getCdexUserInfo,
  LoginData,
  getXbaseToken,
  activeCompanyRecords,
  addInfoToProject,
} from '@/api/user';
import {
  setToken,
  clearToken,
  setBIMBaseToken,
  clearBIMBaseToken,
  setXBaseToken,
  clearXBaseToken,
  setUserId,
  getUserId,
  clearUserId,
  setUserName,
  getUserName,
  clearUserName,
} from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import useAppStore from '../app';
import { initLogCollectionSDK } from '@/utils/log-colletion';
import local, { setLocalstorage, getLocalstorage } from '@/utils/localstorage';
import { getProjectList } from '@/api/project';
import ProjectTemplate from '@/views/home-page/components/project-template/project-template.vue';

const useUserStore = defineStore('user', {
  state: () => ({
    id: undefined,
    username: undefined,
    name: undefined,
    userName: undefined,
    userFullname: undefined,
    phone: undefined,
    email: undefined,
    userNo: undefined,
    accountState: undefined,
    avatarToken: undefined,
    fid: undefined,
    cdeId: undefined, // 记录cdexUserinfo的id
    admin: -1,
    role: '',
    color: '',
    standardTab: 'standard-list',
    orgNo: undefined,
    orgList: [],
    orgName: '',
    pathName: '',
    pathNo: '',
    teamId: local.getLocalstorage('selectedTeamId') || 'global',
    companyId: local.getLocalstorage('companyId') || undefined,
    teamList: local.getLocalstorage('teamList') || [],
    projectList: [],
    // 字符串0 是非项目模板，字符串1 是项目模板
    projectTemplate: local.getLocalstorage('is_project_template') || '0',
  }),

  getters: {
    userInfo(state) {
      return { ...state };
    },
  },

  actions: {
    // Set user's information
    setInfo(partial: any) {
      this.$patch(partial);
      localStorage.setItem('userAdmin', partial.admin);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    // Get user's information
    async info() {
      const username = getUserName() || '';
      const res = await getUserInfo(username);
      const cdexRes = await getCdexUserInfo(username);

      // 项目数据
      const params = {
        pageNo: 1,
        pageSize: 10,
        projectType: 0,
      };
      const projectRes: any = await getProjectList(params);
      const projectList = projectRes?.data?.list || [];

      console.log('res data: ', res);

      const {
        name,
        email,
        fid,
        id,
        userNo,
        accountState,
        avatarToken,
        phone,
        userName,
        color,
        orgNo,
        pathNo,
        orgName,
        pathName,
        userFullname,
        orgList,
      } = res.data;
      console.log(' res.data: ', res.data);
      const { admin } = cdexRes.data;
      const cdeId = cdexRes.data.id;
      // 保存用户名
      if (userName) setUserName(userName as string);
      // 0系统管理员 1项目创建员 -1普通成员 2被停用的项目创建员 3单位管理员
      // admin = admin || 0;
      const role = admin !== -1 ? 'admin' : 'user';
      this.setInfo({
        name,
        email,
        admin,
        fid,
        id,
        cdeId,
        accountState,
        avatarToken,
        phone,
        username: userName || username,
        userNo,
        role,
        color,
        orgNo,
        orgName,
        pathName,
        pathNo,
        userFullname,
        projectList,
        orgList,
      });
      setUserId(id as string);
      // 解决切换账号登陆后 重定向到上次退出页面时  未设置当前用户下得projectId问题
      const userId = getUserId();
      if (
        !getLocalstorage(`last_project_${userId}`) &&
        projectRes?.data?.list?.length
      )
        setLocalstorage(`last_project_${userId}`, projectRes?.data?.list[0].id);

      // this.BIMBaseLogin();
      // 大象云服务token获取修改为本地处理
      this.getXbaseToken();
      // 初始化日志上报sdk
      initLogCollectionSDK(username, userFullname as string, orgList);
    },

    // Login
    async login(loginForm: LoginData) {
      try {
        const res = await userLogin(loginForm);
        // @ts-ignore
        setToken(res.access_token);
        // 保存用户名
        // @ts-ignore
        // localStorage.setItem('username', res.userName);
        setUserName(res.userName);
        await this.info();
      } catch (err) {
        clearToken();
        throw err;
      }
    },
    async submitCode(loginForm: LoginData) {
      const res = await submitCode(loginForm);
      return res;
    },
    async BIMBaseLogin() {
      try {
        const res = await BIMBaseLogin();
        // @ts-ignore
        setBIMBaseToken(res.access_token);
      } catch (err) {
        clearBIMBaseToken();
        throw err;
      }
    },
    // 暂时注释服务,替换本地化获取token
    // async XBaseLogin() {
    //   try {
    //     const res = await XBaseLogin();
    //     setXBaseToken(res.data.token);
    //     // @ts-ignore
    //   } catch (err) {
    //     clearXBaseToken();
    //     throw err;
    //   }
    // },
    async getXbaseToken() {
      try {
        const res = await getXbaseToken();
        setXBaseToken(res.data);
        // @ts-ignore
      } catch (err) {
        clearXBaseToken();
        throw err;
      }
    },
    logoutCallBack() {
      const appStore = useAppStore();
      this.resetInfo();
      clearToken();
      clearUserName();
      removeRouteListener();
      appStore.clearServerMenu();
    },
    // Logout
    async logout(isRquestLogout = true) {
      try {
        if (isRquestLogout) {
          await userLogout();
        }
      } finally {
        this.logoutCallBack();
      }
    },
    setTeamId(id: string) {
      this.teamId = id;
      local.setLocalstorage('selectedTeamId', id);
    },
    setCompanyId(id: string) {
      this.companyId = id;
      local.setLocalstorage('companyId', id);
    },
    setTeamList(list: []) {
      this.teamList = list;
      local.setLocalstorage('teamList', JSON.stringify(list));
    },
    setProjectTemplate(param: string) {
      if(param !== '0' && param !== '1' ){
        return;
      }
      console.log("设置成功模板判断flag",param);
      this.projectTemplate = param;
      local.setLocalstorage('is_project_template', param);
    },

    getTeamId() {
      return this.teamId || local.getLocalstorage('selectedTeamId');
    },
    getCompanyId() {
      return this.companyId || local.getLocalstorage('companyId');
    },
    getTeamList() {
      return (
        this.teamList || JSON.parse(local.getLocalstorage('teamList') || '[]')
      );
    },

    // 添加用户至演示项目
    async addToProject() {
      await addInfoToProject({
        userName: this.username,
      });
    },
  },
});

export default useUserStore;
