export default {
  'dashboard.participating-units': 'Participating Units',
  'dashboard.project-phase': 'Project Phase',
  'dashboard.project-status': 'Project status',
  'dashboard.personal-homepage': 'Personal Homepage',
  'dashboard.project-list': 'Project List',
  'dashboard.cdex-kanban': 'Dashboard',
  'dashboard.project-template': 'Project Template',
  'dashboard.enter-immediately': 'Enter Immediately',
  'dashboard.recent-files': 'Recent Files',
  'dashboard.index': 'Index',
  'dashboard.project-name': 'Name',
  'dashboard.file-name': 'File Name',
  'dashboard.update-time': 'Update Time',
  'dashboard.file-path': 'File Path',
  'dashboard.my-work': 'My Work',
  'dashboard.my-question': 'My Question',
  'dashboard.my-sharing': 'My Sharing',
  'dashboard.my-review': 'My Review',
  'dashboard.my-delivery': 'My Delivery',
  'dashboard.project-location': 'Project Location',
  'dashboard.individual': '',
  'dashboard.project-code': 'Project Code',
  'dashboard.project-time': 'Project Time',
  'dashboard.start': 'Start',
  'dashboard.up-to': 'Up To',
  'dashboard.name': 'Name',
  'dashboard.project-type': 'Project Type',
  'dashboard.coordinate-system': 'Coordinate System',
  'dashboard.height-datum': 'Height Datum',
  'dashboard.create-time': 'Create Time',
  'dashboard.creater': 'Creater',
  'dashboard.delete': 'Delete',
  'dashboard.confirm-delete': 'Do you want to delete this project',
  'dashboard.confirm-delete-template':
    'Do you want to delete this project template?',
  'dashboard.please-enter': 'Please Enter',
  'dashboard.please-select': 'Please Select',
  'dashboard.operations': 'Operations',
  'dashboard.create-roject': 'Create Project',
  'dashboard.template-name': 'Template',
  'dashboard.create-project-template': 'Create Project Template',
  'dashboard.model-engine': 'Model Engine',
  'dashboard.road-level': 'Road Level',
  'dashboard.design-load': 'Design Load',
  'dashboard.safety-level': 'Safety Level',
  'dashboard.environment-category': 'Environment Category',
  'dashboard.seismic-level': 'Seismic Level',
  'dashboard.project-overview': 'Project Overview',
  'dashboard.create-template': 'Creat Template',
  'dashboard.create-template-text1': 'Create a blank project template',
  'dashboard.create-template-text2': 'Create templates from existing projects',
  'dashboard.create-template-text3':
    'Certain aspects of the selected project will not be copied to the template',
  'dashboard.retreat': 'Retreat',
  'dashboard.cancellation': 'Cancellation',
  'dashboard.next-step': 'Next Step',
  'dashboard.welcome': 'Welcome',
  'dashboard.project': 'project',
  'dashboard.constructive-force-model-engine': 'BIM-Base Modelling Engines',
  'dashboard.elephant-cloud-model-engine': 'X-Base Modelling Engines',
  'dashboard.no-project-data':
    'There is currently no project information available',
  'dashboard.no-data': 'There is currently no data available',
  'dashboard.Please-enter-name': 'Please Enter Information',
  'dashboard.project-name-errMsg': 'Please enter project name!',
  'dashboard.project-phase-errMsg': 'Please enter phase! ',
  'dashboard.project-code-errMsg': 'Please enter project code!',
  'dashboard.project-code-number-errMsg': 'Only supports English and numbers',
  'dashboard.project-location-errMsg': 'Please enter project location!',
  'dashboard.project-type-errMsg': 'Please select project type!',
  'dashboard.project-engine-errMsg': 'Please select project engine!',
  'dashboard.road-level-errMsg': 'Please select road level!',
  'dashboard.template-name-errMsg': 'Please enter Template Name！',
  'dashboard.project-select-errMsg': 'Please select a project!',
  'dashboard.please-input-complete-template-information':
    'please Input Complete Template Information',
  'dashboard.point': 'Point',
  'dashboard.number': 'Number',
  'dashboard.project-table-title': 'Project List',
  'dashboard.search-corporation': 'Corporation',
  'dashboard.project-properties': 'Properties',
  'dashboard.project-pro-errMsg': 'Please select project Properties',
  'dashboard.integrated-product-application': 'Integrated Product Application',

  'dashboard.unit-details': 'Unit Details',
  'dashboard.project-details': 'Project Details',
  'dashboard.project-situation': 'Project Situation',

  'dashboard.regularProjects': 'Regular projects',
  'dashboard.pilotProject': 'Pilot project',
  'dashboard.navigationProject': 'Navigation project',
  'dashboard.othersProject': 'Others Project',
  'dashboard.training-project': 'Training Project',
  'dashboard.occupational-intelligence': 'Occupational Intelligence',
  'dashboard.writing-expert': 'Writing Expert',
  'dashboard.new-conversation': 'New Conversation',
  'dashboard.history-conversation': 'History Conversation',
  'dashboard.search': 'Search',
  'dashboard.delete.confirm': 'Are you sure you want to delete this?',
  'dashboard.rename.success': 'Rename successful!',
  'dashboard.delete.success': 'Delete successful!',
  'dashboard.file': 'File',
  'dashboard.view': 'View',
  'dashboard.delete.file': 'Are you sure you want to delete this file?',
  'dashboard.schedule': 'Schedule',
  'dashboard.create.item': 'Create Item',
  'dashboard.create.meeting': 'Create Meeting',
  'dashboard.to': 'To',
};
