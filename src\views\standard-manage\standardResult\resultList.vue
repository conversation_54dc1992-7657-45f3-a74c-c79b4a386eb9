<template>
  <div class="listContent">
    <div class="box">
      <table-title
        style="width: 100px"
        :title="$t('standard-setting.standard-information')"
      ></table-title>
      <a-select
        v-model="historyId"
        class="timeBox"
        :placeholder="$t('please-select')"
        style="width: 190px"
        @change="changeHistory"
      >
        <a-option
          v-for="item of historyOption"
          :key="item.id"
          :value="item.id"
          >{{ item.createDate }}</a-option
        >
      </a-select>
      <a-button type="outline" @click="exportFile">{{
        $t('standard-setting.export-report')
      }}</a-button>
    </div>

    <ul>
      <li v-for="(item, index) in modelData" :key="index">
        <span class="label">{{ item.label }}</span>
        <span class="value">{{ item.value }}</span>
      </li>
    </ul>
    <table-title
      :title="$t('standard.attr-error-list')"
      style="margin-bottom: 16px"
    ></table-title>
    <a-table
      :loading="loading"
      :columns="columns"
      :data="tableData"
      :pagination="PageConfigs"
      row-key="uid"
      :hoverable="true"
      :scroll="{ y: 'calc(100vh - 310px)' }"
      @page-change="pageChange"
      @page-size-change="pageSizeChange"
      @row-click="resultClick"
    >
      <template #guid="{ record }">
        <span class="blueText">{{
          record.guidName || record.guid
        }}</span></template
      >
      <template #errNum="{ record }">
        <span class="redText" @click="getDetail(record)">{{
          record.errNum
        }}</span></template
      >
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, toRaw, onMounted, watchEffect, computed } from 'vue';
  import { getCheckoutResult, getHistoryData, GetExportFile } from '../api';
  import { useRoute } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import useStandardManageStore from '@/store/modules/standard-manage/index';
  import { GetElementldsByGuids } from '@/views/bim-view/XBase/version-compare/api';
  import { useI18n } from 'vue-i18n';
  import TableTitle from '@/components/table-title/index.vue';
  import createUrl from '@/utils/file-blob';

  const { t } = useI18n();
  let compareViewer: any = null;

  const props = defineProps({
    modelFile: {
      type: Object,
      default: () => {
        return {};
      },
    },
    viewers: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const modelData: any = ref([]);
  const modelDetailData = ref([]);

  const tableData: any = ref([]); // 属性错误列表数据
  const detailData: any = ref([]); // 详情数据

  const PageConfigs: any = reactive({
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: false,
    showJumper: false,
    showPageSize: false,
    simple: true,
  });

  // 构件高亮
  const resultClick = async (record: any) => {
    compareViewer?.clearObjectsColor([`'${record.guid}'`]);
    const path = props.modelFile.graphicEngineInfo.split('|')[1];
    console.log(path);
    const result = await GetElementldsByGuids({
      render_path: path,
      guids: [`'${record.guid}'`],
    });
    const elementId = Object.values(result.data.guid_element_id)[0];
    compareViewer.selectEntities(elementId);
  };

  const columns: any = computed(() => [
    {
      title: t('standard.member-name-id'),
      dataIndex: 'guid',
      slotName: 'guid',
      align: 'center',
    },
    {
      title: t('standard.error-attributes-number'),
      dataIndex: 'errNum',
      slotName: 'errNum',
      align: 'center',
    },
  ]);

  const historyOption: any = ref([]); //  历史数据

  const store = useStandardManageStore();
  const { treeDataList } = storeToRefs(store);

  // 获取标准数据
  const standardId: any = ref();

  const loading: any = ref(false);
  const historyId: any = ref();

  const route: any = useRoute();

  // 结果数据
  const getResultData = async () => {
    try {
      loading.value = true;
      const param: any = {
        fileId: route.query.fileId,
        pageNum: PageConfigs.current,
        pageSize: PageConfigs.pageSize,
        historyId: historyId.value,
      };
      const { data } = await getCheckoutResult(param);
      tableData.value = data.list || [];
      PageConfigs.total = data.total || 0;
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const pageChange = (val: number) => {
    PageConfigs.current = val;
    getResultData();
  };
  const pageSizeChange = (val: number) => {
    PageConfigs.pageSize = val;
  };

  // 切换历史版本
  const changeHistory = (value: any) => {
    compareViewer.selectEntities(null);
    historyId.value = value;
    const idx = historyOption.value.findIndex((item: any) => item.id === value);
    modelData.value = modelDetailData.value[idx];

    getResultData();
  };

  // 获取历史数据
  const getHistoryList = async () => {
    const historyData: any = await getHistoryData({
      fileId: route.query.fileId,
    });
    historyId.value = historyData.data[0].id;
    historyOption.value = historyData.data;
    historyData.data.forEach((item: any) => {
      const [errorNum, correctNumber] = item.goujianErrCorrect.split(',');
      const totleNum = Number(correctNumber) + Number(errorNum);
      const correctRate = (correctNumber / totleNum) * 100;
      const errRate = (errorNum / totleNum) * 100;

      const modelDetail: any = [
        {
          label: t('standard.model-name'),

          value: item.fileName,
          span: 4,
        },
        {
          label: t('standard.standard-name'),
          value: item.className,
          span: 4,
        },
        {
          label: t('standard.member-totle'),
          value: totleNum,
          span: 4,
        },
        {
          label: t('standard.correct-number'),
          value: correctNumber || 0,
          span: 2,
        },
        {
          label: t('standard.correct-ratio'),
          value: `${correctRate || 0}%`,
          span: 2,
        },
        {
          label: t('standard.error-number'),
          value: errorNum || 0,
          span: 2,
        },

        {
          label: t('standard.error-ratio'),
          value: `${errRate}%`,
          span: 2,
        },
      ];

      modelDetailData.value.push(modelDetail);
      getResultData();
    });
  };

  // 获取错误详情
  const getDetail = (record: any) => {
    const guId = record.guid;
    const errorMapList = record.errorMap[guId];
    // 详情数据
    detailData.value = errorMapList;
    store.setResultDetail(errorMapList);
  };

  const init = async () => {
    // initView();
    await getHistoryList();
    [modelData.value] = modelDetailData.value;
  };

  // 导出报告
  const exportFile = async () => {
    const formData = new URLSearchParams();
    formData.append('fileId', route.query.fileId);
    formData.append('historyId', historyId.value);

    const res = await GetExportFile(formData);
    const name = modelData.value[0].value.split('.')[0];
    createUrl(
      res.data,
      `${name}${t('standard-setting.model-verification-report')}.docx`
    );
  };

  onMounted(() => {
    init();
  });

  watchEffect(async () => {
    // baseViewer = await toRaw(props.viewers.baseViewer);
    compareViewer = await toRaw(props.viewers.compareViewer);
  });
</script>

<style scoped lang="less">
  .pageContainer {
    padding: 20px;
    background-color: #fff;

    .arco-col {
      background-color: var(--color-bg-1);
    }

    .containerLeft {
      // padding: 20px;
      .leftList {
        div {
          padding: 4px 0 4px 20px;
          font-size: 16px;
          line-height: 30px;
          cursor: pointer;
        }

        .active {
          color: #fff;
          background-color: rgb(var(--link-6));
        }
      }
    }
  }

  .containerRight {
    padding: 20px;
    border-left: 2px solid #efefef;
  }

  .content {
    .table-wrap {
      height: calc(100vh - 250px);
      margin-top: 18px;
    }
  }

  .tips {
    margin-top: 16px;
  }

  .pageBox {
    display: flex;
    background-color: #fff;
    height: calc(100vh - 40px);
  }

  .lefContent {
    width: 360px;
    min-width: 360px;
    padding: 0 20px;
    // margin-right: 20px;
  }

  .rightContent {
    display: flex;
    flex: 1;
    flex-flow: column nowrap; /* 设置主轴为垂直方向 */
    height: calc(100vh - 40px);
    padding-bottom: 20px;
    border-left: 2px solid #efefef;
    padding-left: 20px;
    box-sizing: border-box;

    .my-obv-viewer {
      flex: 1;
    }

    .detail {
      flex-direction: row;
      // height: 220px;
      // padding: 0 20px;
    }
    :deep(.arco-table-container) {
      height: 200px;
    }
  }

  .content-title {
    position: relative;
    margin-bottom: 16px;

    .btns {
      position: absolute;
      top: 0;
      right: 0;
    }

    .title-img {
      width: 20px;
      height: 20px;
    }

    .title-text {
      position: absolute;
      top: 0;
      left: 20px;
      display: inline-block;
      margin-left: 8px;
      color: #1d2129;
      font-weight: 600;
      font-size: 18px;
      line-height: 21px;
    }
  }

  #modelViewerNew1 {
    width: 100%;
    height: 500px;
  }

  .redText {
    color: red;
    cursor: pointer;
  }

  .blueText {
    color: rgb(var(--link-6));
    cursor: pointer;
  }

  .modelData {
    margin-bottom: 16px;
  }

  :deep(.timeBox) {
    position: absolute;
    top: -5px;
    right: 0;
    width: 200px;
  }

  .modelData {
    :deep(.arco-select-view-single) {
      padding: 0;
      color: #333;
      background-color: #fff;
      border: none;

      .arco-select-view-suffix {
        display: none;
      }
    }
  }

  .box {
    display: flex;
    justify-content: space-between;
  }
</style>
