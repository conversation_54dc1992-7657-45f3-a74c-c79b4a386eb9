<template>
  <a-modal
    :visible="visible"
    :title="title"
    :width="676"
    :footer="!disabled"
    :unmount-on-close="true"
    :ok-loading="loading"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
    @open="init"
  >
    <a-form ref="memberRef" :model="formData" :disabled="disabled">
      <a-row :gutter="16">
        <!-- <a-col flex="320px">
          <a-form-item
            field="username"
            label="用户名"
            :validate-trigger="['change', 'input']"
            :disabled="disabled || title == $t('prjMember.edit.title')"
            :rules="[
              {
                required: true,
                message: '用户名必填',
              },
              {
                match: /^[a-z0-9_]*$/g,
                message: '用户名格式必须是字母、数字、下划线',
              },
            ]"
            label-col-flex="68px"
          >
            <UserSelector
              v-model="formData.username"
              placeholder="请选择/输入"
              allow-create
              :using-name="true"
              show-user-name
              show-extra-options
              @change="getUserInfo"
            />
          </a-form-item>
        </a-col> -->
        <a-col flex="320px">
          <a-form-item
            field="userNameKey"
            :label="$t('prjMember.column.name')"
            label-col-flex="68px"
            validate-trigger="input"
            :rules="[
              {
                required: true,
                message: $t('prjMember.name-required'),
              },
            ]"
          >
<<<<<<<< HEAD:src/views/project-member/components/member-manage/member-list/components/add-memberOld.vue
            <a-select
========
            <remove-spaces-input
              v-model="formData.name"
              :placeholder="$t('prjMember.please-enter-phone')"
            />
            <!-- <a-select
              v-model="formData.userNameKey"
>>>>>>>> 6c94c97 (feat: 成员管理改版):src/views/project-member/components/member-manage/member-list/components/memberDetail.vue
              allow-create
              allow-search
              v-model="formData.userNameKey"
              :loading="searchLoading"
              :filter-option="false"
              :placeholder="$t('prjMember.please-enter-name')"
              @search="getAllUserList"
              @change="userChange"
            >
              <a-option
                v-for="item of userList"
                :key="item.id"
                :value="item.userId"
              >
                <div class="user-item">
                  <span>{{ item.name }}</span>
                  <a-tag color="cyan" size="small" v-if="item.phone">{{
                    phoneDesensitize(item.phone)
                  }}</a-tag>
                </div>
              </a-option>
            </a-select> -->
            <!-- <remove-spaces-input
              v-model="formData.name"
              :placeholder="$t('prjMember.please-enter-name')"
            /> -->
            <!-- <a-input
              v-model="formData.name"
              :placeholder="$t('prjMember.please-enter-name')"
            /> -->
          </a-form-item>
        </a-col>
        <a-col flex="320px">
          <a-form-item
            field="phone"
            :label="$t('prjMember.column.phone')"
            label-col-flex="68px"
            validate-trigger="input"
            :disabled="disabled || formData.accountState == 1"
            :rules="[
              {
                required: true,
                message: $t('prjMember.phone-required'),
              },
              {
                match: /^(\+\d{1,3})?\d{7,13}$/, //正则替换  *匹配大陆港澳台
                message: $t('login.form.telInvalid'),
              },
            ]"
          >
            <remove-spaces-input
              v-model="formData.phone"
              :placeholder="$t('prjMember.please-enter-phone')"
            />
            <!-- <a-input
              v-model="formData.phone"
              :placeholder="$t('prjMember.please-enter-phone')"
            /> -->
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col flex="320px">
          <a-form-item
            field="email"
            :label="$t('prjMember.column.email')"
            validate-trigger="input"
            :rules="[
              {
                required: true,
                message: $t('prjMember.email-required'),
              },
              {
                match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, //邮箱宽松正则替换
                message: $t('userSetting.form.email.error'),
              },
            ]"
            label-col-flex="68px"
          >
            <remove-spaces-input
              v-model="formData.email"
              :placeholder="$t('prjMember.please-enter-email')"
            />
            <!-- <a-input
              v-model="formData.email"
              :placeholder="$t('prjMember.please-enter-email')"
            /> -->
          </a-form-item>
        </a-col>
        <!-- <a-col flex="320px">
          <a-form-item
            field="projectAdmin"
            :label="$t('prjMember.member-types')"
            :disabled="disabled || userId == formData.id"
            label-col-flex="68px"
          >
            <a-select
              v-model="formData.projectAdmin"
              :placeholder="$t('prjMember.please-select-member-types')"
            >
              <a-option :value="0">{{
                $t('prjMember.ordinary-member')
              }}</a-option>
              <a-option :value="1">{{
                $t('prjMember.project-manager')
              }}</a-option>
            </a-select>
          </a-form-item>
        </a-col> -->
        <a-col flex="320px">
          <a-form-item
            field="role"
            :label="$t('prjMember.column.role')"
            label-col-flex="68px"
          >
            <remove-spaces-input
              v-model="formData.role"
              :placeholder="$t('prjMember.please-enter-role')"
            />
            <!-- <a-input
              v-model="formData.role"
              :placeholder="$t('prjMember.please-enter-role')"
            /> -->
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col flex="320px">
          <a-form-item
            field="moduleVisible"
            :label="$t('prjMember.visibility-module')"
            :disabled="true"
            :rules="[
              {
                required: true,
                message: $t('prjMember.visibility-module-required'),
              },
            ]"
            :label-col-flex="i18n.global.locale == 'zh-CN' ? '96px' : '140px'"
          >
            <a-checkbox-group v-model="formData.moduleVisible">
              <a-checkbox value="0">{{ $t('prjMember.file') }}</a-checkbox>
              <a-checkbox value="1">{{
                $t('prjMember.collaboration-platform')
              }}</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch, onMounted } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { MemberRecord, addMember, updateMember } from '@/api/member';
  // import { isArray } from '@/utils/is';
  import useLoading from '@/hooks/loading';
  // import UserSelector from '@/components/user-selector/index.vue';
  import { phoneDesensitize } from '@/utils/index';
  // 导入搜索用户下拉框接口
  import { searchUser } from '@/api/user';
  import useLocale from '@/hooks/locale';
  import i18n from '@/locale/index';

  // 国际化类型
  const { currentLocale } = useLocale();

  // 用户列表改下拉框新增
  type userType = {
    id?: string;
    name?: string;
    phone?: string;
    email?: string;
    userId?: string;
  };
  const searchLoading = ref(false);
  const userList = ref<userType[]>([]);
  const getAllUserList = async (searchValue = '') => {
    searchLoading.value = true;
    const { data } = await searchUser(searchValue);
    if(data.length){
      userList.value = data;
    } else {
      userList.value = []
    }

    searchLoading.value = false;
    
  };
  const userChange = (value: any) => {

    formData.value.name = value;
    if (value && userList.value?.length) {
      const user = userList.value.find((e: any) => e?.userId === value);
      if (user?.id) {
        formData.value.phone = user.phone;
        formData.value.email = user.email;
        formData.value.name = user.name;
      } else {
        formData.value.email = ''
        formData.value.phone = ''
        formData.value.role = ''
      }
    } else {
        formData.value.email = ''
        formData.value.phone = ''
        formData.value.role = ''
    }
  };

  const init = () => {
    userList.value = [];
  };

  // onMounted(() => {
  //   getAllUserList();
  // });

  interface MemberRecordExt extends Partial<MemberRecord> {
    moduleVisible?: string[];
    userNameKey?: string;
  }
  const { t } = useI18n();
  const { loading, setLoading } = useLoading(false);

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    userId: {
      type: String,
      default: '',
    },
    form: {
      type: Object,
      default() {
        return {
          id: '',
          userId: '',
          name: '',
          email: '',
          phone: '',
          role: '',
          // projectAdmin: 0,
          moduleVisible: '0,1',
        };
      },
    },
  });
  const emit = defineEmits(['update:visible', 'refresh']);

  const formatForm = (form: MemberRecord): MemberRecordExt => {
    const { moduleVisible, ...rest } = form;
    // 默认moduleVisible为所有
    const moduleVisibleArray: string[] = ['0', '1'];
    // if (!isArray(moduleVisible)) {
    //   moduleVisibleArray = moduleVisible?.split(',') || [];
    // }
    return {
      ...rest,
      userNameKey: form.name,
      moduleVisible: moduleVisibleArray,
    };
  };
  const formData = ref<MemberRecordExt>(formatForm(props.form));
  watch(
    () => props.visible,
    (n) => {
      formData.value = formatForm(props.form || {});
    }
  );

  const selectedUser = ref<userType>({
    id: '',
    name: '',
    email: '',
    phone: '',
  });

  // 选择名字之后手机和邮箱回显
  watch(
    () => formData.value.userId,
    (newVal) => {
      if (newVal && !props.form.name) {
        selectedUser.value = {
          ...userList.value.find((item) => item.id === newVal),
        };
        formData.value.phone = selectedUser.value.phone;
        formData.value.email = selectedUser.value.email;
      }
    }
  );

  const memberRef = ref<FormInstance>();
  const handleBeforeOk = async (done: any) => {
    const res = await memberRef.value?.validate();
    if (!res) {
      setLoading(true);
      let flg = false;
      let msg = t('prjMember.success');
      if (props.title === t('prjMember.edit.title')) {
        flg = await editMember();
        msg = t('prjMember.success');
      } else if (props.title === t('prjMember.add.title')) {
        flg = await addOneMember();
      } else {
        // 查看无按钮
      }
      if (flg) {
        Message.success(msg);
        emit('update:visible', false);
        emit('refresh');
      }
      setLoading(false);
      done();
    }
  };
  const editMember = async () => {
    try {
      const { moduleVisible, userId, ...rest } = formData.value;
      const moduleVisibleString = moduleVisible?.join(',') || '';
      const res = await updateMember({
        ...rest,
        // 用传进来的用户id,避免被修改过
        userId: props.form.userId,
        moduleVisible: moduleVisibleString,
      });
      // 更新用户名单
      getAllUserList();
      return !!res.status;
    } catch (error) {
      return false;
    }
  };
  const addOneMember = async () => {
    try {
      const { moduleVisible, userId, ...rest } = formData.value;
      const moduleVisibleString = moduleVisible?.join(',') || '';
      const res = await addMember({
        ...rest,
        registerUrl: `${window.location.origin}/register-user`,
        moduleVisible: moduleVisibleString,
      });
      // 更新用户名单
      getAllUserList();
      return !!res.status;
    } catch (error) {
      return false;
    }
  };
  const handleCancel = () => {
    emit('update:visible', false);
  };
  // const getUserInfo = (val: any) => {
  //   // 此处有值表示选择，undefined表示新加或数据为空
  //   if (val) {
  //     // 赋值姓名，手机和邮箱
  //     formData.value.name = val.name;
  //     formData.value.email = val.email;
  //     formData.value.phone = val.phone;
  //     // 清除校验
  //     memberRef.value?.clearValidate(['name', 'email', 'phone']);
  //   }
  //   // else {
  //   //   formData.value.name = '';
  //   //   formData.value.email = '';
  //   //   formData.value.phone = '';
  //   // }
  // };
</script>

<script lang="ts">
  export default {
    name: 'AddMember',
  };
</script>
<style scoped lang="less">
  .user-item {
    width: 190px;
    //border: 1px solid red;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
