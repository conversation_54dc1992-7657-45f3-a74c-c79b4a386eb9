import i18n from '@/locale/index';
import { computed } from 'vue';

const { t } = i18n.global;
export const teamLevel = computed(() => [
  {
    label: t('directionary.level-1-team'),
    value: 1,
  },
  {
    label: t('directionary.level-2-team'),
    value: 2,
  },
]);
export const TeamLevelMap = computed(() => {
  const obj = {
    [String(1)]: t('directionary.level-1-team'),
    [String(2)]: t('directionary.level-2-team'),
  };
  return obj;
});

export const projectStatus = computed(() => [
  {
    label: t('directionary.normal'),
    value: 10,
  },
  {
    label: t('directionary.Suspension-of-work'),
    value: 20,
  },
  {
    label: t('directionary.delay'),
    value: 30,
  },
]);
export const ProjectStatusMap = computed(() => {
  const obj = {
    [String(10)]: t('directionary.normal'),
    [String(20)]: t('directionary.Suspension-of-work'),
    [String(30)]: t('directionary.delay'),
  };
  return obj;
});

export const projectPhase = computed(() => [
  {
    label: t('directionary.conceptual-design'),
    value: 10,
  },
  {
    label: t('directionary.design-feasibility-study'),
    value: 20,
  },
  {
    label: t('directionary.preliminary-design'),
    value: 30,
  },
  {
    label: t('directionary.construction-drawing-design'),
    value: 40,
  },
  {
    label: t('directionary.construction'),
    value: 50,
  },
  {
    label: t('directionary.operation'),
    value: 60,
  },
]);
export const ProjectPhaseMaps = computed(() => {
  const obj = {
    [String(10)]: t('directionary.conceptual-design'),
    [String(20)]: t('directionary.design-feasibility-study'),
    [String(30)]: t('directionary.preliminary-design'),
    [String(40)]: t('directionary.construction-drawing-design'),
    [String(50)]: t('directionary.construction'),
    [String(60)]: t('directionary.operation'),
  };
  return obj;
});

export const projectTypes = computed(() => [
  {
    label: t('directionary.highway-engineering'),
    value: 1,
  },
  {
    label: t('directionary.city-road-engineering'),
    value: 2,
  },
  {
    label: t('directionary.airport-runway-engineering'),
    value: 3,
  },
  {
    label: t('directionary.urban-railway-transportation-engineering'),
    value: 4,
  },
  {
    label: t('directionary.bridge-engineering'),
    value: 5,
  },
  {
    label: t('directionary.culvert-engineering'),
    value: 6,
  },
  {
    label: t('directionary.port-engineering'),
    value: 7,
  },
  {
    label: t('directionary.channel-engineering'),
    value: 8,
  },
  {
    label: t('directionary.tunnel-engineering'),
    value: 9,
  },
  {
    label: t('directionary.building-engineering'),
    value: 10,
  },
  {
    label: t('directionary.water-engineering'),
    value: 11,
  },
]);

export const ProjectTypeMaps = computed(() => {
  const obj = {
    [String(1)]: t('directionary.highway-engineering'),
    [String(2)]: t('directionary.city-road-engineering'),
    [String(3)]: t('directionary.airport-runway-engineering'),
    [String(4)]: t('directionary.urban-railway-transportation-engineering'),
    [String(5)]: t('directionary.bridge-engineering'),
    [String(6)]: t('directionary.culvert-engineering'),
    [String(7)]: t('directionary.port-engineering'),
    [String(8)]: t('directionary.channel-engineering'),
    [String(9)]: t('directionary.tunnel-engineering'),
    [String(10)]: t('directionary.building-engineering'),
    [String(11)]: t('directionary.water-engineering'),
  };
  return obj;
});

export const projectCoordinate = computed(() => [
  {
    label: t('directionary.wgs-84-coordinate-system'),
    value: 3,
  },
  {
    label: t('directionary.2000-national-geodetic-coordinate-system'),
    value: 4,
  },
  {
    label: t('directionary.changchun-coordinate-system'),
    value: 5,
  },
  {
    label: t('directionary.local-elevation-system'),
    value: 6,
  },
]);

export const ProjectCoordinateMaps = computed(() => {
  const obj = {
    [String(3)]: t('directionary.84'),
    [String(4)]: t('directionary.2000-national-geodetic'),
    [String(5)]: t('directionary.changchun'),
    [String(6)]: t('directionary.local-elevation-system'),
  };
  return obj;
});

export const projectElevation = computed(() => [
  {
    label: t('directionary.1985-national-height-datum'),
    value: 2,
  },
  {
    label: t('directionary.wusong-height-datum'),
    value: 3,
  },
  {
    label: t('directionary.zhujiang-height-datum'),
    value: 4,
  },
  {
    label: t('directionary.guangzhou-height-datum'),
    value: 5,
  },
  {
    label: t('directionary.Bohai-height'),
    value: 6,
  },
]);

export const ProjectElevationMaps = computed(() => {
  const obj = {
    [String(2)]: t('directionary.1985-national-height-datum'),
    [String(3)]: t('directionary.wusong-height-datum'),
    [String(4)]: t('directionary.zhujiang-height-datum'),
    [String(5)]: t('directionary.guangzhou-height-datum'),
    [String(6)]: t('directionary.Bohai-height'),
  };
  return obj;
});

export const projectEnvironmentType = computed(() => [
  {
    label: t('directionary.class-I'),
    value: 1,
  },
  {
    label: t('directionary.class-II'),
    value: 2,
  },
  {
    label: t('directionary.class-III'),
    value: 3,
  },
  {
    label: t('directionary.class-IV'),
    value: 4,
  },
  {
    label: t('directionary.class-V'),
    value: 5,
  },
  {
    label: t('directionary.class-VI'),
    value: 6,
  },
  {
    label: t('directionary.class-VII'),
    value: 7,
  },
]);

export const ProjectEnvironmentTypeMaps = computed(() => {
  const obj = {
    [String(1)]: t('directionary.class-I'),
    [String(2)]: t('directionary.class-II'),
    [String(3)]: t('directionary.class-III'),
    [String(4)]: t('directionary.class-IV'),
    [String(5)]: t('directionary.class-V'),
    [String(6)]: t('directionary.class-VI'),
    [String(7)]: t('directionary.class-VII'),
  };
  return obj;
});

export const projectMagnitude = computed(() => [
  {
    label: t('directionary.6-degree-0.05g'),
    value: 1,
  },
  {
    label: t('directionary.7-degree-0.1g'),
    value: 2,
  },
  {
    label: t('directionary.7-degree-0.15g'),
    value: 3,
  },
  {
    label: t('directionary.8-degree-0.2g'),
    value: 4,
  },
  {
    label: t('directionary.8-degree-0.3g'),
    value: 5,
  },
  {
    label: t('directionary.9-degree-0.4g'),
    value: 6,
  },
]);

export const ProjectMagnitudeMaps = computed(() => {
  const obj = {
    [String(1)]: t('directionary.6-degree-0.05g'),
    [String(2)]: t('directionary.7-degree-0.1g'),
    [String(3)]: t('directionary.7-degree-0.15g'),
    [String(4)]: t('directionary.8-degree-0.2g'),
    [String(5)]: t('directionary.8-degree-0.3g'),
    [String(6)]: t('directionary.9-degree-0.4g'),
  };
  return obj;
});

export const projectRoadlevel = computed(() => [
  {
    label: t('directionary.high-speed'),
    value: 1,
  },
  {
    label: t('directionary.first-level'),
    value: 2,
  },
  {
    label: t('directionary.second-level'),
    value: 3,
  },
  {
    label: t('directionary.third-level'),
    value: 4,
  },
  {
    label: t('directionary.fourth-level'),
    value: 5,
  },
  {
    label: t('directionary.other-high-speed'),
    value: 6,
  },
  {
    label: t('directionary.expressway'),
    value: 7,
  },
  {
    label: t('directionary.main-secondary'),
    value: 8,
  },
  {
    label: t('directionary.branch-road'),
    value: 9,
  },
]);

export const projectRoadlevelMaps = computed(() => {
  const obj = {
    [String(1)]: t('directionary.high-speed'),
    [String(2)]: t('directionary.first-level'),
    [String(3)]: t('directionary.second-level'),
    [String(4)]: t('directionary.third-level'),
    [String(5)]: t('directionary.fourth-level'),
    [String(6)]: t('directionary.other-high-speed'),
    [String(7)]: t('directionary.expressway'),
    [String(8)]: t('directionary.main-secondary'),
    [String(9)]: t('directionary.branch-road'),
  };
  return obj;
});

export const projectDesignLoad = computed(() => [
  {
    label: t('directionary.highway-I-level'),
    value: 1,
  },
  {
    label: t('directionary.highway-II-level'),
    value: 2,
  },
  {
    label: t('directionary.city-A-level'),
    value: 3,
  },
  {
    label: t('directionary.city-B-level'),
    value: 4,
  },
]);

export const projectDesignLoadMaps = computed(() => {
  const obj = {
    [String(0)]: t('directionary.highway-I-level'),
    [String(1)]: t('directionary.highway-II-level'),
    [String(2)]: t('directionary.city-A-level'),
    [String(3)]: t('directionary.city-B-level'),
  };
  return obj;
});

export const projectSecurityLevel = computed(() => [
  {
    label: t('directionary.first-level-1'), // 这里有一个重复的 "一级"，可能需要您提供更准确的数据
    value: 1,
  },
  {
    label: t('directionary.second-level-2'), // 这里有一个重复的 "二级"，可能需要您提供更准确的数据
    value: 2,
  },
]);

export const projectSecurityLevelMaps = computed(() => {
  const obj = {
    [String(1)]: t('directionary.first-level-1'),
    [String(2)]: t('directionary.second-level-2'),
  };
  return obj;
});
export const projectProperties = computed(() => [
  {
    label: t('directionary.regularProjects'),
    value: 1,
  },
  {
    label: t('directionary.pilotProject'),
    value: 2,
  },
  {
    label: t('directionary.navigationProject'),
    value: 3,
  },
  {
    label: t('directionary.training-project'),
    value: 5,
  },
  {
    label: t('directionary.othersProject'),
    value: 4,
  },
]);

export const integratedProductApplication = computed(() => [
  {
    label: t('directionary.HRDPS'),
    value: '1',
  },
  {
    label: t('directionary.DRTS'),
    value: '2',
  },
  {
    label: t('directionary.DRDS'),
    value: '3',
  },
  {
    label: t('directionary.DBDS'),
    value: '4',
  },
  {
    label: t('directionary.DTDS'),
    value: '5',
  },
  {
    label: t('directionary.HBBDCI'),
    value: '6',
  },
  {
    label: t('directionary.DLS'),
    value: '7',
  },
  {
    label: t('directionary.DCHDS'),
    value: '8',
  },
  {
    label: t('directionary.HEBC'),
    value: '9',
  },
  {
    label: t('directionary.NDRDS'),
    value: '10',
  },
  {
    label: t('directionary.NDBDS'),
    value: '11',
  },
  {
    label: t('directionary.NTDDS'),
    value: '12',
  },
  {
    label: t('directionary.NCHDS'),
    value: '13',
  },
  {
    label: t('directionary.NTES'),
    value: '14',
  },
  {
    label: t('directionary.NHPCDS'),
    value: '15',
  },
  {
    label: t('directionary.NGCSDS'),
    value: '16',
  },
  {
    label: t('directionary.NAASDS'),
    value: '17',
  },
  {
    label: t('directionary.DRCRS'),
    value: '18',
  },
  {
    label: t('directionary.GEMS'),
    value: '19',
  },
  {
    label: t('directionary.VCS'),
    value: '20',
  },
  {
    label: t('directionary.CDexCMS'),
    value: '21',
  },
  {
    label: t('directionary.AIDE-IDE'),
    value: '22',
  },
]);

export default null;
