<template>
  <div class="security-setting-container">
    <a-row>
      <!-- <h2 class="personal-info-title">{{ $t('userSetting.title.editPwd') }}</h2> -->
      <table-title
          :title="$t('userSetting.title.editPwd')"
          class="personal-info-title"
        ></table-title>
    </a-row>
    <a-form ref="formRef" :model="formData" class="form" layout="vertical">
      <a-row :gutter="40">
        <a-row>
          <!-- 手机号输入框 -->
          <a-col :span="8.2">
            <a-form-item
              field="phone"
              :rules="[
                { required: true, message: $t('login.form.telRequired') },
                {
                  pattern: /^(\+\d{1,3})?\d{7,13}$/,
                  message: $t('login.form.telInvalid'),
                },
              ]"
              :validate-trigger="['change', 'blur']"
              :label="$t('userSetting.form.phone')"
              label-col-flex="80px"
              class="userInfoFormItem"
            >
              <a-input
                v-model="formData.phone"
                :placeholder="$t('login.form.telPlaceholder')"
                :maxlength="18"
                disabled
              />
            </a-form-item>
          </a-col>

          <!-- 旧密码输入框 -->
          <!-- <a-row justify="start" align="start" class="form-row">
          <a-col :span="18">
            <a-form-item
              field="oldPwd"
              :label="$t('userSetting.form.oldPwd')"
              class="form-desc"
              :rules="[
                {
                  required: true,
                  message: $t('userSetting.form.oldPwd.required'),
                },
              ]"
            >
              <a-input-password
                v-model="formData.oldPwd"
                :placeholder="$t('userSetting.form.placeholder.common')"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row> -->

          <!-- 新密码输入框 -->
          <a-col :span="8.2">
            <a-form-item
              field="newPwd"
              :label="$t('userSetting.form.newPwd')"
              :rules="[
                {
                  required: true,
                  message: $t('userSetting.form.newPwd.required'),
                },
              ]"
              label-col-flex="80px"
              class="userInfoFormItem"
            >
              <a-input-password
                v-model="formData.newPwd"
                :placeholder="$t('userSetting.form.placeholder.common')"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <!-- 确认新密码输入框 -->
          <a-col :span="8.2">
            <a-form-item
              field="enterPwd"
              :label="$t('userSetting.form.enterPwd')"
              :rules="[
                {
                  required: true,
                  message: $t('userSetting.form.enterPwd.required'),
                },
              ]"
              label-col-flex="80px"
              class="userInfoFormItem"
            >
              <a-input-password
                v-model="formData.enterPwd"
                :placeholder="$t('userSetting.form.placeholder.common')"
                allow-clear
              />
              <template #extra>
                <div>{{ $t('userSetting.password-validation') }}</div>
              </template>
            </a-form-item>
          </a-col>

          <!-- 验证码输入框 -->
          <a-col :span="8.2">
            <a-form-item
              field="captcha"
              :rules="[
                { required: true, message: $t('login.form.captchaRequired') },
              ]"
              :validate-trigger="['change', 'blur']"
              :label="$t('userSetting.form.captcha')"
              label-col-flex="80px"
              class="userInfoFormItem"
            >
              <a-input
                v-model="formData.captcha"
                :placeholder="$t('login.form.captchaPlaceholder')"
                :maxlength="50"
              >
                <template #append>
                  <a-button
                    type="text"
                    :loading="smsLoading"
                    @click="getSMSCaptcha"
                  >
                    <span v-if="countDown === -2" class="captcha-word">
                      {{ $t('login.form.getCaptcha') }}
                    </span>
                    <span v-else-if="countDown === -1" class="captcha-word">
                      {{ $t('login.form.regainCaptcha') }}
                    </span>
                    <span v-else class="captcha-word">
                      {{ `${countDown}s` }}
                    </span>
                  </a-button>
                </template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-row>
    </a-form>

    <!-- 底部按钮 -->
    <div class="bottom">
      <a-button
        type="primary"
        :loading="loading"
        @click="validate"
        class="common-button"
      >
        {{ $t('userSetting.save') }}
      </a-button>
      <!-- <a-button type="secondary" @click="reset">
        {{ $t('userSetting.reset') }}
      </a-button> -->
    </div>
  </div>
</template>

<script setup>
  import { computed, ref, watch } from 'vue';
  import { modifyPassword } from '@/api/user';
  import { Message } from '@arco-design/web-vue';
  import pwdEncrypt from '@/utils/encryption/pwd';
  import useLoading from '@/hooks/loading';
  import { useI18n } from 'vue-i18n';
  import { getPwdSms, getSms } from '@/api/modules/user';
  import { editPassword } from '@/api/user';
  import checkPassWordFormat from '@/api/password-validation';
  import { useUserStore } from '@/store';
  import TableTitle from '@/components/table-title/index.vue';


  // 获取用户存储状态
  const userStore = useUserStore();

  // 加载状态管理
  const { loading, setLoading } = useLoading();

  // 国际化文本
  const { t } = useI18n();

  // 当前用户邮箱地址
  const email = computed(() => userStore.email || '');

  // 表单引用
  const formRef = ref(null);

  // 接口节流限制
  const canSendEditPasswordRequest = ref(true);

  // 表单数据
  const formData = ref({
    phone: userStore.phone,
    // oldPwd: '',
    newPwd: '',
    enterPwd: '',
    captcha: '',
  });

  // 监听用户手机号变化
  watch(
    () => userStore.phone,
    (newPhone) => {
      formData.value.phone = newPhone;
    }
  );

  // 验证码倒计时
  const countDown = ref(-2);

  // 验证码获取加载状态
  const smsLoading = ref(false);

  // 验证码唯一标识
  const captchaKey = ref('');

  /**
   * 更新验证码倒计时
   */
  const updateCountDown = () => {
    countDown.value = 90;
    const counter = setInterval(() => {
      if (countDown.value === 0) {
        clearInterval(counter);
        countDown.value = -1;
      } else {
        countDown.value--;
      }
    }, 1000);
  };

  /**
   * 获取短信验证码
   */
  const getSMSCaptcha = async () => {
    // 如果验证码正在倒计时，提示用户等待
    if (countDown.value >= 0) {
      Message.warning(t('login.form.captchaHoldOn'));
      return;
    }

    // 验证手机号格式
    const res = await formRef.value?.validateField('phone');
    if (res) return;

    smsLoading.value = true;
    try {
      // 调用接口获取验证码
      // const params = {
      //   phone: formData.value.phone,
      //   email: email.value,
      //   phoneReceive: '1',
      // };
      const { data } = await getSms(formData.value.phone);
      captchaKey.value = data;
      console.log(captchaKey.value);
    } catch (err) {
      // 处理错误
      if (typeof err === 'string') {
        Message.error(err);
      }
    } finally {
      // 更新验证码倒计时并关闭加载状态
      smsLoading.value = false;
      updateCountDown();
    }
  };

  /**
   * 表单验证和提交
   */
  const validate = async () => {
    // 如果正在加载，直接返回
    if (loading.value) return;

    // 验证表单
    const res = await formRef.value?.validate();
    if (!res) {
      // 验证新密码和确认密码是否一致
      if (formData.value.newPwd !== formData.value.enterPwd) {
        Message.error(t('userSetting.password.edit.error'));
        return;
      }

      // 验证密码格式
      if (!checkPassWordFormat(formData.value.newPwd)) {
        Message.error(t('userSetting.password-rule-error'));
        return;
      }

      // 设置加载状态
      setLoading(true);
      if (!canSendEditPasswordRequest.value) {
        Message.error(t('setup.frequent.request.message'));
        return;
      }
      canSendEditPasswordRequest.value = false;
      try {
        // 准备提交参数
        const params = {
          pwd: pwdEncrypt(formData.value.newPwd),
          // oldPwd: pwdEncrypt(formData.value.oldPwd),
          phone: formData.value.phone,
          captcha: formData.value.captcha,
          key: captchaKey.value,
        };
        console.log(params);
        //  提交修改密码请求
        const result = await editPassword(params);
        if (result.status) {
          formData.value.captcha = '';
          formData.value.newPwd = '';
          formData.value.enterPwd = '';
          Message.success(t('userSetting.password.edit.success'));
        }
      } catch (e) {
        // 捕获并处理错误
      } finally {
        // 关闭加载状态
        setLoading(false);
        canSendEditPasswordRequest.value = true;
      }
    }
  };

  /**
   * 重置表单
   */
  const reset = async () => {
    //  暂不需要 请求重置接口
    await formRef.value?.resetFields();
  };
</script>

<style scoped lang="less">
  .security-setting-container {
    height: 100%;
    width: 100%;
    overflow: auto;
    position: relative;
    box-sizing: border-box;
    min-width: 1000px;
  }

  .personal-info-title {
    padding: 0;
    margin: 20px;
  }

  .form {
    width: 100%;
    box-sizing: border-box;
    padding: 0 20px;

    .userInfoFormItem {
      width: 100%;
      margin-right: 20px;
      :deep(.arco-input-append) {
        background-color: #ffffff;
        // border-left: 1px solid #c9cdd4;
        padding: 0;
        :deep(.arco-btn) {
          border-radius: 4px;
          border-left: 1px solid #c9cdd4;
          &:hover {
            background-color: #ffffff !important;
          }
        }
      }
    }

    :deep(.arco-form-item-label) {
      font-size: 16px;
    }

    :deep(.arco-form-item-wrapper-col) {
      width: 400px;
    }

    :deep(.arco-form-item-content) {
      border: 1px solid #c9cdd4;
      border-radius: 4px;
      overflow: hidden;
    }

    :deep(.arco-input-wrapper) {
      border: none !important;
      background-color: transparent;
    }

    :deep(.arco-input-focus) {
      border: none;
    }

    :deep(.arco-form-item) {
      margin-bottom: 24px;
    }

    :deep(.arco-row) {
      margin: 0 !important;
    }
  }

  .bottom {
    padding: 0 20px;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    align-items: center;
  }

  .common-button {
    height: 32px;
    font-size: 14px;
    padding: 0 16px;
    border-radius: 4px;
    margin-right: 20px;
  }
</style>
