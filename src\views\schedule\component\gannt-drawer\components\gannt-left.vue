<template>
  <div class="left-container">
    <a-input-search
      v-model="searchValue"
      class="search-input"
      allow-clear
      placeholder="请输入搜索内容"
      @press-enter="searchTreeData"
      @clear="searchTreeData"
    />
    <div class="head">
      <tableTitle :title="'事项名称'" />
      <a-button type="text" @click="addMatter">
        <template #icon><icon-plus /></template>
        <template #default>新建</template>
      </a-button>
      <a-button type="text" @click="handleBatchImport">
        <template #icon><icon-launch /></template>
        <template #default>批量导入</template>
      </a-button>
    </div>

    <div class="tree-content">
      <a-tree
        ref="folderTree"
        :key="treekey"
        block-node
        default-expand-all
        :default-expanded-keys="expandedKeys"
        v-model:selected-keys="selectedKeys"
        :data="treeData"
        :field-names="{
          key: 'id',
          title: 'name',
        }"
        size="large"
        @select="nodeClick"
        @expand="nodeExpand"
      >
        <template #switcher-icon="node">
          <IconDown
            v-if="
              !('children' in node) ||
              !node.children ||
              node.children?.length > 0
            "
          />
        </template>

        <template #icon="node">
          <file-image :file-data="node.node" />
        </template>
      </a-tree>
    </div>
  </div>
  <!-- 创建事项 -->
  <createDrawer @refresh="refreshData"></createDrawer>
  <!-- 批量导入 -->
  <batchImport
    ref="batchImportRef"
    :project-id="currentProjectId"
    :schedule-panel-id="projectCalendarId"
  />
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { storeToRefs } from 'pinia';
  import tableTitle from '@/components/table-title/index.vue';
  import useScheduleStore from '@/store/modules/schedule/index';
  import { usegGanntDrawerStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';

  import { queryTreeData } from '../api';
  import { getProjectPanel } from '@/views/create-schedule/api';
  import { useRoute } from 'vue-router';

  import createDrawer from './createDrawer.vue';
  import batchImport from './batchImport.vue';
  // import fileImage from './image-file.vue';

  const userId = getUserId() || '';
  const gGanntDrawerStore = usegGanntDrawerStore();
  const currentProjectId = ref(getLocalstorage(`last_project_${userId}`) || '');
  const projectCalendarId = ref(''); // 日程数据
  const refreshData = () => {
    // 刷新甘特图左侧树的数据
    getTreeData();
  };
  const route = useRoute();

  const scheduleStore = useScheduleStore();

  const { treeData, expandedKeys, selectedKeys } = storeToRefs(scheduleStore);

  const treekey = ref('started');

  const searchValue = ref<string>('');
  function assignIdToNode(node: any) {
    // 优先使用 scheduleDetailId，否则用 milestoneId
    node.id = node.scheduleDetailId || node.milestoneId || '';
    node.name = node.agendaName || node.milestoneName || '';

    // 如果有 children，则递归处理
    if (Array.isArray(node.children)) {
      node.children.forEach(assignIdToNode);
    }
  }

  // 获取树形数据
  const getTreeData = async () => {
    const params = {
      pageNo: 1,
      pageSize: 9999,
      keyName: searchValue.value,
      projectId: route.params.projectId,
    };
    const res = await queryTreeData(params);
    const initTreeData = res.data || [];
    initTreeData.forEach(assignIdToNode);
    scheduleStore.setTreeData(initTreeData);
    // console.log('initTreeData >', initTreeData);
  };

  // 点击树节点事件
  const nodeClick = async (selectedNodes: string[], nodeData: any) => {
    if (!nodeData.node.milestoneId) {
      gGanntDrawerStore.setCreateVisible(true);
      gGanntDrawerStore.setDrawerType('edit');
      await gGanntDrawerStore.setMatterData(selectedNodes[0]);
    }

    scheduleStore.setSelectedKeys(selectedNodes);
  };

  // 节点展开或关闭
  const nodeExpand = (expandKeys: string[]) => {
    scheduleStore.setExpandedKeys(expandKeys);
  };

  // 查询树数据
  const searchTreeData = () => {
    getTreeData();
  };
  const addMatter = () => {
    gGanntDrawerStore.setCreateVisible(true);
    gGanntDrawerStore.setDrawerType('new');
    gGanntDrawerStore.setMatterData({});
  };
  // 批量导入
  const handleBatchImport = () => {
    gGanntDrawerStore.setBatchImportVisible(true);
  };
  // 获取日历数据
  // 获取日历数据
  const getPanelListHandle = async () => {
    const projectParam = {
      projectId: currentProjectId.value,
    };
    const res = await getProjectPanel(projectParam);
    projectCalendarId.value = res.data?.id || '';
  };

  onMounted(() => {
    getTreeData();
    getPanelListHandle();
  });
</script>

<script lang="ts">
  export default {
    name: 'GanntLeft',
  };
</script>

<style lang="less" scoped>
  .left-container {
    position: relative;
    min-width: 350px;
    width: 24%;
    height: 100%;
    overflow-y: auto;
    border-right: 1px solid #d9d9d9;

    .search-input {
      margin: 15px 20px 15px 20px; // 上右下左
      width: calc(100% - 40px);
    }

    .head {
      border-top: 1px solid #d9d9d9;
      border-bottom: 1px solid #d9d9d9;
      height: 60px;
      padding: 18px 20px;
      display: flex;
      align-items: center;
    }

    .tree-content {
      padding: 28px 16px 28px 16px;
      height: calc(100% - 122px);
      overflow: auto;

      :deep(.arco-tree-node-title:hover) {
        background-color: #e8f2ff;
      }
      :deep(.arco-tree-node-selected) {
        background-color: #e8f2ff;
      }
      :deep(.arco-tree-node:hover) {
        background-color: #e8f2ff;
      }
    }
  }
</style>
