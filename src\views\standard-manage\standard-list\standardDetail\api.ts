import axios from 'axios';
import { getXBaseToken } from '@/utils/auth';

export interface standSetFileParams {
  groupId: string;
  standardId: string;
  fileId: Array<[]>;
  fileType: string;
}

export interface standardFormInter {
  code: string;
  name: string;
  englishName: string;
  description: string;
  existStandardId: string;
  groupId: string;
  id: string;
  attributeLink: string;
  xbaseStandardId: string;
  standardType: number;
  xbaseStandardStatus: number;
  xbaseStandardName: string;
  xbaseCollectionId: string;
}
export interface attrFormParams {
  pageNo: number;
  pageSize: number;
  name: string;
  groupId: string;
}

export interface attributeFormInter {
  name: string;
  type: string;
  dimension: string;
  ruleList: Array<[]>;
  emptyStatus: string;
  dimensionId: string;
  id: string;
}

export interface standardData {
  id: string;
  name: string;
  englishName: string;
  code: string;
  description: string;
  existStandardId: string;
  groupId: string;
  standardId: string;
}

export interface attributeData {
  attributeId: string;
  emptyStatus: string;
  newSort: string;
  standardId: string;
}

export interface standardSelFileData {
  archiveId: string;
  fileId: string;
  fileType: string;
  groupId: string;
  id: string;
  standardId: string;
}

export interface standardParams extends Partial<standardData> {
  pageNo?: number;
  pageSize?: number;
  standardId?: string;
  groupId?: string;
}

export interface standardListRes {
  code: string;
  name: string;
  englishName: string;
  description: string;
  existStandardId: string;
  message: string;
  id: string;
  attributeLink: string;
  xbaseStandardId: string;
  standardType: number;
  xbaseStandardName: string;
  xbaseCollectionId: string;
}
export interface optionInter {
  id: string;
  name: string;
}
export interface enclosureFormInter {
  attributeId: string;
  emptyStatus: string;
}

export interface attrDetailRes {
  name: string;
  type: number;
  emptyStatus: string;
  dimensionId: string;
  id: string;
  sort: string;
  viewStatus: string;
  ruleList: Array<any>;
  list?: Array<any>;
}

// 查询标准列表-分页
export function standarDetailList(id: any) {
  return axios.get<standardListRes>(`/asset-system/standard/detail?id=${id}`);
}
export function standardList(params: standardParams) {
  return axios.get<any>('/asset-system/standard/list', {
    params,
  });
}

// 获取属性详情
export function getAttributeDetail(id: any, standardId: any) {
  return axios.get<attrDetailRes>(
    `/asset-system/attribute/detail?id=${id}&standardId=${standardId}`
  );
}

// 获取已绑定属性
export function getAttribute(params: standardParams) {
  return axios.get<attrDetailRes>('/asset-system/standard/get-attribute', {
    params,
  });
}

// 添加属性
export function addAttribute(data: attributeData) {
  const param: attributeData = { ...data };
  return axios.post<standardListRes>(
    '/asset-system/standard/save-attribute',
    param
  );
}

// 移除已绑定属性
export function removeAttribute(data: attributeData) {
  const param: attributeData = { ...data };
  return axios.post<standardListRes>(
    '/asset-system/standard/romve-attribute',
    param
  );
}

// 排序
export function attributeSort(data: any) {
  const param: any = { ...data };
  return axios.post<standardListRes>(
    '/asset-system/standard/move-attribute',
    param
  );
}
export function getStandardSelFile(params: any) {
  return axios.get<any>('/asset-system/archiveStandard/list', {
    params,
  });
}
// 获取所有标准已绑定的文件数据
export function getStandardSelFileAll() {
  return axios.post<any>('/asset-system/archiveStandard/allFile');
}

// 设置标准作用文件
export function standardSelFile(data: any) {
  return axios.post<any>('/asset-system/archiveStandard/saveAndUpdate', data);
}

// 修改属性展示状态
export function saveAttribute(data: any) {
  const param: any = { ...data };
  return axios.post<any>('/asset-system/standard/save-attribute', param);
}

// 获取xbase属性信息标准
export function getStandadAttribute(params: any) {
  return axios.get('/api/open/v1/standardization/property/content', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 通过属性信息标准生成snl
export function getStandadAttributeSnl(params: any) {
  return axios.post(
    `/api/open/v1/model-check/snl/import/data-standard`,
    params,
    {
      headers: {
        Authorization: `Bearer ${getXBaseToken() || ''}`,
      },
      baseURL: '/x_base_api',
    }
  );
}

export function deleteStandadCollection(params: any) {
  return axios.post(`/api/open/v1/model-check/collection/delete`, params, {
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}
