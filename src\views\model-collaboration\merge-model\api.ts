import { getXBaseToken } from '@/utils/auth';
import axios from 'axios';

export interface mergeParams {
  pageNo: number;
  pageSize: number;
}

// 查询列表-分页
export function queryMergeList(params: any) {
  return axios.get('/cde-collaboration/file/list', {
    params,
  });
}

// 创建合模
export function addMergaFile(data: any) {
  return axios.post('/cde-collaboration/file/save-model', data);
}

export function getFileInfoById(id?: string) {
  return axios.get('/cde-collaboration/file/detail', {
    params: {
      id,
    },
  });
}

export const createXBaseModelMerge = (data: any) => {
  return axios({
    method: 'POST',
    url: '/api/open/v1/semantic-model/assembly',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
};

// 查询语义模型列表
export function getXBaseSemanticModelList(params: any) {
  return axios.get('/api/open/v1/semantic-model/list', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// 查询团队列表
export function queryTeamList(params: any) {
  return axios.get('/cde-collaboration/team/list', {
    params,
  });
}

// 模型管理服务-模型装配
export const createXBaseNonSemanticModelMerge = (data: any) => {
  return axios({
    method: 'POST',
    url: '/api/open/v1/model/assembly',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
};

// 非语义模型合模（走后端）
export function createXBaseNoSemanticModel(data: any) {
  return axios.post('/cde-collaboration/xbase/modelAssembly', data);
}

// 语义模型合模（走后端）
export function createXBaseSemanticModel(params: any) {
  return axios.get('/cde-collaboration/xbase/semanticModelAssembly', {
    params,
  });
}

// 文件删除
export function deleteFile(data: any) {
  return axios.post('/cde-collaboration/file/batch-delete/file', data);
}
