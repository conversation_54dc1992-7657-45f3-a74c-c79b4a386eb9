<template>
  <a-dropdown
    v-model:popup-visible="popupVisible"
    :hide-on-select="false"
    @select="handleSelect($event)"
  >
    <icon-more class="has-pointer" @click="popupVisible = !popupVisible" />
    <template #content>
      <a-doption value="rename">
        <template #icon>
          <icon-pen />
        </template>
        <template #default>{{ t('knowledgenew.rename') }}</template>
      </a-doption>
      <a-doption value="move">
        <template #icon>
          <icon-share-internal />
        </template>
        <template #default>{{ t('knowledgenew.move') }}</template>
      </a-doption>
      <a-doption
        v-if="
          !isNeedToConvert(props.item.status, props.item.isCombination) &&
          props.item.folderId &&
          isRequiredConversion(props.item)
        "
        value="reConvert"
      >
        <template #icon>
          <reConvert style="width: 14px; height: 14px" />
        </template>
        <template #default>{{ t('cloud.reConvert') }}</template>
      </a-doption>
      <!-- <a-popconfirm
        content="确认删除这个文件/文件夹吗"
        type="info"
        position="left"
        @ok="() => handleDeleteAction('delete', props.item)"
      >
        <div @click.stop>
          <a-doption value="delete">
            <template #icon>
              <icon-delete />
            </template>
            <template #default>删除</template>
          </a-doption>
        </div>
      </a-popconfirm> -->
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
  import { defineEmits, ref, defineProps, watch } from 'vue';
  import i18n from '@/locale/index';
  import {
    isNeedToConvert,
    isRequiredConversion,
  } from '@/views/projectSpace/file/utils';
  import reConvert from '@/assets/images/knowledge-base/reConvert.svg';

  const { t } = i18n.global;
  const props = defineProps({
    item: {
      type: Object,
      required: true,
    },
  });
  const emits = defineEmits(['action']);
  const popupVisible = ref(false);
  const handleSelect = (value: string) => {
    if (value === 'delete') {
      return;
    }
    popupVisible.value = false;
    emits('action', { type: value, item: props.item });
  };
  const handleDeleteAction = (type: string, item: any) => {
    popupVisible.value = false;
    emits('action', { type, item });
  };
  watch(
    () => popupVisible.value,
    (val) => {
      emits('action', { type: 'popupVisible', item: props.item, value: val });
    }
  );
</script>

<style scoped lang="less"></style>
