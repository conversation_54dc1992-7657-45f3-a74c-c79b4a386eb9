import usePermissionStore from '@/store/modules/permission';
import { DirectiveBinding, computed } from 'vue';

export default {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    // 兼容以前没传值的用法，或者逻辑判断后需要放开权限校验的情况
    if (!binding.value) return;
    const permissionStore = usePermissionStore();
    const btns = computed(() => permissionStore.permissionBtns);

    const requiredPermission = binding.value;

    let hasPermission = btns.value.includes(requiredPermission);
    console.log(requiredPermission, hasPermission, 12);

    // 团队权限，可被更高级权限覆盖
    if (!hasPermission && requiredPermission.includes('_')) {
      hasPermission = btns.value.includes(requiredPermission.split('_')[1]);
    }

    if (!hasPermission) {
      // 移除该元素
      el.parentNode?.removeChild(el);
    }
  },
};
