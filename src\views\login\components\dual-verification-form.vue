<template>
  <a-form
    ref="loginForm"
    :model="userInfo"
    class="login-form"
    layout="vertical"
    @submit="handleSubmit"
  >
    <a-form-item
      field="username"
      :rules="[{ required: true, message: $t('login.form.userName.errMsg') }]"
      :validate-trigger="['change', 'blur']"
      hide-label
    >
      <a-input
        v-model="userInfo.username"
        :placeholder="$t('login.form.userName.placeholder')"
      >
        <template #prefix>
          <icon-user :size="20" />
        </template>
      </a-input>
    </a-form-item>
    <a-form-item
      field="password"
      :rules="[{ required: true, message: $t('login.form.password.errMsg') }]"
      :validate-trigger="['change', 'blur']"
      hide-label
    >
      <a-input-password
        v-model="userInfo.password"
        :placeholder="$t('login.form.password.placeholder')"
        allow-clear
      >
        <template #prefix>
          <icon-lock :size="20" />
        </template>
      </a-input-password>
    </a-form-item>
    <a-form-item
      v-if="checked"
      field="captcha_code"
      :rules="[{ required: true, message: $t('login.form.captchaRequired') }]"
      :validate-trigger="['change', 'blur']"
      hide-label
    >
      <a-input
        v-model="userInfo.captcha_code"
        :placeholder="$t('login.form.captchaPlaceholder')"
        :maxlegth="50"
      >
        <template #prefix>
          <icon-safe :size="20" />
        </template>
        <template #append>
          <a-button type="text" :loading="smsLoading" @click="getSMSCaptcha">
            <span v-if="countDown === -2" class="captcha-word">{{
              $t('login.form.getCaptcha')
            }}</span>
            <span v-else-if="countDown === -1" class="captcha-word">{{
              $t('login.form.regainCaptcha')
            }}</span>
            <span v-else class="captcha-word">{{ `${countDown}s` }}</span>
          </a-button>
        </template>
      </a-input>
    </a-form-item>
    <a-space :size="16" direction="vertical">
      <div class="login-form-password-actions">
        <a-checkbox
          checked="rememberPassword"
          :model-value="loginConfig.rememberPassword"
          @change="setRememberPassword as any"
        >
          {{ $t('login.form.rememberPassword') }}
        </a-checkbox>
        <a-link @click="changeLogin(LoginMethods.forget)">{{
          $t('login.form.forgetPassword')
        }}</a-link>
      </div>
      <a-button
        type="primary"
        html-type="submit"
        long
        :loading="loading"
        style="font-size: 16px"
      >
        {{ $t('login.form.login') }}
      </a-button>
    </a-space>
    <div class="register-link" @click="changeLogin(LoginMethods.register)">
      <span>
        {{ $t('login.register.link') }}
      </span>
    </div>
    <AccountSwitcherModal
      v-model:showModal="showModal"
      :should-load="true"
      @select="handleAccountSelected"
    />
  </a-form>
</template>

<script setup lang="ts">
  import { reactive, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import { getSms, loginDualVerification } from '@/api/user';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message, Modal } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import { useStorage } from '@vueuse/core';
  import { useUserStore, useGlobalModeStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import type { LoginData } from '@/api/user';
  import pwdEncrypt from '@/utils/encryption/pwd';
  import { dotToSlash } from '@/utils/index';
  import LoginMethods from '../constant';
  import {
    ModalConfig,
    ModalUpdateConfig,
  } from '@arco-design/web-vue/es/modal/interface';
  import checkPassWordFormat from '@/utils/password-validation';
  import { getWpsToken, getWpsAppCode } from '../api';
  import { setWpsToken } from '@/utils/auth';
  import AccountSwitcherModal from '@/components/AccountSwitcherModal/AccountSwitcherModal.vue';

  const showModal = ref(false);
  const openModal = () => {
    console.log(
      'openModal called111111111111111111111111111111111111111111111111',
      showModal.value
    );
    showModal.value = true;
  };
  const emit = defineEmits(['changeLogin', 'getWpsToken']);

  const changeLogin = (method: LoginMethods) => {
    emit('changeLogin', method);
  };

  const router = useRouter();

  // 控制提示成功信息
  const vitifyStatus = ref(false);
  const { t } = useI18n();
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();

  const loginConfig = useStorage('work-login-config', {
    // fawkes OWndZi1mKZdC
    rememberPassword: true,
    username: '',
    password: '',
  });
  const userInfo = reactive({
    username: loginConfig.value.username,
    password: loginConfig.value.password,
    phone: '',
    captcha_code: '',
  });

  const modalCount = ref<number>(-1);
  const updataModalCount = () => {
    modalCount.value = 3;
    const counter = setInterval(() => {
      if (modalCount.value === 0) {
        clearInterval(counter);
      } else {
        modalCount.value--;
      }
    }, 1000);
  };
  const modalInstance = ref<any>();
  // 密码强度格式校验
  const warningModal = () => {
    if (!checkPassWordFormat(userInfo.password)) {
      modalInstance.value = Modal.info({
        title: t('login.form.tips'),
        content: t('login.form.password-risk'),
        escToClose: false,
        maskClosable: false,
        bodyClass: 'arco-modal-body',
        bodyStyle: {
          fontSize: '16px',
        },
        onOpen: () => {
          updataModalCount(); // 开始倒计时
        },
        okText: t('login.form.cancle'),
        onOk: () => {},
      });
    }
  };

  watch(
    () => modalCount.value,
    (n) => {
      console.log('[ n ] >', n);
      if (n === 0) {
        modalInstance.value.close();
        changeLogin(LoginMethods.forget);
      } else {
        const modalBodies = document.getElementsByClassName('arco-modal-body');
        if (modalBodies.length > 0) {
          const modalBody = modalBodies[0] as HTMLElement; // 转换为 HTMLElement 以访问其 innerHTML 属性
          modalBody.innerHTML = `您的密码强度低，存在风险，${modalCount.value}s 后跳转至修改密码界面`; // 修改元素的内容
        }
      }
    }
  );

  // 验证账号密码
  const checked = ref(false);
  const checkPassword = async (values: Record<string, any>) => {
    try {
      const res: any = await loginDualVerification({
        username: values.username,
        password: pwdEncrypt(values.password),
      });
      if (res.status && res.data) {
        checked.value = true;
        userInfo.phone = res.data.phone;
        await getSMSCaptcha();
        if (vitifyStatus.value) {
          Message.success(res.message);
        }
      }
    } catch (error: { code: number }) {
      if (error.data?.code === -8020200) {
        warningModal();
      }
    }
  };

  // 设置wpstoken
  const setWpsTokenHandle = async () => {
    const tokenParam = {
      grant_type: 'client_credentials',
      client_id: 'fusion',
      client_secret: 'fusion_secret',
    };
    const tokenData: any = await getWpsToken(tokenParam);
    setWpsToken(tokenData.access_token);
    // 获取cde系统在wps应用中台对应的code
    const wpsAppCode: any = (await getWpsAppCode({})).data;
    localStorage.setItem('wpsAppCode', wpsAppCode);
  };

  // 存储验证码
  let captchaKey = '';
  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (loading.value) return;
    if (!errors) {
      setLoading(true);
      try {
        if (!checked.value) {
          checkPassword(values);
          return;
        }
        const { username, password } = values;
        const data = {
          username: userInfo.phone,
          captcha_key: captchaKey,
          captcha_code: values.captcha_code,
          grant_type: 'sms_captcha',
          scope: 'all',
        };
        const res: any = await userStore.login(data as LoginData);
        userStore.addToProject();
        setWpsTokenHandle();
        // todo:
        openModal();
        // router.push({
        //   path: dotToSlash(redirect as string) || '/dashboard',
        //   query: {
        //     ...othersQuery,
        //   },
        // });
        Message.success(res.message);
        const { rememberPassword } = loginConfig.value;
        loginConfig.value.username = rememberPassword ? username : '';
        loginConfig.value.password = rememberPassword ? password : '';
      } catch (err: any) {
        if (err?.response?.status === 400) {
          Message.error(err.response.data);
        } else if (typeof err === 'string') {
          Message.error(err);
        }
      } finally {
        setLoading(false);
      }
    }
  };
  const globalModeStore = useGlobalModeStore();

  // 添加处理账号选择的方法
  const handleAccountSelected = ({
    account,
    isSameScene,
  }: {
    account: any;
    isSameScene: boolean;
  }) => {
    console.log('Selected account:', account);
    // 路由跳转
    const { redirect, ...othersQuery } = router.currentRoute.value.query;
    if (!account.id) {
      globalModeStore.changeGlobalMode('work');
      // 个人版
      router
        .push({
          path: '/dashboard',
          // path: dotToSlash(redirect as string) || '/dashboard',
          query: {
            ...othersQuery,
          },
        })
        .then(() => {
          // 路由跳转成功后关闭弹窗
          showModal.value = false;
        })
        .catch((err) => {
          console.error('路由跳转失败', err);
          // 即使路由跳转失败也关闭弹窗
          showModal.value = false;
          Message.error(t('login.navigation.failed'));
        });
    } else {
      // 企业版
      const targetPath = isSameScene
        ? dotToSlash(redirect as string) || '/home-page'
        : '/home-page';

      router
        .push({
          path: targetPath,
          query: {
            ...othersQuery,
          },
        })
        .then(() => {
          // 路由跳转成功后关闭弹窗
          showModal.value = false;
        })
        .catch((err) => {
          console.error('路由跳转失败', err);
          // 即使路由跳转失败也关闭弹窗
          showModal.value = false;
          Message.error(t('login.navigation.failed'));
        });
    }
  };
  const setRememberPassword = (value: boolean) => {
    loginConfig.value.rememberPassword = value;
  };

  // 处理验证码
  const countDown = ref(-2);
  const smsLoading = ref(false);
  const phoneForm = ref<FormInstance>();

  const getSMSCaptcha = async () => {
    if (countDown.value >= 0) {
      Message.warning(t('login.form.captchaHoldOn'));
      return;
    }
    const res = await phoneForm.value?.validateField('phone');
    // 仅判断手机号是否校验通过
    if (res) {
      return;
    }
    smsLoading.value = true;
    try {
      const res = await getSms(userInfo.phone);
      captchaKey = res.data;
      if (res.code === 8000000) {
        vitifyStatus.value = true;
      } else {
        Message.warning(res.message);
      }
    } catch (err) {
      // you can report use errorHandler or other
      if (typeof err === 'string') {
        Message.error(err);
      }
    } finally {
      smsLoading.value = false;
      updataCountDown();
    }
  };

  const updataCountDown = () => {
    countDown.value = 90;
    const counter = setInterval(() => {
      if (countDown.value === 0) {
        clearInterval(counter);
        countDown.value = -1;
      } else {
        countDown.value--;
      }
    }, 1000);
  };
</script>

<script lang="ts">
  export default {
    name: 'AccountForm',
  };
</script>

<style lang="less" scoped>
  :deep(.arco-btn-size-large) {
    height: 48px;
    border-radius: 8px;
  }
  :deep(.arco-input-wrapper) {
    height: 40px;
    border: 1px solid #c9cdd4;
    background-color: #ffffff;
    border-radius: 8px !important;
  }
  .login-form {
    height: 340px;
    margin-top: 16px;
  }
  .register-link {
    text-align: center;
    margin-top: 16px;
    cursor: pointer;
    color: rgb(22, 93, 255);
  }
  .login-form-password-actions{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
