<template>
  <div class="dashboard-wrap">
    <div class="header">
      <commonTabs v-model="tabKey" :tabs="[]"></commonTabs>
    </div>

    <div class="main">
      <div class="agent-panel">
        <a-spin
          v-if="showNavigation"
          :loading="agentLoading"
          style="width: 320px"
        >
          <div class="agent-nav">
            <div v-if="!showSearchBox" class="nav-top">
              <div v-show="!showRecordList" class="title">{{
                t('dashboard.occupational-intelligence')
              }}</div>
              <a-space v-show="showRecordList">
                <arrowLeft class="btn agent-name" @click="backToAgentNav" />
                <span class="title agent-name">{{
                  selectedAgent.name || ''
                }}</span>
              </a-space>
            </div>
            <div
              class="list"
              :class="
                showRecordList ? 'record-list-container' : 'nav-list-container'
              "
            >
              <template v-if="!showRecordList">
                <div
                  v-for="item in agentList"
                  :key="item.agentId"
                  class="agent-card"
                  @click="changeAgent(item)"
                >
                  <img
                    :src="item.avatar"
                    alt=""
                    style="height: 38px; width: 38px"
                  />
                  <div class="agent-text">
                    <div class="title">{{ item.name }}</div>
                    <a-typography-paragraph
                      :ellipsis="{
                        rows: 1,
                        showTooltip: true,
                      }"
                      >{{ item.description }}
                    </a-typography-paragraph>
                  </div>
                  <div class="count-box">
                    <span>{{ item.chatCount || 0 }}</span>
                  </div>
                </div>
              </template>
              <template v-else>
                <a-button
                  type="outline"
                  long
                  size="small"
                  @click="createSession"
                >
                  <template #icon> <icon-plus /> </template>
                  {{ $t('dashboard.new-conversation') }}
                </a-button>
                <div class="historyText">
                  <history></history>
                  {{ $t('dashboard.history-conversation') }}
                </div>
                <a-input-search
                  v-model="searchVal"
                  :placeholder="$t('dashboard.search')"
                  allow-clear
                  @blur="searchHandle"
                  @press-enter="($event.target as any)?.blur()"
                  @search="searchHandle"
                />
                <div v-if="recordList.length === 0" class="empty-record">
                  <EmptyFolder></EmptyFolder>
                  <span>{{ $t('knowledgenew.empty-content') }}</span>
                </div>
                <div class="record-list">
                  <div
                    v-for="(item, index) in recordList"
                    :key="item.id"
                    :class="[
                      'record-card',
                      selectedRecord?.id === item.id
                        ? 'record-card-select'
                        : '',
                    ]"
                    @click="changeRecord(item)"
                    @mouseenter="handleMouseEnter(item)"
                    @mouseleave="handleMouseLeave(item)"
                  >
                    <div class="time"
                      >{{ item.updateDate }}
                      <a-space
                        v-if="
                          getRecordUIState(item.id).showOptions &&
                          !getRecordUIState(item.id).isEditing
                        "
                        :size="12"
                      >
                        <icon-edit
                          :size="16"
                          @click.stop="handleEditRecord(item, index)"
                        />
                        <a-popconfirm
                          :content="$t('dashboard.delete.confirm')"
                          @ok="handleDeleteRecord(item)"
                          @popup-visible-change="
                            popupVisibleChange($event, item)
                          "
                          @click.stop
                        >
                          <icon-delete :size="16" />
                        </a-popconfirm>
                      </a-space>
                      <a-space
                        v-if="getRecordUIState(item.id).isEditing"
                        :size="12"
                      >
                        <icon-check
                          :size="16"
                          @click.stop="handleRenameRecord(item)"
                        />
                        <icon-close
                          :size="16"
                          @click.stop="handleCancelRename(item)"
                        />
                      </a-space>
                    </div>
                    <div class="title">
                      <a-typography-paragraph
                        :ellipsis="{
                          rows: 1,
                          showTooltip: true,
                        }"
                      >
                        <span v-show="!getRecordUIState(item.id).isEditing">{{
                          item.chatSessionName
                        }}</span>
                      </a-typography-paragraph>

                      <div v-show="getRecordUIState(item.id).isEditing">
                        <a-input
                          ref="editInputRef"
                          v-model="item.chatSessionName"
                          :max-length="20"
                          show-word-limit
                          size="small"
                          @click.stop
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </a-spin>
        <div class="agent-content">
          <div class="top">
            <div class="model-name">
              {{ selectedAgent.name ? selectedAgent.name : '' }}
            </div>
            <!-- <a-dropdown>
              <a-space class="model-name model-select">
                {{ currentModel }}
                <icon-down :size="20" />
              </a-space>
              <template #content>
                <a-doption v-for="item in modelOptions" :key="item.name">{{
                  item.name
                }}</a-doption>
              </template>
            </a-dropdown> -->
            <!-- TODO 扩展功能 -->
            <!-- <a-dropdown>
              <icon-more :size="20" class="btn" />
              <template #content>
                <a-doption>复制链接</a-doption>
                <a-doption>生成图片</a-doption>
                <a-doption>生成Word</a-doption>
              </template>
            </a-dropdown> -->
          </div>
          <div ref="dialogContainer" class="dialog-container"> </div>
          <foldLeft
            v-if="showNavigation"
            class="fold-btn btn"
            @click="showNavigation = !showNavigation"
          />
          <foldRight
            v-else
            class="fold-btn btn"
            @click="showNavigation = !showNavigation"
          />
        </div>
      </div>
      <div class="schedule-file">
        <!-- 日程 -->
        <schedule />
        <!-- 最近上传的文件 -->
        <FileRecently />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, nextTick, onUnmounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    getAgentRecordList,
    getAgentList,
    renameAgentRecord,
    deleteAgentRecord,
  } from './api';
  import FileRecently from './component/file-rencently.vue';
  import schedule from './component/schedule.vue';
  import useAIChat from '@/hooks/aiChat';
  import { AgentInfo, QueryRecordParams } from './types';
  import { ChatHistoryRecord } from '@/views/knowledge-base/types';
  import useAIRecordUIState from '@/views/knowledge-base/composables/useAIRecordUIState';
  import useSessionStorageWatcher from '@/hooks/useSessionStorageWatcher';
  import foldLeft from '@/assets/images/dashboard/fold-left.svg';
  import foldRight from '@/assets/images/dashboard/fold-right.svg';
  import arrowLeft from '@/assets/images/dashboard/arrow-left.svg';
  import history from '@/assets/images/dashboard/history.svg';
  import commonTabs from '@/components/common-tabs/index.vue';
  import EmptyFolder from '@/assets/images/knowledge-base/empty-folder.svg';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const {
    genAIToken,
    initAIPage,
    setSelectedRecordSession,
    clearSelectedRecordSession,
  } = useAIChat();

  const showSearchBox = ref(false);

  const tabKey = ref('dashboard');

  const showNavigation = ref(true); // 是否展示左侧导航列表（智能体/记录）
  const showRecordList = ref(false); // 是否展示对话记录列表，记录列表和面包屑同时展示
  const agentList = ref<AgentInfo[]>([]); // 智能体列表
  const agentLoading = ref(false); // 智能体列表loading

  const emptyAgent = {
    agentId: '',
    avatar: '',
    avatarWelcome: '',
    chatCount: 0,
    description: '',
    name: '',
    sort: 0,
  };

  // 选中的智能体，为空则展示智能体列表的第一个
  const selectedAgent = ref<AgentInfo>(emptyAgent);

  // AI对话页面
  const dialogContainer = ref<HTMLElement | null>(null);

  const showAIPage = async () => {
    await genAIToken();
    nextTick(() => {
      const res = initAIPage(
        selectedAgent.value?.agentId || '',
        'agentSession',
        dialogContainer.value,
        null,
        '请输入您的问题'
      );
      if (!res.status) {
        console.error(res.message);
      }
    });
  };

  // 查询智能体列表
  const queryAgentList = () => {
    agentLoading.value = true;
    const params = {
      pageNo: 1,
      pageSize: 100,
    };
    getAgentList(params)
      .then((res) => {
        if (res.status) {
          agentList.value = res.data || [];
          if (res.data.length) {
            // 1. 刚打开页面，左侧智能体列表标题不展示面包屑，右侧展示第一个智能体空对话
            if (!selectedAgent.value?.agentId) {
              selectedAgent.value = res.data[0] || emptyAgent;
              showAIPage();
            }
          }
        }
      })
      .finally(() => {
        agentLoading.value = false;
      });
  };
  queryAgentList();

  // 对话记录UI状态
  const { getRecordUIState, clearRecordUIState } = useAIRecordUIState();

  const emptyRecord = {
    agentId: '',
    chatSessionId: '',
    chatSessionName: '',
    chatSessionStatus: 0,
    chatSessionType: 0,
    createBy: '',
    createDate: '',
    id: '',
    updateBy: '',
    updateDate: '',
    userId: '',
    deleteFlag: 0,
  };

  // 对话记录列表
  const recordList = ref<Array<ChatHistoryRecord>>([]);
  const searchVal = ref('');

  // 选中的对话，为空则展示相应智能体的新对话
  const selectedRecord = ref<ChatHistoryRecord>(emptyRecord);

  // 查询历史记录
  const queryAgentRecord = (id: string) => {
    agentLoading.value = true;
    const params: QueryRecordParams = {
      agentId: id,
      pageNo: 1,
      pageSize: 100,
      keyword: searchVal.value,
    };
    getAgentRecordList(params)
      .then((res) => {
        if (res.status) {
          recordList.value = res?.data?.list || [];
          clearRecordUIState();
        }
      })
      .finally(() => {
        agentLoading.value = false;
      });
  };

  // 搜索历史记录
  const searchHandle = () => {
    queryAgentRecord(selectedAgent.value.agentId);
  };

  // 切换智能体
  const changeAgent = (item: any) => {
    showRecordList.value = true;
    queryAgentRecord(item.agentId);
    if (
      (selectedAgent.value.agentId &&
        selectedAgent.value.agentId !== item.agentId) ||
      (!selectedAgent.value.agentId &&
        item.agentId !== agentList.value[0].agentId)
    ) {
      selectedAgent.value = item;
      showAIPage();
      selectedRecord.value = emptyRecord;
      clearSelectedRecordSession();
    } else {
      selectedAgent.value = item;
    }
  };

  // 返回智能体列表，右侧对话保持不变
  const backToAgentNav = () => {
    queryAgentList();
    showRecordList.value = false;
  };

  // 切换对话
  const changeRecord = (item: any) => {
    if (
      (selectedRecord.value.chatSessionId &&
        selectedRecord.value.chatSessionId !== item.chatSessionId) ||
      !selectedRecord.value.chatSessionId
    ) {
      // 和当前展示不一致，切换对话
      setSelectedRecordSession(
        'agentSession',
        selectedAgent.value?.agentId,
        item.chatSessionId,
        '请输入您的问题'
      );
    }
    selectedRecord.value = item;
  };

  // 新增对话
  const createSession = () => {
    // 去掉判断，直接生成链接，解决在空对话提问后，点击新增对话，无法切换到新对话的问题
    // if (selectedRecord.value.agentId) {
    showAIPage();
    selectedRecord.value = emptyRecord;
    clearSelectedRecordSession();
    // }
  };

  // 鼠标悬停事件处理
  const handleMouseEnter = (item: ChatHistoryRecord) => {
    const state = getRecordUIState(item.id);
    if (!state.showOptions) {
      state.showOptions = true;
    }
  };

  const handleMouseLeave = (item: ChatHistoryRecord) => {
    const state = getRecordUIState(item.id);
    if (!state.isEditing && !state.isDeleting) {
      state.showOptions = false;
    }
  };

  // 编辑对话名称input Ref
  const editInputRef = ref<Array<HTMLInputElement | null>>([]);

  // 修改编辑对话的处理函数
  const handleEditRecord = (item: ChatHistoryRecord, index: number) => {
    const state = getRecordUIState(item.id);
    state.oldName = item.chatSessionName;
    state.isEditing = true;

    nextTick(() => {
      (editInputRef.value[index] as HTMLInputElement)?.focus();
    });
  };

  // 重命名
  const handleRenameRecord = async (item: ChatHistoryRecord) => {
    const state = getRecordUIState(item.id);
    state.isEditing = false;
    const res = await renameAgentRecord({
      chatSessionId: item.chatSessionId,
      chatSessionName: item.chatSessionName,
    });
    if (res.status) {
      Message.success(t('dashboard.rename.success'));
      queryAgentRecord(selectedAgent.value.agentId); // 查询历史对话
    }
  };

  // 取消重命名
  const handleCancelRename = (item: ChatHistoryRecord) => {
    const state = getRecordUIState(item.id);
    state.isEditing = false;
    item.chatSessionName = state.oldName;
  };

  // 删除确认弹窗处理
  const popupVisibleChange = (visible: boolean, record: ChatHistoryRecord) => {
    const state = getRecordUIState(record.id);
    state.isDeleting = visible;
    if (!visible && !state.isEditing) {
      state.showOptions = false;
    }
  };

  // 删除对话
  const handleDeleteRecord = async (item: ChatHistoryRecord) => {
    const res = await deleteAgentRecord({
      chatSessionId: item.chatSessionId,
      chatSessionName: item.chatSessionName,
    });
    if (res.status) {
      Message.success(t('dashboard.delete.success'));
      // 如果删除的是当前对话，则切换到空白对话
      if (selectedRecord.value?.chatSessionId === item.chatSessionId) {
        selectedRecord.value = emptyRecord;
        clearSelectedRecordSession();
        showAIPage();
      }

      queryAgentRecord(selectedAgent.value.agentId); // 查询历史对话
      // console.log('recordList: ', recordList.value);
    }
  };

  // 监听新对话问答结束，刷新历史记录
  const { startWatching, stopWatching } = useSessionStorageWatcher(
    'xkAiIsSessionEnd',
    (newValue: string, oldValue: string) => {
      console.log(newValue, ' ', oldValue);
      if (newValue === 'true') {
        queryAgentRecord(selectedAgent.value.agentId); // 查询历史对话
      }
    }
  );

  // 进入和退出页面都需要清空sessionStorage的当前选中记录信息
  onMounted(() => {
    clearSelectedRecordSession();
    sessionStorage.removeItem('xkAiIsSessionEnd');
    startWatching();
  });

  onUnmounted(() => {
    clearSelectedRecordSession();
    stopWatching();
  });
</script>

<style scoped lang="less">
  .dashboard-wrap {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 16px 0 0 16px;
    padding: 20px;
    border: none;
    //display: flex;
    //flex-direction: column;
    height: 100%;
    width: 100%;
    min-width: 1470px;
    overflow: hidden;
    //border: 1px solid red;
    .header {
      margin: 0 0 24px 0px;
      height: 24px;
      display: flex;
      justify-content: space-between;

      span {
        font-weight: bold;
        font-size: 24px;
        color: #1d2129;
        line-height: 24px;
      }
    }

    .main {
      flex: 1;
      display: flex;
      //overflow: hidden;
      width: 100%;
      min-width: 1430px;
      height: calc(100% - 88px);
      // height: calc(100% - 48px);
      //border: 1px solid red;
    }
  }

  .btn {
    color: #4e5969;
    cursor: pointer;
  }

  :deep(.has-pointer) {
    cursor: pointer;
    font-family: Source Han Sans SC, Source Han Sans SC;
    font-weight: 500;
    font-size: 20px;
    color: #1d2129;
  }

  // :deep(.nav-breadcrumb-item:first-of-type) {
  //   cursor: pointer;
  //   color: var(--color-neutral-6);
  // }

  // :deep(.nav-breadcrumb-item:last-of-type) {
  //   font-size: 20px;
  // }

  .agent-panel {
    flex: 1;
    display: flex;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
  }

  // 日程-文件
  .schedule-file {
    width: 390px;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
  }

  .agent-nav {
    position: relative;
    padding: 0 20px 20px;
    width: 320px;
    height: 100%;
    border-right: 1px solid var(--color-neutral-3);
  }

  .nav-top {
    display: flex;
    div {
      flex: 1;
    }

    .title {
      margin: 20px 0 10px;
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
      line-height: 20px;
    }

    .agent-name {
      margin: 20px 0;
    }

    .search-icon {
      margin-top: 20px;
    }

    // .agent-breadcrumb {
    //   height: 20px;
    //   margin: 20px 0;
    // }
  }

  .record-search-box {
    display: flex;
    align-items: center;
    margin: 16px 0 12px;
  }

  .agent-search-box {
    margin: 16px 0 2px;
    display: flex;
    align-items: center;
  }

  .agent-content {
    position: relative;
    flex: 1;
    width: 0;
    min-width: 700px;
    padding: 20px 20px 1px;
    box-sizing: border-box; /* 确保内边距在宽度计算内 */

    .top {
      margin-bottom: 22px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .model-name {
      font-size: 16px;
      font-weight: 500;
      color: #4e5969;
    }

    .model-select {
      cursor: pointer;
    }

    .dialog-container {
      height: calc(100% - 42px);
    }

    .fold-btn {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }
  }

  .nav-list-container {
    position: relative;
    height: calc(100% - 50px);
    overflow: auto;
  }
  .record-list-container {
    height: calc(100% - 60px);
    :deep(.arco-input-search) {
      height: 28px;
    }
    :deep(.arco-input-wrapper) {
      background-color: #fff;
      border: 1px solid #c9cdd4;
      border-radius: 8px !important;
    }
  }
  .list {
    .agent-card {
      padding: 12px 0;
      min-height: 72px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #ededed;
      cursor: pointer;

      svg {
        margin-top: -1px;
      }

      .agent-text {
        flex: 1;
        margin-left: 9px;
        overflow: hidden;

        .title {
          margin-bottom: 4px;
          font-weight: 500;
          font-size: 16px;
          color: #1d2129;
          line-height: 24px;
        }
      }

      .count-box {
        padding: 0 6px;
        height: 18px;
        background: #fff3e8;
        border-radius: 18px;
        display: flex;
        align-items: center;
        span {
          font-size: 12px;
          font-weight: 500;
          color: #f99057;
          line-height: 12px;
        }
      }
    }

    .record-card {
      margin-top: 4px;
      padding: 8px;
      height: 60px;
      cursor: pointer;

      .time {
        font-size: 14px;
        color: #86909c;
        line-height: 22px;
        display: flex;
        justify-content: space-between;
      }

      .title {
        font-size: 16px;
        color: #1d2129;
        line-height: 22px;
      }
    }

    .record-card:first-of-type {
      margin-top: 0;
    }

    .record-card-select {
      border-radius: 8px;
      background-color: #e8f2ff;
    }
  }

  .record-list {
    height: calc(100% - 104px);
    overflow: auto;
    margin-top: 4px;
  }

  .empty-record {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 129px 0 0;

    span {
      margin-top: 16px;
      font-size: 16px;
      color: #4e5969;
      line-height: 24px;
    }
  }

  // 面包屑样式覆盖
  :deep(.arco-breadcrumb) {
    font-size: 16px;
    .arco-breadcrumb-item,
    .arco-breadcrumb-item-separator {
      line-height: 20px;
    }
  }

  :deep(.arco-btn-outline) {
    border-color: #a0c1ff;
    border-radius: 8px;
  }

  :deep(.arco-typography) {
    margin-bottom: 0;
    color: #4e5969;
    line-height: 21px;
  }

  :deep(.record-card-select) {
    .arco-typography {
      color: #1d2129;
    }
  }
  .historyText {
    margin: 14px 0 16px 0;
    color: #1d2129;
    display: flex;
    align-items: center;
    .icon {
      margin-right: 10px;
    }
  }

  // AI样式覆盖
  .dialog-container {
    :deep(.chat-input-content) {
      .content-edit-box {
        border: none !important;
      }
    }
    :deep(.tiptap) {
      outline: none;
    }
    :deep(.foot-box-action .default-css) {
      border-radius: 26px !important;
    }
    :deep(.arco-list-bordered) {
      border: none;
    }
    :deep(.main-begin .main-view-main) {
      height: 0;
    }
    :deep(.reanoning-content li) {
      margin-left: 16px;
    }
  }

  // .dialog-container {
  //   :deep(.header-box) {
  //     display: none;
  //   }
  //   :deep(.begin-session-box) {
  //     top: 0;
  //     margin: 10% auto 0;
  //     transform: none;
  //   }
  //   :deep(.agent-icon) {
  //     img {
  //       width: 38px !important;
  //       height: 38px !important;
  //     }
  //   }
  //   :deep(.agent-desc) {
  //     font-size: 14px;
  //     line-height: 16px;
  //     white-space: normal;
  //   }
  // }
</style>
