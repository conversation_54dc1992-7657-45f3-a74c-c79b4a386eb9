<template>
  <a-modal
    :visible="props.visible"
    :title="props.title"
    :width="500"
    :unmount-on-close="true"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-space style="justify-content: center" fill>
      <a-form
        ref="requestRef"
        style="padding-top: 20px"
        :model="formData"
        auto-label-width
        :disabled="isView"
      >
        <a-form-item
          field="name"
          label="接口名称"
          validate-trigger="input"
          :rules="[
            {
              required: true,
              message: '接口名称未填写',
            },
          ]"
        >
          <a-input
            v-model="formData.name"
            :placeholder="$t('user-center.please-enter-name')"
          />
        </a-form-item>
        <a-form-item
          field="url"
          label="接口路径"
          validate-trigger="input"
          :rules="[
            {
              required: true,
              message: '接口路径未填写',
            },
          ]"
        >
          <a-input v-model="formData.url" placeholder="请输入接口路径" />
        </a-form-item>
        <a-form-item
          field="httpMethod"
          label="方法类型"
          validate-trigger="input"
        >
          <a-select v-model="formData.httpMethod" placeholder="请选择方法类型">
            <a-option>GET</a-option>
            <a-option>POST</a-option>
            <a-option>PUT</a-option>
            <a-option>DELETE</a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          field="module"
          label="所属模块"
          validate-trigger="input"
          :rules="[
            {
              required: true,
              message: '所属模块未填写',
            },
          ]"
        >
          <a-input v-model="formData.module" placeholder="请输入所属模块" />
        </a-form-item>
      </a-form>
    </a-space>
  </a-modal>
</template>

<script lang="ts" setup name="addRole">
  import { ref, computed, toRefs, PropType } from 'vue';
  import { FormInstance, Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import { addOrUpdateRequest } from '../api';

  const { t } = useI18n();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    select: {
      type: Object as PropType<Permission.Api.PageRequestDto>,
      default() {
        return {};
      },
    },
    type: {
      type: String,
      default: '',
    },
  });

  const formData = ref<Permission.Api.RequestDto>(
    {} as Permission.Api.RequestDto
  );
  const { type } = toRefs(props);

  const isView = computed(() => type.value === 'view');

  const emits = defineEmits(['update:visible', 'refresh']);
  // 绑定form的ref
  const requestRef = ref<FormInstance>();

  const handleBeforeOk = async () => {
    const validateRes = await requestRef.value?.validate();
    if (!validateRes) {
      const data = { ...formData.value };

      if (props.type === 'add') {
        await addOrUpdateRequest(data);
        Message.success('接口新增成功！');
      } else if (props.type === 'edit') {
        await addOrUpdateRequest(data);
        Message.success('接口修改成功！');
      }
      emits('refresh');
      emits('update:visible', false);
    }
  };

  function initialFormData() {
    if (type.value === 'add') {
      formData.value = {
        module: '',
        name: '',
        description: '',
        url: '',
        httpMethod: '',
      };
    } else if (type.value === 'edit') {
      formData.value = { ...props.select };
    }
  }
  initialFormData();

  const handleCancel = () => {
    emits('update:visible', false);
  };
</script>
<style lang="less" scoped>
  .detailTitle {
    display: flex;
    padding-bottom: 16px;
    font-size: 16px;
    align-items: center;
    color: #1d2129;
    .titleContent {
      margin-left: 6px;
    }
  }
  .tab-pane {
    padding: 0 10px;
  }
  :deep(.arco-tabs-content) {
    padding-top: 20px;
  }
</style>
