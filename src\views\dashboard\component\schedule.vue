<template>
  <div class="schedule-body">
    <h3 class="schedule-title">{{ $t('dashboard.schedule') }}</h3>
    <a-row class="schedule-head">
      <a-col :span="12"> {{ currentDate }} </a-col>
      <a-col :span="12" align="right">
        <a-tooltip :content="$t('dashboard.create.item')">
          <a-button type="text" @click="goCreate('matters')">
            <matter class="schedule-icon"></matter
          ></a-button>
        </a-tooltip>
        <a-tooltip :content="$t('dashboard.create.meeting')">
          <a-button type="text" @click="goCreate('meeting')">
            <meeting class="schedule-icon"></meeting
          ></a-button>
        </a-tooltip>
      </a-col>
    </a-row>
    <div class="schedule-con">
      <div
        v-for="item in scheduleList"
        :key="item.id"
        class="schedule-list"
        @click="goSchedule(item)"
      >
        <img
          v-if="item.type === 'meeting'"
          src="@/assets/images/dashboard/meetingIcon.png"
          alt=""
        />
        <img v-else src="@/assets/images/dashboard/matterIcon.png" alt="" />
        <div>
          <div class="schedule-time">
            <span v-if="item.planStartTime">{{
              dayjs(item.planStartTime).format('MM-DD HH:mm')
            }}</span>
            {{ $t('dashboard.to') }}
            <span v-if="item.planEndTime"
              >{{ dayjs(item.planEndTime).format('MM-DD HH:mm') }}
            </span>
          </div>
          <div class="schedule-title">{{ item.title }} </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { getPersonalSchedule } from '@/views/schedule/component/calendar/api';
  import dayjs from 'dayjs';
  import { useRouter } from 'vue-router';
  import matter from '@/assets/images/dashboard/matter.svg';
  import meeting from '@/assets/images/dashboard/meeting.svg';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const currentDate = dayjs().format('YYYY-MM-DD');
  const router = useRouter();

  // 获取个人日程数据
  const scheduleList: any = ref([]);
  const getPersonalScheduleHandle = async () => {
    const param = {
      endTime: currentDate,
      pageNo: 1,
      pageSize: 99999,
      startTime: currentDate,
    };
    const { data } = await getPersonalSchedule(param);
    scheduleList.value = data.list;
  };
  getPersonalScheduleHandle();

  // 跳转新建会议事项
  const goCreate = (val: any) => {
    router.push({
      name: 'schedule',
      query: {
        editType: 'create',
        type: val,
      },
    });
  };

  const goSchedule = (val: any) => {
    router.push({
      name: 'schedule',
      query: {
        editType: 'edit',
        type: val.type === 'meeting' ? 'meeting' : 'matters',
        id: val.type === 'meeting' ? val.scheduleSubId : val.id,
      },
    });
  };
</script>

<script lang="ts">
  export default {
    name: 'Schedule',
  };
</script>

<style lang="less" scoped>
  .schedule-body {
    height: 56%;
    padding: 16px 20px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    margin-bottom: 20px;
    :deep(.arco-btn) {
      padding: 0 5px;
    }
    .schedule-icon {
      cursor: pointer;
    }
    .schedule-title {
      font-size: 20px;
      color: #1d2129;
      margin: 0;
      font-weight: 400;
    }
    .title {
      font-weight: 500;
    }
    .schedule-head {
      font-size: 18px;
      margin: 10px 0;
      color: #555555;
      align-items: center !important;
      img {
        width: 20px;
        height: 20px;
        margin-left: 16px;
        cursor: pointer;
      }
    }
    .schedule-con {
      height: 408px;
      overflow: auto;
      height: calc(100% - 66px);
      overflow-y: auto;
    }
    .schedule-list {
      height: 60px;
      padding-top: 12px;
      margin-bottom: 12px;
      padding-left: 16px;
      background-image: url('@/assets/images/dashboard/scheduleList.png');
      background-size: 100% 60px;
      img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        vertical-align: top;
      }
      > div {
        display: inline-block;
      }
      .schedule-time {
        font-size: 14px;
        color: #4e5969;
        margin-bottom: 6px;
      }
      .schedule-title {
        font-size: 16px;
        color: #1d2129;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 280px;
      }
    }
  }
</style>
