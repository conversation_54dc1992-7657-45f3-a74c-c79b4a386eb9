<template>
  <a-modal
    ref="modal"
    :key="moveModalKey"
    v-model:visible="dialogShow"
    unmount-on-close
    :mask-closable="false"
    :esc-to-close="false"
    :on-before-ok="
      () =>
        okFunction(
          config.type === 0
            ? getFolderKey
            : config.type === 3
            ? getModelFile
            : getDataByCheckedKeys
        )
    "
    :mask="!config.calculate"
    :style="'opacity: ' + (config.calculate ? '0' : '1')"
    draggable
    @cancel="() => (dialogShow = false)"
  >
    <template #title>
      {{ config.type === 0 ? '选择目标文件夹' : '添加文件' }}
    </template>
    <a-spin style="width: 100%" :loading="loading">
      <div>
        <a-tree
          ref="tree"
          v-model:checked-keys="checkedKeys"
          v-model:expanded-keys="expendedKeys"
          :checkable="true"
          :check-strictly="config.type === 0"
          :data="treeData"
          :loading="loading"
          @check="[0, 3].includes(config.type) ? checkNode($event) : null"
        >
          <template #icon="{ node }">
            <file-image
              :file-name="node.name"
              :is-file="node.isFileOrFolder === 0 ? false : true"
              style="margin-right: 8px"
            />
          </template>
        </a-tree>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, defineProps, computed, onMounted, watch } from 'vue';
  import {
    getChildFolderList,
    FileAndFolderNodeMessage,
    FolderMessage,
    getChildrenAll,
  } from '@/api/tree-folder';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import { cloneDeep } from 'lodash';
  import { useRoute } from 'vue-router';
  import useLoading from '@/hooks/loading';
  import { modelall } from '@/utils/BIM-Engine/XBase/format';
  import treeDefault from './json/tree-default.json';
  import FileImage from '@/views/projectSpace/home/<USER>/image-file.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      required: false,
    },
    okFunction: {
      type: Function as () => any,
      required: true,
    },
    config: {
      type: Object,
      default: () => {
        return {
          type: 0, // 0：文件夹模式 1：文件夹+文件模式 2：模型模式 3：模型单选模式
          module: ['wip', 'shared'],
          calculate: false,
          teamIds: [],
        };
      },
    },
    defaultData: {
      type: Array,
      default: () => [],
    },
  });

  const { loading, setLoading } = useLoading(false);
  const projectStore = usePrjPermissionStore();
  const route = useRoute();
  const moveModalKey = ref(0);
  const checkedKeys = ref<(string | number)[]>([]);
  const expendedKeys = ref<(string | number)[]>(props.config.module);
  const emit = defineEmits(['update:visible']);
  const tree = ref();
  const { projectId }: any = route.params;
  const treeData = ref<FileAndFolderNodeMessage[]>(cloneDeep(treeDefault));
  // treeData初始化
  treeData.value.forEach((item) => {
    item.disableCheckbox = [0, 3].includes(props.config.type);
  });
  treeData.value = treeData.value.filter((item) =>
    props.config.module.includes(item.key)
  );
  // 弹窗开关
  const dialogShow = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val),
  });
  let oldCheckKeys: (string | number)[] = [];

  /**
   * 点击checkbox触发事件
   * @param checkeds
   */
  const checkNode = (checkeds: (string | number)[]) => {
    if (checkeds.length > 1) {
      const key = checkeds[1];
      checkedKeys.value = [key];
    }
  };

  /**
   * 格式化getChildrenAll方法获得的数据
   * @param fileAndFolderList [data]
   */
  const formatFileAndFolder = (fileAndFolderList: any[]) => {
    console.log(
      '%c [ fileAndFolderList ]-131',
      'font-size:13px; background:pink; color:#bf2c9f;',
      fileAndFolderList
    );
    const result = fileAndFolderList.filter((item) => !item.sysType);
    fileAndFolderList.forEach((item) => {
      if (item.sunFolders?.length > 0) {
        item.sunFolders = formatFileAndFolder(item.sunFolders);
      }
      if (props.config.type === 0) {
        item.children = item.sunFolders || [];
      } else {
        item.children = [...(item.sunFolders || []), ...(item.files || [])];
      }
      item.children.forEach((itm: any) => {
        if ('fileId' in itm) {
          itm.isFileOrFolder = 1;
          itm.id = itm.fileId;
          itm.key = `file-${itm.id}`;
        } else {
          itm.isFileOrFolder = 0;
          itm.key = `folder-${itm.id}`;
          if (props.config.type === 3) {
            itm.disableCheckbox = true;
          }
        }
        itm.title = itm.name;
      });
      if ([2, 3].includes(props.config.type)) {
        item.children = item.children.filter((itm: any) => {
          if (itm.isFileOrFolder === 0) return true;
          const suffix = itm.name.split('.').pop();
          return modelall.includes(suffix);
        });
      }
    });
    return result;
  };

  /**
   * 获取team文件夹下的所有文件夹和文件
   * @param teamFolder team文件夹信息
   */
  const getTeamAllFileAndFolder = (
    teamFolder: FileAndFolderNodeMessage
  ): Promise<null> => {
    return new Promise((resolve) => {
      getChildrenAll(teamFolder.id || '').then((res) => {
        const treeDataList = formatFileAndFolder([res.data]);
        teamFolder.children = treeDataList[0]?.children;
        resolve(null);
      });
    });
  };

  /**
   * 获取wip/shared目录下的team文件夹
   * @param folder wip/shared文件夹信息
   */
  const getAllTeamFolder = (folder: any): Promise<null> => {
    return new Promise((resolve) => {
      const teamList = props.config.teamIds
        ? props.config.teamIds
        : projectStore.teamList.map((item) => item.id);
      getChildFolderList(projectId, undefined, folder.type, '0').then((res) => {
        let { list }: any = res.data;
        if (teamList.length > 0) {
          list = list.filter(
            (item: FolderMessage) =>
              teamList.includes(item.teamId) || item.name === 'Coordination'
          );
        }
        const teamsPromiseList: Promise<null>[] = [];
        list.forEach((item: FileAndFolderNodeMessage) => {
          console.log(
            '%c [ item ]-201',
            'font-size:13px; background:pink; color:#bf2c9f;',
            item
          );
          item.isFileOrFolder = 0;
          item.title = item.name;
          item.key = `folder-${item.id}`;
          if (props.config.type === 3) {
            item.disableCheckbox = true;
          }
          teamsPromiseList.push(getTeamAllFileAndFolder(item));
        });
        Promise.all(teamsPromiseList).then(() => {
          folder.children = list;
          resolve(null);
        });
      });
    });
  };

  /**
   * 过滤选中的文件和文件夹
   * @param nodeList
   * @param checkedNodeKeys
   * @param halfCheckedNodeKeys
   */
  const nodeFilter = (
    nodeList: any[],
    checkedNodeKeys: (string | number | undefined)[],
    halfCheckedNodeKeys: (string | number | undefined)[]
  ) => {
    const result: any[] = [];
    nodeList.forEach((item) => {
      if (checkedNodeKeys.includes(item.key)) {
        result.push(item);
      } else if (halfCheckedNodeKeys.includes(item.key)) {
        item.children = nodeFilter(
          item.children,
          checkedNodeKeys,
          halfCheckedNodeKeys
        );
        result.push(item);
      }
    });
    return result;
  };

  /**
   * 过滤空文件夹
   * @param treeList
   */
  const emptyFolderFilter = (treeList: FileAndFolderNodeMessage[]) => {
    const result: FileAndFolderNodeMessage[] = [];
    treeList.forEach((item) => {
      if (item.isFileOrFolder === 1) {
        result.push(item);
      } else {
        const childrenResult = emptyFolderFilter(item.children || []);
        if (childrenResult.length !== 0) {
          item.children = childrenResult;
          result.push(item);
        }
      }
    });
    return result;
  };

  /**
   * 文件和文件夹模式，返回选中文件的整个结构
   */
  const getDataByCheckedKeys = async () => {
    const checkedNodes: FileAndFolderNodeMessage[] =
      tree.value.getCheckedNodes();
    const halfCheckedNodes: FileAndFolderNodeMessage[] =
      tree.value.getHalfCheckedNodes();
    const checkedNodeKeys = checkedNodes.map((item) => item.key);
    const halfCheckedNodeKeys = halfCheckedNodes.map((item) => item.key);
    const treeDataClone = cloneDeep(treeData.value);
    const deepResult = nodeFilter(
      treeDataClone,
      checkedNodeKeys,
      halfCheckedNodeKeys
    );
    const result = emptyFolderFilter(deepResult);
    oldCheckKeys = cloneDeep(checkedKeys.value);
    return result;
  };

  /**
   * 文件夹模式，返回选中节点的id
   */
  const getFolderKey = () => {
    let result = '';
    if (checkedKeys.value.length) {
      const val = checkedKeys.value[0] as string;
      [, result] = val.split('-');
    }
    return result;
  };

  /**
   * 递归查找树中的节点
   */
  const getFileInTree: any = (
    key: string,
    treeDataList: FileAndFolderNodeMessage[]
  ) => {
    let result = null;
    for (let i = 0; i < treeDataList.length; i++) {
      if (treeDataList[i].key === key) {
        result = treeDataList[i];
        break;
      } else if (treeDataList[i].children) {
        result = getFileInTree(
          key,
          treeDataList[i].children as FileAndFolderNodeMessage[]
        );
        if (result) break;
      }
    }
    return result;
  };

  /**
   * 模型单选模式，返回选中节点的具体信息
   */
  const getModelFile = () => {
    let result = null;
    if (checkedKeys.value.length) {
      const val = checkedKeys.value[0] as string;
      result = getFileInTree(val, treeData.value);
    }
    return result;
  };

  const init = async () => {
    const promiseList: Promise<null>[] = [];
    treeData.value.forEach((item) => {
      promiseList.push(getAllTeamFolder(item));
    });
    await Promise.all(promiseList);
  };

  const modal = ref();

  onMounted(async () => {
    if (props.config.calculate) {
      await init();
      await modal.value.onBeforeOk();
    }
  });

  watch(
    () => props.visible,
    async (newValue) => {
      if (!newValue || props.config.calculate) return;
      if (props.config.type === 0) {
        // 文件夹模式，清空之前的选择
        checkedKeys.value = [];
      } else if (props.config.type === 1) {
        // 文件和文件夹模式，显示上次确认的选择
        checkedKeys.value = oldCheckKeys;
      }
      setLoading(true);
      await init();
      setLoading(false);
    }
  );

  watch(
    () => props.defaultData,
    (newValue) => {
      // 文件和文件夹模式下需要回显
      if (
        props.config.type === 1 ||
        props.config.type === 2 ||
        props.config.type === 3
      ) {
        const keys: (string | number)[] = [];
        newValue.forEach((item: any) => {
          const idList = item.split('-');
          keys.push(`file-${idList.pop()}`);
        });
        oldCheckKeys = keys;
        checkedKeys.value = keys;
      }
    },
    { immediate: true }
  );
</script>
