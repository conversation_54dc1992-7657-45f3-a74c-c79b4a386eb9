<template>
  <div class="user-transfer-container">
    <a-modal
      :visible="visible"
      :title="title"
      :ok-text="$t('user.transfer.confirm')"
      :cancel-text="$t('user.transfer.cancel')"
      @ok="handleOk"
      @cancel="handleCancel"
      :width="900"
    >
      <table-title
        :title="$t('user.transfer.add.user')"
        class="table-title"
        style="margin-bottom: 16px"
      ></table-title>

      <div class="transfer-container">
        <a-spin
          :loading="loading"
          :tip="$t('user.transfer.loading')"
          style="width: 100%"
        >
          <a-transfer
            :key="transferKey"
            show-search
            :data="sourceList"
            :default-value="selecteList"
            :model-value="selecteList"
            :title="transferTitle"
            :source-input-search-props="{
              placeholder: $t('user.transfer.search.placeholder'),
              allowClear: true,
              searchButton: true,
              onSearch: handleSourceSearch
            }"
            :target-input-search-props="{
              placeholder: $t('user.transfer.search.placeholder'),
              allowClear: true,
              searchButton: true,
              onSearch: handleTargetSearch
            }"
            @change="handleTransferChange"
            @search="handleTransferSearch"
          >
          </a-transfer>
        </a-spin>
      </div>
      <!-- <div>{{ selecteList }}</div>
      <div>{{ sourceList }}</div> -->
      <!-- <div>{{ tableData }}</div> -->
      <table-title
        :title="$t('user.transfer.userList')"
        class="table-title"
        style="margin: 16px 0"
      ></table-title>
      <a-spin
        :loading="tableLoading"
        :tip="$t('user.transfer.loading')"
        style="width: 100%"
      >
        <a-table
          :columns="columns"
          :data="tableData"
          :scroll="{ y: 300 }"
          :pagination="false"
          :bordered="false"
          table-layout-fixed
        >
          <template #operation="{ record }">
            <a-popconfirm
              :content="$t('user.transfer.remove.confirm')"
              type="warning"
              @ok="handleRemoveUser(record)"
            >
              <a-button
                type="text"
                style="color: rgb(var(--red-6))"
                size="small"
              >
                {{ $t('user.transfer.remove') }}
              </a-button>
            </a-popconfirm>
          </template>
        </a-table>
      </a-spin>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch, nextTick, reactive, computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import {
    getSysUserList,
    searchSysUserParams,
    getOrgUser,
    orgAddMember,
    orgRemoveMember,
    SysUserMemberRecord,
    orgUserListType,
  } from '@/views/user-center/api';
  import TableTitle from '@/components/table-title/index.vue';

  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    orgNo: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

  interface TransferItem {
    value: string;
    label: string;
  }

  const sourceList = ref<TransferItem[]>([]); // { value, label }
  const selecteList = ref<string[]>([]); // { value, label }
  const selectedSourceList = ref<TransferItem[]>([]); // 保存已选择的数据
  const total = ref(0);
  const transferPagination = reactive({
    current: 1,
    pageSize: 20,
  });
  const searchValue = ref(''); // 搜索值
  const loading = ref(false); // 左侧 loading
  const tableLoading = ref(false); // 右侧 loading
  const tableData = ref<SysUserMemberRecord[]>([]);
  const transferTitle = computed(() => [
    t('user.transfer.all.user.list'),
    t('user.transfer.add.user.list')
  ]);
  const transferKey = ref(0);

  // 处理手机号码
  function formatPhoneNumber(phone: string) {
    if (!phone) return '';

    const { length } = phone;
    if (length === 11) {
      // 11位手机号：保留前3位和后4位，中间4位用*代替
      return `${phone.slice(0, 3)}****${phone.slice(-4)}`;
    }

    if (length > 11) {
      // 大于11位：保留前5位和后4位，中间用*代替
      const stars = '*'.repeat(length - 9); // 9 = 5(前) + 4(后)
      return `${phone.slice(0, 5)}${stars}${phone.slice(-4)}`;
    }

    return phone; // 如果长度小于11，直接返回原值
  }

  // 定义函数对象
  const functions = {
    // 获取用户列表（懒加载）
    async fetchSourceData(page = 1) {
      loading.value = true;
      try {
        const params: searchSysUserParams = {
          accountStatus: '1',
          pageNo: page,
          pageSize: transferPagination.pageSize,
          searchValue: searchValue.value,
        };
        console.log('请求参数啊啊啊', params);
        const { data } = await getSysUserList(params);

        // 处理新数据
        const newList = (data?.list || []).map((item: any) => ({
          value: item.id,
          label: `${item.userFullname}(${formatPhoneNumber(item.phone)})`,
        }));

        if (page === 1) {
          // 第一页时，合并已选择的数据
          sourceList.value = [
            ...selectedSourceList.value,
            ...newList.filter(
              (item: TransferItem) =>
                !selectedSourceList.value.some(
                  (selected) => selected.value === item.value
                )
            ),
          ];
        } else {
          sourceList.value = [
            ...sourceList.value,
            ...newList.filter(
              (item: TransferItem) =>
                !sourceList.value.some(
                  (existing) => existing.value === item.value
                )
            ),
          ];
        }

        total.value = data?.total || 0;

        // 数据加载完成后，等待DOM更新再绑定滚动事件
        await nextTick();
        await functions.bindScrollEvent();
      } catch (error) {
        Message.error(t('failed-to-get-user-list'));
      } finally {
        loading.value = false;
      }
    },

    // 懒加载：滚动到底部自动加载下一页
    async handleScroll(e: Event) {
      const target = e.target as HTMLElement;
      const { scrollTop, clientHeight, scrollHeight } = target;

      // 添加调试日志
      console.log('滚动事件触发', {
        scrollTop,
        clientHeight,
        scrollHeight,
        isBottom: scrollTop + clientHeight >= scrollHeight - 10,
        isLoading: !loading.value,
        hasMore: sourceList.value.length < total.value,
        currentPage: transferPagination.current,
        totalItems: total.value,
        loadedItems: sourceList.value.length,
      });

      if (
        scrollTop + clientHeight >= scrollHeight - 10 &&
        !loading.value &&
        sourceList.value.length < total.value
      ) {
        console.log('触发加载更多');
        transferPagination.current += 1;
        await functions.fetchSourceData(transferPagination.current);
      }
    },

    // 绑定滚动事件
    async bindScrollEvent() {
      await nextTick();
      // 获取左侧列表的滚动容器
      const leftScrollContainer = document.querySelector(
        '.arco-transfer-view-source .arco-scrollbar-container'
      );
      console.log('bindScrollEvent', leftScrollContainer);
      if (leftScrollContainer) {
        leftScrollContainer.removeEventListener(
          'scroll',
          functions.handleScroll
        );
        leftScrollContainer.addEventListener('scroll', functions.handleScroll);
      }
    },
  };

  // 解构函数以便使用
  const { fetchSourceData, handleScroll, bindScrollEvent } = functions;

  const columns = computed(() => [
    {
      title: t('user.transfer.name'),
      dataIndex: 'userFullname',
      width: 120,
      align: 'left',
      fixed: 'left',
    },
    {
      title: t('user.transfer.account'),
      dataIndex: 'userName',
      width: 120,
      align: 'left',
    },
    {
      title: t('user.transfer.phone'),
      dataIndex: 'phone',
      width: 120,
      align: 'left',
    },
    {
      title: t('user.transfer.email'),
      dataIndex: 'email',
      width: 200,
      align: 'left',
    },
    {
      title: t('user.transfer.operate'),
      width: 100,
      align: 'center',
      slotName: 'operation',
      fixed: 'right',
    },
  ]);

  // 处理移除用户
  const handleRemoveUser = async (record: SysUserMemberRecord) => {
    console.log('移除用户', record);
    console.log('组织编号', props.orgNo);
    console.log('移除用户id', [record.id]);
    try {
      if (!record.id) {
        console.error('用户ID不存在');
        return;
      }
      const params = {
        orgNo: props.orgNo,
        userIdList: [record.id],
      };
      const { data, status } = await orgRemoveMember(params);
      console.log('移除用户结果', data);
      console.log('移除用户结果', status);
      if (status) {
        // 从表格数据中移除
        tableData.value = tableData.value.filter(
          (item) => item.id !== record.id
        );
        // 从已选择的列表中移除
        selecteList.value = selecteList.value.filter((id) => id !== record.id);
        // 从源数据列表中移除
        selectedSourceList.value = selectedSourceList.value.filter(
          (item) => item.value !== record.id
        );
      }
    } catch (error) {
      // Message.error(t('failed-to-get-org-user-list'));
      console.log(error);
    }
  };

  // 获取组织用户列表（右侧）
  const getOrgUserList = async () => {
    if (!props.orgNo) return;
    tableLoading.value = true;
    try {
      const params = {
        orgNo: props.orgNo,
      };
      const { data } = await getOrgUser(params);
      tableData.value = data || [];
      console.log('组织用户数据', data);
    } catch (error) {
      Message.error(t('failed-to-get-org-user-list'));
    } finally {
      tableLoading.value = false;
    }
  };

  // 处理搜索
  const handleTransferSearch = async (value: string, direction: string) => {
    console.log('搜索值：', value, direction);

    if (direction === 'source') {
      console.log('左边搜索值搜索值：', value);
      searchValue.value = value;
      transferPagination.current = 1;
      // 保存当前已选择的数据
      selectedSourceList.value = sourceList.value.filter((item) =>
        selecteList.value.includes(item.value)
      );
      await fetchSourceData(1);
      transferKey.value += 1;
    }
    if (direction === 'target') {
      console.log('右边搜索值搜索值：', value);
    }
  };

  // 处理传输变化
  const handleTransferChange = async (newTargetKeys: string[]) => {
    console.log('新选中的用户：', newTargetKeys);
    selecteList.value = newTargetKeys;
    // 更新已选择的数据
    selectedSourceList.value = sourceList.value.filter((item) =>
      newTargetKeys.includes(item.value)
    );

    if (selecteList.value.length === sourceList.value.length) {
      transferPagination.current += 1;
      await fetchSourceData(transferPagination.current);
    }
  };

  // 清理数据
  const clearData = () => {
    selecteList.value = [];
    sourceList.value = [];
    selectedSourceList.value = [];
    tableData.value = [];
    transferPagination.current = 1;
    searchValue.value = '';
    total.value = 0;
  };

  // 确认按钮
  const handleOk = async () => {
    // 获取选中的用户数据
    console.log('已选择的用户列表selecteList：', selecteList.value);
    const selectedUsers = sourceList.value.filter((item) =>
      selecteList.value.includes(item.value)
    );
    console.log('组织编号：', props.orgNo);
    console.log('已选择的用户列表：', selectedUsers);
    try {
      if (!props.orgNo) {
        Message.error(t('user.transfer.no.org'));
        return;
      }
      if (selecteList.value.length === 0) {
        Message.warning(t('user.transfer.no.user'));
        return;
      }
      const params = {
        orgNo: props.orgNo,
        userIdList: selecteList.value,
      };
      const { data, status } = await orgAddMember(params);
      console.log('移除用户结果', data);
      console.log('移除用户结果', status);
    } catch (error) {
      // Message.error(t('failed-to-get-org-user-list'));
      console.log(error);
    } finally {
      emit('confirm', selectedUsers);
      emit('update:visible', false);
      clearData();
    }
  };

  // 取消按钮
  const handleCancel = () => {
    clearData();
    emit('update:visible', false);
    emit('cancel');
  };

  // 初始化数据
  const initData = async () => {
    clearData();
    transferKey.value += 1; // 强制刷新 transfer
    await fetchSourceData(1);
    await getOrgUserList();
  };

  // 监听弹窗打开时初始化
  watch(
    () => props.visible,
    async (newVal) => {
      console.log('watch', newVal);
      if (newVal) {
        await initData();
      }
    }
  );

  onMounted(async () => {
    console.log('onMounted', props.visible);
    if (props.visible) {
      await initData();
    }
  });

  // 处理右侧搜索
  const handleTargetSearch = (value: string) => {
    // 在已选择的数据中进行过滤
    const filteredList = selectedSourceList.value.filter(item =>
      item.label.toLowerCase().includes(value.toLowerCase())
    );
    // 只更新已选择的数据，不影响sourceList
    selectedSourceList.value = filteredList;
  };

  // 处理左侧搜索
  const handleSourceSearch = (value: string) => {
    handleTransferSearch(value, 'source');
  };
</script>

<style scoped lang="less">
  .user-transfer-container {
    .transfer-container {
      height: 500px;

      :deep(.arco-spin) {
        width: 100% !important;
      }
      :deep(.arco-transfer) {
        display: flex;
        width: 100%;

        :deep(.arco-transfer-view-source) {
          .arco-scrollbar-container {
            height: 100%;
            overflow-y: auto;
          }
        }
      }
    }
  }
  :deep(.arco-transfer) {
    .arco-transfer-view {
      width: calc(50% - 34px) !important;
      height: 300px !important;
    }
  }
  // :deep(.arco-transfer) {
  //   :deep(.arco-transfer-view) {
  //     width: calc(50%-34px) !important;
  //     height: 400px !important;
  //   }
  // }
</style>
