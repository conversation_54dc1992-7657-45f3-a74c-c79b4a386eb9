import { setFusionBiz } from '@/api/interceptor';
import axios from 'axios';
import { QueryRecordParams, DeleteAgentRecordParams } from './types';

export function getAiToken() {
  return axios.get('/cde-work/agent/getToken');
}

export function getAgentList(data: any) {
  return axios.post('/cde-work/agent/list', data);
}

export function getAgentRecordList(data: QueryRecordParams) {
  return axios.post('/cde-work/agent/historyPage', data);
}

// 重命名AI对话
export function renameAgentRecord(data: DeleteAgentRecordParams) {
  return axios.post('/cde-work/agent/rename', data);
}

// 删除AI对话
export function deleteAgentRecord(data: DeleteAgentRecordParams) {
  return axios.post('/cde-work/agent/remove', data);
}

// 查询用户最近文件
export function getRecentlyFiles(data: any) {
  return axios.post('/cde-work/knowledgeBase/listRecentFiles', data);
}

// 查询用户最近文件
export function deleteRecentlyFiles(data: any) {
  return axios.post('/cde-work/knowledgeBase/deleteFile', data);
}

export default null;
