<template>
  <a-modal
    :visible="visible"
    :title="dialogTitle + ' ' + $t('project-setting.milestones')"
    :unmount-on-close="true"
    :mask-closable="false"
    width="660px"
    draggable
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-form
      ref="milestoneRef"
      :model="formData"
      auto-label-width
      :disabled="handleType === 'view'"
      class="form"
    >
      <a-row style="margin-bottom: 16px">
        <a-col :span="24">
          <div class="title">
            <div class="text">
              <img
                src="@/assets/images/project-setting/addTaskTitle.png"
                alt=""
                style="width: 17px; height: 17px"
              />
              <span class="text-font">{{
                $t('project-setting.milestone-information')
              }}</span>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-form-item
        field="name"
        :label="$t('project-setting.milestones-name')"
        :rules="[
          formRule.specialCharts,
          {
            required: true,
            message: t('project-setting.milestones-name-err-msg'),
          },
          {
            maxLength: 255,
            message: t('project-setting.project-name-length-errMsg'),
          },
        ]"
        validate-trigger="input"
      >
        <a-input
          v-model="formData.name"
          :placeholder="$t('please-enter')"
          :max-length="currentLocale === 'en-US' ? 255 : 100"
          show-word-limit
        />
      </a-form-item>

      <a-form-item
        field="endTime"
        :label="$t('project-setting.end-time')"
        :rules="[
          {
            required: true,
            message: t('project-setting.end-time-err-msg'),
          },
        ]"
      >
        <a-date-picker
          v-model="formData.endTime"
          :placeholder="$t('please-select')"
          :disabled-date="(current: any) => dayjs(current).isBefore(planStart) || dayjs(current).isAfter(planEnd)"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item
        field="describe"
        :label="$t('project-setting.describe')"
        :rules="[
          {
            required: true,
            message: t('project-setting.describe-err-msg'),
          },
          {
            maxLength: 255,
            message: t('project-setting.project-name-length-errMsg'),
          },
        ]"
        validate-trigger="input"
      >
        <a-textarea
          v-model="formData.describe"
          :placeholder="$t('please-enter')"
          allow-clear
          :max-length="currentLocale === 'en-US' ? 2000 : 1000"
          show-word-limit
        />
      </a-form-item>
    </a-form>
    <a-divider></a-divider>
    <a-row style="margin-bottom: 16px">
      <a-col :span="24">
        <div class="title">
          <div class="text">
            <img
              src="@/assets/images/project-setting/addTaskTitle.png"
              alt=""
              style="width: 17px; height: 17px"
            />
            <span class="text-font">{{
              $t('project-setting.team-tasks')
            }}</span>
            <a-button
              type="text"
              style="position: absolute; right: 0"
              @click="handleAddTeam"
              >{{ $t('project-setting.add_task') }}</a-button
            >
          </div>
        </div>
      </a-col>
    </a-row>
    <a-form ref="teamListRef" :model="teamForm" auto-label-width class="form">
      <div v-for="(item, index) in teamForm.teamData" :key="index">
        <div class="teamItem">
          <div style="width: 90%">
            <a-form-item
              :field="`teamData[${index}].teamId`"
              :label="$t('project-setting.teamName')"
              :rules="[
                {
                  required: true,
                  message: t('project-setting.team-task-name-errMsg'),
                },
              ]"
            >
              <a-select
                v-model="item.teamId"
                :placeholder="$t('project-setting.team-task-name-errMsg')"
              >
                <a-option
                  v-for="team in teamList"
                  :key="`${team.id}-${team.name}`"
                  :value="team.id"
                  :label="team.name"
                ></a-option>
              </a-select>
            </a-form-item>
            <a-form-item
              :field="`teamData[${index}].date`"
              :label="$t('project-setting.team-start-end-time')"
              :rules="[
                {
                  required: true,
                  message: t('project-setting.team-start-end-time-errMsg'),
                },
              ]"
            >
              <a-range-picker
                v-model="item.date"
                style="width: 100%"
                :disabled-date="(current: any) => dayjs(current).isBefore(planStart) || dayjs(current).isAfter(taskEnd)"
                @change="pickerChange(index, item)"
              ></a-range-picker>
            </a-form-item>
            <a-form-item
              :field="`teamData[${index}].description`"
              :label="$t('project-setting.task-description')"
              :rules="[
                {
                  required: true,
                  message: t('project-setting.task-description-errMsg'),
                },
                {
                  maxLength: 255,
                  message: t('project-setting.project-name-length-errMsg'),
                },
              ]"
              validate-trigger="input"
            >
              <a-textarea
                v-model="item.description"
                :placeholder="$t('project-setting.task-description-errMsg')"
                allow-clear
                :max-length="currentLocale === 'en-US' ? 2000 : 1000"
                show-word-limit
              />
            </a-form-item>
          </div>
          <div>
            <a-button
              type="text"
              status="danger"
              shape="circle"
              @click="handleDelTeam(index, item)"
            >
              <icon-minus-circle />
            </a-button>
          </div>
        </div>
        <a-divider v-if="index !== teamForm.teamData.length - 1"></a-divider>
      </div>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message, Modal, Notification } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  // import teamTasks from './teamTasks-add.vue';
  import {
    MilestoneRecord,
    saveMilestone,
    updateMilestone,
    MilestoneRecordTeam,
    saveMainTask,
    updateMainTask,
    removeTask,
  } from '../api';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';
  import { getTeamList } from '../api';
  import colors from '../json/colors.json';
  import useLocale from '@/hooks/locale';
  import { formRule } from '@/utils/index';

  const { t } = useI18n();
  // 国际化类型
  const { currentLocale } = useLocale();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    handleType: {
      type: String,
      default: 'view',
    },
    originData: {
      type: Object,
      require: true,
    },
    teamData: {
      type: Object,
      require: true,
    },
    planStart: {
      type: String,
      default: '',
    },
    planEnd: {
      type: String,
      default: '',
    },
    taskEnd: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits(['update:visible', 'refresh']);

  const formData = ref<MilestoneRecord>({
    ...props.originData,
  } as MilestoneRecord);
  const teamForm = ref<MilestoneRecordTeam>({
    ...props.teamData,
  } as MilestoneRecordTeam);

  watch(
    () => props.originData,
    (n) => {
      formData.value = { ...n } as MilestoneRecord;
    }
  );
  watch(
    () => props.teamData,
    (n) => {
      teamForm.value = { ...n } as MilestoneRecordTeam;
    }
  );

  const dialogTitle = computed(() => {
    switch (props.handleType) {
      case 'add':
        return t('project-setting.create');
      case 'view':
        return t('project-setting.view');
      default:
        return t('project-setting.edit');
    }
  });

  const milestoneRef = ref<FormInstance>();
  const teamListRef = ref<FormInstance>();
  // 新增里程碑
  const handleAdd = async () => {
    const params = {
      ...formData.value,
    };
    const res = await saveMilestone(params);
    return res;
  };
  // 集成新增和编辑
  const teamHandleAll = async () => {
    const addList = teamForm.value.teamData.filter((item) => {
      return item.id === '';
    });
    const editList = teamForm.value.teamData.filter((item) => {
      return item.id !== '';
    });

    return {
      addList,
      editList,
    };
  };
  // 新增里程碑主任务
  const teamHandleAdd = async (id: string | number, list) => {
    list.forEach(
      async (element: { milestoneId: string | number; color: string }) => {
        element.milestoneId = id;
        const colorIndex = Math.floor(Math.random() * colors.length);
        element.color = colors[colorIndex];
      }
    );
    const res = await saveMainTask(list);
    return !!res.status;
  };
  // 编辑里程碑主任务
  const teamHandleEdit = async (list) => {
    const res = await updateMainTask(list);
    return !!res.status;
  };
  // 编辑里程碑
  const handleEdit = async () => {
    const params = {
      ...formData.value,
    };
    const res = await updateMilestone(params);
    return res;
  };
  // 删除主任务
  const removeTaskItem = async (id: string) => {
    const res = await removeTask(id);
    return res;
  };

  // 删除主任务
  const handleDelTeam = async (index: number, item) => {
    if (item.id && item.children && item.children.length > 0) {
      Modal.info({
        title: t('project-setting.confirm-deletion'),
        hideCancel: false,
        content: t('project-setting.deletion-hint'),
        onOk: async () => {
          const res: any = await removeTask(item.id);
          if (res.status) {
            Notification.success({
              id: 'id',
              title: 'Success',
              content: t('project-setting.edit-success'),
            });
            teamForm.value.teamData.splice(index, 1);
          }
        },
      });
    } else if (item.id) {
      const res1 = await removeTaskItem(item.id);
      if (res1) {
        Notification.success({
          id: 'id',
          title: 'Success',
          content: t('project-setting.edit-success'),
        });
        teamForm.value.teamData.splice(index, 1);
      }
    } else {
      teamForm.value.teamData.splice(index, 1);
    }
  };
  interface Person {
    startTime: string;
    endTime: string;
    date: [string, string];
  }
  const pickerChange = (index: number, item: Person) => {
    const a: string = item.date[0];
    const b: string = item.date[1];
    teamForm.value.teamData[index].startTime = a;
    teamForm.value.teamData[index].endTime = b;
  };

  // 增加主任务
  const handleAddTeam = () => {
    teamForm.value.teamData.push({
      id: '',
      teamId: '',
      projectId: formData.value.projectId,
      startTime: '',
      parentId: '0',
      endTime: '',
      date: [],
      description: '',
      type: 0,
      color: '',
    });
  };
  // 确定按钮点击事件
  const handleBeforeOk = async (done: any) => {
    const res = await milestoneRef.value?.validate();
    const resTeam = await teamListRef.value?.validate();

    if (!res && !resTeam) {
      if (teamForm.value.teamData && teamForm.value.teamData.length > 0) {
        let flag = false;
        teamForm.value.teamData.forEach((element) => {
          if (
            new Date(element.endTime).getTime() >
            new Date(formData.value.endTime).getTime()
          ) {
            flag = true;
          }
        });
        if (flag) {
          Message.error(t('project-setting.add-task-error'));
          return;
        }
      }
      let flg = false;
      let msg = t('project-setting.add-success');
      if (props.handleType === 'add') {
        const res1 = await handleAdd();
        if (teamForm.value.teamData.length > 0) {
          const res2 = await teamHandleAdd(
            res1.data.id,
            teamForm.value.teamData
          );
          flg = !!res2;
        } else {
          flg = !!res1.status;
        }
      } else if (props.handleType === 'edit') {
        const res3 = await handleEdit();
        if (teamForm.value.teamData.length > 0) {
          const list = await teamHandleAll();
          let addSuccess = true;
          let editSuccess = true;

          if (list.editList && list.editList.length > 0) {
            const res4 = await teamHandleEdit(list.editList);
            addSuccess = !!res4;
          }
          if (list.addList && list.addList.length > 0) {
            const res5 = await teamHandleAdd(res3.data.id, list.addList);
            editSuccess = !!res5;
          }
          flg = addSuccess && editSuccess;
        } else {
          flg = !!res3.status;
        }
        msg = t('project-setting.edit-success');
      }

      if (flg) {
        Message.success(msg);
        emit('update:visible', false);
        emit('refresh');
      }
      done();
    }
  };

  // 取消按钮点击事件
  const handleCancel = () => {
    emit('update:visible', false);
    emit('refresh');
  };

  interface TeamObj {
    id?: string | number;
    name?: string;
  }

  const teamList = ref<TeamObj[]>([]);
  // 列表
  const route = useRoute();
  const projectId = computed(() => {
    return String(route?.params?.projectId);
  });

  // 查询列表数据
  const getOneTeamList = () => {
    const params = {
      projectId: projectId.value,
    };
    getTeamList(params)
      .then((res) => {
        teamList.value = res.data || [];
      })
      .catch((err) => {
        // eslint-disable-next-line no-console
        console.log(err);
      });
  };
  getOneTeamList();
</script>

<style lang="less" scoped>
  form {
    :deep(.arco-row) {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .teamItem {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
  }
  .text {
    display: flex;
    align-content: center;
    align-items: center;
    position: relative;
  }
</style>
