<template>
  <div class="schedule-container">
    <a-row class="schedule-head">
      <div class="leftIcon"
        ><commonTabs v-model="tabKey" :tabs="[]"></commonTabs
      ></div>
      <div class="tabs-box">
        <div
          v-for="item in tabsData"
          :key="item.key"
          :class="{ active: item.key === activeTab }"
          @click="changeTabs(item)"
          >{{ item.title }}</div
        >
      </div>
      <span
        v-if="globalMode === 'project'"
        class="project-plan"
        @click="jumpTo"
      >
        <settingIcon />
        <span style="display: inline-block;margin-left: 10px">{{ $t('schedule.project-plan') }}</span>
      </span>
    </a-row>
    <div class="schedule-content">
      <calendar
        v-if="activeTab === 'calendar'"
        @change-tab="changeTabHandle"
      ></calendar>

      <gantt v-if="activeTab === 'gantt'"></gantt>
      <matters
        v-if="activeTab === 'matters'"
        :id="scheduleId"
        :types="editType"
      ></matters>
      <meeting
        v-if="activeTab === 'meeting'"
        :id="scheduleId"
        :types="editType"
        @change-tab="changeTabHandle"
      ></meeting>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, provide, watch, computed, onBeforeMount } from 'vue';
  import calendar from './component/calendar/index.vue';
  import gantt from './component/gantt/index.vue';
  import matters from './component/matters/index.vue';
  import meeting from './component/meeting/index.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { userScheduleStore, useGlobalModeStore } from '@/store';
  import commonTabs from '@/components/common-tabs/index.vue';
  import { useI18n } from 'vue-i18n';
  import settingIcon from '@/assets/images/matter/todo2-line.svg';
  import { setLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';


  const router = useRouter();
  const userId = getUserId() || '';
  const { t } = useI18n();
  const props = defineProps<{
    type?: string;
    id?: string;
  }>();
  const globalModeStore = useGlobalModeStore();
  const globalMode = computed(() => globalModeStore.getGlobalMode);
  const tabKey = ref('issues');

  const route = useRoute();

  const activeTab: any = ref('calendar');
  const scheduleId = ref('');
  const editType = ref('edit');

  const tabsData = computed(() => {
    // 根据 globalMode 判断是否需要日历数据
    const tabs = [
      {
        title: t('schedule.calendar'),
        key: 'calendar',
      },
      {
        title: t('schedule.matters'),
        key: 'matters',
      },
      {
        title: t('schedule.meeting'),
        key: 'meeting',
      },
    ];

    if (route?.params?.projectId) {
      const ganntTab = {
        title: '甘特图',
        key: 'gantt',
      };
      // 插入到索引 1 的位置（即在 calendar 后面）
      tabs.splice(1, 0, ganntTab);
    }

    return tabs;
  });
  const tabsBoxWidth = computed(() => {
    return tabsData.value.length === 3 ? '204px' : '144px'; // 三个元素时宽度为 304px，两个元素时宽度为 204px
  });

  const scheduleStore = userScheduleStore();

  const changeTabs = (item: any) => {
    activeTab.value = item.key;
    scheduleId.value = '';
    editType.value = 'edit';
    scheduleStore.setScheduleTab(item.key);
  };
  scheduleStore.setScheduleTab(activeTab.value);
  // 修改tabs
  const changeTabHandle = (data: any) => {
    console.log('日程页面点击的新建传递到父组件的数据:86', data);
    if (data) {
      // const typeToTab: any = {
      //   create: 'create',
      //   meeting: 'meeting',
      // };
      // const tab = typeToTab[data.type] || 'matters';
      activeTab.value = data.type === 'matters' ? 'matters' : 'meeting';
      scheduleId.value = data.scheduleSubId;
      editType.value = data.editType === 'create' ? 'new' : 'edit';
      console.log('目前传递给子组件的type:93', editType.value);
    }
  };

  // 会议事项公用列表新增跳转到新建页面
  const grandparentMethod = (data: any) => {
    changeTabHandle(data);
  };

  // 新增页面跳转过来后定位到该数据进行编辑
  const addToEdit = (data: any) => {
    console.log('新增跳转过来');
    changeTabHandle(data);
  };
  provide('grandparentMethod', grandparentMethod);
  provide('addToEdit', addToEdit);

  provide('activeTab', activeTab);

  provide('handleJump', (cate: string, id: string) => {
    activeTab.value = cate;
    scheduleId.value = id;
  });

  const init = () => {
    const { query } = route;
    const data = {
      type: query.type,
      scheduleSubId: query.id,
      editType: query.editType,
      // scheduleSubId: query.id,
    };
    setTimeout(() => {
      changeTabHandle(data);
    }, 100);
  };

  watch(
    () => route.query,
    (val) => {
      if (val.type) init();
    },
    { immediate: true, deep: true }
  );

  // 扫码登录成功会重定向回登录页并携带access_token参数，这里判断路由传参到具体的tab栏
  onBeforeMount(async () => {
    console.log('props:', props);
    console.log('type:', props.type);
    console.log('id:', props.id);

    const projectId = route.params.projectId as string;
    const key = `last_project_${userId}`;
    const oldProjectId = localStorage.getItem(key);
    if (route.query.type && projectId && oldProjectId !== projectId) {
      setLocalstorage(key, projectId);
    }

    if (props.type === 'meeting' && props.id) {
      activeTab.value = 'meeting';
      scheduleId.value = props.id;
      editType.value = 'edit';
    } else if (props.type === 'matters' && props.id) {
      activeTab.value = 'matters';
      scheduleId.value = props.id;
      editType.value = 'edit';
    }
  });
  // 跳转到项目计划页面
  const jumpTo = () => {
    router.push({
      name: 'project-setting',
      query: {
        tab: 'project-plan',
      },
    });
  };
</script>

<style scoped lang="less">
  .schedule-container {
    padding: 16px 20px;
    background: #ffffff;
    .schedule-head {
      display: flex;
      align-items: start;
      height: 36px;
      margin-bottom: 16px;
      :deep(.arco-icon-menu-unfold) {
        font-size: 18px;
        color: #4e5969;
      }
      .tabs-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 4px;
        overflow: hidden;
        margin-left: 12px;
        div {
          height: 36px;
          background-color: transparent;
          text-align: center;
          line-height: 36px;
          cursor: pointer;
          color: #4e5969;
          font-size: 16px;
          padding: 0px 16px;
          margin-right: 12px;
        }
        .active {
          background-color: #f2f3f5;
          color: #3366ff;
          border-radius: 36px;
        }
      }
    }
    .schedule-content {
      height: calc(100vh - 170px);
      // margin-top: 18px;
    }
  }
  :deep(.common-tabs) {
    display: inline-block;
    margin-bottom: 0;
  }

  .leftIcon {
    display: inline-block;
    padding-top: 5px;
  }
  .project-plan {
    height: 36px;
    background-color: #ffff;
    position: fixed;
    top: 92px;
    right: 130px;
    cursor: pointer;
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 16px;
    color: #4e5969;
  }
  .setting-icon {
    margin-right: 8px;
  }
</style>
