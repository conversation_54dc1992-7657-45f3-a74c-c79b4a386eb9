import { computed } from 'vue';
import i18n from '@/locale/index';

const { t } = i18n.global;

// 审阅 交付 共享 表单对应的formKey
export const FormKeys = {
  review: 'cde_review_c2ea22fdc8f9dc71e6beede1302d5d1f', //    审阅表单key
  delivery: 'cde_delivery_2bc617d2ae38589f5a26efe29e1cb8e2', //   交付表单key
  collaborate: 'cde_collaborate_6923afa9cc27e806c661132f23921009', //    提资 | 共享 表单key
  issue: 'cde_issue_38038efd004311eeb9ae3cf01131a09c', // 问题 表单 key
  enclosure: 'cde_attach_a55c9e8cff8f11eda13a0242ac1e0006', // 附函 表单key
  share: 'cde_collaborate_9803b5b50fdeefed249a96c7689691e1', // 共享文件夹  目前仅用于消息通知

  reviewNotify: 'cde_review_notify_yb7n9g3tc79il4mo8lp9zv4fb7w0kw4i',
  deliveryNotify: 'cde_delivery_notify_6lq9hwj2pg4m9rl0v4e7ot1bcr2qkn0d',
  collaborateNotify: 'cde_collaborate_notify_ji9sm55i5xz1qhj8jga0ald10mm9fyf0',
  issueNotify: 'cde_issue_notify_jko7b96uw7wg3d1ox2g98cn0b9ha90s5',
  collaborateRemind: 'cde_folder_collaborate_0B23afa9aU17e808UX1132f239I9A01',
  meetingNews: 'cde_meet_notify_d65fh15fcg4m9rhhyde7ot1kju3q5tgf',
};

export const FormConfig = {
  review: {
    formKey: 'cde_review_c2ea22fdc8f9dc71e6beede1302d5d1f',
    passUrl: '/cde-collaboration/review/pass',
    refuseUrl: '/cde-collaboration/review/deleteproc',
    infoUrl: '/cde-collaboration/review/detail',
  },
  delivery: {
    formKey: 'cde_delivery_2bc617d2ae38589f5a26efe29e1cb8e2',
    passUrl: '/cde-collaboration/delivery/pass',
    refuseUrl: '/cde-collaboration/delivery/deleteproc',
    infoUrl: '/cde-collaboration/delivery/detail',
  },
  collaborate: {
    formKey: 'cde_collaborate_6923afa9cc27e806c661132f23921009',
    passUrl: '/cde-collaboration/collaborate/pass',
    refuseUrl: '/cde-collaboration/collaborate/deleteproc',
    infoUrl: '/cde-collaboration/collaborate/detail',
  },
};

export const ProcessStateMap: any = computed(() => [
  t('directionary.on-the-move'),
  t('directionary.completed'),
  t('directionary.abandon'),
  t('directionary.temporary-draft'),
  t('directionary.termination'),
]);

export const ProcessStatusColor = {
  [String(0)]: 'orange',
  [String(1)]: 'green',
  [String(2)]: 'magenta',
  [String(3)]: 'cyan',
  [String(4)]: 'red',
};
export const ProcessStates = [
  {
    label: t('directionary.on-the-move'),
    value: 0,
  },
  {
    label: t('directionary.completed'),
    value: 1,
  },
  {
    label: t('directionary.abandon'),
    value: 2,
  },
  {
    label: t('directionary.temporary-storage'),
    value: 3,
  },
  {
    label: t('directionary.termination'),
    value: 4,
  },
];

export const TaskStateMap = {
  [String(0)]: t('directionary.wait-to-be-done'),
  [String(1)]: t('directionary.completed'),
  [String(2)]: t('directionary.abandon'),
  [String(3)]: t('directionary.temporary-storage'),
  [String(4)]: t('directionary.termination'),
};

export const TaskStatusColor = {
  [String(0)]: 'orange',
  [String(1)]: 'green',
  [String(2)]: 'magenta',
  [String(3)]: 'cyan',
  [String(4)]: 'red',
};

export const TaskApproveState = {
  todo: t('directionary.wait-to-be-done'),
  commit: t('directionary.submit'),
  agree: t('directionary.consent'),
  reject: t('directionary.send-back'),
  delegate: t('directionary.appoint'),
  skip: t('directionary.skip'),
  stage: t('directionary.temporary-storage'),
};

export const TaskApproveStateColor = {
  todo: 'orange',
  commit: 'blue',
  agree: 'green',
  reject: 'red',
  delegate: 'gray',
  skip: 'lime',
  stage: 'cyan',
};
export default null;
