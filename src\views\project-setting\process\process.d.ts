import { BPMNModdle, Definitions, BaseElement } from 'bpmn-moddle';

export interface BPMNModdleOBJ extends BPMNModdle {
  toXML(definitions: any, options?: any): Promise<any>;
  set(key: string, value: any): void;
}
export interface DefinitionExtend extends Definitions {
  rootElement?: any;
}

export interface ElementExtend extends BaseElement {
  get(key: any): any;
  set(key: string, value: any): void;
}
