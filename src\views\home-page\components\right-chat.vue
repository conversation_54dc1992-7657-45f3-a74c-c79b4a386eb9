<template>
  <div class="ai-chat-container">
    <div class="top">
      <div class="model-name"> {{ $t('home.chat.general-dialog') }} </div>
    </div>
    <div ref="dialogContainer" class="dialog-container"> </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import useAIChat from '@/hooks/aiChat';

  // 使用 AI 聊天的逻辑
  const { genAIToken, initAIPage } = useAIChat();

  const dialogContainer = ref<HTMLElement | null>(null);

  const showAIPage = async () => {
    await genAIToken();

    const res = initAIPage(
      'xz33285jrxhvfo4gqrto',
      'agentSession',
      dialogContainer.value,
      null,
      '请输入您的问题'
    );
    if (!res.status) {
      console.error(res.message);
    }
  };

  onMounted(() => {
    showAIPage();
  });
</script>

<style scoped lang="less">
  .ai-chat-container {
    width: 522px;
    height: calc(100% - 168px);
    // position: fixed;
    // right: 20px;
    // top: 76px;
    // bottom: 20px;
    background: white;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    border-radius: 8px 8px 8px 8px;

    .top {
      margin-top: 15px;
      margin-left: 20px;
      margin-bottom: 17px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .model-name {
        font-size: 16px;
        font-weight: 500;
        color: #4e5969;
      }
    }

    .dialog-container {
      height: calc(100% - 52px);
    }
  }

  // AI样式覆盖
  .dialog-container {
    :deep(.chat-input-content) {
      .content-edit-box {
        border: none !important;
      }
    }
    :deep(.tiptap) {
      outline: none;
    }
    :deep(.foot-box-action .default-css) {
      border-radius: 26px !important;
    }
    :deep(.arco-list-bordered) {
      border: none;
    }
    :deep(.main-begin .main-view-main) {
      height: 0;
    }
    :deep(.reanoning-content li) {
      margin-left: 16px;
    }
    // footer换行，高度调整
    :deep(.foot-box-text) {
      height: 40px !important;
    }
  }
</style>
