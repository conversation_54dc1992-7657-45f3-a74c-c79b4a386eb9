<template>
  <div style="padding: 20px">
    <a-row>
      <a-col :span="modelNumber === 2 ? 11 : 24">
        <a-card
          style="width: 100%; text-align: left"
          :title="`${$t('model-collaboration.model') + ' A'}`"
        >
          <a-tree
            v-model:selected-keys="selectedKeysA"
            block-node
            :data="treeData[0]"
            @select="selectedEvent"
          />
        </a-card>
      </a-col>
      <a-col v-if="modelNumber === 2" :span="11" :offset="2">
        <a-card
          style="width: 100%; text-align: left"
          :title="`${$t('model-collaboration.model') + ' B'}`"
        >
          <a-tree
            v-model:selected-keys="selectedKeysB"
            block-node
            :data="treeData[1]"
            @select="selectedEvent"
          />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    modelNumber: {
      type: Number,
      default: 1,
    },
    treeData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  });

  const selectedKeysA = ref([]);
  const selectedKeysB = ref([]);

  const emit = defineEmits(['change']);

  const selectedEvent = () => {
    if (selectedKeysA.value.length + selectedKeysB.value.length === 2) {
      emit('change', false);
    }
  };

  const sumSelectedKeys = computed(() => {
    return selectedKeysA.value.length + selectedKeysB.value.length;
  });

  defineExpose({
    sumSelectedKeys,
  });
</script>

<style scoped lang="less">
  :deep(.arco-tree-node:hover),
  :deep(.arco-tree-node-selected) {
    border-left: 24px solid rgb(235, 240, 255);
    border-right: 24px solid rgb(235, 240, 255);
    background-color: rgb(235, 240, 255);
    .arco-icon-more-vertical {
      opacity: 1;
    }
  }

  :deep(.arco-tree-node-title:hover) {
    background-color: inherit;
  }
</style>
