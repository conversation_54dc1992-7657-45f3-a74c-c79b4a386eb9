<template>
  <a-modal
    :visible="visible"
    width="50%"
    :mask-closable="false"
    :ok-loading="submitBtnLoading"
    draggable
    :esc-to-close="false"
    @cancel="cancel"
    @ok="submitData"
  >
    <template #title>
      {{ title }}
    </template>
    <div class="content">
      <div class="nodes-display">
        <a-row
          v-for="(row, index) in nodeRows"
          :key="`row-${index}`"
          :gutter="10"
          :class="index % 2 === 1 ? 'node-row flexEnd' : 'node-row'"
        >
          <a-col
            v-for="(node, node_index) in index % 2 === 1 ? row.reverse() : row"
            :key="`row-${index}-node-${node_index}`"
            :span="6"
          >
            <div
              :class="`node-item ${node.isStart ? 'start-node' : ''}${
                node.isEnd ? 'end-node' : ''
              }`"
            >
              <div class="node-num"> {{ node['_index'] }} </div>
              <div class="node-name">
                {{ node.name }}
              </div>
              <div
                v-if="
                  !(
                    index % 2 === 0 &&
                    node_index === row.length - 1 &&
                    node.isEnd
                  )
                "
                :class="`line ${node_index === 3 ? 'end-line' : ''}`"
              ></div>
              <div
                v-if="
                  index % 2 === 1 &&
                  node_index === 0 &&
                  index < nodeRows.length - 1
                "
                class="link-left"
              ></div>
              <div
                v-if="
                  index % 2 === 0 &&
                  node_index === 3 &&
                  index < nodeRows.length - 1
                "
                class="link-right"
              ></div>
            </div>
          </a-col>
        </a-row>
      </div>
      <a-form
        ref="formRef"
        :model="formData"
        label-align="right"
        style="margin-top: 12px"
      >
        <a-row>
          <a-col :span="24">
            <div class="title">
              <div class="text">
                <img
                  src="@/assets/images/check/<EMAIL>"
                  alt=""
                  style="width: 17px; height: 17px"
                />
                <span class="text-font">{{
                  $t('project-setting.flow-template-name')
                }}</span>
              </div>
              <div class="file-count"> </div>
            </div>
          </a-col>
        </a-row>
        <a-row :gutter="20" style="margin-top: 16px">
          <a-col :span="12">
            <a-form-item
              field="name"
              :label="$t('project-setting.flow-template-name')"
              :rules="[
                {
                  required: true,
                  message: t('project-setting.please-enter-name'),
                },
              ]"
              label-col-flex="110px"
              :disabled="type === 2"
            >
              <remove-spaces-input
                v-model="formData.name"
                :placeholder="$t('project-setting.please-enter-name')"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="description"
              :label="$t('project-setting.flow-template-description')"
              :rules="[
                {
                  required: true,
                  message: t('project-setting.please-enter-description'),
                },
              ]"
              label-col-flex="110px"
              :disabled="type === 2"
            >
              <remove-spaces-input
                v-model="formData.description"
                :placeholder="$t('project-setting.please-enter-description')"
              />
              <!-- <a-input
                v-model="formData.description"
                :placeholder="$t('project-setting.please-enter-description')"
              /> -->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="id"
              :label="$t('project-setting.team-name')"
              label-col-flex="110px"
              :rules="[
                {
                  required: true,
                  message: t('project-setting.please-select-team'),
                },
              ]"
              :disabled="type === 2"
            >
              <a-select
                v-model="formData.id"
                :placeholder="$t('project-setting.please-select-team')"
              >
                <a-option
                  v-permission="$btn.project.editProject"
                  key="0-通用"
                  :value="0"
                  label="通用"
                />
                <a-option
                  v-for="team in teamList"
                  :key="`${team.id}-${team.name}`"
                  :value="team.id"
                  :label="i18TeamName(team)"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-divider></a-divider>
      <a-form ref="nodeRef" :model="nodeDataMap" label-align="right">
        <a-row style="margin-bottom: 16px">
          <a-col :span="24">
            <div class="title">
              <div class="text">
                <img
                  src="@/assets/images/check/<EMAIL>"
                  alt=""
                  style="width: 17px; height: 17px"
                />
                <span class="text-font">{{
                  $t('project-setting.flow-node-configuration')
                }}</span>
              </div>
              <div class="file-count">
                <a-button type="text" :disabled="type === 2" @click="addNode">{{
                  $t('project-setting.add-node')
                }}</a-button>
              </div>
            </div>
          </a-col>
        </a-row>
        <a-row
          v-for="(node, index) in nodeData"
          :key="`node-${index}`"
          :gutter="20"
        >
          <a-col flex="300px">
            <a-form-item
              :key="`name-${index}`"
              :field="`name-${index}`"
              :label="`${$t('project-setting.node')} ${index + 1} ${$t(
                'project-setting.name'
              )}`"
              label-col-flex="130px"
              :disabled="type === 2"
              :rules="[
                {
                  required: true,
                  message: $t('project-setting.please-enter-name'),
                },
              ]"
              :validate-trigger="['change', 'input']"
            >
              <a-input
                v-model="node.name"
                :placeholder="$t('project-setting.please-enter-name')"
              />
            </a-form-item>
          </a-col>
          <a-col flex="auto">
            <a-form-item
              :key="`user-${index}`"
              :field="`user-${index}`"
              :label="$t('project-setting.auditor')"
              :disabled="type === 2"
              label-col-flex="45px"
            >
              <UserSelector
                v-model:model-value="node.userNameStr"
                :using-name="true"
                :multiple="false"
                :project-id="route.params.projectId"
              ></UserSelector>
            </a-form-item>
          </a-col>
          <!-- <a-col :span="4">
            <a-dropdown-button>
              {{ node.audit === '1' ? $t('project-setting.all-audit-required') : $t('project-setting.one-audit-required') }}
              <template #icon>
                <icon-down />
              </template>
              <template #content>
                <a-doption @click="changeAudit(index, '0')"
                  >{{ $t('project-setting.one-audit-required') }}</a-doption
                >
                <a-doption @click="changeAudit(index, '1')"
                  >{{ $t('project-setting.all-audit-required') }}</a-doption
                >
              </template>
            </a-dropdown-button>
          </a-col> -->
          <!-- <a-col flex="100px">
            <a-switch
              v-if="node.userNameStr.split(',').length > 1"
              checked-color="#3366FF"
              :default-checked="true"
              unchecked-color="#FBBB3E"
              v-model="node.audit"
              checked-value="0"
              unchecked-value="1"
            >
              <template #checked> 一人通过 </template>
              <template #unchecked> 全部通过 </template>
            </a-switch>
            <a-switch
              v-else
              checked-color="#C9CDD4"
              :default-checked="true"
              :disabled="true"
              v-model="node.audit"
              checked-value="0"
              unchecked-value="1"
            >
              <template #checked> 一人通过 </template>
            </a-switch>
          </a-col> -->
          <a-col v-if="props.type !== 2" flex="25px"
            ><icon-delete class="icon-delete" @click="nodeDelete(node.key)" />
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, defineEmits, defineProps, ref, watch } from 'vue';
  import UserSelector from '@/components/user-selector/index.vue';
  import { useRoute } from 'vue-router';
  import { FormInstance } from '@arco-design/web-vue';
  import { Message } from '@arco-design/web-vue';
  import { genXMLByNodes } from './common/gen-process';
  import { createProcess, updateProcess } from './api';
  import { useI18n } from 'vue-i18n';
  import removeSpacesInput from '@/components/removeSpacesInput/index.vue';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';
  import usePrjPermissionStore from '@/store/modules/project-permission';

  const { t } = useI18n();
  const { i18TeamName } = useI18nHandleName();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: Number,
      default: 0, // 0 新建 1 编辑 2 查看
    },
    defaultProcess: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  const route = useRoute();
  const emits = defineEmits(['update:visible', 'submit', 'update']);
  const formRef = ref<FormInstance>();
  const nodeRef = ref<FormInstance>();
  const formData = ref({
    id: '',
    name: '',
    description: '',
  });
  const title = computed(() => {
    let tit = t('project-setting.create-process-template');
    if (props.type === 1) {
      tit = t('project-setting.edit-process-template');
    }
    if (props.type === 2) {
      tit = t('project-setting.view-process-template');
    }
    return tit;
  });
  const nodeData = ref<any[]>([]);
  const nodeDataMap = computed(() => {
    const map: any = {};
    nodeData.value?.forEach((e, index) => {
      map[`name-${index}`] = e.name;
      map[`user-${index}`] = e.userNameStr;
    });
    return map;
  });
  const submitBtnLoading = ref(false);
  const nodeRows = computed(() => {
    const rows: any = [];
    const nodes: any[] = JSON.parse(JSON.stringify(nodeData.value));
    const startNode = {
      name: t('project-setting.start'),
      isStart: true,
    };
    const endNode = {
      name: t('project-setting.end'),
      isEnd: true,
    };
    nodes.unshift(startNode);
    nodes.push(endNode);
    for (let i = 0; i < nodes.length; i += 4) {
      const row = [];
      for (let j = i; j < i + 4; j += 1) {
        if (j < nodes.length) {
          // eslint-disable-next-line no-underscore-dangle
          nodes[j]._index = j + 1;
          row.push(nodes[j]);
        }
      }
      rows.push(row);
    }
    return rows;
  });

  // 将i定义为ref，并在init函数中根据已有节点重新计算最大key值
  const nextNodeKey = ref(1);
  const addNode = () => {
    const num = nodeData.value.length;
    nodeData.value.push({
      name: `${t('project-setting.node')}${num + 1}`,
      userNameStr: '',
      key: nextNodeKey.value++,
      // 初始化审核条件的默认值
      audit: '0',
    });
  };
  const cancel = () => {
    nodeData.value = [];
    emits('update:visible', false);
  };
  const submitData = async (): Promise<string> => {
    if (props.type === 2) {
      cancel();
      return '';
    }
    let validate = true;
    if (!nodeData.value.length) {
      Message.error(
        t('project-setting.please-complete-node-name-or-auditor-info')
      );
      return '';
    }
    nodeData.value.forEach((e) => {
      if (!e.name || !e.userNameStr) {
        validate = false;
      }
    });
    await formRef.value?.validate();
    await nodeRef.value?.validate();
    if (!validate) {
      Message.error(
        t('project-setting.please-complete-node-name-or-auditor-info')
      );
      return '';
    }
    if (
      !formData?.value?.description ||
      !formData?.value?.name ||
      !(formData?.value?.id || formData?.value?.id === 0)
    ) {
      Message.error(t('project-setting.please-complete-basic-info'));
      return '';
    }
    const { variable, processConfig, xml, userTaskIds } = await genXMLByNodes(
      nodeData.value,
      {
        name: formData?.value?.name,
      }
    );
    const processUser = nodeData.value.map((e, index) => {
      const node = JSON.parse(JSON.stringify(e));
      node.USER_TASK_ID = userTaskIds[index];
      return node;
    });
    const params = {
      ...props.defaultProcess,
      variable: JSON.stringify(variable),
      modelKey: processConfig.processId || '',
      modelName: processConfig.name || '',
      bpmnXml: xml,
      projectId: route.params.projectId || '',
      teamId: formData.value?.id,
      describe: formData.value?.description || '',
      processUser: JSON.stringify(processUser),
      BPM_CUSTOM_EXT: '',
    };
    submitBtnLoading.value = true;
    if (props.type === 0) {
      createProcess(params)
        .then((res: any) => {
          if (res.code === 8000000) {
            Message.success(t('project-setting.create-success'));
            emits('submit', true);
            cancel();
          }
          submitBtnLoading.value = false;
        })
        .catch((e) => {
          if (e) {
            submitBtnLoading.value = false;
          }
        });
    } else {
      updateProcess(params)
        .then((res: any) => {
          if (res.code === 8000000) {
            Message.success(t('project-setting.update-success'));
            emits('update', true);
            cancel();
          }
          submitBtnLoading.value = false;
        })
        .catch((e) => {
          if (e) {
            submitBtnLoading.value = false;
          }
        });
    }
    return '';
  };

  interface TeamObj {
    id?: string | number;
    name?: string;
  }

  const teamList = ref<TeamObj[]>([]);

  const usePrjStore = usePrjPermissionStore();
  const currentTeams = computed(() =>
    usePrjStore.teamList
      .filter((item) => item.role === 5)
      .map((item) => ({
        id: item.id,
        name: item.name,
      }))
  );
  // 查询列表数据
  const getTeamList = () => {
    teamList.value = [...currentTeams.value];
  };

  const init = () => {
    getTeamList();
    if (props.type === 1 || props.type === 2) {
      formData.value = {
        id: props.defaultProcess.teamId,
        name: props.defaultProcess.name,
        description: props.defaultProcess.describe,
      };
      nodeData.value = JSON.parse(props.defaultProcess.processUser) || [];
      
      // 计算已有节点中的最大key值，确保新增节点的key不会冲突,解决编辑新增节点时候的key冲突
      if (nodeData.value.length > 0) {
        const maxKey = Math.max(...nodeData.value.map(node => node.key || 0));
        nextNodeKey.value = maxKey + 1;
      } else {
        nextNodeKey.value = 1;
      }
    } else {
      formData.value = {
        id: '',
        name: '',
        description: '',
      };
      nodeData.value = [];
      // 新建模式下重置key计数器
      nextNodeKey.value = 1; 
    }
  };
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        init();
      }
    }
  );

  const nodeDelete = (key: number) => {
    nodeData.value = nodeData.value.filter((item: { key: number }) => {
      return item.key !== key;
    });
  };
  // const changeAudit = (key: number, type: string) => {
  // nodeData.value = nodeData.value.filter((item: { key: number }) => {
  //   return item.key !== key;
  // });
  //   nodeData.value[key].audit = type;
  // };
</script>

<style scoped lang="less">
  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    text-align: right !important;
  }
  :deep(.arco-form-item-content-flex) {
    max-width: 223px !important;
  }
  .nodes-display {
    min-height: 100px;
    //border: 1px solid red;
    padding-left: 50px;
    padding-right: 50px;
    .flexEnd {
      justify-content: flex-end !important;
    }
    .node-row {
      padding-top: 8px;
      //padding-bottom: 8px;
      margin-top: 8px;
      //border-top: 1px solid var(--color-border-2);
      height: 40px;
      position: relative;
      &:first-child {
        border-top: 0px;
      }
    }
    .node-item {
      display: flex;
      justify-content: left;
      align-items: center;
      align-content: center;
      position: relative;
      //border: 1px solid black;

      .node-num {
        height: 28px;
        width: 28px;
        border-radius: 14px;
        background-color: var(--color-fill-2);
        text-align: center;
        line-height: 28px;
      }
      .node-name {
        margin-left: 8px;
        max-width: calc(100% - 70px);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 8px;
      }
      .line {
        height: 0px;
        border-bottom: 1px solid var(--color-border-2);
        //width: 60px;
        flex: 1;
      }
    }
    .link-left {
      height: 48px;
      width: 40px;
      position: absolute;
      top: 14px;
      left: -44px;
      border: 1px solid var(--color-border-2);
      border-right: 0px;
    }
    .link-right {
      height: 48px;
      width: 2px;
      position: absolute;
      top: 14px;
      right: 0px;
      border: 1px solid var(--color-border-2);
      border-left: 0px;
    }
    .start-node {
      .node-num {
        background-color: rgb(var(--arcoblue-6));
        color: white;
      }
    }
  }
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 14px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  // :deep(.arco-form-item-label-col > .arco-form-item-label) {
  //   width: 70px;
  // }
  :deep(.arco-btn-size-medium) {
    padding: 0 !important;
  }
  :deep(.icon-delete) {
    margin-top: 0px !important;
  }
  .icon-delete {
    cursor: pointer;
    color: red;
    font-size: 20px;
    margin-top: 5px;
  }
</style>
