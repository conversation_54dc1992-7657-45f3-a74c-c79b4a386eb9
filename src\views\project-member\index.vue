<template>
  <div class="container">
    <commonTabs
      v-model="tabKey"
      :tabs="tabsData"
      @click-tab="changeTab"
    ></commonTabs>

    <div class="border-box">
      <memberManage v-if="tabKey === 'member-manage'" />
      <TeamManage v-else />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import commonTabs from '@/components/common-tabs/index.vue';
  import memberManage from './components/member-manage/index.vue';
  import TeamManage from '@/views/project-setting/team/index.vue';
  import { useI18n } from 'vue-i18n';
  import { Message } from '@arco-design/web-vue';

  const { t } = useI18n();

  const tabsData: any = ref([]);

  const tabKey = ref('member-manage');
  onMounted(() => {
    tabsData.value = [
      {
        label: t('member.management.title'),
        value: 'member-manage',
      },
      {
        label: t('project-setting.team-manage'),
        value: 'team-manage',
      },
    ];
  });
</script>

<script lang="ts">
  export default {
    name: 'ProjectMember',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 16px 20px;
    height: 100%;
  }
  .border-box {
    height: calc(100% - 70px);
    border: 1px solid #d9d9d9;
    border-radius: 8px;
  }
</style>
