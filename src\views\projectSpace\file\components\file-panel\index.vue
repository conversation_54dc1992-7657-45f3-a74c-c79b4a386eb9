<template>
  <a-layout class="project-file-panel">
    <a-layout-sider
      :width="280"
      :resize-directions="['right']"
      class="file-tree-wrap"
    >
      <FileTree
        ref="fileTree"
        :folder-data="allFolderData"
        @node-click="nodeClick"
        @refresh="treeRefresh"
        @handle-upload="handleUpload"
        @handle-download="handleDownload"
      ></FileTree>
    </a-layout-sider>
    <a-layout-content class="content-panel">
      <div class="content-header">
        <div class="header-buttons">
          <div class="header-title">
            <img src="@/assets/images/table-title.png" />
            <div class="text">
              {{ i18FolderName(currentFolder) }}
            </div>
            <Breadcrumb
              class="breadcrumb"
              @expend-folder="expendFolderNode"
              @init="init"
            />
          </div>
          <div class="filter-buttons">
            <FileFilter ref="fileFilterRef" />
          </div>
        </div>
        <a-divider />
      </div>

      <div style="padding: 12px 20px; display: flex">
        <header-buttons
          :disabled="
            ['SHARED', 'PUBLISHED'].includes(currentFolder.type) ||
            isTopFolder(currentFolder.id) ||
            (!!currentFolder.sysType && currentFolder.sysType !== 5)
          "
          @handle-upload="handleUpload"
        ></header-buttons>
        <buttons-batch
          style="margin-left: 12px"
          @handle-download="handleDownload"
          @expend-folder="expendFolderNode"
          @handle-move="handleMoveEvents"
        />
      </div>
      <div class="folder-table-wrap">
        <FolderTable
          ref="folderTable"
          @expend-folder="expendFolderNode"
          @handle-download="handleDownload"
          @handle-file-location="handleFileLocation"
        />
      </div>
    </a-layout-content>
  </a-layout>
  <UploadModal
    :visible="uploadModel.visible"
    :visible-type="uploadModel.type"
    :selected-folder="uploadModel.selectedFolder"
    @upload-single-success="singleFileSuccessCallback"
    @handle-cancel="uploadModel.visible = false"
    @upload-complete="finishUpLoad"
    @start-upload="startUpload"
  />
  <TransmitPanel
    v-model:visible="TransmitPanelVisible"
    :position="{
      top: 120,
      right: 60,
    }"
    :transmit-type="transmitType"
  />
  <ModalShareFile
    v-model:show="shareModal.show"
    :share-data="shareModal.shareData"
    :share-type="shareModal.shareType"
  />
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { computed, nextTick, provide, ref } from 'vue';
  import { FolderMessage, getChildFolderList } from '@/api/tree-folder';
  import TransmitPanel from '../transmit-panel/index.vue';

  import FileTree from './file-tree.vue';
  import FolderTable from './folder-table.vue';
  import ModalShareFile from '../modal-share-file.vue';

  import HeaderButtons from './buttons-header.vue';
  import Breadcrumb from './breadcrumb.vue';
  import ButtonsBatch from './buttons-batch.vue';

  import FileFilter from './filter-file.vue';
  import UploadModal from '../upload-modal.vue';

  import useFileStore from '@/store/modules/file/index';
  import { finishTaskParams } from '@/store/modules/upload-file/types';
  import { addMergaFile } from '@/api/upload-file';
  import { storeToRefs } from 'pinia';
  import { isTopFolder } from '@/views/projectSpace/file/utils';
  import useI18nHandleName from '../../hooks/backups';
  import { SearchState } from '@/store/modules/file/types';

  const route = useRoute();
  const fileStore = useFileStore();

  const currentFolder = computed(() => fileStore.currentFolder);
  const { allTreeData: allFolderData, currentIdPath } = storeToRefs(fileStore);

  const projectId = (route.params.projectId as string) || '';

  const getFolder = (type = 'WIP', parentId = '0') => {
    return getChildFolderList(projectId, '', type, parentId);
  };

  const uploadModel = ref({ visible: false, type: 0, selectedFolder: {} });

  const { i18FolderName } = useI18nHandleName();
  const TransmitPanelVisible = ref(false);

  const fileTree = ref();
  const transmitType = ref('upload');

  function expendFolderNode(record: FolderMessage) {
    fileTree.value.setNodeSelected(record);
  }

  function startUpload() {
    transmitType.value = 'upload';
    TransmitPanelVisible.value = true;
    uploadModel.value.visible = false;
  }

  const getWIPFolder = async () => {
    const res = await getFolder('WIP');
    if (res.status) {
      const list = res.data?.list || [];
      allFolderData.value[0].children = list;
    }
  };

  const getSharedFolder = async () => {
    const res = await getFolder('SHARED');
    if (res.status) {
      const list = res.data?.list || [];
      allFolderData.value[1].children = list;
    }
  };

  const getPublishedFolder = async () => {
    const res = await getFolder('PUBLISHED');
    if (res.status) {
      allFolderData.value[2].children = (res.data?.list as []) || [];
    }
  };

  const getCommunalSpaceFolder = async () => {
    const res = await getFolder('COMMUNALSPACE');
    if (res.status) {
      allFolderData.value[3].children = (res.data?.list as []) || [];
    }
  };

  async function getChildFolder(type: string, parentId?: string) {
    const res = await getFolder(type, parentId);
    if (res.status) {
      return res.data.list || [];
    } else {
      return [];
    }
  }
  const init = async () => {
    // 根据store中存储的idPath，回显文件夹层级
    if (currentIdPath.value) {
      const idList = currentIdPath.value.split('/');
      idList.shift();
      const type = idList[0];

      // 先异步加载每个路径的文件夹列表
      // 并发加载，减少等待时间
      const results = await Promise.all(
        idList.map(async (id) => {
          if (isTopFolder(id)) {
            return await getChildFolder(type);
          } else {
            return await getChildFolder(type, id);
          }
        })
      );
      let currentValue: FolderMessage | undefined;
      // 将文件夹列表放到对应的节点下
      results.reduce((arr, list, index) => {
        currentValue = arr.find((item) => item.id === idList[index]);
        if (currentValue) {
          currentValue.children = list;
          return currentValue.children;
        }
        return [];
      }, allFolderData.value);

      if (currentValue) {
        fileStore.setSelectedKeys([currentValue.id!]);
        fileStore.setExpandedKeys(idList);
        fileStore.setCurrentFolder(currentValue);
      } else {
        fileTree.value.setNodeSelected(allFolderData.value[0]);
      }
    } else {
      await Promise.all([
        getWIPFolder(),
        getSharedFolder(),
        getPublishedFolder(),
        getCommunalSpaceFolder(),
      ]);
      fileTree.value.setNodeSelected(allFolderData.value[0]);
    }
    // 更新tree组件的可以，重新渲染tree组件
    fileTree.value.updateTreeKey();
  };
  init();

  const nodeClick = async (data: any, callback: () => void) => {
    fileFilterRef.value.forceExitSearchStatus();
    const { nodeInfo } = data;

    if (isTopFolder(nodeInfo.id)) {
      const res = await getFolder(nodeInfo.id);
      if (res.status) {
        const list = res.data?.list || [];
        nodeInfo.children = list;
      }
    } else {
      const res = await getFolder(nodeInfo.type, nodeInfo.id);
      if (res.status) {
        nodeInfo.children = res?.data?.list || [];
      }
    }

    fileStore.setCurrentFolder(nodeInfo);
    await nextTick();
    callback();
  };

  function handleUpload(visibleType: number) {
    uploadModel.value = {
      type: visibleType,
      visible: true,
      selectedFolder: fileStore.currentFolder,
    };
  }
  function handleDownload() {
    transmitType.value = 'download';
    TransmitPanelVisible.value = true;
  }

  async function singleFileSuccessCallback(params: finishTaskParams) {
    await addMergaFile(params)
      .catch((err) => {
        // this.changeFileArrStatus(item, 1);
      })
      .then((res: any) => {
        // 事项中成功需要给出结果
        // this.mattersSaveList.push(res.data);
      })
      .finally(() => {});
  }
  async function finishUpLoad() {
    const res = await getFolder(
      currentFolder.value.type,
      currentFolder.value.id
    );
    if (res.status) {
      // const data = JSON.parse(JSON.stringify(currentFolder.value));

      currentFolder.value.children = res?.data?.list || [];
      // fileStore.setCurrentFolder(data);
    }
  }
  async function treeRefresh(current: any) {
    const res = await getFolder(current.type, current.id);
    if (res.status) {
      current.children = res?.data?.list || [];
    }
  }

  const shareModal = ref({ show: false, shareType: 'tree', shareData: {} });
  provide('shareModalData', shareModal);

  const folderTable = ref();
  function handleMoveEvents() {
    folderTable?.value.batchMoveHandle();
  }

  const serachStatus = ref<SearchState>('2');
  provide('searchStatus', serachStatus);
  const fileFilterRef = ref();
  // 本页面内目录跳转定位
  function handleFileLocation(isSearchModel = true) {
    init();
    if (isSearchModel) {
      fileFilterRef.value.forceExitSearchStatus();
    }
  }
</script>

<style scoped lang="less">
  :deep(.arco-resizebox-trigger-icon-wrapper) {
    background-color: #eee;
  }
  :deep(.arco-layout-sider) {
    min-width: 220px;
    max-width: 600px;
  }
  .project-file-panel {
    border: 1px solid #d9d9d9;
    height: 100%;
    border-radius: 8px;
    .file-tree-wrap {
      border-radius: 8px;
      padding-right: 12px !important;
      height: 100%;
      border-right: 1px solid #d9d9d9;
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
    }
    .content-panel {
      //border: 1px solid red;
      flex: 1;
      position: relative;
      overflow: hidden;
      .content-header {
        padding: 0 20px;
        .header-buttons {
          height: 64px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          overflow: hidden;

          .header-title {
            width: 100%;
            flex: 1;
            display: flex;
            overflow: hidden;
            img {
              width: 20px;
              height: 20px;
              margin-right: 8px;
              margin-top: 7px;
            }
            .text {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 18px;
              font-weight: bold;
              line-height: 32px;
              margin-right: 16px;
              max-width: 350px;
              color: #1d2129;
            }
          }
        }
      }

      .breadcrumb {
        margin-left: 20px;
      }
      .folder-table-wrap {
        height: calc(100% - 47px);
        overflow: hidden;
      }
    }
  }
</style>
