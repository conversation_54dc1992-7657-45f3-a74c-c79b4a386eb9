<template>
  <div class="card">
    <div class="body">
      <file-image
        v-if="versionDetail.name !== undefined"
        :file-name="versionDetail.name"
        :is-file="true"
        style="margin-right: 8px"
      />
      <div class="text" @click="modelView(versionDetail)">
        <div class="title">{{ versionDetail.name }}</div>
        <div class="detail"
          >{{ versionDetail.updateDate }} {{ versionDetail.updateName }}
          {{ $t('file-manage.uploaded') }}{{ versionDetail.name }}</div
        >
      </div>
    </div>
    <div class="foot">
      <a-tag size="small" color="arcoblue"
        >{{
          currentFile?.version === versionDetail.version
            ? $t('file-manage.current-version')
            : ''
        }}
        v{{ versionDetail.version }}</a-tag
      >
      <div class="buttons">
        <!--        <a-tooltip-->
        <!--          v-if="-->
        <!--            currentFile?.type === 'WIP' &&-->
        <!--            currentFile?.version !== versionDetail.version-->
        <!--          "-->
        <!--          :content="$t('file-manage.rollback')"-->
        <!--          position="br"-->
        <!--          @click="rollback(versionDetail.version || '')"-->
        <!--        >-->
        <!--          <icon-reply :size="16" class="svg-button" />-->
        <!--        </a-tooltip>-->
        <a-tooltip
          :content="$t('file-manage.download')"
          position="br"
          @click="handleSingleFile(versionDetail)"
        >
          <icon-download :size="16" class="svg-button" />
        </a-tooltip>
        <!--        <a-tooltip-->
        <!--          v-if="currentFile?.type === 'WIP'"-->
        <!--          :content="$t('file-manage.copy')"-->
        <!--          position="br"-->
        <!--          @click="versionCopy(versionDetail)"-->
        <!--        >-->
        <!--          <icon-copy :size="16" class="svg-button" />-->
        <!--        </a-tooltip>-->
      </div>
    </div>
  </div>
  <!-- 图片预览 -->
  <ImgViewer
    v-if="imgViewModal.visible"
    v-model:visible="imgViewModal.visible"
  ></ImgViewer>
</template>

<script lang="ts" setup>
  import { FileVersion } from '@/views/projectSpace/file/api';
  import { PropType } from 'vue';
  import { storeToRefs } from 'pinia';
  import FileImage from './image-file.vue';
  import { useRoute } from 'vue-router';

  import modelViewBim from '@/utils/common/view';
  import usePrjPermissionStore from '@/store/modules/project-permission';

  import ImgViewer from '@/components/imgView/index.vue';
  import useFileStore from '@/store/modules/file/index';
  import { FileMessage } from '@/api/tree-folder';
  import { handleSingleFile } from '@/views/projectSpace/file/hooks/events';

  defineProps({
    versionDetail: {
      type: Object as PropType<FileVersion>,
      required: true,
    },
    currentFile: {
      type: Object as PropType<FileMessage>,
      required: true,
    },
  });

  const projectStore = usePrjPermissionStore();
  const route = useRoute();
  const store = useFileStore();
  const { imgViewModal } = storeToRefs(store);

  const modelView = async (record: any) => {
    const needParams = {
      version: record.version,
    };
    modelViewBim(record, route.params.projectId as string, needParams);
  };
</script>

<style lang="less" scoped>
  .card {
    border-bottom: 1px solid var(--color-border);
    padding-bottom: 8px;
  }
  .foot {
    display: flex;
    justify-content: space-between;
    margin: 4px 0 0 4px;
  }
  .buttons {
    display: flex;
    align-items: flex-end;
  }
  svg + svg {
    margin-left: 10px;
  }
  .body {
    display: flex;
  }
  .text {
    margin-left: 12px;
    flex: 1;
    cursor: pointer;
  }
  .title {
    font-size: 14px;
    color: rgb(var(--primary-6));
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 236px;
  }
  .detail {
    font-size: 12px;
    color: var(--color-text-2);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 236px;
  }
  .body > img {
    width: 40px;
    height: 40px;
  }
  .svg-button {
    cursor: pointer;
  }
</style>
