<template>
  <template v-if="baseInfo?.aiRagId">
    <div ref="dialogContainer" class="dialog-container"> </div>
    <a-tooltip :content="t('knowledgenew.ai-clear-record')">
      <span class="clear-icon">
        <ClearIcon class="icon-hover-bg" @click="showAIPage(false)"
      /></span>
    </a-tooltip>
  </template>
  <div v-else class="empty-file">
    <EmptyFolder></EmptyFolder>
    <span>{{
      t(hasFile ? 'knowledgenew.ai-creating' : 'knowledgenew.ai-empty-content')
    }}</span>
  </div>
</template>

<script lang="ts" setup>
  import { defineEmits, defineProps, ref, watch, nextTick } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useI18n } from 'vue-i18n';
  import { useKnowledgeBaseStore2 } from '@/store';
  import useAIChat from '@/hooks/aiChat';
  import { getAgentRecordList } from '@/views/dashboard/api';
  import { QueryRecordParams } from '@/views/dashboard/types';
  import { ChatHistoryRecord } from '../../types';
  import ClearIcon from '@/assets/images/knowledge-base/clear-icon.svg';
  import EmptyFolder from '@/assets/images/knowledge-base/empty-folder.svg';

  const { t } = useI18n();

  const props = defineProps({
    refresh: {
      type: Boolean,
      default: false,
    },
  });

  const emits = defineEmits(['update:refresh']);
  const { genAIToken, initAIPage } = useAIChat();

  const knowledgeBaseStore2 = useKnowledgeBaseStore2();
  const { baseInfo, hasFile } = storeToRefs(knowledgeBaseStore2);

  // AI对话直接打开上一次的记录页面
  // 查询历史记录
  const initRecord = ref(false); // 是否查询完成
  const showRecord = ref(true); // 是否显示历史记录
  const recordList = ref<ChatHistoryRecord[]>([]);
  const queryAgentRecord = () => {
    const params: QueryRecordParams = {
      agentId: baseInfo.value?.aiRagId || '',
      pageNo: 1,
      pageSize: 1,
    };
    getAgentRecordList(params)
      .then((res) => {
        if (res.status) {
          recordList.value = res?.data?.list || [];
        }
      })
      .finally(() => {
        initRecord.value = true;
      });
  };

  const dialogContainer = ref<HTMLElement | null>(null);

  const showAIPage = async (openHistory: boolean) => {
    showRecord.value = openHistory;
    await genAIToken();

    // 定义路由跳转处理函数
    const handleRouter = async (router: any) => {
      if (!openHistory) {
        return;
      }
      if (!initRecord.value) {
        // 如果查询未完成，等待查询完成
        await new Promise((resolve) => {
          const check = () => {
            if (initRecord.value) {
              resolve(true);
            } else {
              setTimeout(check, 100);
            }
          };
          check();
        });
      }
      if (recordList.value.length) {
        router.push({
          path: `/xkassistant/${baseInfo.value?.aiRagId}/${recordList.value[0].chatSessionId}`,
          query: {
            placeholder: '您可以基于知识库的内容提问',
          },
        });
      }
    };

    nextTick(() => {
      const res = initAIPage(
        baseInfo.value?.aiRagId || '',
        'xkassistant',
        dialogContainer.value,
        handleRouter,
        '您可以基于知识库的内容提问'
        // t('knowledgenew.ai-question-prompt')
      );
      if (!res.status) {
        console.error(res.message);
      }
      emits('update:refresh', false);
    });
  };

  // 是否是初次加载：初次加载后，后续根据refresh刷新页面
  const init = ref(true);

  watch(
    () => baseInfo.value,
    () => {
      if (init.value && baseInfo.value?.aiRagId) {
        nextTick(() => {
          showAIPage(true);
          init.value = false;
        });
        queryAgentRecord();
      }
    },
    {
      deep: true,
      immediate: true,
    }
  );

  watch(
    () => props.refresh,
    (newVal) => {
      if (newVal && baseInfo.value?.aiRagId) {
        if (
          (recordList.value.length > 0 && !showRecord.value) ||
          recordList.value.length === 0
        ) {
          nextTick(() => {
            showAIPage(showRecord.value);
          });
        }
      }
    }
  );
</script>

<style scoped lang="less">
  .dialog-container {
    height: 100%;
    width: 100%;
  }

  .clear-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;
  }

  .empty-file {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 280px 0 0;

    span {
      margin-top: 16px;
      font-size: 16px;
      color: #4e5969;
      line-height: 24px;
    }
  }

  .icon-hover-bg:hover {
    background: #e5e6eb;
    border-radius: 2px;
  }

  // AI对话样式优化
  :deep(.chat-input-content) {
    .content-edit-box {
      border: none !important;
    }
  }

  :deep(.tag-button-box .default-css) {
    border-radius: 26px !important;
  }

  :deep(.arco-list-bordered) {
    border: none;
  }

  :deep(.reanoning-content li) {
    margin-left: 16px;
  }

  :deep(.main-begin .main-view-main) {
    height: 0;
  }

  // 去掉上传附件
  :deep(.foot-box-action) {
    .arco-upload {
      display: none;
    }
  }
</style>
