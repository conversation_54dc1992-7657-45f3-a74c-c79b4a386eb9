import { defineStore } from 'pinia';
import { KnowledgeBaseState, PathNode, KnowledgeBaseUsage } from './types';
import { set } from 'nprogress';

const useKnowledgeBaseStore2 = defineStore('knowledgeBase2', {
  state: (): KnowledgeBaseState => {
    return {
      baseInfo: null, // 当前知识库信息，在用
      // folderVOId: '', // 当前知识库的根文件夹id
      // baseRootId: '', // 选中的知识库id
      // baseRagId: '', // 选择的知识库的agentID
      isEditing: false, // 知识库是否正在添加/重命名，在用
      selectedKeys: [], // 选中的树节点id(单选)，在用
      baseLeafId: '', // 选中的共享知识库子节点id
      /* 从网盘导入的面包屑begin */
      folderId: '', // 选中的文件夹的id（知识库有一个默认的文件夹）
      history: [], // 打开文件夹的历史路径
      currentIndex: -1, // 面包屑当前路径索引
      /* 从网盘导入的面包屑end */
      isBaseEmpty: true, // 知识库是否为空（控制空白页显隐）
      hasFile: false, // 知识库是否存在文件（默认为空，控制对话框部分页面提示），在用
      fileCnt: 0, // 文件数量
      usedStorage: 0, // 已使用存储
      remainingStorage: 0, // 剩余存储
      isSearch: false, // 是否是搜索状态
      fileId: '', // 搜索文件id
      needRefresh: false, // 是否需要重新查询知识库使用情况
    };
  },
  actions: {
    setBaseInfo(value: any) {
      this.baseInfo = value;
    },
    // setRootId(value: string) {
    //   this.baseRootId = value;
    // },
    setIsBaseEmpty(value: boolean) {
      this.isBaseEmpty = value;
    },
    setHasFile(value: boolean) {
      this.hasFile = value;
    },
    // setRagId(value: string) {
    //   this.baseRagId = value;
    // },
    setLeafId(value: string) {
      this.baseLeafId = value;
    },
    setIsEditting(value: boolean) {
      this.isEditing = value;
    },
    // 通过导航改变当前文件夹（不能改变history）
    changePath(nFolderId: string, nCurrentIndex: number) {
      this.folderId = nFolderId;
      this.currentIndex = nCurrentIndex;
    },
    // 通过导航改变当前文件夹（改变history版本）
    changePath2(nFolderId: string, nCurrentIndex: number) {
      this.folderId = nFolderId;
      this.currentIndex = nCurrentIndex;
      this.history.splice(nCurrentIndex + 1);
    },
    // 打开文件夹，设置history和folderId
    openFolder(data: PathNode) {
      this.folderId = data.id;

      this.history.splice(this.currentIndex + 1); // 清除前进记录
      this.history.push({
        id: data.id,
        name: data.name,
      });
      this.currentIndex++;
    },
    initData() {
      this.baseInfo = null;
      this.isEditing = false;
      this.selectedKeys = [];
      this.hasFile = false;
    },
    // 初始化面包屑
    initHistory() {
      this.folderId = '';
      this.history = [];
      this.currentIndex = -1;
    },
    setKnowledgeBaseUsage(data: KnowledgeBaseUsage) {
      this.fileCnt = data.fileCnt;
      this.usedStorage = data.usedStorage;
      this.remainingStorage = data.remainingStorage;

      if (this.fileCnt > 0) {
        this.hasFile = true;
      } else {
        this.hasFile = false;
      }
      this.needRefresh = false;
    },
    setSearchInfo(isSearch: boolean, folderId?: string, fileId?: string) {
      this.isSearch = isSearch;
      if (isSearch) {
        if (folderId) {
          this.folderId = folderId;
        }
        if (fileId) {
          this.fileId = fileId;
        }
      }
    },
    setSearchFullPath(value: Array<PathNode>) {
      this.history = value;
      this.currentIndex = value.length - 1;
    },
    setNeedRefresh(value: boolean) {
      this.needRefresh = value;
    },
    // setFolderVOId(id: string) {
    //   this.folderVOId = id;
    // },
  },
});

export default useKnowledgeBaseStore2;
