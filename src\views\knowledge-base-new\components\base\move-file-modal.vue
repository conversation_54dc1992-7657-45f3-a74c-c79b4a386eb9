<template>
  <div class="move-file-wrap">
    <a-modal
      :visible="visible"
      width="700px"
      title-align="start"
      :render-to-body="false"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <template #title> {{ $t('knowledgenew.move-to') }} </template>
      <div style="height: 60vh" class="modal-content">
        <div class="header">
          <span
            v-if="breadcrumbList.length <= 1"
            style="font-weight: bold; font-size: 15px"
            >{{ $t('knowledgenew.all-file') }}</span
          >
          <a-breadcrumb v-else :max-count="3" style="cursor: pointer">
            <a-breadcrumb-item
              v-for="(item, index) in breadcrumbList"
              :key="item.id"
              @click="breadcrumbChange(item, index)"
              >{{ item.name }}</a-breadcrumb-item
            >
          </a-breadcrumb>
        </div>
        <div class="file-list-content">
          <a-list header="List title" :bordered="false" :data="folderList">
            <template #item="{ item }">
              <a-list-item
                v-if="!item.isNew"
                :key="item.id"
                style="cursor: pointer"
                @click="changeFolder(item)"
              >
                <a-list-item-meta :title="item.name">
                  <template #avatar>
                    <a-avatar shape="square" :image-url="folderIcon" :size="24">
                    </a-avatar>
                  </template>
                </a-list-item-meta>
              </a-list-item>
              <a-list-item v-if="item.isNew" :key="item.id">
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar shape="square" :image-url="folderIcon" :size="24">
                    </a-avatar>
                  </template>
                  <template #title>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        align-content: center;
                      "
                    >
                      <a-input ref="newFolderRef" v-model="item.name"></a-input>
                      <icon-check
                        style="margin-left: 8px; cursor: pointer"
                        size="18"
                        @click="folderBlur(item)"
                      />
                      <icon-close
                        style="margin-left: 8px; cursor: pointer"
                        size="18"
                        @click="cancelAdd"
                      />
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
      <template #footer>
        <div class="modal-footer">
          <a-button
            style="float: left"
            type="primary"
            shape="round"
            :disabled="folderList.find((e) => e.isNew)"
            @click="addFolder"
            >{{ $t('knowledgenew.add-folder') }}</a-button
          >
          <a-button style="margin-right: 12px" @click="handleCancel">{{
            $t('knowledgenew.cancel')
          }}</a-button>
          <a-button type="primary" @click="handleOk">{{
            $t('knowledgenew.move-to-here')
          }}</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { useI18n } from 'vue-i18n';
  import { Message } from '@arco-design/web-vue';
  import {
    ref,
    defineProps,
    defineEmits,
    watch,
    computed,
    toRaw,
    nextTick,
  } from 'vue';
  import { useKnowledgeBaseStore2 } from '@/store';
  import { getFolderList, batchMoveFile, createFolder } from '../../api';
  import folderIcon from '@/assets/images/knowledge-base/folder.svg?url';

  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    files: {
      type: Array,
      default() {
        return [];
      },
    },
  });
  const emits = defineEmits(['update:visible', 'moveFileSuccess', 'refresh']);
  const knowledgeStore2 = useKnowledgeBaseStore2();
  const folderV0Id = computed(() => {
    return knowledgeStore2.baseInfo?.folderVO?.id;
  });
  const currentFolderId = ref(folderV0Id.value || '');
  const folderList: any = ref([]);
  const getFolderListHandle = () => {
    const params = {
      folderId: currentFolderId.value,
      fullTree: false,
    };
    getFolderList(params).then((res) => {
      folderList.value = res.data?.children || [];
    });
  };
  const breadcrumbList: any = ref([]);
  const breadcrumbChange = (item: any, index: number) => {
    breadcrumbList.value = breadcrumbList.value.slice(0, index + 1);
    currentFolderId.value = item.id;
    getFolderListHandle();
  };

  const changeFolder = (item: any) => {
    breadcrumbList.value.push({
      id: item.id,
      name: item.name,
    });
    currentFolderId.value = item.id;
    getFolderListHandle();
  };
  const handleCancel = () => {
    folderList.value = [];
    currentFolderId.value = '';
    emits('update:visible', false);
  };
  const handleOk = () => {
    const files = toRaw(props.files);
    const fileIds = files
      ?.filter((e: any) => {
        return e.type !== 'folder';
      })
      ?.map((e: any) => e.id);
    const folderIds = files
      ?.filter((e: any) => {
        return e.type === 'folder';
      })
      ?.map((e: any) => e.id);
    const params: any = {
      sourceParentId: files[0]?.parentId,
      targetParentId: currentFolderId.value,
    };
    if (fileIds.length) {
      params.fileIds = fileIds;
    }
    if (folderIds.length) {
      params.folderIds = folderIds;
    }
    if (params.sourceParentId === params.targetParentId) {
      Message.warning(t('knowledgenew.not-move-same-folder'));
      return;
    }
    batchMoveFile(params).then((res) => {
      if (res.status) {
        Message.success(t('knowledgenew.move-success'));
        emits('moveFileSuccess');
        handleCancel();
      }
    });
  };

  const newFolderRef: any = ref(null);
  const addFolder = () => {
    const list = toRaw(folderList.value);
    list.unshift({
      id: new Date().getTime(),
      name: t('knowledgenew.move-new-folder'),
      isNew: true,
    });
    folderList.value = [...list];
    nextTick(() => {
      newFolderRef.value?.focus();
    });
  };
  const folderBlur = (item: any) => {
    const params = {
      name: item.name,
      parentId: currentFolderId.value,
    };
    if (!params.name) return;
    createFolder(params).then((res) => {
      console.log('resL: ', res.data);
      if (res.status) {
        Message.success(t('knowledgenew.create-success'));
        getFolderListHandle();
        emits('refresh');
      }
    });
  };
  const cancelAdd = () => {
    const list = toRaw(folderList.value);
    folderList.value = list.slice(1, list?.length) || [];
  };

  const init = () => {
    currentFolderId.value = folderV0Id.value || '';
    breadcrumbList.value = [
      {
        id: currentFolderId.value,
        name: t('knowledgenew.all-file'),
      },
    ];
    getFolderListHandle();
  };
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        init();
      }
    }
  );
</script>

<style scoped lang="less">
  .move-file-wrap {
    :deep(.arco-modal-body) {
      padding: 0px !important;
    }
  }
  .modal-content {
    .header {
      height: 50px;
      background-color: #ececec;
      padding: 13px 20px;
    }
    :deep(.arco-avatar) {
      background-color: white !important;
    }
  }
</style>
