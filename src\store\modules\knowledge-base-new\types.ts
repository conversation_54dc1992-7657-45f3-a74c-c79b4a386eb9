import { FileAndFolderMessage } from '@/api/tree-folder';
import { FolderMessage } from '@store/modules/knowledge-base-new';

export interface PathNode {
  id: string; // 文件夹id
  name: string; // 名称
}

export interface KnowledgeBaseNewState {
  personal: {
    fileList: (FileAndFolderMessage & { selected: boolean })[];
    folderList: (FileAndFolderMessage & { selected: boolean })[];
    personalType: string;
    projectId: string; // 个人网盘初始化的项目id
    currentFolder: FolderMessage; // 设置默认值是personal
    breadcrumb: PathNode[]; // 面包屑导航默认个人文件
    tableLoading: boolean; // 表格加载状态
    baseFolder: FolderMessage;
  };
  project: {
    tableData: [];
    currentFolder: FolderMessage; // 设置默认值是project
    breadcrumb: PathNode[]; // 面包屑导航默认项目文件
    defaultTreeData: FileAndFolderMessage[]; // 默认的文件夹
    tableLoading: boolean; // 表格加载状态
    folderChildrenFolders: FolderMessage[];
    folderChildrenFiles: FileAndFolderMessage[];
    hiddenSlot: number;
  };
  referenceModal: {
    referenceModalVisible: boolean; // 引用模态框的可见性
    currentProjectId: string; // 当前弹框选择的项目ID
    currentProject: Record<string, any>; // 当前弹框选择的项目数据
    allTreeData: FileAndFolderMessage[]; // 所有树形数据
    currentFolder: FolderMessage; // 当前文件夹
    breadcrumbList: PathNode[]; // 面包屑导航列表
    selectedKeys: string[]; // 选中的树形节点
    expandedKeys?: string[]; // 展开的树形节点
    tableData: FileAndFolderMessage[]; // 表格数据
    tableLoading: boolean; // 表格加载状态
    selectedTableRowkeys: string[]; // 选中的表格行键
    defaultTreeData: FileAndFolderMessage[]; // 默认的树形数据
  };
  activeTabKey: number; // 默认选中个人空间,
  count: number; // 用于记录 onMounted 执行次数
}
