<template>
  <div class="check-wrap">
    <div class="search-bar">
      <a-row>
        <a-col :span="20">
          <a-form v-model="searchParams">
            <a-form-item label-col-flex="60px" :label="$t('check.name')">
              <a-input
                v-model="searchParams.name"
                :placeholder="$t('check.please-enter')"
                style="width: 196px"
                @keydown.enter="getList"
              ></a-input>
            </a-form-item>
          </a-form>
        </a-col>
        <a-col :span="4" style="text-align: right">
          <a-space :size="8">
            <a-button type="primary" @click="getList">{{
              $t('check.search')
            }}</a-button>
            <a-button type="outline" @click="reset">{{
              $t('check.clear')
            }}</a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>
    <div class="content">
      <div class="content-title">
        <div>
          <img
            src="@/assets/images/dashboard/<EMAIL>"
            class="title-img"
            alt=""
          />
          <span class="title-text">{{ $t('check.check') }}</span>
        </div>
        <div class="btns">
          <a-button @click="addProcess">{{
            $t('check.add-process-template')
          }}</a-button>
          <a-button
            type="primary"
            style="margin-left: 6px"
            @click="createDialogVisible = true"
            >{{ $t('check.add-review') }}</a-button
          >
        </div>
      </div>
      <div class="table-wrap">
        <a-table
          :data="checkList"
          stripe
          :bordered="false"
          :scroll="{ x: '100%', y: '100%' }"
          :pagination="pageConfig"
          @page-change="pageChange"
          @page-size-change="pageSizeChange"
        >
          <template #columns>
            <a-table-column
              :title="$t('check.number')"
              align="center"
              :width="60"
            >
              <template #cell="{ rowIndex }">
                {{
                  (pageConfig.pageSize ? pageConfig.pageSize : 10) *
                    ((pageConfig.current ? pageConfig.current : 1) - 1) +
                  (rowIndex + 1)
                }}
              </template>
            </a-table-column>
            <a-table-column :title="$t('check.name')">
              <template #cell="{ record }">
                <span class="name" @click="showProcess(record)">{{
                  record.formName
                }}</span>
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('check.task-name')"
              data-index="taskName"
              align="center"
            ></a-table-column>
            <a-table-column
              :title="$t('check.task-status')"
              data-index="processState"
              align="center"
            >
              <template #cell="{ record }">
                <div class="">
                  <a-tag :color="TaskStatusColor[record.taskState]">
                    {{ TaskStateMap[record.taskState] || '' }}</a-tag
                  >
                </div>
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('check.process-template')"
              data-index="processName"
              align="center"
            >
              <template #cell="{ record }">
                {{ record?.formExtObj?.processName || '' }}
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('check.process-status')"
              data-index="processState"
              align="center"
            >
              <template #cell="{ record }">
                <div class="">
                  <a-tag :color="ProcessStatusColor[record.processState]">
                    {{ ProcessStateMap[record.processState] || '' }}</a-tag
                  >
                </div>
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('check.initiator')"
              data-index="submiter"
              align="center"
            >
              <template #cell="{ record }">
                {{ record?.formExtObj?.submiter || '' }}
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('check.creation-time')"
              data-index="createDate"
              align="center"
            ></a-table-column>
            <a-table-column
              :title="$t('check.file-count')"
              data-index="fileCount"
              align="center"
            >
              <template #cell="{ record }">
                {{ record?.formExtObj?.fileNumber || '' }}
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('check.operation')"
              data-index="fileCount"
              align="center"
            >
              <template #cell="{ record }">
                <a-button
                  v-if="isNeedCheck(record)"
                  type="text"
                  size="small"
                  @click="checkProcess(record)"
                  >{{ $t('check.review') }}</a-button
                >
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </div>
    <div class="components">
      <CreateCheck
        v-model:visible="createDialogVisible"
        @submit="getList"
      ></CreateCheck>
      <CreateProcess
        v-model:visible="createProcessDialogVisible"
      ></CreateProcess>
      <ProcessDetail
        v-model:visible="checkProcessVisible"
        :task-detail="currentProcess"
        :show-type="processDialogType"
        :form-config="FormConfig.review"
        @agreed="getList"
        @refused="getList"
        type="issue"
      ></ProcessDetail>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref } from 'vue';
  import { PaginationProps } from '@arco-design/web-vue';
  import CreateProcess from '@/views/project-setting/process/create-process.vue';
  import ProcessDetail from '@/components/process-detail/index.vue';
  import { getProcessList } from '@/api/process';
  import {
    FormKeys,
    FormConfig,
    TaskStatusColor,
    ProcessStatusColor,
    TaskStateMap,
    ProcessStateMap,
  } from '@/directionary/process';
  import { useUserStore } from '@/store';
  import { useRoute, useRouter } from 'vue-router';
  import CreateCheck from './components/create-check/create-check.vue';

  const userStore = useUserStore();
  const user = computed(() => {
    return userStore.userInfo;
  });

  const route = useRoute();
  const router = useRouter();
  // const globalModeStore = useGlobalModeStore();
  // const globalMode = computed(() => {
  //   return globalModeStore.getGlobalMode;
  // });

  const searchParams = reactive({
    name: '',
  });

  const checkList = ref<object[]>([]);
  const pageConfig: PaginationProps = reactive({
    showTotal: true,
    showMore: false,
    showJumper: true,
    showPageSize: true,
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    total: 100,
  });

  const getList = () => {
    // const params = {
    //   pageNo: pageConfig.current || 1,
    //   pageSize: pageConfig.pageSize || 10,
    //   name: searchParams.name || '',
    //   projectId: route.params.projectId,
    // };
    const params = {
      formKey: FormKeys.review,
      formName: searchParams.name || '',
      column: 'createDate',
      customId: route.params.projectId,
      page: pageConfig.current || 1,
      size: pageConfig.pageSize || 20,
    };

    getProcessList(params)
      .then((res: any) => {
        if (res.code === 8000000) {
          const data = res.data.list || [];
          if (data.length) {
            data.forEach((e: any) => {
              if (e.formExt) {
                e.formExtObj = JSON.parse(e.formExt) || {};
              }
            });
          }
          checkList.value = data;
          pageConfig.total = res.data.total || checkList.value.length || 0;
        }
      })
      .catch((e) => {
        if (e) {
          checkList.value = [];
        }
      });
  };
  const addProcess = () => {
    // dialogType.value = 0;
    // currentProcess.value = {};
    // createDialogVisible.value = true;
    // const path = `/bpmn-create`;
    // const { projectId, teamId, describe, name } = row;
    router.push({
      name: 'bpmnCreate',
      query: {
        projectId: route?.params?.projectId,
        type: 'add',
      },
    });
  };
  const reset = () => {
    searchParams.name = '';
    getList();
  };
  const pageSizeChange = (size: number): void => {
    pageConfig.pageSize = size;
    getList();
  };
  const pageChange = (current: number): void => {
    pageConfig.current = current;
    getList();
  };

  const createDialogVisible = ref(false);
  const createProcessDialogVisible = ref(false);
  const checkProcessVisible = ref(false);
  const currentProcess = ref(null);
  const processDialogType = ref(0);
  const checkProcess = (row: any) => {
    processDialogType.value = 0;
    currentProcess.value = row;
    checkProcessVisible.value = true;
  };
  const isNeedCheck = (row: any) => {
    let result = false;
    if (row.taskAsignee === user.value.username && row.taskState === '0') {
      result = true;
    }
    return result;
  };
  const showProcess = (row: any) => {
    processDialogType.value = 1;
    currentProcess.value = row;
    checkProcessVisible.value = true;
  };
  onMounted(() => {
    getList();
  });
</script>

<style scoped lang="less">
  ::v-deep .arco-btn-size-small {
    padding: 0 6px;
  }
  .check-wrap {
    padding-left: 43px;
    padding-right: 43px;
  }
  .search-bar {
    padding-top: 20px;
    // padding-bottom: 30px;
    border-bottom: 1px solid var(--color-border);
  }
  .search-title {
    display: inline-block;
    width: 60px;
    height: 22px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN, serif;
    font-weight: 600;
    color: #000000;
    line-height: 22px;
  }
  .search-bar {
    margin-top: 8px;
  }
  .content {
    margin-top: 24px;
    .content-title {
      margin-top: 16px;
      //height: 25px;
      position: relative;
      //border: 1px solid red;
      .btns {
        position: absolute;
        right: 0px;
        top: 0px;
      }
      .title-img {
        height: 20px;
        width: 20px;
      }
      .title-text {
        display: inline-block;
        margin-left: 8px;
        font-size: 18px;
        font-weight: 600;
        line-height: 21px;
        position: absolute;
        left: 20px;
        top: 0px;
      }
    }
    .table-wrap {
      margin-top: 18px;
      height: calc(100vh - 250px);
    }
  }
  .name {
    color: rgb(var(--arcoblue-6));
    cursor: pointer;
  }
</style>
