<template>
  <a-layout class="project-file-panel">
    <a-layout-content class="content-panel">
      <div class="content-header">
        <div class="header-buttons">
          <div class="header-title">
            <img src="@/assets/images/table-title.png" />
            <div class="text">
              {{ currentAttach.title || $t('file-manage.attatchments') }}
            </div>
            <Breadcrumb class="breadcrumb" @handle-click="handleBreadClick" />
          </div>
        </div>
      </div>

      <a-space fill wrap style="padding: 0 20px 12px 20px; gap: 0 20px">
        <a-input-search
          v-model="searchForm.keyName"
          :placeholder="$t('file-manage.enter-name')"
          @search="fetchAttachList"
          @keydown.enter="fetchAttachList"
        />
        <a-space>
          <div>{{ $t('file-manage.attatchment-type') }}</div>
          <a-checkbox-group
            v-model="searchForm.switchTypeView"
            @change="handleSwitchTypeChange"
          >
            <a-checkbox
              :value="item.value"
              :disabled="item.disabled"
              v-for="item in typeOptions"
              >{{ item.label }}</a-checkbox
            >
          </a-checkbox-group>
        </a-space>
        <a-space>
          <div>{{ $t('file-manage.arrange-order-date') }}</div>
          <a-range-picker
            v-model="searchDateList"
            value-format="YYYY/MM/DD"
            @change="handleDateChange"
            style="width: 254px"
          />
        </a-space>
        <a-space>
          <div>{{ $t('file-manage.team') }}</div>
          <a-select
            allow-clear
            :placeholder="$t('file-manage.select')"
            v-model="searchForm.teamId"
            @change="fetchAttachList"
            style="width: 250px"
            :options="teamList"
          >
          </a-select>
        </a-space>
        <!-- <a-divider direction="vertical" /> -->
        <a-button @click="reset">{{ $t('file-manage.reset-serach') }}</a-button>
      </a-space>
      <div style="padding: 0 20px">
        <a-divider />
      </div>

      <a-space fill style="padding: 12px 20px">
        <a-button
          type="primary"
          :disabled="!selectedTableRowkeys.length"
          @click="batchMoveHandle"
        >
          <template #icon> <icon-save /> </template>
          {{ $t('file-manage.batch-save') }}</a-button
        >
      </a-space>
      <div class="folder-table-wrap">
        <FolderTable ref="folderTable" @handle-download="handleDownload" />
      </div>
    </a-layout-content>
  </a-layout>
  <TransmitPanel
    v-model:visible="TransmitPanelVisible"
    :position="{
      top: 120,
      right: 60,
    }"
    :transmit-type="transmitType"
  />
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { computed, provide, ref } from 'vue';
  import TransmitPanel from '../transmit-panel/index.vue';

  import FolderTable from './folder-table.vue';

  import Breadcrumb from './breadcrumb.vue';
  import useFileStore from '@/store/modules/file/index';
  import { AttachParams } from '../../api';

  import { storeToRefs } from 'pinia';
  import { usePrjPermissionStore } from '@/store';
  import { useI18n } from 'vue-i18n';

  const route = useRoute();
  const fileStore = useFileStore();

  const projectId = route.params.projectId as string;
  const { t } = useI18n();

  function initSearchForm() {
    return {
      pageNo: 1,
      pageSize: 10,
      switchTypeView: ['item', 'meeting'],
      teamId: '',
      projectId: projectId,
      startTime: '',
      endTime: '',
    };
  }
  const searchForm = ref<AttachParams>(initSearchForm());
  provide('searchForm', searchForm);

  const currentAttach = computed(() => fileStore.currentAttach);

  const { selectedTableRowkeys } = storeToRefs(fileStore);

  const typeOptions = ref([
    { value: 'item', label: t('file-manage.item'), disabled: false },
    { value: 'meeting', label: t('file-manage.meeting'), disabled: false },
  ]);
  function handleSwitchTypeChange(vals: string[]) {
    if (vals.length === 1) {
      typeOptions.value.forEach((item) => {
        if (item.value === vals[0]) {
          item.disabled = true;
        } else {
          item.disabled = false;
        }
      });
    }
    if (vals.length === 2) {
      typeOptions.value.forEach((item) => {
        item.disabled = false;
      });
    }
    fetchAttachList();
  }

  const searchDateList = ref<string[] | undefined>([]);
  function handleDateChange(dateList: string[]) {
    if (dateList && dateList.length === 2) {
      const [startDate, endDate] = dateList;
      searchForm.value.startTime = startDate;
      searchForm.value.endTime = endDate;
    } else {
      searchForm.value.startTime = '';
      searchForm.value.endTime = '';
    }
    fetchAttachList();
  }

  const usePrjStore = usePrjPermissionStore();
  const teamList = computed(() =>
    usePrjStore.teamList.map((item) => ({
      value: item.id,
      label: item.name,
    }))
  );

  function reset() {
    searchDateList.value = undefined;
    searchForm.value = initSearchForm();
    fetchAttachList();
  }

  const folderTable = ref();

  async function fetchAttachList() {
    //获取表格数据
    searchForm.value.pageNo = 1;
    folderTable.value.refreshTableData();
  }

  const moveModal = ref(false);
  provide('moveVisible', moveModal);

  function handleBreadClick(pathList: { name: string; id: string }[]) {
    folderTable.value.breadcrumbChange(pathList);
  }

  function batchMoveHandle() {
    folderTable.value.batchMoveHandle();
  }

  const TransmitPanelVisible = ref(false);
  const transmitType = ref('upload');

  function handleDownload() {
    transmitType.value = 'download';
    TransmitPanelVisible.value = true;
  }
</script>

<style scoped lang="less">
  :deep(.arco-resizebox-trigger-icon-wrapper) {
    background-color: #eee;
  }
  :deep(.arco-layout-sider) {
    min-width: 150px;
    max-width: 600px;
  }
  .project-file-panel {
    border: 1px solid #d9d9d9;
    height: 100%;
    border-radius: 8px;
    .file-tree-wrap {
      border-radius: 8px;
      height: 100%;
      border-right: 1px solid #d9d9d9;
    }
    .content-panel {
      //border: 1px solid red;
      flex: 1;
      position: relative;
      overflow: hidden;
      .content-header {
        padding: 0 20px;
        .header-buttons {
          height: 64px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          overflow: hidden;

          .header-title {
            width: 100%;
            flex: 1;
            display: flex;
            overflow: hidden;
            img {
              width: 20px;
              height: 20px;
              margin-right: 8px;
              margin-top: 7px;
            }
            .text {
              color: #1d2129;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 350px;
              font-size: 18px;
              font-weight: bold;
              line-height: 32px;
              margin-right: 16px;
            }
          }
        }
      }

      .breadcrumb {
        margin-left: 20px;
      }
      .folder-table-wrap {
        height: calc(100% - 47px);
        overflow: hidden;
      }
    }
  }
</style>
