<template>
  <div class="pageContainer">
    <commonTabs />
    <div class="container">
      <a-row>
        <a-col :span="4">
          <div class="containerLeft">
            <div class="content-title">
              <TableTitle :title="$t('dictionary-config.configuration-item')" />
            </div>
            <div class="leftList">
              <div
                v-for="(item, index) in listData"
                :key="index"
                :class="active === index ? 'active' : ''"
                @click="configClick(item, index)"
              >
                <span style="display: flex; align-items: center; gap: 8px">
                  <component
                    :is="iconComponents[iconList[index % iconList.length]]"
                  />
                  {{ item.name }}</span
                >
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="20">
          <div class="containerRight">
            <div class="content">
              <div
                class="content-title"
                style="padding-bottom: 20px; border-bottom: 1px solid #d9d9d9"
              >
                <TableTitle
                  :title="tableTitle || $t('dictionary-config.major')"
                />
              </div>
              <div class="btns" style="padding-top: 20px">
                <a-button type="primary" @click="addHandle">
                  <template #icon> <icon-plus /> </template>
                  <template #default>
                    {{ $t('dictionary-config.add') }}
                  </template>
                </a-button>
              </div>
              <div class="table-wrap">
                <a-table
                  :loading="loading"
                  row-key="id"
                  :scroll="{ x: '100%', y: 'calc(100vh - 325px)' }"
                  :columns="columns"
                  :data="tableData"
                >
                  <template #empty>
                    <div class="empty-wrapper">
                      <img
                        src="@/assets/images/schedule/schedule-bg.png"
                        alt=""
                      />
                      <div>{{ $t('dictionary-config.no-data-yet') }}</div>
                    </div>
                  </template>

                  <template #action="{ record }">
                    <div class="btn-spacing">
                      <span class="edit-btn" @click="editHandle(record)">
                        {{ $t('dictionary-config.edit') }}
                      </span>
                      <a-popconfirm
                        :content="
                          $t('dictionary-config.whether-remove-configuration')
                        "
                        position="left"
                        @ok="delHandle(record)"
                      >
                        <span class="delete-btn">
                          {{ $t('dictionary-config.delete') }}
                        </span>
                      </a-popconfirm>
                    </div>
                  </template>
                </a-table>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>

  <add-edit-config
    v-if="addVisible"
    v-model:visible="addVisible"
    :type="actionType"
    :data="configData"
    :config-id="menuConfig.id"
    @refresh="getList"
  ></add-edit-config>
</template>

<script lang="ts" setup>
  import {
    onMounted,
    reactive,
    ref,
    computed,
    defineAsyncComponent,
  } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import addEditConfig from './components/addEditConfig.vue';
  import { deleteKeyValue, getAllKeyValue } from './api';
  import TableTitle from '@/components/table-title/index.vue';
  import commonTabs from '@/components/common-tabs/index.vue';
  import Major from '@/assets/images/standard/hammer-line.svg';
  import Category from '@/assets/images/standard/shapes-line.svg';
  import Precision from '@/assets/images/standard/equalizer-line.svg';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const iconList = ['Major', 'Category', 'Precision'];
  const iconComponents = {
    Major: defineAsyncComponent(() => Promise.resolve(Major)),
    Category: defineAsyncComponent(() => Promise.resolve(Category)),
    Precision: defineAsyncComponent(() => Promise.resolve(Precision)),
  };

  // 获取场景启用数据

  const configData: any = ref({});

  const loading: any = ref(false);

  const active: any = ref(0);

  const listData: any = computed(() => [
    {
      id: '1922530508708454402',
      code: 'stdField',
      name: t('dictionary-config.major'),
    },
    {
      id: '1922529320684429314',
      code: 'stdType',
      name: t('dictionary-config.information-category'),
    },
    {
      id: '1922530533261910018',
      code: 'stdPrecision',
      name: t('dictionary-config.fineness'),
    },
  ]);

  const addVisible: any = ref(false); // 新增弹窗
  const actionType: any = ref('add'); // 新增类型

  const tableTitle: any = ref('');

  // 配置项选择
  const menuConfig = reactive({
    id: '1922530508708454402',
    code: 'stdField',
    name: t('dictionary-config.major'),
  });

  const configType: any = ref(0);
  const configClick = (value: any, index: any) => {
    active.value = index;
    Object.assign(menuConfig, value);
    tableTitle.value = value.name;
    configType.value = index;
    getList();
  };

  // 最近文件列表部分
  const tableData = ref<object[]>([]);
  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('dictionary-config.index'),
      slotName: 'index',
      width: 110,
      render: (e: any) => {
        const res = e.rowIndex + 1;
        return res;
      },
      align: 'left',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('dictionary-config.name'),
      dataIndex: 'contentMap.zh-CN',
      align: 'left',
      width: 340,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('dictionary-config.note'),
      dataIndex: 'ext1',
      align: 'left',
      width: 340,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('dictionary-config.sort'),
      dataIndex: 'sort',
      width: 110,
      align: 'left',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('dictionary-config.operation'),
      dataIndex: 'action',
      slotName: 'action',
      width: 110,
      align: 'left',
      ellipsis: true,
      tooltip: true,
    },
  ]);

  // 递归提取 content 属性
  const extractContentRecursive = (list: any[]) => {
    const result: any[] = [];
    const traverse = (nodes) => {
      nodes.forEach((node) => {
        if (node.content) {
          result.push(node.content);
        }
        if (Array.isArray(node.children)) {
          traverse(node.children);
        }
      });
    };

    traverse(list);
    return result;
  };

  // 获取列表数据
  const getList = async () => {
    addVisible.value = false;
    loading.value = true;
    try {
      const params = {
        code: menuConfig.code,
        langCode: 'zh-CN',
      };
      const res = await getAllKeyValue(params);

      if (res.status && Array.isArray(res.data) && res.data.length > 0) {
        const root = res.data[0];

        // 检查是否存在有效的 children 数组
        const hasChildren =
          Array.isArray(root.children) && root.children.length > 0;

        // 提取所有 content 对象（如果有 children）
        const allContentObjects =
          menuConfig.code !== 'stdPrecision' && hasChildren
            ? extractContentRecursive(root.children)
            : [];

        if (menuConfig.code === 'stdPrecision') {
          const firstLevelContents = root.children?.map((item) => item.content);
          tableData.value = [...firstLevelContents];
        } else {
          tableData.value = [...allContentObjects];
        }
      }
    } catch (err) {
      console.log(err);
    } finally {
      loading.value = false;
    }
  };

  // 新增
  const addHandle = () => {
    actionType.value = 'add';
    addVisible.value = true;
  };

  // 编辑
  const editHandle = (value: any) => {
    actionType.value = 'edit';
    addVisible.value = true;
    configData.value = value;
  };

  // 删除标签
  const delHandle = async (record: any) => {
    try {
      const res = await deleteKeyValue({ id: record.id });
      if (res.status) {
        Message.success(t('dictionary-config.success'));
        getList();
      }
    } catch (err) {
      console.log(err);
    }
  };

  onMounted(() => {
    getList();
  });
</script>

<script lang="ts">
  export default {
    name: 'DictionaryConfig',
  };
</script>

<style scoped lang="less">
  .pageContainer {
    position: absolute;
    width: 100%;
    height: calc(100% - 60px);
    padding: 16px 20px;

    .container {
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: relative;
      padding: 20px;
      display: flex;
      flex-direction: column;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #d9d9d9;
    }

    .arco-col {
      background-color: var(--color-bg-1);
    }

    .containerLeft {
      padding: 0 20px 0 0;

      .content-title {
        display: flex;
        margin-bottom: 16px;

        div {
          display: flex;
          align-items: center;
          justify-content: left;

          .title-text {
            margin-left: 8px;
            font-weight: bold;
            font-size: 16px;
          }
        }
      }

      .leftList {
        > div {
          padding: 4px 0 4px 10px;
          font-size: 16px;
          line-height: 30px;
          cursor: pointer;
        }

        .active {
          color: rgb(var(--link-6));
          background-color: #e8f2ff;
        }
      }
    }
  }

  .containerRight {
    padding: 0 0 0 20px;
    border-left: 2px solid #efefef;
    height: 100%;

    .content {
      .content-title {
        position: relative;
        width: 100%;

        .btns {
          position: absolute;
          top: 0;
          right: 0;
        }

        .title-img {
          width: 20px;
          height: 20px;
        }

        .title-text {
          position: absolute;
          top: 0;
          left: 20px;
          display: inline-block;
          margin-left: 8px;
          color: #1d2129;
          // color: var(--color-text-1);
          font-weight: 600;
          font-size: 18px;
          // font-family: 'Source Han Sans CN-Medium', 'Source Han Sans CN', serif;
          line-height: 21px;
        }
      }

      .table-wrap {
        height: 100%;
        margin-top: 20px;

        :deep(.arco-table-container) {
          height: 100%;
        }

        .empty-wrapper {
          height: calc(100vh - 386px);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          img {
            display: block;
            width: 140px;
            height: 140px;
          }
          div {
            margin-top: 16px;
            color: #4e5969;
          }
        }

        .btn-spacing {
          display: inline-flex;
          gap: 10px;
          .edit-btn {
            font-size: 16px;
            cursor: pointer;
            color: #165dff;
          }
          .delete-btn {
            font-size: 16px;
            cursor: pointer;
            color: #f53f3f;
          }
        }
      }
    }
  }
</style>
