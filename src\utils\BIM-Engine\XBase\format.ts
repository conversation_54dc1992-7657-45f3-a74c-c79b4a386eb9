export const model2d = ['dwg'];
export const modelElse = ['asm', 'clash'];
export const model3d = [
  'dgn',
  'rvt',
  'rfa',
  '3ds',
  '3dm',
  'obmx',
  'fbx',
  'skp',
  'obj',
  'p3d',
  'ifc',
  'pmodel',
];

/** 大象云-上传文件格式集合 */
export const modelall = [
  'rfa',
  'skp',
  'obj',
  'stl',
  'glb',
  'fbx',
  'gltf',
  'stp',
  'step',
  'x_t',
  '3ds',
  'dae',
  'nwd',
  'rvt',
  'ifc',
  'dgn',
  'dwg',
  'pdf',
  'doc',
  'docx',
  'xlsx',
  'pptx',
  'wps',
  'csv',
  'png',
  'jpeg',
  'jpg',
];

/** 大象云-模型管理服务-上传模型 */
export const xbaseTransformModel = [
  'skp',
  'obj',
  'stl',
  'glb',
  'fbx',
  'gltf',
  'stp',
  'step',
  'x_t',
  '3ds',
  'dae',
  'nwd',
  'rvt',
  'ifc',
  'dgn',

  'rfa',
  'dwg',

  'asm',
  'clash',
];

/** 大象云-构件管理服务-上传构件 */
export const xbaseTransformComponent = ['rfa', 'glb', 'gltf'];

/** 大象云-语义模型服务-语义模型转换 */
export const xbaseSemanticModel = ['rvt', 'ifc', 'dgn']; // ('rvt''ifc'支持数据服务所有功能 'dgn'仅支持模型检查)

/** 大象云-文件管理服务-上传文件 */
export const xbaseVisualFile = [
  'dwg',
  'pdf',
  'doc',
  'docx',
  'xlsx',
  'pptx',
  'wps',
  'csv',
  'png',
  'jpeg',
  'jpg',
];

export const bimBaseStates = {
  success: [
    'complete', // -已完成，
  ],
  fail: [
    'aborted', // -终止，
    'cancelled', // -取消，
    'timed out', // -超时，
    'obsolete', // -废弃
  ],
  wait: [
    'queued', // -排队，
    'busy', // -正在转换，
  ],
};
