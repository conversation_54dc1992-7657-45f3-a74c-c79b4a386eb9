import axios from 'axios';
/**
 * 查询个人日程的参数接口
 * 用于定义查询个人日程接口所需的参数结构
 */
export interface QueryPersonalScheduleParams {
  /**
   * 时间范围字符串，格式为 'YYYY-MM-DD'
   * 表示查询的日期范围
   */
  endTime?: string; // 可选参数
  startTime?: string; // 可选参数
  pageNo: number; // 页码
  pageSize: number; // 每页条数
}

export function deleteProject(id: string) {
  return axios.delete(`/cde-collaboration/project/remove?id=${id}`);
}

export const getMyWorks = (params: any) => {
  return axios.get(
    `/cde-collaboration/process/countTasks?projectId=${params.projectId}`
  );
};
// 查看当前用户下的知识库统计结果
export const getKnowledgeBaseCnt = () => {
  return axios.get(`/cde-work/knowledgeBase/queryKnowledgeBaseCntByUserId`);
};
// 个人日程
export function getPersonalSchedule(data: QueryPersonalScheduleParams) {
  return axios.post('/cde-work/schedule/personal/list', data);
}
export default { deleteProject };
