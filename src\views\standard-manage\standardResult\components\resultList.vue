<template>
  <div class="listContent">
    <a-row>
      <a-col :span="12"
        ><table-title
          :title="$t('standard-setting.standard-information')"
        ></table-title
      ></a-col>
      <a-col :span="12">
        <a-select
          v-model="historyId"
          class="timeBox"
          :placeholder="$t('please-select')"
          style="width: 200px"
          @change="changeHistory"
        >
          <a-option
            v-for="item of historyOption"
            :key="item.id"
            :value="item.id"
            >{{ item.createDate }}</a-option
          >
        </a-select></a-col
      >
    </a-row>
    <ul>
      <li v-for="(item, index) in modelData" :key="index">
        <span class="label">{{ item.label }}</span>
        <span class="value">{{ item.value }}</span>
      </li>
    </ul>
    <table-title
      :title="$t('standard.attr-error-list')"
      style="margin-bottom: 16px"
    ></table-title>
    <a-table
      :loading="loading"
      :columns="columns"
      :data="tableData"
      :pagination="PageConfigs"
      row-key="uid"
      :hoverable="true"
      :scroll="{ y: 'calc(100vh - 510px)' }"
      @page-change="pageChange"
      @page-size-change="pageSizeChange"
      @row-click="resultClick"
    >
      <template #guid="{ record }">
        <span class="blueText">{{
          record.guidName || record.guid
        }}</span></template
      >
      <template #goujianFenLeiMessage="{ record }">
        <span class="blueText">{{
          record.goujianFenLeiMessage.slice(-3)
        }}</span></template
      >
      <template #errNum="{ record }">
        <span class="redText" @click="getDetail(record)">{{
          record.errNum
        }}</span></template
      >
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, toRaw, onMounted, watchEffect, computed } from 'vue';
  import { getCheckoutResult, getHistoryData } from '../api';
  import { useRoute } from 'vue-router';
  import useStandardManageStore from '@/store/modules/standard-manage/index';
  import { GetElementldsByGuids } from '@/views/bim-view/XBase/version-compare/api';
  import { useI18n } from 'vue-i18n';
  import TableTitle from '@/components/table-title/index.vue';

  const { t } = useI18n();
  let compareViewer: any = null;

  const props = defineProps({
    modelFile: {
      type: Object,
      default: () => {
        return {};
      },
    },
    viewers: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const modelData: any = ref([]);
  const modelDetailData = ref([]);

  const tableData: any = ref([]); // 属性错误列表数据
  const detailData: any = ref([]); // 详情数据

  const PageConfigs: any = reactive({
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: false,
    showJumper: false,
    showPageSize: false,
    simple: true,
  });

  // 构件高亮
  const resultClick = async (record: any) => {
    compareViewer?.clearObjectsColor([`'${record.guid}'`]);
    const path = props.modelFile.graphicEngineInfo.split('|')[1];
    const result = await GetElementldsByGuids({
      render_path: path,
      guids: [`'${record.guid}'`],
    });
    const elementId = Object.values(result.data.guid_element_id)[0];
    compareViewer.selectEntities(elementId);
  };

  const columns: any = computed(() => [
    {
      title: t('standard.member-name-id'),
      dataIndex: 'guid',
      slotName: 'guid',
      align: 'center',
    },
    {
      title: t('standard.match-state'),
      dataIndex: 'goujianFenLeiMessage',
      slotName: 'goujianFenLeiMessage',
      align: 'center',
    },
    {
      title: t('standard.error-attributes-number'),
      dataIndex: 'errNum',
      slotName: 'errNum',
      align: 'center',
    },
  ]);

  const historyOption: any = ref([]); //  历史数据

  const store = useStandardManageStore();

  const loading: any = ref(false);
  const historyId: any = ref();

  const route = useRoute();

  // 结果数据
  const getResultData = async () => {
    try {
      loading.value = true;
      const param: any = {
        fileId: route.query.fileId,
        pageNum: PageConfigs.current,
        pageSize: PageConfigs.pageSize,
        historyId: historyId.value,
      };
      const { data } = await getCheckoutResult(param);
      tableData.value = data.list || [];
      PageConfigs.total = data.total || 0;
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const pageChange = (val: number) => {
    PageConfigs.current = val;
    getResultData();
  };
  const pageSizeChange = (val: number) => {
    PageConfigs.pageSize = val;
  };

  // 切换历史版本
  const changeHistory = (value: any) => {
    compareViewer.selectEntities(null);
    historyId.value = value;
    const idx = historyOption.value.findIndex((item: any) => item.id === value);
    modelData.value = modelDetailData.value[idx];
    getResultData();
  };

  // 获取历史数据
  const getHistoryList = async () => {
    const historyData: any = await getHistoryData({
      fileId: route.query.fileId,
    });
    if (!historyData.data?.length) return;
    historyId.value = historyData.data[0].id;
    historyOption.value = historyData.data;
    historyData.data.forEach((item: any) => {
      const [errorNum, correctNumber] = item.goujianErrCorrect.split(',');
      const totleNum = Number(correctNumber) + Number(errorNum);
      const correctRate = (correctNumber / totleNum) * 100;
      const errRate = (errorNum / totleNum) * 100;

      const modelDetail: any = [
        {
          label: t('standard.model-name'),

          value: item.fileName,
          span: 4,
        },
        {
          label: t('standard.standard-name'),
          value: item.className,
          span: 4,
        },
        {
          label: t('standard.member-totle'),
          value: totleNum,
          span: 4,
        },
        {
          label: t('standard.correct-number'),
          value: correctNumber || 0,
          span: 2,
        },
        {
          label: t('standard.correct-ratio'),
          value: `${correctRate.toFixed(2) || 0}%`,
          span: 2,
        },
        {
          label: t('standard.error-number'),
          value: errorNum || 0,
          span: 2,
        },

        {
          label: t('standard.error-ratio'),
          value: `${errRate.toFixed(2)}%`,
          span: 2,
        },
      ];

      modelDetailData.value.push(modelDetail);
      getResultData();
    });
  };

  // 获取错误详情
  const getDetail = (record: any) => {
    const guId = record.guid;
    const errorMapList = record.errorMap[guId];
    // 详情数据
    detailData.value = errorMapList;
    store.setResultDetail(errorMapList);
  };

  const init = async () => {
    await getHistoryList();
    [modelData.value] = modelDetailData.value;
  };

  onMounted(() => {
    init();
  });

  watchEffect(async () => {
    compareViewer = await toRaw(props.viewers.compareViewer);
  });
</script>

<style scoped lang="less">
  .listContent {
    width: 440px;
    padding: 20px;
    border-radius: 8px;

    ul {
      margin: 20px 0;
      list-style: none;
      padding: 0;
      li {
        width: 400px;
        height: 54px;
        line-height: 54px;
        background: #f3f8ff;
        margin-bottom: 12px;
        border-radius: 8px;
        padding: 0 16px;

        .label {
          font-size: 14px;
          color: #4e5969;
          margin-right: 16px;
        }
        .value {
          font-size: 16px;
          color: #1d2129;
        }
      }
      :nth-child(4),
      :nth-child(5),
      :nth-child(6),
      :nth-child(7) {
        width: 194px;
        display: inline-block;
      }
      :nth-child(4),
      :nth-child(6) {
        margin-right: 10px;
      }
      :nth-child(6),
      :nth-child(7) {
        margin-bottom: 0;
      }
    }

    .redText {
      color: red;
      cursor: pointer;
    }

    .blueText {
      color: rgb(var(--link-6));
      cursor: pointer;
    }

    :deep(.table-title img) {
      vertical-align: middle;
    }
  }
</style>
