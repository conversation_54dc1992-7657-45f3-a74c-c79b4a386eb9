export default {
  'design.lane-chart': 'Lane Chart',
  'design.current-team-legend': 'Current Team Legend',
  'design.other-team-legend': 'Team Colors Correspond To Each Team Legend',
  'design.show-current-team': 'Show Current Team',
  'design.show-all-team': 'Show All Team',
  'design.show-more-team': 'Show More Team',
  'design.show-less-team': 'Show Less Team',
  'design.close': 'Close',
  'design.information': 'Information',
  'design.share': 'Share',
  'design.share-name': 'Share Name',
  'design.share-name-tip':
    'The name of the folder under SHARED after the share is passed',
  'design.sharer': 'Sharer',
  'design.share-time': 'Share Time',
  'design.share-file': 'Share File',
  'design.cache-share': 'Are you sure to cache the current share package?',
  'design.cache-share-hint':
    'The current shared package contains invalid files, which cannot be cached in the consumer package',
  'design.no-team': 'There is no team to show',
  'design.no-more-team': 'There is no more teams',
  'design.cancel': 'Cancel',
  'design.save': 'Save',
  'design.submit': 'Submit',
  'design.remove': 'Remove',
  'design.confirm': 'Confirm',
  'design.approve-workflow': 'Approve Workflow',
  'design.please-choose': 'Please Choose',
  'design.name': 'Name',
  'design.recipients': 'Recipients',
  'design-recipients-tip':
    'After all share approvals are passed, a share notification is sent to the recipient',
  'design.file-required': '{name} file required',
  'design.file-counts': 'No File | Total：{count} File | Total：{count} Files',
  'design.add-file': 'Add File',
  'design.delivery': 'Delivery',
  'design.milestone': 'Milestone',
  'design.to-third-system':
    'Are work packages delivered to third party systems?',
  'design.yes': 'Yes',
  'design.no': 'No',
  'design.system-name': 'System Name',
  'design.data-asset': 'Data Asset',
  'design.project-information': 'Do you carry project information?',
  'design.please-enter': 'Please search by entering the name',
  'design.project-name': 'Project Name',
  'design.project-code': 'Project Code',
  'design.project-date': 'Project Date',
  'design.project-type': 'Project Type',
  'design.project-description': 'Project Description',
  'design.enter-name': 'Please enter name',
  'design.select-process': 'Please select process',
  'design.select-milestone': 'Please select milestone',
  'design.select-recipient': 'Please select recipient',
  'design.select-system': 'Please select system',
  'design.attention': 'Attention Please',
  'design.fail-verification_share':
    'Some of the files you submitted did not pass the verification criteria, please go to the temporary area to modify',
  'design.fail-verification_deliver':
    'Some of the files you submitted did not pass the verification criteria, please go to the temporary area to modify',
  'design.to-modify': 'Go To Modify',
  'design.select-least': 'Please select at least one file',
  'design.succeeded': 'Succeeded',
  'design.success-verification':
    'The documents you submitted have been verified. Please follow up the process in time',
  'design.in-examination-verification':
    'The document you submitted is being verified, please follow up the process in time',
  'design.highway-engineering': 'Highway Engineering',
  'design.urban-road-engineering': 'Urban Road Engineering',
  'design.airport-runway-engineering': 'Airport Runway Engineering',
  'design.traffic-engineering': 'Traffic Engineering Of Urban Rail',
  'design.bridge-engineering': 'Bridge Engineering',
  'design.culvert-engineering': 'Culvert Engineering',
  'design.port-engineering': 'Port Engineering',
  'design.navigation-engineering': 'Navigation Engineering',
  'design.tunneling-engineering': 'Tunneling Engineering',
  'design.building-engineering': 'Building Engineering',
  'design.water-transport-engineering': 'Water Transport Engineering',
  'design.people': 'People',
  'design.time': 'Time',
  'design.process-state': 'Process State',
  'design.this-diagram-example': 'This Team Swimlane Diagram Example',
  'design.other-diagram-example': 'Examples Of Other Team Lane Diagrams',
  'design.logo-click': '(Digital Logo Click/Scale To Expand)',
  'design.dotted-circle': 'The Dotted Circle On The Team Timeline',
  'design.dotted-circle-explain':
    'Represents a new shared package that has been saved but not yet committed',
  'design.light-circle': 'A Light Circle On The Team Timeline',
  'design.light-circle-explain':
    'Represents a new shared package that has been submitted but not yet reviewed',
  'design.solid-circle': 'Solid Circle On The Team Timeline',
  'design.shared-package': 'Represents a shared package',
  'design.dashed-square': 'Dashed Square On The Team Timeline',
  'design.dashed-square-explain':
    'Represents a new delivery package that has been saved but not yet submitted',
  'design.light-square': 'A Light Square On The Team Timeline',
  'design.light-square-explain':
    'Represents a new delivery package that has been submitted but not yet reviewed',
  'design.solid-square': 'Solid Square On The Team Timeline',
  'design.published-package': 'Represents a published delivery package',
  'design.solid-circle-with-number': 'Solid Circle with Numbers',
  'design.group-shared-package': 'Represents a group of shared packages',
  'design.solid-drum': 'Solid Drum with Numbers',
  'design.group-completed-package':
    'Represents a group of completed shared and published packages',
  'design.hollow-circle': 'Hollow Circle On Other Teams Timeline',
  'design.hollow-circle-explain':
    'Represents a shared package that has been shared',
  'design.hollow-circle-explain2':
    'but this team has not yet downloaded and used the data package',
  'design.other-solid-circle': 'Solid Circle On Other Teams Timeline',
  'design.solid-circle-explain':
    'Represents a data package that this team has already downloaded and used',
  'design.other-solid-square': 'Solid Square On Other Teams Timeline',
  'design.solid-square-explain':
    'Represents a delivered package that has been published',
  'design.hollow-circle-with-num': 'Hollow Circle with Numbers',
  'design.hollow-circle-with-num-explain':
    'Represents a group of shared packages',
  'design.hollow-circle-with-num-explain2':
    'but this team has not yet downloaded and used',
  'design.solid-circle-with-num': 'Solid Circle with Numbers',
  'design.solid-circle-with-num-explain':
    'Represents a group of shared packages',
  'design.solid-circle-with-num-explain2':
    'and this team has downloaded and used them all',
  'design.two-color-circle-with-num': 'Two-color Circle with Numbers',
  'design.two-color-circle-with-num-explain':
    'Represents a group of shared packages',
  'design.two-color-circle-with-num-explain2':
    'and this team has partially downloaded and used them',
  'design.solid-square-with-num': 'Solid Square with Numbers',
  'design.solid-square-with-num-explain':
    'Represents a group of delivered packages',
  'design.solid-drum-with-num': 'Solid Drum with Numbers',
  'design.solid-drum-with-num-explain':
    'Represents a group of completed shared and published packages',
  'design.two-color-drum-with-num': 'Two-color Drum with Numbers',
  'design.two-color-drum-with-num-explain':
    'Represents a combination of unused shared packages and published packages',
  'design.index': 'Index',
  'design.standard-name': 'Standard Name',
  'design.description': 'Description',
  'design.version': 'Version',
  'design.size': 'Size',
  'design.operation': 'operation',
  'design.file-list': 'File List',
  'design.initiate-delivery': 'Delivery',
  'design.download-source-file': 'Download Source File',
  'design.no-permission': 'Do not have permission',
  'design.to-current-team': 'Set To The Current Team',
  'design.unfollow': 'Unfollow',
  'design.pay-attention': 'Attention',
  'design.reset': 'Reset',
  'design.reset-timeline': 'Reset Timeline',
  'design.shared-package-count':
    'no shared package | {count} shared package | {count} shared packages',
  'design.delivery-package-count':
    'no delivery package | {count} delivery package | {count} delivery packages',
  'design.year': '{count} year',
  'design.month': '{count} month',
  'design.week': '{count} week',
  'design.deleteSuccessfully': 'Delete successfully',
  'design.deleteFailed': 'Delete failed',
  'design-delete-tip': 'Are you sure you want to delete?',
  'design.no-data-available': 'No Data Available',
  'design.sync-push-to-jjt': 'Sync Push to JJT',
  'design.approver': 'Approver',
  'design.recipient': 'Recipient',
  'file-manage.batch-download': 'Batch Download',
  'file-manage.wait-downloading': 'Please wait while the file is downloading',

  'task.share': 'Share',
  'task.deliver': 'Deliver',
  'task.share-name': 'Share Name',
  'task.please-enter': 'Please search by entering the name',
  'task.search': 'Search',
  'task.clear': 'Clear',
  'task.share-list': 'Share List',
  'task.deliver-list': 'Deliver List',
  'task.review': 'Review',
  'task.revocation': 'Revocation',
  'task.index': 'Index',
  'task.task-name': 'Task Name',
  'task.task-status': 'Task Status',
  'task.process-templates': 'Template',
  'task.process-status': 'Process Status',
  'task.initiator': 'Initiator',
  'task.creation-time': 'Creation Time',
  'task.file-count': 'File Count',
  'task.operation': 'Operation',
  'task.whether-revoke-share': 'Whether to revoke the share?',
  'task.deliver-name': 'Deliver Name',
  'task.milestone': 'Milestone',
  'task.whether-revoke-delivery': 'Whether to revoke the delivery?',

  'task.process-detail': 'Process Detail',
  'task.name': 'Name',
  'task.file': 'File',
  'task.view-more': 'View More',
  'task.pack-up': 'Pack Up',
  'task.processing-records': 'Processing Records',
  'task.node-name': 'Node Name',
  'task.operator': 'Operator',
  'task.approval-result': 'Approval Result',
  'task.approval-opinion': 'Approval Opinion',
  'task.approval-time': 'Approval Time',
  'task.reject': 'Reject',
  'task.agree': 'Agree',
  'task.determine': 'Determine',
  'task.please-enter-approval-opinion': 'Please enter your approval opinion!',
  'task.missing-form-configuration': 'Missing form configuration!',
  'task.unable-get-form-details': 'Unable to get form details!',
  'task.fileToken-missing':
    'The fileToken file is missing and cannot be downloaded!',
  'task.upload-attachment': 'upload attachment',
  'task.attachment-info': 'attachment info',
  'task.click-to-download-the-file': 'click to download the file',
  'task.delete-file-success': 'delete file successs',

  'task.upload': 'Upload',
  'task.download': 'Download',
  'task.not-upload-same-file-twice': 'Do not upload the same file twice',
  'task.uploaded-files-counts-exceeds-limit':
    'The number of uploaded files exceeds the limit',
  'task.not-upload-empty-files': 'Do not upload empty files',
  'task.upload-in': 'Upload In',
  'task.description': 'Description',
  'task.disallowed-file-format': 'Disallowed file format',
  'task.uploaded-files-exceeds-limit':
    'The size of the uploaded file cannot exceed',
  'task.uploaded-files-quantity-exceeds-limit':
    'The number of concurrent file uploads exceeds the limit',
  'task.uploaded-files-types-exceeds-limit':
    'The type of uploaded file is out of limit',
  'task.please-check-and-upload again':
    'File upload error, please check and upload again',
  'task.quick-pass-successful': 'Quick pass successful',
  'task.view': 'View',
  'task.share-audit': 'Share Audit',
  'task.deliver-audit': 'Deliver Audit',

  'task.no-data-available': 'No Data Available',
};
