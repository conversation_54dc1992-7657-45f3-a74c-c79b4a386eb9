<template>
  <div class="container">
    <a-card class="general-card no-page">
      <template #title>
        <a-row>
          <table-title :title="$t('menu.org.department')"></table-title>
        </a-row>
      </template>
      <a-table
        row-key="id"
        ref="tableRef"
        :indent-size="50"
        :loading="loading"
        :columns="columns"
        :data="renderData"
        :size="size"
        :pagination="false"
        :scroll="{ y: tableHeight }"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
        <template #orgType="{ record }">
          {{ record.content.orgType === '0' ? '内部' : '外部' }}
        </template>
        <template #expand-icon="{ expanded }">
          <icon-right v-if="!expanded" />
          <icon-down v-if="expanded" />
        </template>
        <template #operations="{ record }">
          <!-- 新增 -->
          <a-button
            v-if="permissions(record, 'create')"
            type="text"
            size="small"
            @click="addSubHandler(record)"
          >
            {{ $t('department.columns.operations.create') }}
          </a-button>
          <!-- 编辑 -->
          <a-button
            v-if="permissions(record, 'edit')"
            type="text"
            size="small"
            @click="editHandler(record)"
          >
            {{ $t('department.columns.operations.edit') }}
          </a-button>
          <a-popconfirm
            content="确认删除该项?"
            type="warning"
            @before-ok="(done) => deleteHandler(done, record)"
          >
            <a-button
              v-if="permissions(record, 'del')"
              type="text"
              style="color: rgb(var(--red-6))"
              size="small"
            >
              {{ $t('department.columns.operations.delete') }}
            </a-button>
          </a-popconfirm>
        </template>
      </a-table>
    </a-card>
    <OrgInfo
      v-model:visible="dialogVisible"
      :form="selectDept"
      @refresh="search"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { usePortalStore, useUserStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import {
    getOrgs,
    OrgServiceRecord,
    OrgRecord,
    SelectDept,
    deleteOrg,
  } from '@/api/modules/department';
  import OrgInfo from './components/org-info.vue';
  import { SizeProps } from './types';
  import TableTitle from '@/components/table-title/index.vue';

  const { loading, setLoading } = useLoading(true);
  const { t } = useI18n();
  const portalStore = usePortalStore();
  const renderData: any = ref([]);

  const size = ref<SizeProps>('medium');
  const tableHeight = ref(window.innerHeight - 200);

  const dialogVisible = ref(false);
  const emptyDept = {
    id: '',
    parentId: '',
    name: '',
    entName: '',
    parentName: '',
    pathName: '',
    pathNo: '',
  };
  const selectDept = ref<SelectDept>({ ...emptyDept });

  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('department.columns.index'),
      dataIndex: 'index',
      slotName: 'index',
      align: 'center',
      width: 170,
    },
    {
      title: t('department.columns.name'),
      dataIndex: 'name',
      align: 'center',
      headerCellStyle: {
        textAlign: 'center',
      },
      width: 260,
    },
    {
      title: t('department.columns.abbr'),
      dataIndex: 'entName',
      align: 'center',
    },
    {
      title: t('department.columns.orgType'),
      dataIndex: 'orgType',
      slotName: 'orgType',
      align: 'center',
    },
    {
      title: t('department.columns.operations'),
      dataIndex: 'operations',
      slotName: 'operations',
      align: 'center',
      width: 260,
    },
  ]);

  const tableRef = ref<any>(null);
  const firstId: any = ref('');
  const fetchData = async () => {
    setLoading(true);
    try {
      const { data }: { data: OrgServiceRecord[] } = await getOrgs({
        isAll: false,
        portalId: portalStore.portalId,
      });
      renderData.value = tranferTreeData(data);
      // 筛选当前用户所属的二级单位
      const pathNo = Number(userStore.userInfo.pathNo?.split('.')[3]);
      if (userStore.admin === 3) {
        // 单位管理员展示当前所属二级单位数据
        renderData.value[0].children = renderData.value[0].children?.filter(
          (item: any) => item.content.orgNo === pathNo
        );
      }
      firstId.value = data[0]?.id;
      // 表格默认展开一级
      tableRef.value.expand([renderData.value[0].id]);
    } catch (err) {
      // you can report use errorHandler or other
    } finally {
      setLoading(false);
    }
  };

  const tranferTreeData = (data: OrgServiceRecord[]) => {
    const result: OrgRecord[] = [];
    data.forEach((item) => {
      const { children, content, id, parentId } = item;
      const obj: OrgRecord = {
        id,
        parentId,
        content,
        name: content.name,
        entName: content.entName,
      };
      if (children && children.length) {
        const childrenRes = tranferTreeData(children);
        obj.children = childrenRes;
      }
      result.push(obj);
    });
    return result;
  };

  const search = () => {
    fetchData();
  };

  fetchData();

  // 打开新增子部门弹窗
  const addSubHandler = (record: OrgRecord) => {
    dialogVisible.value = true;
    selectDept.value = {
      id: '',
      name: '',
      entName: '',
      parentId: record.id,
      pathName: record.content?.pathName,
      pathNo: record.content?.pathNo,
      parentName: record.content?.name,
      orgType: record.content?.orgType,
      operator: 'addSub',
    };
  };

  // 打开编辑组织机构弹窗
  const editHandler = (record: OrgRecord) => {
    dialogVisible.value = true;
    selectDept.value = {
      id: record.id,
      name: record.content?.name,
      entName: record.content?.entName,
      parentId: record.parentId,
      content: record.content,
      orgType: record.content?.orgType,
      operator: 'edit',
    };
  };

  // 删除组织机构
  const deleteHandler = async (done: any, record: OrgRecord) => {
    const id = record.content?.id || '';
    await deleteOrg(id);
    search();
    done();
  };

  const userStore = useUserStore();

  // 权限处理
  const permissions = (record: any, action: any) => {
    // 超级管理员一级单位不允许删除外 其余皆可操作
    if (userStore.admin === 0) {
      if (action !== 'del' || record.parentId !== '0') return true;
    } else if (userStore.admin === 3) {
      // 单位管理员 一级单位不可操作
      if (record.parentId === '0') return false;

      // 编辑、删除 只有二级单位以下的可以操作  新增任何单位都可操作
      if (action !== 'create') {
        if (record.parentId !== firstId.value) return true;
        return false;
      }
      return true;
    }
    return false;
  };
</script>

<script lang="ts">
  export default {
    name: 'Department',
  };
</script>

<style scoped lang="less">
  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }

  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }

  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }

  .setting {
    display: flex;
    align-items: center;
    width: 200px;

    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }

  .card-title {
    font-size: 18px;
    color: #1d2129;
    display: flex;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      vertical-align: middle;
    }
  }
</style>
