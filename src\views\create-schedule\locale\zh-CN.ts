export default {
  'schedule.basicInfo': '基本信息',
  'schedule.assignedTo': '分配给',
  'schedule.selectAssignee': '请选择分配人',
  'schedule.selectPerson': '请选择人员',
  'schedule.matter.starttime': '开始时间',
  'schedule.matter.deadline': '截止日期',
  'schedule.selectStartTime': '请选择开始时间',
  'schedule.selectDeadline': '请选择截止日期',
  'schedule.calendarBelong': '归属日历',
  'schedule.selectCalendar': '请选择日历',
  'schedule.description': '描述',
  'schedule.placeholder': '请输入',
  'schedule.subTaskList': '子事项清单',
  'schedule.aiSubTaskList': 'AI识别描述生成子事项清单',
  'schedule.add': '新增',
  'schedule.inputTitle': '请输入标题',
  'schedule.delete.confirm': '确认删除该事项吗？',
  'schedule.attachment': '附件',
  'schedule.uploadAttachment': '上传附件',
  'schedule.comment': '评论',
  'schedule.delete.comment': '确认删除该评论?',
  'schedule.noComment': '暂无评论',
  'schedule.placeholder.comment': '请输入评论，按回车键发送',
  'schedule.matter.createMatter': '创建事项',
  'schedule.matter.saveChanges': '保存修改',
  'schedule.comment.success': '评论成功',
  'schedule.comment.placeholder': '请输入评论内容',
  'schedule.deadline.error': '截止时间不能早于当前时间',
  'schedule.deadline.create.error': '截止时间不能早于创建时间',
  'schedule.subTaskList.placeholder': '请填写子事项清单',
  'schedule.calendar.edit': '编辑成功',
  'schedule.calendar.edit.error': '编辑失败',
  'schedule.matter.description.placeholder': '请输入事项描述',
  'schedule.delete.success': '删除成功',

  'schedule.delete.push-to-Jiaojiantong': '是否推送至交建通',
  'schedule.go-to': '前往',
  'schedule.save-modifications': '当前页面编辑内容未保存，请问是否保存修改？',
  'schedule.save-as': '另存为',
  'schedule.milestoneId': '所属里程碑',
  'schedule.selected-milestoneId': '请选择所属里程碑',
  'schedule.milestoneId-notips': '所属里程碑不能为空',
  'schedule.rateProgress': '进度',
  'schedule.matter.dependency': '事项依赖',
  'schedule.project-task': '事项',
  'schedule.task-placeholder': '请选择',
  'schedule.relyType': '类型',
  'schedule.relyType-tips': '请选择依赖类型',
  'schedule.relyType-save': '保存',
  'schedule.no-available-task': '没有可以绑定的任务了！',
  'schedule.save-success': '保存成功',
  'schedule.delete-dependency': '确认删除该事项依赖么？',
  'schedule.no-relyType': '未选择依赖事项',
  'schedule.dependency.placeholder': '请完善依赖事项',
};
