import { defineStore } from 'pinia';
import i18n from '@/locale/index';
import { KnowledgeBaseNewState } from './types';
import { FileAndFolderMessage } from '@/api/tree-folder';
import {
  initSessionStorageData,
  setSessionStorageData,
  getBreadcrumbs,
} from '@/store/modules/file/utils';
import { isSysFolder } from '@/views/projectSpace/file/utils';
import { getUserId } from '@/utils/auth';
import {
  deleteApi,
  getProjectFileList,
  unrelateFolder,
} from '@/views/knowledge-base-new/components/api';
import { Notification } from '@arco-design/web-vue';
import { getChildFolderList, getFileList } from '@/api/tree-folder';
import { downloadSource } from '@/views/projectSpace/file/hooks/events';
import mockProjectFolderData from '@/views/knowledge-base-new/components/mockData';

const { t } = i18n.global;
const defaultTreeData = [
  {
    name: 'WIP',
    id: 'WIP',
    children: [],
  },
  // {
  //   name: 'Shared',
  //   id: 'Shared',
  //   children: [],
  // },
  // {
  //   name: 'Published',
  //   id: 'Published',
  //   children: [],
  // },
];

export interface FolderMessage {
  id?: string;
  name?: string;
  title?: string;
  projectId?: string;
  teamId?: string;
  userId?: string;
  parentId?: string;
  childId?: string;
  children?: FolderMessage[];
  isFileOrFolder?: number;
  type?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  sysType?: number;
}
const userId = getUserId() || '';
const useKnowledgeBaseNewStore = defineStore('knowledgeBaseNew', {
  state: (): KnowledgeBaseNewState => {
    return {
      personal: {
        fileList: [],
        folderList: [],
        projectId: initSessionStorageData(`personal_project_${userId}`, ''), // 个人网盘初始化的项目id
        personalType: '',
        // currentFolder: initSessionStorageData('personFileCurrentFolder', {
        //   projectId: '',
        //   type: '',
        //   name: t('cloud.personal-space'),
        //   parentId: '0',
        //   id: '',
        // }), // 设置默认值是personal
        currentFolder: {
          projectId: '',
          type: '',
          name: t('cloud.personal-space'),
          parentId: '0',
          id: '',
        }, // 设置默认值是personal
        // breadcrumb: initSessionStorageData('personalBreadcrumb', []), // 面包屑导航默认个人空间
        breadcrumb: [], // 面包屑导航默认个人空间
        tableLoading: false, // 表格加载状态
        // baseFolder: initSessionStorageData('personalBaseFolder', {
        //   projectId: '',
        //   type: '',
        //   name: t('cloud.personal-space'),
        //   parentId: '0',
        //   id: '',
        // }),
        baseFolder: {
          projectId: '',
          type: '',
          name: t('cloud.personal-space'),
          parentId: '0',
          id: '',
        },
      },
      project: {
        tableData: [],
        // currentFolder: initSessionStorageData('projectFileCurrentFolder', {
        //   id: 'project',
        //   name: 'cloud.project-space',
        // }),
        currentFolder: {
          id: 'project',
          name: 'cloud.project-space',
        },
        // breadcrumb: initSessionStorageData('projectBreadcrumb', [
        //   { id: 'project', name: 'cloud.project-space' },
        // ]), // 面包屑导航默认项目空间
        breadcrumb: [{ id: 'project', name: 'cloud.project-space' }], // 面包屑导航默认项目空间
        defaultTreeData: [],
        tableLoading: false, // 表格加载状态
        folderChildrenFolders: [],
        folderChildrenFiles: [],
        hiddenSlot: 0,
      },
      referenceModal: {
        referenceModalVisible: false, // 引用模态框的可见性
        currentProjectId: '', // 当前弹框ID
        currentProject: {}, // 当前弹框选择的项目数据
        allTreeData: defaultTreeData,
        currentFolder: {},
        breadcrumbList: [],
        selectedKeys: [], // 选中的树形节点
        tableData: [], // 表格数据
        tableLoading: false,
        selectedTableRowkeys: [],
        defaultTreeData,
      },
      // activeTabKey: initSessionStorageData('activeTabKeyCloud', 1), // 默认选中个人空间
      activeTabKey: 1, // 默认选中个人空间
    };
  },
  getters: {
    // 计算属性：拼接文件夹和文件列表，确保 fileList 在末尾
    personalCombinedList(
      state
    ): (FileAndFolderMessage & { selected: boolean })[] {
      return [...state.personal.folderList, ...state.personal.fileList];
    },
    folderChildrenDatas(
      state
    ): (FileAndFolderMessage & { selected: boolean })[] {
      return [
        ...state.project.folderChildrenFolders,
        ...state.project.folderChildrenFiles,
      ];
    },
    selectedProjectChildrenItems(
      state
    ): (FileAndFolderMessage & { selected: boolean })[] {
      return [
        ...state.project.folderChildrenFolders,
        ...state.project.folderChildrenFiles,
      ].filter((item) => item.selected);
    },
    selectedPersonalItems(
      state
    ): (FileAndFolderMessage & { selected: boolean })[] {
      return [...state.personal.folderList, ...state.personal.fileList].filter(
        (item) => item.selected
      );
    },
    selectedProjectItems(): (FileAndFolderMessage & { selected: boolean })[] {
      return this.projectAllItems.filter((item) => item.selected);
    },
    projectAllItems(state) {
      // 展开所有 folderVOList，并为每一项加上所属 item 的 projectId
      return state.project.tableData.flatMap((item) => item.folderVOList || []);
    },
  },
  actions: {
    setSelectedTableRowkeys(rowkeys: string[]) {
      this.referenceModal.selectedTableRowkeys = rowkeys;
    },
    setTableData(arr: FileAndFolderMessage[]) {
      console.log('设置表格数据', arr);
      console.log('setTableData 被调用了111');
      console.trace();
      this.referenceModal.tableData = arr;
    },
    setSelectedKeys(keys: string[]) {
      this.referenceModal.selectedKeys = keys;
    },
    toggleProject() {
      this.referenceModal.currentFolder = {};
      this.referenceModal.breadcrumbList = [];
      this.referenceModal.allTreeData = defaultTreeData;
      this.referenceModal.selectedKeys = [];
      this.referenceModal.expandedKeys = [];
    },
    setReferencereferenceModal() {
      this.referenceModal.currentProjectId = '';
      this.referenceModal.currentProject = {};
      this.referenceModal.tableData = [];
      this.referenceModal.selectedTableRowkeys = [];
      this.toggleProject();
    },
    setCurrentFolder(current: FileAndFolderMessage) {
      this.referenceModal.currentFolder = current;
      console.log(7777, this.referenceModal.currentFolder);
      this.referenceModal.breadcrumbList = getBreadcrumbs(current);
    },
    setBaseFolder(folder: FileAndFolderMessage) {
      this.personal.baseFolder = folder;
      // setSessionStorageData('personalBaseFolder', folder);
    },
    setReferenceModalVisible(visible: boolean) {
      this.referenceModal.referenceModalVisible = visible;
    },
    setReferenceCurrentProjectId(id: string) {
      this.referenceModal.currentProjectId = id;
    },
    setReferenceCurrentProject(item: Record<string, any>) {
      this.referenceModal.currentProject = item;
    },
    setActiveTabKey(key: number) {
      this.activeTabKey = key;
      // setSessionStorageData('activeTabKeyCloud', key);
    },
    setPersonalFileList(arr: (FileAndFolderMessage & { selected: boolean })[]) {
      this.personal.fileList = arr;
    },
    setPersonalfolderList(
      arr: (FileAndFolderMessage & { selected: boolean })[]
    ) {
      this.personal.folderList = arr;
    },
    setFolderChildrenFolders(
      arr: (FileAndFolderMessage & { selected: boolean })[]
    ) {
      this.project.folderChildrenFolders = arr;
    },
    setFolderChildrenFiles(
      arr: (FileAndFolderMessage & { selected: boolean })[]
    ) {
      this.project.folderChildrenFiles = arr;
    },
    setProjectTableData(arr: []) {
      this.project.tableData = arr;
    },
    setProjectId(id: string) {
      console.log('设置个人项目id', id);
      setSessionStorageData(`personal_project_${userId}`, id);
      this.personal.projectId = id;
    },
    setPersonalType(id: string) {
      this.personal.personalType = id;
    },
    setPersonCurrentFolder(current: FileAndFolderMessage) {
      // setSessionStorageData('personFileCurrentFolder', current);
      this.personal.currentFolder = current;
    },
    setProjectCurrentFolder(current: FileAndFolderMessage) {
      console.log('点击面包屑，设置当前文件夹信息', current);
      // setSessionStorageData('projectFileCurrentFolder', current);
      this.project.currentFolder = current;
    },
    syncPersonalCurrentFolder() {
      // ！注意：这会在 store 初始化、获取到 projectId 后调用
      this.personal.currentFolder = {
        ...this.personal.currentFolder,
        projectId: this.personal.projectId,
      };
    },
    setPersonalBreadcrumb(breadcrumb: any[]) {
      this.personal.breadcrumb = breadcrumb;
      // setSessionStorageData('personalBreadcrumb', breadcrumb);
      // localStorage.setItem('personalBreadcrumb', JSON.stringify(breadcrumb));
    },
    setProjectBreadcrumb(breadcrumb: any[]) {
      this.project.breadcrumb = breadcrumb;
      console.log('设置项目面包屑', breadcrumb);
      // setSessionStorageData('projectBreadcrumb', breadcrumb);
    },
    setPersonalTableLoading(loading: boolean) {
      this.personal.tableLoading = loading;
    },
    setProjectTableLoading(loading: boolean) {
      this.project.tableLoading = loading;
    },
    pushBreadcrumb(newItem: any) {
      // 虽然点击事件做了防抖，这里还是需要逻辑控制一下，如果已经push进来的，就不要重复push了
      if (!this.personal.breadcrumb.find((item) => item.id === newItem.id)) {
        this.personal.breadcrumb.push(newItem);
        // setSessionStorageData('personalBreadcrumb', this.personal.breadcrumb);
      }
    },
    pushProjectBreadcrumb(newItem: any) {
      // 虽然点击事件做了防抖，这里还是需要逻辑控制一下，如果已经push进来的，就不要重复push了
      if (
        !this.project.breadcrumb.find(
          (item) => item.id === (newItem.folderId || newItem.id)
        )
      ) {
        this.project.breadcrumb.push({
          ...newItem,
          id: newItem.folderId || newItem.id,
          name: newItem.folderName || newItem.name,
        });
        // setSessionStorageData('projectBreadcrumb', this.project.breadcrumb);
      }
      // setSessionStorageData('projectBreadcrumb', this.project.breadcrumb);
    },
    groupCheckedRowsByProject(checkedRows: any[]) {
      const resultMap = new Map();

      checkedRows.forEach((row) => {
        const projectId = row.projectId;
        const teamId = row.teamId;
        // 以 projectId 为 key，分组
        if (!resultMap.has(projectId)) {
          resultMap.set(projectId, {
            sourceProjectId: projectId,
            sourceTeamId: teamId,
            sourceFileIdList: [],
            sourceFolderIdList: [],
          });
        }
        const group = resultMap.get(projectId);
        if (row.type === 'folder') {
          group.sourceFolderIdList.push(row.folderId);
        } else if (row.type === 'file') {
          group.sourceFileIdList.push(row.fileId);
        }
      });

      // 返回分组后的数组
      return Array.from(resultMap.values());
    },
    groupCheckedRowsByProjectChildren(checkedRows: any[]) {
      if (!checkedRows.length) return [];
      const { projectId, teamId } = checkedRows[0];
      const sourceFileIdList: number[] = [];
      const sourceFolderIdList: number[] = [];
      checkedRows.forEach((row) => {
        if (row.folderId) {
          sourceFileIdList.push(row.id);
        } else {
          sourceFolderIdList.push(row.id);
        }
      });
      return [
        {
          sourceProjectId: projectId,
          sourceTeamId: teamId,
          sourceFileIdList,
          sourceFolderIdList,
        },
      ];
    },
    // 删除文件引用
    async deleteProjectItems(checkedRows: any[], t: (key: string) => string) {
      // 构造请求参数
      // 准备一个数组，然后遍历checkedRows，如果遇到projectId不同，就将其添加到数组中
      let params = [];
      if (this.project.currentFolder.id === 'project') {
        params = this.groupCheckedRowsByProject(checkedRows) || [];
      } else {
        params = this.groupCheckedRowsByProjectChildren(checkedRows) || [];
      }

      try {
        const res: any = await unrelateFolder(params);
        if (res.code === 8000000) {
          Notification.success({
            id: 'delete',
            title: 'Success',
            content: t('file-manage.success'),
          });
          // 删除之后的刷新
          if (this.project.currentFolder.id === 'project') {
            this.getProjectFiles();
          }
          // this.getPersonalFolder('project');
          // this.getfiles('project');
          // 删除成功后，可以调用一个刷新方法，如重新获取当前文件夹数据
          // this.fetchFolderContents(currentFolder.id);
        }
      } catch (error) {
        console.error('请求错误', error);
      }
    },
    async deleteItems(
      checkedRows: any[],
      t: (key: string) => string,
      type: string
    ) {
      // 构造请求参数
      const params: {
        folderIds: string[];
        fileIds: string[];
        projectId?: string;
      } = {
        folderIds: [],
        fileIds: [],
        projectId: this.personal.projectId,
        // 如果有选中项，取第一个的 teamId
        // targetTeamId: checkedRows[0]?.teamId,
      };
      console.log('删除的选中项', checkedRows);
      checkedRows.forEach((row: any) => {
        // 根据你的原始逻辑，若 row.folderId 存在，则认为这是文件，否则为文件夹
        if (row.folderId) {
          params.fileIds.push(row.id);
        } else {
          params.folderIds.push(row.id);
        }
      });

      try {
        const res: any = await deleteApi(params);
        if (res.code === 8000000) {
          Notification.success({
            id: 'delete',
            title: 'Success',
            content: t('file-manage.success'),
          });
          this.getPersonalFolder(type);
          this.getfiles(type);
          // 删除成功后，可以调用一个刷新方法，如重新获取当前文件夹数据
          // this.fetchFolderContents(currentFolder.id);
        }
      } catch (error) {
        console.error('请求错误', error);
      }
    },
    async getFolder(type: string, parentId: string) {
      return getChildFolderList(this.personal.projectId, '', type, parentId);
    },
    async getProjectFolder(type: string, parentId: string) {
      console.log('projectId', this.project.currentFolder.projectId);
      console.log('type', type);
      console.log('parentId', parentId);
      return getChildFolderList(
        this.project.currentFolder.projectId,
        '',
        type,
        parentId
      );
    },
    async getPersonalFolder(type: string) {
      if (type === 'personal') {
        this.setPersonalTableLoading(true);
        const res = await this.getFolder(
          this.personal.currentFolder?.type ?? '',
          this.personal.currentFolder?.id
        );
        if (res.status) {
          const list = (res.data?.list || []).map((item) => ({
            ...item,
            selected: false,
          }));
          // 如果this.personal.currentFolder?.parentId==='0',则查出来的个人文件去掉不显示
          this.setPersonalfolderList(list);
        }
        this.setPersonalTableLoading(false);
      } else {
        // 项目文件，下级文件
        console.log('点击的面包屑信息111111', this.project.currentFolder);
        const res = await this.getProjectFolder(
          'WIP',
          this.project.currentFolder?.id || this.project.currentFolder?.folderId
        );
        if (res.status) {
          const list = (res.data?.list || []).map((item) => ({
            ...item,
            selected: false,
            editName: item.name,
            editFileType: false,
            projectName: this.project.currentFolder?.projectName || '',
            disabled: isSysFolder(item.sysType),
          }));
          // 如果this.personal.currentFolder?.parentId==='0',则查出来的个人文件去掉不显示
          this.setFolderChildrenFolders(list);
        } else {
          this.setFolderChildrenFolders([]);
        }
      }
    },
    async getfiles(type: string) {
      this.setPersonalTableLoading(true);
      const id =
        type === 'personal'
          ? this.personal.currentFolder.id
          : this.project.currentFolder.id ||
            this.project.currentFolder.folderId;
      console.log(this.project.currentFolder, '3333');
      const res: any = await getFileList(id);
      if (res.status) {
        const list = (res.data?.list || []).map((item: any) => {
          const idx = item.name.lastIndexOf('.');
          return {
            ...item,
            selected: false,
            editName: item.name.slice(0, idx),
            editFileType: item.name.slice(idx),
          };
        });

        if (type === 'personal') {
          this.setPersonalFileList(list);
        } else {
          this.setFolderChildrenFiles(list);
        }
      }
      this.setPersonalTableLoading(false);
    },
    async getProjectFiles() {
      this.setProjectTableLoading(true);
      const res: any = await getProjectFileList();
      if (res.status) {
        (res.data || []).forEach((item) => {
          if (Array.isArray(item.folderVOList)) {
            item.folderVOList.forEach((vo) => {
              vo.selected = false;
              vo.id = vo.fileId || vo.folderId;
              vo.name = vo.folderName || vo.fileName;
              vo.projectId = item.projectId;
              vo.projectName = item.projectName;
              const segments =
                vo.type === 'folder'
                  ? vo.idPath.split('/').filter(Boolean)
                  : []; // 移除空字符串
              const parentFolderId = segments[segments.length - 2]; // 倒数第 2 项
              vo.parentId = vo.type === 'file' ? vo.folderId : parentFolderId;
              vo.editName =
                vo.type === 'folder'
                  ? vo.folderName
                  : vo.fileName.slice(0, vo.fileName.lastIndexOf('.'));
              vo.editFileType =
                vo.type === 'folder'
                  ? false
                  : vo.fileName.slice(vo.fileName.lastIndexOf('.'));
            });
          }
        });

        this.setProjectTableData(res.data);
      }
      this.setProjectTableLoading(false);
    },
    async downloadFile(type: string) {
      if (type === 'personal') {
        this.selectedPersonalItems.forEach((row) => {
          downloadSource(row);
        });
      }
      this.selectedProjectItems.forEach((row) => {
        downloadSource(row);
      });
    },
    setSelectedByRowKeys(rowKeys: string[]) {
      this.personal.folderList.forEach((item: any) => {
        item.selected = rowKeys.includes(item.id);
        console.log('设置选中行的keys', item);
      });
      this.personal.fileList.forEach((item: any) => {
        item.selected = rowKeys.includes(item.id);
      });
    },
    setProjectSelectedByRowKeys(rowKeys: string[]) {
      if (this.project.currentFolder.id === 'project') {
        console.log('项目列表勾选的id', rowKeys);
        this.project.tableData.forEach((item) => {
          if (Array.isArray(item.folderVOList)) {
            item.folderVOList.forEach((vo) => {
              vo.selected = rowKeys.includes(vo.id);
            });
          }
        });
        console.log('项目列表选中的项信息', this.projectAllItems);
        console.log('项目列表选中的项信息', this.selectedProjectItems);
      } else {
        this.project.folderChildrenFolders.forEach((item: any) => {
          item.selected = rowKeys.includes(item.id);
          console.log('设置选中行的keys', item);
        });
        this.project.folderChildrenFiles.forEach((item: any) => {
          item.selected = rowKeys.includes(item.id);
        });
      }
    },
  },
});

export default useKnowledgeBaseNewStore;
