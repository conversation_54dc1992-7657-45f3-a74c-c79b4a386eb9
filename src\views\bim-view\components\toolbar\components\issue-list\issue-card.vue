<template>
  <a-card hoverable :class="{ 'card-shadow': shadow }" style="width: 372px">
    <a-row class="grid-row">
      <a-col flex="76px">
        <div class="row-title">{{ $t('model-viewer.issueTitle') }}</div>
      </a-col>
      <a-col flex="auto">
        <a-typography-paragraph
          :ellipsis="{
            rows: 1,
            showTooltip: true,
          }"
          >{{ issueDetail.title }}
        </a-typography-paragraph>
      </a-col>
    </a-row>
    <template v-if="type === 'default'">
      <a-row v-if="issueDetail.message" class="grid-row">
        <a-col flex="76px">
          <div class="row-title">{{ $t('model-viewer.issueDescription') }}</div>
        </a-col>
        <a-col flex="auto">
          <a-typography-paragraph
            :ellipsis="{
              rows: 1,
              showTooltip: true,
            }"
          >
            {{ issueDetail.message }}
          </a-typography-paragraph>
        </a-col>
      </a-row>
      <a-row class="grid-row">
        <a-col flex="76px">
          <div class="row-title">{{ $t('model-viewer.creator') }}</div>
        </a-col>
        <a-col flex="82px">
          <a-typography-paragraph
            :ellipsis="{
              rows: 1,
              showTooltip: true,
            }"
            ><span
              v-if="issueDetail.createBy === userName"
              style="color: rgb(var(--primary-6))"
            >
              {{ issueDetail.creater }}</span
            >
            <span v-else> {{ issueDetail.creater }}</span>
          </a-typography-paragraph>
        </a-col>
        <a-col flex="76px">
          <div class="row-title">{{ $t('model-viewer.issueStatus') }}</div>
        </a-col>
        <a-col flex="76px">
          <a-tag v-if="issueDetail.state === 0" bordered color="red">
            {{ $t('model-viewer.unresolved') }}
          </a-tag>
          <a-tag v-if="issueDetail.state === 1" bordered color="green">
            {{ $t('model-viewer.resolved') }}
          </a-tag>
          <a-tag v-if="issueDetail.state === 2" bordered color="blue">
            {{ $t('model-viewer.inProgress') }}
          </a-tag>
        </a-col>
      </a-row>
      <a-row class="grid-row">
        <a-col flex="76px">
          <div class="row-title">{{ $t('model-viewer.createTime') }}</div>
        </a-col>
        <a-col flex="auto">
          {{ issueDetail.createDate }}
        </a-col>
      </a-row>
      <a-row class="grid-row-other">
        <a-col flex="76px">
          <div class="row-title">{{ $t('model-viewer.screenshot') }}</div>
        </a-col>
        <a-col flex="auto">
          <a-image
            v-if="issueDetail.issueFileItems[0].picToken"
            width="68"
            height="68"
            :src="issueDetail.picUrl"
            @click="setView"
            @preview-visible-change="viewChange"
          ></a-image>
          <span v-else>无</span>
        </a-col>
      </a-row>
      <a-row class="grid-row">
        <a-col flex="76px">
          <div class="row-title">{{ $t('model-viewer.published-status') }}</div>
        </a-col>
        <a-col flex="auto">
          <a-switch
            v-model="issueDetail.publishStatus"
            :checked-value="1"
            :unchecked-value="0"
            size="small"
            :disabled="
              issueDetail.createBy !== userName && userStore.admin !== 0
            "
            @change="setStatus(issueDetail)"
          />
        </a-col>
      </a-row>
    </template>
    <a-row v-else class="grid-row-other">
      <a-col flex="76px">
        <div class="row-title">
          <span>{{ $t('model-viewer.issueStage') }}</span>
        </div>
      </a-col>
      <a-col flex="90px">
        <a-typography-paragraph
          :ellipsis="{
            rows: 1,
            showTooltip: true,
          }"
        >
          <span v-if="issueDetail.stage === 1">{{
            $t('model-viewer.shareBefore')
          }}</span>
          <span v-if="issueDetail.stage === 2">{{
            $t('model-viewer.share')
          }}</span>
          <span v-if="issueDetail.stage === 3">{{
            $t('model-viewer.deliveryBefore')
          }}</span>
          <span v-if="issueDetail.stage === 4">{{
            $t('model-viewer.delivery')
          }}</span>
        </a-typography-paragraph>
      </a-col>
      <a-col flex="76px">
        <div class="row-title">
          <span>{{ $t('model-viewer.type') }}</span>
        </div>
      </a-col>
      <a-col flex="90px">
        <a-typography-paragraph
          :ellipsis="{
            rows: 1,
            showTooltip: true,
          }"
        >
          <span v-if="issueDetail.type === 1">{{
            $t('model-viewer.collisionDetection')
          }}</span>
          <span v-if="issueDetail.type === 2">{{
            $t('model-viewer.violateStandard')
          }}</span>
          <span v-if="issueDetail.type === 3">{{
            $t('model-viewer.ownerStandard')
          }}</span>
        </a-typography-paragraph>
      </a-col>

      <a-col flex="76px">
        <div class="row-title">
          <span>{{ $t('model-viewer.recipient') }}</span></div
        >
      </a-col>
      <a-col flex="260px">
        <a-typography-paragraph
          v-for="item in issueDetail.issueRecipientList"
          :key="item.id"
          :ellipsis="{
            rows: 1,
            showTooltip: true,
          }"
          >{{ item.userName }}
        </a-typography-paragraph>
      </a-col>
      <a-col flex="76px">
        <div class="row-title">{{ $t('model-viewer.operationLogs') }}</div>
      </a-col>
      <a-col flex="auto">
        <a-timeline style="margin-top: 8px">
          <a-timeline-item
            :label="issueLogList[issueLogList.length - 1].createDate"
          >
            <div
              style="cursor: pointer"
              @click="
                () => showIssueReply(issueLogList[issueLogList.length - 1])
              "
            >
              {{ issueLogList[issueLogList.length - 1].createUsername || '' }}
            </div>
            <div
              style="
                width: 220px;
                word-break: break-word;
                white-space: pre-line;
              "
              @click="
                () => showIssueReply(issueLogList[issueLogList.length - 1])
              "
              >{{ issueLogList[issueLogList.length - 1].reply || '' }}</div
            >
          </a-timeline-item>
        </a-timeline>
      </a-col>
    </a-row>
    <div class="list-footer">
      <template v-if="type === 'default'">
        <a-tooltip :content="$t('model-viewer.view')">
          <icon-eye class="icon" @click="setView" />
        </a-tooltip>
        <a-tooltip :content="$t('model-viewer.reply')">
          <icon-message class="icon" @click="() => showIssueReply()" />
        </a-tooltip>
        <a-tooltip :content="$t('model-viewer.details')">
          <icon-bookmark class="icon" @click="showOperating('operating')" />
        </a-tooltip>
      </template>
      <a-tooltip v-else :content="$t('model-viewer.return')">
        <icon-undo class="icon" @click="showOperating('default')" />
      </a-tooltip>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { onMounted, ref, toRefs, watch } from 'vue';
  import { isEmpty } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  // import { getViewer } from '@/utils/modelViewer/index';
  // import useModelToolsStore from '@/store/modules/model-viewer';
  import {
    IssuesDetail,
    queryissueLog,
    IssueLog,
    setPublishedStatus,
  } from './api';
  import { useUserStore } from '@/store';
  import { useI18n } from 'vue-i18n';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const { t } = useI18n();
  const userStore = useUserStore();
  const userName = ref(userStore.username);
  const props = defineProps<{
    shadow?: boolean;
    issueDetail: IssuesDetail;
    issueReplyModel: string;
    viewer: any;
  }>();

  const { shadow } = toRefs(props);
  const emits = defineEmits<{
    (e: 'setShadowKey', value: any): void;
    (e: 'setReplyIssue', value: IssuesDetail): void;
    (e: 'setIssueReplyModel', value: string): void;
    (e: 'showReply'): void;
    (e: 'setIssueReplyData', value: any): void;
  }>();
  // const modelToolsStore = useModelToolsStore();

  interface dimension {
    nodeId: {
      dbId: number;
      modelId: number;
    };
    viewPoint: object;
    imageLocation: {
      startX: number;
      startY: number;
      width: number;
      height: number;
    };
  }

  const type = ref('default');
  const dimensionString = ref<dimension>({
    nodeId: {
      dbId: 0,
      modelId: 0,
    },
    viewPoint: {},
    imageLocation: {
      startX: 0,
      startY: 0,
      width: 0,
      height: 0,
    },
  });
  const width = ref();
  const height = ref();
  const left = ref('0px');
  const top = ref('200px');
  const issueLogList = ref<IssueLog[]>([]);
  const getIssueLog = async (issueId: string) => {
    try {
      const res = await queryissueLog(issueId);
      if (res.status) {
        issueLogList.value = res.data.list;
      }
    } catch (e) {
      console.log(e);
    }
  };

  const showOperating = async (value: string) => {
    if (value === 'operating') {
      await getIssueLog(props.issueDetail.id);
      type.value = value;
    } else {
      type.value = value;
    }
  };

  const objectsColor = ref(false);
  const setView = async (value?: string) => {
    const { issueId } = router.currentRoute.value.query; // 获取当前页的issueId
    const dimensionObj = JSON.parse(
      props.issueDetail?.issueFileItems[0]?.dimensionString || '{}'
    );
    if (!isEmpty(dimensionObj)) {
      objectsColor.value = true;
      // 构件镜头
      // if (dimensionObj.elementId) {
      //   console.log('[ dimensionObj ] 296>', dimensionObj);
      //   // 有构件id用构件id定位
      //   const guid = (await props.viewer?.searchEntityId(
      //     dimensionObj.elementId
      //   )) as string;
      //   if (value === 'issue') {
      //     console.log('[ issue-guid ] >', guid);
      //     if (issueId === props.issueDetail.id) {
      //       props.viewer?.selectEntities(guid);
      //     }
      //   } else {
      //     console.log('[ guid ] >', guid);
      //     props.viewer?.selectEntities(guid);
      //   }
      // }

      if (!isEmpty(dimensionObj.viewPoint)) {
        // 没有构件id用截图时的摄像机位置定位
        props.viewer?.setCamera(dimensionObj.viewPoint);
      } else if (!value) {
        Message.warning(t('model-viewer.missingViews'));
      }
      // 构件高亮
      if (value === 'issue') {
        if (issueId === props.issueDetail.id) {
          props.viewer?.setObjectsColor(dimensionObj.nodeInfo);
          emits('setShadowKey', props.issueDetail.id);
        }
      } else {
        console.log('[ dimensionObj ] >', dimensionObj);
        props.viewer?.setObjectsColor(dimensionObj.nodeInfo);
        emits('setShadowKey', props.issueDetail.id);
      }
    } else {
      Message.warning(t('model-viewer.missingViews'));
    }
  };

  watch(
    () => objectsColor.value,
    (value: boolean) => {
      if (value === true) {
        const dimensionObj = JSON.parse(
          props.issueDetail?.issueFileItems[0]?.dimensionString || '{}'
        );
        // props.viewer?.clearObjectsColor(dimensionObj.nodeInfo);
        objectsColor.value = false;
      }
    }
  );

  const viewChange = () => {
    // width.value = `${dimensionString.value?.imageLocation.width}px`;
    // height.value = `${dimensionString.value?.imageLocation.height}px`;
    // left.value = `${dimensionString.value?.imageLocation.startX}px`;
    // top.value = `${dimensionString.value?.imageLocation.startY}px`;
    // console.log(width.value, height.value, left.value, top.value);
  };

  const showIssueReply = (data?: IssueLog) => {
    if (data?.reply !== null) {
      emits('showReply');
      if (data) {
        emits('setIssueReplyModel', 'view');
        // modelToolsStore.setIssueReplyData(data);
        emits('setIssueReplyData', data);
      } else {
        emits('setIssueReplyModel', 'view');
        // modelToolsStore.setIssueReplyData({});
        emits('setIssueReplyData', {});
      }
      emits('setReplyIssue', props.issueDetail);
    }
  };

  const init = () => {
    if (props.issueDetail.issueFileItems[0].dimensionString) {
      dimensionString.value = JSON.parse(
        props.issueDetail.issueFileItems[0].dimensionString || ''
      );
      // setView('issue');
      left.value = `0px`;
      top.value = `200px`;
    }
  };

  /**
   * 设置发布状态
   */
  const setStatus = async (data: any) => {
    try {
      const params = {
        issueId: data.id,
        publishStatus: data.publishStatus,
      };
      const res: any = await setPublishedStatus(params);
      if (res.status) {
        Message.success(res.message);
      }
    } catch (error) {
      console.log('[ error ] >', error);
    }
  };

  onMounted(() => {
    init();
  });
</script>

<style lang="less" scoped>
  .card-shadow {
    box-shadow: 0 0 12px rgb(0 0 0 / 25%);
  }
  .grid-row {
    height: 28px;
    line-height: 28px;
    flex-wrap: nowrap;
    .row-title {
      color: var(--color-text-3);
    }
    :deep(.arco-typography) {
      height: 28px;
      line-height: 28px;
    }
  }
  .grid-row-other {
    line-height: 28px;
    .row-title {
      color: var(--color-text-3);
    }
    :deep(.arco-image-error-alt) {
      padding: 0;
    }
    :deep(.arco-typography) {
      height: 28px;
      line-height: 28px;
    }
  }
  :deep(.arco-typography) {
    margin-bottom: 0;
  }

  .list-footer {
    width: 100%;
    text-align: right;
    padding-right: 12px;
    .icon {
      font-size: 20px;
      cursor: pointer;
      margin-right: 8px;
    }
    .icon:last-child {
      margin-right: 0;
    }
  }
</style>

<style scoped lang="less">
  .arco-image-preview-img-container .arco-image-preview-img {
    position: absolute;
    left: v-bind(left);
    top: v-bind(top);
    width: v-bind(width);
    height: v-bind(height);
  }
</style>
