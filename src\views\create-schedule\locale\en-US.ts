export default {
  'schedule.basicInfo': 'Basic Information',
  'schedule.assignedTo': 'Assigned To',
  'schedule.selectAssignee': 'Please select an assignee',
  'schedule.selectPerson': 'Please select person',
  'schedule.matter.starttime': 'StartTime',
  'schedule.matter.deadline': 'Deadline',
  'schedule.selectStartTime': 'Please select start time',
  'schedule.selectDeadline': 'Please select a deadline',
  'schedule.calendarBelong': 'Calendar Belonging',
  'schedule.selectCalendar': 'Please select a calendar',
  'schedule.description': 'Description',
  'schedule.placeholder': 'Please enter',
  'schedule.subTaskList': 'Sub-task List',
  'schedule.aiSubTaskList': 'AI-generated Sub-task List',
  'schedule.add': 'Add',
  'schedule.inputTitle': 'Please enter a title',
  'schedule.delete.confirm': 'Are you sure you want to delete this item?',
  'schedule.attachment': 'Attachment',
  'schedule.uploadAttachment': 'Upload Attachment',
  'schedule.comment': 'Comment',
  'schedule.delete.comment': 'Are you sure you want to delete this comment?',
  'schedule.noComment': 'No comments yet',
  'schedule.placeholder.comment':
    'Please enter a comment and press Enter to send',
  'schedule.matter.createMatter': 'Create Matter',
  'schedule.matter.saveChanges': 'Save Changes',
  'schedule.comment.success': 'Comment successful',
  'schedule.comment.placeholder': 'Please enter a comment',
  'schedule.deadline.error': 'Deadline cannot be earlier than the current time',
  'schedule.deadline.create.error':
    'Deadline cannot be earlier than the creation time',
  'schedule.subTaskList.placeholder': 'Please enter sub-task list',
  'schedule.calendar.edit': 'Edit successful',
  'schedule.calendar.edit.error': 'Edit failed',
  'schedule.matter.description.placeholder': 'Please enter matter description',
  'schedule.delete.success': 'Delete successful',

  'schedule.delete.push-to-Jiaojiantong': 'Whether to push to Jiaojiantong',
  'schedule.go-to': 'Go to',
  'schedule.save-modifications':
    'The edited content on the current page has not been saved. May I ask if you want to save the modifications?',
  'schedule.save-as': 'Save as',
  'schedule.milestoneId': 'Milestone',
  'schedule.selected-milestoneId': 'Please select a milestone',
  'schedule.milestoneId-notips': 'Milestone cannot be empty',
  'schedule.rateProgress': 'Progress',
  'schedule.matter.dependency': 'Task Dependency',
  'schedule.project-task': 'Task',
  'schedule.relyType': 'Type',
  'schedule.relyType-tips': 'Please select a type',
  'schedule.relyType-save': 'Save',
  'schedule.task-placeholder': 'Please select',
  'schedule.no-available-task': 'No available tasks to bind!',
  'schedule.save-success': 'Save successful',
  'schedule.delete-dependency':
    'Are you sure you want to delete this task dependency?',
  'schedule.no-relyType': 'No dependent task selected',
  'schedule.dependency.placeholder': 'Please complete the dependent task',
};
