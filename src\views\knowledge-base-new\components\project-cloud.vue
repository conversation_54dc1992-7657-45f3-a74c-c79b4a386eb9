<template>
  <div class="card-page">
    <PathAndOperations
      :options="operations"
      @new-person-item-added="onNewItemAdded"
      @person-handle-upload="handleUpload(0)"
      @person-handle-download="handleDownload"
    />
    <card v-if="props.isCard" ref="cardRef" @handle-list="handleList"></card>
    <list v-if="!props.isCard" ref="listRef" @handle-list="handleList"></list>
  </div>
  <UploadModal
    :visible="uploadModel.visible"
    :visible-type="uploadModel.type"
    :selected-folder="uploadModel.selectedFolder"
    @upload-single-success="singleFileSuccessCallback"
    @handle-cancel="uploadModel.visible = false"
    @upload-complete="finishUpLoad"
    @start-upload="startUpload"
  />
  <TransmitPanel
    v-model:visible="TransmitPanelVisible"
    :position="{
      top: 120,
      right: 60,
    }"
    :transmit-type="transmitType"
  />
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import { ref, toRefs, onMounted, defineEmits, defineProps } from 'vue';
  import i18n from '@/locale/index';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import PathAndOperations from '@/views/knowledge-base-new/components/project-options.vue';
  import uploadIcon from '@/assets/images/knowledge-base/upload2.svg';
  import UploadModal from '@/views/projectSpace/file/components/upload-modal.vue';
  import { addMergaFile } from '@/api/upload-file';
  import TransmitPanel from '@/views/projectSpace/file/components/transmit-panel/index.vue';
  import { finishTaskParams } from '@/store/modules/upload-file/types';
  import {
    addChildFolder,
    updateFolder,
    updateFile,
  } from '@/views/projectSpace/file/api';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import { useThrottleFn } from '@vueuse/core';
  import { Message } from '@arco-design/web-vue';
  import { FolderMessage } from '@/api/tree-folder';
  import modelViewBim from '@/utils/common/view';
  import { isWpsFile } from '@/views/projectSpace/file/utils';
  import { wpsViewHandle } from '@/hooks/wps';
  import { encode } from 'js-base64';
  import FolderActionDropdown from './folder-action-dropdown.vue';
  import card from './project-card.vue';
  import list from './project-list.vue';
  import { options } from '@fullcalendar/core/preact';

  const { t } = i18n.global;
  const props = defineProps({
    isCard: {
      type: Boolean,
      default: true,
    },
  });
  const emit = defineEmits(['refreshFolder']);
  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { personal, personalCombinedList } = storeToRefs(knowledgeBaseNewStore);
  const { currentFolder, folderList, breadcrumb, projectId } = toRefs(
    personal.value
  );
  const operations = ref([]);
  const popupVisible = ref(false);
  const uploadModel = ref({ visible: false, type: 0, selectedFolder: {} });
  const TransmitPanelVisible = ref(false);
  const transmitType = ref('upload');
  function handleUpload(visibleType: number) {
    uploadModel.value = {
      type: visibleType,
      visible: true,
      selectedFolder: currentFolder,
    };
  }
  async function singleFileSuccessCallback(params: finishTaskParams) {
    await addMergaFile(params)
      .catch((err) => {
        // this.changeFileArrStatus(item, 1);
      })
      .then((res: any) => {
        // 事项中成功需要给出结果
        // this.mattersSaveList.push(res.data);
        emit('refreshFolder');
        console.log('上传接口');
      })
      .finally(() => {});
  }
  // 上传完成通知列表刷新
  const finishUpLoad = () => {
    console.log('上传完成');
  };
  const startUpload = () => {
    transmitType.value = 'upload';
    TransmitPanelVisible.value = true;
    uploadModel.value.visible = false;
  };
  const handleDownload = () => {
    transmitType.value = 'download';
    TransmitPanelVisible.value = true;
  };
  // 点击进入下一文件夹
  const onSelect = (item: any) => {
    // 如果点击文件夹，进入下一文件夹，如果点击文件，则进入预览文件
    if (!item.folderId) {
      // 文件夹点击事件，进入下一层
      knowledgeBaseNewStore.setPersonCurrentFolder(item);
      knowledgeBaseNewStore.pushBreadcrumb(item);
      knowledgeBaseNewStore.getPersonalFolder('project');
      knowledgeBaseNewStore.getfiles('project');
      return;
    }
    const needParams = {};
    if (item.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: item.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      Object.assign(needParams, params);
    }

    modelViewBim(item, projectId as string, needParams);
  };
  const cardRef = ref();
  const listRef = ref();
  // 添加文件夹
  const onNewItemAdded = () => {
    // 接收到了添加文件夹的需求，区分通知哪个card/list组件
    if (props.isCard) {
      cardRef.value.focusInput();
    } else {
      listRef.value.focusInput();
    }
  };
  // 添加文件夹请求
  const addFolderRequest = useThrottleFn(async (item: any) => {
    const name = item.editName;
    // 如果名称为空则认为撤销新增，删除列表中的该项
    if (name.trim() === '') {
      const index = folderList.value.findIndex((i: any) => i === item);
      if (index !== -1) {
        folderList.value.splice(index, 1);
      }
      // 可显示提示信息，例如：Message.info('撤销新增');
      return;
    }

    // 禁用字符校验，正则：不允许出现 \ / : * ? " < > |
    const pattern = /^[^\\/:*?"<>|]+$/;
    if (!pattern.test(name)) {
      Message.warning(t('file-manage.name-exclude-2'));
      return;
    }
    if (item.isAdd) {
      // 调用新增文件夹接口
      const res: any = await addChildFolder(item);
      if (res.code === 8000000) {
        Message.success(t('file-manage.success'));
        // 新增成功后取消 isAdd 标识
        item.isAdd = false;
      } else {
        Message.error(res.data);
      }
    }
    if (item.isEdit) {
      const param: any = {
        ...item,
        name: !item.editFileType
          ? encode(item.editName)
          : item.editName + item.editFileType,
      };
      delete param.path;
      const res: any = item.folderId
        ? await updateFile(param)
        : await updateFolder(param);
      if (res.code === 8000000) {
        Message.success(t('file-manage.success'));
        item.isEdit = false;
      }
    }
    knowledgeBaseNewStore.getPersonalFolder('project');
    knowledgeBaseNewStore.getfiles('project');
  }, 1000);
  const handleList = (handleType: string, item: any) => {
    switch (handleType) {
      case 'upload':
        handleUpload(0);
        break;
      case 'add':
        addFolderRequest(item);
        break;
      case 'rename':
        addFolderRequest(item);
        break;
      default:
        break;
    }
  };
  onMounted(() => {});
</script>

<style scoped lang="less">
  .card-page {
    position: absolute;
    height: calc(100% - 40px);
    width: 100%;
  }
  :deep(.has-pointer) {
    cursor: pointer;
  }
  .card-list {
    height: calc(100% - 64px);
    overflow: auto;
    display: flex;
    flex-wrap: wrap; // 允许换行
    gap: 20px;
    align-content: flex-start; /* 关键 */
    .file-card {
      width: 133px;
      height: 130px;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 0;
      &:hover {
        background: #e8f2ff;
        border: 1px solid #d9d9d9;
        .hover-show {
          visibility: visible;
          opacity: 1;
        }
      }
      .hover-show.always-show {
        visibility: visible;
        opacity: 1;
      }
      .hover-show {
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      .file-card-header {
        width: 133px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 8px 0 8px;
      }
      .folder-image {
        width: 60px;
        height: 60px;
      }
      .file-icon {
        width: 60px;
        height: 60px;
        margin-top: 8px;
        background: rgba(242, 243, 245, 0.8);
        border-radius: 8px 8px 8px 8px;
        border: 1px dashed #e5e6eb;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 13px;

        .upload-icon {
          width: 20px;
          height: 18px;
        }
      }
      .file-name {
        font-size: 14px;
        color: #1d2129;
        line-height: 22px;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏超出容器的内容 */
        text-overflow: ellipsis;
        max-width: 133px;
        width: 100%;
        display: block;
        word-break: keep-all;
        word-break: break-word;
        text-align: center;
      }
    }
    .icon-name {
      display: flex;
      flex-direction: column; /* 上下排列 */
      align-items: center; /* 水平居中 */
    }
    .icon-name > div {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    .selected {
      background: #e8f2ff;
      border: 1px solid #d9d9d9;
    }
  }
</style>
