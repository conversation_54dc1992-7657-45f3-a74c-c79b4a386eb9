import axios from 'axios';
import { Paging } from '@/types/global';
// 团队列表-人员信息
export type UserRecord = {
  id?: string;
  name?: string;
  username?: string;
  role?: number;
  accountState?: number;
};

// 团队列表-团队信息
export interface TeamRecord {
  id?: string;
  name: string;
  englishName: string;
  projectId: string;
  parentId?: string;
  level?: string;
  color: string;
  role: number;
  count: number;
  teamUsers?: Array<UserRecord>;
  userIds?: string; // 用于选人组件的临时字段
  isShowColor?: boolean; // 是否显示修改颜色弹窗
  isShowTooltip?: boolean; // 是否显示名称tooltip
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  defaultTeam: number | null;
  isInherit: number; // 是否从属关系，1有 0无
}

// 分页查询参数
export interface TeamSearchParams {
  pageNo: number;
  pageSize: number;
  projectId: string;
  name?: string;
}
// 新增文件夹操作
export interface FolderMessage {
  id?: string;
  name?: string;
  title?: string;
  projectId?: string;
  teamId?: string;
  userId?: string;
  parentId?: number;
  childId?: string;
  children?: [];
  isFileOrFolder?: number;
  type?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  sysType?: number;
}
export interface FileMessage {
  id?: string;
  name?: string;
  projectId?: string;
  teamId?: string;
  folderId?: string;
  fileToken?: string;
  description?: string;
  size?: string;
  type?: string;
  version?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  isLocked?: boolean;
  status?: number;
}

// 查询团队列表
export function queryTeamList(params: TeamSearchParams) {
  return axios.get('/cde-collaboration/team/list', {
    params,
  });
}

// 查询团队详情
export function queryTeamDetail(params: object) {
  return axios.get('/cde-collaboration/team/detail', {
    params,
  });
}

// 新增团队
export function saveTeam(data: TeamRecord) {
  return axios.post('/cde-collaboration/team/save', data);
}

// 编辑团队
export function updateTeam(data: TeamRecord) {
  return axios.post('/cde-collaboration/team/update', data);
}

// 上移团队
export function upTeam(data: any) {
  return axios.post('/cde-collaboration/team/moveUp', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 下移团队
export function downTeam(data: any) {
  return axios.post('/cde-collaboration/team/moveDown', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 置顶团队
export function topTeam(data: any) {
  return axios.post('/cde-collaboration/team/moveTop', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 置底团队
export function bottomTeam(data: any) {
  return axios.post('/cde-collaboration/team/moveBottom', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 查询团队及其成员（查询收件人）
export function queryTeamListAll(id: any) {
  return axios.post(`/cde-collaboration/team/all-team?id=${id}`);
}

// 查询团队列表设置文件树数据
export function getTeamFileTree(teamId: string) {
  return axios.get('/cde-collaboration/team/query-code-stand', {
    params: {
      teamId,
    },
  });
}

// 查询团队的构件属性
export function getComponentAttribute(id: string, teamId: string) {
  return axios.get('/cde-collaboration/team/query-attribute', {
    params: {
      id,
      teamId,
    },
  });
}
export function getChildFolderList(
  projectId?: string,
  teamId?: string,
  type?: string,
  parentId = '0'
) {
  return axios.get<Paging<FolderMessage>>(
    '/cde-collaboration/folder/children',
    {
      params: {
        projectId,
        teamId,
        type,
        parentId,
        pageSize: 9999,
      },
    }
  );
}
export function getFileList(folderId?: string, fileName?: string) {
  if (folderId === undefined) return null;
  return axios.get<Paging<FileMessage>>('/cde-collaboration/file/list', {
    params: {
      folderId,
      fileName,
      pageSize: 9999,
    },
  });
}
export function getTeamFolder(teamId: string) {
  return axios.get('/cde-collaboration/folder/teamFolder', {
    params: {
      teamId,
    },
  });
}
// 移除项目成员
export function removeTeams(teamId: string) {
  return axios.get('/cde-collaboration/team/removeTeam', {
    params: {
      teamId,
    },
  });
}
