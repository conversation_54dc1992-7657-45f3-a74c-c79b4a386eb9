import { AppRouteRecordRaw } from '../types';
import { useUserStore } from '@/store';
import { Message } from '@arco-design/web-vue';

const USERCENTER: AppRouteRecordRaw = {
  path: 'user',
  name: 'user',
  component: () => import('@/views/user-center/index.vue'),
  beforeEnter: (to, from, next) => {
    const userStore = useUserStore();
    if (userStore?.admin === 0 || userStore?.admin === 3) {
      next();
    } else {
      Message.info('非管理员账号暂无权限');
    }
  },
  meta: {
    locale: 'menu.user-manage',
    requiresAuth: true,
    icon: 'icon-user-group',
    order: 6,
    hideInMenu: true,
    showAI: true,
    globalMode: ['project'],
  },
};

export default USERCENTER;
