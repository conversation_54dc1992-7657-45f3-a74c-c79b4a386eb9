<template>
  <a-dropdown
    v-model:popup-visible="popupVisible"
    position="bl"
    :hide-on-select="false"
    @select="handleSelect($event)"
  >
    <icon-more :size="14" style="color: #4e5969" />
    <template #content>
      <!-- 移动 -->
      <a-doption v-if="hasEditPermission" value="move">
        <template #icon>
          <icon-drag-arrow :size="16" style="color: #4e5969" />
        </template>
        <template #default>{{ t('knowledgenew.move') }}</template>
      </a-doption>
      <!-- 重命名 -->
      <a-doption v-if="hasEditPermission" value="rename">
        <template #icon>
          <icon-pen :size="16" style="color: #4e5969" />
        </template>
        <template #default>{{ t('knowledgenew.rename') }}</template>
      </a-doption>
      <!-- 下载源文件 -->
      <a-doption
        v-if="nodeData.type !== 'folder' && hasEditPermission"
        value="download"
      >
        <template #icon>
          <icon-download :size="16" style="color: #4e5969" />
        </template>
        <template #default>{{ t('knowledgenew.download') }}</template>
      </a-doption>
      <!-- 删除 -->
      <a-popconfirm
        :content="
          nodeData.type === 'folder'
            ? t('knowledgenew.delete-folder-confirm')
            : t('knowledgenew.delete-file-confirm')
        "
        type="info"
        position="right"
        @ok="handleDelete(nodeData, 'delete')"
      >
        <a-doption v-if="hasEditPermission" value="delete">
          <template #icon>
            <icon-delete :size="16" style="color: #4e5969" />
          </template>
          <template #default>{{ t('knowledgenew.delete') }}</template>
        </a-doption>
      </a-popconfirm>
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
  import { computed, PropType, toRefs, defineEmits, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { storeToRefs } from 'pinia';
  import { useKnowledgeBaseStore2 } from '@/store';
  import { Node } from '../../types';

  const { t } = useI18n();

  const knowledgeBaseStore2 = useKnowledgeBaseStore2();
  const { baseInfo } = storeToRefs(knowledgeBaseStore2);

  const props = defineProps({
    nodeData: {
      type: Object as PropType<Node>,
      required: true,
    },
  });

  const emits = defineEmits(['eventsHandle']);

  const { nodeData } = toRefs(props);

  const popupVisible = ref(false);

  // 是否有编辑权限
  const hasEditPermission = computed(() => {
    return (
      baseInfo.value?.type === 'PERSONAL' ||
      baseInfo.value?.owner ||
      baseInfo.value?.edit
    );
  });

  function handleDelete(data: any, event: string) {
    popupVisible.value = false;
    emits('eventsHandle', event, nodeData.value);
  }

  // dropdown事件委托
  const handleSelect = async (event: any) => {
    switch (event) {
      case 'move':
        popupVisible.value = false;
        emits('eventsHandle', event, nodeData.value);
        break;
      case 'rename':
        popupVisible.value = false;
        emits('eventsHandle', event, nodeData.value);
        break;
      case 'download':
        popupVisible.value = false;
        emits('eventsHandle', event, nodeData.value);
        break;
      default:
        break;
    }
  };
</script>
