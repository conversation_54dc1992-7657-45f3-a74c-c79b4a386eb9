import { MarkerType } from '../type';

declare const DX: any;

export const iconTypeXbase = {
  unsolvedImg:
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAAvCAYAAAB+OePrAAAAAXNSR0IArs4c6QAABYFJREFUWEftmEtslFUUx3/nzqMPoNJSKi0vEwqRV2g7XWA0sSYmECEBF0TjAlFoI1ETY1LXrG0MJppgWojoSsMCTDSBHSYkbjptEwQUSwIUKaG1xT6Y6TzukW/6YJjO9Pu+UheafpvJ5Jx7zu/+73fvOd8VnuJpatLg6CZ2AfuBRoVagaUKYwI3US4pnOvqkAtPkQaZ7+BIi+5B+Ryh1jWG0iPKsc6T8oOrbx4H35DPHdLiFWG+AI74Tqic/ivJ0ZunJe5nrC9IB7AyzFmF3U6SpSXwagRSabh0GR6MuacWOD+Y4HU/oL4gI836NcIhB6WqHI6/D8UhGIvDzXvw2fcw8tAdFDgZbZdmT57g/Z1sPKL71HBuOvAbr8CL22E8Ngnp/Dpqdv7uNTV7o+3ykxdvz0pGmrUboW466Gs7YUP1FGAcxmLQeweu3/GSFlTp7fqDzVyUlNsIT5ANzbpLhPPZwZ4th7qNkwpm1IxB3wDEE24pn7B7UtMTZKRZTyC8l5t+WSkUhSbBYhOQtr4AHWdP76Y3yBa9DGzzjeA2QOmJdki9m5snyIYWHXUqiVswv3anMnW1yzK3cZ4gIy2qboHma4+2iyuDq4OT/L+iZDc8Pn7mq1qecb9G22W7WzxPSkZatGNetdotu/JVtEOOurl5hdzzqBX70S2YX7squ720cZ4gadJgw0auiZe2zCupx+PHCecNEsj0jwuoZtqyv8djf+kZ0plRfYt2mPn0kTnqqnK6q0Pe8Sq6L0inn6wIc1am+kmvSbL9FM4P/Zv9pJMsAxrihEz1lX5ALZwcTvChn4bX1zuZC1N3RPcFhGPZ7VshYKcts8JHPR77x9w4vpY7H0SmjXO+FoWXbDqxzQTC2HQCY8I9Vug0cC56nQte+sZCk3xqyOzAKxs+nanxA12fLFjsBQvkwC5C+tmhc/kuKrmo5AIosLi7F0DETIj/l5KRFg39PU5JqaHYBikqChEmQSgVImAMQZvC3O1u65pWr6a+tSEgpI1iE0rSpkmVGBKxAIkiQzw2TOzqGfF031FQyS0HNFxSTlkiRVnIZL65Q27L19fV1jnts7ahtdHNXy2pFIyGg4zEhhkpBJ0DqfL8QSqWBqm0k2CzHlWWi1CpUCGGZdZSYgxLRAncjrZ9PAPZ2HpcFUepCVHGMIxgeaDKoAgP8sVOWcYSKQZ/+5YheHQ3PPXMQG49rBUBqAkKRbkBrLIKQ60xrEEpLqRQX2fbB1mQX86hZEyUPxVuoPTn8UsmQ/RdPiHDUxtHpe4Q602QFbNlY6UaXhChokDCUqBGlCrnGuZWtG3TtN/6SOt1hVGFQQy3USYKrMyQWH5BGMi1O6p3n5JbEmnRarXUzAogrFOhScDkBVQ2CGzNtt+Ktq3Jgpy5qVRIq3AN6C0QKw38jHI71z5u6Zf6d3WHGIK5RhEOqLCk0JKJsld4clz/1W+qErH74XBJVaJ6y9v3s8cqpFTm+HZX4ijfzVLTkioISYA353r/sNSKsFkg4LqLwSpcQbhRyFeVcVHO5IXccVBXB8KsyrfcwMvIHBBKkcA6oBJwrvCc/wFneZ1dDYypcB/lLkLBK38FK8rFfMsdi3M3s7vrD+t651iZNQuhCtg5x8ZxE9HVror7xpmOsv0tLQ+VsjbvoS1UW9hghNVAiWtmNwchbi13sPQa4V6ue1qIpyz9V07J0NQR9MTrLVsPUx5QVgZdDnMMy62lTCRz6BeJEM6eoCgJFdLWMm4MMbU4t8VDbod5WhiYhpsmm7MspsopK/JRFt0EzLUnICmWsYkgI0HvZbFwGqeWx8spKbUUkyYsQkicei6EgoJJK4GAPD5T04o1Qay1pIJJZwTJiSQJk2LioSX+zBJi0XZJepnYPxegRg+dLKSWAAAAAElFTkSuQmCC',
  resolvedImg:
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAuCAYAAABJcBuEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUjSURBVHgBrVi9cttGEN67ozzqzFROKsOdO9NVZvIjApQ84y7KE1h+AktPILFMJfoJLHVJFanzjEQSVCYz6sJSXaAurqJSQxE4f3sCwCOFX1I7I+Fwd7jvu9293T0Kqiju0G1KJbdJUps0tYQWDrqb8fCNFjpA3xjj/trd2uiz9zmosq4om/B2+Na5U3f7aG5bgFXkaC1c65YRySXAO1ZSHWDGB1pBBIneNJx2fc+/yR7PkHjXQzSduQFtVH3Cqg5FGMAkYzVR35Cip5GOWljNxRwXz0VNBdCGl6WNBwTeDN+0IhUxeLqI1rAvdhJG4XHeTmzpDDs78IV9IYRjdd/IUHpn3tk4l0C8838WwHtRFHWrANsSO+0+iO/aJKCJ17YmhP2BUorBnXS6pt1+u/+RVhBoY1cocWh1BWEYvk421Eh6AX5YBL55sbkN+7+iCgLVB/2f+sfcHniDHkiQRcJpqAafqj0zl//Fqv83xYbaB+3BXrqLC9hU0yuoc1yFAIi6Qgo/IRGvcWibA1rwoAXfaCA+5wl4wDaf2xGJdiSio8HGYEQVZOuvrWucirbdF4VRV0q5nTgmNM6YvuTdo7FjgR3UdbhE+ASxL2WN8ZrQSs/qco2jTtTEtTpvYPfjIpCYcCY4H18Er3d534bTkNdON6ca6p3Ejn+xiVKJcIDqjDoHWeDsO0WnJtbsSfKO+S0Jh3HSGaKcAILJr7Djh4SEDQ7H7ZZ9DwzbkV0ZZzUjcJxSL+dIxhHNkIBn1wJn0XQ94yKakupluHkSJHZqgZOJEbaDNxtFk5MaAEAO/rax4xdPwic+h1ImgfEX9olhc4QqbKH5HJptcfzA8TspOlVMIKA4AipSc9qIwT9p6A1Pl/vghD4eHrcXF44a0SGAXWjlfrcgrdYUv6QnC2N2nhmzCfy0w3bIAsVkHUXTx6m4RObCORxSYnez6KZN1VMqfBRtEoh8blw/lEqiSdPWwpdsI5oFBzcvki2IyR1IUOYPqnxYvGTIopZQX5xKtiO0cJR0yoasU4I5VAE4kamapmszJmNLAxrK02QAatnNC7erCK8J0DQbQvMmYhoC5965TzNnbEK9n+iRRET3537SmPxpdfvYvQl6aRwQoehqpd341d262Do83zjf6/zdqeRcedL/sX+9Odrs8V0i6YsoOkhx7clwKAZzZ4OixyRoSYlL+x4WsjOk39/oe8mLtD9gLdjvbDP28mV8go8mHHq4AM6V0Ps5zMUPAci2yooHR7gLHPs/+z6VAIP4flZQYs9HVVVMAGpz4uo4Lx7ccNrGYmNr5aYpte5Bc+MIds+5IygkEO9iH8HlgB5ReL2srCmzJk+n049cnNLjgQd5KTuTgIlQkXxPj0VA6J28MZk3wMEJdu7RquBwvOHGcFSbAAvX8quYwtwxwqiwWioksKop4jtGQMsSYFnWFHGJflw2r5QAS11TZF3vViJQ1xQA96pe7yoRYDGmCHVpYuKAU2b3pQiw8F0fnnWZO0HTJXb/G9WQ0p/pkBvW8WihRH+J2c8A8i1fSGgh5sNRWeVHmMPPW1RW14j9V2gHReYo+pluHbdXzmzfPxickViPe24x73esFmStheTmn/1wNqpLgPN5O2/cZEASL01TmMx4SwUSTaM/oImrxf6iq1mxF0PV2PUlVRMm91/2MgUCLXAd14ImntOSAqcMUASe1vqlNIMIO5yDX7qe4Sh+hzrvqZaa7b9uTbtFBXyLy8YXzPsf876g7wrAhab5CjAnwDVBGzigAAAAAElFTkSuQmCC',
  inHandImg:
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAuCAYAAABJcBuEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAYSSURBVHgBrVjNchNHEO6e/bELDignIBYV+cYt4pQUMYX8BJgnQFTFhhviCSQfc7I4pEIwVUi35GTzBJYLA76hIzeLYBI4RVTFYPZnOt1r7WpX3l1J2F+V7NGOprunu6f7m0WYEJXaXsE/sJa0gutIUAaCEiAUgkmCPo97hNDlccdX7vbuw/neJHJxrOK7/5R80nXStBQpnABsTMtHd3WcIZkGyI69A7MBiPfgBGAFTcNxVzut+X7GfIpy2bXWWwRUSkywq0nBJmrVRUU938QuOPobwzTOkUdlQKoA8WfEUwTQ47AspnnjmAFXl/fLCmArLkQEIKmm6X5pZ+0kjmvL+1X2XD2xATZeAyy+WC92Mw2QnXu+/yqufJwLs1CpctLaFhsBtbgRnuFeiXsiMiDI8k/2q6TVqraz/u0DOAF+WtmvsZK1oQ3Qsxz3SrghM5zwP9tro8oR9MeF5f0tOAFMx73p2haERvD/kniGh/flu5I/4noiqoaLxO2yc37W46TrDUJSlvG4z0AAJyL2keuC7PT5o2JTZMa8UFv4+W0l8oCc87iL2OpVGe88vtThf52FlXd1Qqo+/714G8ZAEpBlVLSFtRe/zr0Jn0se+ba9FHmZk1Rkq9HdMxrTJlwa7IMvH+PfRSYRRl4QL0mimr7vV6JU5Cx9vl5sH5NG4k4qyQJ3xlrL1MplmEidA9L9tE3IMfYsqxGeMs+auWWSwhtcPAZWYSdNbrDQtpqead9TRP3E0YpBO/BA2bShCTbT5sWoa3febYYeJ6W55lA889MNkIVBEiHVfAL2EDYQsAexnbPyK4YJNRZe8N2jHEoDaezG1lVU0NUGYKHdrIXPHhXvcxd8qhBeIVDBcNQiP+6CxgZXuPtqBta4Ad3gT223ld2AEL0oMVleQU3T4Z49LlYRsSUhcCy/4Cn35s7juVWl+IxrKEup5SPXzpPBIR/mBooBU2Bh5W0jiJ+43HX7Ftl16R2ooY1H537jx7t7pWlkqmQsdSFPOZtcF5ebhnHbmrHrYowo1RwTCQnnSd/Q1pacliw5qGk4R9CVEHSi74iltEVSKyLlpmpLwwozWUorf1qerde4gC1KXAelNhUExvfDsRigaXs4rZfSFgW1guF5Tlt4Qnre0JKca86yJp/qKmRBOEM05kpouO5mwOmOHlTS3Mee+U5KtDpjFY6RlBHhSPRGDEyTE3iSj174nbviUxWcceZv4UMpNlnyDU+XIAdsXDkcO2dnzo3O+9qPZLOXWqI7OAWk6GlsF7UwkyXDA3bDnVBiC2jcgBwEv1F4XcbKg6VrK39FIQ16TqyC8jjgGYEBOw+564XJyO4zfetJMIkoheWJxFeejzStFAtivyHNuaCehKHwtL8BQ+2dkJpFjGjhLvdnjVtDWdCU6gengIXlt804u0YyKs/WL24nDAh+eIfZTyxJTmpEQPP+s5pMVG5FD3n3O+vFxfBrshIiJZqIxIw53d601U0gHvU+WVsJ5QzPdROk5hgtZ/azEcR8BHLTQZ/aA5aUq5h/XI97MpLBmc98I9+ASpWpuZWk5kkpcg8M2nbUOZmscPJhKe1SEofnuPOjnTL1ZiQckDU14DTBZVw65+jj1G5oOs6DRJM6IaSKpinPNCAgkEqPZcCTQpFRzZzLmpDiFOfyXwtJvPDMT2WAQLj8SUIR3Ipz+OFYA04hFI08fjjWAMHXhkLWjOOHExkgmDYU4nrDyXd9iLHviEKMNqs8pBWcLEzMiiUUNLhS54ILzqTKpzJAINds/rebNc8G7ppzzi8wBca/pqvvzbrvrDLz98t8qTjPhOMCCulATNZ8uTPypYVzRd4ZHvJd4Y0P9No+6/Y6zezbNuYp9t6b3NnUD8cWIV0YMN/ZI+VwyLv/gy8nvTRZvqE7L3+7tD2VAcEbDKWuZ80zAS3w4stHY+zy+BBy4AP8+fLR3OvR52bWAo0eM1Y7axoCVw/yYWwc2UOfXed9upwcXF3eKxtgleVeAF8J9lTvgPl/d5o3paMoM7M9YwHT6pnzJuiLBPwWBGiWV8/GNHEI8BDB++CB+S+6+sPf4L7uteZzQ/M/Lx8pv+mT1lwAAAAASUVORK5CYII=',
};
export const iconTypeBim = {
  unsolvedImg:
    '<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAuCAYAAABJcBuEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUgSURBVHgBrVg7c9tGEN490hpNYsVMZacy1bkLU6UU1GUcPTD+A2J+QeRfQKpMZeYXmOqSytTDGXeBOndhqS5QF1fh2I4mYwm3/u4AggcSL5reGUjgPXa/fdzuHphqkvh+iz5EPjXUFgl1SFEb/1t2kmlCmkL8HxNzgLkLHo3COny5agEEtymSHlb6qcB6NKQGH1UB4RLBLWjVJ5GfaSWSAb1TRxyMJlQXgNVay5/QuD03ZZiMAAqmlpDu3BmT1l9TxPdIog7M72HePK05KSEp3s6zBucI7yTCXSYhhA7ovTou0iTD4/FuF7HSw2s7Az4CiD9G40IAieZ/ZYWXm7AQhHFhpAGCDzMgGvydawnObIiFt2dc5JDPT3+lFUh+3D0kpZ7RTGJIbwEiUaiZTkRiFrWLhMuO7xPpb6kOKRXy6ejYynt5NgAISkEYBTeMZehpjIfSo/a3g3vAZ6dP01+7ftcK1zSuBwDBiHwwBRHz2HuWcYdE23x+HjQT7XvO9tD4PMtRtvAMoc0F1SBY64pEb2UGG+AZCaw4tXLDyAyU1Z6omy7U0q8KONnz+3gOMr939vplexB4E1hlMBsgTzy/1aTIntspTfjl6TFVkcBKsAgEm1+biJcefNyt3KfoOE5uySm7qw8UGO07SwKqQXw2GuLvTxA8tMKZu66/C/cZKwgSWTrAHQVTtNMBkYDqkrtP68262yDUDWQPFnAYsdSKcuPzqebWEsy9qhhISdOV8wsxsFyFSzG4ZscxNX8f1tw7yQIokzLtAayV2EeO36S1RgBfZo5pHBNJHfkQmV7hIQl3bE24bozKTpUBENLUDdGcNeIG5HkixkMeJ7q1cbKdy81k04bycJRRLcGroXz6Ugtm3ACdyRAamxgI0gHFbaoic37j3JGhZMyL1+hDe0JyyUnnbABE2slu7FMdQrl2QSDzebaE16G4Z5jyCRR8OrI9XYzIZqdKJqagoHbI7r59cHrympfFba6VDP2nTlScHBxz3dXLtGBtojzBKoCm/YXhKMN7aIJTxdDUSTrMfJjn46WIxVS9oTsU83SqYcS21FsAfD4KaBaMLUT6c1qFBLHEaN3tu8Tu1fJiNk/BtDVrOpuOgNyLNUAsoH6bnkCePKkXXAXEZydXyJwDAOnMBnU/fXUXI6CMMM8ZyTQmy5JNZLd6ALcezAah/flJmkdUdgfPNSKIhz1E+ifERHo0XeGGblE7XAkLG3f3ja/y8gFKb3Rs2iiqEsy2w/LyeMAlFQB+gLZr8615Zoep6YG9nMzGsJbbJnZKi9sNb/Kr7OUk/2a05/cgoE+fk8APXfbR/LDKXazInNGQPh+FecJjUTkUZ8dssKxGuls0o4om4uQkA1qdEHjF7bwq22l7+dVcESLwjsoWlAJY2RXmjvGq/ANFuQVoFVcgi9a4Y1QCsLS8K3KudysAWNoVN/gQUfN7Qj0LUOIKrasLk1T7/ZMAWBC46yN3vi5eIa/p/dtfaBmeVQvE89ZpY8PU8kfAex8jD7CtS7SQ8yfmwopaYG7B/5O+xRVdLul6LSxzB5cK/uIrDzb6fmFS8QMI6uJtPRmBQPrNfjnLlYLLzNmLi+UA7Ox4kLRFxdBNBXwUL9aojNC6lPTvKOWX86PFVzPdnJDShdPW1MbnCZpSMi551/wnd6psnzzGXY/MXY/rXjxzmCAnoP9f6kvpAg97SUX/zzf30cJ/A0D3oNU6gmx9xglaiuChNwD9L97f0PX1JQdBqWs+AuC9XZ73lvusAAAAAElFTkSuQmCC" alt="">',
  resolvedImg:
    '<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAuCAYAAABJcBuEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUjSURBVHgBrVi9cttGEN67ozzqzFROKsOdO9NVZvIjApQ84y7KE1h+AktPILFMJfoJLHVJFanzjEQSVCYz6sJSXaAurqJSQxE4f3sCwCOFX1I7I+Fwd7jvu9293T0Kqiju0G1KJbdJUps0tYQWDrqb8fCNFjpA3xjj/trd2uiz9zmosq4om/B2+Na5U3f7aG5bgFXkaC1c65YRySXAO1ZSHWDGB1pBBIneNJx2fc+/yR7PkHjXQzSduQFtVH3Cqg5FGMAkYzVR35Cip5GOWljNxRwXz0VNBdCGl6WNBwTeDN+0IhUxeLqI1rAvdhJG4XHeTmzpDDs78IV9IYRjdd/IUHpn3tk4l0C8838WwHtRFHWrANsSO+0+iO/aJKCJ17YmhP2BUorBnXS6pt1+u/+RVhBoY1cocWh1BWEYvk421Eh6AX5YBL55sbkN+7+iCgLVB/2f+sfcHniDHkiQRcJpqAafqj0zl//Fqv83xYbaB+3BXrqLC9hU0yuoc1yFAIi6Qgo/IRGvcWibA1rwoAXfaCA+5wl4wDaf2xGJdiSio8HGYEQVZOuvrWucirbdF4VRV0q5nTgmNM6YvuTdo7FjgR3UdbhE+ASxL2WN8ZrQSs/qco2jTtTEtTpvYPfjIpCYcCY4H18Er3d534bTkNdON6ca6p3Ejn+xiVKJcIDqjDoHWeDsO0WnJtbsSfKO+S0Jh3HSGaKcAILJr7Djh4SEDQ7H7ZZ9DwzbkV0ZZzUjcJxSL+dIxhHNkIBn1wJn0XQ94yKakupluHkSJHZqgZOJEbaDNxtFk5MaAEAO/rax4xdPwic+h1ImgfEX9olhc4QqbKH5HJptcfzA8TspOlVMIKA4AipSc9qIwT9p6A1Pl/vghD4eHrcXF44a0SGAXWjlfrcgrdYUv6QnC2N2nhmzCfy0w3bIAsVkHUXTx6m4RObCORxSYnez6KZN1VMqfBRtEoh8blw/lEqiSdPWwpdsI5oFBzcvki2IyR1IUOYPqnxYvGTIopZQX5xKtiO0cJR0yoasU4I5VAE4kamapmszJmNLAxrK02QAatnNC7erCK8J0DQbQvMmYhoC5965TzNnbEK9n+iRRET3537SmPxpdfvYvQl6aRwQoehqpd341d262Do83zjf6/zdqeRcedL/sX+9Odrs8V0i6YsoOkhx7clwKAZzZ4OixyRoSYlL+x4WsjOk39/oe8mLtD9gLdjvbDP28mV8go8mHHq4AM6V0Ps5zMUPAci2yooHR7gLHPs/+z6VAIP4flZQYs9HVVVMAGpz4uo4Lx7ccNrGYmNr5aYpte5Bc+MIds+5IygkEO9iH8HlgB5ReL2srCmzJk+n049cnNLjgQd5KTuTgIlQkXxPj0VA6J28MZk3wMEJdu7RquBwvOHGcFSbAAvX8quYwtwxwqiwWioksKop4jtGQMsSYFnWFHGJflw2r5QAS11TZF3vViJQ1xQA96pe7yoRYDGmCHVpYuKAU2b3pQiw8F0fnnWZO0HTJXb/G9WQ0p/pkBvW8WihRH+J2c8A8i1fSGgh5sNRWeVHmMPPW1RW14j9V2gHReYo+pluHbdXzmzfPxickViPe24x73esFmStheTmn/1wNqpLgPN5O2/cZEASL01TmMx4SwUSTaM/oImrxf6iq1mxF0PV2PUlVRMm91/2MgUCLXAd14ImntOSAqcMUASe1vqlNIMIO5yDX7qe4Sh+hzrvqZaa7b9uTbtFBXyLy8YXzPsf876g7wrAhab5CjAnwDVBGzigAAAAAElFTkSuQmCC" alt="">',
  inHandImg:
    '<img src="data:image/png;base64,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">',
};
export interface MarkerConfig {
  id?: string;
  size?: number[];
  icon?: string;
  name?: string;
  position?: number[];
}

export class Marker implements MarkerType {
  private viewer: any;

  private markerIds: string[] = [];

  private markerAcc = 0;

  constructor(viewer: any) {
    this.viewer = viewer;
  }

  async addMarker(pageX: number, pageY: number, config?: MarkerConfig) {
    if (!this.viewer) return null;
    const data = await this.viewer.pickPointAsync([pageX, pageY]);
    if (data.point !== null) {
      const position = data.result.slice(0, 3);
      // 添加标签
      this.viewer.createMarker([
        {
          id: `${this.markerAcc}`,
          name: ' ',
          size: [15, 25],
          icon: config?.icon,
          position,
          ...config,
        },
      ]);
      this.markerIds.push(config?.id || `${this.markerAcc++}`);
      return { id: data.id, position: data.result.slice(0, 3) };
    }
    return null;
  }

  addMarkerBy3DPosition(markers: MarkerConfig[]) {
    if (!this.viewer) return;
    const markerList: MarkerConfig[] = [];
    markers.forEach((item: MarkerConfig) => {
      markerList.push({
        id: item.id,
        name: item.name,
        size: [15, 25],
        icon: item.icon,
        position: item.position,
      });
      this.markerIds.push(item.id as string);
    });
    this.viewer.createMarker(markerList);
  }

  clearMarker(ids?: string[]) {
    if (!this.viewer) return;
    if (ids) {
      this.viewer.removeMarker(ids);
      ids.forEach((item: string) => {
        const index = this.markerIds.findIndex((val: string) => val === item);
        if (index !== -1) this.markerIds.splice(index, 1);
      });
    } else if (this.markerIds.length) {
      this.viewer.removeMarker(this.markerIds);
      this.markerIds = [];
      this.markerAcc = 0;
    }
  }

  onMarkerClick(func: (data: string) => void) {
    console.log('[ 执行了 ] >');
    if (!this.viewer) return;
    this.viewer.on(DX.Events.MARKER_SELECTED, func);
  }
}
