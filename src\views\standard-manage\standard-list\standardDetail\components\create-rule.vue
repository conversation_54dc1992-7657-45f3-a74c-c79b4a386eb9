<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="$t('standard-setting.generating-rules')"
    unmount-on-close
    width="720px"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    :on-before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form :model="form">
      <a-form-item
        field="ifc_mapping"
        :label="$t('standard-setting.semantic-entity-mapping')"
        :rules="[
          {
            message: $t('standard-setting.required'),
            required: true,
          },
        ]"
      >
        <a-table
          stripe
          :bordered="true"
          row-key="property_group_id"
          :loading="tableLoading"
          :scroll="{ x: '100%', y: '100%' }"
          :pagination="false"
          :columns="columns"
          :data="tableDataList"
        >
          <template #classIdentifier="{ record }">
            <span>{{ record.classIdentifier }}</span>
          </template>
          <template #IFCSemanticEntity="{ record }">
            <a-select v-model="record.IFCSemanticEntity">
              <a-option
                v-for="template in ['土木工程元素/IfcCivilElement']"
                :key="template"
                :value="template"
                :label="template"
              ></a-option>
            </a-select>
          </template>
        </a-table>
      </a-form-item>

      <a-form-item
        field="check"
        :label="$t('standard-setting.check-range')"
        :rules="rules"
      >
        <a-checkbox-group v-model="checkboxValue" @change="checkboxChange">
          <a-checkbox v-model="form.prop_group_check" value="1">{{
            $t('standard-setting.attribute-groups')
          }}</a-checkbox>
          <a-checkbox v-model="form.prop_check" value="2">{{
            $t('standard-setting.attribute-types')
          }}</a-checkbox>
          <a-checkbox v-model="form.unit_check" value="3">{{
            $t('standard-setting.unit')
          }}</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { deleteStandadCollection, getStandadAttributeSnl } from '../api';
  import { Column } from '@/views/design/components/table-column';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const { t } = useI18n();
  const emits = defineEmits(['updateState']);
  const props = defineProps({
    attributeStandardData: {
      type: Object,
      // default() {
      //   return {};
      // },
    },
    standardForm: {
      type: Object,
      default() {
        return {};
      },
    },
  });

  const modalVisible = ref(false);
  const form = ref({
    property_name: '',
    ifc_mapping: {},
    prop_group_check: true,
    prop_check: true,
    unit_check: true,
  });

  const checkboxValue = ref<string[]>(['1', '2', '3']);
  const rules = ref([
    {
      message: '',
      required: true,
    },
  ]);

  const checkboxChange = () => {
    if (
      !checkboxValue.value.includes('2') &&
      !checkboxValue.value.includes('3')
    ) {
      rules.value[0].message = t('standard-setting.select-at-least-one-of');
    } else {
      rules.value[0].message = ' ';
    }
  };

  const tableLoading = ref(false);
  const tableDataList = ref<any[]>([]);
  const columns = computed<Column[]>(() => {
    return [
      {
        title: t('standard-setting.class-identifier1'),
        dataIndex: 'classIdentifier',
        slotName: 'classIdentifier',
        align: 'left',
      },
      {
        title: t('standard-setting.IFC-semantic-entities'),
        dataIndex: 'IFCSemanticEntity',
        slotName: 'IFCSemanticEntity',
        align: 'left',
      },
    ];
  });

  const xbaseStandardIdData = ref<string>();
  const xbaseIdentifyData = ref<string[]>([]);
  const standardFormNameData = ref();
  const ifcMappingData = ref();

  const changeModalVisible = (value: boolean) => {
    modalVisible.value = value;

    xbaseStandardIdData.value = props.standardForm.xbaseStandardId;
    standardFormNameData.value = props.standardForm.xbaseStandardName;
    const temporaryData: string[] = [];
    props.attributeStandardData?.forEach((item: { identify: string }) => {
      if (item.identify) temporaryData.push(item.identify);
    });
    xbaseIdentifyData.value = [...new Set(temporaryData)];

    for (let i = 0; i < xbaseIdentifyData.value.length; i++) {
      tableDataList.value.push({
        classIdentifier: xbaseIdentifyData.value[i],
        IFCSemanticEntity: '土木工程元素/IfcCivilElement',
      });
    }

    // 使用 reduce 方法将数据数组转换为 ifc_mapping 对象
    ifcMappingData.value = tableDataList.value.reduce(
      (acc, curr) => ({
        ...acc,
        [curr.classIdentifier]: curr.IFCSemanticEntity,
      }),
      {}
    );
  };

  const handleCancel = () => {
    modalVisible.value = false;
    tableDataList.value.length = 0;
    checkboxValue.value = ['1', '2', '3'];
  };

  const setStandadAttributeSnl = async () => {
    emits('updateState', true);
    const params = {
      ifc_mapping: ifcMappingData.value,
      prop_check: checkboxValue.value.includes('2'),
      prop_group_check: checkboxValue.value.includes('1'),
      property_id: xbaseStandardIdData.value,
      property_name: standardFormNameData.value,
      unit_check: checkboxValue.value.includes('3'),
      group_id: route.params.projectId.toString(),
    };
    const res: any = await getStandadAttributeSnl(params);
    if (res.code === 0) {
      handleCancel();
    } else {
      Message.error(res.msg);
    }
  };

  const handleBeforeOk = async () => {
    if (props.standardForm.xbaseCollectionId) {
      deleteStandadCollection({
        collection_id: props.standardForm.xbaseCollectionId,
      }).then(() => {
        setStandadAttributeSnl();
      });
    } else setStandadAttributeSnl();
  };

  defineExpose({
    changeModalVisible,
  });
</script>

<script lang="ts">
  export default {
    name: 'CreateRule',
  };
</script>

<style lang="less" scoped>
  :deep(.arco-select-view),
  :deep(.arco-textarea-wrapper),
  :deep(.arco-input-wrapper),
  :deep(.arco-picker),
  :deep(.arco-input-tag),
  :deep(.arco-select-view-single),
  :deep(.arco-textarea),
  :deep(.arco-form-item-content-wrapper) {
    background-color: #fff;
    border-radius: 8px;
  }
  :deep(.arco-select-view),
  :deep(.arco-form-item-content-wrapper) {
    border: 1px solid #c9cdd4 !important;
  }
</style>
