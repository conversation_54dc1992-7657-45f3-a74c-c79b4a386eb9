import { TableColumnData } from '@arco-design/web-vue/es/table/interface';

export type Column = TableColumnData & { checked?: true };

export function getFileColumns(t: any, computed: any) {
  return computed(() => [
    {
      title: t('design.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 30,
      align: 'center',
    },
    {
      title: t('design.name'),
      dataIndex: 'name',
      slotName: 'name',
      align: 'left',
      width: 150,
    },
    {
      title: t('design.standard-name'),
      dataIndex: 'standardName',
      slotName: 'standardName',
      align: 'left',
      width: 150,
    },
    {
      title: t('design.description'),
      dataIndex: 'description',
      slotName: 'description',
      align: 'left',
      width: 200,
    },
    {
      title: t('design.version'),
      dataIndex: 'version',
      slotName: 'version',
      width: 50,
      align: 'center',
    },
    {
      title: t('design.size'),
      dataIndex: 'size',
      slotName: 'size',
      width: 50,
      align: 'center',
    },
    {
      title: t('design.operation'),
      dataIndex: 'operation',
      slotName: 'operation',
      width: 50,
      align: 'center',
    },
  ]);
}
