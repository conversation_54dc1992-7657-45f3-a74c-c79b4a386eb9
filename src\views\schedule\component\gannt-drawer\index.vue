<template>
  <div class="gannt-container">
    <!-- 左侧 -->
    <GanntLeft />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import GanntLeft from './components/gannt-left.vue';

  const size = ref('300px');
</script>

<script lang="ts">
  export default {
    name: 'Gannt',
  };
</script>

<style lang="less" scoped>
  .gannt-container {
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: calc(100vh - 168px);
  }
</style>
