<template>
  <div class="file-list-container">
    <h3 style="font-weight: 500">{{ $t('dashboard.file') }}</h3>

    <div v-for="(file, index) in files" :key="index" class="file-item">
      <div draggable="true" class="file-content">
        <!-- <FileImage :file-name="file.name" :is-file="true" class="file-image" /> -->
        <img
          :src="getFileIcon(file.type)"
          style="width: 28px; height: 28px; margin-right: 4px"
        />
        <span class="file-name">{{ file.name }}</span>

        <a-dropdown class="dropdown" :popup-visible="file.popVisible">
          <a-button
            class="dropdown-button"
            type="text"
            @click="dropHandle(file.id)"
          >
            <template #icon>
              <icon-more class="icon-more" />
            </template>
          </a-button>
          <template #content>
            <a-doption value="view" @click="viewHandle(file)">{{ $t('dashboard.view') }}</a-doption>
            <a-popconfirm
              :popup-visible="popconfirmVisible"
              :content="$t('dashboard.delete.file')"
              position="left"
              type="warning"
              class="popconfirm"
              @ok="deleteFile(file)"
              @cancel="handleCancel"
            >
              <a-doption value="delete" @click="deleteHandle()">{{ $t('dashboard.delete') }}</a-doption>
            </a-popconfirm>
          </template>
        </a-dropdown>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  // import FileImage from '@/components/uploadTheSpecifiedFolder/components/image-file.vue';
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import { wpsViewHandle } from '@/hooks/wps';
  import { deleteRecentlyFiles, getRecentlyFiles } from '../api';
  import { Message } from '@arco-design/web-vue';

  import useFolderContent from '@/views/knowledge-base/composables/useFolderContent';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const { getFileIcon } = useFolderContent();

  const files = ref();
  // 二次删除确认框是否显示
  const popconfirmVisible = ref<boolean>(false);

  // 获取最近文件
  const getRecentFile = async () => {
    try {
      const params = {
        pageParam: {
          pageNo: 1,
          pageSize: 5,
        },
      };
      // 调用接口
      const res = await getRecentlyFiles(params);

      // 检查接口返回值是否成功
      if (res.status) {
        res.data?.list?.forEach((element: { popVisible: boolean }) => {
          element.popVisible = false;
        });
        files.value = res.data?.list || [];
      }
    } catch (error) {
      // 错误处理
      console.error('获取文件列表失败:', error);
    } finally {
      // 无论成功或失败都要执行
    }
  };

  // 展开下拉框菜单
  const dropHandle = (id: string) => {
    files.value?.forEach((item: { id: string; popVisible: boolean }) => {
      if (item.id === id) {
        item.popVisible = !item.popVisible;
      } else {
        item.popVisible = false;
      }
    });
    popconfirmVisible.value = false;
  };

  // 预览文件
  const viewHandle = (record: any) => {
    wpsViewHandle(record, 'preview', 'admin');
    record.popVisible = false;
  };

  // 删除文件前置处理
  const deleteHandle = () => {
    popconfirmVisible.value = true;
  };

  // 取消删除
  const handleCancel = () => {
    popconfirmVisible.value = false;
  };

  // 删除文件
  const deleteFile = async (record: any) => {
    try {
      const params = {
        fileId: record.id,
      };
      // 调用接口
      const res: any = await deleteRecentlyFiles(params);

      // 检查接口返回值是否成功
      if (res.status) {
        Message.success(res.message);
        getRecentFile();
      }
    } catch (error) {
      // 错误处理
      console.error('删除文件失败:', error);
    } finally {
      // 无论成功或失败都要执行
    }
  };

  // 点击空白处关闭下拉菜单
  const handleClickOutside = (event: any) => {
    const dropdownMenu = event.target.closest('.dropdown'); // ① 检查是否点击在 a-dropdown 内
    const dropdownButton = event.target.closest('.dropdown-button'); // ② 检查是否点击按钮
    const popConfirmBox = event.target.closest('.popconfirm'); // ③ 检查是否点击在 popconfirm 内

    if (!dropdownMenu && !dropdownButton && !popConfirmBox) {
      // 如果点击的既不是下拉菜单、按钮，也不是二次确认框，则关闭
      files.value?.forEach((item) => {
        item.popVisible = false;
      });
      popconfirmVisible.value = false;
    }
  };

  onMounted(async () => {
    getRecentFile();
    // 添加全局点击事件监听器
    document.addEventListener('click', handleClickOutside);
  });

  onBeforeUnmount(() => {
    // 移除全局点击事件监听器
    document.removeEventListener('click', handleClickOutside);
  });
</script>

<script lang="ts">
  export default {
    name: 'FileRecently',
  };
</script>

<style lang="less" scoped>
  .file-list-container {
    height: 44%;
    border: 1px solid #d9d9d9; /* 设置边框颜色 */
    border-radius: 8px; /* 设置圆角 */
    padding: 16px 20px; /* 设置内边距：上下16px，左右20px */
    overflow: auto;
  }

  h3 {
    margin: 0;
    font-size: 20px;
    line-height: 30px;
    width: 41px;
  }

  .file-item {
    display: flex;
    height: 60px;
    border-bottom: 1px solid #ededed;
  }

  .file-content {
    display: flex;
    align-items: center; /* 保证图片和文字垂直居中 */
    gap: 10px; /* 设置图片和文字之间的间距 */
    width: 100%;
  }

  .file-image {
    width: 28px; /* 根据需要设置图片的宽度 */
    height: 28px; /* 根据需要设置图片的高度 */
    object-fit: cover; /* 保证图片的缩放效果 */
  }

  .file-name {
    font-size: 16px; /* 设置文字样式 */
    line-height: 24px;
    color: #1d2129;
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    cursor: default;
  }

  .icon-more {
    height: 20px;
    width: 20px;
    color: #4e5969;
  }
</style>
