<template>
  <div class="project-home">
    <a-spin :loading="pageLoading">
      <commonTabs />
      <div class="container">
        <div ref="timeLine" onselectstart="return false" class="timeline">
          <a-row :gutter="16" style="margin-bottom: 16px">
            <a-col :flex="1">
              <table-title :title="$t('design.lane-chart')"> </table-title>
            </a-col>
            <a-col :flex="'380px'" style="text-align: right">
              <a-space :size="8">
                <div class="button-explain">
                  <div
                    class="explain-self"
                    :style="'color: ' + (teamData[0]?.color || '#F76560')"
                  >
                    <a-popover position="br">
                      <icon-exclamation-circle-fill class="explain-icon" />
                      <template #content>
                        <explain-panel
                          :color="teamData[0]?.color || '#F76560'"
                          :is-self="true"
                        ></explain-panel>
                      </template>
                    </a-popover>
                    {{ $t('design.current-team-legend') }}
                  </div>
                  <div class="explain-other">
                    <a-popover position="br">
                      <icon-exclamation-circle-fill class="explain-icon" />
                      <template #content>
                        <explain-panel :is-self="false"></explain-panel>
                      </template>
                    </a-popover>
                    {{ $t('design.other-team-legend') }}
                  </div>
                </div>
              </a-space>
            </a-col>
          </a-row>
          <upload-timeline
            v-if="filterData.length > 0"
            ref="uploadTimelineRef"
            :data="filterData"
            :all-data="teamData"
            :plan-data="planData"
            :team-attention-list="teamAttentionList"
            :self-teams="teamList"
            :current-team-id="currentTeamId"
            @set-team-attention="setTeamAttention"
            @check-current-team="checkCurrentTeam"
            @shared-tobe-reviewed="sharedTobeReviewed"
            @other-team-shared="otherTeamShared"
            @shared-in-wip="sharedInWip"
            @save-delivery="saveDelivery"
            @delivery-tobe-reviewed="deliveryTobeReviewed"
            @delivery-in-wip="deliveryInWip"
            @save-shared="saveShared"
            @change-filter-data="changeFilterData"
          ></upload-timeline>
          <div class="button-box">
            <div
              v-if="showButtonSwitch === 0"
              class="loadmore-button"
              :title="$t('design.show-current-team')"
              @click="loadmore"
            >
              <img src="@/assets/images/design/load-more.png" />
            </div>
            <div v-if="showButtonSwitch === 1" class="middle-button">
              <div class="button-list">
                <div
                  class="button-base"
                  :title="$t('design.show-all-team')"
                  @click.stop="loadmore3"
                >
                  <icon-double-down size="20" />
                </div>
                <div
                  class="button-base"
                  :title="$t('design.show-more-team')"
                  @click="loadmore2"
                >
                  <icon-down size="20" />
                </div>
                <div
                  class="button-base"
                  :title="$t('design.close')"
                  @click="close"
                >
                  <icon-close size="20" />
                </div>
              </div>
            </div>
            <div v-if="showButtonSwitch === 2" class="middle-button">
              <div class="button-list">
                <div
                  class="button-base"
                  :title="$t('design.show-current-team')"
                  @click.stop="loadmore"
                >
                  <icon-double-up size="20" />
                </div>
                <div
                  class="button-base"
                  :title="$t('design.show-more-team')"
                  @click.stop="loadmore3"
                >
                  <icon-down size="20" />
                </div>
                <div
                  class="button-base"
                  :title="$t('design.close')"
                  @click="close"
                >
                  <icon-close size="20" />
                </div>
              </div>
            </div>
            <div v-if="showButtonSwitch === 3" class="middle-button">
              <div class="button-list">
                <div
                  class="button-base"
                  :title="$t('design.show-current-team')"
                  @click.stop="loadmore"
                >
                  <icon-double-up size="20" />
                </div>
                <div
                  class="button-base"
                  :title="$t('design.show-less-team')"
                  @click.stop="loadmore2"
                >
                  <icon-up size="20" />
                </div>
                <div
                  class="button-base"
                  :title="$t('design.close')"
                  @click="close"
                >
                  <icon-close size="20" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <file-table
          ref="fileTableRef"
          :table-data="tableData"
          :breadcrumb-data="breadcrumbData"
          :current-team-id="currentTeamId"
          :loading="fileLoading"
          :team-list="teamList"
          :table-height="tableHeight"
          @folder-skip="folderSkip"
          @refresh-team-data="refreshTeamData"
        ></file-table>
      </div>
      <a-modal
        v-model:visible="visible"
        :width="600"
        :mask-closable="false"
        :esc-to-close="false"
        draggable
        @cancel="handleCancel"
      >
        <template #title> {{ $t('design.information') }} </template>
        <template #footer>
          <a-button @click="handleCancel">{{ $t('design.cancel') }}</a-button>
          <a-button
            v-if="!catchBtnIsShow"
            type="primary"
            @click="handleBeforeOk"
            >{{ $t('design.confirm') }}</a-button
          >
          <a-popconfirm
            v-else
            :content="$t('design.cache-share-hint')"
            :ok-text="$t('design.confirm')"
            :cancel-text="$t('design.cancel')"
            @ok="handleBeforeOk"
          >
            <a-button type="primary">{{ $t('design.confirm') }}</a-button>
          </a-popconfirm>
        </template>
        <div class="line">
          <span>{{ $t('design.share-name') }}：</span
          >{{ modalObject.shareName || modalObject.deliveryName }}
        </div>
        <div class="line">
          <span>{{ $t('design.sharer') }}：</span>{{ modalObject.user }}
        </div>
        <div class="line">
          <span>{{ $t('design.share-time') }}：</span> {{ modalObject.time }}
        </div>
        <div class="line">
          <span>{{ $t('design.share-file') }}：</span>
          {{ countFiles(modalObject.files) || 0 }}
        </div>

        <a-tree
          v-if="formattedTreeData.length"
          ref="tree"
          class="custom-tree"
          :data="formattedTreeData"
          :block-node="true"
          default-expand-all
          @select="onSelect"
        >
          <template #icon="{ isLeaf }">
            <img
              v-if="isLeaf"
              src="@/assets/images/design/file-simple.png"
              style="width: 16px; margin-right: 4px; cursor: pointer"
              @click="() => modelView(nodeData)"
            />
          </template>
          <template #extra="nodeData">
            <span>
              <icon-eye
                v-if="!nodeData.isFolder && !nodeData.abandon"
                :size="16"
                style="cursor: pointer; margin-left: 8px"
                @click="() => modelView(nodeData)"
              />
              <icon-close-circle
                v-if="!nodeData.isFolder && nodeData.abandon"
                style="color: red; margin-left: 8px"
              />
            </span>
          </template>
        </a-tree>
        <div v-if="!catchBtnIsShow" class="line" style="margin: 12px 0 0 12px"
          >{{ $t('design.cache-share') }}
        </div>
        <div v-else class="line" style="margin: 12px 0 0 12px">
          {{ $t('design.cache-share') }}
        </div>
      </a-modal>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, provide, onUnmounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash';
  import { useI18n } from 'vue-i18n';
  import { Message } from '@arco-design/web-vue';
  import useUserStore from '@/store/modules/user';
  import TableTitle from '@/components/table-title/index.vue';
  import { getChildFolderList, getFileList } from '@/api/tree-folder';
  import UploadTimeline from './components/upload-timeline.vue';
  import FileTable from './components/table-file.vue';
  import ExplainPanel from './components/explain-panel.vue';
  import {
    getAllTeam,
    getProjectProgress,
    queryMilestoneList,
    getSharedFiles,
    getDeliveryFiles,
    consumeSharedFiles,
    getShareDetail,
    getDetailTree,
    getDeliveryDetail,
    BubbleObject,
    getUserTeamsInPrj,
  } from './api';
  import CONSTANT from './event/constant';
  import modelViewBim from '@/utils/common/view';
  import commonTabs from '@/components/common-tabs/index.vue';

  const { t } = useI18n();
  const userStore = useUserStore();

  const showMenu = ref(true);
  const showButtonSwitch = ref(0);

  const fileTableRef = ref();
  const uploadTimelineRef = ref();

  // 泳道图团队列表数据
  const teamList = ref<any[]>([]);
  // 使用 provide 提供 teamList
  provide('teamList', teamList);

  // 交付计划数据
  const planData = ref([]);
  // 过滤之后的泳道图数据
  const filterData = ref<any[]>([]);
  // 团队数据及泳道图数据
  const teamData = ref<any[]>([]);
  const filterStart = ref(0);
  let maxLineNum = 4;

  const route = useRoute();
  const projectId = (route?.params.projectId || '') as string;

  const teamStorageKey = `first-${projectId}-${userStore.id}`;
  const attentionListKey = `attention-${projectId}-${userStore.id}`;

  const fileLoading = ref(false);
  const pageLoading = ref(false);
  const expandedKeys = ref<string[]>([]);
  // 假设 modalObject.value.files 具有新的 folderTreeList 结构
  const formattedTreeData = ref<any[]>([]);

  const currentTeamId = ref<string>('');

  /**
   * 进入页面时获取当前选中的团队
   */
  const getCurrentTeamId = () => {
    let result = '';
    const localStorageId = localStorage.getItem(teamStorageKey);
    const isMyTeam = teamList.value.findIndex(
      (item) => item.id === localStorageId
    );
    if (teamList.value.length === 0) {
      localStorage.removeItem(teamStorageKey);
    } else if (localStorageId && isMyTeam !== -1) {
      result = localStorageId;
    } else {
      result = teamList.value[0].id;
      localStorage.setItem(teamStorageKey, result);
    }
    console.log('[ result ] >', result);
    return result;
  };

  const teamAttentionList = ref(
    JSON.parse(localStorage.getItem(attentionListKey) || '[]')
  );

  /**
   * 团队排序
   * @param data 团队列表
   */
  const teamDataSort = (data: any[]) => {
    const dataClone = cloneDeep(data);
    teamAttentionList.value.forEach((item: any) => {
      const index = dataClone.findIndex((itm: any) => itm.id === item);
      const k = dataClone.splice(index, 1);
      dataClone.splice(0, 0, k[0]);
    });
    dataClone.sort((a: any) => {
      let result = 0;
      if (a.id === currentTeamId.value) result = -1;
      return result;
    });
    return dataClone;
  };

  let teamFolderData: any[] = [];
  const tableData = ref<any[]>([]);
  const breadcrumbData = ref<any[]>([]);

  const getFolderByTeamId = (theTeamId: string) => {
    const team = teamFolderData.filter((item) => item.teamId === theTeamId);
    return team[0];
  };

  const modelView = async (record: any) => {
    modelViewBim(record, route.params.projectId as string);
  };

  /**
   * 获取文件夹和文件
   * @param folderId 文件夹id
   */
  const loadFolderAndFile = async (folderId = '0') => {
    fileLoading.value = true;
    const promiseList = [
      await getChildFolderList(projectId, currentTeamId.value, 'WIP', folderId),
      await getFileList(folderId),
    ];
    const res = await Promise.all(promiseList);
    const folderList: any[] = res[0]?.data.list || [];
    const fileList: any[] = res[1]?.data.list || [];
    tableData.value = [...folderList, ...fileList];
    fileLoading.value = false;
  };

  /**
   * 选择团队的wip文件夹
   * @param teamId 团队id
   */
  const setCurrentFolder = (teamId: string) => {
    const theFolder = getFolderByTeamId(teamId);
    if (!theFolder) return;
    loadFolderAndFile(theFolder.id);
    breadcrumbData.value = [
      {
        id: theFolder.id,
        name: theFolder.name,
        englishName: theFolder.englishName,
        sysType: theFolder.sysType,
      },
    ];
  };

  /**
   * 格式化团队的共享交付包信息（uploadTime、type）
   * @param uploadList 共享交付包信息
   * @param isSelfTeam 是否是自己团队
   */
  const formatUploadData = (uploadList: any[], isSelfTeam: boolean) => {
    uploadList.forEach((item, index) => {
      if (index === 0) {
        item.uploadTime = new Date(item.updateDate);
      } else {
        const beforeUploadTime = uploadList[index - 1].uploadTime;
        const thisUploadTime = new Date(item.updateDate);
        item.uploadTime = new Date(
          Math.max(
            thisUploadTime.getTime(),
            beforeUploadTime.getTime() +
              (document.body.offsetHeight < 700 ? 240000 : 120000)
          )
        );
      }
      if (isSelfTeam) {
        if (item.entity === 'collaborate' && item.processState === '3') {
          item.type = 1;
        } else if (item.entity === 'collaborate' && item.processState === '0') {
          item.type = 3;
        } else if (item.entity === 'collaborate' && item.processState === '1') {
          item.type = 4;
        } else if (item.entity === 'delivery' && item.processState === '3') {
          item.type = 5;
        } else if (item.entity === 'delivery' && item.processState === '0') {
          item.type = 7;
        } else if (item.entity === 'delivery' && item.processState === '1') {
          item.type = 8;
        } else {
          item.type = 12;
        }
      } else if (
        item.entity === 'collaborate' &&
        item.isDownload === false &&
        item.processState === '1'
      ) {
        item.type = 2;
      } else if (
        item.entity === 'collaborate' &&
        item.isDownload === true &&
        item.processState === '1'
      ) {
        item.type = 4;
      } else if (item.entity === 'delivery' && item.processState === '1') {
        item.type = 8;
      } else {
        item.type = 12;
      }
    });
    uploadList = uploadList.filter((item) => item.type !== 12);
    return uploadList;
  };

  /**
   * 获取单个team的共享交付包的promise
   * @param team 团队信息
   */
  const getTeamProgress = (team: any) => {
    return new Promise((resolve) => {
      getProjectProgress(team.projectId, currentTeamId.value, team.id).then(
        (res) => {
          team.uploadHistory = formatUploadData(
            res.data,
            team.id === currentTeamId.value
          );
          resolve(null);
        }
      );
    });
  };

  /**
   * 获取teamData和team共享交付包的所有promise
   * @param result 保存结果的对象
   */
  const getTeamDataPromise = async (result: any) => {
    const res = await getAllTeam(projectId);
    result.teamData = teamDataSort(res.data.list);
    const promiseList: any = [];
    if (currentTeamId.value) {
      result.teamData.forEach((item: any) => {
        promiseList.push(getTeamProgress(item));
      });
    }
    return promiseList;
  };

  /**
   * 重新获取teamData数据
   */
  const refreshTeamData = async () => {
    const allPromiseResult = {
      teamData: [],
    };
    // 制造获取所有team的promise List
    const teamDataPromiseList = await getTeamDataPromise(allPromiseResult);
    // 制造获取planData的promise
    await Promise.all(teamDataPromiseList);
    teamData.value = allPromiseResult.teamData;
    if (showButtonSwitch.value === 1) {
      filterData.value = [teamData.value[0]];
    } else {
      filterData.value = teamData.value.slice(
        filterStart.value,
        filterStart.value + maxLineNum
      );
    }
  };

  /**
   * 关注团队
   * @param teamDataItem 团队信息
   */
  const setTeamAttention = (teamDataItem: any) => {
    if (teamAttentionList.value.includes(teamDataItem.id)) {
      teamAttentionList.value.splice(
        teamAttentionList.value.findIndex(
          (item: any) => item === teamDataItem.id
        ),
        1
      );
    } else {
      teamAttentionList.value.push(teamDataItem.id);
    }
    localStorage.setItem(
      attentionListKey,
      JSON.stringify(teamAttentionList.value)
    );
    refreshTeamData();
  };

  /**
   * 选择当前团队
   * @param team 团队信息
   */
  const checkCurrentTeam = (team: any) => {
    // 如果选的不是自己团队，直接返回
    // 如果团队没有对应的folder，直接返回（脏数据）
    if (
      teamList.value.findIndex((item) => item.id === team.id) === -1 ||
      !getFolderByTeamId(team.id)
    )
      return;
    // 设置当前teamId
    currentTeamId.value = team.id;
    localStorage.setItem(teamStorageKey, team.id);
    // 更新teamData
    refreshTeamData();
    // 更新文件区
    setCurrentFolder(currentTeamId.value);
  };

  /**
   * 获取所有团队文件夹
   */
  const getTeamFolder = async () => {
    const res = await getChildFolderList(projectId, undefined, 'WIP', '0');
    teamFolderData = res.data.list;
  };

  /**
   * 文件夹跳转
   * @param folderMsg 文件夹信息
   * @param index breadcrumbData中的位置
   */
  const folderSkip = (folderMsg: any, index = -1) => {
    if (index === breadcrumbData.value.length - 1) return;
    loadFolderAndFile(folderMsg.id);
    if (index === -1) {
      breadcrumbData.value.push({
        id: folderMsg.id,
        name: folderMsg.name,
        englishName: folderMsg.englishName,
        sysType: folderMsg.sysType,
      });
    } else {
      breadcrumbData.value = breadcrumbData.value.slice(0, index + 1);
    }
  };

  /**
   * 获取planData的promise
   * @param result 保存结果的对象
   */
  const getPlanDataPromise = (result: any): Promise<null> => {
    return new Promise((resolve) => {
      queryMilestoneList(projectId).then((res) => {
        result.planData = res.data.list;
        result.planData.forEach((item: any) => {
          item.uploadTime = new Date(item.endTime);
        });
        resolve(null);
      });
    });
  };

  const sharedInWip = async (history: any) => {
    fileLoading.value = true;
    const res = await getSharedFiles(history.bizId);

    res.data.list.forEach((item: any) => {
      item.id = item.fileId;
    });

    tableData.value = res.data.list;
    breadcrumbData.value = [];
    fileLoading.value = false;
  };

  /**
   * 待审核共享包点击事件
   * @param history 共享交付包信息
   */
  const sharedTobeReviewed = async (history: any) => {
    fileLoading.value = true;
    const res = await getSharedFiles(history.bizId);
    tableData.value = res.data.list;
    breadcrumbData.value = [];
    fileLoading.value = false;
  };

  const saveShared = async (history: any) => {
    const res = await getShareDetail({
      params: { id: history.bizId },
    });
    const shareData = res.data;
    if (shareData) {
      fileTableRef.value.startShare(shareData);
    }
  };

  const visible = ref(false);
  let modalHistory: any = null;
  const modalObject = ref<BubbleObject>({});
  const handleBeforeOk = async () => {
    await consumeSharedFiles(
      modalHistory.bizId,
      modalHistory.projectId,
      currentTeamId.value
    );
    refreshTeamData();
    sharedInWip(modalHistory);
    visible.value = false;
    // return true;
  };
  const handleCancel = () => {
    visible.value = false;
  };
  // 转换 folderTreeList 为 a-tree 结构
  const transformToTreeData = (folderTreeList: any) => {
    return folderTreeList.map((folder: any) => {
      const children = folder.children
        ? transformToTreeData(folder.children)
        : []; // 递归处理子文件夹

      const files = folder.files
        ? folder.files.map((file: any) => ({
            title: file.name,
            key: file.fileId,
            name: file.name,
            graphicEngineInfo: file.graphicEngineInfo,
            size: file.size,
            version: file.version,
            fileToken: file.fileToken,
            fileId: file.fileId,
            isFolder: false,
            abandon: file.abandon,
          }))
        : [];
      return {
        title: folder.name,
        key: folder.id,
        children: [...children, ...files], // 合并子文件夹和文件
        isFolder: true, // 设置为文件夹
      };
    });
  };
  // 定义缓存按钮提交时，是否显示提示的条件
  let catchBtnIsShow = false;

  const otherTeamShared = async (history: any) => {
    const res = await getSharedFiles(history.bizId);
    catchBtnIsShow = res.data.list.some((item: any) => {
      return item.abandon === 1;
    });
    const params = {
      id: history.bizId,
    };
    const resFloder = await getDetailTree(params); // 现在要取这个接口的folderTreeList
    visible.value = true;
    modalHistory = history;
    modalObject.value = {
      shareName: history.name,
      user: history.createUserName,
      time: history.createDate,
      files: resFloder.data.folderTreeList || [],
    };
    // 格式化数据
    formattedTreeData.value = transformToTreeData(modalObject.value.files);
  };
  // 函数，用于计算文件数量
  const countFiles = (files: any) => {
    let count = 0;

    const recursiveCount = (item: any) => {
      // 增加文件的计数
      if (item.files) {
        count += item.files.length; // 如果有文件，则增加数量
      }

      // 遍历子文件夹
      if (item.children) {
        item.children.forEach((child: any) => recursiveCount(child));
      }
    };

    // 开始递归计算
    if (files) {
      files.forEach((file: any) => recursiveCount(file));
    }

    return count;
  };
  // 处理树的展开事件
  const onExpand = (expandedKeysValue: string[]) => {
    expandedKeys.value = expandedKeysValue;
  };

  // 处理树的选择事件
  const onSelect = (selectedKeys: string[]) => {
    console.log('Selected keys:', selectedKeys);
    // 这里可以根据需要执行文件或文件夹的查看操作
  };
  const saveDelivery = async (history: any) => {
    const res = await getDeliveryDetail({
      params: { id: history.bizId },
    });
    const deliveryData = res.data;
    fileTableRef.value.startDeliver(deliveryData);
  };

  const deliveryTobeReviewed = async (history: any) => {
    fileLoading.value = true;
    const res = await getDeliveryFiles(history.bizId);
    tableData.value = res.data.list;
    breadcrumbData.value = [];
    fileLoading.value = false;
  };

  const deliveryInWip = async (history: any) => {
    fileLoading.value = true;
    const res = await getDeliveryFiles(history.bizId);
    tableData.value = res.data.list;
    breadcrumbData.value = [];
    fileLoading.value = false;
  };

  const setConstant = (size: string) => {
    if (size === 'normal') {
      CONSTANT.LINE_HEIGHT = 28;
      CONSTANT.CIRCLE_DIAMETER = 25;
      CONSTANT.RECT_WIDTH = 25;
      CONSTANT.DRUM_WIDTH = 32;
      CONSTANT.DRUM_HEIGHT = 25;
    } else if (size === 'small') {
      CONSTANT.LINE_HEIGHT = 24;
      CONSTANT.CIRCLE_DIAMETER = 20;
      CONSTANT.RECT_WIDTH = 20;
      CONSTANT.DRUM_WIDTH = 28;
      CONSTANT.DRUM_HEIGHT = 20;
    }
  };

  /**
   * 加载一条团队信息
   */
  const loadmore = () => {
    if (teamData.value.length === 0) {
      Message.warning(t('design.no-team'));
      return;
    }
    setConstant('normal');
    showButtonSwitch.value = 1;
    filterData.value = [teamData.value[0]];
    filterData.value.forEach((item) => {
      item.beforeHistoryMap = null;
    });
    uploadTimelineRef.value?.teamBoxToTop();
  };

  /**
   * 加载多条团队信息
   */
  const loadmore2 = () => {
    if (teamData.value.length === 1) {
      Message.warning(t('design.no-more-team'));
      return;
    }
    setConstant('normal');
    maxLineNum = 4;
    showButtonSwitch.value = 2;
    filterStart.value = 0;
    filterData.value = teamData.value.slice(0, maxLineNum);
    filterData.value.forEach((item) => {
      item.beforeHistoryMap = null;
    });
  };

  /**
   * 加载多条团队信息
   */
  const loadmore3 = () => {
    if (
      teamData.value.length === 1 ||
      (teamData.value.length <= 4 && showButtonSwitch.value === 2)
    ) {
      Message.warning(t('design.no-team'));
      return;
    }
    if (teamData.value.length <= 4) {
      loadmore2();
      return;
    }
    setConstant('small');
    maxLineNum = 8;
    showButtonSwitch.value = 3;
    filterStart.value = 0;
    filterData.value = teamData.value.slice(0, maxLineNum);
    filterData.value.forEach((item) => {
      item.beforeHistoryMap = null;
    });
  };

  /**
   * 关闭泳道图
   */
  const close = () => {
    showButtonSwitch.value = 0;
    filterData.value = [];
    filterStart.value = 0;
  };

  /**
   * 滚动加载团队信息
   */
  const changeFilterData = (sliceVal: number) => {
    filterStart.value = sliceVal;
    filterData.value = teamData.value.slice(sliceVal, sliceVal + maxLineNum);
    filterData.value.forEach((item) => {
      item.beforeHistoryMap = null;
    });
  };

  //  获取用户在当前项目下的teams和权限;
  const getTeamData = async () => {
    try {
      const res = await getUserTeamsInPrj(route.params.projectId as string);
      if (res.status) {
        teamList.value = res.data[0].teamList || [];
      }
    } catch (err) {
      console.log(err);
    } finally {
      // handle finally
    }
  };

  onMounted(async () => {
    await getTeamData(); // 获取用户在当前项目下的teams和权限
    currentTeamId.value = getCurrentTeamId();
    console.log(currentTeamId.value, 878);
    if (currentTeamId.value) pageLoading.value = true;
    // 获取所有wip下folder信息，用来做teamId->folderId的映射
    await getTeamFolder();
    // 获取当前文件区文件
    if (currentTeamId.value) setCurrentFolder(currentTeamId.value);
    const allPromiseResult = {
      teamData: [],
      planData: [],
    };
    // 制造获取所有team的promise List
    const teamDataPromiseList = await getTeamDataPromise(allPromiseResult);
    // 制造获取planData的promise
    const planDataPromise = getPlanDataPromise(allPromiseResult);
    await Promise.all([...teamDataPromiseList, planDataPromise]);
    teamData.value = allPromiseResult.teamData;
    planData.value = allPromiseResult.planData;
    if (currentTeamId.value) {
      loadmore();
      pageLoading.value = false;
    }
  });

  const timeLine = ref<HTMLElement | null>(null); // 用于引用 DOM 元素
  const tableHeight = ref<string>(''); // 用于存储高度
  let resizeObserver: ResizeObserver | null = null; // 用于存储 ResizeObserver 实例

  onMounted(() => {
    if (timeLine.value) {
      // 初始化 ResizeObserver
      resizeObserver = new ResizeObserver((entries) => {
        // eslint-disable-next-line no-restricted-syntax
        for (const entry of entries) {
          const { height } = entry.contentRect; // 直接在循环内声明 height
          tableHeight.value = `calc(100vh - ${height}px - 304px)`;
          console.log('[ tableHeight.value ] >', tableHeight.value);
        }
      });

      // 开始观察元素
      resizeObserver.observe(timeLine.value);
    }
  });

  onUnmounted(() => {
    // 在组件卸载时停止观察
    if (resizeObserver && timeLine.value) {
      resizeObserver.unobserve(timeLine.value);
    }
  });
</script>

<style lang="less" scoped>
  .project-home {
    width: 100%;
    height: 100%;
    padding: 16px 20px;
  }

  .container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    padding: 20px;
    display: flex;
    flex-direction: column;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #d9d9d9;
  }
  .timeline {
    border-bottom: 1px solid var(--color-border);
    width: 100%;
    border-radius: 8px;
    position: relative;
    transition: height 0.2s ease-out;
  }
  .button-box {
    width: 108px;
    height: 38px;
    position: absolute;
    bottom: -38px;
    left: 50%;
    border: 1px solid var(--color-border);
    border-top: 1px solid var(--color-bg-1);
    border-radius: 0 0 8px 8px;
    .loadmore-button {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      margin-top: -4px;
      cursor: pointer;
      img {
        width: 96px;
        height: 37px;
      }
    }
    .middle-button {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .button-list {
        width: 96px;
        height: 32px;
        background-color: rgb(235, 240, 255);
        color: #3366ff;
        border-radius: 6px;
        position: relative;
        margin-top: -4px;
        display: flex;
        .button-base {
          height: 32px;
          width: 32px;
          color: #3366ff;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 6px;
        }
        .button-base:hover {
          background-color: #3366ff;
          color: #fff;
        }
      }
    }
  }
  .button-explain {
    display: flex;
    align-items: center;
    .explain-icon {
      margin-right: 8px;
      cursor: pointer;
    }
    .explain-self {
      margin-right: 16px;
    }
    .explain-other {
      color: #999999;
    }
  }
  .file {
    width: 100%;
    padding: 24px 44px 0 44px;
    flex: 1;
  }
  .file-header {
    font-size: 18px;
    font-weight: 500;
    margin: -24px 0 24px 0;
    display: flex;
    img {
      margin-right: 8px;
    }
    .text {
      color: var(--color-text-1);
      font-size: 18px;
      font-weight: 500;
    }
  }
  .line > span:first-child {
    display: inline-block;
    width: 78px;
    color: #86909c;
    text-align: right;
    margin-right: 8px;
  }
  .line {
    line-height: 32px;
  }
  .file-card {
    width: 100%;
    height: 32px;
    background-color: #f7f8fa;
    color: #3366ff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    border-radius: 4px;
    margin-top: 12px;
    div {
      display: flex;
      align-items: center;
      width: calc(100% - 16px);
      span {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }
      img {
        cursor: pointer;
      }
    }
  }
  .custom-tree {
    max-height: 500px; /* 设置最大高度 */
    overflow-y: auto; /* 溢出时可滚动 */
  }
</style>
