import * as Icons from './toolIcons';

import IssueList from '@/views/bim-view/components/toolbar/components/issue-list/index.vue';
// import Quantities from '@/views/model-view/components/quantities/index.vue';
import modelAssembly from '@/views/bim-view/components/toolbar/components/modelAssembly/index.vue';
import Collision from '@/views/bim-view/components/toolbar/components/collision/index.vue';
// import linkage from '@/views/model-view/components/Linkage/index.vue';
// import Annotation from '@/views/model-view/components/annotation/index.vue';

function getQueryParams(): Record<string, string> {
  const queryParams: Record<string, string> = {};
  const urlParams = new URLSearchParams(window.location.search);
  // eslint-disable-next-line
  for (const [key, value] of urlParams.entries()) {
    queryParams[key] = value;
  }
  return queryParams;
}

// 默认
const defaultTools: any = [
  {
    id: 'issueList',
    name: '问题列表 Issue List',
    defaultIcon: Icons.issueList,
    component: IssueList,
    // eslint-disable-next-line
    disabled: getQueryParams()['noIssue'] === 'true' ? true : false,
    width: 400,
  },
  // {
  //   id: 'quantities',
  //   name: '工程量统计',
  //   defaultIcon: Icons.quantities,
  //   component: Quantities,
  //   // eslint-disable-next-line
  //   disabled: getQueryParams()['noTools'] === 'true' ? true : false,
  //   width: 800,
  // },
  // {
  //   id: '2dViewList',
  //   name: '二三维联动',
  //   defaultIcon: Icons.linkage,
  //   component: linkage,
  //   width: 0,
  // },
];

// 合模
const mergeTools: any = [
  {
    id: 'issueList',
    name: '问题列表 Issue List',
    defaultIcon: Icons.issueList,
    component: IssueList,
    width: 400,
  },
  {
    id: 'modelAssembly',
    name: '模型装配',
    defaultIcon: Icons.linkage,
    component: modelAssembly,
    width: 400,
  },
];

// 碰撞检测
const collisionTools: any = [
  {
    id: 'issueList',
    name: '问题列表 Issue List',
    defaultIcon: Icons.issueList,
    component: IssueList,
    width: 400,
  },
  // {
  //   id: 'collision',
  //   name: '碰撞检查 Collision Detection',
  //   defaultIcon: Icons.issue,
  //   component: Collision,
  //   width: 800,
  // },
];

const d2Tools: any = [
  // {
  //   id: 'sign',
  //   name: '图纸批注',
  //   defaultIcon: Icons.sign,
  //   component: Annotation,
  //   width: 0,
  // },
];

export const getAllTools = () => {
  const tools = [...d2Tools, ...defaultTools, ...collisionTools, ...mergeTools];
  const result: any = [];
  tools.forEach((e: any) => {
    const isExit = result.find((a: any) => a.id === e.id);
    if (!isExit) result.push(e);
  });
  return result;
};

export default {
  defaultTools,
  mergeTools,
  collisionTools,
  d2Tools,
  getAllTools,
};
