import axios from 'axios';

export interface metasData {
  name: string;
  type: number;
  description: string;
}

export interface dimensionsData {
  name: string;
  description: string;
}

export interface metasParams extends Partial<metasData> {
  pageNo: number;
  pageSize: number;
  groupId: string;
}

// 创建维表
export function addDimensions(data: dimensionsData) {
  const param: dimensionsData = { ...data };
  return axios.post<string>('/asset-system/dimension/save', param);
}

// 更新维表
export function updateDimensions(data: any) {
  return axios.post('/asset-system/dimension/update', data);
}

// 查询维表详情
export function queryDimensionsInfo(id: any) {
  return axios.get<string>('/asset-system/dimension/detail', {
    params: {
      id,
    },
  });
}

// 查询元属性列表-分页
export function queryMetasList(params: metasParams) {
  return axios.get<string>('/asset-system/attribute/list', {
    params,
  });
}

// 查询规则
export function queryRuleList(params: metasParams) {
  return axios.get<any>('/cde-collaboration/asset-system/rule/page', {
    params,
  });
}

// 查询维表
export function queryDimensionList(params: metasParams) {
  return axios.get<any>('/asset-system/dimension/list', {
    params,
  });
}

// 查询标准
export function queryStandardList(params: metasParams) {
  return axios.get<any>('/asset-system/standard/list', {
    params,
  });
}

// 创建元属性
export function addMetas(data: any) {
  const param: metasData = { ...data };
  return axios.post<string>('/asset-system/attribute/save', param);
}

// 更新元属性状态
export function updateMetas(data: any) {
  return axios.post('/asset-system/attribute/update', data);
}

// 删除
export function deleteMetas(data: any) {
  return axios.post<string>('/asset-system/attribute/remove', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// 添加用户在项目下元属性的配置
export function addUserProjectMeta(data: any) {
  return axios.post('/cde-collaboration/filemeta/user/save', data);
}

// 查询用户在项目下元属性的配置
export function getUserProjectMeta(params: any) {
  return axios.get('/cde-collaboration/filemeta/user/file-metas', { params });
}
