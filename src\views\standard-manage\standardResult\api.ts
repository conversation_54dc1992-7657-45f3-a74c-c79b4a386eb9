import axios from 'axios';

// 查询自定义标签
export function getTag(params: any) {
  return axios.get<any>('/asset-system/tag/list', {
    params,
  });
}

// 获取模型数据
export function getChildrenAll(params: any) {
  return axios.get<any>('/asset-system/folder/childrenAll', {
    params,
  });
}
// 获取标准校验结果
export function getCheckoutResult(data: any) {
  return axios.post('/cde-collaboration/bimFileCheckout/result', data);
}
// 获取历史版本数据
export function getHistoryData(params: any) {
  return axios.get<any>('/cde-collaboration/bimFileCheckout/timeHistory', {
    params,
  });
}

// 模型校验报告导出
export function GetExportFile(data: object) {
  return axios.post(
    '/cde-collaboration/bimFileCheckout/checkResultExport',
    data,
    {
      responseType: 'blob',
    }
  );
}
