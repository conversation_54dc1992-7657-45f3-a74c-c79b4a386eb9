import { AppRouteRecordRaw } from '../types';

const STANDARD: AppRouteRecordRaw = {
  path: 'standard',
  name: 'standard',
  // redirect: '/standard/list',
  // component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.standard-manage',
    requiresAuth: true,
    icon: 'icon-bookmark',
    hideInMenu: false,
    order: 100,
    globalMode: ['project'],
  },
  children: [
    {
      path: 'list',
      name: 'standardList',
      component: () =>
        import('@/views/standard-manage/standard-list/index.vue'),
      meta: {
        locale: 'menu.standard-list',
        requiresAuth: true,
        hideInMenu: false,
        order: 1,
        showAI: true,
        globalMode: ['project'],
      },
    },
    {
      path: 'standardDetail',
      name: 'standardDetail',
      component: () =>
        import(
          '@/views/standard-manage/standard-list/standardDetail/index.vue'
        ),
      meta: {
        locale: 'menu.standard-list',
        requiresAuth: true,
        hideInMenu: true,
        globalMode: ['project'],
      },
    },
    // 分类和编码标准
    {
      path: 'classCodeStandard',
      name: 'classCodeStandard',
      component: () =>
        import('@/views/standard-manage/classCodeStandard/index.vue'),
      meta: {
        locale: 'menu.class-code-standard',
        requiresAuth: true,
        hideInMenu: false,
        order: 2,
        showAI: true,
        globalMode: ['project'],
      },
    },
    // 属性特征标准
    {
      path: 'attrFeaturesStandard',
      name: 'attrFeaturesStandard',
      component: () =>
        import('@/views/standard-manage/attrFeaturesStandard/index.vue'),
      meta: {
        locale: 'menu.attr-features-standard',
        requiresAuth: true,
        hideInMenu: false,
        order: 3,
        showAI: true,
        globalMode: ['project'],
      },
    },
    // 字典配置
    {
      path: 'dictionaryConfig',
      name: 'dictionaryConfig',
      component: () =>
        import('@/views/standard-manage/dictionaryConfig/index.vue'),
      meta: {
        locale: 'menu.dictionary-onfig',
        requiresAuth: true,
        hideInMenu: false,
        order: 4,
        showAI: true,
        globalMode: ['project'],
      },
    },
    // 属性校验
    {
      path: 'attrCheck',
      name: 'attrCheck',
      component: () => import('@/views/standard-manage/attrCheck/index.vue'),
      meta: {
        locale: 'menu.attr-check',
        requiresAuth: true,
        hideInMenu: false,
        order: 5,
        showAI: true,
        globalMode: ['project'],
      },
    },
    // 命名校验
    {
      path: 'namedCheck',
      name: 'namedCheck',
      component: () => import('@/views/standard-manage/namedCheck/index.vue'),
      meta: {
        locale: 'menu.named-check',
        requiresAuth: true,
        hideInMenu: false,
        order: 6,
        showAI: true,
        globalMode: ['project'],
      },
    },
  ],
};

export default STANDARD;
