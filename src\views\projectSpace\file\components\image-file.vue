<template>
  <img
    v-if="fileType === 'folder'"
    src="@/assets/images/file-manager/folder.png"
  />
  <img
    v-else-if="fileType === 'item'"
    src="@/assets/images/file-manager/item.png"
  />
  <img
    v-else-if="fileType === 'meeting'"
    src="@/assets/images/file-manager/meeting.png"
  />
  <img
    v-else-if="fileType === 'sysFolder'"
    src="@/assets/images/file-manager/sysFolder.png"
  />
  <img
    v-else-if="fileType === 'WIP'"
    src="@/assets/images/file-manager/Wip.png"
  />
  <img
    v-else-if="fileType === 'Shared'"
    src="@/assets/images/file-manager/Shared.png"
  />
  <img
    v-else-if="fileType === 'Published'"
    src="@/assets/images/file-manager/Published.png"
  />
  <img
    v-else-if="fileType === '3dm'"
    src="@/assets/images/file-format/3dm.png"
  />
  <img
    v-else-if="fileType === '3ds'"
    src="@/assets/images/file-format/3ds.png"
  />
  <img
    v-else-if="fileType === 'cel'"
    src="@/assets/images/file-format/cel.png"
  />
  <img
    v-else-if="fileType === 'dae'"
    src="@/assets/images/file-format/dae.png"
  />
  <img
    v-else-if="fileType === 'dgn'"
    src="@/assets/images/file-format/dgn.png"
  />
  <img
    v-else-if="fileType === 'dgnlib'"
    src="@/assets/images/file-format/dgnlib.png"
  />
  <img
    v-else-if="['doc', 'docx'].includes(fileType)"
    src="@/assets/images/file-format/doc.png"
  />
  <img
    v-else-if="fileType === 'dwf'"
    src="@/assets/images/file-format/dwf.png"
  />
  <img
    v-else-if="fileType === 'dwg'"
    src="@/assets/images/file-format/dwg.png"
  />
  <img
    v-else-if="fileType === 'dxf'"
    src="@/assets/images/file-format/dxf.png"
  />
  <img
    v-else-if="fileType === 'fbx'"
    src="@/assets/images/file-format/fbx.png"
  />
  <img
    v-else-if="fileType === 'glb'"
    src="@/assets/images/file-format/glb.png"
  />
  <img
    v-else-if="fileType === 'hln'"
    src="@/assets/images/file-format/hln.png"
  />
  <img
    v-else-if="fileType === 'ifc'"
    src="@/assets/images/file-format/ifc.png"
  />
  <img
    v-else-if="fileType === 'igs'"
    src="@/assets/images/file-format/igs.png"
  />
  <img
    v-else-if="fileType === 'jpg'"
    src="@/assets/images/file-format/jpg.png"
  />
  <img
    v-else-if="fileType === 'nwc'"
    src="@/assets/images/file-format/nwc.png"
  />
  <img
    v-else-if="fileType === 'nwd'"
    src="@/assets/images/file-format/nwd.png"
  />
  <img
    v-else-if="fileType === 'obj'"
    src="@/assets/images/file-format/obj.png"
  />
  <img
    v-else-if="fileType === 'osgb'"
    src="@/assets/images/file-format/osgb.png"
  />
  <img
    v-else-if="fileType === 'pdf'"
    src="@/assets/images/file-format/pdf.png"
  />
  <img
    v-else-if="fileType === 'png'"
    src="@/assets/images/file-format/png.png"
  />
  <img
    v-else-if="['ppt', 'pptx'].includes(fileType)"
    src="@/assets/images/file-format/ppt.png"
  />
  <img
    v-else-if="fileType === 'rfa'"
    src="@/assets/images/file-format/rfa.png"
  />
  <img
    v-else-if="fileType === 'rvt'"
    src="@/assets/images/file-format/rvt.png"
  />
  <img
    v-else-if="fileType === 'skp'"
    src="@/assets/images/file-format/skp.png"
  />
  <img
    v-else-if="fileType === 'stl'"
    src="@/assets/images/file-format/stl.png"
  />
  <img
    v-else-if="fileType === 'stp'"
    src="@/assets/images/file-format/stp.png"
  />
  <img
    v-else-if="fileType === 'step'"
    src="@/assets/images/file-format/step.png"
  />
  <img
    v-else-if="fileType === 'txt'"
    src="@/assets/images/file-format/txt.png"
  />
  <img
    v-else-if="fileType === 'x_t'"
    src="@/assets/images/file-format/x_t.png"
  />
  <img
    v-else-if="['xls', 'xlsx', 'xlsm', 'xlsb', 'csv'].includes(fileType)"
    src="@/assets/images/file-format/xlsx.png"
  />
  <img
    v-else-if="fileType === 'xml'"
    src="@/assets/images/file-format/xml.png"
  />
  <img
    v-else-if="fileType === 'gltf'"
    src="@/assets/images/file-format/gltf.png"
  />
  <img v-else src="@/assets/images/file-format/other.png" />
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    fileName: {
      type: String,
      required: true,
    },
    isFile: {
      type: Boolean,
      required: true,
    },
    size: {
      type: String,
      default: '20px',
    },
    isSysFile: {
      type: Boolean,
      default: false,
    },
    attatchmentType: {
      type: String,
      default: '',
    },
  });

  const getFileType = () => {
    let result = '';
    const nameSuffix = props.fileName.split('.').pop()?.toLocaleLowerCase();
    if (!props.isFile) {
      if (props.isSysFile) {
        result = 'sysFolder';
      } else if (props.fileName === 'WIP') {
        result = 'WIP';
      } else if (props.fileName === 'Shared') {
        result = 'Shared';
      } else if (props.fileName === 'Published') {
        result = 'Published';
      } else result = 'folder';

      if (props.attatchmentType) {
        result = props.attatchmentType;
      }
    } else if (nameSuffix) {
      result = nameSuffix;
    }
    return result;
  };
  let fileType: string = getFileType();

  watch(
    () => props.fileName,
    (val) => {
      if (val) fileType = getFileType();
    }
  );
</script>

<style lang="less" scoped>
  img {
    width: v-bind(size);
  }
</style>
