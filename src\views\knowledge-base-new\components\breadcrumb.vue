<template>
  <a-breadcrumb :max-count="4">
    <template #separator>
      <icon-right />
    </template>
    <a-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="item.id">
      <a-tooltip :content="item.name" mini>
        <div class="breadcrumb-item" @click="handleClick(item, index)">{{
          item.name
        }}</div>
      </a-tooltip>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import { getFolderById } from '@/store/modules/file/utils';
  import { defineEmits, toRefs } from 'vue';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';

  const knowledgeStore = useKnowledgeBaseNewStore();
  const { referenceModal } = storeToRefs(knowledgeStore);
  const { breadcrumbList, allTreeData } = toRefs(referenceModal.value);

  const emits = defineEmits(['expendFolder']);

  function handleClick(item: { name: string; id: string }, index: number) {
    if (index === breadcrumbList.value.length - 1) return;
    const folder = getFolderById(allTreeData.value, item.id);
    emits('expendFolder', folder);
  }
</script>

<style lang="less" scoped>
  .breadcrumb-item {
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100px;
  }
  :deep(.arco-breadcrumb-item:last-child) {
    font-weight: 600;
  }
  :deep(.arco-breadcrumb-item-separator) {
    margin: 0;
  }
</style>
