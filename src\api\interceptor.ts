import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Message } from '@arco-design/web-vue';
// @ts-ignore
import { getToken, clearToken, getWpsToken, clearWpsToken } from '@/utils/auth';
// @ts-ignore
import { getUTCStr } from '@/utils/timezone';
import defaultSettings from '@/config/sys-settings';
import { SM4Encrypt } from '@/utils/encryption/sm4';
import { LocationQueryRaw } from 'vue-router';
import { slashToDot } from '@/utils/index';
import { removeRouteListener } from '@/utils/route-listener';
import router from '@/router';
import { getSign, getSignWps } from '../utils/request-sign';
// @ts-ignore
import { verifyRequest, verifyResponse, verifyUrl } from '@/utils/verify-api';
import i18n from '@/locale/index';
import { getLocalstorage } from '@/utils/localstorage';

import { ignoreErrMsg } from '@/directionary/request-list';

const { t } = i18n.global;
export interface HttpResponse<T = unknown> {
  status: number;
  msg: string;
  message?: string;
  code: number;
  data: T;
}

export interface IFusionBiz {
  timeZone?: string;
  timeFormat?: string;
  portalId?: string;
  buttonId?: string;
  apiCrypto?: any;
  companyId?: any;
  projectId?: any;
}

function errorMsgFormate(res: any) {
  if (typeof res !== 'object' || !res.message) return 'Request Error';
  let msg = res.message;
  const { data } = res;
  if (typeof data !== 'object' || !data) return msg;
  let keys = Object.keys(data);
  keys = keys.filter((key: string) => data[key]);
  keys.forEach((key: string) => {
    msg = msg.replace(`{${key}}`, data[key]);
  });
  return msg;
}

// const XBaseCode = [
//   0, 10370012, 10460806, 10001004, 10490001, 10170053, 10170058,
// ];

if (import.meta.env.VITE_API_BASE_URL) {
  axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;
  axios.defaults.withCredentials = true;
  axios.defaults.timeout = 600000;
}

// 是否解密响应
const responseEncryptMap = new Map();

// 生成FusionBiz头部
export const setFusionBiz = () => {
  const FusionBiz: IFusionBiz = {};
  // 通过请求头传递时区
  const UTC = getUTCStr(defaultSettings.timezone);
  FusionBiz.timeZone = UTC;
  FusionBiz.timeFormat = 'yyyy-MM-dd HH:mm:ss';
  FusionBiz.companyId = getLocalstorage('companyId');
  return SM4Encrypt(JSON.stringify(FusionBiz));
};

axios.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    let FusionBiz: IFusionBiz = {};
    // 通过请求头传递时区
    const UTC = getUTCStr(defaultSettings.timezone);
    FusionBiz.timeZone = UTC;
    FusionBiz.timeFormat = 'yyyy-MM-dd HH:mm:ss';
    // todo 方便测试此处先写死100000  后续切换身份做好后 companyId动态获取
    FusionBiz.companyId = getLocalstorage('companyId');

    const url = config.url || '';

    // 请求头添加当前路由下的projectId
    let projectId: any = '';
    if (window.location.pathname.includes('/project/')) {
      const matchResult: any =
        window.location.pathname.match(/\/project\/(.*?)\//);
      [, projectId] = matchResult;
      // 获取项目列表接口不需要添加projectId
      if (config.url !== '/cde-collaboration/project/list') {
        FusionBiz.projectId = projectId;
      }
    }
    // const len = url.indexOf('?');
    // const subUrl =
    //   (len > -1
    //     ? url.substring(url.indexOf('/', 1), len)
    //     : url.substring(url.indexOf('/', 1))) || url;
    // const requestEncrypt =
    //   verifyRequest(config) && defaultSettings.requestEncrypt;
    // const responseEncrypt =
    //   verifyResponse(config) && defaultSettings.responseEncrypt;
    // const apiCrypto = {
    //   requestEncrypt: {
    //     encrypt: requestEncrypt,
    //     ignoreUrl: requestEncrypt ? '' : subUrl,
    //   },
    //   responseEncrypt: {
    //     encrypt: responseEncrypt,
    //     ignoreUrl: responseEncrypt ? '' : subUrl,
    //   },
    // };
    // FusionBiz = { ...FusionBiz, apiCrypto };
    FusionBiz = { ...FusionBiz };
    // responseEncryptMap.set(config.url, apiCrypto.responseEncrypt.encrypt);
    config.headers = config.headers || {};
    const { timeZone } = Intl.DateTimeFormat().resolvedOptions();
    config.headers['Time-Zone'] = timeZone;
    config.headers['Lang-Code'] =
      i18n.global.locale === 'en-US' ? 'en' : i18n.global.locale;
    if (config.headers.isNeedToken !== 'false') {
      config.headers.isNeedToken = 'true';
    }
    if (url !== '/cde-collaboration/chBim/verifySignature') {
      config.headers['Fusion-Biz'] = SM4Encrypt(JSON.stringify(FusionBiz));
    }
    if (verifyUrl(config, 'sign')) {
      // 若为wps接口 使用wps签名
      if (config.baseURL === '/wps') config.params = getSignWps(config.params);
      else config.params = getSign(config.params);
      delete config.headers['Fusion-Biz'];
    } else if (config.headers.isNeedToken === 'false') {
      config.headers['Fusion-Auth'] = '';
    } else if (config.baseURL === '/wps') {
      config.headers['Fusion-Auth'] = getWpsToken() || '';
      config.headers['System-Auth'] = getToken() || '';
    } else config.headers['Fusion-Auth'] = getToken() || '';

    // if (
    //   apiCrypto.requestEncrypt.encrypt &&
    //   !apiCrypto.requestEncrypt.ignoreUrl
    // ) {
    //   // config = encrypt(config);
    // }
    return config;
  },
  (error) => {
    // do something
    return Promise.reject(error);
  }
);
// add response interceptors
axios.interceptors.response.use(
  (response: AxiosResponse<HttpResponse>) => {
    if (responseEncryptMap.get(response?.config?.url)) {
      // response = decrypt(response);
    }
    if (response.config?.responseType === 'blob') {
      return response;
    }
    const res = response.data;

    if (verifyUrl(response.config, 'cMsg')) {
      return res;
    }
    if (res?.code !== 8000000) {
      if (res?.message) Message.error(res.message || '');
    }
    return res;
  },
  (error) => {
    if (error.message === 'canceled') return error; // 暂停上传
    let message = '';
    const errMsg = error.toJSON();
    if (errMsg) {
      switch (errMsg.status) {
        case 303:
          message = error.response?.data?.message || '无权限访问';
          break;
        case 400:
          message = t('api.error-request');
          break;
        case 401:
          message = error.response?.data?.message || t('api.unauthorized');
          // 跳回登录页，此时store未挂载，不能使用store，只能清除一下storage
          clearToken();
          clearWpsToken();
          localStorage.removeItem('username');
          removeRouteListener();
          router.push({
            name: 'login',
            query: {
              redirect: slashToDot(router.currentRoute?.value?.path),
              ...router.currentRoute?.value?.query,
            } as LocationQueryRaw,
          });
          break;
        case 403:
          message = t('api.access-denied');
          break;
        case 404:
          message = t('api.resource-not-found');
          break;
        case 405:
          message = t('api.method-not-allowed');
          break;
        case 408:
          message = t('api.request-timeout');
          break;
        case 500:
          message = t('api.server-error');
          break;
        case 501:
          message = t('api.network-not-implemented');
          break;
        case 502:
          message = t('api.network-error');
          break;
        case 503:
          message = t('api.service-unavailable');
          break;
        case 504:
          message = t('api.gateway-timeout');
          break;
        case 505:
          message = t('api.http-version-not-supported');
          break;
        default:
          message = t('api.connection-error') + errMsg.status;
      }
      if (errMsg.config && verifyUrl(errMsg.config, 'cMsg')) {
        message = '';
      }
    }
    if (message) {
      Message.error({
        content: message || t('api.request-error'),
        duration: 3 * 1000,
      });
    }
    return Promise.reject(error);
  }
);
