<template>
  <a-row justify="space-between" align="center" class="title-bar">
    <a-col :span="5">
      <div v-if="props.showTitle" class="title-left">
        <img class="icon" :src="pageTitleIcon" />
        <span>{{ props.titleText }}</span>
      </div>
    </a-col>
    <a-col v-if="props.showButton" :span="8" class="btn">
      <a-space>
        <!-- <slot name="button-list">
          <a-button type="primary" @click="handleViewDiagram">{{
            props.diagramBtnText
          }}</a-button>
        </slot> -->
        <slot name="button-list">
          <a-button :type="props.btnType" @click="handleAdd">
            <template v-if="$slots['button-content']">
              <slot name="button-content" />
            </template>
            <template v-else>
              {{ props.btnText }}
            </template>
          </a-button>
        </slot>
      </a-space>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
  import pageTitleIcon from '@/assets/images/table-title.png';

  const props = withDefaults(defineProps<{
    showTitle?: boolean
    showButton?: boolean
    titleText?: string
    btnText?: string
    diagramBtnText?: string
    btnType?: 'primary' | 'secondary' | 'outline' | 'dashed' | 'text'
  }>(), {
    showTitle: true,
    showButton: true,
    titleText: '列表',
    btnText: '添加',
    diagramBtnText: '查看图示',
    btnType: 'primary',
  });

  const emit = defineEmits(['add', 'open']);

  const handleAdd = () => {
    emit('add');
  };

  const handleViewDiagram = () => {
    emit('open');
  };
</script>

<style scoped lang="less">
  .title-bar {
    margin-bottom: 16px;
    :deep(button) {
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    }

    .title-left {
      display: flex;
      height: 32px;
      align-items: center;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      color: #1D2129;
      line-height: 21px;

      .icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
    }

    .btn {
      text-align: right;

      /* .arco-btn-size-medium {
        border-radius: var(--border-radius-medium);
      } */
    }
  }
</style>
