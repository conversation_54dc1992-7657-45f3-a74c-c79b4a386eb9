<template>
  <div class="content">
    <div class="describe-box">
      <a-input v-model="title" class="title" placeholder="请输入标题"></a-input>
      <!-- <a-dropdown>
        <a-button type="text">
          <template #icon>
            <icon-more />
          </template>
        </a-button>
        <template #content>
          <a-doption value="delete" @click="deleteMeetOrMatter">删除</a-doption>
        </template>
      </a-dropdown> -->
    </div>

    <!-- 子事项清单 -->
    <div class="describe" style="margin-bottom: 8px">
      <icon-apps class="describe-icon" />
      <span class="describe-span"> 类别</span>

      <a-space style="margin-left: 23px">
        <a-button class="meeting-button" @click="categoryChange('meeting')"
          >会议</a-button
        >
        <a-button class="matter-button" @click="categoryChange('matter')"
          >事项</a-button
        >
      </a-space>
    </div>

    <categoryMeeting
      v-if="categoryType === 'meeting'"
      v-model:title="title"
      type="create"
      @refresh="refreshMeeting"
    />
    <categoryMatter
      v-if="categoryType === 'matter'"
      v-model:title="title"
      :type="'new'"
      :category-type="categoryType"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import categoryMatter from './category-matter.vue';
  import categoryMeeting from '@/views/schedule/component/meeting/componemt/createEditMeeting.vue';

  const title = ref('');

  const categoryType = ref('meeting');
  const matterTextColor = ref('#4E5969');
  const matterBackColor = ref('#fff');
  const meetingTextColor = ref('#3366FF');
  const meetingBackColor = ref('var(--color-secondary)');

  const categoryChange = (value: string) => {
    categoryType.value = value;
    meetingTextColor.value = value === 'meeting' ? '#3366FF' : '#4E5969'; // 修改会议文本颜色
    meetingBackColor.value =
      value === 'meeting' ? 'var(--color-secondary)' : '#fff'; // 修改会议背景颜色
    matterTextColor.value = value === 'matter' ? '#3366FF' : '#4E5969'; // 修改事项文本颜色
    matterBackColor.value =
      value === 'matter' ? 'var(--color-secondary)' : '#fff'; // 修改事项背景颜色
  };

  defineExpose({
    categoryChange,
  });

  // const warningMsg = ref();
  // const deleteMeetOrMatter = async () => {
  //   if (categoryType.value === 'matter') {
  //     const res = await deleteMatterFlag('1902557398254489602');
  //     if (res.data.flag === false) {
  //       warningMsg.value = res.data?.err[0]; // 该事项下有子事项
  //     }
  //   } else {
  //     // 删除会议
  //     // TODO
  //   }
  // };

  const emits = defineEmits(['refreshList']);

  // 会议新增成功后刷新
  const refreshMeeting = () => {
    title.value = '';
    emits('refreshList');
  };
</script>

<script lang="ts">
  export default {
    name: 'Category',
  };
</script>

<style lang="less" scoped>
  .content {
    border-radius: 8px; /* 圆角 */
    background-color: #fff;
    overflow-y: auto;
    // padding-top: 20px;
    height: calc(100vh - 180px);
    position: relative;
    // border: 1px solid #999;

    .describe-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      border-bottom: 1px solid #ededed;
      border-width: calc(100% + 40px);
      :deep(.arco-input-wrapper) {
        padding-left: 20px;
      }
      // padding: 0 0 20px 0; /* 设置内边距：下16px */
      .describe-title {
        font-size: 18px;
        line-height: 24px;
        color: #1d2129;
        height: 24px;
        font-weight: 500;
      }
      .title {
        font-size: 20px;
        background-color: #fff;
        color: #1d2129;
        border: none !important;
        height: 55px;
        line-height: 55px;
      }
      :deep(.arco-input) {
        font-size: 20px !important;
        color: #1d2129 !important;
      }
    }

    .describe {
      padding: 20px 20px 0 20px;
      display: flex;
      align-items: center;
      .describe-icon {
        width: 20px;
        height: 20px;
      }
      .describe-span {
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
        color: #1d2129;
        margin-left: 8px;
      }

      .meeting-button {
        border-radius: 25px !important;
        height: 32px;
        margin-right: 5px;
        color: v-bind(meetingTextColor);
        background-color: v-bind(meetingBackColor);
      }
      .matter-button {
        border-radius: 25px !important;
        height: 32px;
        color: v-bind(matterTextColor);
        background-color: v-bind(matterBackColor);
      }
    }

    .arco-input-wrapper {
      padding: 0 20px 0 20px;
    }
  }
</style>
