/**
 * Listening to routes alone would waste rendering performance. Use the publish-subscribe model for distribution management
 * 单独监听路由会浪费渲染性能。使用发布订阅模式去进行分发管理。
 */
// 使用存在以下问题：多个组件增加监听时，组件销毁不能清除监听事件，导致内存泄漏
import mitt, { Handler } from 'mitt';
import { getAllTools } from '@/views/bim-view/components/toolbar/toolsConfig';

const emitter = mitt();

const key = Symbol('BIM_VIEW_TOOL_CHANGE');

const allTools = getAllTools();
let currentTool: any = '';
export function changeToolFn(toolIdStr: any) {
  const tool = allTools.find((e: any) => e.id === toolIdStr);
  console.log('ccccc: ', allTools, tool);
  if (tool) {
    currentTool = { ...tool, visible: true };
    emitter.emit(key, currentTool);
  }
}

export function listenerToolChange(
  handler: (toolObj: any) => void,
  immediate = true
) {
  emitter.on(key, handler as Handler);
  if (immediate && currentTool) {
    handler(currentTool);
  }
}

export function removeToolListener() {
  emitter.off(key);
}
