import { defineStore } from 'pinia';
import { scheduleParams } from './types';
import { getMeetingDetail } from '@/views/schedule/component/meeting/api';
import { agendaDetail } from '@/views/create-schedule/api';

const useScheduleStore = defineStore('schedule', {
  state: (): scheduleParams => {
    return {
      userData: [],
      summaryVisible: false, // 概要弹窗开关
      summaryData: {}, // 概要数据
      summaryLoading: false, // 概要loading
      scheduleId: '', // 我的日历id
      currentProjectScheduleId: '', // 项目日历id
      scheduleTab: '',
    };
  },
  actions: {
    setUserData(value: any) {
      this.userData = value;
    },
    // 设置概要弹窗开关
    setSummaryVisible(value: any) {
      this.summaryVisible = value;
      this.summaryLoading = true;
    },
    // 设置概要数据（通过id查询会议、事项详情数据）
    async setSummaryData(val: any) {
      if (val.type === 'meeting') {
        const { data } = await getMeetingDetail(val.id);
        this.summaryData = data;
      } else {
        const { data } = await agendaDetail(val.id);
        data.status = data.agendaStatus; // 为使用统一字段
        this.summaryData = data;
      }
      this.summaryLoading = false;
    },

    // 设置我的日历选择（用于新增会议事项携带参数）
    setScheduleId(value: any) {
      this.scheduleId = value || '';
    },
    // 设置项目的日历选择（用于新增会议事项携带参数）
    setCurrentProjectScheduleId(value: any) {
      this.currentProjectScheduleId = value || '';
    },
    // 设置日程当前tab
    setScheduleTab(value: any) {
      this.scheduleTab = value || '';
    },
  },
});

export default useScheduleStore;
