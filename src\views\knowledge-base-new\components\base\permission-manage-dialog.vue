<template>
  <a-modal
    :visible="visible"
    :width="400"
    :title="t('knowledgenew.permission-manage')"
    title-align="start"
    :mask-closable="false"
    :unmount-on-close="true"
    :ok-text="t('knowledgenew.ok-text')"
    :ok-loading="btnLoading"
    :esc-to-close="false"
    class="permission-management-dialog"
    @before-open="initData"
    @cancel="cancel"
    @ok="submitData"
  >
    <div>
      <a-space style="margin-bottom: 16px">
        <a-switch v-model="formData.view" @change="handleViewChange" />
        {{ t('knowledgenew.view-content') }}
      </a-space>
    </div>
    <div>
      <a-space style="margin-bottom: 16px">
        <a-switch v-model="formData.upload" />
        {{ t('knowledgenew.upload-file') }}
      </a-space>
    </div>
    <div>
      <a-space style="margin-bottom: 16px">
        <a-switch v-model="formData.share" />
        {{ t('knowledgenew.share-kb') }}
      </a-space>
    </div>
    <div>
      <a-space>
        <a-switch v-model="formData.edit" @change="handleEditChange" />
        {{ t('knowledgenew.edit-delete-file') }}
      </a-space>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { defineEmits, defineProps, ref, PropType } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { updateBase } from '../../api';
  import { KnowledgeBaseRecord, CreateOrUpdateBaseParams } from '../../types';
  import { Message } from '@arco-design/web-vue';

  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object as PropType<KnowledgeBaseRecord>,
      required: true,
    },
  });
  const emits = defineEmits(['update:visible', 'submit']);

  const btnLoading = ref(false);

  const formData = ref();

  // 表单初始化
  const initData = () => {
    formData.value = JSON.parse(
      JSON.stringify(props.data)
    ) as KnowledgeBaseRecord;
  };

  // 查看权限开关
  const handleViewChange = (value: boolean) => {
    if (!value) {
      formData.value.edit = false;
    }
  };

  // 编辑权限开关
  const handleEditChange = (value: boolean) => {
    if (value) {
      formData.value.view = true;
    }
  };

  // 提交数据
  const submitData = async () => {
    try {
      btnLoading.value = true;
      const params: CreateOrUpdateBaseParams = {
        id: formData.value.id,
        description: formData.value.description,
        name: formData.value.name,
        picUrl: formData.value.picUrl,
        recommendedQuestions: formData.value.recommendedQuestions,
        edit: formData.value.edit,
        share: formData.value.share,
        upload: formData.value.upload,
        view: formData.value.view,
      };
      const submitRes = await updateBase(params);
      if (submitRes.status) {
        Message.success(t('knowledgenew.edit-success'));
        emits('update:visible', false);
        emits('submit');
      }
      btnLoading.value = false;
    } catch (err) {
      btnLoading.value = false;
      console.error(err);
    }
  };

  const cancel = () => {
    emits('update:visible', false);
  };
</script>

<style scoped lang="less">
  .question-label {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    color: var(--color-text-2);
  }
  .add-btn {
    border: none;
    height: auto;
    padding: 0 4px;
  }

  .input-container {
    margin-bottom: 20px;
  }
  .delete-icon {
    margin-left: 10px;
    color: #4e5969;
    cursor: pointer;
  }

  .delete-icon:hover {
    background-color: #f2f3f5;
    border-radius: 2px;
  }
  .formdisable-delete-icon {
    cursor: not-allowed;
  }

  // 上传图片默认样式覆盖
  .arco-upload-picture-card {
    height: 88px;
    width: 88px;
    background: rgba(242, 243, 245, 0.8);
    border-radius: 8px;
    color: #86909c;
  }
  .arco-upload-list-picture {
    margin: 0;
    height: 88px;
    width: 88px;
    border-radius: 8px;
  }
  .arco-upload-list-picture-mask {
    line-height: 88px;
  }
</style>

<style lang="less">
  .permission-management-dialog {
    .arco-modal-header {
      height: 52px;
    }
    .arco-modal-title {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
      line-height: 28px;
    }
    .arco-modal-body {
      padding: 20px;
    }
  }
</style>
