<template>
  <div class="card-page">
    <PathAndOperations
      type="personal-card"
      @new-person-item-added="onNewItemAdded"
      @person-handle-upload="handleUpload(0)"
      @person-handle-download="handleDownload"
    />
    <div class="card-list">
      <!-- 文件/文件夹卡片 -->
      <div
        v-for="item in personalCombinedList"
        :key="item.id"
        class="file-card"
        :class="{ selected: item.selected }"
      >
        <div>
          <div
            class="file-card-header hover-show"
            :class="{ 'always-show': item.selected }"
          >
            <a-checkbox v-model="item.selected" class="folder-checkbox" />
            <FolderActionDropdown
              :item="item"
              @action="handleAction"
            ></FolderActionDropdown>
          </div>
          <div class="icon-name" @click="onSelect(item)">
            <file-image
              :file-name="item.name ?? ''"
              :is-sysFile="false"
              :is-file="!!item.folderId"
              class="folder-image"
            />
            <a-tooltip
              v-if="!item.isAdd && !item.isEdit"
              :content="item.name || undefined"
            >
              <div class="file-name">{{ item.name }}</div>
            </a-tooltip>
            <div v-else-if="item.isAdd" class="file-name">
              <a-input
                ref="addInputRef"
                v-model="item.name"
                style="width: 95%"
                @blur="addFolderRequest(item)"
                @keydown.enter="addFolderRequest(item)"
              />
            </div>
            <div v-else-if="item.isEdit && !item.folderId" class="file-name">
              <a-input
                ref="editInputRef"
                v-model="item.name"
                style="width: 95%"
                @blur="addFolderRequest(item)"
                @keydown.enter="addFolderRequest(item)"
              />
            </div>
            <div v-else-if="item.isEdit && item.folderId" class="file-name">
              <a-input
                ref="editInputRef"
                v-model="item.filename"
                style="width: 95%"
                @blur="addFolderRequest(item)"
                @keydown.enter="addFolderRequest(item)"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 上传文件卡片 -->
      <div class="file-card upload-card" @click="handleUpload(0)">
        <div class="file-icon">
          <uploadIcon class="has-pointer upload-icon" />
        </div>
        <div class="file-name">上传文件</div>
      </div>
    </div>
  </div>
  <UploadModal
    :visible="uploadModel.visible"
    :visible-type="uploadModel.type"
    :selected-folder="uploadModel.selectedFolder"
    @upload-single-success="singleFileSuccessCallback"
    @handle-cancel="uploadModel.visible = false"
    @upload-complete="finishUpLoad"
    @start-upload="startUpload"
  />
  <TransmitPanel
    v-model:visible="TransmitPanelVisible"
    :position="{
      top: 120,
      right: 60,
    }"
    :transmit-type="transmitType"
  />
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import { ref, computed, toRefs, onMounted, defineEmits, nextTick } from 'vue';
  import i18n from '@/locale/index';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import PathAndOperations from '@/views/knowledge-base-new/components/path-and-operations.vue';
  import uploadIcon from '@/assets/images/knowledge-base/upload2.svg';
  import UploadModal from '@/views/projectSpace/file/components/upload-modal.vue';
  import { addMergaFile } from '@/api/upload-file';
  import TransmitPanel from '@/views/projectSpace/file/components/transmit-panel/index.vue';
  import { finishTaskParams } from '@/store/modules/upload-file/types';
  import {
    addChildFolder,
    updateFolder,
    updateFile,
  } from '@/views/projectSpace/file/api';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import { useThrottleFn } from '@vueuse/core';
  import { Message } from '@arco-design/web-vue';
  import { FolderMessage } from '@/api/tree-folder';
  import modelViewBim from '@/utils/common/view';
  import { isWpsFile } from '@/views/projectSpace/file/utils';
  import { wpsViewHandle } from '@/hooks/wps';
  import { encode } from 'js-base64';
  import FolderActionDropdown from './folder-action-dropdown.vue';

  const { t } = i18n.global;
  const emit = defineEmits(['refreshFolder']);
  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { personal, personalCombinedList } = storeToRefs(knowledgeBaseNewStore);
  const { currentFolder, folderList, breadcrumb, projectId } = toRefs(
    personal.value
  );
  const popupVisible = ref(false);
  const uploadModel = ref({ visible: false, type: 0, selectedFolder: {} });
  const TransmitPanelVisible = ref(false);
  const transmitType = ref('upload');
  function handleUpload(visibleType: number) {
    uploadModel.value = {
      type: visibleType,
      visible: true,
      selectedFolder: currentFolder,
    };
  }
  async function singleFileSuccessCallback(params: finishTaskParams) {
    await addMergaFile(params)
      .catch((err) => {
        // this.changeFileArrStatus(item, 1);
      })
      .then((res: any) => {
        // 事项中成功需要给出结果
        // this.mattersSaveList.push(res.data);
        emit('refreshFolder');
        console.log('上传接口');
      })
      .finally(() => {});
  }
  // 上传完成通知列表刷新
  const finishUpLoad = () => {
    console.log('上传完成');
  };
  const startUpload = () => {
    transmitType.value = 'upload';
    TransmitPanelVisible.value = true;
    uploadModel.value.visible = false;
  };
  const handleDownload = () => {
    transmitType.value = 'download';
    TransmitPanelVisible.value = true;
  };
  // 点击进入下一文件夹
  const onSelect = (item: any) => {
    // 如果点击文件夹，进入下一文件夹，如果点击文件，则进入预览文件
    if (!item.folderId) {
      // 文件夹点击事件，进入下一层
      knowledgeBaseNewStore.setPersonCurrentFolder(item);
      knowledgeBaseNewStore.pushBreadcrumb(item);
      knowledgeBaseNewStore.getPersonalFolder('personal');
      knowledgeBaseNewStore.getfiles('personal');
      return;
    }
    const needParams = {};
    if (item.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: item.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      Object.assign(needParams, params);
    }

    modelViewBim(item, projectId as string, needParams);
  };
  const addInputRef = ref(null);
  const editInputRef = ref(null);
  // 添加文件夹
  const onNewItemAdded = () => {
    nextTick(() => {
      const inputs = addInputRef.value;
      console.log(inputs, 'inputs111');
      // 在 v-for 中 ref 会生成一个数组
      if (inputs && inputs.length) {
        inputs[inputs.length - 1].focus();
      }
    });
  };
  // 添加文件夹请求
  const addFolderRequest = useThrottleFn(async (item: any) => {
    const name = item.folderId ? item.filename : item.name;
    // 如果名称为空则认为撤销新增，删除列表中的该项
    if (name.trim() === '') {
      const index = folderList.value.findIndex((i: any) => i === item);
      if (index !== -1) {
        folderList.value.splice(index, 1);
      }
      // 可显示提示信息，例如：Message.info('撤销新增');
      return;
    }

    // 禁用字符校验，正则：不允许出现 \ / : * ? " < > |
    const pattern = /^[^\\/:*?"<>|]+$/;
    if (!pattern.test(name)) {
      Message.warning(t('file-manage.name-exclude-2'));
      return;
    }
    if (item.isAdd) {
      // 调用新增文件夹接口
      const res: any = await addChildFolder(item);
      if (res.code === 8000000) {
        Message.success(t('file-manage.success'));
        // 新增成功后取消 isAdd 标识
        item.isAdd = false;
      } else {
        Message.error(res.data);
      }
    }
    if (item.isEdit) {
      const param: any = {
        ...item,
        name: !item.folderId
          ? encode(item.name)
          : item.filename + item.filetype,
      };
      delete param.path;
      const res: any = item.folderId
        ? await updateFile(param)
        : await updateFolder(param);
      if (res.code === 8000000) {
        Message.success(t('file-manage.success'));
        item.isEdit = false;
      }
    }
    knowledgeBaseNewStore.getPersonalFolder('personal');
    knowledgeBaseNewStore.getfiles('personal');
  }, 1000);
  const handleRename = (item: any) => {
    // 重命名
    // 如果是新增状态，直接返回
    if (item.isAdd) {
      return;
    }
    item.isEdit = true;
    item.popupVisible = !item.popupVisible;
    nextTick(() => {
      const inputEdits = editInputRef.value;
      // 在 v-for 中 ref 会生成一个数组
      if (inputEdits && inputEdits.length) {
        inputEdits[inputEdits.length - 1].focus();
      }
    });

    // // 编辑文件
    // const needParams = {};
    // if (item.isCombination === 2) {
    //   const params = {
    //     type: 'collision',
    //     engine: 0,
    //     modelNumber: item.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
    //   };
    //   Object.assign(needParams, params);
    // }
    // modelViewBim(item, projectId as string, needParams);
  };
  // dropdown事件委托
  const handleAction = (record) => {
    switch (record.type) {
      case 'rename':
        if (record.item.folderId) {
          const idx = record.item.name.lastIndexOf('.');
          record.item.filename = record.item.name.slice(0, idx);
          record.item.filetype = record.item.name.slice(idx);
        }

        handleRename(record.item);
        break;
      case 'delete':
        knowledgeBaseNewStore.deleteItems([record.item], t);
        break;
      // case 'fileEdit':
      //   wpsViewHandle(record.item, 'edit', 'admin');
      //   break;
      case 'popupVisible':
        record.item.selected = record.value;
        break;
      case 'move':
        break;
      default:
    }
  };
  onMounted(() => {});
</script>

<style scoped lang="less">
  .card-page {
    position: absolute;
    height: calc(100% - 40px);
    width: 100%;
  }
  :deep(.has-pointer) {
    cursor: pointer;
  }
  .card-list {
    height: calc(100% - 64px);
    overflow: auto;
    display: flex;
    flex-wrap: wrap; // 允许换行
    gap: 20px;
    align-content: flex-start; /* 关键 */
    .file-card {
      width: 133px;
      height: 130px;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 0;
      &:hover {
        background: #e8f2ff;
        border: 1px solid #d9d9d9;
        .hover-show {
          visibility: visible;
          opacity: 1;
        }
      }
      .hover-show.always-show {
        visibility: visible;
        opacity: 1;
      }
      .hover-show {
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      .file-card-header {
        width: 133px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 8px 0 8px;
      }
      .folder-image {
        width: 60px;
        height: 60px;
      }
      .file-icon {
        width: 60px;
        height: 60px;
        margin-top: 8px;
        background: rgba(242, 243, 245, 0.8);
        border-radius: 8px 8px 8px 8px;
        border: 1px dashed #e5e6eb;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 13px;

        .upload-icon {
          width: 20px;
          height: 18px;
        }
      }
      .file-name {
        font-size: 14px;
        color: #1d2129;
        line-height: 22px;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏超出容器的内容 */
        text-overflow: ellipsis;
        max-width: 133px;
        width: 100%;
        display: block;
        word-break: keep-all;
        word-break: break-word;
        text-align: center;
      }
    }
    .icon-name {
      display: flex;
      flex-direction: column; /* 上下排列 */
      align-items: center; /* 水平居中 */
    }
    .icon-name > div {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    .selected {
      background: #e8f2ff;
      border: 1px solid #d9d9d9;
    }
  }
</style>
