<template>
  <div class="standard">
    <table-title
      class="table-title"
      :title="$t('standard-setting.standard-list')"
    ></table-title>
    <a-row class="standard-search">
      <a-col :span="20">
        <a-form
          :model="searchParams"
          label-align="left"
          layout="inline"
          auto-label-width
          class="search-area"
        >
          <a-form-item
            field="name"
            :label="$t('standard-setting.standard-name')"
            label-col-flex="auto"
            content-class="item"
          >
            <a-input
              v-model="searchParams.standardName"
              :placeholder="$t('standard-setting.please-enter')"
              allow-clear
              style="width: 180px"
              @press-enter="search"
              @search="search"
              @clear="search"
            />
          </a-form-item>
          <!-- <a-form-item
            field="name"
            :label="$t('standard-setting.standard-number')"
            label-col-flex="auto"
            content-class="item"
          >
            <a-input
              v-model="searchParams.standardCode"
              :placeholder="$t('standard-setting.please-enter')"
              allow-clear
              style="width: 180px"
              @press-enter="search"
              @search="search"
              @clear="search"
            />
          </a-form-item> -->
        </a-form>
      </a-col>
      <a-col :span="4" style="text-align: right">
        <a-space :size="8">
          <a-button type="outline" @click="search">
            <template #icon> <icon-search /> </template>
            {{ $t('list.options.btn.search') }}</a-button
          >
          <a-button type="outline" @click="reset"
            ><template #icon><icon-loop /> </template
            >{{ $t('list.options.btn.reset') }}</a-button
          >
        </a-space>
      </a-col>
    </a-row>
    <a-divider margin="10px 0 24px" />

    <a-row style="margin-bottom: 16px">
      <a-space style="float: right">
        <a-button type="primary" @click="createStandardDialog"
          ><template #icon><icon-plus /></template>
          {{ $t('list.options.btn.new') }}
        </a-button>
        <a-popconfirm
          :content="$t('standard-setting.whether-delete-checked-standard')"
          type="warning"
          position="left"
          @ok="batchDelete"
        >
          <a-button type="outline" :disabled="isBatchDetlete" status="danger"
            ><template #icon> <icon-delete /> </template
            >{{ $t('standard-setting.delete') }}</a-button
          >
        </a-popconfirm>
      </a-space>
    </a-row>
    <a-table
      v-table-height
      stripe
      row-key="id"
      :loading="loading"
      :pagination="pagination"
      :columns="(cloneColumns as TableColumnData[])"
      :data="renderData"
      :bordered="false"
      :scroll="scroll"
      :scrollbar="true"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      table-layout-fixed
      @page-change="onPageChange"
      @page-size-change="pageSizeChange"
      @selection-change="selectionChange"
    >
      <template #index="{ rowIndex }">
        {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
      </template>
      <!-- <template #code="{ record }">
        <router-link
          class="name"
          :to="{ name: 'standardDetail', query: { id: record.id } }"
          >{{ record.code }}</router-link
        >
      </template> -->
      <template #name="{ record }">
        <router-link
          v-if="record.standardType === 0"
          class="name"
          :to="{ name: 'standardDetail', query: { id: record.id } }"
          >{{ record.name }}</router-link
        >
        <span v-else>{{ record.name }}</span>
      </template>
      <template #standardType="{ record }">
        <a-tag v-if="record.standardType === 1" color="arcoblue">{{
          $t('standard-attribute.attribute-standard')
        }}</a-tag>
        <a-tag v-else color="purple">{{
          $t('standard-setting.naming-standard')
        }}</a-tag>
      </template>

      <template #operations="{ record }">
        <a-space>
          <a-button type="text" size="small" @click="handSetting(record)">
            {{ $t('table.opt.setting') }}
          </a-button>
          <a-button
            type="text"
            size="small"
            :disabled="admin !== 0 && record.groupId === '0'"
            @click="handEdit(record)"
          >
            {{ $t('table.opt.edit') }}
          </a-button>
          <a-popconfirm
            :content="$t('standard-setting.whether-delete-standard')"
            position="left"
            @ok="handleDelete(record)"
          >
            <a-button
              type="text"
              size="small"
              status="danger"
              :disabled="admin !== 0 && record.groupId === '0'"
            >
              {{ $t('table.opt.delete') }}
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>
    </a-table>
    <CreateStandard
      ref="createStandardRef"
      @refresh="updateData"
    ></CreateStandard>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive, watch } from 'vue';
  import useLoading from '@/hooks/loading';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import cloneDeep from 'lodash/cloneDeep';
  import {
    standardList,
    standardData,
    standardParams,
    deleteStandard,
    deleteStandadAttribute,
  } from './api';
  import CreateStandard from './components/create-standard.vue';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import TableTitle from '@/components/table-title/index.vue';
  import { useUserStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';
  import router from '@/router';

  const { t } = useI18n();
  // 配置
  type Column = TableColumnData & { checked?: true };
  const { loading, setLoading } = useLoading(true);
  const scroll = {
    y: 'calc(100vh - 360px)',
  };

  const userStore = useUserStore();
  const admin = computed(() => userStore.admin);
  const userId = getUserId() || '';
  const projectId = getLocalstorage(`last_project_${userId}`) || '';

  const renderData = ref<standardData[]>([]);
  const searchParams = reactive<any>({
    standardCode: '',
    standardName: '',
    groupId: userStore.admin === 0 ? '0' : projectId,
  });
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  const pagination = reactive({
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    total: 0,
  });

  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('standard-setting.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 80,
      align: 'left',
    },
    {
      title: t('standard-setting.standard-number'),
      dataIndex: 'code',
      slotName: 'code',
      align: 'left',
    },
    {
      title: t('standard-setting.standard-chinese-name'),
      dataIndex: 'name',
      slotName: 'name',
      align: 'left',
    },
    {
      title: t('standard-setting.standard-english-name'),
      dataIndex: 'englishName',
      slotName: 'englishName',
      align: 'left',
    },
    {
      title: t('standard-setting.standard-type'),
      dataIndex: 'standardType',
      align: 'center',
      slotName: 'standardType',
    },

    {
      title: t('standard-setting.description'),
      dataIndex: 'description',
      align: 'left',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('standard-setting.update-time'),
      dataIndex: 'updateDate',
      align: 'left',
    },
    {
      title: t('standard-setting.operation'),
      dataIndex: 'operations',
      slotName: 'operations',
      align: 'left',
      width: 150,
    },
  ]);

  // 列表数据
  const fetchData = async (
    params: standardParams = {
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      standardCode: '',
      standardName: '',
    }
  ) => {
    setLoading(true);
    try {
      const { data } = await standardList(params);
      console.log(data, 456);
      renderData.value = data.list;
      pagination.total = data.total;
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 禁用系统内标准选择框
   */
  const setTableRowStatus = () => {
    // 遍历 renderData.value 数组
    renderData.value.forEach((item) => {
      if (item.groupId === '0') {
        item.disabled = true;
      }
    });
  };

  // 查询
  const search = async () => {
    await fetchData({
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      ...searchParams,
    } as unknown as standardParams);
    if (admin.value !== 0) setTableRowStatus();
  };
  search();

  // 清空
  const reset = () => {
    searchParams.standardName = '';
    searchParams.standardCode = '';
    search();
  };

  const createStandardRef = ref<any>(null);
  // 新增
  const createStandardDialog = () => {
    createStandardRef.value.alertShow({}, 'add');
  };

  // 设置
  const handSetting = (record: any) => {
    if (record.standardType === 0) {
      router.push({
        name: 'standardDetail',
        query: { id: record.id },
      });
    } else {
      router.push({
        name: 'classCodeStandard',
        query: { id: record.id },
      });
    }
  };

  // 修改
  const handEdit = (data: any) => {
    createStandardRef.value.alertShow(data, 'edit');
  };

  // 删除
  const handleDelete = async (record: any) => {
    const res = await deleteStandard(record.id);
    if (res.status) {
      if (record.standardType === 1) {
        const params = {
          standard_id: record.xbaseStandardId,
          groupId: userStore.admin === 0 ? '0' : projectId,
        };
        deleteStandadAttribute(params);
      }
      Message.success(t('standard-setting.success'));
      search();
    }
  };

  const isBatchDetlete = ref(true);
  // 批量删除
  const batchDelete = async () => {
    const ids = selectionIds.value.toString();
    const res = await deleteStandard(ids);
    if (res.status) {
      search();
      Message.success(t('standard-setting.success'));
    }
  };

  const selectionIds = ref([]);
  // 获取已选择表格行数据
  const selectionChange = (keys: any) => {
    selectionIds.value = [];
    const data = renderData.value.filter((item: any) => {
      if (keys.includes(item.id)) selectionIds.value.push(item.id);
      return keys.includes(item.id);
    });
    if (selectionIds.value.length > 0) isBatchDetlete.value = false;
    else isBatchDetlete.value = true;
  };

  // 翻页
  const onPageChange = (pageNo: number) => {
    pagination.current = pageNo;
    fetchData({
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      ...searchParams,
    });
  };
  // 修改条数
  const pageSizeChange = (pageSize: number): void => {
    pagination.pageSize = pageSize;
    fetchData({
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      ...searchParams,
    });
  };

  // 修改或新增后刷新
  const updateData = () => {
    search();
  };

  const tableHeight = ref(0);
  // table根据父组件计算空白高度
  const vTableHeight = {
    mounted(el: Element) {
      tableHeight.value = Math.max(
        (el.parentElement?.offsetHeight || 0) - 148,
        0
      );
    },
  };

  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );
</script>

<script lang="ts">
  export default {
    name: 'Standard',
  };
</script>

<style scoped lang="less">
  .standard {
    .card-title {
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
      img {
        position: relative;
        top: 3px;
        height: 20px;
      }
    }

    .search-title {
      height: 22px;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #1d2129;
      line-height: 22px;
    }
  }

  :deep(.arco-card-header) {
    height: auto;
    padding: 20px 20px 0px 20px;
    border: none;
    margin-top: 20px;
  }

  :deep(.arco-card-bordered) {
    border: none;
  }

  :deep(.arco-scrollbar-thumb-bar) {
    width: 0;
  }

  .name {
    text-decoration: none;
    color: #1d2129;
  }
  .table-title {
    margin-bottom: 16px;
  }
  :deep(.arco-table-td .arco-btn) {
    padding: 0;
  }
</style>
