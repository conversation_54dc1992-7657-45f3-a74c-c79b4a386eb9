<template>
  <div class="container">
    <a-row :gutter="16" style="margin-bottom: 16px">
      <a-col :flex="1">
        <table-title :title="$t('project-setting.team-list')"></table-title>
      </a-col>
    </a-row>

    <!-- <list-title
      :title-text="$t('project-setting.team-list')"
      :btn-text="$t('project-setting.add-team')"
      :show-button="false"
      @add="handleAdd" -->
    <!-- /> -->
    <!-- 搜索区 -->
    <a-form :model="searchForm">
      <a-row>
        <a-col :span="20">
          <div>
            <span>{{ $t('project-setting.name') }}</span>
            <a-input
              v-model="searchForm.name"
              :style="{ width: '196px', marginLeft: '16px' }"
              :placeholder="$t('project-setting.please-enter')"
              allow-clear
              @search="handleSearch"
              @press-enter="handleSearch"
              @clear="handleSearch"
            />
          </div>
        </a-col>
        <a-col :span="4" style="text-align: right">
          <a-space :size="8">
            <a-button type="outline" @click="handleSearch">
              <template #icon> <icon-search /> </template
              >{{ $t('list.options.btn.search') }}</a-button
            >
            <a-button type="outline" @click="reset">
              <template #icon><icon-loop /> </template
              >{{ $t('list.options.btn.reset') }}</a-button
            >
          </a-space>
        </a-col>
      </a-row>
    </a-form>
    <a-divider margin="20px 0 20px" />

    <div class="table-container">
      <a-row style="margin-bottom: 16px">
        <a-button
          v-permission="$btn.team.addTeam"
          type="primary"
          @click="handleAdd"
          >{{ $t('project-setting.add-team') }}</a-button
        >
      </a-row>
      <a-table
        v-model:expanded-keys="expandedKeys"
        :loading="loading"
        :columns="teamColumns"
        :data="teamList"
        row-key="id"
        :scroll="scroll"
        :bordered="false"
        :pagination="pagination"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #expand-icon="{ expanded }">
          <!-- 或者使用不同的图标元素 -->
          <icon-down v-if="!expanded" />
          <icon-up v-else />
        </template>
        <template #index="{ record }">
          <span>{{ record.fullOrder }}</span>
        </template>
        <template #name="{ record }">
          <div class="name-column">
            <!-- 团队颜色 -->
            <a-trigger
              :popup-visible="record.isShowColor"
              trigger="click"
              :unmount-on-close="false"
              :popup-translate="[94, -8]"
              update-at-scroll
              @popup-visible-change="handleColorVisibleChange($event, record)"
            >
              <div class="icon-container">
                <span
                  class="normal-icon"
                  :class="{
                    'active-icon': record.isShowColor,
                  }"
                  :style="{ backgroundColor: record.color }"
                ></span>
              </div>
              <template #content>
                <color-select
                  :init-color="record.color"
                  @color-changed="handleColorChanged($event, record)"
                />
              </template>
            </a-trigger>

            <!-- 团队名称 -->
            <a-tooltip
              :popup-visible="record.isShowTooltip"
              :content="i18TeamName(record)"
            >
              <span
                class="name-text"
                @mouseenter="handleShowTooltip($event, record)"
                @mouseleave="handleHideTooltip(record)"
                @click="handleOperation(record, 'view')"
                >{{ i18TeamName(record) }}
              </span>
            </a-tooltip>
          </div>
        </template>

        <template #locationChange="{ record }">
          <a-tooltip
            v-if="!record.parentId"
            :content="$t('project-setting.move-up')"
            background-color="#3491FA"
          >
            <a-button
              :disabled="record.fullOrder == 1"
              type="text"
              size="small"
              @click="changeLocation(record, 'up')"
            >
              <img
                class="icon"
                width="23"
                src="@/assets/images/project-setting/to-up.png"
              />
            </a-button>
          </a-tooltip>
          <a-tooltip
            v-if="!record.parentId"
            :content="$t('project-setting.move-down')"
            background-color="#3491FA"
          >
            <a-button
              :disabled="record.fullOrder == teamListLength"
              type="text"
              size="small"
              @click="changeLocation(record, 'down')"
            >
              <img
                class="icon"
                width="23"
                src="@/assets/images/project-setting/to-down.png"
              />
            </a-button>
          </a-tooltip>
          <a-tooltip
            v-if="!record.parentId"
            :content="$t('project-setting.top')"
            background-color="#3491FA"
          >
            <a-button
              :disabled="record.fullOrder == 1"
              type="text"
              size="small"
              @click="changeLocation(record, 'top')"
            >
              <img
                class="icon"
                width="20"
                src="@/assets/images/project-setting/to-top.png"
              />
            </a-button>
          </a-tooltip>
          <a-tooltip
            v-if="!record.parentId"
            :content="$t('project-setting.bottom')"
            background-color="#3491FA"
          >
            <a-button
              :disabled="record.fullOrder == teamListLength"
              type="text"
              size="small"
              @click="changeLocation(record, 'bottom')"
            >
              <img
                class="icon"
                width="20"
                src="@/assets/images/project-setting/to-bottom.png"
              />
            </a-button>
          </a-tooltip>
        </template>
        <template #count="{ record }">
          <span>{{ record.count }}</span>
        </template>
        <template #teamUsers="{ record }">
          <span>{{ record.adminName }}</span>
        </template>
        <template #operations="{ record }">
          <a-button
            v-if="record.parentId"
            type="text"
            size="small"
            v-permission="`${record.id}_${$btn.team.editTeam}`"
            @click="handleOperation(record, 'edit', '2')"
            >{{ $t('project-setting.edit') }}
          </a-button>
          <a-button
            v-else-if="!record.parentId"
            v-permission="`${record.id}_${$btn.team.editTeam}`"
            type="text"
            size="small"
            @click="handleOperation(record, 'edit', '1')"
            >{{ $t('project-setting.edit') }}
          </a-button>
          <!-- 安泰得功能，已废弃 -->
          <!-- <a-button
            type="text"
            v-if="
              record.parentId &&
              (storeAdmin.admin === 1 ||
                storeAdmin.admin === 0 ||
                ownTeamList.includes(record.parentId))
            "
            size="small"
            @click="handleSetting(record)"
            >{{ $t('project-setting.setting') }}
          </a-button> -->
          <!-- <a-button
            type="text"
            v-else-if="
              !record.parentId &&
              (storeAdmin.admin === 1 ||
                storeAdmin.admin === 0 ||
                ownTeamList.includes(record.id))
            "
            size="small"
            @click="handleSetting(record)"
            >{{ $t('project-setting.setting') }}
          </a-button> -->
          <!-- //新增二级团队 -->
          <a-button
            v-if="!record.parentId"
            v-permission="`${record.id}_${$btn.team.addSubTeam}`"
            type="text"
            size="small"
            @click="addSubordinateTeam(record.id)"
            >{{ $t('project-setting.add-subordinate-team') }}
          </a-button>

          <a-button
            v-if="!record.parentId"
            v-permission="`${record.id}_${$btn.team.deleteTeam1}`"
            type="text"
            size="small"
            @click="removeTeam(record.id, record.name, record.teams)"
            >{{ $t('table.opt.delete') }}</a-button
          >
          <a-button
            v-else
            v-permission="`${record.id}_${$btn.team.deleteTeam2}`"
            type="text"
            size="small"
            @click="removeTeam(record.id, record.name, record.teams)"
            >{{ $t('table.opt.delete') }}</a-button
          >
        </template>
      </a-table>
    </div>

    <add-team
      v-model:visible="addDialogVisible"
      :form="addTeamInfo"
      :click-event="clickEvent"
      @refresh="handleSearch"
    />
    <team-info-dialog
      v-model:visible="editDialogVisible"
      :origin-data="editTeamInfo"
      :handle-type="handleType"
      @refresh="editRefresh"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, reactive, ref } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useRoute } from 'vue-router';
  import useLoading from '@/hooks/loading';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import { Message } from '@arco-design/web-vue';
  import useUserStore from '@/store/modules/user/index';
  import { usePrjPermissionStore } from '@/store/index';
  import {
    TeamRecord,
    queryTeamList,
    updateTeam,
    upTeam,
    downTeam,
    topTeam,
    bottomTeam,
    getTeamFolder,
    getFileList,
    removeTeams,
  } from './api';
  import ListTitle from '@/components/list-title/index.vue';
  import AddTeam from './components/add-team.vue';
  import colorSelect from './components/color-select.vue';
  import TeamInfoDialog from './components/edit-team.vue';
  import Modal from '@arco-design/web-vue/es/modal';
  import colors from './json/colors.json';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import TableTitle from '@/components/table-title/index.vue';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';
  import usePermissionStore from '@/store/modules/permission';

  const { t } = useI18n();
  // const store = useFileManageStore();
  const storeAdmin = useUserStore();
  const { admin } = storeToRefs(storeAdmin);

  const { currentLocale } = useLocale(); // 国际化类型

  const scroll = {
    y: 'calc(100vh - 380px)',
  };

  const clickEvent = ref(true);
  const teamFlag = ref(false);
  const folderId = ref('');
  interface TeamList {
    color: string;
    count: number;
    createBy: string;
    createDate: string;
    deleteFlag: number;
    id: string;
    isShowColor: false;
    isShowTooltip: false;
    name: string;
    order: number;
    professionalDetailType: null;
    professionalType: null;
    projectId: number;
    role: number;
    updateBy: string;
    updateDate: string;
    teamUsers: string;
  }
  // const handleSetting = (record: TeamList) => {
  //   store.teamData = record;
  //   store.settingTeamVisible = true;
  // };

  const route = useRoute();
  const projectId = computed(() => {
    return String(route?.params?.projectId);
  });

  const projectStore = usePrjPermissionStore();
  const isPrjAdmin = projectStore.projectAdmin;
  // 获取到具有权限的团队id数据
  const ownTeamList = computed(() => {
    const teamLists: string[] = [];
    projectStore.teamList.forEach((item: any) => {
      if (item.role === 5) {
        teamLists.push(item.id);
      }
    });
    return teamLists;
  });

  const addDialogVisible = ref(false);
  const emptyTeam: TeamRecord = {
    projectId: projectId.value,
    name: '',
    color: '',
    role: 1,
    count: 0,
    teamUsers: [],
    userIds: '',
    isInherit: 1,
  };
  const addTeamInfo = ref<TeamRecord>({ ...emptyTeam });

  // 查看/编辑
  const editDialogVisible = ref(false);
  const handleType = ref('view');
  const editTeamInfo = ref<TeamRecord>({ ...emptyTeam });

  // 搜索栏
  const searchForm = reactive<any>({
    name: '',
  });

  // 列表
  const { loading, setLoading } = useLoading(true);
  const teamColumns = computed<TableColumnData[]>(() => [
    {
      title: t('project-setting.index'),
      dataIndex: 'index',
      slotName: 'index',
      align: 'left',
      width: 100,
    },
    {
      title: t('project-setting.team-name'),
      dataIndex: 'name',
      slotName: 'name',
      ellipsis: true,
      align: 'left',
      bodyCellClass: 'link-text',
    },
    {
      title: t('project-setting.sequence-adjustment'),
      slotName: 'locationChange',
      align: 'left',
      width: 270,
    },
    {
      title: t('project-setting.member-count'),
      dataIndex: 'count',
      slotName: 'count',
      align: 'left',
      // bodyCellClass: 'link-text',
      width: 240,
    },
    // 团队管理员
    {
      title: t('project-setting.team-admins'),
      slotName: 'teamUsers',
      align: 'left',
      width: 300,
    },
    {
      title: t('project-setting.operations'),
      slotName: 'operations',
      align: 'left',
      width: 250,
    },
  ]);
  // const columnsEdit = computed<TableColumnData[]>(() => [
  //   ...teamColumns.value,
  //   {
  //     title: t('project-setting.operations'),
  //     slotName: 'operations',
  //     align: 'center',
  //     width: 250,
  //   },
  // ]);

  const pagination = reactive({
    showTotal: true,
    showPageSize: true,
    showJumper: true,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    current: 1,
    total: 0,
  });

  // 团队名称超长时显示tooltip
  let timer: any = null;
  const handleShowTooltip = (event: any, record: TeamRecord) => {
    if (event.target?.scrollWidth > event.target?.clientWidth) {
      timer = setTimeout(() => {
        record.isShowTooltip = true;
      }, 200);
    }
  };

  const handleHideTooltip = (record: TeamRecord) => {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    record.isShowTooltip = false;
  };
  const expandedKeys = ref([]);
  const teamList = ref();
  const teamListLength = ref(0);

  const { i18TeamName } = useI18nHandleName();

  // 查询列表数据
  const getTeamList = () => {
    setLoading(true);
    console.log('projectId111111111111111111111111', projectId.value);
    const params: any = {
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      projectId:
        !projectId.value || projectId.value === 'undefined'
          ? '1721357192716677122'
          : projectId.value,
    };
    Object.keys(searchForm).forEach((key) => {
      if (searchForm[key]) {
        params[key] = searchForm[key];
      }
    });
    queryTeamList(params)
      .then((res) => {
        teamList.value = res.data.list || [];
        teamListLength.value = res.data.list.length || 0;
        pagination.total = res.data.total;
        teamList.value.forEach((team: TeamRecord) => {
          team.isShowColor = false;
          team.isShowTooltip = false;
        });
        for (let i = 0; i < teamList.value?.length; i++) {
          if (teamList.value[i].teams.length !== 0) {
            teamList.value[i].children = teamList.value[i].teams;
          }
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const removeTeam = async (
    teamId: string,
    teamName: string,
    teams: string[]
  ) => {
    console.log('删除团队，获取团队名称', teamName);
    teamFlag.value = false; // false允许删除，true不允许删除
    const res = await getTeamFolder(teamId);
    folderId.value = res.data[1].parentId;

    const result: any = await getFileList(folderId.value);
    if (result.data.list.length !== 0) {
      teamFlag.value = true;
    }
    if (teamFlag.value) {
      Message.warning(t('prjMember.table.opt.removeTeams.tips'));
      return;
    }
    if (teams.length !== 0) {
      Modal.warning({
        title: t('prjMember.table.opt.removeTeams'),
        content: t('prjMember.table.opt.removeTeams.warning'),
        closable: true,
        hideCancel: true,
        onOk: async () => {},
      });
    } else {
      Modal.warning({
        title: t('prjMember.table.opt.removeTeams'),
        content: t('prjMember.table.opt.removeTeams.confirm', { teamName }),
        closable: true,
        hideCancel: false,
        onOk: async () => {
          await removeTeams(teamId);
          Message.success(t('prjMember.table.opt.removeTeams.success'));
          pagination.current = 1;
          getTeamList();
          projectStore.setPermission(projectId.value);
        },
      });
    }
  };

  const permissionStore = usePermissionStore();
  const handleSearch = async () => {
    await permissionStore.setProjectPremission(projectId.value);
    pagination.current = 1;
    getTeamList();
  };

  const editRefresh = () => {
    // 编辑之后先更新团队权限信息
    projectStore.setPermission(projectId.value);
    handleSearch();
  };

  // 清空
  const reset = () => {
    searchForm.name = '';
    handleSearch();
  };

  // 新增按钮点击事件
  const handleAdd = () => {
    clickEvent.value = true;
    // 设置新增的团队颜色
    const colorIndex = Math.floor(Math.random() * colors.length);
    addTeamInfo.value = { ...emptyTeam, color: colors[colorIndex] };
    addDialogVisible.value = true;
  };
  // 新增二级团队
  const addSubordinateTeam = (parentId: string) => {
    clickEvent.value = false;
    const colorIndex = Math.floor(Math.random() * colors.length);
    addTeamInfo.value = { ...emptyTeam, parentId, color: colors[colorIndex] };
    addDialogVisible.value = true;
  };

  // 团队顺序设置
  const changeLocation = async (record: any, operation: string) => {
    const formData = new FormData();
    formData.append('id', record?.id);
    let res: any = {};
    switch (operation) {
      case 'up':
        res = await upTeam(formData);

        break;
      case 'down':
        res = await downTeam(formData);

        break;
      case 'top':
        res = await topTeam(formData);

        break;
      case 'bottom':
        res = await bottomTeam(formData);
        break;
      default:
        break;
    }
    if (res.status) {
      Message.success(res.message);
      getTeamList();
    }
  };
  // 查看/编辑按钮点击事件
  const handleOperation = (
    record: TeamRecord,
    operation: string,
    level: string
  ) => {
    handleType.value = operation;
    editTeamInfo.value = { ...record, level };
    editDialogVisible.value = true;
  };

  // 选择颜色面板显示隐藏处理
  const handleColorVisibleChange = (visible: boolean, record: TeamRecord) => {
    record.isShowColor = visible;
  };

  // 调用接口修改团队颜色
  const handleColorChanged = async (nValue: string, record: TeamRecord) => {
    const params = {
      ...record,
      color: nValue,
    };
    delete params.isShowColor;
    delete params.isShowTooltip;
    const res = await updateTeam(params);
    if (res.status) {
      Message.success(t('edit-successful'));
      record.isShowColor = false;
      record.color = nValue;
      projectStore.setPermission(projectId.value);
    }
  };

  // 页码改变事件处理
  const handlePageChange = (page: number) => {
    pagination.current = page;
    getTeamList();
  };

  // 每页条数改变事件处理
  const handlePageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    getTeamList();
  };

  getTeamList();
</script>

<style scoped lang="less">
  .container {
    height: calc(100vh - 156px);
    padding: 20px !important;
  }
  .search-area {
    padding: 8px 0 4px 0;

    :deep(.item) {
      width: 220px;
    }
  }

  .table-container {
    height: calc(100% - 129px);
  }

  .name-column {
    display: flex;
    align-items: center;

    .icon-container {
      display: flex;
      justify-content: center;
      width: 26px;

      .normal-icon {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
      }

      .active-icon {
        box-shadow: 0px 2px 5px 0px #e5e6eb;
        border: 2px solid #ffffff;
        width: 24px;
        height: 24px;
      }
    }

    .name-text {
      width: 100%;
      margin-left: 10px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }

  .link-text {
    span {
      color: #3366ff;
    }
  }

  :deep(.arco-btn-size-small) {
    padding: 0 7px;
  }
  :deep(.arco-table-expand-btn) {
    background: #ffffff;
  }

  :deep(.arco-table-container) {
    height: calc(100% - 32px);
  }
</style>
