<template>
  <div class="userCenter">
    <a-row style="margin-bottom: 20px">
      <a-col :flex="1">
        <table-title :title="$t('user-center.user-list')"></table-title>
      </a-col>
    </a-row>

    <div
      class="arco-row arco-row-align-start arco-row-justify-start"
      style="margin-bottom: 20px"
    >
      <div class="arco-col" style="flex: 1 1 0%">
        <a-row>
          <a-col :span="14">
            <!-- <a-form-item
              field="name"
              :label="$t('user-center.search.name')"
              label-col-flex="10px"
            > -->
            <!-- <a-form-item> -->
            <a-input
              v-model="searchValueParam"
              :placeholder="$t('please-enter-name-email-phone-to-search')"
              @keyup.enter="search"
              allow-clear
              @clear="reset"
            />
            <!-- </a-form-item> -->
          </a-col>
          <!-- <a-col :span="8">
            <a-form-item
              field="phone"
              :label="$t('user-center.search.phone')"
              label-col-flex="45px"
            >
              <a-input
                v-model="formModel.phone"
                :placeholder="$t('please-enter')"
                @keyup.enter="search"
              />
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="8">
            <a-form-item
              field="email"
              :label="$t('user-center.search.email')"
              label-col-flex="30px"
            >
              <a-input
                v-model="formModel.email"
                :placeholder="$t('please-enter')"
                @keyup.enter="search"
              />
            </a-form-item>
          </a-col> -->
        </a-row>
        <a-row>
          <!-- <a-col :span="8">
            <a-form-item
              field="emailsecondaryUnit"
              :label="$t('user-center.search.secondary-unit')"
              label-col-flex="60px"
              :disabled="String(userInfo?.admin) !== '0'"
            >
              <a-select
                v-model="formModel.secondaryUnit"
                allow-clear
                :placeholder="$t('please-select')"
                @change="secondaryUnitChange"
              >
                <a-option
                  v-for="item of secondaryUnitOptions"
                  :key="item.id"
                  :value="item.id"
                  :label="item.content?.name"
                />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="subsidiary"
              :label="$t('user-center.search.subsidiary')"
              label-col-flex="100px"
            >
              <a-select
                v-model="formModel.subsidiary"
                allow-clear
                :placeholder="$t('please-select')"
              >
                <a-option
                  v-for="item in subsidiaryOptions"
                  :key="item.id"
                  :value="item.id"
                  :label="item.content?.name"
                />
              </a-select>
            </a-form-item>
          </a-col> -->
        </a-row>
      </div>
      <div
        data-v-04aea224=""
        role="separator"
        class="arco-divider arco-divider-vertical"
        style="height: 32px"
        ><!----></div
      >
      <div
        data-v-04aea224=""
        class="arco-col"
        style="flex: 0 0 86px; text-align: right"
      >
        <a-space
          ><a-button type="outline" @click="search">
            <template #icon> <icon-search /> </template
            >{{ $t('list.options.btn.search') }}</a-button
          >
          <a-button type="outline" @click="reset"
            ><template #icon><icon-loop /> </template
            >{{ $t('list.options.btn.reset') }}</a-button
          >
        </a-space>
      </div>
    </div>

    <a-divider :margin="20"/>
    <!-- todo: 用户-邀请按钮 -->
    <a-row style="margin-bottom: 16px">
      <a-button type="primary" @click="showInviteUserModal" class="invite-btn" style="padding: 0 15px !important;">{{
        $t('invite-user.title')
      }}</a-button>
    </a-row>
    <!-- 新增、导入、导出 -->
    <!-- <a-row style="margin-bottom: 16px">
      <a-col>
        <a-space :size="8">
          <a-button type="primary" @click="addUser">{{
            $t('user-center.add-users')
          }}</a-button>
          <a-button type="outline" @click="exportUsers">
            {{ $t('user-center.export-users') }}
          </a-button>
          <a-button type="outline" @click="importUsers"
            >{{ $t('user-center.import-users') }}
          </a-button>
        </a-space>
      </a-col>
    </a-row> -->

    <div class="table-box">
      <a-table
        stripe
        row-key="id"
        :loading="loading"
        :scroll="scroll"
        :pagination="pagination"
        :columns="columns"
        :data="renderData"
        :bordered="false"
        table-layout-fixed
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #unitAdmin="{ record }">
          <a-switch
            v-model="record.unitManagement"
            type="round"
            checked-color="#3366FF"
            unchecked-color="#C9CDD4"
            :checked-value="'是'"
            :unchecked-value="'不是'"
            :checked-text="$t('table.yes')"
            :unchecked-text="$t('table.no')"
            :disabled="record.admin === 0 || userId === record.id"
            @change="unitAdminChange(record)"
          ></a-switch>
        </template>
        <template #accountState="{ record }">
          <!-- 1激活状态 2休眠状态 3注销账号 -->
          <a-tag v-if="record.accountStatus == '1'" color="blue">{{
            $t('user-center.table.status.normal')
          }}</a-tag>
          <!-- <a-tag
            v-else-if="record.accountStatus === '4'"
            color="orange"
            >{{ $t('user-center.table.status.unaudited') }}</a-tag
          > -->
          <a-tag
            v-else-if="record.accountStatus == '3'"
            color="purple"
            >{{ $t('user-center.table.status.cancel') }}</a-tag
          >
          <a-tag v-else  color="magenta">{{
            $t('user-center.table.status.alreadyAsleep')
          }}</a-tag>
        </template>
        <!-- 编辑、恢复、查看、注销、邀请链接 -->
        <template #operation="{ record }">
          <!-- <a-button
            v-if="
              record.accountStateCode === '1' || record.accountStateCode === '2'
            "
            type="text"
            size="small"
            @click="updateUser(record.id)"
          >
            {{ $t('table.opt.edit') }}</a-button
          >
          <a-button
            v-if="record.accountStateCode === '3'"
            type="text"
            size="small"
            @click="recoverUser(record.id)"
          >
            {{ $t('user-center.table.opt.recover') }}</a-button
          >
          <a-button
            v-if="record.accountStateCode === '1'"
            type="text"
            size="small"
            @click="viewUser(record.id)"
            >{{ $t('user-center.table.view') }}</a-button
          >
          <a-button
            v-if="record.accountStateCode === '1'"
            type="text"
            size="small"
            @click="removeUser(record.id)"
            >{{ $t('user-center.table.opt.cancel1') }}</a-button
          >
          <a-button
            v-if="record.accountStateCode === '2'"
            type="text"
            size="small"
            @click="produceLink(record)"
            >{{ $t('user-center.copy-link') }}</a-button
          >
          <span v-if="record.accountStateCode === '4'">-</span> -->
          <a-button
            type="text"
            class="change-btn"
            @click="showInviteUserModal(record)"
          >
            {{ $t('table.opt.edit') }}
          </a-button>
          <a-button
            type="text"
            status="danger"
            class="remove-btn"
            @click="showRemoveUserConfirm(record)"
          >
            <!-- <template #icon><icon-export /></template> -->
            {{ $t('user-center.table.opt.remove-user') }}
          </a-button>
        </template>
      </a-table>
    </div>
    <AddUsers
      v-if="addDialogVisible"
      v-model:visible="addDialogVisible"
      :title="dialogTitle"
      :select-id="selectId"
      :unit="formModel.secondaryUnit"
      :current-title="currentTitle"
      @refresh="refreshData"
    />
    <inviteUser
      v-model:visible="inviteUserModalVisible"
      @inviteSuccess="refreshData"
      :editing-user-name="editingUserName"
    />
    <!-- <UploadUsers
      v-if="importDialogVisible"
      v-model="importDialogVisible"
      :unit-name="unitName"
      @refresh="refreshData"
    /> -->
    <!-- 移出用户确认弹窗 -->
    <a-modal
      v-model:visible="removeUserModalVisible"
      :title="$t('user-center.table.opt.remove-user-confirm')"
      @ok="handleRemoveUserConfirm"
      @cancel="handleRemoveUserCancel"
    >
      <p>{{
        $t('user-center.table.opt.remove-user-message', {
          name: removeUserFullName,
        })
      }}</p>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, onMounted, reactive } from 'vue';
  import AddUsers from './components/add-users.vue';
  import inviteUser from './components/invite-user.vue';
  import UploadUsers from './components/upload-users.vue';
  import TableTitle from '@/components/table-title/index.vue';
  import { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import { Message, Tree } from '@arco-design/web-vue';
  import Modal from '@arco-design/web-vue/es/modal';
  import useClipboard from 'vue-clipboard3';
  import { useI18n } from 'vue-i18n';
  import createUrl from '@/utils/file-blob';
  import { useUserStore } from '@/store';
  import {
    MemberRecord,
    SysUserMemberRecord,
    searchSysUserParams,
    queryUnitTree,
    unitTreeData,
    updateUserDetail,
    exportUser,
    querryUserInfo,
    userInfoType,
    getSysUserList,
    searchData,
    companyAndOrgRemoveUserApi,
  } from './api';
  import useLocale from '@/hooks/locale';
  import commonTabs from '@/components/common-tabs/index.vue';

  const tabKey = ref('user');

  const scroll = {
    y: 'calc(100% -380px)',
  };

  const { currentLocale } = useLocale();
  const { t } = useI18n();
  const userStore = useUserStore();
  // 登录用户id
  const userId = computed(() => {
    return userStore.id;
  });
  // 企业id
  const storeCompanyId = computed(() => {
    return userStore.companyId;
  });

  // 导入参数的二级单位名
  const unitName = computed(() => {
    return formModel.value.secondaryUnit
      ? secondaryUnitOptions.value?.find(
          (item) => item.id === formModel.value.secondaryUnit
        )?.content?.name
      : '';
  });
  // 用户权限信息
  const userInfo = ref<userInfoType>({});
  // 搜索框form对象
  const formModel = ref<MemberRecord>({});
  //
  const searchValueParam = ref('');
  // 二级单位下拉框列表数据
  const secondaryUnitOptions = ref<unitTreeData[]>();
  // 子公司下拉框数据
  const subsidiaryOptions = ref<unitTreeData[]>();
  // 展开dialog的标题
  const dialogTitle = ref('');
  //  当前弹框标识符
  const currentTitle = ref('');
  // 获取表格数据loading状态
  const loading = ref(false);
  // 导入控制展开
  const importDialogVisible = ref(false);
  // 新增控制展开
  const addDialogVisible = ref(false);

  const pagination = reactive({
    showJumper: true,
    showTotal: true,
    showPageSize: true,
    current: 1,
    pageSize: 20,
    total: 0,
    pageSizeOptions: [20, 50, 100],
    pageSizeChangeResetCurrent: true,
  });
  // 编辑带入的数据
  const selectId = ref('');
  // table的data
  const renderData = ref<SysUserMemberRecord[]>([]);
  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('user-center.index'),
      slotName: 'index',
      width: 80,
    },
    {
      title: t('user-center.search.name'),
      dataIndex: 'userFullname',
      width: 120,
      align: 'left',
    },
    {
      title: t('user-center.account'),
      dataIndex: 'userName',
      align: 'left',
    },
    {
      title: t('user-center.search.phone'),
      dataIndex: 'phone',
      align: 'left',
    },
    {
      title: t('user-center.search.email'),
      dataIndex: 'email',
      align: 'left',
    },
    // {
    //   title: t('user-center.search.secondary-unit'),
    //   dataIndex: 'secondLevel',
    //   align: 'left',
    // },
    // {
    //   title: t('user-center.search.subsidiary'),
    //   dataIndex: 'subsidiaryCorporation',
    //   align: 'left',
    // },
    // {
    //   title: t('user-center.job'),
    //   dataIndex: 'post',
    //   align: 'left',
    // },
    // {
    //   title: t('user-center.manage'),
    //   align: 'left',
    //   width: 160,
    //   slotName: 'unitAdmin',
    // },
    {
      title: t('user-center.account-status'),
      align: 'left',
      width: 120,
      slotName: 'accountState',
    },
    {
      title: t('user-center.operate'),
      // accountStatus: 1是激活状态，2是休眠状态，3是注销账号
      align: 'center',
      width: 160,
      slotName: 'operation',
    },
  ]);
  // 获取用户权限
  const getUserInfo = async () => {
    const { data } = await querryUserInfo();
    userInfo.value = data;
    // 非超管，如果二级单位已被删除，构造一个回显数据
    if (
      data.admin &&
      data.secondLevelCode &&
      !secondaryUnitOptions.value?.find(
        (item) => item.id === String(data.secondLevelCode)
      )
    ) {
      secondaryUnitOptions.value = [
        {
          content: { name: data.secondLevel || '' },
          id: String(data.secondLevelCode) || '',
        },
      ];
    }
    formModel.value.secondaryUnit =
      (data.secondLevelCode
        ? String(data.secondLevelCode)
        : data.secondLevelCode) || '';
    // 超管，如果二级单位已被删除，不回显
    if (
      !data.admin &&
      data.secondLevelCode &&
      !secondaryUnitOptions.value?.find(
        (item) => item.id === String(data.secondLevelCode)
      )
    ) {
      formModel.value.secondaryUnit = '';
    }
    secondaryUnitChange(formModel.value.secondaryUnit);
    search();
  };
  // 获取table数据
  const getTableData = async (params: searchSysUserParams) => {
    const { data } = await getSysUserList(params);
    console.log('获取的table数据111111111111111111', data);
    renderData.value = data?.list || [];
    pagination.total = data?.total || 0;
    loading.value = false;
  };
  // 获取单位树数据
  const getUnitTree = async () => {
    const { data } = await queryUnitTree(true);
    secondaryUnitOptions.value = data[0]?.children || [];
  };
  // 二级单位选值变化
  const secondaryUnitChange = (val: any) => {
    formModel.value.subsidiary = '';
    subsidiaryOptions.value =
      secondaryUnitOptions.value?.find((item) => item.id === val)?.children ||
      [];
  };
  // 弹框关闭刷新数据
  const refreshData = () => {
    pagination.current = 1;
    // pagination.value.pageSize = 20;
    search();
  };
  // 查询
  const search = () => {
    // 单位管理员如果没有二级单位不调接口
    // if (
    //   (loading.value || String(userInfo.value?.isSecondLevel) === 'false') &&
    //   userInfo.value?.admin !== '0'
    // ) {
    //   return;
    // }
    if (loading.value && userInfo.value?.admin !== '0') {
      return;
    }
    loading.value = true;
    // “姓名,手机,邮箱”这种格式传参,空值默认为null
    // const arr = [
    //   formModel.value.name || 'null',
    //   formModel.value.phone || 'null',
    //   formModel.value.email || 'null',
    // ];
    const params = {
      // accountStatus: '1',
      pageNo: pagination.current || 1,
      pageSize: pagination.pageSize || 20,
      searchValue: searchValueParam.value,
    };
    getTableData(params);
  };
  // 重置
  const reset = () => {
    // formModel.value = {};
    searchValueParam.value = '';
    // const isAdmin = String(userInfo.value?.admin) === '0';
    // // 回显二级单位
    // if (!isAdmin) {
    //   formModel.value.secondaryUnit = userInfo.value?.secondLevelCode
    //     ? String(userInfo.value?.secondLevelCode)
    //     : '';
    // }
    pagination.current = 1;
    search();
  };
  // table当前页变化
  const onPageChange = (val: any) => {
    pagination.current = val;
    search();
  };
  // table每页条数变化
  const onPageSizeChange = (val: any) => {
    pagination.pageSize = val;
    search();
  };
  // 是否是单位管理员switch
  const unitAdminChange = async (data: MemberRecord) => {
    const obj = {
      userId: data.id,
      // 接口返回中文"是"，"不是"判断
      manager: data.unitManagement === '是' ? '3' : '-1',
    };
    await updateUserDetail(obj);
    // 调编辑接口
    // 更新列表
    search();
  };
  // 生成激活链接并保存到粘贴板
  const { toClipboard } = useClipboard();
  const produceLink = async (record: MemberRecord) => {
    try {
      const org = record.pathNo?.split('.')[3] || '';
      const text = `${window.location.origin}/register-user?userid=${record.id}&orgNo=${org}`;
      await toClipboard(text);
      Message.success(t('user-center.table.opt.invite-success'));
    } catch (err) {
      Message.error(t('user-center.table.opt.invite-error'));
    }
  };
  // 导出
  const exportUsers = async () => {
    const arr = [
      formModel.value.name || 'null',
      formModel.value.phone || 'null',
      formModel.value.email || 'null',
    ];
    const params = {
      searchValue: arr?.join(','),
      orgNo: formModel.value.subsidiary || formModel.value.secondaryUnit || '',
    };
    const response = await exportUser(params);
    createUrl(response.data, '用户列表.xlsx');
  };
  // 导入
  const importUsers = () => {
    if (unitName.value) {
      importDialogVisible.value = true;
    } else {
      Message.warning(t('user-center.import-info'));
    }
  };
  // 新增用户
  const addUser = () => {
    dialogTitle.value = t('user-center.add-users');
    currentTitle.value = 'add';
    addDialogVisible.value = true;
  };
  // 查看用户
  const viewUser = (id: any) => {
    selectId.value = id;
    dialogTitle.value = t('user-center.view.title');
    currentTitle.value = 'view';
    addDialogVisible.value = true;
  };
  // 编辑用户
  const updateUser = (id: string) => {
    selectId.value = id;
    dialogTitle.value = t('user-center.edit.title');
    currentTitle.value = 'edit';
    addDialogVisible.value = true;
  };
  // 注销后的恢复操作
  const recoverUser = async (id: any) => {
    await updateUserDetail({ logout: 1, userId: id });
    Message.success(t('user-center.table.opt.recover-success'));
    search();
  };
  // 注销用户
  const removeUser = (id: any) => {
    Modal.warning({
      title: t('user-center.table.opt.remove'),
      content: t('user-center.table.opt.remove-confirm'),
      closable: true,
      hideCancel: false,
      onOk: async () => {
        // 此处调注销接口
        await updateUserDetail({ logout: 3, userId: id });
        Message.success(t('user-center.table.opt.remove-success'));
        search();
      },
    });
  };
  // 移出用户相关状态
  const removeUserModalVisible = ref(false);
  const removeUserName = ref('');
  const removeUserFullName = ref('');
  const removeUserId = ref('');
  const inviteUserModalVisible = ref(false);
  const editingUserName = ref('');

  // 显示移出用户确认弹窗
  const showRemoveUserConfirm = (record: any) => {
    console.log('确认删除的用户', record);
    removeUserName.value = record.userName;
    removeUserFullName.value = record.userFullname;
    removeUserId.value = record.id;
    removeUserModalVisible.value = true;
  };

  // 取消移出用户
  const handleRemoveUserCancel = () => {
    removeUserModalVisible.value = false;
    removeUserName.value = '';
    removeUserFullName.value = '';
    removeUserId.value = '';
    // Message.info(t('user-center.table.opt.remove-user-cancel'));
  };

  // 确认移出用户
  const handleRemoveUserConfirm = async () => {
    try {
      const params = {
        companyId: storeCompanyId.value,
        companyMembers: [removeUserName.value],
      };
      console.log('确认移出用户', params);
      const { status } = await companyAndOrgRemoveUserApi(params);
      if (status) {
        Message.success(t('user-center.table.opt.remove-user-success'));
        search();
      } else {
        Message.error(t('user-center.table.opt.remove-user-error'));
      }
      handleRemoveUserCancel();
    } catch (error) {
      console.error('移出用户失败:', error);
      Message.error(t('user-center.table.opt.remove-user-error'));
    }
  };

  // 显示邀请用户弹窗
  const showInviteUserModal = (record?: any) => {
    inviteUserModalVisible.value = true;
    if (record) {
      editingUserName.value = record.userName;
    } else {
      editingUserName.value = '';
    }
  };

  onMounted(async () => {
    // await getUnitTree();
    // getUserInfo();
    search();
  });
</script>

<script lang="ts">
  export default {
    name: 'UserCenter',
  };
</script>

<style scoped lang="less">
  .userCenter {
    padding: 20px;
    border: none;
    height: 100%;
    .table-box {
      height: calc(100% - 190px);
      // height: calc(100% - 135px);
      :deep(.arco-table-container) {
        height: 100%;
      }
    }
    :deep(.arco-table) {
      height: 100%;
    }
  }
  :deep(.arco-btn-size-small) {
    padding: 0 5px !important;
  }
  :deep(.arco-form-item-label) {
    width: 100px;
  }
  :deep(.arco-col-8) {
    padding: 0 8px;
  }
  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    white-space: nowrap !important;
  }
  .border-box {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 20px;
  }
  :deep(.arco-btn-size-medium) {
    padding: 0 5px !important;
  }

</style>
