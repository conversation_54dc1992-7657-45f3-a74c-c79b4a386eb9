interface EtagObjArray {
  etag: any;
  number: number;
}
interface EtagObj {
  [key: string]: EtagObjArray[];
}
interface UploadFolder {
  folderId?: string;
}
export interface TaskArrItem {
  id?: string;
  fileHash?: string;
  fileName: string;
  fileSize?: number;
  finishNumber?: number;
}
export interface FileObj {
  name: string;
  size: string;
  lastModifiedDate: string;
}

// eslint-disable-next-line import/export
export interface CurrentFolder {
  id: string;
  name: string;
  path: string;
  projectId: string;
  teamId: string;
  type: string;
  parentId: string;
  sysType: string;
  folderAllDtoList: any[];
}

export interface attachConfig {
  processInstanceId: string;
  taskId: string;
  collaborateId: string;
  deliveryId: string;
}
export interface InTaskArrItem {
  state: number; // 0是什么都不做,1文件处理中,2是上传中,3是暂停,4是上传完成,5上传中断，6是上传失败
  fileHash: string;
  fileName: string;
  name: string;
  fileSize: number;
  allChunkList: any[]; // 所有请求的数据
  whileRequests: any[]; // 正在请求中的请求个数,目前是要永远都保存请求个数为6
  finishNumber: number; // 请求完成的个数
  errNumber: number; // 报错的个数,默认是0个,超多3个就是直接上传中断
  percent: number; // 单个文件上传进度条
  cancel: any; // 用于取消切片上传接口
  description: string;
  config?: attachConfig;
  type?: string;
  resultType?: number;
  fileToken?: string;
  i?: number;
  uid?: string; // 唯一标识
  model?: string; // 所属模块，knowledgeBase表示知识库
}
export interface finishTaskParams {
  fileToken: string;
  folderId: string;
  name: string;
  projectId: string | undefined;
  size: number;
  teamId: string | undefined;
  description: string;
  type?: string;
  processInstanceId?: string;
  taskId?: string;
  collaborateId?: string;
  deliveryId?: string;
  status?: number;
  uid?: string
}
export interface UploadFile {
  attachId?: string;
  fileArr: any[];
  uploadFileList: any[];
  chunkSize?: number;
  groupToken?: string;
  e9y?: string;
  maxRequest?: number;
  etagObj: EtagObj;
  loading: boolean;
  projectId: undefined | string;
  teamId: undefined | string;
  uploadFolderObj: UploadFolder;
  flattenedFolders: any[];
  temporaryFlag: boolean;
  mattersSaveList: any[];
  hasTokenFile: any[];
}
