<template>
  <div class="project-setting">
    <commonTabs
      v-model="tabKey"
      :tabs="tabsData"
      @click-tab="clickTabHandle"
    ></commonTabs>

    <div class="border-box">
      <Standard v-if="tabKey === 'standard'"></Standard>
      <Metas v-if="tabKey === 'attribute'" ref="metasRef"></Metas>
      <CodingRule v-if="tabKey === 'rule'"></CodingRule>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import CodingRule from './codingrule/index.vue';
  import Metas from './metas/index.vue';
  import Standard from './standard/index.vue';
  import commonTabs from '@/components/common-tabs/index.vue';

  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const tabKey = ref('standard');
  const tabsData: any = ref([]);

  onMounted(() => {
    tabsData.value = [
      {
        label: t('menu.standard-list'),
        value: 'standard',
      },
      {
        label: t('standard-setting.meta-attribute-setting'),
        value: 'attribute',
      },
      {
        label: t('standard-setting.coding-rule-management'),
        value: 'rule',
      },
    ];
  });

  const init = () => {
    const tab = localStorage.getItem('standard-default-tab');
    if (tab) {
      tabKey.value = tab;
    }
  };
  init();

  // 手动点击后清除默认tab
  const clickTabHandle = () => {
    localStorage.removeItem('standard-default-tab');
  };

  const activeKey = ref<string>('standard-list');
  const metasRef = ref();

  watch(
    () => activeKey.value,
    (val) => {
      if (val === 'meta-setting') metasRef.value.refreshRuleList();
    }
  );
</script>

<script lang="ts">
  export default {
    name: 'ProjectSetting',
  };
</script>

<style scoped lang="less">
  .border-box {
    border: 1px solid #d9d9d9;
    padding: 20px;
    border-radius: 8px;
  }
  .project-setting {
    width: 100%;
    height: 100%;
    padding: 16px 20px;
    overflow: hidden;
  }

  :deep(.arco-tabs-nav-tab-list) {
    .arco-tabs-tab {
      margin: 0 18px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
</style>
