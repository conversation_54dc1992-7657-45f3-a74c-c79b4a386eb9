<template>
  <div class="opt-line">
    <div class="breadcrumb-wrapper">
      <a-breadcrumb v-if="breadcrumbList.length > 1" :max-count="3">
        <template #separator>
          <icon-right />
        </template>

        <a-breadcrumb-item
          v-for="(item, index) in breadcrumbList"
          :key="item.id"
          class="has-pointer-nav"
        >
          <a-tooltip
            :content="
              index === 0 && locale === 'en-US' ? item.nameEn : item.name
            "
            mini
          >
            <div class="breadcrumb-item" @click="jumpToFolder(item, index)">{{
              index === 0 && locale === 'en-US' ? item.nameEn : item.name
            }}</div>
          </a-tooltip>
        </a-breadcrumb-item>
      </a-breadcrumb>
      <!-- 占位空元素 -->
      <div v-else class="breadcrumb-placeholder"></div>
    </div>

    <a-space
      :size="16"
      class="opt-list"
      style="width: auto; justify-content: flex-end"
    >
      <a-tooltip :content="t('knowledgenew.import')">
        <importIcon class="has-pointer" @click="importFolder" />
      </a-tooltip>
      <a-tooltip :content="t('knowledgenew.add-folder')">
        <folderAdd class="has-pointer" @click="createFolder" />
      </a-tooltip>
      <a-tooltip :content="t('cloud.upload-file')" @click="handleUpload">
        <uploadIcon class="has-pointer" />
      </a-tooltip>
      <a-tooltip :content="t('knowledgenew.download')" @click="handleDownload">
        <downloadIcon
          class="has-pointer"
          :class="{ 'not-active': isDisabled }"
        />
      </a-tooltip>
      <a-popconfirm
        :content="t('cloud.person-delete-tips')"
        type="info"
        position="left"
        @ok="handleDelete()"
      >
        <a-tooltip :content="t('knowledgenew.delete')">
          <icon-delete
            :size="16"
            class="has-pointer icon-color"
            :class="{ 'not-active': isDisabled }"
          />
        </a-tooltip>
      </a-popconfirm>
    </a-space>
  </div>
</template>

<script setup>
  import { storeToRefs } from 'pinia';
  import { ref, computed, toRefs, defineProps, defineEmits } from 'vue';
  import i18n from '@/locale/index';

  import downloadIcon from '@/assets/images/knowledge-base/download2.svg';
  import uploadIcon from '@/assets/images/knowledge-base/upload2.svg';

  import folderAdd from '@/assets/images/knowledge-base/folder-add.svg';
  import importIcon from '@/assets/images/knowledge-base/import.svg';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import { downloadSource } from '@/views/projectSpace/file/hooks/events';
  import { useI18n } from 'vue-i18n';

  const { t, locale } = useI18n();

  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits([
    'newPersonItemAdded',
    'personHandleUpload',
    'personHandleDownload',
  ]);
  const knowledgeStore = useKnowledgeBaseNewStore();
  const { personal, selectedPersonalItems } = storeToRefs(knowledgeStore);
  const { breadcrumb, currentFolder } = toRefs(personal.value);
  const breadcrumbList = computed(() => breadcrumb.value);
  const isDisabled = computed(() => selectedPersonalItems.value.length === 0);
  const createFolder = () => {
    const newFolder = {
      projectId: currentFolder.value.projectId,
      type: currentFolder.value.type,
      parentId: currentFolder.value.id,
      name: '',
      id: '',
      selected: true,
      isAdd: true,
    };
    // 假设 folderList 存储在 store.personal.folderList 中，
    // 使用 setPersonalfolderList 方法更新 state
    knowledgeStore.setPersonalfolderList([
      newFolder,
      ...knowledgeStore.personal.folderList,
    ]);
    // 通知父组件personal-clound
    emit('newPersonItemAdded');
  };
  const jumpToFolder = (item, index) => {
    // 如果点击的是最后一个，就直接返回
    if (index === breadcrumb.value.length - 1) {
      return;
    }
    const newBreadcrumb = breadcrumb.value.slice(0, index + 1);
    knowledgeStore.setPersonCurrentFolder(item);
    knowledgeStore.setPersonalBreadcrumb(newBreadcrumb);
    knowledgeStore.getPersonalFolder('personal');
    knowledgeStore.getfiles('personal');
  };
  const handleDelete = () => {
    knowledgeStore.deleteItems(selectedPersonalItems.value, t, 'personal');
  };
  const handleUpload = () => {
    emit('personHandleUpload');
  };
  // 下载文件
  const handleDownload = () => {
    emit('personHandleDownload');
    selectedPersonalItems.value.forEach((row) => {
      downloadSource(row);
    });

    // knowledgeStore.downloadFile('personal');
  };
  const importFolder = () => {
    emit('personHandleImport');
  };
</script>

<style scoped lang="less">
  .opt-line {
    padding: 16px 0;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .icon-color {
    color: #4e5969;
  }
  .opt-list {
    padding: 8px 16px;
    background: rgba(232, 242, 255, 0.6);
    border-radius: 4px;
  }

  :deep(.has-pointer-nav) {
    cursor: pointer;
  }
  :deep(.has-pointer) {
    cursor: pointer;
    outline: none;
  }
  :deep(.has-pointer:hover) {
    color: rgb(var(--arcoblue-5));
  }
  :deep(.not-active) {
    filter: grayscale(100%);
    pointer-events: none;
    cursor: not-allowed;
    opacity: 0.6;
  }
  :deep(.arco-breadcrumb) {
    flex: 1;
  }
  :deep(.arco-breadcrumb-item) {
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .breadcrumb-item {
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100px;
  }
  :deep(.arco-breadcrumb-item:last-child) {
    font-weight: 600;
  }
  :deep(.arco-breadcrumb-item-separator) {
    margin: 0;
  }
  .breadcrumb-placeholder {
    width: 24px; /* 跟 <a-breadcrumb> 高度差不多 */
  }
</style>
