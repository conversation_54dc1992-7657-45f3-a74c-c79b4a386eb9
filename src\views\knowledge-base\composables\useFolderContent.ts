import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { Message } from '@arco-design/web-vue';
import { useKnowledgeBaseStore } from '@/store';
import { getFolderList, getSearchFile } from '../api';
import {
  fileDownload,
  download,
} from '@/components/file-image/hooks/dropdow-events';
import { zipDownload } from '@/api/upload-file';
import type { Node } from '../types';
import { wpsViewHandle } from '@/hooks/wps';
import folderIcon from '@/assets/images/knowledge-base/folder.svg?url';
import pdfIcon from '@/assets/images/knowledge-base/pdf.svg?url';
import docIcon from '@/assets/images/knowledge-base/doc.svg?url';
import i18n from '@/locale/index';

const { t } = i18n.global;

export default function useFolderContent() {
  const knowledgeBaseStore = useKnowledgeBaseStore();
  const { folderId, isBaseEmpty } = storeToRefs(knowledgeBaseStore);

  const tableData = ref<Array<Node>>([]);
  const folderList = ref<Array<Node>>([]);
  const fileList = ref<Array<Node>>([]);
  const tableLoading = ref(false);

  // 排序方法
  const sortByUpdateDate = (nodeList: Array<Node>, asc: boolean) => {
    if (asc) {
      nodeList.sort((a: Node, b: Node) =>
        a.updateDate.localeCompare(b.updateDate)
      );
    } else {
      nodeList.sort((a: Node, b: Node) =>
        b.updateDate.localeCompare(a.updateDate)
      );
    }
  };

  // 查询文件夹内容
  const queryFolderContent = async (onComplete?: () => void) => {
    try {
      tableLoading.value = true;
      const params = {
        folderId: folderId.value,
        fullTree: false,
      };

      const res = await getFolderList(params);
      if (res.status) {
        folderList.value = [];
        fileList.value = [];

        // 处理文件夹数据
        const folders = res.data.children;
        folderList.value = folders.map((folder: any) => ({
          id: folder.id,
          name: folder.name,
          parentId: folder.parentId,
          type: '文件夹',
          updateDate: folder.updateDate,
          isFolder: true,
          isEdit: false,
          isDelete: false,
          showAllOptions: false,
        }));

        sortByUpdateDate(folderList.value, false);

        // 处理文件数据
        const files = res.data.fileList;
        fileList.value = files.map((file: any) => ({
          id: file.id,
          name: file.name,
          parentId: folderId.value,
          type: file.type,
          size: file.fileSize,
          fileToken: file.ossToken,
          updateDate: file.updateDate,
          isFolder: false,
          isEdit: false,
          isDelete: false,
          showAllOptions: false,
          aiStatus: file.aiStatus,
        }));

        sortByUpdateDate(fileList.value, false);

        tableData.value = [...folderList.value, ...fileList.value];
        if (res.data?.parentId === '-1') {
          knowledgeBaseStore.setIsBaseEmpty(tableData.value.length === 0);
        }

        // 执行回调
        onComplete?.();
      }
    } catch (error) {
      Message.error(t('knowledge.query-file-error-tips'));
      console.error('Query folder content error:', error);
    } finally {
      tableLoading.value = false;
      knowledgeBaseStore.setSearchInfo(false);
    }
  };

  // 打开文件夹/查看文件
  const openFileOrFolder = (item: Node) => {
    if (item.isFolder) {
      knowledgeBaseStore.openFolder({ id: item.id || '', name: item.name });
    } else {
      wpsViewHandle(item, 'preview', 'admin');
    }
  };

  // 检查文件夹名称是否正确
  const checkFolderName = async (name: string) => {
    if (!name) {
      Message.warning(t('knowledge.input-file-name-tips'));
      return false;
    }

    // 名称长度限制
    if (name.length > 255) {
      Message.warning(t('knowledge.input-file-rule-tips'));
      return false;
    }

    // 禁用字符校验
    const pattern = /^[^\\/:*?"<>|]+$/;
    const regResult = pattern.test(name);
    if (!regResult) {
      Message.warning(t('knowledge.input-file-rule1-tips'));
      return false;
    }

    // 名称重复校验
    const queryRes = await getFolderList({
      folderId: folderId.value,
      fullTree: false,
    });
    if (queryRes.status) {
      const isExist = queryRes.data?.children.find(
        (folder: any) => folder.name === name
      );
      if (isExist) {
        Message.warning(t('knowledge.file-name-exsit'));
        return false;
      }
    }
    return true;
  };

  // 下载文件/文件夹
  const downloadZipParams = ref<any>([]);
  const convertFolder = (folder: any, isRoot: boolean) => {
    const folderDownload = {
      id: folder.id,
      parentId: isRoot ? '-1' : folder.parentId,
      dirName: folder.name,
      file: {
        g9s: null,
        f8s:
          folder.fileList?.map((file: any) => {
            return file.ossToken;
          }) || [],
      },
    };
    downloadZipParams.value.push(folderDownload);
    folder.children?.forEach((childFolder: any) => {
      return convertFolder(childFolder, false);
    });
  };

  const downloadBtnClick = (record: Node) => {
    if (record.isFolder) {
      // 查询完整树结构
      const params = {
        folderId: record.id,
        fullTree: true,
      };
      getFolderList(params).then((res: any) => {
        if (res.status) {
          downloadZipParams.value = [];
          convertFolder(res.data, true);
          downloadZipParams.value.push({
            id: '-1',
            parentId: '0',
            dirName: '',
            file: {
              g9s: null,
              f8s: [],
            },
          });
          zipDownload(downloadZipParams.value).then((downloadRes: any) => {
            if (downloadRes.status) {
              download({ name: `${record.name}.zip` }, downloadRes.data);
            }
          });
        }
      });
    } else {
      fileDownload({ fileToken: record.fileToken || '' }).then((res: any) => {
        download({ name: record.name }, res.data);
      });
    }
  };

  const getFileIcon = (type: string) => {
    let fileIcon;
    switch (type) {
      case '文件夹':
        fileIcon = folderIcon;
        break;
      case 'PDF':
        fileIcon = pdfIcon;
        break;
      case 'DOC':
        fileIcon = docIcon;
        break;
      default:
        fileIcon = folderIcon;
    }
    return fileIcon;
  };

  const getSearchFilehandle = async (data: any) => {
    tableLoading.value = true;

    try {
      const param: any = {
        fileName: data.fileName,
        kbId: data.personBaseId,
        pageParam: {},
      };
      const res = await getSearchFile(param);
      if (res.status) tableData.value = res.data?.list || [];
      tableData.value.forEach((item: any) => {
        item.size = item.fileSize; // 因为列表展示的字段为size
      });
      tableLoading.value = false;
    } catch (error) {
      Message.error(t('knowledge.search-fail'));
    } finally {
      tableLoading.value = false;
    }
  };

  return {
    tableData,
    folderList,
    fileList,
    tableLoading,
    queryFolderContent,
    sortByUpdateDate,
    openFileOrFolder,
    downloadBtnClick,
    getFileIcon,
    checkFolderName,
    getSearchFilehandle,
  };
}
