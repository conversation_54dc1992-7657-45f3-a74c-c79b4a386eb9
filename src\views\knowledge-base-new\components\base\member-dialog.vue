<template>
  <a-modal
    :visible="visible"
    :width="600"
    :title="t('knowledgenew.member-dialog-title')"
    title-align="start"
    :mask-closable="false"
    :unmount-on-close="true"
    :esc-to-close="false"
    :footer="false"
    class="member-dialog"
    @before-open="initData"
    @cancel="cancel"
  >
    <!-- <a-input-search
      v-model="searchValue"
      size="small"
      placeholder="搜索"
      allow-clear
      @blur="searchMember"
      @press-enter="($event.target as any)?.blur()"
    /> -->
    <a-table
      :columns="columns"
      :data="tableData"
      row-key="id"
      :loading="loading"
      :pagination="pagination"
      :bordered="false"
      :scroll="{ x: '100%', y: '375px' }"
      @page-change="handlePageChange"
    >
      <template #name="{ record }">
        <div class="table-name">
          <img
            v-if="record.avatarToken"
            :src="
              '/work/api/sys-storage/download_image?f8s=' + record.avatarToken
            "
            class="avatar-img"
          />
          <a-avatar v-else :size="24">
            {{ record.name?.substring(0, 1) || '' }}
          </a-avatar>
          <span
            style="
              margin-left: 8px;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
            >{{ record.name }}</span
          >
        </div>
      </template>
      <template #owner="{ record }">
        <a-tag v-if="record.owner" color="orangered">{{
          t('knowledgenew.creator')
        }}</a-tag>
        <a-tag v-else color="arcoblue">{{ t('knowledgenew.member') }}</a-tag>
      </template>
      <template #options="{ record }">
        <a-popconfirm
          v-if="!record.owner"
          :content="t('knowledgenew.remove-member-confirm')"
          type="info"
          position="top"
          @ok="handleRemoveMember(record)"
        >
          <a-button
            v-if="!record.owner"
            type="text"
            status="danger"
            style="height: 24px; padding: 0 4px; border: none"
            >{{ t('knowledgenew.remove-member') }}</a-button
          >
        </a-popconfirm>
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, defineEmits, defineProps, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { getUserName } from '@/utils/auth';
  import { getBaseMemberList, removeMember } from '../../api';

  const { t } = useI18n();
  const username = getUserName() || '';

  const route = useRoute();
  const { kbId } = route.params;

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    creatorUsername: {
      type: String,
      default: '',
    },
  });
  const emits = defineEmits(['update:visible', 'submit']);

  const loading = ref(false);

  const searchValue = ref('');
  const tableData = ref([]);

  // 分页配置
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    // pageSizeOptions: [20, 50, 100],
    showTotal: false,
    showJumper: false,
    showPageSize: false,
  });

  // 查询知识库成员列表
  const queryMemberList = async () => {
    try {
      tableData.value = [];
      loading.value = true;
      const params = {
        kbId: kbId as string,
        pageParam: {
          pageNo: pagination.value.current,
          pageSize: pagination.value.pageSize,
        },
      };
      const res = await getBaseMemberList(params);
      if (res.status) {
        tableData.value = res.data?.list || [];
        pagination.value.total = res.data?.total || 0;
      }
    } catch (error) {
      console.error(error);
    }
    loading.value = false;
  };

  // 翻页
  const handlePageChange = (current: number): void => {
    pagination.value.current = current;
    queryMemberList();
  };

  // 数据初始化
  const initData = () => {
    pagination.value.current = 1;
    pagination.value.total = 0;
    queryMemberList();
  };

  const columns = computed(() => {
    if (username === props.creatorUsername) {
      return [
        {
          title: t('knowledgenew.member-name'),
          dataIndex: 'name',
          slotName: 'name',
        },
        {
          title: t('knowledgenew.organization-type'),
          dataIndex: 'owner',
          slotName: 'owner',
          width: 160,
        },
        {
          title: t('knowledgenew.operator'),
          dataIndex: 'options',
          slotName: 'options',
          width: 110,
        },
      ];
    }
    return [
      {
        title: t('knowledgenew.member-name'),
        dataIndex: 'name',
        slotName: 'name',
      },
      {
        title: t('knowledgenew.organization-type'),
        dataIndex: 'owner',
        slotName: 'owner',
        width: 104,
      },
    ];
  });

  // 移除成员
  const handleRemoveMember = async (record: any) => {
    try {
      const params = {
        kbId: kbId as string,
        userNames: [record.username],
      };
      const res = await removeMember(params);
      if (res.status) {
        Message.success(t('knowledgenew.remove-member-success'));
        // 更新列表数据
        queryMemberList();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const cancel = () => {
    emits('update:visible', false);
  };
</script>

<style scoped lang="less">
  .table-name {
    display: flex;
    align-items: center;
  }

  .avatar-img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
    display: block;
  }

  :deep(.arco-tag-size-medium) {
    font-size: 14px;
  }

  :deep(.arco-table-body .arco-table-cell) {
    padding: 8px 16px !important;
  }

  :deep(.arco-table-pagination) {
    margin-top: 16px;
    position: absolute;
    bottom: 0;
    right: 0;
  }
</style>

<style lang="less">
  .member-dialog {
    .arco-modal-header {
      height: 52px;
    }
    .arco-modal-title {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
      line-height: 28px;
    }
    .arco-modal-body {
      padding: 16px 20px 20px;
    }

    // 搜索框样式覆盖
    .arco-input-wrapper {
      margin-bottom: 16px;
      background-color: #fff;
      border: 1px solid #c9cdd4 !important;
      border-radius: var(--border-radius-medium);
    }
  }
</style>
