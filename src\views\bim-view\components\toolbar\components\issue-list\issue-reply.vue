<template>
  <a-card
    v-draggable="{ handle: 'strong', bounds: false }"
    class="issue-card"
    :style="`right:${route.name === 'bimView' ? '400px' : '0'}`"
  >
    <template #extra>
      <a-button type="text" @click="close">{{
        $t('model-viewer.close')
      }}</a-button>
    </template>
    <template #title>
      <strong class="title">{{ $t('model-viewer.issueReply') }}</strong>
    </template>
    <a-form ref="issueFormRef" :model="issueForm">
      <a-form-item field="photo" :label="$t('model-viewer.problemScreenshot')">
        <a-upload
          v-if="!isViewModal"
          ref="uploadRef"
          v-model:file-list="fileList"
          action="/api/sys-storage/image/upload"
          :headers="{
            'Fusion-Auth': getToken() || '',
            'Fusion-Biz': setFusionBiz() || '',
          }"
          :limit="1"
          accept="image/png, image/jpeg,image/jpg"
          image-preview
          list-type="picture-card"
          :show-link="true"
          :on-button-click="handleButtonClick"
        />
        <a-image
          v-else
          width="80"
          height="80"
          :src="issueForm.picToken"
        ></a-image>
      </a-form-item>
      <a-form-item
        v-if="props.issueReplyModel === 'create'"
        field="stage"
        :label="$t('model-viewer.modifyStatus')"
      >
        <a-select v-model="issueForm.status">
          <a-option :value="0">{{ $t('model-viewer.unresolved') }}</a-option>
          <a-option :value="2">{{ $t('model-viewer.inProgress') }}</a-option>
          <a-option :value="1">{{ $t('model-viewer.resolved') }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="message" :label="$t('model-viewer.replyIssue')">
        <a-textarea
          v-model="issueForm.reply"
          :placeholder="isViewModal ? '' : $t('model-viewer.enterIssueContent')"
          :max-length="500"
          :show-word-limit="isViewModal ? false : true"
          :disabled="isViewModal"
          :auto-size="{
            minRows: 4,
            maxRows: 4,
          }"
        />
      </a-form-item>
      <a-button v-if="!isViewModal" type="primary" @click="replySubmit">{{
        $t('model-viewer.confirm')
      }}</a-button>
    </a-form>
  </a-card>
  <!--截图组件-->
  <screen-short
    v-if="screenshotStatus"
    @destroy-component="destroyComponent"
    @get-image-data="completeCallback"
  ></screen-short>
</template>

<script setup lang="ts">
  // import ScreenShort from 'js-web-screen-shot';
  import { ref, reactive, computed, watch } from 'vue';
  import { setFusionBiz } from '@/api/interceptor';
  import { getToken } from '@/utils/auth';
  import { Message } from '@arco-design/web-vue';
  // import useModelToolsStore from '@/store/modules/model-viewer';
  import { IssuesDetail, IssueFormInter, replyIssue, IssueLog } from './api';
  import { fileUpload } from '../create-issue/api';
  import { useRoute } from 'vue-router';

  const route = useRoute();
  const props = defineProps<{
    currentIssue: IssuesDetail;
    issueReplyData: any;
    issueReplyModel: any;
  }>();

  const emits = defineEmits<{
    (e: 'issueListReload'): void;
    (e: 'close'): void;
  }>();

  const curPicToken = ref<string>('');
  const fileList = reactive<any[]>([]);

  const issueForm = reactive<IssueFormInter>({
    id: '',
    reply: '',
    status: props.currentIssue.state,
    items: [],
    picToken: '',
  });

  const screenshotStatus = ref<boolean>(false);
  // 销毁组件函数
  const destroyComponent = (status: boolean) => {
    screenshotStatus.value = status;
  };
  const completeCallback = async (base64data: any) => {
    const url = base64data;
    // const url = base64data.base64;
    const bytes = window.atob(url.split(',')[1]);
    const buffer = new ArrayBuffer(bytes.length);
    const uint = new Uint8Array(buffer);
    for (let j = 0; j < bytes.length; j++) {
      uint[j] = bytes.charCodeAt(j);
    }
    const imageFile = new Blob([buffer], { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('file', imageFile, `${Date.now()}.jpeg`);
    const res = await fileUpload(formData);
    if (res.status) {
      curPicToken.value = res.data.fileToken;

      fileList.push({
        url: `/api/sys-storage/download_image?f8s=${res.data.fileToken}`,
      });
    }
  };
  // const screenShot = () => {
  //   /* eslint-disable no-new */
  //   screenShotHandler.value = new ScreenShort({
  //     enableWebRtc: false, // 是否显示选项框
  //     level: 9999, // 层级级别
  //     completeCallback, // 截图成功完成的回调
  //     closeCallback,
  //   });
  // };
  const handleButtonClick = (event: Event): Promise<FileList> | void => {
    event.preventDefault();
    screenshotStatus.value = true;
    // screenShot();
  };

  // const modelToolsStore = useModelToolsStore();
  const close = () => {
    // modelToolsStore.setIssueReplyModal(false);
    emits('close');
  };

  // const modalHeight = computed(() => {
  //   return modelToolsStore.issueReplyModel === 'create' ? '400px' : '300px';
  // });
  // const isViewModal = computed(() => {
  //   return modelToolsStore.issueReplyModel !== 'create';
  // });

  watch(
    () => props.issueReplyData,
    (value: IssueLog) => {
      issueForm.reply = value.reply;
      issueForm.picToken = `/api/sys-storage/download_image?f8s=${value.picToken}`;
    },
    {
      immediate: true,
    }
  );

  const replySubmit = async () => {
    issueForm.items = [
      {
        fileId: props.currentIssue.issueFileList[0].fileId,
        picToken: curPicToken.value,
      },
    ];
    issueForm.id = props.currentIssue?.id;
    issueForm.projectId = route.query.projectId;
    try {
      const res = await replyIssue(issueForm);
      if (res.status) {
        Message.success(res.message);
        close();
        emits('issueListReload');
      }
    } catch (e) {
      console.log(e);
    }
  };
</script>

<style lang="less" scoped>
  .title {
    display: block;
    width: 100%;
    font-weight: 400;
    cursor: move;
  }
  .issue-card {
    position: absolute;
    top: 25%;
    right: 0;
    width: 400px;
    height: v-bind(modalHeight);
    z-index: 10;
  }
</style>
