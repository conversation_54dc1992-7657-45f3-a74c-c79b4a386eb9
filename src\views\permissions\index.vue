<template>
  <div class="permission">
    <a-row style="margin-bottom: 20px">
      <a-col :flex="1">
        <table-title title="权限列表"></table-title>
      </a-col>
    </a-row>
    <a-tabs default-active-key="menu" style="height: calc(100vh - 265px)">
      <a-tab-pane class="tab-pane" key="menu" title="菜单权限">
        <menu-manage></menu-manage>
      </a-tab-pane>
      <a-tab-pane class="tab-pane" key="request" title="接口权限">
        <request-manage></request-manage>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, onMounted, reactive } from 'vue';
  import RequestManage from './components/request-manage.vue';
  import MenuManage from './components/menu-manage.vue';
  import TableTitle from '@/components/table-title/index.vue';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';

  const { t } = useI18n();

  const loading = ref(false);

  // onMounted(async () => {
  //   search();
  // });
</script>

<style scoped lang="less">
  .permission {
    padding: 20px;
    border: none;
    height: 100%;
    .table-box {
      // height: calc(100% - 230px);
      height: calc(100% - 125px);
    }
    :deep(.arco-table) {
      height: 100%;
    }
  }
</style>
