!(function (e, t) {
  'object' == typeof exports && 'object' == typeof module
    ? (module.exports = t())
    : 'function' == typeof define && define.amd
    ? define([], t)
    : 'object' == typeof exports
    ? (exports.OpenSDK = t())
    : (e.OpenSDK = t());
})(self, function () {
  return (function () {
    var e = {
        192: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(20),
            n(716),
            n(147),
            n(575),
            (function () {
              var e = r,
                t = e.lib.BlockCipher,
                n = e.algo,
                i = [],
                o = [],
                a = [],
                s = [],
                c = [],
                u = [],
                l = [],
                f = [],
                d = [],
                h = [];
              !(function () {
                for (var e = [], t = 0; t < 256; t++)
                  e[t] = t < 128 ? t << 1 : (t << 1) ^ 283;
                var n = 0,
                  r = 0;
                for (t = 0; t < 256; t++) {
                  var p = r ^ (r << 1) ^ (r << 2) ^ (r << 3) ^ (r << 4);
                  (p = (p >>> 8) ^ (255 & p) ^ 99), (i[n] = p), (o[p] = n);
                  var v = e[n],
                    g = e[v],
                    _ = e[g],
                    E = (257 * e[p]) ^ (16843008 * p);
                  (a[n] = (E << 24) | (E >>> 8)),
                    (s[n] = (E << 16) | (E >>> 16)),
                    (c[n] = (E << 8) | (E >>> 24)),
                    (u[n] = E),
                    (E =
                      (16843009 * _) ^
                      (65537 * g) ^
                      (257 * v) ^
                      (16843008 * n)),
                    (l[p] = (E << 24) | (E >>> 8)),
                    (f[p] = (E << 16) | (E >>> 16)),
                    (d[p] = (E << 8) | (E >>> 24)),
                    (h[p] = E),
                    n
                      ? ((n = v ^ e[e[e[_ ^ v]]]), (r ^= e[e[r]]))
                      : (n = r = 1);
                }
              })();
              var p = [0, 1, 2, 4, 8, 16, 32, 64, 128, 27, 54],
                v = (n.AES = t.extend({
                  _doReset: function () {
                    if (!this._nRounds || this._keyPriorReset !== this._key) {
                      for (
                        var e = (this._keyPriorReset = this._key),
                          t = e.words,
                          n = e.sigBytes / 4,
                          r = 4 * ((this._nRounds = n + 6) + 1),
                          o = (this._keySchedule = []),
                          a = 0;
                        a < r;
                        a++
                      )
                        a < n
                          ? (o[a] = t[a])
                          : ((u = o[a - 1]),
                            a % n
                              ? n > 6 &&
                                a % n == 4 &&
                                (u =
                                  (i[u >>> 24] << 24) |
                                  (i[(u >>> 16) & 255] << 16) |
                                  (i[(u >>> 8) & 255] << 8) |
                                  i[255 & u])
                              : ((u =
                                  (i[(u = (u << 8) | (u >>> 24)) >>> 24] <<
                                    24) |
                                  (i[(u >>> 16) & 255] << 16) |
                                  (i[(u >>> 8) & 255] << 8) |
                                  i[255 & u]),
                                (u ^= p[(a / n) | 0] << 24)),
                            (o[a] = o[a - n] ^ u));
                      for (
                        var s = (this._invKeySchedule = []), c = 0;
                        c < r;
                        c++
                      ) {
                        if (((a = r - c), c % 4)) var u = o[a];
                        else u = o[a - 4];
                        s[c] =
                          c < 4 || a <= 4
                            ? u
                            : l[i[u >>> 24]] ^
                              f[i[(u >>> 16) & 255]] ^
                              d[i[(u >>> 8) & 255]] ^
                              h[i[255 & u]];
                      }
                    }
                  },
                  encryptBlock: function (e, t) {
                    this._doCryptBlock(e, t, this._keySchedule, a, s, c, u, i);
                  },
                  decryptBlock: function (e, t) {
                    var n = e[t + 1];
                    (e[t + 1] = e[t + 3]),
                      (e[t + 3] = n),
                      this._doCryptBlock(
                        e,
                        t,
                        this._invKeySchedule,
                        l,
                        f,
                        d,
                        h,
                        o
                      ),
                      (n = e[t + 1]),
                      (e[t + 1] = e[t + 3]),
                      (e[t + 3] = n);
                  },
                  _doCryptBlock: function (e, t, n, r, i, o, a, s) {
                    for (
                      var c = this._nRounds,
                        u = e[t] ^ n[0],
                        l = e[t + 1] ^ n[1],
                        f = e[t + 2] ^ n[2],
                        d = e[t + 3] ^ n[3],
                        h = 4,
                        p = 1;
                      p < c;
                      p++
                    ) {
                      var v =
                          r[u >>> 24] ^
                          i[(l >>> 16) & 255] ^
                          o[(f >>> 8) & 255] ^
                          a[255 & d] ^
                          n[h++],
                        g =
                          r[l >>> 24] ^
                          i[(f >>> 16) & 255] ^
                          o[(d >>> 8) & 255] ^
                          a[255 & u] ^
                          n[h++],
                        _ =
                          r[f >>> 24] ^
                          i[(d >>> 16) & 255] ^
                          o[(u >>> 8) & 255] ^
                          a[255 & l] ^
                          n[h++],
                        E =
                          r[d >>> 24] ^
                          i[(u >>> 16) & 255] ^
                          o[(l >>> 8) & 255] ^
                          a[255 & f] ^
                          n[h++];
                      (u = v), (l = g), (f = _), (d = E);
                    }
                    (v =
                      ((s[u >>> 24] << 24) |
                        (s[(l >>> 16) & 255] << 16) |
                        (s[(f >>> 8) & 255] << 8) |
                        s[255 & d]) ^
                      n[h++]),
                      (g =
                        ((s[l >>> 24] << 24) |
                          (s[(f >>> 16) & 255] << 16) |
                          (s[(d >>> 8) & 255] << 8) |
                          s[255 & u]) ^
                        n[h++]),
                      (_ =
                        ((s[f >>> 24] << 24) |
                          (s[(d >>> 16) & 255] << 16) |
                          (s[(u >>> 8) & 255] << 8) |
                          s[255 & l]) ^
                        n[h++]),
                      (E =
                        ((s[d >>> 24] << 24) |
                          (s[(u >>> 16) & 255] << 16) |
                          (s[(l >>> 8) & 255] << 8) |
                          s[255 & f]) ^
                        n[h++]),
                      (e[t] = v),
                      (e[t + 1] = g),
                      (e[t + 2] = _),
                      (e[t + 3] = E);
                  },
                  keySize: 8,
                }));
              e.AES = t._createHelper(v);
            })(),
            r.AES);
        },
        575: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(147),
            void (
              r.lib.Cipher ||
              (function (e) {
                var t = r,
                  n = t.lib,
                  i = n.Base,
                  o = n.WordArray,
                  a = n.BufferedBlockAlgorithm,
                  s = t.enc,
                  c = (s.Utf8, s.Base64),
                  u = t.algo.EvpKDF,
                  l = (n.Cipher = a.extend({
                    cfg: i.extend(),
                    createEncryptor: function (e, t) {
                      return this.create(this._ENC_XFORM_MODE, e, t);
                    },
                    createDecryptor: function (e, t) {
                      return this.create(this._DEC_XFORM_MODE, e, t);
                    },
                    init: function (e, t, n) {
                      (this.cfg = this.cfg.extend(n)),
                        (this._xformMode = e),
                        (this._key = t),
                        this.reset();
                    },
                    reset: function () {
                      a.reset.call(this), this._doReset();
                    },
                    process: function (e) {
                      return this._append(e), this._process();
                    },
                    finalize: function (e) {
                      return e && this._append(e), this._doFinalize();
                    },
                    keySize: 4,
                    ivSize: 4,
                    _ENC_XFORM_MODE: 1,
                    _DEC_XFORM_MODE: 2,
                    _createHelper: (function () {
                      function e(e) {
                        return 'string' == typeof e ? y : _;
                      }
                      return function (t) {
                        return {
                          encrypt: function (n, r, i) {
                            return e(r).encrypt(t, n, r, i);
                          },
                          decrypt: function (n, r, i) {
                            return e(r).decrypt(t, n, r, i);
                          },
                        };
                      };
                    })(),
                  })),
                  f =
                    ((n.StreamCipher = l.extend({
                      _doFinalize: function () {
                        return this._process(!0);
                      },
                      blockSize: 1,
                    })),
                    (t.mode = {})),
                  d = (n.BlockCipherMode = i.extend({
                    createEncryptor: function (e, t) {
                      return this.Encryptor.create(e, t);
                    },
                    createDecryptor: function (e, t) {
                      return this.Decryptor.create(e, t);
                    },
                    init: function (e, t) {
                      (this._cipher = e), (this._iv = t);
                    },
                  })),
                  h = (f.CBC = (function () {
                    var t = d.extend();
                    function n(t, n, r) {
                      var i,
                        o = this._iv;
                      o ? ((i = o), (this._iv = e)) : (i = this._prevBlock);
                      for (var a = 0; a < r; a++) t[n + a] ^= i[a];
                    }
                    return (
                      (t.Encryptor = t.extend({
                        processBlock: function (e, t) {
                          var r = this._cipher,
                            i = r.blockSize;
                          n.call(this, e, t, i),
                            r.encryptBlock(e, t),
                            (this._prevBlock = e.slice(t, t + i));
                        },
                      })),
                      (t.Decryptor = t.extend({
                        processBlock: function (e, t) {
                          var r = this._cipher,
                            i = r.blockSize,
                            o = e.slice(t, t + i);
                          r.decryptBlock(e, t),
                            n.call(this, e, t, i),
                            (this._prevBlock = o);
                        },
                      })),
                      t
                    );
                  })()),
                  p = ((t.pad = {}).Pkcs7 = {
                    pad: function (e, t) {
                      for (
                        var n = 4 * t,
                          r = n - (e.sigBytes % n),
                          i = (r << 24) | (r << 16) | (r << 8) | r,
                          a = [],
                          s = 0;
                        s < r;
                        s += 4
                      )
                        a.push(i);
                      var c = o.create(a, r);
                      e.concat(c);
                    },
                    unpad: function (e) {
                      var t = 255 & e.words[(e.sigBytes - 1) >>> 2];
                      e.sigBytes -= t;
                    },
                  }),
                  v =
                    ((n.BlockCipher = l.extend({
                      cfg: l.cfg.extend({ mode: h, padding: p }),
                      reset: function () {
                        var e;
                        l.reset.call(this);
                        var t = this.cfg,
                          n = t.iv,
                          r = t.mode;
                        this._xformMode == this._ENC_XFORM_MODE
                          ? (e = r.createEncryptor)
                          : ((e = r.createDecryptor),
                            (this._minBufferSize = 1)),
                          this._mode && this._mode.__creator == e
                            ? this._mode.init(this, n && n.words)
                            : ((this._mode = e.call(r, this, n && n.words)),
                              (this._mode.__creator = e));
                      },
                      _doProcessBlock: function (e, t) {
                        this._mode.processBlock(e, t);
                      },
                      _doFinalize: function () {
                        var e,
                          t = this.cfg.padding;
                        return (
                          this._xformMode == this._ENC_XFORM_MODE
                            ? (t.pad(this._data, this.blockSize),
                              (e = this._process(!0)))
                            : ((e = this._process(!0)), t.unpad(e)),
                          e
                        );
                      },
                      blockSize: 4,
                    })),
                    (n.CipherParams = i.extend({
                      init: function (e) {
                        this.mixIn(e);
                      },
                      toString: function (e) {
                        return (e || this.formatter).stringify(this);
                      },
                    }))),
                  g = ((t.format = {}).OpenSSL = {
                    stringify: function (e) {
                      var t = e.ciphertext,
                        n = e.salt;
                      return (
                        n
                          ? o
                              .create([1398893684, 1701076831])
                              .concat(n)
                              .concat(t)
                          : t
                      ).toString(c);
                    },
                    parse: function (e) {
                      var t,
                        n = c.parse(e),
                        r = n.words;
                      return (
                        1398893684 == r[0] &&
                          1701076831 == r[1] &&
                          ((t = o.create(r.slice(2, 4))),
                          r.splice(0, 4),
                          (n.sigBytes -= 16)),
                        v.create({ ciphertext: n, salt: t })
                      );
                    },
                  }),
                  _ = (n.SerializableCipher = i.extend({
                    cfg: i.extend({ format: g }),
                    encrypt: function (e, t, n, r) {
                      r = this.cfg.extend(r);
                      var i = e.createEncryptor(n, r),
                        o = i.finalize(t),
                        a = i.cfg;
                      return v.create({
                        ciphertext: o,
                        key: n,
                        iv: a.iv,
                        algorithm: e,
                        mode: a.mode,
                        padding: a.padding,
                        blockSize: e.blockSize,
                        formatter: r.format,
                      });
                    },
                    decrypt: function (e, t, n, r) {
                      return (
                        (r = this.cfg.extend(r)),
                        (t = this._parse(t, r.format)),
                        e.createDecryptor(n, r).finalize(t.ciphertext)
                      );
                    },
                    _parse: function (e, t) {
                      return 'string' == typeof e ? t.parse(e, this) : e;
                    },
                  })),
                  E = ((t.kdf = {}).OpenSSL = {
                    execute: function (e, t, n, r) {
                      r || (r = o.random(8));
                      var i = u.create({ keySize: t + n }).compute(e, r),
                        a = o.create(i.words.slice(t), 4 * n);
                      return (
                        (i.sigBytes = 4 * t),
                        v.create({ key: i, iv: a, salt: r })
                      );
                    },
                  }),
                  y = (n.PasswordBasedCipher = _.extend({
                    cfg: _.cfg.extend({ kdf: E }),
                    encrypt: function (e, t, n, r) {
                      var i = (r = this.cfg.extend(r)).kdf.execute(
                        n,
                        e.keySize,
                        e.ivSize
                      );
                      r.iv = i.iv;
                      var o = _.encrypt.call(this, e, t, i.key, r);
                      return o.mixIn(i), o;
                    },
                    decrypt: function (e, t, n, r) {
                      (r = this.cfg.extend(r)), (t = this._parse(t, r.format));
                      var i = r.kdf.execute(n, e.keySize, e.ivSize, t.salt);
                      return (
                        (r.iv = i.iv), _.decrypt.call(this, e, t, i.key, r)
                      );
                    },
                  }));
              })()
            ));
        },
        751: function (e, t, n) {
          var r;
          e.exports =
            ((r =
              r ||
              (function (e, t) {
                var r;
                if (
                  ('undefined' != typeof window &&
                    window.crypto &&
                    (r = window.crypto),
                  'undefined' != typeof self &&
                    self.crypto &&
                    (r = self.crypto),
                  'undefined' != typeof globalThis &&
                    globalThis.crypto &&
                    (r = globalThis.crypto),
                  !r &&
                    'undefined' != typeof window &&
                    window.msCrypto &&
                    (r = window.msCrypto),
                  !r && void 0 !== n.g && n.g.crypto && (r = n.g.crypto),
                  !r)
                )
                  try {
                    r = n(480);
                  } catch (e) {}
                var i = function () {
                    if (r) {
                      if ('function' == typeof r.getRandomValues)
                        try {
                          return r.getRandomValues(new Uint32Array(1))[0];
                        } catch (e) {}
                      if ('function' == typeof r.randomBytes)
                        try {
                          return r.randomBytes(4).readInt32LE();
                        } catch (e) {}
                    }
                    throw new Error(
                      'Native crypto module could not be used to get secure random number.'
                    );
                  },
                  o =
                    Object.create ||
                    (function () {
                      function e() {}
                      return function (t) {
                        var n;
                        return (
                          (e.prototype = t),
                          (n = new e()),
                          (e.prototype = null),
                          n
                        );
                      };
                    })(),
                  a = {},
                  s = (a.lib = {}),
                  c = (s.Base = {
                    extend: function (e) {
                      var t = o(this);
                      return (
                        e && t.mixIn(e),
                        (t.hasOwnProperty('init') && this.init !== t.init) ||
                          (t.init = function () {
                            t.$super.init.apply(this, arguments);
                          }),
                        (t.init.prototype = t),
                        (t.$super = this),
                        t
                      );
                    },
                    create: function () {
                      var e = this.extend();
                      return e.init.apply(e, arguments), e;
                    },
                    init: function () {},
                    mixIn: function (e) {
                      for (var t in e) e.hasOwnProperty(t) && (this[t] = e[t]);
                      e.hasOwnProperty('toString') &&
                        (this.toString = e.toString);
                    },
                    clone: function () {
                      return this.init.prototype.extend(this);
                    },
                  }),
                  u = (s.WordArray = c.extend({
                    init: function (e, n) {
                      (e = this.words = e || []),
                        (this.sigBytes = n != t ? n : 4 * e.length);
                    },
                    toString: function (e) {
                      return (e || f).stringify(this);
                    },
                    concat: function (e) {
                      var t = this.words,
                        n = e.words,
                        r = this.sigBytes,
                        i = e.sigBytes;
                      if ((this.clamp(), r % 4))
                        for (var o = 0; o < i; o++) {
                          var a = (n[o >>> 2] >>> (24 - (o % 4) * 8)) & 255;
                          t[(r + o) >>> 2] |= a << (24 - ((r + o) % 4) * 8);
                        }
                      else
                        for (var s = 0; s < i; s += 4)
                          t[(r + s) >>> 2] = n[s >>> 2];
                      return (this.sigBytes += i), this;
                    },
                    clamp: function () {
                      var t = this.words,
                        n = this.sigBytes;
                      (t[n >>> 2] &= 4294967295 << (32 - (n % 4) * 8)),
                        (t.length = e.ceil(n / 4));
                    },
                    clone: function () {
                      var e = c.clone.call(this);
                      return (e.words = this.words.slice(0)), e;
                    },
                    random: function (e) {
                      for (var t = [], n = 0; n < e; n += 4) t.push(i());
                      return new u.init(t, e);
                    },
                  })),
                  l = (a.enc = {}),
                  f = (l.Hex = {
                    stringify: function (e) {
                      for (
                        var t = e.words, n = e.sigBytes, r = [], i = 0;
                        i < n;
                        i++
                      ) {
                        var o = (t[i >>> 2] >>> (24 - (i % 4) * 8)) & 255;
                        r.push((o >>> 4).toString(16)),
                          r.push((15 & o).toString(16));
                      }
                      return r.join('');
                    },
                    parse: function (e) {
                      for (var t = e.length, n = [], r = 0; r < t; r += 2)
                        n[r >>> 3] |=
                          parseInt(e.substr(r, 2), 16) << (24 - (r % 8) * 4);
                      return new u.init(n, t / 2);
                    },
                  }),
                  d = (l.Latin1 = {
                    stringify: function (e) {
                      for (
                        var t = e.words, n = e.sigBytes, r = [], i = 0;
                        i < n;
                        i++
                      ) {
                        var o = (t[i >>> 2] >>> (24 - (i % 4) * 8)) & 255;
                        r.push(String.fromCharCode(o));
                      }
                      return r.join('');
                    },
                    parse: function (e) {
                      for (var t = e.length, n = [], r = 0; r < t; r++)
                        n[r >>> 2] |=
                          (255 & e.charCodeAt(r)) << (24 - (r % 4) * 8);
                      return new u.init(n, t);
                    },
                  }),
                  h = (l.Utf8 = {
                    stringify: function (e) {
                      try {
                        return decodeURIComponent(escape(d.stringify(e)));
                      } catch (e) {
                        throw new Error('Malformed UTF-8 data');
                      }
                    },
                    parse: function (e) {
                      return d.parse(unescape(encodeURIComponent(e)));
                    },
                  }),
                  p = (s.BufferedBlockAlgorithm = c.extend({
                    reset: function () {
                      (this._data = new u.init()), (this._nDataBytes = 0);
                    },
                    _append: function (e) {
                      'string' == typeof e && (e = h.parse(e)),
                        this._data.concat(e),
                        (this._nDataBytes += e.sigBytes);
                    },
                    _process: function (t) {
                      var n,
                        r = this._data,
                        i = r.words,
                        o = r.sigBytes,
                        a = this.blockSize,
                        s = o / (4 * a),
                        c =
                          (s = t
                            ? e.ceil(s)
                            : e.max((0 | s) - this._minBufferSize, 0)) * a,
                        l = e.min(4 * c, o);
                      if (c) {
                        for (var f = 0; f < c; f += a)
                          this._doProcessBlock(i, f);
                        (n = i.splice(0, c)), (r.sigBytes -= l);
                      }
                      return new u.init(n, l);
                    },
                    clone: function () {
                      var e = c.clone.call(this);
                      return (e._data = this._data.clone()), e;
                    },
                    _minBufferSize: 0,
                  })),
                  v =
                    ((s.Hasher = p.extend({
                      cfg: c.extend(),
                      init: function (e) {
                        (this.cfg = this.cfg.extend(e)), this.reset();
                      },
                      reset: function () {
                        p.reset.call(this), this._doReset();
                      },
                      update: function (e) {
                        return this._append(e), this._process(), this;
                      },
                      finalize: function (e) {
                        return e && this._append(e), this._doFinalize();
                      },
                      blockSize: 16,
                      _createHelper: function (e) {
                        return function (t, n) {
                          return new e.init(n).finalize(t);
                        };
                      },
                      _createHmacHelper: function (e) {
                        return function (t, n) {
                          return new v.HMAC.init(e, n).finalize(t);
                        };
                      },
                    })),
                    (a.algo = {}));
                return a;
              })(Math)),
            r);
        },
        20: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            (function () {
              var e = r,
                t = e.lib.WordArray;
              function n(e, n, r) {
                for (var i = [], o = 0, a = 0; a < n; a++)
                  if (a % 4) {
                    var s =
                      (r[e.charCodeAt(a - 1)] << ((a % 4) * 2)) |
                      (r[e.charCodeAt(a)] >>> (6 - (a % 4) * 2));
                    (i[o >>> 2] |= s << (24 - (o % 4) * 8)), o++;
                  }
                return t.create(i, o);
              }
              e.enc.Base64 = {
                stringify: function (e) {
                  var t = e.words,
                    n = e.sigBytes,
                    r = this._map;
                  e.clamp();
                  for (var i = [], o = 0; o < n; o += 3)
                    for (
                      var a =
                          (((t[o >>> 2] >>> (24 - (o % 4) * 8)) & 255) << 16) |
                          (((t[(o + 1) >>> 2] >>> (24 - ((o + 1) % 4) * 8)) &
                            255) <<
                            8) |
                          ((t[(o + 2) >>> 2] >>> (24 - ((o + 2) % 4) * 8)) &
                            255),
                        s = 0;
                      s < 4 && o + 0.75 * s < n;
                      s++
                    )
                      i.push(r.charAt((a >>> (6 * (3 - s))) & 63));
                  var c = r.charAt(64);
                  if (c) for (; i.length % 4; ) i.push(c);
                  return i.join('');
                },
                parse: function (e) {
                  var t = e.length,
                    r = this._map,
                    i = this._reverseMap;
                  if (!i) {
                    i = this._reverseMap = [];
                    for (var o = 0; o < r.length; o++) i[r.charCodeAt(o)] = o;
                  }
                  var a = r.charAt(64);
                  if (a) {
                    var s = e.indexOf(a);
                    -1 !== s && (t = s);
                  }
                  return n(e, t, i);
                },
                _map: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
              };
            })(),
            r.enc.Base64);
        },
        320: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            (function () {
              var e = r,
                t = e.lib.WordArray;
              function n(e, n, r) {
                for (var i = [], o = 0, a = 0; a < n; a++)
                  if (a % 4) {
                    var s =
                      (r[e.charCodeAt(a - 1)] << ((a % 4) * 2)) |
                      (r[e.charCodeAt(a)] >>> (6 - (a % 4) * 2));
                    (i[o >>> 2] |= s << (24 - (o % 4) * 8)), o++;
                  }
                return t.create(i, o);
              }
              e.enc.Base64url = {
                stringify: function (e, t = !0) {
                  var n = e.words,
                    r = e.sigBytes,
                    i = t ? this._safe_map : this._map;
                  e.clamp();
                  for (var o = [], a = 0; a < r; a += 3)
                    for (
                      var s =
                          (((n[a >>> 2] >>> (24 - (a % 4) * 8)) & 255) << 16) |
                          (((n[(a + 1) >>> 2] >>> (24 - ((a + 1) % 4) * 8)) &
                            255) <<
                            8) |
                          ((n[(a + 2) >>> 2] >>> (24 - ((a + 2) % 4) * 8)) &
                            255),
                        c = 0;
                      c < 4 && a + 0.75 * c < r;
                      c++
                    )
                      o.push(i.charAt((s >>> (6 * (3 - c))) & 63));
                  var u = i.charAt(64);
                  if (u) for (; o.length % 4; ) o.push(u);
                  return o.join('');
                },
                parse: function (e, t = !0) {
                  var r = e.length,
                    i = t ? this._safe_map : this._map,
                    o = this._reverseMap;
                  if (!o) {
                    o = this._reverseMap = [];
                    for (var a = 0; a < i.length; a++) o[i.charCodeAt(a)] = a;
                  }
                  var s = i.charAt(64);
                  if (s) {
                    var c = e.indexOf(s);
                    -1 !== c && (r = c);
                  }
                  return n(e, r, o);
                },
                _map: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
                _safe_map:
                  'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',
              };
            })(),
            r.enc.Base64url);
        },
        814: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            (function () {
              var e = r,
                t = e.lib.WordArray,
                n = e.enc;
              function i(e) {
                return ((e << 8) & 4278255360) | ((e >>> 8) & 16711935);
              }
              (n.Utf16 = n.Utf16BE =
                {
                  stringify: function (e) {
                    for (
                      var t = e.words, n = e.sigBytes, r = [], i = 0;
                      i < n;
                      i += 2
                    ) {
                      var o = (t[i >>> 2] >>> (16 - (i % 4) * 8)) & 65535;
                      r.push(String.fromCharCode(o));
                    }
                    return r.join('');
                  },
                  parse: function (e) {
                    for (var n = e.length, r = [], i = 0; i < n; i++)
                      r[i >>> 1] |= e.charCodeAt(i) << (16 - (i % 2) * 16);
                    return t.create(r, 2 * n);
                  },
                }),
                (n.Utf16LE = {
                  stringify: function (e) {
                    for (
                      var t = e.words, n = e.sigBytes, r = [], o = 0;
                      o < n;
                      o += 2
                    ) {
                      var a = i((t[o >>> 2] >>> (16 - (o % 4) * 8)) & 65535);
                      r.push(String.fromCharCode(a));
                    }
                    return r.join('');
                  },
                  parse: function (e) {
                    for (var n = e.length, r = [], o = 0; o < n; o++)
                      r[o >>> 1] |= i(e.charCodeAt(o) << (16 - (o % 2) * 16));
                    return t.create(r, 2 * n);
                  },
                });
            })(),
            r.enc.Utf16);
        },
        147: function (e, t, n) {
          var r, i, o, a, s, c, u, l;
          e.exports =
            ((l = n(751)),
            n(653),
            n(498),
            (i = (r = l).lib),
            (o = i.Base),
            (a = i.WordArray),
            (s = r.algo),
            (c = s.MD5),
            (u = s.EvpKDF =
              o.extend({
                cfg: o.extend({ keySize: 4, hasher: c, iterations: 1 }),
                init: function (e) {
                  this.cfg = this.cfg.extend(e);
                },
                compute: function (e, t) {
                  for (
                    var n,
                      r = this.cfg,
                      i = r.hasher.create(),
                      o = a.create(),
                      s = o.words,
                      c = r.keySize,
                      u = r.iterations;
                    s.length < c;

                  ) {
                    n && i.update(n), (n = i.update(e).finalize(t)), i.reset();
                    for (var l = 1; l < u; l++) (n = i.finalize(n)), i.reset();
                    o.concat(n);
                  }
                  return (o.sigBytes = 4 * c), o;
                },
              })),
            (r.EvpKDF = function (e, t, n) {
              return u.create(n).compute(e, t);
            }),
            l.EvpKDF);
        },
        573: function (e, t, n) {
          var r, i, o, a;
          e.exports =
            ((a = n(751)),
            n(575),
            (i = (r = a).lib.CipherParams),
            (o = r.enc.Hex),
            (r.format.Hex = {
              stringify: function (e) {
                return e.ciphertext.toString(o);
              },
              parse: function (e) {
                var t = o.parse(e);
                return i.create({ ciphertext: t });
              },
            }),
            a.format.Hex);
        },
        498: function (e, t, n) {
          var r, i, o, a;
          e.exports =
            ((r = n(751)),
            (o = (i = r).lib.Base),
            (a = i.enc.Utf8),
            void (i.algo.HMAC = o.extend({
              init: function (e, t) {
                (e = this._hasher = new e.init()),
                  'string' == typeof t && (t = a.parse(t));
                var n = e.blockSize,
                  r = 4 * n;
                t.sigBytes > r && (t = e.finalize(t)), t.clamp();
                for (
                  var i = (this._oKey = t.clone()),
                    o = (this._iKey = t.clone()),
                    s = i.words,
                    c = o.words,
                    u = 0;
                  u < n;
                  u++
                )
                  (s[u] ^= 1549556828), (c[u] ^= 909522486);
                (i.sigBytes = o.sigBytes = r), this.reset();
              },
              reset: function () {
                var e = this._hasher;
                e.reset(), e.update(this._iKey);
              },
              update: function (e) {
                return this._hasher.update(e), this;
              },
              finalize: function (e) {
                var t = this._hasher,
                  n = t.finalize(e);
                return t.reset(), t.finalize(this._oKey.clone().concat(n));
              },
            })));
        },
        406: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(235),
            n(654),
            n(814),
            n(20),
            n(320),
            n(716),
            n(653),
            n(846),
            n(438),
            n(28),
            n(450),
            n(159),
            n(945),
            n(498),
            n(121),
            n(147),
            n(575),
            n(81),
            n(107),
            n(769),
            n(191),
            n(946),
            n(969),
            n(311),
            n(970),
            n(243),
            n(239),
            n(573),
            n(192),
            n(627),
            n(579),
            n(339),
            n(251),
            r);
        },
        654: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            (function () {
              if ('function' == typeof ArrayBuffer) {
                var e = r.lib.WordArray,
                  t = e.init,
                  n = (e.init = function (e) {
                    if (
                      (e instanceof ArrayBuffer && (e = new Uint8Array(e)),
                      (e instanceof Int8Array ||
                        ('undefined' != typeof Uint8ClampedArray &&
                          e instanceof Uint8ClampedArray) ||
                        e instanceof Int16Array ||
                        e instanceof Uint16Array ||
                        e instanceof Int32Array ||
                        e instanceof Uint32Array ||
                        e instanceof Float32Array ||
                        e instanceof Float64Array) &&
                        (e = new Uint8Array(
                          e.buffer,
                          e.byteOffset,
                          e.byteLength
                        )),
                      e instanceof Uint8Array)
                    ) {
                      for (var n = e.byteLength, r = [], i = 0; i < n; i++)
                        r[i >>> 2] |= e[i] << (24 - (i % 4) * 8);
                      t.call(this, r, n);
                    } else t.apply(this, arguments);
                  });
                n.prototype = e;
              }
            })(),
            r.lib.WordArray);
        },
        716: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            (function (e) {
              var t = r,
                n = t.lib,
                i = n.WordArray,
                o = n.Hasher,
                a = t.algo,
                s = [];
              !(function () {
                for (var t = 0; t < 64; t++)
                  s[t] = (4294967296 * e.abs(e.sin(t + 1))) | 0;
              })();
              var c = (a.MD5 = o.extend({
                _doReset: function () {
                  this._hash = new i.init([
                    1732584193, 4023233417, 2562383102, 271733878,
                  ]);
                },
                _doProcessBlock: function (e, t) {
                  for (var n = 0; n < 16; n++) {
                    var r = t + n,
                      i = e[r];
                    e[r] =
                      (16711935 & ((i << 8) | (i >>> 24))) |
                      (4278255360 & ((i << 24) | (i >>> 8)));
                  }
                  var o = this._hash.words,
                    a = e[t + 0],
                    c = e[t + 1],
                    h = e[t + 2],
                    p = e[t + 3],
                    v = e[t + 4],
                    g = e[t + 5],
                    _ = e[t + 6],
                    E = e[t + 7],
                    y = e[t + 8],
                    T = e[t + 9],
                    m = e[t + 10],
                    w = e[t + 11],
                    b = e[t + 12],
                    I = e[t + 13],
                    S = e[t + 14],
                    k = e[t + 15],
                    N = o[0],
                    A = o[1],
                    D = o[2],
                    x = o[3];
                  (N = u(N, A, D, x, a, 7, s[0])),
                    (x = u(x, N, A, D, c, 12, s[1])),
                    (D = u(D, x, N, A, h, 17, s[2])),
                    (A = u(A, D, x, N, p, 22, s[3])),
                    (N = u(N, A, D, x, v, 7, s[4])),
                    (x = u(x, N, A, D, g, 12, s[5])),
                    (D = u(D, x, N, A, _, 17, s[6])),
                    (A = u(A, D, x, N, E, 22, s[7])),
                    (N = u(N, A, D, x, y, 7, s[8])),
                    (x = u(x, N, A, D, T, 12, s[9])),
                    (D = u(D, x, N, A, m, 17, s[10])),
                    (A = u(A, D, x, N, w, 22, s[11])),
                    (N = u(N, A, D, x, b, 7, s[12])),
                    (x = u(x, N, A, D, I, 12, s[13])),
                    (D = u(D, x, N, A, S, 17, s[14])),
                    (N = l(
                      N,
                      (A = u(A, D, x, N, k, 22, s[15])),
                      D,
                      x,
                      c,
                      5,
                      s[16]
                    )),
                    (x = l(x, N, A, D, _, 9, s[17])),
                    (D = l(D, x, N, A, w, 14, s[18])),
                    (A = l(A, D, x, N, a, 20, s[19])),
                    (N = l(N, A, D, x, g, 5, s[20])),
                    (x = l(x, N, A, D, m, 9, s[21])),
                    (D = l(D, x, N, A, k, 14, s[22])),
                    (A = l(A, D, x, N, v, 20, s[23])),
                    (N = l(N, A, D, x, T, 5, s[24])),
                    (x = l(x, N, A, D, S, 9, s[25])),
                    (D = l(D, x, N, A, p, 14, s[26])),
                    (A = l(A, D, x, N, y, 20, s[27])),
                    (N = l(N, A, D, x, I, 5, s[28])),
                    (x = l(x, N, A, D, h, 9, s[29])),
                    (D = l(D, x, N, A, E, 14, s[30])),
                    (N = f(
                      N,
                      (A = l(A, D, x, N, b, 20, s[31])),
                      D,
                      x,
                      g,
                      4,
                      s[32]
                    )),
                    (x = f(x, N, A, D, y, 11, s[33])),
                    (D = f(D, x, N, A, w, 16, s[34])),
                    (A = f(A, D, x, N, S, 23, s[35])),
                    (N = f(N, A, D, x, c, 4, s[36])),
                    (x = f(x, N, A, D, v, 11, s[37])),
                    (D = f(D, x, N, A, E, 16, s[38])),
                    (A = f(A, D, x, N, m, 23, s[39])),
                    (N = f(N, A, D, x, I, 4, s[40])),
                    (x = f(x, N, A, D, a, 11, s[41])),
                    (D = f(D, x, N, A, p, 16, s[42])),
                    (A = f(A, D, x, N, _, 23, s[43])),
                    (N = f(N, A, D, x, T, 4, s[44])),
                    (x = f(x, N, A, D, b, 11, s[45])),
                    (D = f(D, x, N, A, k, 16, s[46])),
                    (N = d(
                      N,
                      (A = f(A, D, x, N, h, 23, s[47])),
                      D,
                      x,
                      a,
                      6,
                      s[48]
                    )),
                    (x = d(x, N, A, D, E, 10, s[49])),
                    (D = d(D, x, N, A, S, 15, s[50])),
                    (A = d(A, D, x, N, g, 21, s[51])),
                    (N = d(N, A, D, x, b, 6, s[52])),
                    (x = d(x, N, A, D, p, 10, s[53])),
                    (D = d(D, x, N, A, m, 15, s[54])),
                    (A = d(A, D, x, N, c, 21, s[55])),
                    (N = d(N, A, D, x, y, 6, s[56])),
                    (x = d(x, N, A, D, k, 10, s[57])),
                    (D = d(D, x, N, A, _, 15, s[58])),
                    (A = d(A, D, x, N, I, 21, s[59])),
                    (N = d(N, A, D, x, v, 6, s[60])),
                    (x = d(x, N, A, D, w, 10, s[61])),
                    (D = d(D, x, N, A, h, 15, s[62])),
                    (A = d(A, D, x, N, T, 21, s[63])),
                    (o[0] = (o[0] + N) | 0),
                    (o[1] = (o[1] + A) | 0),
                    (o[2] = (o[2] + D) | 0),
                    (o[3] = (o[3] + x) | 0);
                },
                _doFinalize: function () {
                  var t = this._data,
                    n = t.words,
                    r = 8 * this._nDataBytes,
                    i = 8 * t.sigBytes;
                  n[i >>> 5] |= 128 << (24 - (i % 32));
                  var o = e.floor(r / 4294967296),
                    a = r;
                  (n[15 + (((i + 64) >>> 9) << 4)] =
                    (16711935 & ((o << 8) | (o >>> 24))) |
                    (4278255360 & ((o << 24) | (o >>> 8)))),
                    (n[14 + (((i + 64) >>> 9) << 4)] =
                      (16711935 & ((a << 8) | (a >>> 24))) |
                      (4278255360 & ((a << 24) | (a >>> 8)))),
                    (t.sigBytes = 4 * (n.length + 1)),
                    this._process();
                  for (var s = this._hash, c = s.words, u = 0; u < 4; u++) {
                    var l = c[u];
                    c[u] =
                      (16711935 & ((l << 8) | (l >>> 24))) |
                      (4278255360 & ((l << 24) | (l >>> 8)));
                  }
                  return s;
                },
                clone: function () {
                  var e = o.clone.call(this);
                  return (e._hash = this._hash.clone()), e;
                },
              }));
              function u(e, t, n, r, i, o, a) {
                var s = e + ((t & n) | (~t & r)) + i + a;
                return ((s << o) | (s >>> (32 - o))) + t;
              }
              function l(e, t, n, r, i, o, a) {
                var s = e + ((t & r) | (n & ~r)) + i + a;
                return ((s << o) | (s >>> (32 - o))) + t;
              }
              function f(e, t, n, r, i, o, a) {
                var s = e + (t ^ n ^ r) + i + a;
                return ((s << o) | (s >>> (32 - o))) + t;
              }
              function d(e, t, n, r, i, o, a) {
                var s = e + (n ^ (t | ~r)) + i + a;
                return ((s << o) | (s >>> (32 - o))) + t;
              }
              (t.MD5 = o._createHelper(c)),
                (t.HmacMD5 = o._createHmacHelper(c));
            })(Math),
            r.MD5);
        },
        81: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(575),
            (r.mode.CFB = (function () {
              var e = r.lib.BlockCipherMode.extend();
              function t(e, t, n, r) {
                var i,
                  o = this._iv;
                o
                  ? ((i = o.slice(0)), (this._iv = void 0))
                  : (i = this._prevBlock),
                  r.encryptBlock(i, 0);
                for (var a = 0; a < n; a++) e[t + a] ^= i[a];
              }
              return (
                (e.Encryptor = e.extend({
                  processBlock: function (e, n) {
                    var r = this._cipher,
                      i = r.blockSize;
                    t.call(this, e, n, i, r),
                      (this._prevBlock = e.slice(n, n + i));
                  },
                })),
                (e.Decryptor = e.extend({
                  processBlock: function (e, n) {
                    var r = this._cipher,
                      i = r.blockSize,
                      o = e.slice(n, n + i);
                    t.call(this, e, n, i, r), (this._prevBlock = o);
                  },
                })),
                e
              );
            })()),
            r.mode.CFB);
        },
        769: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(575),
            /** @preserve
             * Counter block mode compatible with  Dr Brian Gladman fileenc.c
             * derived from CryptoJS.mode.CTR
             * <NAME_EMAIL>
             */
            (r.mode.CTRGladman = (function () {
              var e = r.lib.BlockCipherMode.extend();
              function t(e) {
                if (255 == ((e >> 24) & 255)) {
                  var t = (e >> 16) & 255,
                    n = (e >> 8) & 255,
                    r = 255 & e;
                  255 === t
                    ? ((t = 0),
                      255 === n ? ((n = 0), 255 === r ? (r = 0) : ++r) : ++n)
                    : ++t,
                    (e = 0),
                    (e += t << 16),
                    (e += n << 8),
                    (e += r);
                } else e += 1 << 24;
                return e;
              }
              function n(e) {
                return 0 === (e[0] = t(e[0])) && (e[1] = t(e[1])), e;
              }
              var i = (e.Encryptor = e.extend({
                processBlock: function (e, t) {
                  var r = this._cipher,
                    i = r.blockSize,
                    o = this._iv,
                    a = this._counter;
                  o && ((a = this._counter = o.slice(0)), (this._iv = void 0)),
                    n(a);
                  var s = a.slice(0);
                  r.encryptBlock(s, 0);
                  for (var c = 0; c < i; c++) e[t + c] ^= s[c];
                },
              }));
              return (e.Decryptor = i), e;
            })()),
            r.mode.CTRGladman);
        },
        107: function (e, t, n) {
          var r, i, o;
          e.exports =
            ((o = n(751)),
            n(575),
            (o.mode.CTR =
              ((r = o.lib.BlockCipherMode.extend()),
              (i = r.Encryptor =
                r.extend({
                  processBlock: function (e, t) {
                    var n = this._cipher,
                      r = n.blockSize,
                      i = this._iv,
                      o = this._counter;
                    i &&
                      ((o = this._counter = i.slice(0)), (this._iv = void 0));
                    var a = o.slice(0);
                    n.encryptBlock(a, 0), (o[r - 1] = (o[r - 1] + 1) | 0);
                    for (var s = 0; s < r; s++) e[t + s] ^= a[s];
                  },
                })),
              (r.Decryptor = i),
              r)),
            o.mode.CTR);
        },
        946: function (e, t, n) {
          var r, i;
          e.exports =
            ((i = n(751)),
            n(575),
            (i.mode.ECB =
              (((r = i.lib.BlockCipherMode.extend()).Encryptor = r.extend({
                processBlock: function (e, t) {
                  this._cipher.encryptBlock(e, t);
                },
              })),
              (r.Decryptor = r.extend({
                processBlock: function (e, t) {
                  this._cipher.decryptBlock(e, t);
                },
              })),
              r)),
            i.mode.ECB);
        },
        191: function (e, t, n) {
          var r, i, o;
          e.exports =
            ((o = n(751)),
            n(575),
            (o.mode.OFB =
              ((r = o.lib.BlockCipherMode.extend()),
              (i = r.Encryptor =
                r.extend({
                  processBlock: function (e, t) {
                    var n = this._cipher,
                      r = n.blockSize,
                      i = this._iv,
                      o = this._keystream;
                    i &&
                      ((o = this._keystream = i.slice(0)), (this._iv = void 0)),
                      n.encryptBlock(o, 0);
                    for (var a = 0; a < r; a++) e[t + a] ^= o[a];
                  },
                })),
              (r.Decryptor = i),
              r)),
            o.mode.OFB);
        },
        969: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(575),
            (r.pad.AnsiX923 = {
              pad: function (e, t) {
                var n = e.sigBytes,
                  r = 4 * t,
                  i = r - (n % r),
                  o = n + i - 1;
                e.clamp(),
                  (e.words[o >>> 2] |= i << (24 - (o % 4) * 8)),
                  (e.sigBytes += i);
              },
              unpad: function (e) {
                var t = 255 & e.words[(e.sigBytes - 1) >>> 2];
                e.sigBytes -= t;
              },
            }),
            r.pad.Ansix923);
        },
        311: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(575),
            (r.pad.Iso10126 = {
              pad: function (e, t) {
                var n = 4 * t,
                  i = n - (e.sigBytes % n);
                e.concat(r.lib.WordArray.random(i - 1)).concat(
                  r.lib.WordArray.create([i << 24], 1)
                );
              },
              unpad: function (e) {
                var t = 255 & e.words[(e.sigBytes - 1) >>> 2];
                e.sigBytes -= t;
              },
            }),
            r.pad.Iso10126);
        },
        970: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(575),
            (r.pad.Iso97971 = {
              pad: function (e, t) {
                e.concat(r.lib.WordArray.create([2147483648], 1)),
                  r.pad.ZeroPadding.pad(e, t);
              },
              unpad: function (e) {
                r.pad.ZeroPadding.unpad(e), e.sigBytes--;
              },
            }),
            r.pad.Iso97971);
        },
        239: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(575),
            (r.pad.NoPadding = { pad: function () {}, unpad: function () {} }),
            r.pad.NoPadding);
        },
        243: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(575),
            (r.pad.ZeroPadding = {
              pad: function (e, t) {
                var n = 4 * t;
                e.clamp(), (e.sigBytes += n - (e.sigBytes % n || n));
              },
              unpad: function (e) {
                var t = e.words,
                  n = e.sigBytes - 1;
                for (n = e.sigBytes - 1; n >= 0; n--)
                  if ((t[n >>> 2] >>> (24 - (n % 4) * 8)) & 255) {
                    e.sigBytes = n + 1;
                    break;
                  }
              },
            }),
            r.pad.ZeroPadding);
        },
        121: function (e, t, n) {
          var r, i, o, a, s, c, u, l, f;
          e.exports =
            ((f = n(751)),
            n(653),
            n(498),
            (i = (r = f).lib),
            (o = i.Base),
            (a = i.WordArray),
            (s = r.algo),
            (c = s.SHA1),
            (u = s.HMAC),
            (l = s.PBKDF2 =
              o.extend({
                cfg: o.extend({ keySize: 4, hasher: c, iterations: 1 }),
                init: function (e) {
                  this.cfg = this.cfg.extend(e);
                },
                compute: function (e, t) {
                  for (
                    var n = this.cfg,
                      r = u.create(n.hasher, e),
                      i = a.create(),
                      o = a.create([1]),
                      s = i.words,
                      c = o.words,
                      l = n.keySize,
                      f = n.iterations;
                    s.length < l;

                  ) {
                    var d = r.update(t).finalize(o);
                    r.reset();
                    for (
                      var h = d.words, p = h.length, v = d, g = 1;
                      g < f;
                      g++
                    ) {
                      (v = r.finalize(v)), r.reset();
                      for (var _ = v.words, E = 0; E < p; E++) h[E] ^= _[E];
                    }
                    i.concat(d), c[0]++;
                  }
                  return (i.sigBytes = 4 * l), i;
                },
              })),
            (r.PBKDF2 = function (e, t, n) {
              return l.create(n).compute(e, t);
            }),
            f.PBKDF2);
        },
        251: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(20),
            n(716),
            n(147),
            n(575),
            (function () {
              var e = r,
                t = e.lib.StreamCipher,
                n = e.algo,
                i = [],
                o = [],
                a = [],
                s = (n.RabbitLegacy = t.extend({
                  _doReset: function () {
                    var e = this._key.words,
                      t = this.cfg.iv,
                      n = (this._X = [
                        e[0],
                        (e[3] << 16) | (e[2] >>> 16),
                        e[1],
                        (e[0] << 16) | (e[3] >>> 16),
                        e[2],
                        (e[1] << 16) | (e[0] >>> 16),
                        e[3],
                        (e[2] << 16) | (e[1] >>> 16),
                      ]),
                      r = (this._C = [
                        (e[2] << 16) | (e[2] >>> 16),
                        (4294901760 & e[0]) | (65535 & e[1]),
                        (e[3] << 16) | (e[3] >>> 16),
                        (4294901760 & e[1]) | (65535 & e[2]),
                        (e[0] << 16) | (e[0] >>> 16),
                        (4294901760 & e[2]) | (65535 & e[3]),
                        (e[1] << 16) | (e[1] >>> 16),
                        (4294901760 & e[3]) | (65535 & e[0]),
                      ]);
                    this._b = 0;
                    for (var i = 0; i < 4; i++) c.call(this);
                    for (i = 0; i < 8; i++) r[i] ^= n[(i + 4) & 7];
                    if (t) {
                      var o = t.words,
                        a = o[0],
                        s = o[1],
                        u =
                          (16711935 & ((a << 8) | (a >>> 24))) |
                          (4278255360 & ((a << 24) | (a >>> 8))),
                        l =
                          (16711935 & ((s << 8) | (s >>> 24))) |
                          (4278255360 & ((s << 24) | (s >>> 8))),
                        f = (u >>> 16) | (4294901760 & l),
                        d = (l << 16) | (65535 & u);
                      for (
                        r[0] ^= u,
                          r[1] ^= f,
                          r[2] ^= l,
                          r[3] ^= d,
                          r[4] ^= u,
                          r[5] ^= f,
                          r[6] ^= l,
                          r[7] ^= d,
                          i = 0;
                        i < 4;
                        i++
                      )
                        c.call(this);
                    }
                  },
                  _doProcessBlock: function (e, t) {
                    var n = this._X;
                    c.call(this),
                      (i[0] = n[0] ^ (n[5] >>> 16) ^ (n[3] << 16)),
                      (i[1] = n[2] ^ (n[7] >>> 16) ^ (n[5] << 16)),
                      (i[2] = n[4] ^ (n[1] >>> 16) ^ (n[7] << 16)),
                      (i[3] = n[6] ^ (n[3] >>> 16) ^ (n[1] << 16));
                    for (var r = 0; r < 4; r++)
                      (i[r] =
                        (16711935 & ((i[r] << 8) | (i[r] >>> 24))) |
                        (4278255360 & ((i[r] << 24) | (i[r] >>> 8)))),
                        (e[t + r] ^= i[r]);
                  },
                  blockSize: 4,
                  ivSize: 2,
                }));
              function c() {
                for (var e = this._X, t = this._C, n = 0; n < 8; n++)
                  o[n] = t[n];
                for (
                  t[0] = (t[0] + 1295307597 + this._b) | 0,
                    t[1] =
                      (t[1] + 3545052371 + (t[0] >>> 0 < o[0] >>> 0 ? 1 : 0)) |
                      0,
                    t[2] =
                      (t[2] + 886263092 + (t[1] >>> 0 < o[1] >>> 0 ? 1 : 0)) |
                      0,
                    t[3] =
                      (t[3] + 1295307597 + (t[2] >>> 0 < o[2] >>> 0 ? 1 : 0)) |
                      0,
                    t[4] =
                      (t[4] + 3545052371 + (t[3] >>> 0 < o[3] >>> 0 ? 1 : 0)) |
                      0,
                    t[5] =
                      (t[5] + 886263092 + (t[4] >>> 0 < o[4] >>> 0 ? 1 : 0)) |
                      0,
                    t[6] =
                      (t[6] + 1295307597 + (t[5] >>> 0 < o[5] >>> 0 ? 1 : 0)) |
                      0,
                    t[7] =
                      (t[7] + 3545052371 + (t[6] >>> 0 < o[6] >>> 0 ? 1 : 0)) |
                      0,
                    this._b = t[7] >>> 0 < o[7] >>> 0 ? 1 : 0,
                    n = 0;
                  n < 8;
                  n++
                ) {
                  var r = e[n] + t[n],
                    i = 65535 & r,
                    s = r >>> 16,
                    c = ((((i * i) >>> 17) + i * s) >>> 15) + s * s,
                    u = (((4294901760 & r) * r) | 0) + (((65535 & r) * r) | 0);
                  a[n] = c ^ u;
                }
                (e[0] =
                  (a[0] +
                    ((a[7] << 16) | (a[7] >>> 16)) +
                    ((a[6] << 16) | (a[6] >>> 16))) |
                  0),
                  (e[1] = (a[1] + ((a[0] << 8) | (a[0] >>> 24)) + a[7]) | 0),
                  (e[2] =
                    (a[2] +
                      ((a[1] << 16) | (a[1] >>> 16)) +
                      ((a[0] << 16) | (a[0] >>> 16))) |
                    0),
                  (e[3] = (a[3] + ((a[2] << 8) | (a[2] >>> 24)) + a[1]) | 0),
                  (e[4] =
                    (a[4] +
                      ((a[3] << 16) | (a[3] >>> 16)) +
                      ((a[2] << 16) | (a[2] >>> 16))) |
                    0),
                  (e[5] = (a[5] + ((a[4] << 8) | (a[4] >>> 24)) + a[3]) | 0),
                  (e[6] =
                    (a[6] +
                      ((a[5] << 16) | (a[5] >>> 16)) +
                      ((a[4] << 16) | (a[4] >>> 16))) |
                    0),
                  (e[7] = (a[7] + ((a[6] << 8) | (a[6] >>> 24)) + a[5]) | 0);
              }
              e.RabbitLegacy = t._createHelper(s);
            })(),
            r.RabbitLegacy);
        },
        339: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(20),
            n(716),
            n(147),
            n(575),
            (function () {
              var e = r,
                t = e.lib.StreamCipher,
                n = e.algo,
                i = [],
                o = [],
                a = [],
                s = (n.Rabbit = t.extend({
                  _doReset: function () {
                    for (
                      var e = this._key.words, t = this.cfg.iv, n = 0;
                      n < 4;
                      n++
                    )
                      e[n] =
                        (16711935 & ((e[n] << 8) | (e[n] >>> 24))) |
                        (4278255360 & ((e[n] << 24) | (e[n] >>> 8)));
                    var r = (this._X = [
                        e[0],
                        (e[3] << 16) | (e[2] >>> 16),
                        e[1],
                        (e[0] << 16) | (e[3] >>> 16),
                        e[2],
                        (e[1] << 16) | (e[0] >>> 16),
                        e[3],
                        (e[2] << 16) | (e[1] >>> 16),
                      ]),
                      i = (this._C = [
                        (e[2] << 16) | (e[2] >>> 16),
                        (4294901760 & e[0]) | (65535 & e[1]),
                        (e[3] << 16) | (e[3] >>> 16),
                        (4294901760 & e[1]) | (65535 & e[2]),
                        (e[0] << 16) | (e[0] >>> 16),
                        (4294901760 & e[2]) | (65535 & e[3]),
                        (e[1] << 16) | (e[1] >>> 16),
                        (4294901760 & e[3]) | (65535 & e[0]),
                      ]);
                    for (this._b = 0, n = 0; n < 4; n++) c.call(this);
                    for (n = 0; n < 8; n++) i[n] ^= r[(n + 4) & 7];
                    if (t) {
                      var o = t.words,
                        a = o[0],
                        s = o[1],
                        u =
                          (16711935 & ((a << 8) | (a >>> 24))) |
                          (4278255360 & ((a << 24) | (a >>> 8))),
                        l =
                          (16711935 & ((s << 8) | (s >>> 24))) |
                          (4278255360 & ((s << 24) | (s >>> 8))),
                        f = (u >>> 16) | (4294901760 & l),
                        d = (l << 16) | (65535 & u);
                      for (
                        i[0] ^= u,
                          i[1] ^= f,
                          i[2] ^= l,
                          i[3] ^= d,
                          i[4] ^= u,
                          i[5] ^= f,
                          i[6] ^= l,
                          i[7] ^= d,
                          n = 0;
                        n < 4;
                        n++
                      )
                        c.call(this);
                    }
                  },
                  _doProcessBlock: function (e, t) {
                    var n = this._X;
                    c.call(this),
                      (i[0] = n[0] ^ (n[5] >>> 16) ^ (n[3] << 16)),
                      (i[1] = n[2] ^ (n[7] >>> 16) ^ (n[5] << 16)),
                      (i[2] = n[4] ^ (n[1] >>> 16) ^ (n[7] << 16)),
                      (i[3] = n[6] ^ (n[3] >>> 16) ^ (n[1] << 16));
                    for (var r = 0; r < 4; r++)
                      (i[r] =
                        (16711935 & ((i[r] << 8) | (i[r] >>> 24))) |
                        (4278255360 & ((i[r] << 24) | (i[r] >>> 8)))),
                        (e[t + r] ^= i[r]);
                  },
                  blockSize: 4,
                  ivSize: 2,
                }));
              function c() {
                for (var e = this._X, t = this._C, n = 0; n < 8; n++)
                  o[n] = t[n];
                for (
                  t[0] = (t[0] + 1295307597 + this._b) | 0,
                    t[1] =
                      (t[1] + 3545052371 + (t[0] >>> 0 < o[0] >>> 0 ? 1 : 0)) |
                      0,
                    t[2] =
                      (t[2] + 886263092 + (t[1] >>> 0 < o[1] >>> 0 ? 1 : 0)) |
                      0,
                    t[3] =
                      (t[3] + 1295307597 + (t[2] >>> 0 < o[2] >>> 0 ? 1 : 0)) |
                      0,
                    t[4] =
                      (t[4] + 3545052371 + (t[3] >>> 0 < o[3] >>> 0 ? 1 : 0)) |
                      0,
                    t[5] =
                      (t[5] + 886263092 + (t[4] >>> 0 < o[4] >>> 0 ? 1 : 0)) |
                      0,
                    t[6] =
                      (t[6] + 1295307597 + (t[5] >>> 0 < o[5] >>> 0 ? 1 : 0)) |
                      0,
                    t[7] =
                      (t[7] + 3545052371 + (t[6] >>> 0 < o[6] >>> 0 ? 1 : 0)) |
                      0,
                    this._b = t[7] >>> 0 < o[7] >>> 0 ? 1 : 0,
                    n = 0;
                  n < 8;
                  n++
                ) {
                  var r = e[n] + t[n],
                    i = 65535 & r,
                    s = r >>> 16,
                    c = ((((i * i) >>> 17) + i * s) >>> 15) + s * s,
                    u = (((4294901760 & r) * r) | 0) + (((65535 & r) * r) | 0);
                  a[n] = c ^ u;
                }
                (e[0] =
                  (a[0] +
                    ((a[7] << 16) | (a[7] >>> 16)) +
                    ((a[6] << 16) | (a[6] >>> 16))) |
                  0),
                  (e[1] = (a[1] + ((a[0] << 8) | (a[0] >>> 24)) + a[7]) | 0),
                  (e[2] =
                    (a[2] +
                      ((a[1] << 16) | (a[1] >>> 16)) +
                      ((a[0] << 16) | (a[0] >>> 16))) |
                    0),
                  (e[3] = (a[3] + ((a[2] << 8) | (a[2] >>> 24)) + a[1]) | 0),
                  (e[4] =
                    (a[4] +
                      ((a[3] << 16) | (a[3] >>> 16)) +
                      ((a[2] << 16) | (a[2] >>> 16))) |
                    0),
                  (e[5] = (a[5] + ((a[4] << 8) | (a[4] >>> 24)) + a[3]) | 0),
                  (e[6] =
                    (a[6] +
                      ((a[5] << 16) | (a[5] >>> 16)) +
                      ((a[4] << 16) | (a[4] >>> 16))) |
                    0),
                  (e[7] = (a[7] + ((a[6] << 8) | (a[6] >>> 24)) + a[5]) | 0);
              }
              e.Rabbit = t._createHelper(s);
            })(),
            r.Rabbit);
        },
        579: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(20),
            n(716),
            n(147),
            n(575),
            (function () {
              var e = r,
                t = e.lib.StreamCipher,
                n = e.algo,
                i = (n.RC4 = t.extend({
                  _doReset: function () {
                    for (
                      var e = this._key,
                        t = e.words,
                        n = e.sigBytes,
                        r = (this._S = []),
                        i = 0;
                      i < 256;
                      i++
                    )
                      r[i] = i;
                    i = 0;
                    for (var o = 0; i < 256; i++) {
                      var a = i % n,
                        s = (t[a >>> 2] >>> (24 - (a % 4) * 8)) & 255;
                      o = (o + r[i] + s) % 256;
                      var c = r[i];
                      (r[i] = r[o]), (r[o] = c);
                    }
                    this._i = this._j = 0;
                  },
                  _doProcessBlock: function (e, t) {
                    e[t] ^= o.call(this);
                  },
                  keySize: 8,
                  ivSize: 0,
                }));
              function o() {
                for (
                  var e = this._S, t = this._i, n = this._j, r = 0, i = 0;
                  i < 4;
                  i++
                ) {
                  n = (n + e[(t = (t + 1) % 256)]) % 256;
                  var o = e[t];
                  (e[t] = e[n]),
                    (e[n] = o),
                    (r |= e[(e[t] + e[n]) % 256] << (24 - 8 * i));
                }
                return (this._i = t), (this._j = n), r;
              }
              e.RC4 = t._createHelper(i);
              var a = (n.RC4Drop = i.extend({
                cfg: i.cfg.extend({ drop: 192 }),
                _doReset: function () {
                  i._doReset.call(this);
                  for (var e = this.cfg.drop; e > 0; e--) o.call(this);
                },
              }));
              e.RC4Drop = t._createHelper(a);
            })(),
            r.RC4);
        },
        945: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            /** @preserve
  (c) 2012 by Cédric Mesnil. All rights reserved.
  	Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
  	    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
      - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
  	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  */
            (function (e) {
              var t = r,
                n = t.lib,
                i = n.WordArray,
                o = n.Hasher,
                a = t.algo,
                s = i.create([
                  0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 7, 4,
                  13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8, 3, 10, 14, 4,
                  9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12, 1, 9, 11, 10, 0, 8,
                  12, 4, 13, 3, 7, 15, 14, 5, 6, 2, 4, 0, 5, 9, 7, 12, 2, 10,
                  14, 1, 3, 8, 11, 6, 15, 13,
                ]),
                c = i.create([
                  5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12, 6, 11,
                  3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2, 15, 5, 1, 3, 7,
                  14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13, 8, 6, 4, 1, 3, 11, 15,
                  0, 5, 12, 2, 13, 9, 7, 10, 14, 12, 15, 10, 4, 1, 5, 8, 7, 6,
                  2, 13, 14, 0, 3, 9, 11,
                ]),
                u = i.create([
                  11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8, 7, 6,
                  8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12, 11, 13, 6,
                  7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5, 11, 12, 14, 15,
                  14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12, 9, 15, 5, 11, 6, 8,
                  13, 12, 5, 12, 13, 14, 11, 8, 5, 6,
                ]),
                l = i.create([
                  8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6, 9, 13,
                  15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11, 9, 7, 15, 11,
                  8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5, 15, 5, 8, 11, 14,
                  14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8, 8, 5, 12, 9, 12, 5, 14,
                  6, 8, 13, 6, 5, 15, 13, 11, 11,
                ]),
                f = i.create([
                  0, 1518500249, 1859775393, 2400959708, 2840853838,
                ]),
                d = i.create([
                  1352829926, 1548603684, 1836072691, 2053994217, 0,
                ]),
                h = (a.RIPEMD160 = o.extend({
                  _doReset: function () {
                    this._hash = i.create([
                      1732584193, 4023233417, 2562383102, 271733878, 3285377520,
                    ]);
                  },
                  _doProcessBlock: function (e, t) {
                    for (var n = 0; n < 16; n++) {
                      var r = t + n,
                        i = e[r];
                      e[r] =
                        (16711935 & ((i << 8) | (i >>> 24))) |
                        (4278255360 & ((i << 24) | (i >>> 8)));
                    }
                    var o,
                      a,
                      h,
                      T,
                      m,
                      w,
                      b,
                      I,
                      S,
                      k,
                      N,
                      A = this._hash.words,
                      D = f.words,
                      x = d.words,
                      B = s.words,
                      L = c.words,
                      W = u.words,
                      O = l.words;
                    for (
                      w = o = A[0],
                        b = a = A[1],
                        I = h = A[2],
                        S = T = A[3],
                        k = m = A[4],
                        n = 0;
                      n < 80;
                      n += 1
                    )
                      (N = (o + e[t + B[n]]) | 0),
                        (N +=
                          n < 16
                            ? p(a, h, T) + D[0]
                            : n < 32
                            ? v(a, h, T) + D[1]
                            : n < 48
                            ? g(a, h, T) + D[2]
                            : n < 64
                            ? _(a, h, T) + D[3]
                            : E(a, h, T) + D[4]),
                        (N = ((N = y((N |= 0), W[n])) + m) | 0),
                        (o = m),
                        (m = T),
                        (T = y(h, 10)),
                        (h = a),
                        (a = N),
                        (N = (w + e[t + L[n]]) | 0),
                        (N +=
                          n < 16
                            ? E(b, I, S) + x[0]
                            : n < 32
                            ? _(b, I, S) + x[1]
                            : n < 48
                            ? g(b, I, S) + x[2]
                            : n < 64
                            ? v(b, I, S) + x[3]
                            : p(b, I, S) + x[4]),
                        (N = ((N = y((N |= 0), O[n])) + k) | 0),
                        (w = k),
                        (k = S),
                        (S = y(I, 10)),
                        (I = b),
                        (b = N);
                    (N = (A[1] + h + S) | 0),
                      (A[1] = (A[2] + T + k) | 0),
                      (A[2] = (A[3] + m + w) | 0),
                      (A[3] = (A[4] + o + b) | 0),
                      (A[4] = (A[0] + a + I) | 0),
                      (A[0] = N);
                  },
                  _doFinalize: function () {
                    var e = this._data,
                      t = e.words,
                      n = 8 * this._nDataBytes,
                      r = 8 * e.sigBytes;
                    (t[r >>> 5] |= 128 << (24 - (r % 32))),
                      (t[14 + (((r + 64) >>> 9) << 4)] =
                        (16711935 & ((n << 8) | (n >>> 24))) |
                        (4278255360 & ((n << 24) | (n >>> 8)))),
                      (e.sigBytes = 4 * (t.length + 1)),
                      this._process();
                    for (var i = this._hash, o = i.words, a = 0; a < 5; a++) {
                      var s = o[a];
                      o[a] =
                        (16711935 & ((s << 8) | (s >>> 24))) |
                        (4278255360 & ((s << 24) | (s >>> 8)));
                    }
                    return i;
                  },
                  clone: function () {
                    var e = o.clone.call(this);
                    return (e._hash = this._hash.clone()), e;
                  },
                }));
              function p(e, t, n) {
                return e ^ t ^ n;
              }
              function v(e, t, n) {
                return (e & t) | (~e & n);
              }
              function g(e, t, n) {
                return (e | ~t) ^ n;
              }
              function _(e, t, n) {
                return (e & n) | (t & ~n);
              }
              function E(e, t, n) {
                return e ^ (t | ~n);
              }
              function y(e, t) {
                return (e << t) | (e >>> (32 - t));
              }
              (t.RIPEMD160 = o._createHelper(h)),
                (t.HmacRIPEMD160 = o._createHmacHelper(h));
            })(Math),
            r.RIPEMD160);
        },
        653: function (e, t, n) {
          var r, i, o, a, s, c, u, l;
          e.exports =
            ((l = n(751)),
            (i = (r = l).lib),
            (o = i.WordArray),
            (a = i.Hasher),
            (s = r.algo),
            (c = []),
            (u = s.SHA1 =
              a.extend({
                _doReset: function () {
                  this._hash = new o.init([
                    1732584193, 4023233417, 2562383102, 271733878, 3285377520,
                  ]);
                },
                _doProcessBlock: function (e, t) {
                  for (
                    var n = this._hash.words,
                      r = n[0],
                      i = n[1],
                      o = n[2],
                      a = n[3],
                      s = n[4],
                      u = 0;
                    u < 80;
                    u++
                  ) {
                    if (u < 16) c[u] = 0 | e[t + u];
                    else {
                      var l = c[u - 3] ^ c[u - 8] ^ c[u - 14] ^ c[u - 16];
                      c[u] = (l << 1) | (l >>> 31);
                    }
                    var f = ((r << 5) | (r >>> 27)) + s + c[u];
                    (f +=
                      u < 20
                        ? 1518500249 + ((i & o) | (~i & a))
                        : u < 40
                        ? 1859775393 + (i ^ o ^ a)
                        : u < 60
                        ? ((i & o) | (i & a) | (o & a)) - 1894007588
                        : (i ^ o ^ a) - 899497514),
                      (s = a),
                      (a = o),
                      (o = (i << 30) | (i >>> 2)),
                      (i = r),
                      (r = f);
                  }
                  (n[0] = (n[0] + r) | 0),
                    (n[1] = (n[1] + i) | 0),
                    (n[2] = (n[2] + o) | 0),
                    (n[3] = (n[3] + a) | 0),
                    (n[4] = (n[4] + s) | 0);
                },
                _doFinalize: function () {
                  var e = this._data,
                    t = e.words,
                    n = 8 * this._nDataBytes,
                    r = 8 * e.sigBytes;
                  return (
                    (t[r >>> 5] |= 128 << (24 - (r % 32))),
                    (t[14 + (((r + 64) >>> 9) << 4)] = Math.floor(
                      n / 4294967296
                    )),
                    (t[15 + (((r + 64) >>> 9) << 4)] = n),
                    (e.sigBytes = 4 * t.length),
                    this._process(),
                    this._hash
                  );
                },
                clone: function () {
                  var e = a.clone.call(this);
                  return (e._hash = this._hash.clone()), e;
                },
              })),
            (r.SHA1 = a._createHelper(u)),
            (r.HmacSHA1 = a._createHmacHelper(u)),
            l.SHA1);
        },
        438: function (e, t, n) {
          var r, i, o, a, s, c;
          e.exports =
            ((c = n(751)),
            n(846),
            (i = (r = c).lib.WordArray),
            (o = r.algo),
            (a = o.SHA256),
            (s = o.SHA224 =
              a.extend({
                _doReset: function () {
                  this._hash = new i.init([
                    3238371032, 914150663, 812702999, 4144912697, 4290775857,
                    1750603025, 1694076839, 3204075428,
                  ]);
                },
                _doFinalize: function () {
                  var e = a._doFinalize.call(this);
                  return (e.sigBytes -= 4), e;
                },
              })),
            (r.SHA224 = a._createHelper(s)),
            (r.HmacSHA224 = a._createHmacHelper(s)),
            c.SHA224);
        },
        846: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            (function (e) {
              var t = r,
                n = t.lib,
                i = n.WordArray,
                o = n.Hasher,
                a = t.algo,
                s = [],
                c = [];
              !(function () {
                function t(t) {
                  for (var n = e.sqrt(t), r = 2; r <= n; r++)
                    if (!(t % r)) return !1;
                  return !0;
                }
                function n(e) {
                  return (4294967296 * (e - (0 | e))) | 0;
                }
                for (var r = 2, i = 0; i < 64; )
                  t(r) &&
                    (i < 8 && (s[i] = n(e.pow(r, 0.5))),
                    (c[i] = n(e.pow(r, 1 / 3))),
                    i++),
                    r++;
              })();
              var u = [],
                l = (a.SHA256 = o.extend({
                  _doReset: function () {
                    this._hash = new i.init(s.slice(0));
                  },
                  _doProcessBlock: function (e, t) {
                    for (
                      var n = this._hash.words,
                        r = n[0],
                        i = n[1],
                        o = n[2],
                        a = n[3],
                        s = n[4],
                        l = n[5],
                        f = n[6],
                        d = n[7],
                        h = 0;
                      h < 64;
                      h++
                    ) {
                      if (h < 16) u[h] = 0 | e[t + h];
                      else {
                        var p = u[h - 15],
                          v =
                            ((p << 25) | (p >>> 7)) ^
                            ((p << 14) | (p >>> 18)) ^
                            (p >>> 3),
                          g = u[h - 2],
                          _ =
                            ((g << 15) | (g >>> 17)) ^
                            ((g << 13) | (g >>> 19)) ^
                            (g >>> 10);
                        u[h] = v + u[h - 7] + _ + u[h - 16];
                      }
                      var E = (r & i) ^ (r & o) ^ (i & o),
                        y =
                          ((r << 30) | (r >>> 2)) ^
                          ((r << 19) | (r >>> 13)) ^
                          ((r << 10) | (r >>> 22)),
                        T =
                          d +
                          (((s << 26) | (s >>> 6)) ^
                            ((s << 21) | (s >>> 11)) ^
                            ((s << 7) | (s >>> 25))) +
                          ((s & l) ^ (~s & f)) +
                          c[h] +
                          u[h];
                      (d = f),
                        (f = l),
                        (l = s),
                        (s = (a + T) | 0),
                        (a = o),
                        (o = i),
                        (i = r),
                        (r = (T + (y + E)) | 0);
                    }
                    (n[0] = (n[0] + r) | 0),
                      (n[1] = (n[1] + i) | 0),
                      (n[2] = (n[2] + o) | 0),
                      (n[3] = (n[3] + a) | 0),
                      (n[4] = (n[4] + s) | 0),
                      (n[5] = (n[5] + l) | 0),
                      (n[6] = (n[6] + f) | 0),
                      (n[7] = (n[7] + d) | 0);
                  },
                  _doFinalize: function () {
                    var t = this._data,
                      n = t.words,
                      r = 8 * this._nDataBytes,
                      i = 8 * t.sigBytes;
                    return (
                      (n[i >>> 5] |= 128 << (24 - (i % 32))),
                      (n[14 + (((i + 64) >>> 9) << 4)] = e.floor(
                        r / 4294967296
                      )),
                      (n[15 + (((i + 64) >>> 9) << 4)] = r),
                      (t.sigBytes = 4 * n.length),
                      this._process(),
                      this._hash
                    );
                  },
                  clone: function () {
                    var e = o.clone.call(this);
                    return (e._hash = this._hash.clone()), e;
                  },
                }));
              (t.SHA256 = o._createHelper(l)),
                (t.HmacSHA256 = o._createHmacHelper(l));
            })(Math),
            r.SHA256);
        },
        159: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(235),
            (function (e) {
              var t = r,
                n = t.lib,
                i = n.WordArray,
                o = n.Hasher,
                a = t.x64.Word,
                s = t.algo,
                c = [],
                u = [],
                l = [];
              !(function () {
                for (var e = 1, t = 0, n = 0; n < 24; n++) {
                  c[e + 5 * t] = (((n + 1) * (n + 2)) / 2) % 64;
                  var r = (2 * e + 3 * t) % 5;
                  (e = t % 5), (t = r);
                }
                for (e = 0; e < 5; e++)
                  for (t = 0; t < 5; t++)
                    u[e + 5 * t] = t + ((2 * e + 3 * t) % 5) * 5;
                for (var i = 1, o = 0; o < 24; o++) {
                  for (var s = 0, f = 0, d = 0; d < 7; d++) {
                    if (1 & i) {
                      var h = (1 << d) - 1;
                      h < 32 ? (f ^= 1 << h) : (s ^= 1 << (h - 32));
                    }
                    128 & i ? (i = (i << 1) ^ 113) : (i <<= 1);
                  }
                  l[o] = a.create(s, f);
                }
              })();
              var f = [];
              !(function () {
                for (var e = 0; e < 25; e++) f[e] = a.create();
              })();
              var d = (s.SHA3 = o.extend({
                cfg: o.cfg.extend({ outputLength: 512 }),
                _doReset: function () {
                  for (var e = (this._state = []), t = 0; t < 25; t++)
                    e[t] = new a.init();
                  this.blockSize = (1600 - 2 * this.cfg.outputLength) / 32;
                },
                _doProcessBlock: function (e, t) {
                  for (
                    var n = this._state, r = this.blockSize / 2, i = 0;
                    i < r;
                    i++
                  ) {
                    var o = e[t + 2 * i],
                      a = e[t + 2 * i + 1];
                    (o =
                      (16711935 & ((o << 8) | (o >>> 24))) |
                      (4278255360 & ((o << 24) | (o >>> 8)))),
                      (a =
                        (16711935 & ((a << 8) | (a >>> 24))) |
                        (4278255360 & ((a << 24) | (a >>> 8)))),
                      ((A = n[i]).high ^= a),
                      (A.low ^= o);
                  }
                  for (var s = 0; s < 24; s++) {
                    for (var d = 0; d < 5; d++) {
                      for (var h = 0, p = 0, v = 0; v < 5; v++)
                        (h ^= (A = n[d + 5 * v]).high), (p ^= A.low);
                      var g = f[d];
                      (g.high = h), (g.low = p);
                    }
                    for (d = 0; d < 5; d++) {
                      var _ = f[(d + 4) % 5],
                        E = f[(d + 1) % 5],
                        y = E.high,
                        T = E.low;
                      for (
                        h = _.high ^ ((y << 1) | (T >>> 31)),
                          p = _.low ^ ((T << 1) | (y >>> 31)),
                          v = 0;
                        v < 5;
                        v++
                      )
                        ((A = n[d + 5 * v]).high ^= h), (A.low ^= p);
                    }
                    for (var m = 1; m < 25; m++) {
                      var w = (A = n[m]).high,
                        b = A.low,
                        I = c[m];
                      I < 32
                        ? ((h = (w << I) | (b >>> (32 - I))),
                          (p = (b << I) | (w >>> (32 - I))))
                        : ((h = (b << (I - 32)) | (w >>> (64 - I))),
                          (p = (w << (I - 32)) | (b >>> (64 - I))));
                      var S = f[u[m]];
                      (S.high = h), (S.low = p);
                    }
                    var k = f[0],
                      N = n[0];
                    for (k.high = N.high, k.low = N.low, d = 0; d < 5; d++)
                      for (v = 0; v < 5; v++) {
                        var A = n[(m = d + 5 * v)],
                          D = f[m],
                          x = f[((d + 1) % 5) + 5 * v],
                          B = f[((d + 2) % 5) + 5 * v];
                        (A.high = D.high ^ (~x.high & B.high)),
                          (A.low = D.low ^ (~x.low & B.low));
                      }
                    A = n[0];
                    var L = l[s];
                    (A.high ^= L.high), (A.low ^= L.low);
                  }
                },
                _doFinalize: function () {
                  var t = this._data,
                    n = t.words,
                    r = (this._nDataBytes, 8 * t.sigBytes),
                    o = 32 * this.blockSize;
                  (n[r >>> 5] |= 1 << (24 - (r % 32))),
                    (n[((e.ceil((r + 1) / o) * o) >>> 5) - 1] |= 128),
                    (t.sigBytes = 4 * n.length),
                    this._process();
                  for (
                    var a = this._state,
                      s = this.cfg.outputLength / 8,
                      c = s / 8,
                      u = [],
                      l = 0;
                    l < c;
                    l++
                  ) {
                    var f = a[l],
                      d = f.high,
                      h = f.low;
                    (d =
                      (16711935 & ((d << 8) | (d >>> 24))) |
                      (4278255360 & ((d << 24) | (d >>> 8)))),
                      (h =
                        (16711935 & ((h << 8) | (h >>> 24))) |
                        (4278255360 & ((h << 24) | (h >>> 8)))),
                      u.push(h),
                      u.push(d);
                  }
                  return new i.init(u, s);
                },
                clone: function () {
                  for (
                    var e = o.clone.call(this),
                      t = (e._state = this._state.slice(0)),
                      n = 0;
                    n < 25;
                    n++
                  )
                    t[n] = t[n].clone();
                  return e;
                },
              }));
              (t.SHA3 = o._createHelper(d)),
                (t.HmacSHA3 = o._createHmacHelper(d));
            })(Math),
            r.SHA3);
        },
        450: function (e, t, n) {
          var r, i, o, a, s, c, u, l;
          e.exports =
            ((l = n(751)),
            n(235),
            n(28),
            (i = (r = l).x64),
            (o = i.Word),
            (a = i.WordArray),
            (s = r.algo),
            (c = s.SHA512),
            (u = s.SHA384 =
              c.extend({
                _doReset: function () {
                  this._hash = new a.init([
                    new o.init(3418070365, 3238371032),
                    new o.init(1654270250, 914150663),
                    new o.init(2438529370, 812702999),
                    new o.init(355462360, 4144912697),
                    new o.init(1731405415, 4290775857),
                    new o.init(2394180231, 1750603025),
                    new o.init(3675008525, 1694076839),
                    new o.init(1203062813, 3204075428),
                  ]);
                },
                _doFinalize: function () {
                  var e = c._doFinalize.call(this);
                  return (e.sigBytes -= 16), e;
                },
              })),
            (r.SHA384 = c._createHelper(u)),
            (r.HmacSHA384 = c._createHmacHelper(u)),
            l.SHA384);
        },
        28: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(235),
            (function () {
              var e = r,
                t = e.lib.Hasher,
                n = e.x64,
                i = n.Word,
                o = n.WordArray,
                a = e.algo;
              function s() {
                return i.create.apply(i, arguments);
              }
              var c = [
                  s(1116352408, 3609767458),
                  s(1899447441, 602891725),
                  s(3049323471, 3964484399),
                  s(3921009573, 2173295548),
                  s(961987163, 4081628472),
                  s(1508970993, 3053834265),
                  s(2453635748, 2937671579),
                  s(2870763221, 3664609560),
                  s(3624381080, 2734883394),
                  s(310598401, 1164996542),
                  s(607225278, 1323610764),
                  s(1426881987, 3590304994),
                  s(1925078388, 4068182383),
                  s(2162078206, 991336113),
                  s(2614888103, 633803317),
                  s(3248222580, 3479774868),
                  s(3835390401, 2666613458),
                  s(4022224774, 944711139),
                  s(264347078, 2341262773),
                  s(604807628, 2007800933),
                  s(770255983, 1495990901),
                  s(1249150122, 1856431235),
                  s(1555081692, 3175218132),
                  s(1996064986, 2198950837),
                  s(2554220882, 3999719339),
                  s(2821834349, 766784016),
                  s(2952996808, 2566594879),
                  s(3210313671, 3203337956),
                  s(3336571891, 1034457026),
                  s(3584528711, 2466948901),
                  s(113926993, 3758326383),
                  s(338241895, 168717936),
                  s(666307205, 1188179964),
                  s(773529912, 1546045734),
                  s(1294757372, 1522805485),
                  s(1396182291, 2643833823),
                  s(1695183700, 2343527390),
                  s(1986661051, 1014477480),
                  s(2177026350, 1206759142),
                  s(2456956037, 344077627),
                  s(2730485921, 1290863460),
                  s(2820302411, 3158454273),
                  s(3259730800, 3505952657),
                  s(3345764771, 106217008),
                  s(3516065817, 3606008344),
                  s(3600352804, 1432725776),
                  s(4094571909, 1467031594),
                  s(275423344, 851169720),
                  s(430227734, 3100823752),
                  s(506948616, 1363258195),
                  s(659060556, 3750685593),
                  s(883997877, 3785050280),
                  s(958139571, 3318307427),
                  s(1322822218, 3812723403),
                  s(1537002063, 2003034995),
                  s(1747873779, 3602036899),
                  s(1955562222, 1575990012),
                  s(2024104815, 1125592928),
                  s(2227730452, 2716904306),
                  s(2361852424, 442776044),
                  s(2428436474, 593698344),
                  s(2756734187, 3733110249),
                  s(3204031479, 2999351573),
                  s(3329325298, 3815920427),
                  s(3391569614, 3928383900),
                  s(3515267271, 566280711),
                  s(3940187606, 3454069534),
                  s(4118630271, 4000239992),
                  s(116418474, 1914138554),
                  s(174292421, 2731055270),
                  s(289380356, 3203993006),
                  s(460393269, 320620315),
                  s(685471733, 587496836),
                  s(852142971, 1086792851),
                  s(1017036298, 365543100),
                  s(1126000580, 2618297676),
                  s(1288033470, 3409855158),
                  s(1501505948, 4234509866),
                  s(1607167915, 987167468),
                  s(1816402316, 1246189591),
                ],
                u = [];
              !(function () {
                for (var e = 0; e < 80; e++) u[e] = s();
              })();
              var l = (a.SHA512 = t.extend({
                _doReset: function () {
                  this._hash = new o.init([
                    new i.init(1779033703, 4089235720),
                    new i.init(3144134277, 2227873595),
                    new i.init(1013904242, 4271175723),
                    new i.init(2773480762, 1595750129),
                    new i.init(1359893119, 2917565137),
                    new i.init(2600822924, 725511199),
                    new i.init(528734635, 4215389547),
                    new i.init(1541459225, 327033209),
                  ]);
                },
                _doProcessBlock: function (e, t) {
                  for (
                    var n = this._hash.words,
                      r = n[0],
                      i = n[1],
                      o = n[2],
                      a = n[3],
                      s = n[4],
                      l = n[5],
                      f = n[6],
                      d = n[7],
                      h = r.high,
                      p = r.low,
                      v = i.high,
                      g = i.low,
                      _ = o.high,
                      E = o.low,
                      y = a.high,
                      T = a.low,
                      m = s.high,
                      w = s.low,
                      b = l.high,
                      I = l.low,
                      S = f.high,
                      k = f.low,
                      N = d.high,
                      A = d.low,
                      D = h,
                      x = p,
                      B = v,
                      L = g,
                      W = _,
                      O = E,
                      M = y,
                      P = T,
                      G = m,
                      C = w,
                      H = b,
                      U = I,
                      R = S,
                      j = k,
                      z = N,
                      F = A,
                      X = 0;
                    X < 80;
                    X++
                  ) {
                    var Y,
                      K,
                      V = u[X];
                    if (X < 16)
                      (K = V.high = 0 | e[t + 2 * X]),
                        (Y = V.low = 0 | e[t + 2 * X + 1]);
                    else {
                      var q = u[X - 15],
                        J = q.high,
                        Z = q.low,
                        $ =
                          ((J >>> 1) | (Z << 31)) ^
                          ((J >>> 8) | (Z << 24)) ^
                          (J >>> 7),
                        Q =
                          ((Z >>> 1) | (J << 31)) ^
                          ((Z >>> 8) | (J << 24)) ^
                          ((Z >>> 7) | (J << 25)),
                        ee = u[X - 2],
                        te = ee.high,
                        ne = ee.low,
                        re =
                          ((te >>> 19) | (ne << 13)) ^
                          ((te << 3) | (ne >>> 29)) ^
                          (te >>> 6),
                        ie =
                          ((ne >>> 19) | (te << 13)) ^
                          ((ne << 3) | (te >>> 29)) ^
                          ((ne >>> 6) | (te << 26)),
                        oe = u[X - 7],
                        ae = oe.high,
                        se = oe.low,
                        ce = u[X - 16],
                        ue = ce.high,
                        le = ce.low;
                      (K =
                        (K =
                          (K =
                            $ + ae + ((Y = Q + se) >>> 0 < Q >>> 0 ? 1 : 0)) +
                          re +
                          ((Y += ie) >>> 0 < ie >>> 0 ? 1 : 0)) +
                        ue +
                        ((Y += le) >>> 0 < le >>> 0 ? 1 : 0)),
                        (V.high = K),
                        (V.low = Y);
                    }
                    var fe,
                      de = (G & H) ^ (~G & R),
                      he = (C & U) ^ (~C & j),
                      pe = (D & B) ^ (D & W) ^ (B & W),
                      ve = (x & L) ^ (x & O) ^ (L & O),
                      ge =
                        ((D >>> 28) | (x << 4)) ^
                        ((D << 30) | (x >>> 2)) ^
                        ((D << 25) | (x >>> 7)),
                      _e =
                        ((x >>> 28) | (D << 4)) ^
                        ((x << 30) | (D >>> 2)) ^
                        ((x << 25) | (D >>> 7)),
                      Ee =
                        ((G >>> 14) | (C << 18)) ^
                        ((G >>> 18) | (C << 14)) ^
                        ((G << 23) | (C >>> 9)),
                      ye =
                        ((C >>> 14) | (G << 18)) ^
                        ((C >>> 18) | (G << 14)) ^
                        ((C << 23) | (G >>> 9)),
                      Te = c[X],
                      me = Te.high,
                      we = Te.low,
                      be = z + Ee + ((fe = F + ye) >>> 0 < F >>> 0 ? 1 : 0),
                      Ie = _e + ve;
                    (z = R),
                      (F = j),
                      (R = H),
                      (j = U),
                      (H = G),
                      (U = C),
                      (G =
                        (M +
                          (be =
                            (be =
                              (be =
                                be +
                                de +
                                ((fe += he) >>> 0 < he >>> 0 ? 1 : 0)) +
                              me +
                              ((fe += we) >>> 0 < we >>> 0 ? 1 : 0)) +
                            K +
                            ((fe += Y) >>> 0 < Y >>> 0 ? 1 : 0)) +
                          ((C = (P + fe) | 0) >>> 0 < P >>> 0 ? 1 : 0)) |
                        0),
                      (M = W),
                      (P = O),
                      (W = B),
                      (O = L),
                      (B = D),
                      (L = x),
                      (D =
                        (be +
                          (ge + pe + (Ie >>> 0 < _e >>> 0 ? 1 : 0)) +
                          ((x = (fe + Ie) | 0) >>> 0 < fe >>> 0 ? 1 : 0)) |
                        0);
                  }
                  (p = r.low = p + x),
                    (r.high = h + D + (p >>> 0 < x >>> 0 ? 1 : 0)),
                    (g = i.low = g + L),
                    (i.high = v + B + (g >>> 0 < L >>> 0 ? 1 : 0)),
                    (E = o.low = E + O),
                    (o.high = _ + W + (E >>> 0 < O >>> 0 ? 1 : 0)),
                    (T = a.low = T + P),
                    (a.high = y + M + (T >>> 0 < P >>> 0 ? 1 : 0)),
                    (w = s.low = w + C),
                    (s.high = m + G + (w >>> 0 < C >>> 0 ? 1 : 0)),
                    (I = l.low = I + U),
                    (l.high = b + H + (I >>> 0 < U >>> 0 ? 1 : 0)),
                    (k = f.low = k + j),
                    (f.high = S + R + (k >>> 0 < j >>> 0 ? 1 : 0)),
                    (A = d.low = A + F),
                    (d.high = N + z + (A >>> 0 < F >>> 0 ? 1 : 0));
                },
                _doFinalize: function () {
                  var e = this._data,
                    t = e.words,
                    n = 8 * this._nDataBytes,
                    r = 8 * e.sigBytes;
                  return (
                    (t[r >>> 5] |= 128 << (24 - (r % 32))),
                    (t[30 + (((r + 128) >>> 10) << 5)] = Math.floor(
                      n / 4294967296
                    )),
                    (t[31 + (((r + 128) >>> 10) << 5)] = n),
                    (e.sigBytes = 4 * t.length),
                    this._process(),
                    this._hash.toX32()
                  );
                },
                clone: function () {
                  var e = t.clone.call(this);
                  return (e._hash = this._hash.clone()), e;
                },
                blockSize: 32,
              }));
              (e.SHA512 = t._createHelper(l)),
                (e.HmacSHA512 = t._createHmacHelper(l));
            })(),
            r.SHA512);
        },
        627: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            n(20),
            n(716),
            n(147),
            n(575),
            (function () {
              var e = r,
                t = e.lib,
                n = t.WordArray,
                i = t.BlockCipher,
                o = e.algo,
                a = [
                  57, 49, 41, 33, 25, 17, 9, 1, 58, 50, 42, 34, 26, 18, 10, 2,
                  59, 51, 43, 35, 27, 19, 11, 3, 60, 52, 44, 36, 63, 55, 47, 39,
                  31, 23, 15, 7, 62, 54, 46, 38, 30, 22, 14, 6, 61, 53, 45, 37,
                  29, 21, 13, 5, 28, 20, 12, 4,
                ],
                s = [
                  14, 17, 11, 24, 1, 5, 3, 28, 15, 6, 21, 10, 23, 19, 12, 4, 26,
                  8, 16, 7, 27, 20, 13, 2, 41, 52, 31, 37, 47, 55, 30, 40, 51,
                  45, 33, 48, 44, 49, 39, 56, 34, 53, 46, 42, 50, 36, 29, 32,
                ],
                c = [1, 2, 4, 6, 8, 10, 12, 14, 15, 17, 19, 21, 23, 25, 27, 28],
                u = [
                  {
                    0: 8421888,
                    268435456: 32768,
                    536870912: 8421378,
                    805306368: 2,
                    1073741824: 512,
                    1342177280: 8421890,
                    1610612736: 8389122,
                    1879048192: 8388608,
                    2147483648: 514,
                    2415919104: 8389120,
                    2684354560: 33280,
                    2952790016: 8421376,
                    3221225472: 32770,
                    3489660928: 8388610,
                    3758096384: 0,
                    4026531840: 33282,
                    134217728: 0,
                    402653184: 8421890,
                    671088640: 33282,
                    939524096: 32768,
                    1207959552: 8421888,
                    1476395008: 512,
                    1744830464: 8421378,
                    2013265920: 2,
                    2281701376: 8389120,
                    2550136832: 33280,
                    2818572288: 8421376,
                    3087007744: 8389122,
                    3355443200: 8388610,
                    3623878656: 32770,
                    3892314112: 514,
                    4160749568: 8388608,
                    1: 32768,
                    268435457: 2,
                    536870913: 8421888,
                    805306369: 8388608,
                    1073741825: 8421378,
                    1342177281: 33280,
                    1610612737: 512,
                    1879048193: 8389122,
                    2147483649: 8421890,
                    2415919105: 8421376,
                    2684354561: 8388610,
                    2952790017: 33282,
                    3221225473: 514,
                    3489660929: 8389120,
                    3758096385: 32770,
                    4026531841: 0,
                    134217729: 8421890,
                    402653185: 8421376,
                    671088641: 8388608,
                    939524097: 512,
                    1207959553: 32768,
                    1476395009: 8388610,
                    1744830465: 2,
                    2013265921: 33282,
                    2281701377: 32770,
                    2550136833: 8389122,
                    2818572289: 514,
                    3087007745: 8421888,
                    3355443201: 8389120,
                    3623878657: 0,
                    3892314113: 33280,
                    4160749569: 8421378,
                  },
                  {
                    0: 1074282512,
                    16777216: 16384,
                    33554432: 524288,
                    50331648: 1074266128,
                    67108864: 1073741840,
                    83886080: 1074282496,
                    100663296: 1073758208,
                    117440512: 16,
                    134217728: 540672,
                    150994944: 1073758224,
                    167772160: 1073741824,
                    184549376: 540688,
                    201326592: 524304,
                    218103808: 0,
                    234881024: 16400,
                    251658240: 1074266112,
                    8388608: 1073758208,
                    25165824: 540688,
                    41943040: 16,
                    58720256: 1073758224,
                    75497472: 1074282512,
                    92274688: 1073741824,
                    109051904: 524288,
                    125829120: 1074266128,
                    142606336: 524304,
                    159383552: 0,
                    176160768: 16384,
                    192937984: 1074266112,
                    209715200: 1073741840,
                    226492416: 540672,
                    243269632: 1074282496,
                    260046848: 16400,
                    268435456: 0,
                    285212672: 1074266128,
                    301989888: 1073758224,
                    318767104: 1074282496,
                    335544320: 1074266112,
                    352321536: 16,
                    369098752: 540688,
                    385875968: 16384,
                    402653184: 16400,
                    419430400: 524288,
                    436207616: 524304,
                    452984832: 1073741840,
                    469762048: 540672,
                    486539264: 1073758208,
                    503316480: 1073741824,
                    520093696: 1074282512,
                    276824064: 540688,
                    293601280: 524288,
                    310378496: 1074266112,
                    327155712: 16384,
                    343932928: 1073758208,
                    360710144: 1074282512,
                    377487360: 16,
                    394264576: 1073741824,
                    411041792: 1074282496,
                    427819008: 1073741840,
                    444596224: 1073758224,
                    461373440: 524304,
                    478150656: 0,
                    494927872: 16400,
                    511705088: 1074266128,
                    528482304: 540672,
                  },
                  {
                    0: 260,
                    1048576: 0,
                    2097152: 67109120,
                    3145728: 65796,
                    4194304: 65540,
                    5242880: 67108868,
                    6291456: 67174660,
                    7340032: 67174400,
                    8388608: 67108864,
                    9437184: 67174656,
                    10485760: 65792,
                    11534336: 67174404,
                    12582912: 67109124,
                    13631488: 65536,
                    14680064: 4,
                    15728640: 256,
                    524288: 67174656,
                    1572864: 67174404,
                    2621440: 0,
                    3670016: 67109120,
                    4718592: 67108868,
                    5767168: 65536,
                    6815744: 65540,
                    7864320: 260,
                    8912896: 4,
                    9961472: 256,
                    11010048: 67174400,
                    12058624: 65796,
                    13107200: 65792,
                    14155776: 67109124,
                    15204352: 67174660,
                    16252928: 67108864,
                    16777216: 67174656,
                    17825792: 65540,
                    18874368: 65536,
                    19922944: 67109120,
                    20971520: 256,
                    22020096: 67174660,
                    23068672: 67108868,
                    24117248: 0,
                    25165824: 67109124,
                    26214400: 67108864,
                    27262976: 4,
                    28311552: 65792,
                    29360128: 67174400,
                    30408704: 260,
                    31457280: 65796,
                    32505856: 67174404,
                    17301504: 67108864,
                    18350080: 260,
                    19398656: 67174656,
                    20447232: 0,
                    21495808: 65540,
                    22544384: 67109120,
                    23592960: 256,
                    24641536: 67174404,
                    25690112: 65536,
                    26738688: 67174660,
                    27787264: 65796,
                    28835840: 67108868,
                    29884416: 67109124,
                    30932992: 67174400,
                    31981568: 4,
                    33030144: 65792,
                  },
                  {
                    0: 2151682048,
                    65536: 2147487808,
                    131072: 4198464,
                    196608: 2151677952,
                    262144: 0,
                    327680: 4198400,
                    393216: 2147483712,
                    458752: 4194368,
                    524288: 2147483648,
                    589824: 4194304,
                    655360: 64,
                    720896: 2147487744,
                    786432: 2151678016,
                    851968: 4160,
                    917504: 4096,
                    983040: 2151682112,
                    32768: 2147487808,
                    98304: 64,
                    163840: 2151678016,
                    229376: 2147487744,
                    294912: 4198400,
                    360448: 2151682112,
                    425984: 0,
                    491520: 2151677952,
                    557056: 4096,
                    622592: 2151682048,
                    688128: 4194304,
                    753664: 4160,
                    819200: 2147483648,
                    884736: 4194368,
                    950272: 4198464,
                    1015808: 2147483712,
                    1048576: 4194368,
                    1114112: 4198400,
                    1179648: 2147483712,
                    1245184: 0,
                    1310720: 4160,
                    1376256: 2151678016,
                    1441792: 2151682048,
                    1507328: 2147487808,
                    1572864: 2151682112,
                    1638400: 2147483648,
                    1703936: 2151677952,
                    1769472: 4198464,
                    1835008: 2147487744,
                    1900544: 4194304,
                    1966080: 64,
                    2031616: 4096,
                    1081344: 2151677952,
                    1146880: 2151682112,
                    1212416: 0,
                    1277952: 4198400,
                    1343488: 4194368,
                    1409024: 2147483648,
                    1474560: 2147487808,
                    1540096: 64,
                    1605632: 2147483712,
                    1671168: 4096,
                    1736704: 2147487744,
                    1802240: 2151678016,
                    1867776: 4160,
                    1933312: 2151682048,
                    1998848: 4194304,
                    2064384: 4198464,
                  },
                  {
                    0: 128,
                    4096: 17039360,
                    8192: 262144,
                    12288: 536870912,
                    16384: 537133184,
                    20480: 16777344,
                    24576: 553648256,
                    28672: 262272,
                    32768: 16777216,
                    36864: 537133056,
                    40960: 536871040,
                    45056: 553910400,
                    49152: 553910272,
                    53248: 0,
                    57344: 17039488,
                    61440: 553648128,
                    2048: 17039488,
                    6144: 553648256,
                    10240: 128,
                    14336: 17039360,
                    18432: 262144,
                    22528: 537133184,
                    26624: 553910272,
                    30720: 536870912,
                    34816: 537133056,
                    38912: 0,
                    43008: 553910400,
                    47104: 16777344,
                    51200: 536871040,
                    55296: 553648128,
                    59392: 16777216,
                    63488: 262272,
                    65536: 262144,
                    69632: 128,
                    73728: 536870912,
                    77824: 553648256,
                    81920: 16777344,
                    86016: 553910272,
                    90112: 537133184,
                    94208: 16777216,
                    98304: 553910400,
                    102400: 553648128,
                    106496: 17039360,
                    110592: 537133056,
                    114688: 262272,
                    118784: 536871040,
                    122880: 0,
                    126976: 17039488,
                    67584: 553648256,
                    71680: 16777216,
                    75776: 17039360,
                    79872: 537133184,
                    83968: 536870912,
                    88064: 17039488,
                    92160: 128,
                    96256: 553910272,
                    100352: 262272,
                    104448: 553910400,
                    108544: 0,
                    112640: 553648128,
                    116736: 16777344,
                    120832: 262144,
                    124928: 537133056,
                    129024: 536871040,
                  },
                  {
                    0: 268435464,
                    256: 8192,
                    512: 270532608,
                    768: 270540808,
                    1024: 268443648,
                    1280: 2097152,
                    1536: 2097160,
                    1792: 268435456,
                    2048: 0,
                    2304: 268443656,
                    2560: 2105344,
                    2816: 8,
                    3072: 270532616,
                    3328: 2105352,
                    3584: 8200,
                    3840: 270540800,
                    128: 270532608,
                    384: 270540808,
                    640: 8,
                    896: 2097152,
                    1152: 2105352,
                    1408: 268435464,
                    1664: 268443648,
                    1920: 8200,
                    2176: 2097160,
                    2432: 8192,
                    2688: 268443656,
                    2944: 270532616,
                    3200: 0,
                    3456: 270540800,
                    3712: 2105344,
                    3968: 268435456,
                    4096: 268443648,
                    4352: 270532616,
                    4608: 270540808,
                    4864: 8200,
                    5120: 2097152,
                    5376: 268435456,
                    5632: 268435464,
                    5888: 2105344,
                    6144: 2105352,
                    6400: 0,
                    6656: 8,
                    6912: 270532608,
                    7168: 8192,
                    7424: 268443656,
                    7680: 270540800,
                    7936: 2097160,
                    4224: 8,
                    4480: 2105344,
                    4736: 2097152,
                    4992: 268435464,
                    5248: 268443648,
                    5504: 8200,
                    5760: 270540808,
                    6016: 270532608,
                    6272: 270540800,
                    6528: 270532616,
                    6784: 8192,
                    7040: 2105352,
                    7296: 2097160,
                    7552: 0,
                    7808: 268435456,
                    8064: 268443656,
                  },
                  {
                    0: 1048576,
                    16: 33555457,
                    32: 1024,
                    48: 1049601,
                    64: 34604033,
                    80: 0,
                    96: 1,
                    112: 34603009,
                    128: 33555456,
                    144: 1048577,
                    160: 33554433,
                    176: 34604032,
                    192: 34603008,
                    208: 1025,
                    224: 1049600,
                    240: 33554432,
                    8: 34603009,
                    24: 0,
                    40: 33555457,
                    56: 34604032,
                    72: 1048576,
                    88: 33554433,
                    104: 33554432,
                    120: 1025,
                    136: 1049601,
                    152: 33555456,
                    168: 34603008,
                    184: 1048577,
                    200: 1024,
                    216: 34604033,
                    232: 1,
                    248: 1049600,
                    256: 33554432,
                    272: 1048576,
                    288: 33555457,
                    304: 34603009,
                    320: 1048577,
                    336: 33555456,
                    352: 34604032,
                    368: 1049601,
                    384: 1025,
                    400: 34604033,
                    416: 1049600,
                    432: 1,
                    448: 0,
                    464: 34603008,
                    480: 33554433,
                    496: 1024,
                    264: 1049600,
                    280: 33555457,
                    296: 34603009,
                    312: 1,
                    328: 33554432,
                    344: 1048576,
                    360: 1025,
                    376: 34604032,
                    392: 33554433,
                    408: 34603008,
                    424: 0,
                    440: 34604033,
                    456: 1049601,
                    472: 1024,
                    488: 33555456,
                    504: 1048577,
                  },
                  {
                    0: 134219808,
                    1: 131072,
                    2: 134217728,
                    3: 32,
                    4: 131104,
                    5: 134350880,
                    6: 134350848,
                    7: 2048,
                    8: 134348800,
                    9: 134219776,
                    10: 133120,
                    11: 134348832,
                    12: 2080,
                    13: 0,
                    14: 134217760,
                    15: 133152,
                    2147483648: 2048,
                    2147483649: 134350880,
                    2147483650: 134219808,
                    2147483651: 134217728,
                    2147483652: 134348800,
                    2147483653: 133120,
                    2147483654: 133152,
                    2147483655: 32,
                    2147483656: 134217760,
                    2147483657: 2080,
                    2147483658: 131104,
                    2147483659: 134350848,
                    2147483660: 0,
                    2147483661: 134348832,
                    2147483662: 134219776,
                    2147483663: 131072,
                    16: 133152,
                    17: 134350848,
                    18: 32,
                    19: 2048,
                    20: 134219776,
                    21: 134217760,
                    22: 134348832,
                    23: 131072,
                    24: 0,
                    25: 131104,
                    26: 134348800,
                    27: 134219808,
                    28: 134350880,
                    29: 133120,
                    30: 2080,
                    31: 134217728,
                    2147483664: 131072,
                    2147483665: 2048,
                    2147483666: 134348832,
                    2147483667: 133152,
                    2147483668: 32,
                    2147483669: 134348800,
                    2147483670: 134217728,
                    2147483671: 134219808,
                    2147483672: 134350880,
                    2147483673: 134217760,
                    2147483674: 134219776,
                    2147483675: 0,
                    2147483676: 133120,
                    2147483677: 2080,
                    2147483678: 131104,
                    2147483679: 134350848,
                  },
                ],
                l = [
                  4160749569, 528482304, 33030144, 2064384, 129024, 8064, 504,
                  2147483679,
                ],
                f = (o.DES = i.extend({
                  _doReset: function () {
                    for (var e = this._key.words, t = [], n = 0; n < 56; n++) {
                      var r = a[n] - 1;
                      t[n] = (e[r >>> 5] >>> (31 - (r % 32))) & 1;
                    }
                    for (var i = (this._subKeys = []), o = 0; o < 16; o++) {
                      var u = (i[o] = []),
                        l = c[o];
                      for (n = 0; n < 24; n++)
                        (u[(n / 6) | 0] |=
                          t[(s[n] - 1 + l) % 28] << (31 - (n % 6))),
                          (u[4 + ((n / 6) | 0)] |=
                            t[28 + ((s[n + 24] - 1 + l) % 28)] <<
                            (31 - (n % 6)));
                      for (
                        u[0] = (u[0] << 1) | (u[0] >>> 31), n = 1;
                        n < 7;
                        n++
                      )
                        u[n] = u[n] >>> (4 * (n - 1) + 3);
                      u[7] = (u[7] << 5) | (u[7] >>> 27);
                    }
                    var f = (this._invSubKeys = []);
                    for (n = 0; n < 16; n++) f[n] = i[15 - n];
                  },
                  encryptBlock: function (e, t) {
                    this._doCryptBlock(e, t, this._subKeys);
                  },
                  decryptBlock: function (e, t) {
                    this._doCryptBlock(e, t, this._invSubKeys);
                  },
                  _doCryptBlock: function (e, t, n) {
                    (this._lBlock = e[t]),
                      (this._rBlock = e[t + 1]),
                      d.call(this, 4, 252645135),
                      d.call(this, 16, 65535),
                      h.call(this, 2, 858993459),
                      h.call(this, 8, 16711935),
                      d.call(this, 1, 1431655765);
                    for (var r = 0; r < 16; r++) {
                      for (
                        var i = n[r],
                          o = this._lBlock,
                          a = this._rBlock,
                          s = 0,
                          c = 0;
                        c < 8;
                        c++
                      )
                        s |= u[c][((a ^ i[c]) & l[c]) >>> 0];
                      (this._lBlock = a), (this._rBlock = o ^ s);
                    }
                    var f = this._lBlock;
                    (this._lBlock = this._rBlock),
                      (this._rBlock = f),
                      d.call(this, 1, 1431655765),
                      h.call(this, 8, 16711935),
                      h.call(this, 2, 858993459),
                      d.call(this, 16, 65535),
                      d.call(this, 4, 252645135),
                      (e[t] = this._lBlock),
                      (e[t + 1] = this._rBlock);
                  },
                  keySize: 2,
                  ivSize: 2,
                  blockSize: 2,
                }));
              function d(e, t) {
                var n = ((this._lBlock >>> e) ^ this._rBlock) & t;
                (this._rBlock ^= n), (this._lBlock ^= n << e);
              }
              function h(e, t) {
                var n = ((this._rBlock >>> e) ^ this._lBlock) & t;
                (this._lBlock ^= n), (this._rBlock ^= n << e);
              }
              e.DES = i._createHelper(f);
              var p = (o.TripleDES = i.extend({
                _doReset: function () {
                  var e = this._key.words;
                  if (2 !== e.length && 4 !== e.length && e.length < 6)
                    throw new Error(
                      'Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.'
                    );
                  var t = e.slice(0, 2),
                    r = e.length < 4 ? e.slice(0, 2) : e.slice(2, 4),
                    i = e.length < 6 ? e.slice(0, 2) : e.slice(4, 6);
                  (this._des1 = f.createEncryptor(n.create(t))),
                    (this._des2 = f.createEncryptor(n.create(r))),
                    (this._des3 = f.createEncryptor(n.create(i)));
                },
                encryptBlock: function (e, t) {
                  this._des1.encryptBlock(e, t),
                    this._des2.decryptBlock(e, t),
                    this._des3.encryptBlock(e, t);
                },
                decryptBlock: function (e, t) {
                  this._des3.decryptBlock(e, t),
                    this._des2.encryptBlock(e, t),
                    this._des1.decryptBlock(e, t);
                },
                keySize: 6,
                ivSize: 2,
                blockSize: 2,
              }));
              e.TripleDES = i._createHelper(p);
            })(),
            r.TripleDES);
        },
        235: function (e, t, n) {
          var r;
          e.exports =
            ((r = n(751)),
            (function (e) {
              var t = r,
                n = t.lib,
                i = n.Base,
                o = n.WordArray,
                a = (t.x64 = {});
              (a.Word = i.extend({
                init: function (e, t) {
                  (this.high = e), (this.low = t);
                },
              })),
                (a.WordArray = i.extend({
                  init: function (t, n) {
                    (t = this.words = t || []),
                      (this.sigBytes = n != e ? n : 8 * t.length);
                  },
                  toX32: function () {
                    for (
                      var e = this.words, t = e.length, n = [], r = 0;
                      r < t;
                      r++
                    ) {
                      var i = e[r];
                      n.push(i.high), n.push(i.low);
                    }
                    return o.create(n, this.sigBytes);
                  },
                  clone: function () {
                    for (
                      var e = i.clone.call(this),
                        t = (e.words = this.words.slice(0)),
                        n = t.length,
                        r = 0;
                      r < n;
                      r++
                    )
                      t[r] = t[r].clone();
                    return e;
                  },
                }));
            })(),
            r);
        },
        658: function (e, t, n) {
          'use strict';
          function r(e) {
            return (
              (r =
                'function' == typeof Symbol &&
                'symbol' == typeof Symbol.iterator
                  ? function (e) {
                      return typeof e;
                    }
                  : function (e) {
                      return e &&
                        'function' == typeof Symbol &&
                        e.constructor === Symbol &&
                        e !== Symbol.prototype
                        ? 'symbol'
                        : typeof e;
                    }),
              r(e)
            );
          }
          function i() {
            i = function () {
              return e;
            };
            var e = {},
              t = Object.prototype,
              n = t.hasOwnProperty,
              o =
                Object.defineProperty ||
                function (e, t, n) {
                  e[t] = n.value;
                },
              a = 'function' == typeof Symbol ? Symbol : {},
              s = a.iterator || '@@iterator',
              c = a.asyncIterator || '@@asyncIterator',
              u = a.toStringTag || '@@toStringTag';
            function l(e, t, n) {
              return (
                Object.defineProperty(e, t, {
                  value: n,
                  enumerable: !0,
                  configurable: !0,
                  writable: !0,
                }),
                e[t]
              );
            }
            try {
              l({}, '');
            } catch (e) {
              l = function (e, t, n) {
                return (e[t] = n);
              };
            }
            function f(e, t, n, r) {
              var i = t && t.prototype instanceof p ? t : p,
                a = Object.create(i.prototype),
                s = new N(r || []);
              return o(a, '_invoke', { value: b(e, n, s) }), a;
            }
            function d(e, t, n) {
              try {
                return { type: 'normal', arg: e.call(t, n) };
              } catch (e) {
                return { type: 'throw', arg: e };
              }
            }
            e.wrap = f;
            var h = {};
            function p() {}
            function v() {}
            function g() {}
            var _ = {};
            l(_, s, function () {
              return this;
            });
            var E = Object.getPrototypeOf,
              y = E && E(E(A([])));
            y && y !== t && n.call(y, s) && (_ = y);
            var T = (g.prototype = p.prototype = Object.create(_));
            function m(e) {
              ['next', 'throw', 'return'].forEach(function (t) {
                l(e, t, function (e) {
                  return this._invoke(t, e);
                });
              });
            }
            function w(e, t) {
              function i(o, a, s, c) {
                var u = d(e[o], e, a);
                if ('throw' !== u.type) {
                  var l = u.arg,
                    f = l.value;
                  return f && 'object' == r(f) && n.call(f, '__await')
                    ? t.resolve(f.__await).then(
                        function (e) {
                          i('next', e, s, c);
                        },
                        function (e) {
                          i('throw', e, s, c);
                        }
                      )
                    : t.resolve(f).then(
                        function (e) {
                          (l.value = e), s(l);
                        },
                        function (e) {
                          return i('throw', e, s, c);
                        }
                      );
                }
                c(u.arg);
              }
              var a;
              o(this, '_invoke', {
                value: function (e, n) {
                  function r() {
                    return new t(function (t, r) {
                      i(e, n, t, r);
                    });
                  }
                  return (a = a ? a.then(r, r) : r());
                },
              });
            }
            function b(e, t, n) {
              var r = 'suspendedStart';
              return function (i, o) {
                if ('executing' === r)
                  throw new Error('Generator is already running');
                if ('completed' === r) {
                  if ('throw' === i) throw o;
                  return { value: void 0, done: !0 };
                }
                for (n.method = i, n.arg = o; ; ) {
                  var a = n.delegate;
                  if (a) {
                    var s = I(a, n);
                    if (s) {
                      if (s === h) continue;
                      return s;
                    }
                  }
                  if ('next' === n.method) n.sent = n._sent = n.arg;
                  else if ('throw' === n.method) {
                    if ('suspendedStart' === r)
                      throw ((r = 'completed'), n.arg);
                    n.dispatchException(n.arg);
                  } else 'return' === n.method && n.abrupt('return', n.arg);
                  r = 'executing';
                  var c = d(e, t, n);
                  if ('normal' === c.type) {
                    if (
                      ((r = n.done ? 'completed' : 'suspendedYield'),
                      c.arg === h)
                    )
                      continue;
                    return { value: c.arg, done: n.done };
                  }
                  'throw' === c.type &&
                    ((r = 'completed'), (n.method = 'throw'), (n.arg = c.arg));
                }
              };
            }
            function I(e, t) {
              var n = t.method,
                r = e.iterator[n];
              if (void 0 === r)
                return (
                  (t.delegate = null),
                  ('throw' === n &&
                    e.iterator.return &&
                    ((t.method = 'return'),
                    (t.arg = void 0),
                    I(e, t),
                    'throw' === t.method)) ||
                    ('return' !== n &&
                      ((t.method = 'throw'),
                      (t.arg = new TypeError(
                        "The iterator does not provide a '" + n + "' method"
                      )))),
                  h
                );
              var i = d(r, e.iterator, t.arg);
              if ('throw' === i.type)
                return (
                  (t.method = 'throw'), (t.arg = i.arg), (t.delegate = null), h
                );
              var o = i.arg;
              return o
                ? o.done
                  ? ((t[e.resultName] = o.value),
                    (t.next = e.nextLoc),
                    'return' !== t.method &&
                      ((t.method = 'next'), (t.arg = void 0)),
                    (t.delegate = null),
                    h)
                  : o
                : ((t.method = 'throw'),
                  (t.arg = new TypeError('iterator result is not an object')),
                  (t.delegate = null),
                  h);
            }
            function S(e) {
              var t = { tryLoc: e[0] };
              1 in e && (t.catchLoc = e[1]),
                2 in e && ((t.finallyLoc = e[2]), (t.afterLoc = e[3])),
                this.tryEntries.push(t);
            }
            function k(e) {
              var t = e.completion || {};
              (t.type = 'normal'), delete t.arg, (e.completion = t);
            }
            function N(e) {
              (this.tryEntries = [{ tryLoc: 'root' }]),
                e.forEach(S, this),
                this.reset(!0);
            }
            function A(e) {
              if (e) {
                var t = e[s];
                if (t) return t.call(e);
                if ('function' == typeof e.next) return e;
                if (!isNaN(e.length)) {
                  var r = -1,
                    i = function t() {
                      for (; ++r < e.length; )
                        if (n.call(e, r))
                          return (t.value = e[r]), (t.done = !1), t;
                      return (t.value = void 0), (t.done = !0), t;
                    };
                  return (i.next = i);
                }
              }
              return { next: D };
            }
            function D() {
              return { value: void 0, done: !0 };
            }
            return (
              (v.prototype = g),
              o(T, 'constructor', { value: g, configurable: !0 }),
              o(g, 'constructor', { value: v, configurable: !0 }),
              (v.displayName = l(g, u, 'GeneratorFunction')),
              (e.isGeneratorFunction = function (e) {
                var t = 'function' == typeof e && e.constructor;
                return (
                  !!t &&
                  (t === v || 'GeneratorFunction' === (t.displayName || t.name))
                );
              }),
              (e.mark = function (e) {
                return (
                  Object.setPrototypeOf
                    ? Object.setPrototypeOf(e, g)
                    : ((e.__proto__ = g), l(e, u, 'GeneratorFunction')),
                  (e.prototype = Object.create(T)),
                  e
                );
              }),
              (e.awrap = function (e) {
                return { __await: e };
              }),
              m(w.prototype),
              l(w.prototype, c, function () {
                return this;
              }),
              (e.AsyncIterator = w),
              (e.async = function (t, n, r, i, o) {
                void 0 === o && (o = Promise);
                var a = new w(f(t, n, r, i), o);
                return e.isGeneratorFunction(n)
                  ? a
                  : a.next().then(function (e) {
                      return e.done ? e.value : a.next();
                    });
              }),
              m(T),
              l(T, u, 'Generator'),
              l(T, s, function () {
                return this;
              }),
              l(T, 'toString', function () {
                return '[object Generator]';
              }),
              (e.keys = function (e) {
                var t = Object(e),
                  n = [];
                for (var r in t) n.push(r);
                return (
                  n.reverse(),
                  function e() {
                    for (; n.length; ) {
                      var r = n.pop();
                      if (r in t) return (e.value = r), (e.done = !1), e;
                    }
                    return (e.done = !0), e;
                  }
                );
              }),
              (e.values = A),
              (N.prototype = {
                constructor: N,
                reset: function (e) {
                  if (
                    ((this.prev = 0),
                    (this.next = 0),
                    (this.sent = this._sent = void 0),
                    (this.done = !1),
                    (this.delegate = null),
                    (this.method = 'next'),
                    (this.arg = void 0),
                    this.tryEntries.forEach(k),
                    !e)
                  )
                    for (var t in this)
                      't' === t.charAt(0) &&
                        n.call(this, t) &&
                        !isNaN(+t.slice(1)) &&
                        (this[t] = void 0);
                },
                stop: function () {
                  this.done = !0;
                  var e = this.tryEntries[0].completion;
                  if ('throw' === e.type) throw e.arg;
                  return this.rval;
                },
                dispatchException: function (e) {
                  if (this.done) throw e;
                  var t = this;
                  function r(n, r) {
                    return (
                      (a.type = 'throw'),
                      (a.arg = e),
                      (t.next = n),
                      r && ((t.method = 'next'), (t.arg = void 0)),
                      !!r
                    );
                  }
                  for (var i = this.tryEntries.length - 1; i >= 0; --i) {
                    var o = this.tryEntries[i],
                      a = o.completion;
                    if ('root' === o.tryLoc) return r('end');
                    if (o.tryLoc <= this.prev) {
                      var s = n.call(o, 'catchLoc'),
                        c = n.call(o, 'finallyLoc');
                      if (s && c) {
                        if (this.prev < o.catchLoc) return r(o.catchLoc, !0);
                        if (this.prev < o.finallyLoc) return r(o.finallyLoc);
                      } else if (s) {
                        if (this.prev < o.catchLoc) return r(o.catchLoc, !0);
                      } else {
                        if (!c)
                          throw new Error(
                            'try statement without catch or finally'
                          );
                        if (this.prev < o.finallyLoc) return r(o.finallyLoc);
                      }
                    }
                  }
                },
                abrupt: function (e, t) {
                  for (var r = this.tryEntries.length - 1; r >= 0; --r) {
                    var i = this.tryEntries[r];
                    if (
                      i.tryLoc <= this.prev &&
                      n.call(i, 'finallyLoc') &&
                      this.prev < i.finallyLoc
                    ) {
                      var o = i;
                      break;
                    }
                  }
                  o &&
                    ('break' === e || 'continue' === e) &&
                    o.tryLoc <= t &&
                    t <= o.finallyLoc &&
                    (o = null);
                  var a = o ? o.completion : {};
                  return (
                    (a.type = e),
                    (a.arg = t),
                    o
                      ? ((this.method = 'next'), (this.next = o.finallyLoc), h)
                      : this.complete(a)
                  );
                },
                complete: function (e, t) {
                  if ('throw' === e.type) throw e.arg;
                  return (
                    'break' === e.type || 'continue' === e.type
                      ? (this.next = e.arg)
                      : 'return' === e.type
                      ? ((this.rval = this.arg = e.arg),
                        (this.method = 'return'),
                        (this.next = 'end'))
                      : 'normal' === e.type && t && (this.next = t),
                    h
                  );
                },
                finish: function (e) {
                  for (var t = this.tryEntries.length - 1; t >= 0; --t) {
                    var n = this.tryEntries[t];
                    if (n.finallyLoc === e)
                      return this.complete(n.completion, n.afterLoc), k(n), h;
                  }
                },
                catch: function (e) {
                  for (var t = this.tryEntries.length - 1; t >= 0; --t) {
                    var n = this.tryEntries[t];
                    if (n.tryLoc === e) {
                      var r = n.completion;
                      if ('throw' === r.type) {
                        var i = r.arg;
                        k(n);
                      }
                      return i;
                    }
                  }
                  throw new Error('illegal catch attempt');
                },
                delegateYield: function (e, t, n) {
                  return (
                    (this.delegate = {
                      iterator: A(e),
                      resultName: t,
                      nextLoc: n,
                    }),
                    'next' === this.method && (this.arg = void 0),
                    h
                  );
                },
              }),
              e
            );
          }
          function o(e, t, n, r, i, o, a) {
            try {
              var s = e[o](a),
                c = s.value;
            } catch (e) {
              return void n(e);
            }
            s.done ? t(c) : Promise.resolve(c).then(r, i);
          }
          function a(e) {
            return function () {
              var t = this,
                n = arguments;
              return new Promise(function (r, i) {
                var a = e.apply(t, n);
                function s(e) {
                  o(a, r, i, s, c, 'next', e);
                }
                function c(e) {
                  o(a, r, i, s, c, 'throw', e);
                }
                s(void 0);
              });
            };
          }
          function s(e, t) {
            for (var n = 0; n < t.length; n++) {
              var r = t[n];
              (r.enumerable = r.enumerable || !1),
                (r.configurable = !0),
                'value' in r && (r.writable = !0),
                Object.defineProperty(e, u(r.key), r);
            }
          }
          function c(e, t, n) {
            return (
              (t = u(t)) in e
                ? Object.defineProperty(e, t, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0,
                  })
                : (e[t] = n),
              e
            );
          }
          function u(e) {
            var t = (function (e, t) {
              if ('object' != r(e) || null === e) return e;
              var n = e[Symbol.toPrimitive];
              if (void 0 !== n) {
                var i = n.call(e, t);
                if ('object' != r(i)) return i;
                throw new TypeError(
                  '@@toPrimitive must return a primitive value.'
                );
              }
              return String(e);
            })(e, 'string');
            return 'symbol' == r(t) ? t : String(t);
          }
          n.r(t),
            n.d(t, {
              default: function () {
                return f;
              },
            });
          var l = new ((function (e, t, n) {
            return (
              t && s(e.prototype, t),
              n && s(e, n),
              Object.defineProperty(e, 'prototype', { writable: !1 }),
              e
            );
          })(function e() {
            var t = this;
            !(function (e, t) {
              if (!(e instanceof t))
                throw new TypeError('Cannot call a class as a function');
            })(this, e),
              c(this, 'init', function (e) {
                var n = e.url,
                  r = e.token,
                  i = e.refreshToken,
                  o = e.mount;
                return (
                  t.instance && t.instance.destroy(),
                  n &&
                    ((t.fileLink = n),
                    (t.iframe = t.initIframe(t.fileLink, o)),
                    t.initPostMessage(),
                    r && t.initToken(r),
                    i && (t.refreshTokenCallback = i)),
                  (t.instance = {
                    fileLink: n,
                    setToken: t.setToken,
                    destroy: t.destroy,
                    mount: t.iframe,
                    print: t.print,
                  }),
                  t.instance
                );
              }),
              c(this, 'destroy', function () {
                t.instance &&
                  (t.iframe.parentElement.removeChild(t.iframe),
                  (t.iframe = void 0),
                  t.root && document.body.removeChild(t.root),
                  window.removeEventListener('message', t.handleMessage),
                  (t.instance = void 0));
              }),
              c(this, 'initToken', function (e) {
                t.refreshToken(e);
              }),
              c(this, 'setToken', function (e) {
                t.refreshToken(e);
              }),
              c(this, 'refreshToken', function (e) {
                e.timeout &&
                  t.refreshTokenCallback &&
                  (clearTimeout(t.refreshTokenTimer),
                  (t.refreshTokenTimer = setTimeout(function () {
                    t.refreshTokenCallback().then(function (n) {
                      t.refreshToken({ token: n, timeout: e.timeout });
                    });
                  }, Math.max(e.timeout - 3e4, 3e4)))),
                  (t.token = e.token || e),
                  (document.cookie = 'x-user-token='
                    .concat(t.token, ';path=/;Domain=')
                    .concat(document.domain, ';Expires=')
                    .concat(new Date(Date.now() + e.timeout || 3e5))),
                  t.postMessage('token.refresh.back', t.token);
              }),
              c(this, 'initIframe', function (e, n) {
                var r = document.createElement('iframe');
                return (
                  (r.style.cssText =
                    'width:100%;height:100%;border: 0;display: block;'),
                  r.setAttribute('allowfullscreen', !0),
                  (r.src = e),
                  n
                    ? n.appendChild(r)
                    : ((t.root = document.createElement('div')),
                      t.root.classList.add('lite-preview-mount'),
                      (t.root.style.cssText = 'height: 100%;width: 100%;'),
                      document.body.appendChild(t.root),
                      t.root.appendChild(r)),
                  r
                );
              }),
              c(this, 'handleMessage', function (e) {
                if (t.fileLink.match(e.origin)) {
                  var n = e.data,
                    r = n.eventName,
                    i = n.data;
                  switch (r) {
                    case 'ready':
                      t.postMessage(
                        'performance',
                        JSON.stringify(window.performance.timing)
                      ),
                        t.token
                          ? t.postMessage('token.set', t.token)
                          : t.postMessage('token.undefined');
                      break;
                    case 'token.refresh':
                      t.refreshTokenCallback &&
                        t.refreshTokenCallback().then(function (e) {
                          t.postMessage('token.refresh.back', e);
                        });
                      break;
                    case 'print.response':
                      t.printResolve &&
                        (clearTimeout(t.printTimer),
                        delete t.printTimer,
                        i && t.printResolve(i),
                        delete t.printResolve);
                  }
                }
              }),
              c(this, 'initPostMessage', function () {
                window.addEventListener('message', t.handleMessage),
                  (t.postMessage = function (e, n) {
                    return t.iframe.contentWindow.postMessage(
                      { eventName: e, data: n },
                      '*'
                    );
                  });
              }),
              c(
                this,
                'print',
                a(
                  i().mark(function e() {
                    var n,
                      r = arguments;
                    return i().wrap(function (e) {
                      for (;;)
                        switch ((e.prev = e.next)) {
                          case 0:
                            if (
                              ((n =
                                r.length > 0 && void 0 !== r[0] ? r[0] : {}),
                              t.printTimer || t.printResolve)
                            ) {
                              e.next = 5;
                              break;
                            }
                            return e.abrupt(
                              'return',
                              new Promise(function (e, r) {
                                t.postMessage('print.request', n),
                                  clearTimeout(t.printTimer),
                                  (t.printTimer = setTimeout(function () {
                                    delete t.printResolve,
                                      delete t.printTimer,
                                      r('ERROR:导出接口超时');
                                  }, 6e5)),
                                  (t.printResolve = e);
                              })
                            );
                          case 5:
                            console.error(
                              'WARN:已存在未完成的导出任务，请稍后重试'
                            );
                          case 6:
                          case 'end':
                            return e.stop();
                        }
                    }, e);
                  })
                )
              );
          }))();
          window.litePreviewSDK = { config: l.init };
          var f = { config: l.init };
        },
        257: function (e, t, n) {
          'use strict';
          function r(e) {
            return (
              (r =
                'function' == typeof Symbol &&
                'symbol' == typeof Symbol.iterator
                  ? function (e) {
                      return typeof e;
                    }
                  : function (e) {
                      return e &&
                        'function' == typeof Symbol &&
                        e.constructor === Symbol &&
                        e !== Symbol.prototype
                        ? 'symbol'
                        : typeof e;
                    }),
              r(e)
            );
          }
          n.r(t),
            n.d(t, {
              config: function () {
                return le;
              },
            });
          var i = function () {
            return (i =
              Object.assign ||
              function (e) {
                for (var t, n = 1, r = arguments.length; n < r; n++)
                  for (var i in (t = arguments[n]))
                    Object.prototype.hasOwnProperty.call(t, i) && (e[i] = t[i]);
                return e;
              }).apply(this, arguments);
          };
          function o(e, t, n, r) {
            return new (n || (n = Promise))(function (i, o) {
              function a(e) {
                try {
                  c(r.next(e));
                } catch (e) {
                  o(e);
                }
              }
              function s(e) {
                try {
                  c(r.throw(e));
                } catch (e) {
                  o(e);
                }
              }
              function c(e) {
                var t;
                e.done
                  ? i(e.value)
                  : ((t = e.value),
                    t instanceof n
                      ? t
                      : new n(function (e) {
                          e(t);
                        })).then(a, s);
              }
              c((r = r.apply(e, t || [])).next());
            });
          }
          function a(e, t) {
            var n,
              r,
              i,
              o,
              a = {
                label: 0,
                sent: function () {
                  if (1 & i[0]) throw i[1];
                  return i[1];
                },
                trys: [],
                ops: [],
              };
            return (
              (o = { next: s(0), throw: s(1), return: s(2) }),
              'function' == typeof Symbol &&
                (o[Symbol.iterator] = function () {
                  return this;
                }),
              o
            );
            function s(o) {
              return function (s) {
                return (function (o) {
                  if (n) throw new TypeError('Generator is already executing.');
                  for (; a; )
                    try {
                      if (
                        ((n = 1),
                        r &&
                          (i =
                            2 & o[0]
                              ? r.return
                              : o[0]
                              ? r.throw || ((i = r.return) && i.call(r), 0)
                              : r.next) &&
                          !(i = i.call(r, o[1])).done)
                      )
                        return i;
                      switch (((r = 0), i && (o = [2 & o[0], i.value]), o[0])) {
                        case 0:
                        case 1:
                          i = o;
                          break;
                        case 4:
                          return a.label++, { value: o[1], done: !1 };
                        case 5:
                          a.label++, (r = o[1]), (o = [0]);
                          continue;
                        case 7:
                          (o = a.ops.pop()), a.trys.pop();
                          continue;
                        default:
                          if (
                            !(i = (i = a.trys).length > 0 && i[i.length - 1]) &&
                            (6 === o[0] || 2 === o[0])
                          ) {
                            a = 0;
                            continue;
                          }
                          if (
                            3 === o[0] &&
                            (!i || (o[1] > i[0] && o[1] < i[3]))
                          ) {
                            a.label = o[1];
                            break;
                          }
                          if (6 === o[0] && a.label < i[1]) {
                            (a.label = i[1]), (i = o);
                            break;
                          }
                          if (i && a.label < i[2]) {
                            (a.label = i[2]), a.ops.push(o);
                            break;
                          }
                          i[2] && a.ops.pop(), a.trys.pop();
                          continue;
                      }
                      o = t.call(e, a);
                    } catch (e) {
                      (o = [6, e]), (r = 0);
                    } finally {
                      n = i = 0;
                    }
                  if (5 & o[0]) throw o[1];
                  return { value: o[0] ? o[1] : void 0, done: !0 };
                })([o, s]);
              };
            }
          }
          var s = (function () {
            function e() {}
            return (
              (e.add = function (t) {
                e.HANDLE_LIST.push(t),
                  window.addEventListener('message', t, !1);
              }),
              (e.remove = function (t) {
                var n = e.HANDLE_LIST.indexOf(t);
                n >= 0 && e.HANDLE_LIST.splice(n, 1),
                  window.removeEventListener('message', t, !1);
              }),
              (e.empty = function () {
                for (; e.HANDLE_LIST.length; )
                  window.removeEventListener(
                    'message',
                    e.HANDLE_LIST.shift(),
                    !1
                  );
              }),
              (e.parse = function (e) {
                try {
                  return 'object' == r(e) ? e : e ? JSON.parse(e) : e;
                } catch (t) {
                  return console.log('Message.parse Error:', t), e;
                }
              }),
              (e.HANDLE_LIST = []),
              e
            );
          })();
          function c(e) {
            return '[object Function]' === {}.toString.call(e);
          }
          var u,
            l,
            f,
            d,
            h,
            p = { origin: '' };
          function v(e, t) {
            p[e] = t;
          }
          function g(e) {
            return p[e];
          }
          function _(e) {
            var t = g('origin');
            return (
              !!(function (e, t) {
                return (
                  e !== t &&
                  (e.replace(/www\./i, '').toLowerCase() !==
                    t.replace(/www\./i, '').toLowerCase() ||
                    (e.match('www.') ? void 0 : (v('origin', t), !1)))
                );
              })(t, e.origin) &&
              (console.warn('postMessage 域名检查不通过', {
                safeOrigin: t,
                eventOrigin: e.origin,
              }),
              !0)
            );
          }
          ((h = u || (u = {})).unknown = 'unknown'),
            (h.spreadsheet = 's'),
            (h.writer = 'w'),
            (h.presentation = 'p'),
            (h.pdf = 'f'),
            (function (e) {
              (e.wps = 'w'),
                (e.et = 's'),
                (e.presentation = 'p'),
                (e.pdf = 'f');
            })(l || (l = {})),
            (function (e) {
              (e.nomal = 'nomal'), (e.simple = 'simple');
            })(f || (f = {})),
            (function (e) {
              (e[(e.requestFullscreen = 1)] = 'requestFullscreen'),
                (e[(e.exitFullscreen = 0)] = 'exitFullscreen');
            })(d || (d = {}));
          var E,
            y,
            T,
            m =
              ((E = 0),
              function () {
                return (E += 1);
              }),
            w = function (e, t, n) {
              void 0 === n && (n = !0);
              var r = t;
              if (!y) {
                var i = function e(t) {
                  var n = t.clientHeight,
                    r = t.clientWidth;
                  0 !== n || 0 !== r || T
                    ? (0 === n && 0 === r) || !T || (T.disconnect(), (T = null))
                    : window.ResizeObserver &&
                      (T = new ResizeObserver(function (n) {
                        e(t);
                      })).observe(t),
                    (y.style.cssText +=
                      'height: ' + n + 'px; width: ' + r + 'px');
                }.bind(null, r);
                (y = document.createElement('iframe')).classList.add(
                  'web-office-iframe'
                );
                var o = {
                  id: 'office-iframe',
                  src: e,
                  scrolling: 'no',
                  frameborder: '0',
                  allowfullscreen: 'allowfullscreen',
                  webkitallowfullscreen: 'true',
                  mozallowfullscreen: 'true',
                  allow: 'clipboard-read; clipboard-write',
                };
                for (var a in (r
                  ? ((o.style =
                      'width: ' +
                      r.clientWidth +
                      'px; height: ' +
                      r.clientHeight +
                      'px;'),
                    n && window.addEventListener('resize', i))
                  : ((r = document.createElement('div')).classList.add(
                      'web-office-default-container'
                    ),
                    (function (e) {
                      var t = document.createElement('style');
                      document.head.appendChild(t);
                      var n = t.sheet;
                      n.insertRule(
                        '.web-office-default-container {position: absolute; padding: 0;  margin: 0; width: 100%; height: 100%; left: 0; top: 0;}',
                        n.cssRules.length
                      );
                    })(),
                    document.body.appendChild(r),
                    (o.style =
                      'position: fixed; top: 0; right: 0; bottom: 0; left: 0; width: 100%; height: 100%;')),
                o))
                  y.setAttribute(a, o[a]);
                r.appendChild(y),
                  (y.destroy = function () {
                    y.parentNode.removeChild(y),
                      (y = null),
                      window.removeEventListener('resize', i),
                      T && (T.disconnect(), (T = null));
                  });
              }
              return y;
            },
            b = function (e) {
              w().contentWindow &&
                w().contentWindow.postMessage(JSON.stringify(e), g('origin'));
            };
          function I(e, t, n) {
            return new Promise(function (r) {
              var i = m();
              s.add(function e(t) {
                if (!_(t)) {
                  var o = s.parse(t.data);
                  o.eventName === n &&
                    o.msgId === i &&
                    (r(o.data), s.remove(e));
                }
              }),
                b({ data: e, msgId: i, eventName: t });
            });
          }
          var S = function (e) {
              return I(e, 'wps.jssdk.api', 'wps.api.reply');
            },
            k = function (e) {
              return I(e, 'api.basic', 'api.basic.reply');
            },
            N = { idMap: {} };
          function A(e) {
            return o(this, void 0, void 0, function () {
              var t, n, r, i, o, c, u, l, f, d;
              return a(this, function (a) {
                switch (a.label) {
                  case 0:
                    return _(e)
                      ? [2]
                      : ((t = s.parse(e.data)),
                        (n = t.eventName),
                        (r = t.callbackId),
                        (i = t.data),
                        r && (o = N.idMap[r])
                          ? ((c = o.split(':')),
                            (u = c[0]),
                            (l = c[1]),
                            'api.callback' === n && N[u] && N[u][l]
                              ? [4, (d = N[u][l]).callback.apply(d, i.args)]
                              : [3, 2])
                          : [3, 2]);
                  case 1:
                    (f = a.sent()),
                      b({
                        result: f,
                        callbackId: r,
                        eventName: 'api.callback.reply',
                      }),
                      (a.label = 2);
                  case 2:
                    return [2];
                }
              });
            });
          }
          var D = function (e) {
              return o(void 0, void 0, void 0, function () {
                function t() {
                  return Object.keys(N.idMap).find(function (e) {
                    return N.idMap[e] === r + ':' + n;
                  });
                }
                var n, r, i, o, c, u, l, f, d;
                return a(this, function (a) {
                  switch (a.label) {
                    case 0:
                      return (
                        (n = e.prop),
                        (r = e.parentObjId),
                        [4, B([(i = e.value)])]
                      );
                    case 1:
                      return (
                        (o = a.sent()),
                        (c = o[0]),
                        (u = o[1]),
                        (e.value = c[0]),
                        (l = Object.keys(u)[0]),
                        (f = N[r]),
                        null === i &&
                          f &&
                          f[n] &&
                          ((d = t()) && delete N.idMap[d],
                          delete f[n],
                          Object.keys(f).length || delete N[r],
                          Object.keys(N.idMap).length || s.remove(A)),
                        l &&
                          (Object.keys(N.idMap).length || s.add(A),
                          N[r] || (N[r] = {}),
                          (N[r][n] = { callbackId: l, callback: u[l] }),
                          (d = t()) && delete N.idMap[d],
                          (N.idMap[l] = r + ':' + n)),
                        [2]
                      );
                  }
                });
              });
            },
            x = function (e, t, n, r) {
              return o(void 0, void 0, void 0, function () {
                var c, u, l, f, d, h, p, v;
                return a(this, function (g) {
                  switch (g.label) {
                    case 0:
                      return (
                        (c = m()),
                        (f = new Promise(function (e, t) {
                          (u = e), (l = t);
                        })),
                        (d = {}),
                        t.args ? [4, B(t.args)] : [3, 2]
                      );
                    case 1:
                      (h = g.sent()),
                        (p = h[0]),
                        (v = h[1]),
                        (t.args = p),
                        (d = v),
                        (g.label = 2);
                    case 2:
                      return 'api.setter' !== e ? [3, 4] : [4, D(t)];
                    case 3:
                      g.sent(), (g.label = 4);
                    case 4:
                      return (
                        (function (e) {
                          var t = e[0],
                            n = e[1];
                          'function' == typeof (t = i({}, t)).data &&
                            (t.data = t.data()),
                            n(),
                            b(t);
                        })([
                          { eventName: e, data: t, msgId: c },
                          function () {
                            var t = this;
                            return (
                              s.add(function i(f) {
                                return o(t, void 0, void 0, function () {
                                  var t, o, h;
                                  return a(this, function (a) {
                                    switch (a.label) {
                                      case 0:
                                        return _(f)
                                          ? [2]
                                          : 'api.callback' ===
                                              (t = s.parse(f.data)).eventName &&
                                            t.callbackId &&
                                            d[t.callbackId]
                                          ? [
                                              4,
                                              d[t.callbackId].apply(
                                                d,
                                                t.data.args
                                              ),
                                            ]
                                          : [3, 2];
                                      case 1:
                                        (o = a.sent()),
                                          b({
                                            result: o,
                                            eventName: 'api.callback.reply',
                                            callbackId: t.callbackId,
                                          }),
                                          (a.label = 2);
                                      case 2:
                                        return (
                                          t.eventName === e + '.reply' &&
                                            t.msgId === c &&
                                            (t.error
                                              ? (((h = new Error('')).stack =
                                                  t.error + '\n' + n),
                                                r && r(),
                                                l(h))
                                              : u(t.result),
                                            s.remove(i)),
                                          [2]
                                        );
                                    }
                                  });
                                });
                              }),
                              f
                            );
                          },
                        ]),
                        [2, f]
                      );
                  }
                });
              });
            };
          function B(e) {
            return o(this, void 0, void 0, function () {
              var t, n, r, i, o, s, c, u, l, f, d;
              return a(this, function (a) {
                switch (a.label) {
                  case 0:
                    (t = {}), (n = []), (r = e.slice(0)), (a.label = 1);
                  case 1:
                    return r.length ? ((i = void 0), [4, r.shift()]) : [3, 13];
                  case 2:
                    return (o = a.sent()) && o.done ? [4, o.done()] : [3, 4];
                  case 3:
                    a.sent(), (a.label = 4);
                  case 4:
                    if (
                      !(function (e) {
                        if (!e) return !1;
                        for (var t = e; null !== Object.getPrototypeOf(t); )
                          t = Object.getPrototypeOf(t);
                        return Object.getPrototypeOf(e) === t;
                      })(i)
                    )
                      return [3, 11];
                    for (c in ((i = {}), (s = []), o)) s.push(c);
                    (u = 0), (a.label = 5);
                  case 5:
                    return u < s.length
                      ? ((l = s[u]),
                        (f = o[l]),
                        /^[A-Z]/.test(l)
                          ? f && f.done
                            ? [4, f.done()]
                            : [3, 7]
                          : [3, 8])
                      : [3, 10];
                  case 6:
                    a.sent(), (a.label = 7);
                  case 7:
                    f && f.objId
                      ? (f = { objId: f.objId })
                      : 'function' == typeof f &&
                        ((d = m()), (t[d] = f), (f = { callbackId: d })),
                      (a.label = 8);
                  case 8:
                    (i[l] = f), (a.label = 9);
                  case 9:
                    return u++, [3, 5];
                  case 10:
                    return [3, 12];
                  case 11:
                    o && o.objId
                      ? (i = { objId: o.objId })
                      : 'function' == typeof o && void 0 === o.objId
                      ? ((d = m()), (t[d] = o), (i = { callbackId: d }))
                      : (i = o),
                      (a.label = 12);
                  case 12:
                    return n.push(i), [3, 1];
                  case 13:
                    return [2, [n, t]];
                }
              });
            });
          }
          var L = function (e, t) {
              void 0 === t && (t = !0);
              var n = i({}, e),
                o = n.headers,
                a = void 0 === o ? {} : o,
                s = n.subscriptions,
                c = void 0 === s ? {} : s,
                u = n.mode,
                l = void 0 === u ? f.nomal : u,
                d = n.commonOptions,
                h = a.backBtn,
                p = void 0 === h ? {} : h,
                v = a.shareBtn,
                g = void 0 === v ? {} : v,
                _ = a.otherMenuBtn,
                E = void 0 === _ ? {} : _,
                y = function (e, n) {
                  e.subscribe &&
                    'function' == typeof e.subscribe &&
                    ((e.callback = n),
                    (c[n] = e.subscribe),
                    t && delete e.subscribe);
                };
              if (
                (y(p, 'wpsconfig_back_btn'),
                y(g, 'wpsconfig_share_btn'),
                y(E, 'wpsconfig_other_menu_btn'),
                E.items && Array.isArray(E.items))
              ) {
                var T = [];
                E.items.forEach(function (e, t) {
                  switch ((void 0 === e && (e = {}), e.type)) {
                    case 'export_img':
                      (e.type = 1), (e.callback = 'export_img');
                      break;
                    case 'export_pdf':
                      (e.type = 1), (e.callback = 'export_pdf');
                      break;
                    case 'save_version':
                      (e.type = 1), (e.callback = 'save_version');
                      break;
                    case 'about_wps':
                      (e.type = 1), (e.callback = 'about_wps');
                      break;
                    case 'split_line':
                      e.type = 2;
                      break;
                    case 'custom':
                      (e.type = 3),
                        y(e, 'wpsconfig_other_menu_btn_' + t),
                        T.push(e);
                  }
                }),
                  T.length && (P || G) && (E.items = T);
              }
              n.url = n.url || n.wpsUrl;
              var m = [];
              if (
                ((l === f.simple || (d && !1 === d.isShowTopArea)) &&
                  m.push('simple', 'hidecmb'),
                n.debug && m.push('debugger'),
                n.url &&
                  m.length &&
                  (n.url =
                    n.url +
                    (n.url.indexOf('?') >= 0 ? '&' : '?') +
                    m.join('&')),
                d &&
                  (d.isParentFullscreen || d.isBrowserViewFullscreen) &&
                  (document.addEventListener('fullscreenchange', C),
                  document.addEventListener('webkitfullscreenchange', C),
                  document.addEventListener('mozfullscreenchange', C)),
                n.wordOptions && (n.wpsOptions = n.wordOptions),
                n.excelOptions && (n.etOptions = n.excelOptions),
                n.pptOptions && (n.wppOptions = n.pptOptions),
                'object' == r(c.print))
              ) {
                var w = 'wpsconfig_print';
                'function' == typeof c.print.subscribe &&
                  ((c[w] = c.print.subscribe),
                  (n.print = { callback: w }),
                  void 0 !== c.print.custom &&
                    (n.print.custom = c.print.custom)),
                  delete c.print;
              }
              return (
                'function' == typeof c.exportPdf &&
                  ((c[(w = 'wpsconfig_export_pdf')] = c.exportPdf),
                  (n.exportPdf = { callback: w }),
                  delete c.exportPdf),
                n.commandBars && O(n.commandBars, !1),
                i(i({}, n), { subscriptions: c })
              );
            },
            W = function (e) {
              void 0 === e && (e = '');
              var t = '';
              if (!t && e) {
                var n = e.toLowerCase();
                -1 !== n.indexOf('/office/s/') && (t = u.spreadsheet),
                  -1 !== n.indexOf('/office/w/') && (t = u.writer),
                  -1 !== n.indexOf('/office/p/') && (t = u.presentation),
                  -1 !== n.indexOf('/office/f/') && (t = u.pdf);
              }
              if (!t) {
                var r = e.match(/[\?&]type=([a-z]+)/) || [];
                t = l[r[1]] || '';
              }
              return t;
            };
          function O(e, t) {
            void 0 === t && (t = !0);
            var n = e.map(function (e) {
              var t = e.attributes;
              if (!Array.isArray(t)) {
                var n = [];
                for (var r in t)
                  if (t.hasOwnProperty(r)) {
                    var i = { name: r, value: t[r] };
                    n.push(i);
                  }
                e.attributes = n;
              }
              return e;
            });
            return t && b({ data: n, eventName: 'setCommandBars' }), n;
          }
          var M = window.navigator.userAgent.toLowerCase(),
            P = /Android|webOS|iPhone|iPod|BlackBerry|iPad/i.test(M),
            G = (function () {
              try {
                return (
                  -1 !==
                  window._parent.location.search.indexOf('from=wxminiprogram')
                );
              } catch (e) {
                return !1;
              }
            })();
          function C() {
            var e = { status: d.requestFullscreen },
              t = document,
              n =
                t.fullscreenElement ||
                t.webkitFullscreenElement ||
                t.mozFullScreenElement;
            (e.status = n ? d.requestFullscreen : d.exitFullscreen),
              b({ data: e, eventName: 'fullscreenchange' });
          }
          var H = function () {
            N.idMap = {};
          };
          function U() {
            console.group('JSSDK 事件机制调整说明'),
              console.warn(
                'jssdk.on、jssdk.off 和 jssdk.Application.Sub 将在后续版本中被弃用，建议使用改进后的 ApiEvent'
              ),
              console.warn(
                '具体请参考：https://wwo.wps.cn/docs/front-end/basic-usage/events/intro/'
              ),
              console.groupEnd();
          }
          var R = 0,
            j = new Set();
          function z(e) {
            return (
              (R += 1),
              !e &&
                (function (e) {
                  j.forEach(function (t) {
                    return t(e);
                  });
                })(R),
              R
            );
          }
          function F() {
            var e = new Error('');
            return (e.stack || e.message || '').split('\n').slice(2).join('\n');
          }
          function X(e, t) {
            var n,
              r = this,
              c = t.Events,
              l = t.Enum,
              f = t.Props,
              d = f[0],
              h = f[1],
              p = { objId: R };
            switch (
              ((function e(t, n, r) {
                for (
                  var o = n.slice(0),
                    a = function () {
                      var n = o.shift();
                      !n.alias &&
                        ~Y.indexOf(n.prop) &&
                        o.push(i(i({}, n), { alias: n.prop + 'Async' })),
                        Object.defineProperty(t, n.alias || n.prop, {
                          get: function () {
                            var o = this,
                              a = 1 === n.cache,
                              s = a && this['__' + n.prop + 'CacheValue'];
                            if (!s) {
                              var c = F(),
                                u = z(a),
                                l = function e() {
                                  for (
                                    var o, a = [], s = 0;
                                    s < arguments.length;
                                    s++
                                  )
                                    a[s] = arguments[s];
                                  return (
                                    void 0 !== n.caller
                                      ? (function e(t, n, r) {
                                          for (
                                            var o = n.slice(0),
                                              a = function () {
                                                var n = o.shift();
                                                !n.alias &&
                                                  ~Y.indexOf(n.prop) &&
                                                  o.push(
                                                    i(i({}, n), {
                                                      alias: n.prop + 'Async',
                                                    })
                                                  ),
                                                  Object.defineProperty(
                                                    t,
                                                    n.alias || n.prop,
                                                    {
                                                      get: function () {
                                                        var i = this,
                                                          o = 1 === n.cache,
                                                          a =
                                                            o &&
                                                            this[
                                                              '__' +
                                                                n.prop +
                                                                'CacheValue'
                                                            ];
                                                        if (!a) {
                                                          var s = F(),
                                                            c = z(o),
                                                            u = function i() {
                                                              for (
                                                                var o,
                                                                  a = [],
                                                                  c = 0;
                                                                c <
                                                                arguments.length;
                                                                c++
                                                              )
                                                                a[c] =
                                                                  arguments[c];
                                                              return (
                                                                void 0 !==
                                                                n.caller
                                                                  ? e(
                                                                      (o = {
                                                                        objId:
                                                                          z(),
                                                                      }),
                                                                      r[
                                                                        n.caller
                                                                      ],
                                                                      r
                                                                    )
                                                                  : (o = {}),
                                                                K(
                                                                  i,
                                                                  o,
                                                                  'api.caller',
                                                                  {
                                                                    obj: i,
                                                                    args: a,
                                                                    parentObjId:
                                                                      t.objId,
                                                                    objId:
                                                                      o.objId,
                                                                    prop: n.prop,
                                                                  },
                                                                  s
                                                                ),
                                                                o
                                                              );
                                                            };
                                                          return (
                                                            (u.objId = -1),
                                                            void 0 !==
                                                              n.getter &&
                                                              ((u.objId = c),
                                                              e(
                                                                u,
                                                                r[n.getter],
                                                                r
                                                              )),
                                                            K(
                                                              t,
                                                              u,
                                                              'api.getter',
                                                              {
                                                                parentObjId:
                                                                  t.objId,
                                                                objId: u.objId,
                                                                prop: n.prop,
                                                              },
                                                              s,
                                                              function () {
                                                                delete i[
                                                                  '__' +
                                                                    n.prop +
                                                                    'CacheValue'
                                                                ];
                                                              }
                                                            ),
                                                            o &&
                                                              (this[
                                                                '__' +
                                                                  n.prop +
                                                                  'CacheValue'
                                                              ] = u),
                                                            u
                                                          );
                                                        }
                                                        return a;
                                                      },
                                                      set: function (e) {
                                                        var r = F();
                                                        return K(
                                                          t,
                                                          {},
                                                          'api.setter',
                                                          {
                                                            value: e,
                                                            parentObjId:
                                                              t.objId,
                                                            objId: -1,
                                                            prop: n.prop,
                                                          },
                                                          r
                                                        );
                                                      },
                                                    }
                                                  );
                                              };
                                            o.length;

                                          )
                                            a();
                                        })((o = { objId: z() }), r[n.caller], r)
                                      : (o = {}),
                                    K(
                                      e,
                                      o,
                                      'api.caller',
                                      {
                                        obj: e,
                                        args: a,
                                        parentObjId: t.objId,
                                        objId: o.objId,
                                        prop: n.prop,
                                      },
                                      c
                                    ),
                                    o
                                  );
                                };
                              return (
                                (l.objId = -1),
                                void 0 !== n.getter &&
                                  ((l.objId = u), e(l, r[n.getter], r)),
                                K(
                                  t,
                                  l,
                                  'api.getter',
                                  {
                                    parentObjId: t.objId,
                                    objId: l.objId,
                                    prop: n.prop,
                                  },
                                  c,
                                  function () {
                                    delete o['__' + n.prop + 'CacheValue'];
                                  }
                                ),
                                a && (this['__' + n.prop + 'CacheValue'] = l),
                                l
                              );
                            }
                            return s;
                          },
                          set: function (e) {
                            var r = F();
                            return K(
                              t,
                              {},
                              'api.setter',
                              {
                                value: e,
                                parentObjId: t.objId,
                                objId: -1,
                                prop: n.prop,
                              },
                              r
                            );
                          },
                        });
                    };
                  o.length;

                )
                  a();
              })(p, d, h),
              (p.Events = c),
              (p.Enum = l),
              (e.Enum = p.Enum),
              (e.Events = p.Events),
              (e.Props = f),
              W(e.url))
            ) {
              case u.writer:
                e.WordApplication = e.WpsApplication = function () {
                  return p;
                };
                break;
              case u.spreadsheet:
                e.ExcelApplication = e.EtApplication = function () {
                  return p;
                };
                break;
              case u.presentation:
                e.PPTApplication = e.WppApplication = function () {
                  return p;
                };
                break;
              case u.pdf:
                e.PDFApplication = function () {
                  return p;
                };
            }
            (e.Application = p),
              (e.Free = function (e) {
                return x('api.free', { objId: e }, '');
              }),
              (e.Stack = p.Stack =
                ((n = function (t) {
                  e && e.Free(t);
                }),
                function () {
                  var e = [],
                    t = function (t) {
                      e.push(t);
                    };
                  return (
                    j.add(t),
                    {
                      End: function () {
                        n(e), j.delete(t);
                      },
                    }
                  );
                }));
            var v = {};
            s.add(function (e) {
              return o(r, void 0, void 0, function () {
                var t, n, r, i, o;
                return a(this, function (a) {
                  switch (a.label) {
                    case 0:
                      return _(e)
                        ? [2]
                        : 'api.event' === (t = s.parse(e.data)).eventName &&
                          t.data
                        ? ((n = t.data),
                          (r = n.eventName),
                          (i = n.data),
                          (o = v[r]) ? [4, o(i)] : [3, 2])
                        : [3, 2];
                    case 1:
                      a.sent(), (a.label = 2);
                    case 2:
                      return [2];
                  }
                });
              });
            }),
              (p.Sub = {});
            var g = function (e) {
              var t = c[e];
              Object.defineProperty(p.Sub, t, {
                set: function (e) {
                  U(),
                    (v[t] = e),
                    b({
                      eventName: 'api.event.register',
                      data: { eventName: t, register: !!e, objId: (R += 1) },
                    });
                },
              });
            };
            for (var E in c) g(E);
          }
          var Y = [
            'ExportAsFixedFormat',
            'GetOperatorsInfo',
            'ImportDataIntoFields',
            'ReplaceText',
            'ReplaceBookmark',
            'GetBookmarkText',
            'GetComments',
          ];
          function K(e, t, n, r, i, o) {
            var a,
              s = (e.done ? e.done() : Promise.resolve()).then(function () {
                return a || (a = x(n, r, i, o)), a;
              });
            (t.done = function () {
              return s;
            }),
              (t.then = function (e, n) {
                return r.objId >= 0
                  ? ((t.then = null),
                    (t.catch = null),
                    s
                      .then(function () {
                        e(t);
                      })
                      .catch(function (e) {
                        return n(e);
                      }))
                  : s.then(e, n);
              }),
              (t.catch = function (e) {
                return s.catch(e);
              }),
              (t.Destroy = function () {
                return x('api.free', { objId: t.objId }, '');
              });
          }
          var V = {},
            q = null,
            J = 'fileOpen',
            Z = 'fileSaved',
            $ = 'fileStatus',
            Q = 'fullscreenChange',
            ee = 'error',
            te = 'stage',
            ne = 'api.getToken',
            re = 'event.toast',
            ie = 'event.hyperLinkOpen',
            oe = 'api.getClipboardData';
          function ae(e, t, n, r, c, u, l) {
            var f = this;
            void 0 === n && (n = {}),
              s.add(function (d) {
                return o(f, void 0, void 0, function () {
                  var o, f, h, p, v, g, E, y, T, m, w, I, S, k, N, A, D, x, B;
                  return a(this, function (a) {
                    switch (a.label) {
                      case 0:
                        return _(d)
                          ? [2]
                          : ((o = s.parse(d.data)),
                            (f = o.eventName),
                            (h = void 0 === f ? '' : f),
                            (p = o.data),
                            (v = void 0 === p ? null : p),
                            (g = o.url),
                            (E = void 0 === g ? null : g),
                            -1 !== ['wps.jssdk.api'].indexOf(h)
                              ? [2]
                              : 'ready' !== h
                              ? [3, 1]
                              : (c.apiReadySended &&
                                  (function (e) {
                                    var t = [];
                                    Object.keys(V).forEach(function (n) {
                                      V[n].forEach(function (r) {
                                        var i = n;
                                        e.off(i, r),
                                          t.push({ handle: r, eventName: i });
                                      }),
                                        delete V[n];
                                    }),
                                      t.forEach(function (e) {
                                        var t = e.eventName,
                                          n = e.handle;
                                        null == q ||
                                          q.ApiEvent.AddApiEventListener(t, n);
                                      });
                                  })(t),
                                b({
                                  eventName: 'setConfig',
                                  data: i(i({}, n), { version: e.version }),
                                }),
                                e.tokenData &&
                                  e.setToken(
                                    i(i({}, e.tokenData), {
                                      hasRefreshTokenConfig: !!n.refreshToken,
                                    })
                                  ),
                                (e.iframeReady = !0),
                                [3, 15]));
                      case 1:
                        return 'error' !== h
                          ? [3, 2]
                          : (t.emit(ee, v), [3, 15]);
                      case 2:
                        return 'open.result' !== h
                          ? [3, 3]
                          : (void 0 !==
                              (null === (D = null == v ? void 0 : v.fileInfo) ||
                              void 0 === D
                                ? void 0
                                : D.officeVersion) &&
                              ((e.mainVersion = v.fileInfo.officeVersion),
                              console.log(
                                'WebOfficeSDK Main Version: V' + e.mainVersion
                              )),
                            t.emit(J, v),
                            [3, 15]);
                      case 3:
                        return 'api.scroll' !== h
                          ? [3, 4]
                          : (window.scrollTo(v.x, v.y), [3, 15]);
                      case 4:
                        if (h !== ne) return [3, 9];
                        (y = { token: !1 }), (a.label = 5);
                      case 5:
                        return a.trys.push([5, 7, , 8]), [4, c.refreshToken()];
                      case 6:
                        return (y = a.sent()), [3, 8];
                      case 7:
                        return (
                          (T = a.sent()),
                          console.error(
                            'refreshToken: ' + (T || 'fail to get')
                          ),
                          [3, 8]
                        );
                      case 8:
                        return (
                          b({ eventName: ne + '.reply', data: y }), [3, 15]
                        );
                      case 9:
                        if (h !== oe) return [3, 14];
                        (m = { text: '', html: '' }), (a.label = 10);
                      case 10:
                        return (
                          a.trys.push([10, 12, , 13]), [4, c.getClipboardData()]
                        );
                      case 11:
                        return (m = a.sent()), [3, 13];
                      case 12:
                        return (
                          (w = a.sent()),
                          console.error(
                            'getClipboardData: ' + (w || 'fail to get')
                          ),
                          [3, 13]
                        );
                      case 13:
                        return (
                          b({ eventName: oe + '.reply', data: m }), [3, 15]
                        );
                      case 14:
                        h === re
                          ? c.onToast(v)
                          : h === ie
                          ? c.onHyperLinkOpen(v)
                          : 'stage' === h
                          ? t.emit(te, v)
                          : 'event.callback' === h
                          ? ((I = v.eventName),
                            (S = v.data),
                            (k = I),
                            'fullScreenChange' === I && (k = Q),
                            'file.saved' === I && (k = $),
                            ((null === (x = n.commonOptions) || void 0 === x
                              ? void 0
                              : x.isBrowserViewFullscreen) ||
                              (null === (B = n.commonOptions) || void 0 === B
                                ? void 0
                                : B.isParentFullscreen)) &&
                              'fullscreenchange' === k &&
                              ((N = S.status),
                              (A = S.isDispatchEvent),
                              n.commonOptions.isBrowserViewFullscreen
                                ? (function (e, t, n, r) {
                                    0 === e
                                      ? (t.style =
                                          'position: static; width: ' +
                                          n.width +
                                          '; height: ' +
                                          n.height)
                                      : 1 === e &&
                                        (t.style =
                                          'position: absolute; width: 100%; height: 100%'),
                                      r &&
                                        (function (e) {
                                          [
                                            'fullscreen',
                                            'fullscreenElement',
                                          ].forEach(function (t) {
                                            Object.defineProperty(document, t, {
                                              get: function () {
                                                return !!e.status;
                                              },
                                              configurable: !0,
                                            });
                                          });
                                          var t = new CustomEvent(
                                            'fullscreenchange'
                                          );
                                          document.dispatchEvent(t);
                                        })({ status: e });
                                  })(N, u, l, A)
                                : n.commonOptions.isParentFullscreen &&
                                  (function (e, t, n) {
                                    var r = document.querySelector(n),
                                      i = r && 1 === r.nodeType ? r : t;
                                    if (0 === e) {
                                      var o = document;
                                      (
                                        o.exitFullscreen ||
                                        o.mozCancelFullScreen ||
                                        o.msExitFullscreen ||
                                        o.webkitCancelFullScreen ||
                                        o.webkitExitFullscreen
                                      ).call(document);
                                    } else if (1 === e) {
                                      (
                                        i.requestFullscreen ||
                                        i.mozRequestFullScreen ||
                                        i.msRequestFullscreen ||
                                        i.webkitRequestFullscreen
                                      ).call(i);
                                    }
                                  })(N, u, n.commonOptions.isParentFullscreen)),
                            t.emit(k, S))
                          : 'api.ready' === h && X(e, v),
                          (a.label = 15);
                      case 15:
                        return (
                          'function' == typeof r[h] && r[h](e, E || v), [2]
                        );
                    }
                  });
                });
              });
          }
          function se(e) {
            return new Promise(function (t) {
              s.add(function n(r) {
                _(r) || (s.parse(r.data).eventName === e && (t(), s.remove(n)));
              });
            });
          }
          function ce(e) {
            var t,
              n = this;
            void 0 === e && (e = {}), q && q.destroy();
            try {
              var r = L(e),
                i = r.subscriptions,
                u = void 0 === i ? {} : i,
                l = r.mount,
                f = void 0 === l ? null : l,
                d = r.url,
                h = r.refreshToken,
                p = r.onToast,
                g = r.onHyperLinkOpen,
                _ = r.getClipboardData;
              v('origin', (d.match(/https*:\/\/[^\/]+/g) || [])[0]);
              var E = w(d, f),
                y = se('ready'),
                T = se('open.result'),
                m = se('api.ready'),
                I = f
                  ? {
                      width: f.clientWidth + 'px',
                      height: f.clientHeight + 'px',
                    }
                  : { width: '100vw', height: '100vh' };
              delete r.mount, d && delete r.url, delete r.subscriptions;
              var N =
                  ((t = t || Object.create(null)),
                  {
                    on: function (e, n) {
                      (t[e] || (t[e] = [])).push(n);
                    },
                    off: function (e, n) {
                      t[e] && t[e].splice(t[e].indexOf(n) >>> 0, 1);
                    },
                    emit: function (e, n) {
                      (t[e] || []).slice().map(function (e) {
                        e(n);
                      }),
                        (t['*'] || []).slice().map(function (t) {
                          t(e, n);
                        });
                    },
                  }),
                A = { apiReadySended: !1 },
                D = function (e, t, r) {
                  return o(n, void 0, void 0, function () {
                    return a(this, function (n) {
                      switch (n.label) {
                        case 0:
                          return (function (e, t, n) {
                            if (V[e]) {
                              var r = !!V[e].find(function (e) {
                                return e === t;
                              });
                              return r && 'off' === n
                                ? (N.off(e, t),
                                  (V[e] = V[e].filter(function (e) {
                                    return e !== t;
                                  })),
                                  !!V[e].length || ((V[e] = void 0), !1))
                                : (r ||
                                    'on' !== n ||
                                    (V[e].push(t), N.on(e, t)),
                                  !0);
                            }
                            return 'on' === n
                              ? ((V[e] = []), V[e].push(t), !1)
                              : 'off' === n || void 0;
                          })(e, t, r)
                            ? [3, 2]
                            : [4, y];
                        case 1:
                          n.sent(),
                            (function (e, t) {
                              var n = e.eventName,
                                r = e.type,
                                i = e.handle;
                              'on' === t ? N.on(n, i) : N.off(n, i),
                                'base.event' === r &&
                                  b({
                                    eventName: 'basic.event',
                                    data: { eventName: n, action: t },
                                  }),
                                U();
                            })(
                              (function (e, t) {
                                var n = e,
                                  r = 'base.event';
                                switch (n) {
                                  case Z:
                                    console.warn(
                                      'fileSaved事件监听即将弃用， 推荐使用fileStatus进行文件状态的监听'
                                    ),
                                      (n = 'fileStatus');
                                    break;
                                  case Q:
                                    n = 'fullscreenchange';
                                    break;
                                  case 'error':
                                  case 'fileOpen':
                                    r = 'callback.event';
                                }
                                return { eventName: n, type: r, handle: t };
                              })(e, t),
                              r
                            ),
                            (n.label = 2);
                        case 2:
                          return [2];
                      }
                    });
                  });
                };
              return (
                (q = {
                  url: d,
                  iframe: E,
                  version: '1.1.19',
                  iframeReady: !1,
                  tokenData: null,
                  commandBars: null,
                  tabs: {
                    getTabs: function () {
                      return o(this, void 0, void 0, function () {
                        return a(this, function (e) {
                          switch (e.label) {
                            case 0:
                              return [4, y];
                            case 1:
                              return e.sent(), [2, k({ api: 'tab.getTabs' })];
                          }
                        });
                      });
                    },
                    switchTab: function (e) {
                      return o(this, void 0, void 0, function () {
                        return a(this, function (t) {
                          switch (t.label) {
                            case 0:
                              return [4, y];
                            case 1:
                              return (
                                t.sent(),
                                [
                                  2,
                                  k({
                                    api: 'tab.switchTab',
                                    args: { tabKey: e },
                                  }),
                                ]
                              );
                          }
                        });
                      });
                    },
                  },
                  setCooperUserColor: function (e) {
                    return o(this, void 0, void 0, function () {
                      return a(this, function (t) {
                        switch (t.label) {
                          case 0:
                            return [4, y];
                          case 1:
                            return (
                              t.sent(),
                              [2, k({ api: 'setCooperUserColor', args: e })]
                            );
                        }
                      });
                    });
                  },
                  setToken: function (e) {
                    return o(this, void 0, void 0, function () {
                      return a(this, function (t) {
                        switch (t.label) {
                          case 0:
                            return [4, y];
                          case 1:
                            return (
                              t.sent(),
                              (q.tokenData = e),
                              b({ eventName: 'setToken', data: e }),
                              [2]
                            );
                        }
                      });
                    });
                  },
                  ready: function () {
                    return o(this, void 0, void 0, function () {
                      return a(this, function (e) {
                        switch (e.label) {
                          case 0:
                            return A.apiReadySended ? [3, 2] : [4, T];
                          case 1:
                            e.sent(),
                              (A.apiReadySended = !0),
                              b({ eventName: 'api.ready' }),
                              (e.label = 2);
                          case 2:
                            return [4, m];
                          case 3:
                            return (
                              e.sent(),
                              [
                                2,
                                new Promise(function (e) {
                                  return setTimeout(function () {
                                    return e(
                                      null == q ? void 0 : q.Application
                                    );
                                  }, 0);
                                }),
                              ]
                            );
                        }
                      });
                    });
                  },
                  destroy: function () {
                    E.destroy(),
                      s.empty(),
                      (q = null),
                      (j = new Set()),
                      (R = 0),
                      document.removeEventListener('fullscreenchange', C),
                      H();
                  },
                  save: function () {
                    return o(this, void 0, void 0, function () {
                      return a(this, function (e) {
                        switch (e.label) {
                          case 0:
                            return [4, y];
                          case 1:
                            return e.sent(), [2, S({ api: 'save' })];
                        }
                      });
                    });
                  },
                  setCommandBars: function (e) {
                    return o(this, void 0, void 0, function () {
                      return a(this, function (t) {
                        switch (t.label) {
                          case 0:
                            return [4, y];
                          case 1:
                            return t.sent(), O(e), [2];
                        }
                      });
                    });
                  },
                  updateConfig: function (e) {
                    return (
                      void 0 === e && (e = {}),
                      o(this, void 0, void 0, function () {
                        return a(this, function (t) {
                          switch (t.label) {
                            case 0:
                              return [4, y];
                            case 1:
                              return (
                                t.sent(),
                                e.commandBars
                                  ? (console.warn(
                                      'Deprecated: `updateConfig()` 方法即将废弃，请使用`setCommandBars()`代替`updateConfig()`更新`commandBars`配置。'
                                    ),
                                    [4, O(e.commandBars)])
                                  : [3, 3]
                              );
                            case 2:
                              t.sent(), (t.label = 3);
                            case 3:
                              return [2];
                          }
                        });
                      })
                    );
                  },
                  executeCommandBar: function (e) {
                    return o(this, void 0, void 0, function () {
                      return a(this, function (t) {
                        switch (t.label) {
                          case 0:
                            return [4, y];
                          case 1:
                            return (
                              t.sent(),
                              O([
                                {
                                  cmbId: e,
                                  attributes: [{ name: 'click', value: !0 }],
                                },
                              ]),
                              [2]
                            );
                        }
                      });
                    });
                  },
                  on: function (e, t) {
                    return o(this, void 0, void 0, function () {
                      return a(this, function (n) {
                        return [2, this.ApiEvent.AddApiEventListener(e, t)];
                      });
                    });
                  },
                  off: function (e, t) {
                    return o(this, void 0, void 0, function () {
                      return a(this, function (n) {
                        return [2, this.ApiEvent.RemoveApiEventListener(e, t)];
                      });
                    });
                  },
                  ApiEvent: {
                    AddApiEventListener: function (e, t) {
                      return o(this, void 0, void 0, function () {
                        return a(this, function (n) {
                          switch (n.label) {
                            case 0:
                              return [4, D(e, t, 'on')];
                            case 1:
                              return [2, n.sent()];
                          }
                        });
                      });
                    },
                    RemoveApiEventListener: function (e, t) {
                      return o(this, void 0, void 0, function () {
                        return a(this, function (n) {
                          switch (n.label) {
                            case 0:
                              return [4, D(e, t, 'off')];
                            case 1:
                              return [2, n.sent()];
                          }
                        });
                      });
                    },
                  },
                }),
                (function (e, t, n, r, i, o) {
                  t &&
                    c(t) &&
                    ((i.refreshToken = t),
                    (e.refreshToken = { eventName: ne })),
                    o &&
                      c(o) &&
                      ((i.getClipboardData = o),
                      (e.getClipboardData = { eventName: oe })),
                    n &&
                      c(n) &&
                      ((i.onToast = n), (e.onToast = { eventName: re })),
                    r &&
                      c(r) &&
                      ((i.onHyperLinkOpen = r),
                      (e.onHyperLinkOpen = { eventName: ie }));
                })(r, h, p, g, A, _),
                ae(q, N, r, u, A, E, I),
                q
              );
            } catch (e) {
              console.error(e);
            }
          }
          console.log('WebOfficeSDK JS-SDK V1.1.19');
          var ue = Object.freeze({ __proto__: null, listener: ae, config: ce });
          window.WPS = ue;
          var le = ce;
          t.default = { config: ce };
        },
        620: function (e, t, n) {
          'use strict';
          var r =
              (this && this.__awaiter) ||
              function (e, t, n, r) {
                return new (n || (n = Promise))(function (i, o) {
                  function a(e) {
                    try {
                      c(r.next(e));
                    } catch (e) {
                      o(e);
                    }
                  }
                  function s(e) {
                    try {
                      c(r.throw(e));
                    } catch (e) {
                      o(e);
                    }
                  }
                  function c(e) {
                    var t;
                    e.done
                      ? i(e.value)
                      : ((t = e.value),
                        t instanceof n
                          ? t
                          : new n(function (e) {
                              e(t);
                            })).then(a, s);
                  }
                  c((r = r.apply(e, t || [])).next());
                });
              },
            i =
              (this && this.__generator) ||
              function (e, t) {
                var n,
                  r,
                  i,
                  o,
                  a = {
                    label: 0,
                    sent: function () {
                      if (1 & i[0]) throw i[1];
                      return i[1];
                    },
                    trys: [],
                    ops: [],
                  };
                return (
                  (o = { next: s(0), throw: s(1), return: s(2) }),
                  'function' == typeof Symbol &&
                    (o[Symbol.iterator] = function () {
                      return this;
                    }),
                  o
                );
                function s(o) {
                  return function (s) {
                    return (function (o) {
                      if (n)
                        throw new TypeError('Generator is already executing.');
                      for (; a; )
                        try {
                          if (
                            ((n = 1),
                            r &&
                              (i =
                                2 & o[0]
                                  ? r.return
                                  : o[0]
                                  ? r.throw || ((i = r.return) && i.call(r), 0)
                                  : r.next) &&
                              !(i = i.call(r, o[1])).done)
                          )
                            return i;
                          switch (
                            ((r = 0), i && (o = [2 & o[0], i.value]), o[0])
                          ) {
                            case 0:
                            case 1:
                              i = o;
                              break;
                            case 4:
                              return a.label++, { value: o[1], done: !1 };
                            case 5:
                              a.label++, (r = o[1]), (o = [0]);
                              continue;
                            case 7:
                              (o = a.ops.pop()), a.trys.pop();
                              continue;
                            default:
                              if (
                                !((i = a.trys),
                                (i = i.length > 0 && i[i.length - 1]) ||
                                  (6 !== o[0] && 2 !== o[0]))
                              ) {
                                a = 0;
                                continue;
                              }
                              if (
                                3 === o[0] &&
                                (!i || (o[1] > i[0] && o[1] < i[3]))
                              ) {
                                a.label = o[1];
                                break;
                              }
                              if (6 === o[0] && a.label < i[1]) {
                                (a.label = i[1]), (i = o);
                                break;
                              }
                              if (i && a.label < i[2]) {
                                (a.label = i[2]), a.ops.push(o);
                                break;
                              }
                              i[2] && a.ops.pop(), a.trys.pop();
                              continue;
                          }
                          o = t.call(e, a);
                        } catch (e) {
                          (o = [6, e]), (r = 0);
                        } finally {
                          n = i = 0;
                        }
                      if (5 & o[0]) throw o[1];
                      return { value: o[0] ? o[1] : void 0, done: !0 };
                    })([o, s]);
                  };
                }
              };
          Object.defineProperty(t, '__esModule', { value: !0 }),
            (t.CTX = void 0);
          n(406);
          var o,
            a,
            s,
            c = n(257),
            u = n(658),
            l = n(737),
            f = n(210),
            d = n(853),
            h = n(328),
            p = (function () {
              function e(e) {
                var t = this;
                if (
                  ((this.executeCommandBar = function (e) {
                    return r(t, void 0, void 0, function () {
                      return i(this, function (t) {
                        switch (t.label) {
                          case 0:
                            return 'TabPrintPreview' !== e
                              ? [3, 2]
                              : [4, this.printExecute()];
                          case 1:
                            t.sent(), (t.label = 2);
                          case 2:
                            return [2];
                        }
                      });
                    });
                  }),
                  (this.litePreviewPrint = {
                    CommandBars: function (e) {
                      if ('TabPrintPreview' === e)
                        return {
                          Execute: function () {
                            return r(t, void 0, void 0, function () {
                              return i(this, function (e) {
                                switch (e.label) {
                                  case 0:
                                    return [4, this.printExecute()];
                                  case 1:
                                    return [2, e.sent()];
                                }
                              });
                            });
                          },
                        };
                    },
                  }),
                  console.log('OPEN_JSSDK_VERSION '.concat('0.0.12')),
                  console.log(
                    (0, l.isLitePreviewUrl)(null == e ? void 0 : e.url)
                      ? '极速预览'
                      : 'wo预览'
                  ),
                  (0, l.isLitePreviewUrl)(null == e ? void 0 : e.url))
                )
                  o = u.default;
                else
                  try {
                    (o = WebOfficeSDK), console.log('使用外部的sdk');
                  } catch (e) {
                    (o = c.default), console.log('使用内部的sdk');
                  }
                e
                  ? e.url &&
                    ((0, l.isOpenDocUrl)(e.url) || (0, l.isWebofficeUrl)(e.url))
                    ? ((e.mount && e.mount instanceof Node) ||
                        (e.mount = document.getElementsByTagName('body')[0]),
                      (this.config = e),
                      this.addPreviewContainer())
                    : console.error('请设置文档中台预览链接')
                  : console.error('初始化sdk失败，缺失config参数');
              }
              return (
                (e.prototype.getInstance = function () {
                  var e = this,
                    t = this.instance;
                  return (
                    (0, l.isLitePreviewUrl)(this.config.url) &&
                      ((t.Application = this.litePreviewPrint),
                      (t.executeCommandBar = this.executeCommandBar),
                      (t.ready = function () {
                        return r(e, void 0, void 0, function () {
                          return i(this, function (e) {
                            return [2];
                          });
                        });
                      })),
                    (0, l.isWebofficeUrl)(this.config.url) &&
                      ((this.config.hasWidgetPermission = !0),
                      (t.WidgetInstance = new f.Widget(this.instance, s)),
                      Object.defineProperty(t, 'Widget', {
                        get: function () {
                          return (
                            this.WidgetInstance ||
                              console.error('用户没有使用连接器的权限'),
                            this.WidgetInstance
                          );
                        },
                      })),
                    t
                  );
                }),
                (e.prototype.addPreviewContainer = function () {
                  var e,
                    t = document.createElement('div');
                  (t.style.position = 'relative'),
                    (t.style.width = '100%'),
                    (t.style.height = '100%'),
                    null === (e = this.config.mount) ||
                      void 0 === e ||
                      e.appendChild(t),
                    (this.config.mount = t),
                    (a = (0, l.parseUrl)(this.config.url)),
                    (this.config.webpath = (0, l.parseWebpath)(a.pathname)),
                    this.config.url && (0, l.isWebofficeUrl)(this.config.url)
                      ? this.renderWebOffice()
                      : this.renderOpendoc();
                }),
                (e.prototype.renderWebOffice = function () {
                  return r(this, void 0, void 0, function () {
                    var e;
                    return i(this, function (t) {
                      return (
                        (e = new o.config(this.config)),
                        (this.instance = e),
                        [2]
                      );
                    });
                  });
                }),
                (e.prototype.isExpired = function (e) {
                  return Date.now() > 1e3 * e;
                }),
                (e.prototype.renderOpendoc = function () {
                  var e,
                    t = document.createElement('iframe');
                  (t.allowFullscreen = !0),
                    (t.src = this.config.url),
                    (t.allowFullscreen = !0),
                    (t.frameBorder = 'none'),
                    (t.style.width = '100%'),
                    (t.style.height = '100%'),
                    null === (e = this.config.mount) ||
                      void 0 === e ||
                      e.appendChild(t),
                    (s = t),
                    (this.instance = {
                      setToken: this.setToken.bind(this),
                      print: this.openDocPrint.bind(this),
                      download: this.openDocDownload.bind(this),
                    });
                }),
                (e.prototype.setToken = function (e) {
                  console.log('this.config', this.config),
                    (this.config.setToken = e),
                    (0, d.setToken)(this.config, s);
                }),
                (e.prototype.openDocPrint = function () {
                  (0, h.openDocFunc)(h.EVENT_TYPE.PRINT, s);
                }),
                (e.prototype.openDocDownload = function () {
                  (0, h.openDocFunc)(h.EVENT_TYPE.DOWNLOAD, s);
                }),
                (e.prototype.printExecute = function () {
                  return r(this, void 0, void 0, function () {
                    var e, t;
                    return i(this, function (n) {
                      switch (n.label) {
                        case 0:
                          return [4, this.instance.print()];
                        case 1:
                          return (
                            (e = n.sent()),
                            (t = document.createElement('iframe')).setAttribute(
                              'style',
                              'display:none'
                            ),
                            fetch(e)
                              .then(function (e) {
                                return e.blob();
                              })
                              .then(function (e) {
                                var n = URL.createObjectURL(e);
                                (t.src = n),
                                  document.body.appendChild(t),
                                  (t.onload = function () {
                                    t.contentWindow.print();
                                  });
                              }),
                            [2]
                          );
                      }
                    });
                  });
                }),
                e
              );
            })();
          t.CTX = p;
        },
        328: function (e, t) {
          'use strict';
          Object.defineProperty(t, '__esModule', { value: !0 }),
            (t.openDocFunc = t.EVENT_TYPE = void 0),
            (function (e) {
              (e.PRINT = 'print'), (e.DOWNLOAD = 'download');
            })(t.EVENT_TYPE || (t.EVENT_TYPE = {}));
          t.openDocFunc = function (e, t) {
            var n, r;
            (n = { eventName: e }),
              (r = t.contentWindow) && r.postMessage(n, '*');
          };
        },
        853: function (e, t) {
          'use strict';
          Object.defineProperty(t, '__esModule', { value: !0 }),
            (t.setToken = void 0);
          var n,
            r = null,
            i = null,
            o = !0,
            a = null;
          function s(e) {
            var t, n;
            (t = { eventName: o ? 'setToken' : 'setTokenRefresh', data: e }),
              (n = a.contentWindow) && n.postMessage(t, '*'),
              (o = !1),
              (r = e),
              (i = new Date().getTime());
          }
          function c(e) {
            return (
              e instanceof Promise ||
              'function' == typeof (null == e ? void 0 : e.then)
            );
          }
          t.setToken = function (e, t) {
            if (!e.setToken || !e.setToken.token)
              return console.error('请按照文档规范设置token格式');
            (n = e),
              window.addEventListener('message', function (u) {
                'wpsPreviewDidMount' === u.data &&
                  ((r = null),
                  (i = null),
                  (o = !0),
                  (a = t),
                  s(e.setToken),
                  'function' == typeof e.refreshToken &&
                    (function (e) {
                      window.document.addEventListener(
                        'visibilitychange',
                        function () {
                          if ('hidden' !== document.visibilityState) {
                            var e = new Date().getTime();
                            if (r && e - i > r.timeout) {
                              var t = n.refreshToken();
                              c(t)
                                ? t.then(function (e) {
                                    s(e);
                                  })
                                : s(t);
                            }
                          }
                        }
                      );
                      var t = function (e) {
                          s(e), e.timeout && o(e.timeout);
                        },
                        o = function (e) {
                          var r,
                            i = e - 3e5;
                          setTimeout(function () {
                            var o = new Date().getTime(),
                              a = n.refreshToken();
                            if (c(a))
                              a.then(function (n) {
                                r = n;
                                var a = new Date().getTime();
                                setTimeout(
                                  function () {
                                    t(r);
                                  },
                                  i > 0 ? 3e5 - (a - o) : e - (a - o)
                                );
                              });
                            else {
                              r = a;
                              var s = new Date().getTime();
                              setTimeout(
                                function () {
                                  t(r);
                                },
                                i > 0 ? 3e5 - (s - o) : e - (s - o)
                              );
                            }
                          }, i);
                        };
                      o(e);
                    })(e.setToken.timeout));
              });
          };
        },
        737: function (e, t) {
          'use strict';
          function n(e) {
            return new RegExp(/\/weboffice\/office\//).test(e);
          }
          Object.defineProperty(t, '__esModule', { value: !0 }),
            (t.decryptTag =
              t.encryptTag =
              t.querystring =
              t.isLitePreviewUrl =
              t.isWebofficeUrl =
              t.isOpenDocUrl =
              t.parseWebpath =
              t.parseUrl =
              t.isJSON =
                void 0),
            (t.isJSON = function (e) {
              if ('string' != typeof e) return !1;
              try {
                var t = JSON.parse(e);
                return !('object' != typeof t || !t);
              } catch (t) {
                return console.log('error：' + e + '!!!' + t), !1;
              }
            }),
            (t.parseUrl = function (e) {
              var t = document.createElement('a');
              t.href = e;
              var n = t.hostname,
                r = '80' === t.port || '443' === t.port ? '' : t.port,
                i = n + (r ? ':'.concat(r) : '');
              return {
                href: e,
                protocol: t.protocol || '',
                host: i,
                hostname: n,
                port: r,
                search: t.search.replace(t.hash, '') || '',
                hash: t.hash || '',
                pathname:
                  0 === (t.pathname || '').indexOf('/')
                    ? t.pathname || ''
                    : '/' + (t.pathname || ''),
                relative: (e.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1] || '',
                segments: t.pathname.replace(/^\//, '').split('/') || [],
                origin: t.protocol + '//' + i || '',
              };
            }),
            (t.parseWebpath = function (e) {
              var t = e || window.location.pathname || '';
              return t.substring(0, t.lastIndexOf('/docs/viewweb/')) || '';
            }),
            (t.isOpenDocUrl = function (e) {
              return new RegExp(/\/micsweb\/viewweb\/reader\//).test(e);
            }),
            (t.isWebofficeUrl = n),
            (t.isLitePreviewUrl = function (e) {
              return n(e) && e.includes('wpsCachePreview');
            }),
            (t.querystring = function (e) {
              var t = (e || window.location.search)
                  .replace(/^(\S*)\?/, '')
                  .split('&'),
                n = {};
              return (
                t.forEach(function (e) {
                  var t = e.split('=');
                  n[t[0]] = t[1];
                }),
                n
              );
            });
          t.encryptTag = function (e, t) {
            if (!e) return '';
            try {
              var n = encodeURIComponent(e),
                r = encodeURIComponent(t),
                i = btoa(n);
              return btoa(r) + '_' + i;
            } catch (e) {
              return '';
            }
          };
          t.decryptTag = function (e, t) {
            if (!e) return '';
            var n = e.split('_');
            if (n.length < 2) return '';
            try {
              var r = decodeURIComponent(atob(n[1]));
              return decodeURIComponent(atob(n[0])) !== t ? '' : r;
            } catch (e) {
              return '';
            }
          };
        },
        882: function (e, t, n) {
          'use strict';
          var r;
          Object.defineProperty(t, '__esModule', { value: !0 }),
            (t.DEFAULT_PLACEHOLDER_TEXT = void 0);
          var i = n(153);
          t.DEFAULT_PLACEHOLDER_TEXT =
            (((r = {})[i.BASE_WIDGET_TYPE.SINGLE_LINE_TEXT] = '请输入文本'),
            (r[i.BASE_WIDGET_TYPE.MULTILINE_TEXT] = '请输入文本'),
            (r[i.BASE_WIDGET_TYPE.HTML] = '请输入HTML'),
            (r[i.BASE_WIDGET_TYPE.IMAGE] = ''),
            r);
        },
        210: function (e, t, n) {
          'use strict';
          var r =
              (this && this.__assign) ||
              function () {
                return (
                  (r =
                    Object.assign ||
                    function (e) {
                      for (var t, n = 1, r = arguments.length; n < r; n++)
                        for (var i in (t = arguments[n]))
                          Object.prototype.hasOwnProperty.call(t, i) &&
                            (e[i] = t[i]);
                      return e;
                    }),
                  r.apply(this, arguments)
                );
              },
            i =
              (this && this.__awaiter) ||
              function (e, t, n, r) {
                return new (n || (n = Promise))(function (i, o) {
                  function a(e) {
                    try {
                      c(r.next(e));
                    } catch (e) {
                      o(e);
                    }
                  }
                  function s(e) {
                    try {
                      c(r.throw(e));
                    } catch (e) {
                      o(e);
                    }
                  }
                  function c(e) {
                    var t;
                    e.done
                      ? i(e.value)
                      : ((t = e.value),
                        t instanceof n
                          ? t
                          : new n(function (e) {
                              e(t);
                            })).then(a, s);
                  }
                  c((r = r.apply(e, t || [])).next());
                });
              },
            o =
              (this && this.__generator) ||
              function (e, t) {
                var n,
                  r,
                  i,
                  o,
                  a = {
                    label: 0,
                    sent: function () {
                      if (1 & i[0]) throw i[1];
                      return i[1];
                    },
                    trys: [],
                    ops: [],
                  };
                return (
                  (o = { next: s(0), throw: s(1), return: s(2) }),
                  'function' == typeof Symbol &&
                    (o[Symbol.iterator] = function () {
                      return this;
                    }),
                  o
                );
                function s(o) {
                  return function (s) {
                    return (function (o) {
                      if (n)
                        throw new TypeError('Generator is already executing.');
                      for (; a; )
                        try {
                          if (
                            ((n = 1),
                            r &&
                              (i =
                                2 & o[0]
                                  ? r.return
                                  : o[0]
                                  ? r.throw || ((i = r.return) && i.call(r), 0)
                                  : r.next) &&
                              !(i = i.call(r, o[1])).done)
                          )
                            return i;
                          switch (
                            ((r = 0), i && (o = [2 & o[0], i.value]), o[0])
                          ) {
                            case 0:
                            case 1:
                              i = o;
                              break;
                            case 4:
                              return a.label++, { value: o[1], done: !1 };
                            case 5:
                              a.label++, (r = o[1]), (o = [0]);
                              continue;
                            case 7:
                              (o = a.ops.pop()), a.trys.pop();
                              continue;
                            default:
                              if (
                                !((i = a.trys),
                                (i = i.length > 0 && i[i.length - 1]) ||
                                  (6 !== o[0] && 2 !== o[0]))
                              ) {
                                a = 0;
                                continue;
                              }
                              if (
                                3 === o[0] &&
                                (!i || (o[1] > i[0] && o[1] < i[3]))
                              ) {
                                a.label = o[1];
                                break;
                              }
                              if (6 === o[0] && a.label < i[1]) {
                                (a.label = i[1]), (i = o);
                                break;
                              }
                              if (i && a.label < i[2]) {
                                (a.label = i[2]), a.ops.push(o);
                                break;
                              }
                              i[2] && a.ops.pop(), a.trys.pop();
                              continue;
                          }
                          o = t.call(e, a);
                        } catch (e) {
                          (o = [6, e]), (r = 0);
                        } finally {
                          n = i = 0;
                        }
                      if (5 & o[0]) throw o[1];
                      return { value: o[0] ? o[1] : void 0, done: !0 };
                    })([o, s]);
                  };
                }
              },
            a =
              (this && this.__values) ||
              function (e) {
                var t = 'function' == typeof Symbol && Symbol.iterator,
                  n = t && e[t],
                  r = 0;
                if (n) return n.call(e);
                if (e && 'number' == typeof e.length)
                  return {
                    next: function () {
                      return (
                        e && r >= e.length && (e = void 0),
                        { value: e && e[r++], done: !e }
                      );
                    },
                  };
                throw new TypeError(
                  t
                    ? 'Object is not iterable.'
                    : 'Symbol.iterator is not defined.'
                );
              },
            s =
              (this && this.__read) ||
              function (e, t) {
                var n = 'function' == typeof Symbol && e[Symbol.iterator];
                if (!n) return e;
                var r,
                  i,
                  o = n.call(e),
                  a = [];
                try {
                  for (; (void 0 === t || t-- > 0) && !(r = o.next()).done; )
                    a.push(r.value);
                } catch (e) {
                  i = { error: e };
                } finally {
                  try {
                    r && !r.done && (n = o.return) && n.call(o);
                  } finally {
                    if (i) throw i.error;
                  }
                }
                return a;
              },
            c =
              (this && this.__spreadArray) ||
              function (e, t, n) {
                if (n || 2 === arguments.length)
                  for (var r, i = 0, o = t.length; i < o; i++)
                    (!r && i in t) ||
                      (r || (r = Array.prototype.slice.call(t, 0, i)),
                      (r[i] = t[i]));
                return e.concat(r || Array.prototype.slice.call(t));
              };
          Object.defineProperty(t, '__esModule', { value: !0 }),
            (t.Widget = void 0);
          var u = n(737),
            l = n(882),
            f = n(153),
            d = n(532),
            h = (function () {
              function e(e, t) {
                (this.wpsInstance = e), (this.iframe = t);
              }
              return (
                (e.prototype.addApiEventListener = function (e, t) {
                  var n,
                    a = this,
                    s = new URL(
                      null === (n = this.wpsInstance) || void 0 === n
                        ? void 0
                        : n.url
                    ),
                    c = null == s ? void 0 : s.searchParams,
                    l = null == c ? void 0 : c.get('_w_appid');
                  this.wpsInstance.ApiEvent.AddApiEventListener(
                    e,
                    function (n) {
                      return i(a, void 0, void 0, function () {
                        var i, a, s, c, f;
                        return o(this, function (o) {
                          switch (o.label) {
                            case 0:
                              if (
                                ((i = (0, u.decryptTag)(
                                  null == n ? void 0 : n.tag,
                                  l
                                )),
                                (a = JSON.parse(i)),
                                'ContentControlBeforeDelete' === e)
                              )
                                try {
                                  return (
                                    (s = r(r({}, n), {
                                      tag: null == a ? void 0 : a.tag,
                                      widgetId:
                                        (null == a ? void 0 : a.widgetId) || '',
                                      widgetName:
                                        (null == a ? void 0 : a.widgetName) ||
                                        '',
                                    })),
                                    t(s),
                                    [2]
                                  );
                                } catch (e) {
                                  console.error('Error', e), t(n);
                                }
                              return [4, this.getWpsWidgetItem(n.id)];
                            case 1:
                              return (
                                (c = o.sent()),
                                (f = !!c) ? [4, this.getWidgetInfo(c)] : [3, 3]
                              );
                            case 2:
                              (f = o.sent()), (o.label = 3);
                            case 3:
                              return t(f || n), [2];
                          }
                        });
                      });
                    }
                  );
                }),
                (e.prototype.insertWidget = function (e) {
                  var t = e.baseWidgetType,
                    n = e.widgetItem,
                    r = e.range;
                  return i(this, void 0, void 0, function () {
                    var e;
                    return o(this, function (i) {
                      switch (i.label) {
                        case 0:
                          return (
                            i.trys.push([0, 3, , 4]),
                            [
                              4,
                              this.initWidgetInfo({
                                baseWidgetType: t,
                                widgetItem: n,
                                range: r,
                              }),
                            ]
                          );
                        case 1:
                          return (e = i.sent()), [4, this.getWidgetInfo(e)];
                        case 2:
                          return [2, i.sent()];
                        case 3:
                          return [2, i.sent()];
                        case 4:
                          return [2];
                      }
                    });
                  });
                }),
                (e.prototype.getWidgetList = function (e) {
                  return i(this, void 0, void 0, function () {
                    var t, n, r, i, a;
                    return o(this, function (o) {
                      switch (o.label) {
                        case 0:
                          return [4, this.wpsInstance.ready()];
                        case 1:
                          return (
                            o.sent(),
                            (t = []),
                            (null == e ? void 0 : e.id)
                              ? [
                                  4,
                                  this.getWidgetItem(
                                    null == e ? void 0 : e.id,
                                    !1
                                  ),
                                ]
                              : [3, 3]
                          );
                        case 2:
                          return (
                            (i = o.sent()),
                            (null == e ? void 0 : e.tag)
                              ? [
                                  2,
                                  (null == i ? void 0 : i.tag) ===
                                  (null == e ? void 0 : e.tag)
                                    ? [i]
                                    : [],
                                ]
                              : [2, [i]]
                          );
                        case 3:
                          return [
                            4,
                            this.wpsInstance.Application.ActiveDocument
                              .ContentControls.Count,
                          ];
                        case 4:
                          (n = o.sent()), (r = 1), (o.label = 5);
                        case 5:
                          if (!(r <= n)) return [3, 10];
                          o.label = 6;
                        case 6:
                          return (
                            o.trys.push([6, 8, , 9]),
                            [4, this.getWidgetItem(r, !0)]
                          );
                        case 7:
                          return (
                            (i = o.sent()),
                            [
                              f.WIDGET_TYPE.BASE_WIDGET,
                              f.WIDGET_TYPE.BUSINESS_WIDGET,
                            ].includes(null == i ? void 0 : i.widgetType) &&
                              t.push(i),
                            [3, 9]
                          );
                        case 8:
                          return (
                            (a = o.sent()), console.error('Error', a), [3, 9]
                          );
                        case 9:
                          return r++, [3, 5];
                        case 10:
                          return [
                            2,
                            (null == e ? void 0 : e.tag)
                              ? null == t
                                ? void 0
                                : t.filter(function (t) {
                                    return (
                                      (null == t ? void 0 : t.tag) ===
                                      (null == e ? void 0 : e.tag)
                                    );
                                  })
                              : t,
                          ];
                      }
                    });
                  });
                }),
                (e.prototype.modifyWidgetItemProperty = function (e) {
                  var t = e.id,
                    n = e.tag,
                    s = e.widgetItem;
                  return i(this, void 0, void 0, function () {
                    var e,
                      i,
                      c,
                      u,
                      l,
                      d,
                      h,
                      p,
                      v,
                      g,
                      _,
                      E,
                      y = this;
                    return o(this, function (o) {
                      switch (o.label) {
                        case 0:
                          if (((e = new Map()), !t && !n))
                            throw new Error(
                              '控件ID和标识符tag至少选择一个输入！'
                            );
                          return s
                            ? ((i = function (t, n) {
                                var r, i, o, a;
                                try {
                                  switch (
                                    null == t ? void 0 : t.baseWidgetType
                                  ) {
                                    case f.BASE_WIDGET_TYPE.SINGLE_LINE_TEXT:
                                    case f.BASE_WIDGET_TYPE.MULTILINE_TEXT:
                                      ((null == s ? void 0 : s.imageSource) ||
                                        (null == s ? void 0 : s.html)) &&
                                        e.set(
                                          null == t ? void 0 : t.id,
                                          null === (r = Object.keys(s)) ||
                                            void 0 === r
                                            ? void 0
                                            : r.filter(function (e) {
                                                return ![
                                                  'widgetName',
                                                  'tag',
                                                  'content',
                                                  'placeholderText',
                                                ].includes(e);
                                              })
                                        );
                                      break;
                                    case f.BASE_WIDGET_TYPE.IMAGE:
                                      ((null == s
                                        ? void 0
                                        : s.placeholderText) ||
                                        (null == s ? void 0 : s.content) ||
                                        (null == s ? void 0 : s.html)) &&
                                        e.set(
                                          null == t ? void 0 : t.id,
                                          null === (i = Object.keys(s)) ||
                                            void 0 === i
                                            ? void 0
                                            : i.filter(function (e) {
                                                return ![
                                                  'imageSource',
                                                  'widgetName',
                                                  'tag',
                                                ].includes(e);
                                              })
                                        );
                                      break;
                                    case f.BASE_WIDGET_TYPE.HTML:
                                      (s.imageSource || s.content) &&
                                        e.set(
                                          t.id,
                                          null === (o = Object.keys(s)) ||
                                            void 0 === o
                                            ? void 0
                                            : o.filter(function (e) {
                                                return ![
                                                  'widgetName',
                                                  'tag',
                                                  'placeholderText',
                                                  'html',
                                                ].includes(e);
                                              })
                                        );
                                  }
                                  if (e.size > 0)
                                    throw new Error(
                                      '控件'
                                        .concat(
                                          null == t ? void 0 : t.id,
                                          '不支持修改下列属性：'
                                        )
                                        .concat(
                                          null ===
                                            (a = e.get(
                                              null == t ? void 0 : t.id
                                            )) || void 0 === a
                                            ? void 0
                                            : a.join(),
                                          '，设置失败'
                                        )
                                    );
                                  n();
                                } catch (e) {
                                  console.error('Error', e), n();
                                }
                              }),
                              t ? [3, 2] : [4, this.getWidgetList()])
                            : [2];
                        case 1:
                          if (
                            ((c = o.sent()),
                            (u =
                              null == c
                                ? void 0
                                : c.filter(function (e) {
                                    return (null == e ? void 0 : e.tag) === n;
                                  })),
                            !((null == u ? void 0 : u.length) > 0))
                          )
                            throw new Error(
                              '未检索到和tag = "'.concat(n, '"匹配的控件')
                            );
                          l = function (e) {
                            i(e, function () {
                              var t = r(r({}, e), s);
                              y.setWidgetInfo(null == e ? void 0 : e.id, t);
                            });
                          };
                          try {
                            for (d = a(u), h = d.next(); !h.done; h = d.next())
                              (p = h.value), l(p);
                          } catch (e) {
                            _ = { error: e };
                          } finally {
                            try {
                              h && !h.done && (E = d.return) && E.call(d);
                            } finally {
                              if (_) throw _.error;
                            }
                          }
                          return [3, 5];
                        case 2:
                          return [4, this.getWpsWidgetItem(t)];
                        case 3:
                          return (p = o.sent()), [4, this.getWidgetInfo(p)];
                        case 4:
                          if (
                            ((v = o.sent()),
                            (g = r(r({}, v), s)),
                            n && (null == v ? void 0 : v.tag) !== n)
                          )
                            throw new Error(
                              '未检索到id:'
                                .concat(t, '和tag:"')
                                .concat(n, '"匹配的控件')
                            );
                          i(g, function () {
                            y.setWidgetInfo(t, g);
                          }),
                            (o.label = 5);
                        case 5:
                          return [2];
                      }
                    });
                  });
                }),
                (e.prototype.setLocate = function (e) {
                  return i(this, void 0, void 0, function () {
                    return o(this, function (t) {
                      switch (t.label) {
                        case 0:
                          return [4, this.getWpsWidgetItem(e)];
                        case 1:
                          return [4, t.sent().Locate()];
                        case 2:
                          return t.sent(), [2];
                      }
                    });
                  });
                }),
                (e.prototype.hasBasePropertyExist = function (e) {
                  var t = [];
                  return (
                    ['tag', 'widgetId', 'widgetName'].forEach(function (n) {
                      t.push(!!e[n]);
                    }),
                    t.some(function (e) {
                      return e;
                    })
                  );
                }),
                (e.prototype.deleteWidgetItem = function (e) {
                  return i(this, void 0, void 0, function () {
                    var t, n;
                    return o(this, function (r) {
                      switch (r.label) {
                        case 0:
                          return [4, this.getWpsWidgetItem(e)];
                        case 1:
                          return (t = r.sent()), [4, this.getWidgetInfo(t)];
                        case 2:
                          return (n = r.sent()), [4, t.Delete()];
                        case 3:
                          return r.sent(), [2, n];
                      }
                    });
                  });
                }),
                (e.prototype.getWidgetItem = function (e, t) {
                  return i(this, void 0, void 0, function () {
                    var n, r;
                    return o(this, function (i) {
                      switch (i.label) {
                        case 0:
                          return t
                            ? [4, this.getWpsWidgetItemByIndex(e)]
                            : [3, 2];
                        case 1:
                          return (r = i.sent()), [3, 4];
                        case 2:
                          return [4, this.getWpsWidgetItem(e)];
                        case 3:
                          (r = i.sent()), (i.label = 4);
                        case 4:
                          return (n = r), [4, this.getWidgetInfo(n)];
                        case 5:
                          return [2, i.sent()];
                      }
                    });
                  });
                }),
                (e.prototype.getWpsWidgetItem = function (e) {
                  var t;
                  return i(this, void 0, void 0, function () {
                    return o(this, function (n) {
                      switch (n.label) {
                        case 0:
                          return [4, this.wpsInstance.ready()];
                        case 1:
                          return (
                            n.sent(),
                            [
                              4,
                              null === (t = this.wpsInstance.Application) ||
                              void 0 === t
                                ? void 0
                                : t.ActiveDocument.ContentControls({ ID: e }),
                            ]
                          );
                        case 2:
                          return [2, n.sent()];
                      }
                    });
                  });
                }),
                (e.prototype.getWpsWidgetItemByIndex = function (e) {
                  var t;
                  return i(this, void 0, void 0, function () {
                    return o(this, function (n) {
                      switch (n.label) {
                        case 0:
                          return [4, this.wpsInstance.ready()];
                        case 1:
                          return (
                            n.sent(),
                            [
                              2,
                              null === (t = this.wpsInstance) || void 0 === t
                                ? void 0
                                : t.Application.ActiveDocument.ContentControls.Item(
                                    e
                                  ),
                            ]
                          );
                      }
                    });
                  });
                }),
                (e.prototype.typeIsWidgetType = function (e) {
                  var t = [];
                  for (var n in f.WIDGET_TYPE_ENUM)
                    isNaN(Number(n)) || t.push(Number(n));
                  return t.includes(e);
                }),
                (e.prototype.initWidgetInfo = function (e) {
                  var t,
                    n = e.baseWidgetType,
                    r = e.widgetItem,
                    a = e.range;
                  return i(this, void 0, void 0, function () {
                    var e,
                      d,
                      h,
                      p,
                      v,
                      g = this;
                    return o(this, function (_) {
                      switch (_.label) {
                        case 0:
                          if (!n)
                            throw new Error('请输入要插入的基础控件类型！');
                          if (isNaN(f.BASE_WIDGET_CONTROL_MAP[n]))
                            throw new Error('基础控件类型不存在！');
                          if (
                            a &&
                            (null == a ? void 0 : a.end) <=
                              (null == a ? void 0 : a.start)
                          )
                            throw new Error(
                              '控件插入的结束位置需要大于起始位置: '
                                .concat(null == a ? void 0 : a.start, '-')
                                .concat(null == a ? void 0 : a.end)
                            );
                          if (
                            n === f.BASE_WIDGET_TYPE.HTML &&
                            !/<(\w+)[^>]*>(.*?<\/\1>)?/i.test(
                              null == r ? void 0 : r.html
                            )
                          )
                            throw new Error('输入HTML内容不合法');
                          (d =
                            null !== (t = f.BASE_WIDGET_CONTROL_MAP[n]) &&
                            void 0 !== t
                              ? t
                              : 0),
                            (_.label = 1);
                        case 1:
                          return (
                            _.trys.push([1, 3, , 4]),
                            [
                              4,
                              this.wpsInstance.Application.ActiveDocument.ContentControls.Add(
                                d,
                                {
                                  Start: null == a ? void 0 : a.start,
                                  End: null == a ? void 0 : a.end,
                                }
                              ),
                            ]
                          );
                        case 2:
                          return (e = _.sent()), [3, 4];
                        case 3:
                          return (
                            (h = _.sent()),
                            console.error('Error', '该位置无法插入控件', h),
                            [2, h]
                          );
                        case 4:
                          switch (
                            ((p = function (t) {
                              return i(g, void 0, void 0, function () {
                                var a,
                                  s,
                                  c = this;
                                return o(this, function (d) {
                                  return (
                                    ((s = {})[f.WIDGET_SETTING_ENUM.NAME] =
                                      function () {
                                        return i(
                                          c,
                                          void 0,
                                          void 0,
                                          function () {
                                            return o(this, function (t) {
                                              return [
                                                2,
                                                (e.Title =
                                                  (null == r
                                                    ? void 0
                                                    : r.widgetName) || ''),
                                              ];
                                            });
                                          }
                                        );
                                      }),
                                    (s[f.WIDGET_SETTING_ENUM.TAG] =
                                      function () {
                                        return i(
                                          c,
                                          void 0,
                                          void 0,
                                          function () {
                                            var t, i, a, s, c, l, d;
                                            return o(this, function (o) {
                                              try {
                                                (t = new URL(
                                                  null ===
                                                    (d = this.wpsInstance) ||
                                                  void 0 === d
                                                    ? void 0
                                                    : d.url
                                                )),
                                                  (i =
                                                    null == t
                                                      ? void 0
                                                      : t.searchParams),
                                                  (a =
                                                    null == i
                                                      ? void 0
                                                      : i.get('_w_appid')),
                                                  (s = {
                                                    tag:
                                                      (null == r
                                                        ? void 0
                                                        : r.tag) || '',
                                                    appId: a,
                                                    widgetId:
                                                      f.BASE_WIDGET_IDS[n],
                                                    dataImportWay: {
                                                      dataImportType:
                                                        'hand_write',
                                                    },
                                                    baseWidgetType: n || '',
                                                    widgetType: 'base_widget',
                                                    originType: 'base_widget',
                                                  }),
                                                  (c = ''.concat(
                                                    JSON.stringify(s)
                                                  )),
                                                  (l = (0, u.encryptTag)(c, a)),
                                                  (e.Tag = l);
                                              } catch (e) {
                                                throw new Error(e);
                                              }
                                              return [2];
                                            });
                                          }
                                        );
                                      }),
                                    (s[f.WIDGET_SETTING_ENUM.PLACEHOLDER] =
                                      function () {
                                        return i(
                                          c,
                                          void 0,
                                          void 0,
                                          function () {
                                            return o(this, function (t) {
                                              switch (t.label) {
                                                case 0:
                                                  return [
                                                    4,
                                                    e.SetPlaceholderText({
                                                      Text:
                                                        (null == r
                                                          ? void 0
                                                          : r.placeholderText) ||
                                                        l
                                                          .DEFAULT_PLACEHOLDER_TEXT[
                                                          n
                                                        ],
                                                    }),
                                                  ];
                                                case 1:
                                                  return t.sent(), [2];
                                              }
                                            });
                                          }
                                        );
                                      }),
                                    (s[f.WIDGET_SETTING_ENUM.CONTENT] =
                                      function () {
                                        return i(
                                          c,
                                          void 0,
                                          void 0,
                                          function () {
                                            return o(this, function (t) {
                                              if (
                                                !(null == r
                                                  ? void 0
                                                  : r.content)
                                              )
                                                return [2];
                                              switch (n) {
                                                case f
                                                  .BASE_WIDGET_TYPE.SINGLE_LINE_TEXT:
                                                case f
                                                  .BASE_WIDGET_TYPE.MULTILINE_TEXT:
                                                  e.Content =
                                                    null == r
                                                      ? void 0
                                                      : r.content;
                                              }
                                              return [2];
                                            });
                                          }
                                        );
                                      }),
                                    (s[f.WIDGET_SETTING_ENUM.ALLOW_MULTILINE] =
                                      function () {
                                        return i(
                                          c,
                                          void 0,
                                          void 0,
                                          function () {
                                            var t;
                                            return o(this, function (r) {
                                              return (
                                                (t =
                                                  n ===
                                                  f.BASE_WIDGET_TYPE
                                                    .MULTILINE_TEXT),
                                                (e.MultiLine = t),
                                                [2]
                                              );
                                            });
                                          }
                                        );
                                      }),
                                    (s[f.WIDGET_SETTING_ENUM.IMAGE_SOURCE] =
                                      function () {
                                        return i(
                                          c,
                                          void 0,
                                          void 0,
                                          function () {
                                            var t, i, a, s, c, u, l, d, h, p, v;
                                            return o(this, function (o) {
                                              switch (o.label) {
                                                case 0:
                                                  if (
                                                    n !==
                                                      f.BASE_WIDGET_TYPE
                                                        .IMAGE ||
                                                    !(null == r
                                                      ? void 0
                                                      : r.imageSource)
                                                  )
                                                    return [3, 2];
                                                  if (
                                                    !(null ===
                                                      (i =
                                                        null == r
                                                          ? void 0
                                                          : r.imageSource) ||
                                                    void 0 === i
                                                      ? void 0
                                                      : i.imageUrl) &&
                                                    !(null ===
                                                      (a =
                                                        null == r
                                                          ? void 0
                                                          : r.imageSource) ||
                                                    void 0 === a
                                                      ? void 0
                                                      : a.imageData)
                                                  )
                                                    throw new Error(
                                                      '图片控件数据源缺失！'
                                                    );
                                                  return [4, e.Shape.Reset()];
                                                case 1:
                                                  if (
                                                    (o.sent(),
                                                    (t =
                                                      /^(https?:\/\/).*\.(png|jpg|jpeg|gif|webp|svg)$/i),
                                                    /^\s*data:(?:[a-z]+\/[a-z0-9-+.]+(?:;[a-z-]+=[a-z0-9-]+)?)?(?:;base64)?,([a-z0-9!$&',()*+;=\-._~:@/?%\s]*?)\s*$/i.test(
                                                      null ===
                                                        (s =
                                                          null == r
                                                            ? void 0
                                                            : r.imageSource) ||
                                                        void 0 === s
                                                        ? void 0
                                                        : s.imageData
                                                    ))
                                                  )
                                                    e.Shape.Data =
                                                      null ===
                                                        (c =
                                                          null == r
                                                            ? void 0
                                                            : r.imageSource) ||
                                                      void 0 === c
                                                        ? void 0
                                                        : c.imageData;
                                                  else {
                                                    if (
                                                      !t.test(
                                                        null ===
                                                          (u =
                                                            null == r
                                                              ? void 0
                                                              : r.imageSource) ||
                                                          void 0 === u
                                                          ? void 0
                                                          : u.imageUrl
                                                      )
                                                    )
                                                      throw new Error(
                                                        '图片数据源格式非法!'
                                                      );
                                                    e.Shape.Url =
                                                      null ===
                                                        (l =
                                                          null == r
                                                            ? void 0
                                                            : r.imageSource) ||
                                                      void 0 === l
                                                        ? void 0
                                                        : l.imageUrl;
                                                  }
                                                  (null ===
                                                    (d =
                                                      null == r
                                                        ? void 0
                                                        : r.imageSource) ||
                                                  void 0 === d
                                                    ? void 0
                                                    : d.width) &&
                                                    (e.Shape.Width =
                                                      null ===
                                                        (h =
                                                          null == r
                                                            ? void 0
                                                            : r.imageSource) ||
                                                      void 0 === h
                                                        ? void 0
                                                        : h.width),
                                                    (null ===
                                                      (p =
                                                        null == r
                                                          ? void 0
                                                          : r.imageSource) ||
                                                    void 0 === p
                                                      ? void 0
                                                      : p.height) &&
                                                      (e.Shape.Height =
                                                        null ===
                                                          (v =
                                                            null == r
                                                              ? void 0
                                                              : r.imageSource) ||
                                                        void 0 === v
                                                          ? void 0
                                                          : v.height),
                                                    (o.label = 2);
                                                case 2:
                                                  return [2];
                                              }
                                            });
                                          }
                                        );
                                      }),
                                    (s[f.WIDGET_SETTING_ENUM.EXTENSION_DATA] =
                                      function () {
                                        return i(
                                          c,
                                          void 0,
                                          void 0,
                                          function () {
                                            var t;
                                            return o(this, function (i) {
                                              switch (i.label) {
                                                case 0:
                                                  return (
                                                    !0,
                                                    !0 ==
                                                    (n ===
                                                      f.BASE_WIDGET_TYPE.HTML)
                                                      ? [3, 1]
                                                      : [3, 4]
                                                  );
                                                case 1:
                                                  return (
                                                    (t =
                                                      null == r
                                                        ? void 0
                                                        : r.html),
                                                    t
                                                      ? [
                                                          4,
                                                          e.AddOrEditExtensionData(
                                                            [
                                                              {
                                                                key: f
                                                                  .EXTENSION_DATA
                                                                  .WIDGET_HTML_FIGMENT,
                                                                value: t,
                                                              },
                                                            ]
                                                          ),
                                                        ]
                                                      : [3, 3]
                                                  );
                                                case 2:
                                                  i.sent(), (i.label = 3);
                                                case 3:
                                                case 4:
                                                  return [3, 5];
                                                case 5:
                                                  return [2];
                                              }
                                            });
                                          }
                                        );
                                      }),
                                    (s[f.WIDGET_SETTING_ENUM.HTML_PASTE] =
                                      function () {
                                        return i(
                                          c,
                                          void 0,
                                          void 0,
                                          function () {
                                            var t, n, i, a;
                                            return o(this, function (o) {
                                              switch (o.label) {
                                                case 0:
                                                  return (
                                                    o.trys.push([0, 7, , 8]),
                                                    [
                                                      4,
                                                      this.wpsInstance.ready(),
                                                    ]
                                                  );
                                                case 1:
                                                  return (
                                                    o.sent(),
                                                    [4, e.LockContents]
                                                  );
                                                case 2:
                                                  return (
                                                    o.sent() &&
                                                      (e.LockContents = !1),
                                                    [4, e.Range]
                                                  );
                                                case 3:
                                                  return [
                                                    4,
                                                    (t = o.sent()).Start,
                                                  ];
                                                case 4:
                                                  return (
                                                    (n = o.sent()), [4, t.End]
                                                  );
                                                case 5:
                                                  return (
                                                    (i = o.sent()),
                                                    [
                                                      4,
                                                      this.wpsInstance.Application.ActiveDocument.Range(
                                                        n,
                                                        i
                                                      )
                                                        .PasteHtml({
                                                          HTML:
                                                            null == r
                                                              ? void 0
                                                              : r.html,
                                                        })
                                                        .then(function () {
                                                          e.LockContents = !0;
                                                        }),
                                                    ]
                                                  );
                                                case 6:
                                                  return o.sent(), [3, 8];
                                                case 7:
                                                  throw (
                                                    ((a = o.sent()),
                                                    new Error(a))
                                                  );
                                                case 8:
                                                  return [2];
                                              }
                                            });
                                          }
                                        );
                                      }),
                                    (s[f.WIDGET_SETTING_ENUM.LOCK_EDIT] =
                                      function () {
                                        return i(
                                          c,
                                          void 0,
                                          void 0,
                                          function () {
                                            return o(this, function (t) {
                                              switch (t.label) {
                                                case 0:
                                                  return [
                                                    4,
                                                    this.wpsInstance.ready(),
                                                  ];
                                                case 1:
                                                  if (
                                                    (t.sent(),
                                                    n ===
                                                      f.BASE_WIDGET_TYPE.HTML)
                                                  )
                                                    e.SetPlaceholderText({
                                                      Text:
                                                        (null == r
                                                          ? void 0
                                                          : r.placeholderText) ||
                                                        l
                                                          .DEFAULT_PLACEHOLDER_TEXT[
                                                          n
                                                        ],
                                                    }),
                                                      (e.LockContents = !0);
                                                  return [2];
                                              }
                                            });
                                          }
                                        );
                                      }),
                                    (a = s),
                                    t.forEach(function (e) {
                                      var t;
                                      null === (t = a[e]) ||
                                        void 0 === t ||
                                        t.call(a);
                                    }),
                                    [2]
                                  );
                                });
                              });
                            }),
                            (v = [
                              f.WIDGET_SETTING_ENUM.NAME,
                              f.WIDGET_SETTING_ENUM.TAG,
                            ]),
                            n)
                          ) {
                            case f.BASE_WIDGET_TYPE.SINGLE_LINE_TEXT:
                              p(
                                c(
                                  c([], s(v), !1),
                                  [
                                    f.WIDGET_SETTING_ENUM.ALLOW_MULTILINE,
                                    f.WIDGET_SETTING_ENUM.CONTENT,
                                    f.WIDGET_SETTING_ENUM.PLACEHOLDER,
                                  ],
                                  !1
                                )
                              );
                            case f.BASE_WIDGET_TYPE.MULTILINE_TEXT:
                              p(
                                c(
                                  c([], s(v), !1),
                                  [
                                    f.WIDGET_SETTING_ENUM.CONTENT,
                                    f.WIDGET_SETTING_ENUM.ALLOW_MULTILINE,
                                    f.WIDGET_SETTING_ENUM.PLACEHOLDER,
                                  ],
                                  !1
                                )
                              );
                              break;
                            case f.BASE_WIDGET_TYPE.IMAGE:
                              p(
                                c(
                                  c([], s(v), !1),
                                  [f.WIDGET_SETTING_ENUM.IMAGE_SOURCE],
                                  !1
                                )
                              );
                              break;
                            case f.BASE_WIDGET_TYPE.HTML:
                              p(
                                c(
                                  c([], s(v), !1),
                                  [
                                    f.WIDGET_SETTING_ENUM.EXTENSION_DATA,
                                    f.WIDGET_SETTING_ENUM.LOCK_EDIT,
                                    f.WIDGET_SETTING_ENUM.HTML_PASTE,
                                    f.WIDGET_SETTING_ENUM.PLACEHOLDER,
                                  ],
                                  !1
                                )
                              );
                          }
                          return [2, e];
                      }
                    });
                  });
                }),
                (e.prototype.getWidgetInfo = function (e) {
                  var t, n, a, s, c, l, h;
                  return i(this, void 0, void 0, function () {
                    var i,
                      p,
                      v,
                      g,
                      _,
                      E,
                      y,
                      T,
                      m,
                      w,
                      b,
                      I,
                      S,
                      k,
                      N,
                      A,
                      D,
                      x,
                      B,
                      L,
                      W,
                      O;
                    return o(this, function (o) {
                      switch (o.label) {
                        case 0:
                          return [4, e.Title];
                        case 1:
                          return (i = o.sent() || ''), [4, e.Tag];
                        case 2:
                          (p = o.sent()),
                            (v = new URL(
                              null === (t = this.wpsInstance) || void 0 === t
                                ? void 0
                                : t.url
                            )),
                            (g = null == v ? void 0 : v.searchParams),
                            (_ = null == g ? void 0 : g.get('_w_appid')),
                            (E = (0, u.decryptTag)(p, _)),
                            (y = ''),
                            '',
                            (T = ''),
                            (m = {}),
                            (w = {}),
                            (b = '');
                          try {
                            (m = JSON.parse(E)),
                              (y = (null == m ? void 0 : m.widgetId) || ''),
                              (null == m ? void 0 : m.widgetName) || '';
                          } catch (e) {
                            console.error('Error', e);
                          }
                          return [4, e.ID];
                        case 3:
                          return (I = o.sent()), [4, e.Type];
                        case 4:
                          return (S = o.sent()), [4, e.PlaceholderText];
                        case 5:
                          return (
                            (k = o.sent()),
                            (N = null == m ? void 0 : m.baseWidgetType),
                            [
                              f.WIDGET_TYPE_ENUM.TEXT,
                              f.WIDGET_TYPE_ENUM.RTF_CONTENT,
                            ].includes(S) &&
                            [
                              f.BASE_WIDGET_TYPE.SINGLE_LINE_TEXT,
                              f.BASE_WIDGET_TYPE.MULTILINE_TEXT,
                              f.BASE_WIDGET_TYPE.HTML,
                            ].includes(N)
                              ? [4, e.Content]
                              : [3, 7]
                          );
                        case 6:
                          (T = o.sent() || ''), (o.label = 7);
                        case 7:
                          if (
                            S !== f.WIDGET_TYPE_ENUM.IMAGE ||
                            N !== f.BASE_WIDGET_TYPE.IMAGE
                          )
                            return [3, 16];
                          o.label = 8;
                        case 8:
                          return (
                            o.trys.push([8, 15, , 16]),
                            (A = w),
                            [
                              4,
                              null === (n = null == e ? void 0 : e.Shape) ||
                              void 0 === n
                                ? void 0
                                : n.Width,
                            ]
                          );
                        case 9:
                          return (
                            (A.width = o.sent()),
                            (D = w),
                            [
                              4,
                              null === (a = null == e ? void 0 : e.Shape) ||
                              void 0 === a
                                ? void 0
                                : a.Height,
                            ]
                          );
                        case 10:
                          return (
                            (D.height = o.sent()),
                            (
                              null === (s = null == e ? void 0 : e.Shape) ||
                              void 0 === s
                                ? void 0
                                : s.Url
                            )
                              ? ((x = w),
                                [
                                  4,
                                  null === (c = null == e ? void 0 : e.Shape) ||
                                  void 0 === c
                                    ? void 0
                                    : c.Url,
                                ])
                              : [3, 12]
                          );
                        case 11:
                          (x.imageUrl = o.sent()), (o.label = 12);
                        case 12:
                          return (
                            null === (l = null == e ? void 0 : e.Shape) ||
                            void 0 === l
                              ? void 0
                              : l.Data
                          )
                            ? ((B = w),
                              [
                                4,
                                null === (h = null == e ? void 0 : e.Shape) ||
                                void 0 === h
                                  ? void 0
                                  : h.Data,
                              ])
                            : [3, 14];
                        case 13:
                          (B.imageData = o.sent()), (o.label = 14);
                        case 14:
                          return [3, 16];
                        case 15:
                          return (
                            (L = o.sent()),
                            console.error(
                              'Error',
                              '该控件'.concat(I, '没有对应的属性'),
                              L
                            ),
                            [3, 16]
                          );
                        case 16:
                          return S !== f.WIDGET_TYPE_ENUM.RTF_CONTENT ||
                            N !== f.BASE_WIDGET_TYPE.HTML
                            ? [3, 18]
                            : [
                                4,
                                (0, d.getExtensionData)(
                                  this.wpsInstance,
                                  I,
                                  f.EXTENSION_DATA.WIDGET_HTML_FIGMENT
                                ),
                              ];
                        case 17:
                          (W = o.sent()) && (b = W.value), (o.label = 18);
                        case 18:
                          switch (
                            ((O = {
                              widgetId: y,
                              widgetName: i || '',
                              tag: (null == m ? void 0 : m.tag) || '',
                              id: I,
                              widgetType: null == m ? void 0 : m.widgetType,
                              baseWidgetType:
                                null == m ? void 0 : m.baseWidgetType,
                            }),
                            N)
                          ) {
                            case f.BASE_WIDGET_TYPE.SINGLE_LINE_TEXT:
                            case f.BASE_WIDGET_TYPE.MULTILINE_TEXT:
                              return [
                                2,
                                r(r({}, O), { content: T, placeholderText: k }),
                              ];
                            case f.BASE_WIDGET_TYPE.IMAGE:
                              return [2, r(r({}, O), { imageSource: w })];
                            case f.BASE_WIDGET_TYPE.HTML:
                              return [
                                2,
                                r(r({}, O), {
                                  content: T,
                                  placeholderText: k,
                                  html: b,
                                }),
                              ];
                          }
                          return [2];
                      }
                    });
                  });
                }),
                (e.prototype.setWidgetInfo = function (e, t) {
                  return i(this, void 0, void 0, function () {
                    var n,
                      a,
                      s,
                      c,
                      l,
                      d = this;
                    return o(this, function (h) {
                      switch (h.label) {
                        case 0:
                          return [4, this.getWpsWidgetItem(e)];
                        case 1:
                          return [4, (n = h.sent()).LockContents];
                        case 2:
                          return (
                            h.sent() && (n.LockContents = !1),
                            [4, null == n ? void 0 : n.Tag]
                          );
                        case 3:
                          return (a = h.sent()), [4, this.getWidgetInfo(n)];
                        case 4:
                          return (
                            (s = h.sent()),
                            (c = null == s ? void 0 : s.baseWidgetType),
                            [4, null == n ? void 0 : n.Type]
                          );
                        case 5:
                          switch (
                            (h.sent(),
                            (l = function (e) {
                              return i(d, void 0, void 0, function () {
                                var l,
                                  d,
                                  h = this;
                                return o(this, function (p) {
                                  return (
                                    ((d = {})[f.WIDGET_SETTING_ENUM.NAME] =
                                      function () {
                                        return i(
                                          h,
                                          void 0,
                                          void 0,
                                          function () {
                                            return o(this, function (e) {
                                              return (
                                                t.widgetName &&
                                                  (n.Title = t.widgetName),
                                                [2]
                                              );
                                            });
                                          }
                                        );
                                      }),
                                    (d[f.WIDGET_SETTING_ENUM.TAG] =
                                      function () {
                                        return i(
                                          h,
                                          void 0,
                                          void 0,
                                          function () {
                                            var e, i, s, c, l, f, d;
                                            return o(this, function (o) {
                                              try {
                                                (e = new URL(
                                                  null ===
                                                    (d = this.wpsInstance) ||
                                                  void 0 === d
                                                    ? void 0
                                                    : d.url
                                                )),
                                                  (i =
                                                    null == e
                                                      ? void 0
                                                      : e.searchParams),
                                                  (s =
                                                    null == i
                                                      ? void 0
                                                      : i.get('_w_appid')),
                                                  (c = JSON.parse(
                                                    (0, u.decryptTag)(a, s)
                                                  )),
                                                  (l = JSON.stringify(
                                                    r(r({}, c), {
                                                      tag:
                                                        null == t
                                                          ? void 0
                                                          : t.tag,
                                                    })
                                                  )),
                                                  (f = (0, u.encryptTag)(l, s)),
                                                  (n.Tag = f);
                                              } catch (e) {
                                                console.error('Error', e);
                                              }
                                              return [2];
                                            });
                                          }
                                        );
                                      }),
                                    (d[f.WIDGET_SETTING_ENUM.PLACEHOLDER] =
                                      function () {
                                        return i(
                                          h,
                                          void 0,
                                          void 0,
                                          function () {
                                            return o(this, function (e) {
                                              switch (e.label) {
                                                case 0:
                                                  return (
                                                    null == t
                                                      ? void 0
                                                      : t.placeholderText
                                                  )
                                                    ? (!0,
                                                      !0 ==
                                                      (c ===
                                                        f.BASE_WIDGET_TYPE.HTML)
                                                        ? [3, 1]
                                                        : [3, 3])
                                                    : [3, 5];
                                                case 1:
                                                case 3:
                                                  return [
                                                    4,
                                                    n.SetPlaceholderText({
                                                      Text: t.placeholderText,
                                                    }),
                                                  ];
                                                case 2:
                                                case 4:
                                                  return e.sent(), [3, 5];
                                                case 5:
                                                  return [2];
                                              }
                                            });
                                          }
                                        );
                                      }),
                                    (d[f.WIDGET_SETTING_ENUM.CONTENT] =
                                      function () {
                                        return i(
                                          h,
                                          void 0,
                                          void 0,
                                          function () {
                                            return o(this, function (e) {
                                              return (
                                                (null == t
                                                  ? void 0
                                                  : t.content) &&
                                                  c !==
                                                    f.BASE_WIDGET_TYPE.HTML &&
                                                  (n.Content = t.content),
                                                [2]
                                              );
                                            });
                                          }
                                        );
                                      }),
                                    (d[f.WIDGET_SETTING_ENUM.ALLOW_MULTILINE] =
                                      function () {
                                        return i(
                                          h,
                                          void 0,
                                          void 0,
                                          function () {
                                            var e;
                                            return o(this, function (r) {
                                              return (
                                                (e =
                                                  (null == t
                                                    ? void 0
                                                    : t.baseWidgetType) ===
                                                  f.BASE_WIDGET_TYPE
                                                    .MULTILINE_TEXT),
                                                (null == s
                                                  ? void 0
                                                  : s.baseWidgetType) !==
                                                  f.BASE_WIDGET_TYPE.IMAGE &&
                                                  (n.MultiLine = e),
                                                [2]
                                              );
                                            });
                                          }
                                        );
                                      }),
                                    (d[f.WIDGET_SETTING_ENUM.IMAGE_SOURCE] =
                                      function () {
                                        return i(
                                          h,
                                          void 0,
                                          void 0,
                                          function () {
                                            var e, r, i, a, s, c, u, l, f, d, h;
                                            return o(this, function (o) {
                                              switch (o.label) {
                                                case 0:
                                                  if (
                                                    !(null ===
                                                      (r =
                                                        null == t
                                                          ? void 0
                                                          : t.imageSource) ||
                                                    void 0 === r
                                                      ? void 0
                                                      : r.imageUrl) &&
                                                    !(null ===
                                                      (i =
                                                        null == t
                                                          ? void 0
                                                          : t.imageSource) ||
                                                    void 0 === i
                                                      ? void 0
                                                      : i.imageData)
                                                  )
                                                    throw new Error(
                                                      '图片控件数据源缺失！'
                                                    );
                                                  return [4, n.Shape.Reset()];
                                                case 1:
                                                  if (
                                                    (o.sent(),
                                                    (e =
                                                      /^(https?:\/\/).*\.(png|jpg|jpeg|gif|webp|svg)$/i),
                                                    /^\s*data:(?:[a-z]+\/[a-z0-9-+.]+(?:;[a-z-]+=[a-z0-9-]+)?)?(?:;base64)?,([a-z0-9!$&',()*+;=\-._~:@/?%\s]*?)\s*$/i.test(
                                                      null ===
                                                        (a =
                                                          null == t
                                                            ? void 0
                                                            : t.imageSource) ||
                                                        void 0 === a
                                                        ? void 0
                                                        : a.imageData
                                                    ))
                                                  )
                                                    n.Shape.Data =
                                                      null ===
                                                        (s =
                                                          null == t
                                                            ? void 0
                                                            : t.imageSource) ||
                                                      void 0 === s
                                                        ? void 0
                                                        : s.imageData;
                                                  else {
                                                    if (
                                                      !e.test(
                                                        null ===
                                                          (c =
                                                            null == t
                                                              ? void 0
                                                              : t.imageSource) ||
                                                          void 0 === c
                                                          ? void 0
                                                          : c.imageUrl
                                                      )
                                                    )
                                                      throw new Error(
                                                        '图片数据源格式非法!'
                                                      );
                                                    n.Shape.Url =
                                                      null ===
                                                        (u =
                                                          null == t
                                                            ? void 0
                                                            : t.imageSource) ||
                                                      void 0 === u
                                                        ? void 0
                                                        : u.imageUrl;
                                                  }
                                                  return (
                                                    (null ===
                                                      (l =
                                                        null == t
                                                          ? void 0
                                                          : t.imageSource) ||
                                                    void 0 === l
                                                      ? void 0
                                                      : l.width) &&
                                                      (n.Shape.Width =
                                                        null ===
                                                          (f =
                                                            null == t
                                                              ? void 0
                                                              : t.imageSource) ||
                                                        void 0 === f
                                                          ? void 0
                                                          : f.width),
                                                    (null ===
                                                      (d =
                                                        null == t
                                                          ? void 0
                                                          : t.imageSource) ||
                                                    void 0 === d
                                                      ? void 0
                                                      : d.height) &&
                                                      (n.Shape.Height =
                                                        null ===
                                                          (h =
                                                            null == t
                                                              ? void 0
                                                              : t.imageSource) ||
                                                        void 0 === h
                                                          ? void 0
                                                          : h.height),
                                                    [2]
                                                  );
                                              }
                                            });
                                          }
                                        );
                                      }),
                                    (d[f.WIDGET_SETTING_ENUM.EXTENSION_DATA] =
                                      function () {
                                        return i(
                                          h,
                                          void 0,
                                          void 0,
                                          function () {
                                            var e;
                                            return o(this, function (r) {
                                              switch (r.label) {
                                                case 0:
                                                  return [4, n.LockContents];
                                                case 1:
                                                  return (
                                                    r.sent(),
                                                    !0,
                                                    !0 ==
                                                    (c ===
                                                      f.BASE_WIDGET_TYPE.HTML)
                                                      ? [3, 2]
                                                      : [3, 5]
                                                  );
                                                case 2:
                                                  return (
                                                    (e =
                                                      null == t
                                                        ? void 0
                                                        : t.html),
                                                    e
                                                      ? [
                                                          4,
                                                          n.AddOrEditExtensionData(
                                                            [
                                                              {
                                                                key: f
                                                                  .EXTENSION_DATA
                                                                  .WIDGET_HTML_FIGMENT,
                                                                value: e,
                                                              },
                                                            ]
                                                          ),
                                                        ]
                                                      : [3, 4]
                                                  );
                                                case 3:
                                                  r.sent(), (r.label = 4);
                                                case 4:
                                                case 5:
                                                  return [3, 6];
                                                case 6:
                                                  return [2];
                                              }
                                            });
                                          }
                                        );
                                      }),
                                    (d[f.WIDGET_SETTING_ENUM.HTML_PASTE] =
                                      function () {
                                        return i(
                                          h,
                                          void 0,
                                          void 0,
                                          function () {
                                            var e, r, i, a;
                                            return o(this, function (o) {
                                              switch (o.label) {
                                                case 0:
                                                  return (
                                                    o.trys.push([0, 7, , 8]),
                                                    (
                                                      null == t
                                                        ? void 0
                                                        : t.html
                                                    )
                                                      ? [
                                                          4,
                                                          this.wpsInstance.ready(),
                                                        ]
                                                      : [3, 6]
                                                  );
                                                case 1:
                                                  return o.sent(), [4, n.Range];
                                                case 2:
                                                  return [
                                                    4,
                                                    (e = o.sent()).Start,
                                                  ];
                                                case 3:
                                                  return (
                                                    (r = o.sent()), [4, e.End]
                                                  );
                                                case 4:
                                                  return (
                                                    (i = o.sent()),
                                                    [
                                                      4,
                                                      this.wpsInstance.Application.ActiveDocument.Range(
                                                        r,
                                                        i
                                                      )
                                                        .PasteHtml({
                                                          HTML:
                                                            null == t
                                                              ? void 0
                                                              : t.html,
                                                        })
                                                        .then(function () {
                                                          n.LockContents = !0;
                                                        }),
                                                    ]
                                                  );
                                                case 5:
                                                  o.sent(), (o.label = 6);
                                                case 6:
                                                  return [3, 8];
                                                case 7:
                                                  return (
                                                    (a = o.sent()),
                                                    (n.LockContents = !0),
                                                    console.error(a),
                                                    [3, 8]
                                                  );
                                                case 8:
                                                  return [2];
                                              }
                                            });
                                          }
                                        );
                                      }),
                                    (d[f.WIDGET_SETTING_ENUM.LOCK_EDIT] =
                                      function () {
                                        return i(
                                          h,
                                          void 0,
                                          void 0,
                                          function () {
                                            return o(this, function (e) {
                                              return [2];
                                            });
                                          }
                                        );
                                      }),
                                    (l = d),
                                    e.forEach(function (e) {
                                      var t;
                                      null === (t = l[e]) ||
                                        void 0 === t ||
                                        t.call(l);
                                    }),
                                    [2]
                                  );
                                });
                              });
                            }),
                            c)
                          ) {
                            case f.BASE_WIDGET_TYPE.SINGLE_LINE_TEXT:
                            case f.BASE_WIDGET_TYPE.MULTILINE_TEXT:
                              l([
                                f.WIDGET_SETTING_ENUM.NAME,
                                f.WIDGET_SETTING_ENUM.TAG,
                                f.WIDGET_SETTING_ENUM.PLACEHOLDER,
                                f.WIDGET_SETTING_ENUM.CONTENT,
                                f.WIDGET_SETTING_ENUM.ALLOW_MULTILINE,
                              ]);
                              break;
                            case f.BASE_WIDGET_TYPE.IMAGE:
                              l([
                                f.WIDGET_SETTING_ENUM.NAME,
                                f.WIDGET_SETTING_ENUM.TAG,
                                f.WIDGET_SETTING_ENUM.IMAGE_SOURCE,
                              ]);
                              break;
                            case f.BASE_WIDGET_TYPE.HTML:
                              l([
                                f.WIDGET_SETTING_ENUM.NAME,
                                f.WIDGET_SETTING_ENUM.TAG,
                                f.WIDGET_SETTING_ENUM.PLACEHOLDER,
                                f.WIDGET_SETTING_ENUM.EXTENSION_DATA,
                                f.WIDGET_SETTING_ENUM.HTML_PASTE,
                              ]);
                          }
                          return [2];
                      }
                    });
                  });
                }),
                e
              );
            })();
          t.Widget = h;
        },
        153: function (e, t) {
          'use strict';
          var n, r, i, o;
          Object.defineProperty(t, '__esModule', { value: !0 }),
            (t.EXTENSION_DATA =
              t.BASE_WIDGET_IDS =
              t.BASE_WIDGET_CONTROL_MAP =
              t.WIDGET_SETTING_ENUM =
              t.CONTROL_ELEMENT_ENUM =
              t.BASE_WIDGET_TYPE =
              t.WIDGET_TYPE =
              t.WIDGET_TYPE_ENUM =
                void 0),
            (function (e) {
              (e[(e.RTF_CONTENT = 0)] = 'RTF_CONTENT'),
                (e[(e.TEXT = 1)] = 'TEXT'),
                (e[(e.IMAGE = 2)] = 'IMAGE'),
                (e[(e.COMBINATION = 3)] = 'COMBINATION'),
                (e[(e.DROP_DOWN = 4)] = 'DROP_DOWN'),
                (e[(e.DATE = 6)] = 'DATE'),
                (e[(e.CHECKBOX = 8)] = 'CHECKBOX'),
                (e[(e.REPEAT = 9)] = 'REPEAT');
            })((i = t.WIDGET_TYPE_ENUM || (t.WIDGET_TYPE_ENUM = {}))),
            (function (e) {
              (e.BASE_WIDGET = 'base_widget'),
                (e.BUSINESS_WIDGET = 'business_widget');
            })(t.WIDGET_TYPE || (t.WIDGET_TYPE = {})),
            (function (e) {
              (e.SINGLE_LINE_TEXT = 'single_line_text'),
                (e.MULTILINE_TEXT = 'multiline_text'),
                (e.IMAGE = 'image'),
                (e.HTML = 'html');
            })((o = t.BASE_WIDGET_TYPE || (t.BASE_WIDGET_TYPE = {}))),
            (function (e) {
              (e.BASE_WIDGET = 'base_widget'),
                (e.BUSINESS_WIDGET = 'business_widget'),
                (e.BUSINESS_COMPONENTS = 'business_components'),
                (e.GROUP = 'group'),
                (e.REPEAT_GROUP = 'repeat_group');
            })(t.CONTROL_ELEMENT_ENUM || (t.CONTROL_ELEMENT_ENUM = {})),
            (function (e) {
              (e.NAME = '标题'),
                (e.TAG = '标识符'),
                (e.PLACEHOLDER = '占位符'),
                (e.CONTENT = '内容'),
                (e.EXTENSION_DATA = '拓展属性'),
                (e.ALLOW_MULTILINE = '设置是否允许换行'),
                (e.HTML_PASTE = 'HTML内容'),
                (e.LOCK_EDIT = '限制编辑'),
                (e.IMAGE_SOURCE = '图片数据源');
            })(t.WIDGET_SETTING_ENUM || (t.WIDGET_SETTING_ENUM = {})),
            (t.BASE_WIDGET_CONTROL_MAP =
              (((n = {})[o.SINGLE_LINE_TEXT] = i.TEXT),
              (n[o.MULTILINE_TEXT] = i.TEXT),
              (n[o.IMAGE] = i.IMAGE),
              (n[o.HTML] = i.RTF_CONTENT),
              n)),
            (t.BASE_WIDGET_IDS =
              (((r = {})[o.SINGLE_LINE_TEXT] = 'baseSingleLineTextWidgetId'),
              (r[o.MULTILINE_TEXT] = 'baseMultiLineTextWidgetId'),
              (r[o.IMAGE] = 'baseImageWidgetId'),
              (r[o.HTML] = 'baseHTMLWidgetId'),
              r)),
            (function (e) {
              e.WIDGET_HTML_FIGMENT = 'widget_html_figment';
            })(t.EXTENSION_DATA || (t.EXTENSION_DATA = {}));
        },
        532: function (e, t) {
          'use strict';
          var n =
              (this && this.__awaiter) ||
              function (e, t, n, r) {
                return new (n || (n = Promise))(function (i, o) {
                  function a(e) {
                    try {
                      c(r.next(e));
                    } catch (e) {
                      o(e);
                    }
                  }
                  function s(e) {
                    try {
                      c(r.throw(e));
                    } catch (e) {
                      o(e);
                    }
                  }
                  function c(e) {
                    var t;
                    e.done
                      ? i(e.value)
                      : ((t = e.value),
                        t instanceof n
                          ? t
                          : new n(function (e) {
                              e(t);
                            })).then(a, s);
                  }
                  c((r = r.apply(e, t || [])).next());
                });
              },
            r =
              (this && this.__generator) ||
              function (e, t) {
                var n,
                  r,
                  i,
                  o,
                  a = {
                    label: 0,
                    sent: function () {
                      if (1 & i[0]) throw i[1];
                      return i[1];
                    },
                    trys: [],
                    ops: [],
                  };
                return (
                  (o = { next: s(0), throw: s(1), return: s(2) }),
                  'function' == typeof Symbol &&
                    (o[Symbol.iterator] = function () {
                      return this;
                    }),
                  o
                );
                function s(o) {
                  return function (s) {
                    return (function (o) {
                      if (n)
                        throw new TypeError('Generator is already executing.');
                      for (; a; )
                        try {
                          if (
                            ((n = 1),
                            r &&
                              (i =
                                2 & o[0]
                                  ? r.return
                                  : o[0]
                                  ? r.throw || ((i = r.return) && i.call(r), 0)
                                  : r.next) &&
                              !(i = i.call(r, o[1])).done)
                          )
                            return i;
                          switch (
                            ((r = 0), i && (o = [2 & o[0], i.value]), o[0])
                          ) {
                            case 0:
                            case 1:
                              i = o;
                              break;
                            case 4:
                              return a.label++, { value: o[1], done: !1 };
                            case 5:
                              a.label++, (r = o[1]), (o = [0]);
                              continue;
                            case 7:
                              (o = a.ops.pop()), a.trys.pop();
                              continue;
                            default:
                              if (
                                !((i = a.trys),
                                (i = i.length > 0 && i[i.length - 1]) ||
                                  (6 !== o[0] && 2 !== o[0]))
                              ) {
                                a = 0;
                                continue;
                              }
                              if (
                                3 === o[0] &&
                                (!i || (o[1] > i[0] && o[1] < i[3]))
                              ) {
                                a.label = o[1];
                                break;
                              }
                              if (6 === o[0] && a.label < i[1]) {
                                (a.label = i[1]), (i = o);
                                break;
                              }
                              if (i && a.label < i[2]) {
                                (a.label = i[2]), a.ops.push(o);
                                break;
                              }
                              i[2] && a.ops.pop(), a.trys.pop();
                              continue;
                          }
                          o = t.call(e, a);
                        } catch (e) {
                          (o = [6, e]), (r = 0);
                        } finally {
                          n = i = 0;
                        }
                      if (5 & o[0]) throw o[1];
                      return { value: o[0] ? o[1] : void 0, done: !0 };
                    })([o, s]);
                  };
                }
              },
            i =
              (this && this.__read) ||
              function (e, t) {
                var n = 'function' == typeof Symbol && e[Symbol.iterator];
                if (!n) return e;
                var r,
                  i,
                  o = n.call(e),
                  a = [];
                try {
                  for (; (void 0 === t || t-- > 0) && !(r = o.next()).done; )
                    a.push(r.value);
                } catch (e) {
                  i = { error: e };
                } finally {
                  try {
                    r && !r.done && (n = o.return) && n.call(o);
                  } finally {
                    if (i) throw i.error;
                  }
                }
                return a;
              },
            o =
              (this && this.__spreadArray) ||
              function (e, t, n) {
                if (n || 2 === arguments.length)
                  for (var r, i = 0, o = t.length; i < o; i++)
                    (!r && i in t) ||
                      (r || (r = Array.prototype.slice.call(t, 0, i)),
                      (r[i] = t[i]));
                return e.concat(r || Array.prototype.slice.call(t));
              };
          Object.defineProperty(t, '__esModule', { value: !0 }),
            (t.pastControlFormatHTML =
              t.removeExtensionData =
              t.addOrEditExtensionData =
              t.getExtensionData =
              t.checkSpecialSymbols =
                void 0),
            (t.checkSpecialSymbols = function (e) {
              var t,
                n = /[&%]/g,
                r = {
                  tag: null == e ? void 0 : e.tag,
                  widgetName: null == e ? void 0 : e.widgetName,
                },
                i = { tag: '', widgetName: '' };
              return (
                null === (t = Object.keys(r)) ||
                  void 0 === t ||
                  t.forEach(function (e) {
                    n.test(r[e]) &&
                      (i[e] = ''.concat(e, '不允许输入特殊符号&、%'));
                  }),
                i
              );
            }),
            (t.getExtensionData = function (e, t, i) {
              return n(this, void 0, void 0, function () {
                var n, o;
                return r(this, function (r) {
                  switch (r.label) {
                    case 0:
                      return r.trys.push([0, 3, , 4]), [4, e.ready()];
                    case 1:
                      return (
                        r.sent(),
                        [
                          4,
                          e.Application.ActiveDocument.ContentControls.Item({
                            ID: t,
                          }).ExtensionData,
                        ]
                      );
                    case 2:
                      return (
                        (n = r.sent()),
                        i
                          ? [
                              2,
                              null == n
                                ? void 0
                                : n.filter(function (e) {
                                    return (null == e ? void 0 : e.key) === i;
                                  })[0],
                            ]
                          : [2, n]
                      );
                    case 3:
                      return (o = r.sent()), console.error(o), [3, 4];
                    case 4:
                      return [2];
                  }
                });
              });
            }),
            (t.addOrEditExtensionData = function (e, t, i) {
              return n(this, void 0, void 0, function () {
                var n;
                return r(this, function (r) {
                  switch (r.label) {
                    case 0:
                      return r.trys.push([0, 3, , 4]), [4, e.ready()];
                    case 1:
                      return (
                        r.sent(),
                        [
                          4,
                          e.Application.ActiveDocument.ContentControls.Item({
                            ID: t,
                          }).AddOrEditExtensionData([
                            { key: i.key, value: i.value },
                          ]),
                        ]
                      );
                    case 2:
                      return r.sent(), [3, 4];
                    case 3:
                      return (n = r.sent()), console.error(n), [3, 4];
                    case 4:
                      return [2];
                  }
                });
              });
            }),
            (t.removeExtensionData = function (e, t, a) {
              return n(this, void 0, void 0, function () {
                var n;
                return r(this, function (r) {
                  switch (r.label) {
                    case 0:
                      return r.trys.push([0, 3, , 4]), [4, e.ready()];
                    case 1:
                      return (
                        r.sent(),
                        [
                          4,
                          e.Application.ActiveDocument.ContentControls.Item({
                            ID: t,
                          }).RemoveExtensionData(o([], i(a), !1)),
                        ]
                      );
                    case 2:
                      return r.sent(), [3, 4];
                    case 3:
                      return (n = r.sent()), console.error(n), [3, 4];
                    case 4:
                      return [2];
                  }
                });
              });
            }),
            (t.pastControlFormatHTML = function (e, t) {
              return n(this, void 0, void 0, function () {
                var n, i, o, a, s;
                return r(this, function (r) {
                  switch (r.label) {
                    case 0:
                      return (
                        r.trys.push([0, 6, , 7]),
                        [
                          4,
                          this.app.ActiveDocument.ContentControls.Item({
                            ID: e,
                          }),
                        ]
                      );
                    case 1:
                      return [4, null == (n = r.sent()) ? void 0 : n.Range];
                    case 2:
                      return [4, (i = r.sent()).Start];
                    case 3:
                      return (o = r.sent()), [4, i.End];
                    case 4:
                      return (
                        (a = r.sent()),
                        [
                          4,
                          this.app.ActiveDocument.Range(o, a).PasteHtml({
                            HTML: t,
                          }),
                        ]
                      );
                    case 5:
                      return r.sent(), [3, 7];
                    case 6:
                      return (s = r.sent()), console.error(s), [3, 7];
                    case 7:
                      return [2];
                  }
                });
              });
            });
        },
        480: function () {},
      },
      t = {};
    function n(r) {
      var i = t[r];
      if (void 0 !== i) return i.exports;
      var o = (t[r] = { exports: {} });
      return e[r].call(o.exports, o, o.exports, n), o.exports;
    }
    (n.d = function (e, t) {
      for (var r in t)
        n.o(t, r) &&
          !n.o(e, r) &&
          Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
    }),
      (n.g = (function () {
        if ('object' == typeof globalThis) return globalThis;
        try {
          return this || new Function('return this')();
        } catch (e) {
          if ('object' == typeof window) return window;
        }
      })()),
      (n.o = function (e, t) {
        return Object.prototype.hasOwnProperty.call(e, t);
      }),
      (n.r = function (e) {
        'undefined' != typeof Symbol &&
          Symbol.toStringTag &&
          Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
          Object.defineProperty(e, '__esModule', { value: !0 });
      });
    var r = {};
    return (
      (function () {
        'use strict';
        var e = r;
        Object.defineProperty(e, '__esModule', { value: !0 }),
          (e.config = void 0);
        var t = n(620);
        e.config = function (e) {
          return new t.CTX(e).getInstance();
        };
      })(),
      r
    );
  })();
});
