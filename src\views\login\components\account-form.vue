<template>
  <div class="account-form-container">
    <a-form
      ref="loginForm"
      :model="userInfo"
      class="login-form"
      layout="vertical"
      style="margin-top: 16px"
      @submit="handleSubmit"
    >
      <a-form-item
        field="username"
        :rules="[{ required: true, message: $t('login.form.userName.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input
          v-model="userInfo.username"
          :placeholder="$t('login.form.userName.number.placeholder')"
        >
          <template #prefix>
            <icon-user />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item
        field="password"
        :rules="[{ required: true, message: $t('login.form.password.errMsg') }]"
        :validate-trigger="['change', 'blur']"
        hide-label
      >
        <a-input-password
          v-model="userInfo.password"
          :placeholder="$t('login.form.password.placeholder')"
          allow-clear
        >
          <template #prefix>
            <icon-lock />
          </template>
        </a-input-password>
      </a-form-item>
      <a-space :size="16" direction="vertical">
        <div class="login-form-password-actions">
          <a-checkbox
            checked="rememberPassword"
            :model-value="loginConfig.rememberPassword"
            @change="setRememberPassword as any"
          >
            {{ $t('login.form.rememberPassword') }}
          </a-checkbox>
          <a-link @click="changeLogin(LoginMethods.forget)">{{
            $t('login.form.forgetPassword')
          }}</a-link>
        </div>
        <a-button
          type="primary"
          html-type="submit"
          size="medium"
          long
          :loading="loading"
        >
          {{ $t('login.form.login') }}
        </a-button>
      </a-space>
    </a-form>
    <div class="register-link">
      <a-link @click="changeLogin(LoginMethods.register)">{{
        $t('login.register.link')
      }}</a-link>
    </div>
    <!-- <button @click="openModal">打开账户切换弹窗</button> -->

    <AccountSwitcherModal
      v-model:show-modal="showModal"
      :should-load="true"
      @select="handleAccountSelected"
    />
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import { useI18n } from 'vue-i18n';
  import { useStorage } from '@vueuse/core';
  import { useUserStore, useGlobalModeStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import type { LoginData } from '@/api/user';
  import pwdEncrypt from '@/utils/encryption/pwd';
  import { dotToSlash } from '@/utils/index';
  import LoginMethods from '../constant';
  import { getWpsToken, getWpsAppCode } from '../api';
  import { setWpsToken } from '@/utils/auth';
  import AccountSwitcherModal from '@/components/AccountSwitcherModal/AccountSwitcherModal.vue';

  const showModal = ref(false);
  const openModal = () => {
    // console.log('openModal called111111111111111111111111111111111111111111111111', showModal.value);
    showModal.value = true;
  };

  const emit = defineEmits(['changeLogin']);

  const changeLogin = (method: LoginMethods) => {
    emit('changeLogin', method);
  };

  const router = useRouter();
  const { t } = useI18n();
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();

  const loginConfig = useStorage('work-login-config', {
    // fawkes OWndZi1mKZdC
    rememberPassword: true,
    username: '',
    password: '',
  });
  const userInfo = reactive({
    username: loginConfig.value.username,
    password: loginConfig.value.password,
  });

  // 设置wpstoken
  const setWpsTokenHandle = async () => {
    const tokenParam = {
      grant_type: 'client_credentials',
      client_id: 'fusion',
      client_secret: 'fusion_secret',
    };
    const tokenData: any = await getWpsToken(tokenParam);
    setWpsToken(tokenData.access_token);
    // 获取cde系统在wps应用中台对应的code
    const wpsAppCode: any = (await getWpsAppCode({})).data;
    localStorage.setItem('wpsAppCode', wpsAppCode);
  };

  const globalModeStore = useGlobalModeStore();

  // 添加处理账号选择的方法
  const handleAccountSelected = ({
    account,
    isSameScene,
  }: {
    account: any;
    isSameScene: boolean;
  }) => {
    console.log('Selected account:', account);
    // 路由跳转
    const { redirect, ...othersQuery } = router.currentRoute.value.query;
    if (!account.id) {
      globalModeStore.changeGlobalMode('work');
      // 个人版
      router
        .push({
          path: '/dashboard',
          // path: dotToSlash(redirect as string) || '/dashboard',
          query: {
            ...othersQuery,
          },
        })
        .then(() => {
          // 路由跳转成功后关闭弹窗
          showModal.value = false;
        })
        .catch((err) => {
          console.error('路由跳转失败', err);
          // 即使路由跳转失败也关闭弹窗
          showModal.value = false;
          Message.error(t('login.navigation.failed'));
        });
    } else {
      // 企业版
      const targetPath = isSameScene
        ? dotToSlash(redirect as string) || '/home-page'
        : '/home-page';

      router
        .push({
          path: targetPath,
          query: {
            ...othersQuery,
          },
        })
        .then(() => {
          // 路由跳转成功后关闭弹窗
          showModal.value = false;
        })
        .catch((err) => {
          console.error('路由跳转失败', err);
          // 即使路由跳转失败也关闭弹窗
          showModal.value = false;
          Message.error(t('login.navigation.failed'));
        });
    }
  };

  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (loading.value) return;
    if (!errors) {
      setLoading(true);
      try {
        const { username, password } = values;
        const data = {
          username,
          password: pwdEncrypt(password),
          grant_type: 'password',
        };
        await userStore.login(data as LoginData);
        userStore.addToProject();
        console.log('login success', userStore.getCompanyId());
        // 登录成功后检查 companyId
        // if (!userStore.getCompanyId()) {
        // 如果没有 companyId，显示切换账号弹窗
        openModal();
        // } else {
        //   // 如果有 companyId，直接跳转
        //   const { redirect, ...othersQuery } = router.currentRoute.value.query;
        //   router.push({
        //     path: dotToSlash(redirect as string) || '/dashboard',
        //     query: {
        //       ...othersQuery,
        //     },
        //   });
        // }

        Message.success(t('login.form.login.success'));
        const { rememberPassword } = loginConfig.value;
        loginConfig.value.username = rememberPassword ? username : '';
        loginConfig.value.password = rememberPassword ? password : '';
        setWpsTokenHandle();
      } catch (err: any) {
        if (err?.response?.status === 400) {
          Message.error(err.response.data);
        } else if (typeof err === 'string') {
          Message.error(err);
        }
      } finally {
        setLoading(false);
      }
    }
  };
  const setRememberPassword = (value: boolean) => {
    loginConfig.value.rememberPassword = value;
  };
</script>

<script lang="ts">
  export default {
    name: 'AccountForm',
  };
</script>

<style lang="less" scoped>
  .account-form-container {
    height: 340px;
  }
  .login-form-password-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  :deep(.arco-btn-size-large) {
    height: 48px;
    border-radius: 8px;
  }
  :deep(.arco-input-wrapper) {
    height: 40px;
    border: 1px solid #c9cdd4;
    background-color: #ffffff;
    border-radius: 8px !important;
  }
  .register-link {
    text-align: center;
    margin-top: 16px;
    cursor: pointer;
    color: rgb(22, 93, 255);
  }
</style>
