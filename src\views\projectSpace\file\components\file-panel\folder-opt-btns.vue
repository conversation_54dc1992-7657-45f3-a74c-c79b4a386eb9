<template>
  <a-dropdown
    position="bl"
    @select="handleSelect($event)"
    :hide-on-select="false"
    v-model:popup-visible="popupVisible"
  >
    <icon-more-vertical
      class="icon-more-vertical"
      style="transform: rotate(90deg)"
    />
    <template #content>
      <!--      添加子文件夹-->
      <a-doption
        v-if="
          isCommunalFolder(nodeData) ||
          isCommunalChildren(nodeData) ||
          (nodeData.type === 'WIP' &&
            'parentId' in nodeData &&
            (!nodeData.sysType || nodeData.sysType === 5))
        "
        v-permission="
          isCommunalFolder(nodeData) || isCommunalChildren(nodeData)
            ? ''
            : `${nodeData.teamId}_${$btn.file.addFolder}`
        "
        value="add-folder"
      >
        <template #icon>
          <icon-plus />
        </template>
        <template #default>{{ $t('file-manage.add-folder') }}</template>
      </a-doption>
      <a-doption
        v-if="
          isCommunalChildren(nodeData) ||
          (nodeData.type === 'WIP' &&
            nodeData.parentId !== 0 &&
            !nodeData.sysType)
        "
        v-permission="
          isCommunalChildren(nodeData)
            ? ''
            : `${nodeData.teamId}_${$btn.file.renameFolder}`
        "
        value="rename-folder"
      >
        <template #icon>
          <icon-pen />
        </template>
        <template #default>{{ $t('file-manage.rename-folder') }}</template>
      </a-doption>
      <!--      上传文件-->
      <a-doption
        v-if="
          isCommunalFolder(nodeData) ||
          isCommunalChildren(nodeData) ||
          (nodeData.type === 'WIP' &&
            'parentId' in nodeData &&
            !nodeData.sysType) ||
          nodeData.sysType === 5
        "
        value="upload"
        v-permission="
          isCommunalFolder(nodeData) || isCommunalChildren(nodeData)
            ? ''
            : `${nodeData.teamId}_${$btn.file.upload}`
        "
      >
        <template #icon>
          <icon-upload />
        </template>
        <template #default>{{ $t('file-manage.upload') }}</template>
      </a-doption>
      <!--      下载源文件-->
      <a-doption
        v-if="
          isCommunalChildren(nodeData) ||
          ('parentId' in nodeData && !nodeData.sysType)
        "
        v-permission="
          isCommunalChildren(nodeData)
            ? ''
            : `${nodeData.teamId}_${$btn.file.download}`
        "
        value="download"
      >
        <template #icon>
          <icon-download />
        </template>
        <template #default>{{ $t('file-manage.download-source') }}</template>
      </a-doption>
      <!-- 分享 -->
      <a-doption
        v-if="
          nodeData.type === 'WIP' && 'parentId' in nodeData && !nodeData.sysType
        "
        v-permission="`${nodeData.teamId}_${$btn.file.shared}`"
        value="share-file"
      >
        <template #icon>
          <icon-share-alt />
        </template>
        <template #default>{{ $t('file-manage.share') }}</template>
      </a-doption>
      <!-- 删除 -->
      <a-popconfirm
        :content="
          $t('file-manage.confirm-delete-file') +
          (nodeData.folderId
            ? $t('file-manage.file')
            : $t('file-manage.folder')) +
          '？'
        "
        type="info"
        position="left"
        @ok="deleteFolder(nodeData, 'delete-folder')"
      >
        <a-doption
          v-if="
            (isCommunalChildren(nodeData) && userName === nodeData.createBy) ||
            (nodeData.parentId !== 0 &&
              nodeData.type === 'WIP' &&
              !nodeData.sysType)
          "
          v-permission="
            isCommunalChildren(nodeData) && userName === nodeData.createBy
              ? ''
              : `${nodeData.teamId}_${$btn.file.delete}`
          "
          value="delete-folder"
        >
          <template #icon>
            <icon-delete />
          </template>
          <template #default>{{ $t('file-manage.delete') }}</template>
        </a-doption>
      </a-popconfirm>
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
  import { PropType, toRefs, defineEmits, ref, inject, Ref } from 'vue';
  import { FileAndFolderNodeMessage } from '@/api/tree-folder';

  import { storeToRefs } from 'pinia';
  import useFileStore from '@/store/modules/file/index';

  import { downloadSource } from '@/views/projectSpace/file/hooks/events';
  import {
    filterSysTreeData,
    transformData,
    isCommunalFolder,
    isCommunalChildren,
  } from '@/views/projectSpace/file/utils';

  import { deleteApi, getFileChildrenAll } from '@/views/projectSpace/file/api';
  import { Notification } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';
  import { useUserStore } from '@/store';

  const fileStore = useFileStore();
  const { t } = useI18n();
  const userStore = useUserStore();

  const { username: userName } = storeToRefs(userStore);

  const props = defineProps({
    nodeData: {
      type: Object as PropType<FileAndFolderNodeMessage>,
      required: true,
    },
    teamFolderIdList: {
      type: String,
      default: '',
    },
  });
  const emits = defineEmits(['eventsHandle']);

  const { nodeData } = toRefs(props);
  const { newFolderName } = storeToRefs(fileStore);

  const popupVisible = ref(false);
  const route = useRoute();
  const projectId = route.params.projectId;

  function addFolders(data: any, event: string) {
    const isIndex = data.children.findIndex((item: any) => item.id === 'add');
    if (isIndex !== -1) {
      return;
    }
    newFolderName.value = '';
    const item = {
      name: '',
      parentId: data.id,
      projectId: data.projectId,
      teamId: data.teamId,
      type: data.type,
      isAdd: true,
      id: 'add',
    };

    if (isCommunalFolder(data)) {
      item.parentId = '0';
      item.projectId = projectId;
      item.type = 'COMMUNALSPACE';
    }

    if (data.children) {
      data.children.push(item);
    } else {
      data.children = [item];
    }
    emits('eventsHandle', event, data);
  }

  function renameFolder(data: any, event: string) {
    newFolderName.value = data.name;
    data.isEdit = true;
    emits('eventsHandle', event);
    popupVisible.value = false;
  }

  const shareModalData = inject<Ref<Record<string, any>>>('shareModalData');

  async function handleShare(record: FileAndFolderNodeMessage, event: string) {
    const res: any = await getFileChildrenAll(record.id!);
    const shareData = transformData(res.data).shareLinkDtoList[0];
    shareData.shareLinkDtoList = filterSysTreeData(shareData.shareLinkDtoList);
    shareModalData!.value = {
      show: true,
      shareData,
      shareType: 'rightBtn',
    };
  }

  function deleteFolder(data: any, event: string) {
    const params = {
      folderIds: [data.id],
      targetTeamId: data.teamId,
    };
    deleteApi(params)
      .then((res: any) => {
        if (res.code === 8000000) {
          Notification.success({
            id: 'delete',
            title: 'Success',
            content: t('file-manage.success'),
          });
          emits('eventsHandle', event, data);
        }
      })
      .catch((error) => {
        console.error('请求错误', error);
      })
      .finally(() => {
        popupVisible.value = false;
      });
  }

  // dropdown事件委托
  const handleSelect = async (event: any) => {
    switch (event) {
      case 'add-folder':
        popupVisible.value = false;
        addFolders(nodeData.value, event);
        break;
      case 'asc':
      case 'desc':
      case 'date':
        // order(nodeData, event);
        break;
      case 'rename-folder':
        renameFolder(nodeData.value, event);
        break;
      case 'upload':
        popupVisible.value = false;
        emits('eventsHandle', event);
        break;
      case 'download':
        popupVisible.value = false;
        emits('eventsHandle', event);
        downloadSource(nodeData.value);
        break;
      case 'delete-folder':
        break;
      case 'share-file':
        popupVisible.value = false;
        handleShare(nodeData.value, event);
        break;
      default:
        break;
    }
  };
</script>
