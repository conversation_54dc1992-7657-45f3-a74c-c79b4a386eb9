import { AttachmentDto, AttachmentFileDto } from '@/store/modules/file/types';

export function folderTransToTableView(folder: AttachmentDto) {
  const type = folder.type;
  let detailId = '';
  if (type === 'item') {
    detailId = folder.scheduleDetailId;
  } else if (type === 'meeting') {
    detailId = folder.folderId;
  }
  return {
    id: folder.folderId,
    isFolder: true,
    teamName: folder.teamId,
    updateName: folder.updateByName,
    updateTime: folder.updateDateItemOrMeeting,
    detailId,
    type,
    title: folder.title,
    child: folder.child || [],
    attachmentList: folder.attachmentFileMap?.attachmentFileInfoList || [],
  };
}

export function fileTransToTableView(file: AttachmentFileDto) {
  const type = file.type;
  let detailId = '';
  if (type === 'item') {
    detailId = file.scheduleDetailId;
  } else if (type === 'meeting') {
    detailId = file.correlationId;
  }
  return {
    id: file.attachmentId,
    isFolder: false,
    teamName: file.teamName,
    updateName: file.updateByName,
    detailId,
    updateTime: file.updateDateAttachment,
    type,
    fileToken: file.attachmentToken,
    title: file.attachmentName,
    child: [],
    attachmentList: [],
  };
}
