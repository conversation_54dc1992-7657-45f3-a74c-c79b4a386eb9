export function getRatio() {
  const {devicePixelRatio} = window;
  const UIWidth = 1920;
  let htmlWidth =  window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  if(htmlWidth < 1600) htmlWidth = 1600;
  return  (htmlWidth/UIWidth)  * devicePixelRatio
}
// 设置 rem 函数
function setRem () {
  const baseFontSize = 100;
  // 得到html的Dom元素
  const htmlDom = document.getElementsByTagName('html')[0];
  const fontSize = baseFontSize * getRatio();
  // 设置根元素字体大小
  htmlDom.style.fontSize=`${ fontSize }px`;
}

export default setRem;