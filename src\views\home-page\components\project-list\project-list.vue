<template>
  <div class="project-list">
    <div class="top-box">
      <list-title
        :title-text="$t('dashboard.project-table-title')"
        :show-button="false"
        class="title"
      />
      <div class="search-bar">
        <!-- <div style="margin-bottom: 10px">
        <span class="search-title">{{ $t('dashboard.project-name') }}</span>
        <a-input
          v-model="searchParams.projectName"
          :placeholder="$t('dashboard.please-enter')"
          style="width: 196px"
          allow-clear
          @keydown.enter="getList"
          @clear="getList"
        ></a-input>
      </div> -->
        <!--      <span class="search-title">项目名称</span>-->
        <!--      <a-input-->
        <!--        v-model="searchParams.projectName"-->
        <!--        placeholder="请输入名称查询"-->
        <!--        style="width: 196px; margin-left: 24px"-->
        <!--        @keydown.enter="getList"-->
        <!--      ></a-input>-->
        <!-- <a-button
        v-if="[0, 1].includes(user.admin)"
        type="primary"
        style="float: right"
        class="opt-btn"
        @click="createProject"
        >{{ $t('dashboard.create-roject') }}</a-button -->
        <a-row :gutter="16">
          <a-col :flex="1">
            <a-form
              ref="formModel"
              :model="searchParams"
              :rules="formRules"
              label-align="left"
            >
              <a-row :gutter="36">
                <a-col flex="215px">
                  <a-form-item
                    field="projectName"
                    :label="$t('dashboard.project-name')"
                    label-col-flex="35px"
                  >
                    <a-input
                      v-model="searchParams.projectName"
                      :placeholder="$t('dashboard.please-enter')"
                      style="width: 215px"
                      allow-clear
                      @keydown.enter="search"
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col flex="215px">
                  <a-form-item
                    field="organization"
                    :label="$t('dashboard.search-corporation')"
                    label-col-flex="35px"
                  >
                    <a-input
                      v-model="searchParams.organization"
                      :placeholder="$t('dashboard.please-enter')"
                      style="width: 215px"
                      allow-clear
                      @keydown.enter="search"
                    ></a-input>
                  </a-form-item>
                </a-col>
                <a-col flex="350px">
                  <a-form-item
                    field="projectProperties"
                    :label="$t('dashboard.project-properties')"
                    label-col-flex="70px"
                  >
                    <a-select
                      v-model="searchParams.projectProperties"
                      :placeholder="$t('dashboard.please-select')"
                      style="width: 180px"
                      allow-clear
                      @change="propertiesChange"
                    >
                      <a-option
                        v-for="item in projectProperties"
                        :key="`${item?.value}_${item?.label}`"
                        :value="item?.value"
                        :label="item?.label"
                      ></a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-col>
          <a-col :flex="'128px'" style="text-align: right">
            <a-space :size="8">
              <a-button type="primary" @click="search">{{
                $t('list.options.btn.search')
              }}</a-button>
              <a-button type="outline" @click="reset">{{
                $t('list.options.btn.clear')
              }}</a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- <a-divider :margin="16" style="margin-top: 4px" /> -->
      </div>
    </div>
    <!-- todo:项目标签和创建项目 -->
    <a-row :gutter="16" style="margin-bottom: 16px" justify="start">
      <a-col>
        <!-- <a-space :size="8"> -->
        <a-button
          v-if="userAdmin === '0' || userAdmin === '1'"
          type="primary"
          @click="createProject"
          >{{ $t('dashboard.create-roject') }}</a-button
        >
        <!-- </a-space> -->
      </a-col>
    </a-row>
    <div class="content">
      <a-table
        :data="projectList"
        :bordered="false"
        :stripe="false"
        :hoverable="false"
        :scroll="{ x: '100%', y: 'calc(100vh - 350px)' }"
        :pagination="pageConfig"
        @page-change="pageChange"
        @page-size-change="pageSizeChange"
        class="project-table"
      >
        <template #columns>
          <a-table-column
            :title="$t('dashboard.index')"
            :width="80"
            align="left"
            :ellipsis="true"
            :tooltip="true"
          >
            <template #cell="{ rowIndex }">
              {{
                (pageConfig.pageSize ? pageConfig.pageSize : 10) *
                  ((pageConfig.current ? pageConfig.current : 1) - 1) +
                (rowIndex + 1)
              }}
            </template>
          </a-table-column>
          <a-table-column
            :title="$t('dashboard.project-name')"
            :width="260"
            align="left"
            :ellipsis="true"
            :tooltip="true"
          >
            <template #cell="{ record }">
              <span class="name" @click="clickRow(record)">{{
                record.name
              }}</span>
            </template>
          </a-table-column>

          <a-table-column
            :title="$t('dashboard.project-location')"
            :width="200"
            align="left"
            :ellipsis="true"
            :tooltip="true"
          >
            <template #cell="{ record }">
              <span class="position" @click="clickRow(record)">{{
                record.position
              }}</span>
            </template>
          </a-table-column>

          <a-table-column
            :title="$t('dashboard.project-type')"
            :width="200"
            align="left"
            :ellipsis="true"
            :tooltip="true"
          >
            <template #cell="{ record }">
              <a-tag v-if="record.type" class="type-tag">
                <span class="tag-content">
                  <component
                    :is="iconMap[record.type] || iconMap[0]"
                    class="type-icon"
                  />
                  <span class="type-text">{{
                    ProjectTypeMaps[record.type] || record.type
                  }}</span>
                </span>
              </a-tag>
            </template>
          </a-table-column>

          <a-table-column
            :title="$t('dashboard.project-properties')"
            data-index="projectProperties"
            :width="140"
            align="left"
            :ellipsis="true"
            :tooltip="true"
          >
            <template #cell="{ record }">
              <a-tag
                v-if="[1, 2, 3, 4, 5].includes(record.projectProperties)"
                :class="`project-property project-property-${record.projectProperties}`"
              >
                <template v-if="record.projectProperties === 1">
                  {{ $t('dashboard.regularProjects') }}
                </template>
                <template v-else-if="record.projectProperties === 2">
                  {{ $t('dashboard.pilotProject') }}
                </template>
                <template v-else-if="record.projectProperties === 3">
                  {{ $t('dashboard.navigationProject') }}
                </template>
                <template v-else-if="record.projectProperties === 4">
                  {{ $t('dashboard.othersProject') }}
                </template>
                <template v-else-if="record.projectProperties === 5">
                  {{ $t('dashboard.training-project') }}
                </template>
              </a-tag>
            </template>
          </a-table-column>

          <a-table-column
            :title="$t('dashboard.project-code')"
            data-index="code"
            :width="160"
            align="left"
            :ellipsis="true"
            :tooltip="true"
          >
          </a-table-column>
          <a-table-column
            :title="$t('dashboard.create-time')"
            data-index="createDate"
            :width="200"
            align="left"
            :ellipsis="true"
            :tooltip="true"
          >
          </a-table-column>
          <a-table-column
            :title="$t('dashboard.creater')"
            data-index="createName"
            :width="150"
            align="left"
            :ellipsis="true"
            :tooltip="true"
          >
          </a-table-column>
          <a-table-column
            :title="$t('dashboard.search-corporation')"
            data-index="organization"
            :width="160"
            align="left"
            :ellipsis="true"
            :tooltip="true"
          >
          </a-table-column>
          <a-table-column
            :title="$t('dashboard.operations')"
            :width="100"
            align="center"
            :ellipsis="true"
            :tooltip="true"
          >
            <template #cell="{ record }">
              <a-popconfirm
                :content="$t('dashboard.confirm-delete')"
                position="left"
                @ok="remove(record)"
              >
                <a-tooltip :content="$t('dashboard.delete')">
                  <a-button type="text" v-if="userAdmin === '0' || userAdmin === '1'">
                    <img src="@/assets/images/delete.png" style="width: 20px" />
                  </a-button>
                </a-tooltip>
              </a-popconfirm>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <div class="components">
      <CreateProject
        v-model:visible="createDialogVisible"
        @submit="createSuccess"
      ></CreateProject>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref } from 'vue';
  import { PaginationProps, Notification } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  import { useGlobalModeStore, useUserStore } from '@/store';
  import { getProjectList, ProjectListParams } from '@/api/project';
  import {
    ProjectTypeMaps,
    projectProperties,
    ProjectCoordinateMaps,
    ProjectElevationMaps,
  } from '@/directionary/project';
  import ListTitle from '@/components/list-title/index.vue';

  import CityRoadEngineering from '@/assets/images/project-setting/UrbanRoadWorks.svg';
  import HousingConstructionEngineering from '@/assets/images/project-setting/HouseConstructionWorks.svg';
  import UrbanRailTransitEngineering from '@/assets/images/project-setting/UrbanRailTransitEngineering.svg';
  import PortEngineering from '@/assets/images/project-setting/PortEngin.svg';
  import HighwayEngineering from '@/assets/images/project-setting/HighwayWorks.svg';
  import WaterTransportEngineering from '@/assets/images/project-setting/WaterTransportEngineering.svg';
  import TunnelingEngineering from '@/assets/images/project-setting/TunnelingWorks.svg';
  import BridgeEngineering from '@/assets/images/project-setting/BridgeEngineering.svg';
  import CulvertWorks from '@/assets/images/project-setting/CulvertWorks.svg';
  import WaterwayEngineering from '@/assets/images/project-setting/WaterwayEngineering.svg';
  import AirportRoadEngineering from '@/assets/images/project-setting/AirportRoadEngineering.svg';

  // import { setProject } from '@/utils/project-listener';
  import { AxiosResponseCustom, Paging } from '@/types/global';
  import CreateProject from './create-project.vue';
  import { deleteProject } from './api';
  import { useI18n } from 'vue-i18n';
  import {
    storeCurrentProjectId,
    storeCurrenttUserId,
  } from '@/api/storage-project';
  import { setLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';


  const userAdmin = localStorage.getItem('userAdmin'); // 0 系统管理员 -1 系统普通成员
  const userId = getUserId() || '';
  const { t } = useI18n();
  const formRules = reactive({});
  const router = useRouter();
  const globalModeStore = useGlobalModeStore();
  const globalMode = computed(() => {
    return globalModeStore.getGlobalMode;
  });
  const userStore = useUserStore();
  // companyId有值就代表是企业账号，没值就代表是个人账号，个人账号没有项目
  const companyId = computed(() => userStore.companyId);
  const user = ref(userStore.userInfo);

  const createDialogVisible = ref(false);
  const createProject = () => {
    createDialogVisible.value = true;
  };

  const searchParams = reactive({
    projectName: '',
    organization: '',
    projectProperties: '',
  });

  const projectList = ref<object[]>([]);

  const pageConfig: PaginationProps = reactive({
    showTotal: true,
    showMore: false,
    showJumper: true,
    showPageSize: true,
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    total: 100,
  });

  const getList = () => {
    const params: ProjectListParams = {
      pageNo: pageConfig.current || 1,
      pageSize: pageConfig.pageSize || 20,
      projectType: 0,
      name: searchParams.projectName || '',
      organization: searchParams.organization,
      projectProperties: searchParams.projectProperties,
    };
    getProjectList(params).then(
      (res: AxiosResponseCustom<Paging<any>, any>) => {
        if (res.code === 8000000) {
          projectList.value = res.data.list || [];
          pageConfig.total = res.data.total || projectList.value.length || 10;
        }
      }
    );
  };
  const search = () => {
    pageConfig.current = 1;
    getList();
  };
  // 新增组织机构
  const reset = () => {
    pageConfig.current = 1;
    searchParams.projectName = '';
    searchParams.organization = '';
    searchParams.projectProperties = '';
    getList();
  };
  const clickRow = (row: any) => {
    userStore.setProjectTemplate('0');
    const mode: string = globalMode.value;
    const path = `project/${row.id}/home`;
    // setProject(row);
    router.push({
      path,
      // name: 'project',
      // params: {
      //   projectId: row.id,
      // },
    });
    globalModeStore.changeGlobalMode('project');
    setLocalstorage(`last_project_${userId}`, row.id);
    storeCurrentProjectId(row.id);
    storeCurrenttUserId();
  };
  const pageSizeChange = (size: number): void => {
    pageConfig.pageSize = size;
    getList();
  };
  const pageChange = (current: number): void => {
    pageConfig.current = current;
    getList();
  };

  const createSuccess = (state: boolean) => {
    if (state) {
      getList();
    }
  };

  const remove = async (record: any) => {
    const res: any = await deleteProject(record.id);
    if (res.code === 8000000) {
      Notification.success({
        id: 'deleteProject',
        title: 'Success',
        content: t('delete-successful'),
      });
      getList();
    } else {
      Notification.error({
        id: 'deleteProject',
        title: 'error',
        content: t('delete-failed'),
      });
    }
  };

  const propertiesChange = () => {
    pageConfig.current = 1;
    getList();
  };
  // 修改iconMap的定义
  const iconMap: Record<number, any> = {
    1: HighwayEngineering,
    2: CityRoadEngineering,
    3: AirportRoadEngineering,
    4: UrbanRailTransitEngineering,
    5: BridgeEngineering,
    6: CulvertWorks,
    7: PortEngineering,
    8: WaterwayEngineering,
    9: TunnelingEngineering,
    10: HousingConstructionEngineering,
    11: WaterTransportEngineering,
    0: CityRoadEngineering,
  };

  onMounted(() => {
    if (companyId.value) {
      getList();
    }
  });
</script>

<style scoped lang="less">
  .project-list {
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: var(--color-bg-2);
    border: 1px solid var(--color-neutral-3);
  }
  .search-title {
    display: inline-block;
    width: 50px;
    height: 22px;
    font-size: 14px;
    color: #000000;
    line-height: 22px;
  }

  .top-box {
    border-bottom: 1px solid #e5e6eb;
    margin-bottom: 20px;
    flex-shrink: 0;
  }

  .search-bar {
    position: relative;
    flex-shrink: 0;
    background-color: var(--color-bg-2);
    // padding: 16px;
    border-radius: 4px;
    .opt-btn {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: var(--color-bg-2);
    border-radius: 4px;
  }

  .project-table {
    height: 100%;
    :deep(.arco-table-container) {
      height: 100%;
      background-color: var(--color-bg-2);
    }
    :deep(.arco-table-pagination) {
      margin-top: 16px;
      padding: 0;
      background-color: var(--color-bg-2);
    }
    :deep(.arco-table-th) {
      background-color: var(--color-fill-2);
      font-weight: 500;
    }
    // :deep(.arco-table-tr) {
    //   &:hover {
    //     background-color: var(--color-fill-2);
    //   }
    // }
  }

  .name {
    color: rgb(var(--arcoblue-6));
    cursor: pointer;
    // &:hover {
    //   text-decoration: underline;
    // }
  }
  // 统一标签基础样式
  .project-property {
    height: 24px;
    font-family: PingFang SC, sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    border-radius: 2px;
    padding: 0 8px;
    border: none;

    // 不同状态的样式
    &-1 {
      background: #f2f3f5;
      color: #4e5969;
    }
    &-2 {
      background: #ffe8f1;
      color: #f5319d;
    }
    &-3 {
      background: #e8f4ff;
      color: #165dff;
    }
    &-4 {
      background: #e8ffea;
      color: #00b42a;
    }
    &-5 {
      background: #fff7e8;
      color: #ff7d00;
    }
  }

  .type-tag {
    height: 24px;
    border-radius: 2px;
    border: 1px solid #0fc6c2;
    font-family: PingFang SC, sans-serif;
    font-weight: 500;
    font-size: 14px;
    color: #0fc6c2;
    line-height: 22px;
    background: #e6f7ff;
    padding: 0 8px;
    display: inline-flex;
    align-items: center;

    .tag-content {
      display: flex;
      align-items: flex-end;
      gap: 4px;
      height: 100%;
    }

    .type-icon {
      width: 14px;
      height: 14px;
      flex-shrink: 0;
      margin-bottom: 3px;
    }

    .type-text {
      display: inline-block;
      line-height: 22px;
    }
  }
  :deep(.arco-table-content .arco-scrollbar:nth-child(2)) {
    height: 100%;
  }
  :deep(.arco-table-header + .arco-scrollbar-track-direction-horizontal) {
    bottom: -510px;
  }
  :deep(.arco-input-wrapper) {
    border: 1px solid #c9cdd4;
    background-color: transparent;
  }

  :deep(.arco-select-view) {
    border: 1px solid #c9cdd4;
    background-color: transparent;
  }
</style>
