import axios from 'axios';

export function queryProCreatorList(params: any) {
  return axios.get<string>('/cde-collaboration/user/adminList', {
    params,
  });
}
// 用户注册（可覆盖）
export function ApplyApplication(data: any) {
  return axios.post<string>('/cde-collaboration/user/register-blue', data);
}

// export function getChbimKey() {
//   return axios({
//     method: 'get',
//     baseURL: '/chbim',
//     url: '/baseIP/getPubKey',
//   });
// }

/** 获取 chbim 云平台公钥 */
export function getChbimKey() {
  return axios.get<string>('/cde-collaboration/chBim/getchBimPubKey');
}

// export function getChbimToken(data: { signature: string }) {
//   return axios({
//     method: 'post',
//     baseURL: '/chbim',
//     url: '/baseIP/verifySignature',
//     data,
//   });
// }

/** 获取 chbim 云平台 token */
export function getChbimToken(data: any) {
  return axios.post('/cde-collaboration/chBim/verifySignature', data);
}

// export function sysUserRegistration(token: string, data: object) {
//   return axios({
//     method: 'post',
//     baseURL: '/chbim',
//     url: '/baseIP/sysUserRegister/add',
//     data,
//     headers: {
//       Authorization: `Bearer ${token}`,
//     },
//   });
// }

/** chbim 云平台 用户注册 */
export function sysUserRegistration(data: object) {
  return axios({
    method: 'post',
    url: '/cde-collaboration/chBim/sysUserRegister',
    data,
  });
}

/** 公路数字化方案设计系统用户注册 */
export function HDSDSRegistration(data: any) {
  return axios.post(`/cde-collaboration/user/registerHighroad`, data);
}

/** 获取应用申请列表 */
export function registerBlueList(params: {
  queryValue: string;
  pageNo: number;
  pageSize: number;
}) {
  return axios.get<any[]>('/cde-collaboration/user/register-blue-page', {
    params,
  });
}

/** 应用申请数据修改 */
export function ApplyCoverage(data: any) {
  return axios.post<any[]>('/cde-collaboration/user/registerBlueCover', data);
}

/** 应用申请数据删除 */
export function ApplyDlete(applyId: string) {
  return axios.post<string>(
    `/cde-collaboration/user/deleteApplyUser?applyId=${applyId}`
  );
}

/** 通过手机号、邮箱获取覆盖状态 */
export function getIsCoverState(data: any) {
  return axios.post<any[]>('/cde-collaboration/user/registerBlueVerify', data);
}
