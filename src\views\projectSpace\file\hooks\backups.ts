import { FolderMessage } from '@/api/tree-folder';
import { TeamRecord } from '@/views/project-setting/team/api';
import { useI18n } from 'vue-i18n';

export default function useI18nHandleName() {
  const { locale } = useI18n();
  const i18Map = new Map<string, 'englishName' | 'name'>()
    .set('zh-CN', 'name')
    .set('en-US', 'englishName');

  function i18FolderName(record: FolderMessage) {
    if (!record) return;
    return record[i18Map.get(locale.value)!] || record.name;
    // if (record.sysType && [1, 2, 3, 4, 5].includes(record.sysType)) {
    //   return record[i18Map.get(locale.value)!] || record.name;
    // } else {
    //   return record.name;
    // }
  }

  function i18TeamName(record: TeamRecord) {
    if (record.defaultTeam === 0) {
      return record[i18Map.get(locale.value)!] || record.name;
    } else {
      return record.name;
    }
  }

  return {
    i18FolderName,
    i18TeamName,
  };
}
