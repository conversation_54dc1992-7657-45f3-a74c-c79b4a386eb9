<template>
  <div class="set-up-container">
    <commonTabs v-model="tabKey" :tabs="[]"></commonTabs>

    <div class="main">
      <a-row :gutter="30" class="content">
        <a-col :span="4" class="menu-col">
          <div class="menu-demo">
            <a-menu
              mode="pop"
              :default-selected-keys="['personalInfo']"
              class="aside"
            >
              <a-menu-item
                v-for="menu in menuList"
                :key="menu.key"
                class="asideItem"
                @click="activeTab = menu.key"
              >
                <template #icon>
                  <component :is="menu.icon" />
                </template>
                {{ t(`setup.menu.${menu.key}`) }}
              </a-menu-item>
            </a-menu>
            <!-- <a-button class="exitButton" @click="handleLogout">
              {{ t('setup.logout') }}
            </a-button> -->
          </div>
        </a-col>
        <a-col :span="20" class="set-up-content">
          <component
            :is="currentComponent"
            v-if="currentComponent"
            class="compontent"
          ></component>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import MemberManagement from './component/member-management.vue';
  import organization from '@/views/organization/division/index.vue';
  import user from '@/views/user-center/index.vue';
  import Role from '@/views/role/index.vue';
  import Permission from '@/views/permissions/index.vue';
  import PersonalInfo from './component/personal-info.vue';
  import SecuritySettings from './component/security-settings.vue';

  import { useI18n } from 'vue-i18n';
  import commonTabs from '@/components/common-tabs/index.vue';
  // 导入图标
  import personalInfoIcon from '@/assets/images/dashboard/personalInfoIcon1.svg';
  import securityIcon from '@/assets/images/dashboard/securityIcon.svg';
  import memberIcon from '@/assets/images/dashboard/memberIcon.svg';
  import organizationIcon from '@/assets/images/setting/organization-chart.svg';
  import userManageIcon from '@/assets/images/dashboard/userManageIcon.svg';
  import { useUserStore, useGlobalModeStore } from '@/store';

  const { t } = useI18n();

  const globalModeStore = useGlobalModeStore();

  const tabKey = ref('setUp');

  const menuListAll = ref([
    {
      title: t('setup.menu.personalInfo'),
      key: 'personalInfo',
      icon: personalInfoIcon,
    },
    {
      title: t('setup.menu.securitySettings'),
      key: 'securitySettings',
      icon: securityIcon,
    },
    {
      title: t('setup.menu.groupMember'),
      key: 'groupMember',
      icon: memberIcon,
    },
    {
      title: t('setup.menu.organization'),
      key: 'organization',
      icon: organizationIcon,
    },
    {
      title: t('setup.menu.user'),
      key: 'user',
      icon: userManageIcon,
    },
    {
      title: t('setup.menu.permission'),
      key: 'permission',
      icon: memberIcon,
    },
    {
      title: t('setup.menu.role'),
      key: 'role',
      icon: organizationIcon,
    },
  ]);
  const menuList = ref([]);

  const userStore = useUserStore();

  /**
   * 统一处理导航栏菜单显示逻辑
   * 根据用户权限和全局模式决定显示哪些菜单项
   */
  const updateMenuList = () => {
    // 获取当前全局模式
    const currentMode = globalModeStore.getGlobalMode;
    // 先获取基础菜单列表
    let filteredMenu = [...menuListAll.value];

    // 根据用户权限过滤菜单，这里配置的是非管理员身份
    if (userStore.admin !== 0 && userStore.admin !== 3) {
      filteredMenu = filteredMenu.filter(
        (item) => item.key !== 'user' && item.key !== 'organization'
      );
    }

    // 根据身份再次进行过滤，这里配置的是非企业身份
    if (!userStore.companyId) {
      filteredMenu = filteredMenu.filter(
        (item) => item.key !== 'user' && item.key !== 'organization'
      );
    }

    if (userStore.admin !== 0) {
      filteredMenu = filteredMenu.filter(
        (item) => !['role', 'permission'].includes(item.key)
      );
    }

    // 根据全局模式过滤菜单，这里配置的是项目空间
    if (currentMode === 'project') {
      filteredMenu = filteredMenu.filter((item) => item.key !== 'groupMember');
    }

    // 更新菜单列表
    menuList.value = filteredMenu;

    console.log('更新菜单列表:', {
      mode: currentMode,
      admin: userStore.admin,
      companyId: userStore.companyId,
      menuItems: filteredMenu.map((item) => item.key),
    });
  };

  // 监听可能影响菜单显示的所有状态变化
  watch(
    [
      () => userStore.admin,
      () => userStore.companyId,
      () => globalModeStore.getGlobalMode,
    ],
    () => {
      updateMenuList();
    },
    { immediate: true }
  );

  const activeTab = ref('personalInfo');

  const componentMap = {
    personalInfo: PersonalInfo,
    securitySettings: SecuritySettings,
    groupMember: MemberManagement,
    organization,
    user,
    role: Role,
    permission: Permission,
  };

  const currentComponent = computed(() => {
    return componentMap[activeTab.value];
  });
</script>

<style scoped lang="less">
  .set-up-container {
    background-color: #ffffff;
    padding: 16px 20px;
    border: none;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .main {
    flex: 1;
    display: flex;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    overflow: hidden;
  }

  .general-card {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
  }

  .content {
    flex: 1;
    display: flex;
    width: 100%;
    margin: 0 !important;
    background-color: #ffffff;
  }

  .menu-col {
    height: 100%;
    display: flex;
    align-items: flex-start;
    padding: 20px 0 0 20px !important;
    width: 220px;
  }

  .menu-demo {
    display: block;
    overflow: hidden;
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #ffffff;

    :deep(.arco-menu.arco-menu-light) {
      background-color: #ffffff !important;
    }
  }
  // :deep(.arco-menu-selected) {
  //     // border-radius: 8px;
  //     // height: 48px;
  //     color: #1d2129;
  //     // background-color: #e8f2ff;
  //     // border: 1px solid #ededed;
  //   }

  .menu-demo::before,
  .menu-demo::after {
    content: '';
    display: table;
    clear: both;
  }

  .aside {
    height: 100%;
    width: 100%;
    background-color: #ffffff;
    box-sizing: border-box;

    :deep(.arco-menu-inner) {
      background-color: #ffffff;
    }

    :deep(.icon) {
      fill: #4e5969 !important;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    :deep(.arco-menu-item) {
      color: #4e5969;
      font-size: 18px;
      height: 40px !important;
      margin-bottom: 8px;

      .arco-icon {
        color: #4e5969;
      }


    }
    :deep(.arco-menu-item.arco-menu-selected),
    :deep(.arco-menu-item.arco-menu-selected .arco-menu-title) {
      color: #1d2129 !important;
      background-color: #e8f2ff !important;
    }
    :deep(.arco-menu-item.arco-menu-selected .arco-icon),
    :deep(.arco-menu-item.arco-menu-selected .icon) {
      color: #1d2129 !important;
      fill: #1d2129 !important;
    }
  }

  .aside .asideItem {
    width: 100%;
    height: 40px;
    border-radius: 6px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 450;
    font-size: 18px;
    // color: #4e5969;
    line-height: 30px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .exitButton {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    position: absolute;
    bottom: 20px;
    display: block;
  }

  .set-up-content {
    height: 100%;
    overflow: hidden;
    padding: 0 !important;
    border-left: 1px solid #d9d9d9;
    width: calc(100% - 240px);
  }

  .compontent {
    height: 100%;
    overflow: auto;
  }

  .btn {
    color: #4e5969;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      color: var(--color-text-1);
    }
  }
</style>
