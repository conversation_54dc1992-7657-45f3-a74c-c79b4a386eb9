<template>
  <a-input
    v-model="inputVal"
    :style="{ width: '100%' }"
    :placeholder="props.placeholder || $t('please-enter')"
    v-bind="$attrs"
    :show-word-limit="props.showWordLimit"
    :max-length="props.maxLength"
    @input="handleInput"
    @clear="clearInput"
  />
</template>

<script lang="ts" setup>
  import { ref, defineEmits, defineProps } from 'vue';

  const props = defineProps({
    placeholder: {
      type: String,
    },
    maxLength: {
      type: Number,
      default: 255,
      required: false,
    },
    showWordLimit: {
      type: Boolean,
      default: false,
      required: false,
    },
  });

  const inputVal = ref('');
  const emit = defineEmits(['update:modelValue']);

  const handleInput = (val: any) => {
    inputVal.value = val.trim(); // 处理输入数据，去除两端空格
    inputVal.value = inputVal.value.substr(0, props.maxLength);
    emit('update:modelValue', inputVal.value); // 发射处理后的数据给父组件
  };

  const clearInput = () => {
    inputVal.value = '';
    emit('update:modelValue', '');
  };
</script>
