import { ref } from 'vue';

export default function useUIState() {
  // 定义UI状态管理对象
  const uiState = ref(
    new Map<
      string,
      {
        isEditing: boolean;
        showOptions: boolean;
        isDeleting: boolean;
      }
    >()
  );

  // 获取UI状态
  const getItemState = (id: string) => {
    if (!uiState.value.has(id)) {
      uiState.value.set(id, {
        isEditing: false,
        showOptions: false,
        isDeleting: false,
      });
    }
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return uiState.value.get(id)!;
  };

  // 清空UI状态
  const clearUIState = () => {
    uiState.value.clear();
  };

  return {
    uiState,
    getItemState,
    clearUIState,
  };
}
