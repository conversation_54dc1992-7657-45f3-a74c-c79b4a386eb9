import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { Message } from '@arco-design/web-vue';
import { useRoute } from 'vue-router';
import { useKnowledgeBaseStore2 } from '@/store';
import {
  getFolderList,
  getSearchFile,
  deleteFolder,
  deleteFile,
  renameFolder,
  renameFile,
} from '../api';
import {
  fileDownload,
  download,
} from '@/components/file-image/hooks/dropdow-events';
import type { Node } from '../types';
import folderIcon from '@/assets/images/knowledge-base/folder-icon.svg?url';
import pdfIcon from '@/assets/images/knowledge-base/pdf.svg?url';
import docIcon from '@/assets/images/knowledge-base/doc.svg?url';
import i18n from '@/locale/index';

const { t } = i18n.global;

export default function useFolderContent() {
  const route = useRoute();
  const knowledgeBaseStore2 = useKnowledgeBaseStore2();
  const { folderId, isBaseEmpty } = storeToRefs(knowledgeBaseStore2);

  const allBaseFolderData = ref<Node>();
  const folderList = ref<Array<Node>>([]);
  const fileList = ref<Array<Node>>([]);
  const tableLoading = ref(false);

  // // 排序方法
  // const sortByUpdateDate = (nodeList: Array<Node>, asc: boolean) => {
  //   if (asc) {
  //     nodeList.sort((a: Node, b: Node) =>
  //       a.updateDate.localeCompare(b.updateDate)
  //     );
  //   } else {
  //     nodeList.sort((a: Node, b: Node) =>
  //       b.updateDate.localeCompare(a.updateDate)
  //     );
  //   }
  // };

  // 处理文件夹
  const handleFolder = (folder: any) => {
    folder.type = 'folder';
    // 处理子文件夹
    folder.children?.forEach((subFolder: any) => {
      handleFolder(subFolder);
    });

    // 处理子文件
    folder.fileList?.forEach((file: any) => {
      file.parentId = folder.id;
    });

    if (folder.fileList && folder.fileList.length) {
      if (!folder.children) {
        folder.children = []; // 子节点不能有children属性
      }
      // 将文件移动到children中
      folder.children.push(...folder.fileList);
    }
    if (!folder.children) {
      delete folder.children;
    }
    delete folder.fileList;
  };

  // 查询知识库全量内容
  const queryFolderContent = async (
    // eslint-disable-next-line default-param-last
    isAll = true,
    onComplete?: () => void
  ): Promise<Node | undefined> => {
    const { rootFolderId } = route.params;
    try {
      tableLoading.value = true;
      const params = {
        folderId: rootFolderId as string,
        fullTree: isAll, // 查询全量
      };

      const res = await getFolderList(params);
      if (res.status) {
        const rootFolder = JSON.parse(JSON.stringify(res.data));
        handleFolder(rootFolder);
        allBaseFolderData.value = rootFolder;
        // console.log(rootFolder);

        // folderList.value = [];
        // fileList.value = [];

        // // 处理文件夹数据
        // const folders = res.data.children;
        // folderList.value = folders.map((folder: any) => ({
        //   id: folder.id,
        //   name: folder.name,
        //   parentId: folder.parentId,
        //   type: '文件夹',
        //   updateDate: folder.updateDate,
        //   isFolder: true,
        //   isEdit: false,
        //   isDelete: false,
        //   showAllOptions: false,
        // }));

        // sortByUpdateDate(folderList.value, false);

        // // 处理文件数据
        // const files = res.data.fileList;
        // fileList.value = files.map((file: any) => ({
        //   id: file.id,
        //   name: file.name,
        //   parentId: folderId.value,
        //   type: file.type,
        //   size: file.fileSize,
        //   fileToken: file.ossToken,
        //   updateDate: file.updateDate,
        //   isFolder: false,
        //   isEdit: false,
        //   isDelete: false,
        //   showAllOptions: false,
        //   aiStatus: file.aiStatus,
        // }));

        // sortByUpdateDate(fileList.value, false);

        // allBaseFolderData.value = [...folderList.value, ...fileList.value];
        // if (res.data?.parentId === '-1') {
        //   knowledgeBaseStore2.setIsBaseEmpty(
        //     allBaseFolderData.value.length === 0
        //   );
        // }

        // 执行回调
        onComplete?.();
      }
    } catch (error) {
      Message.error(t('knowledgenew.query-file-error-tips'));
      console.error('Query folder content error:', error);
    } finally {
      tableLoading.value = false;
      knowledgeBaseStore2.setSearchInfo(false);
    }
    return allBaseFolderData.value;
  };

  // 检查文件夹名称是否正确
  const checkFolderName = async (name: string, parentFolderId: string) => {
    if (!name) {
      Message.warning(t('knowledgenew.input-folder-name-tips'));
      return false;
    }

    // 名称长度限制
    if (name.length > 255) {
      Message.warning(t(t('knowledgenew.folder-name-length-no-exceed')));
      return false;
    }

    // 禁用字符校验
    const pattern = /^[^\\/:*?"<>|]+$/;
    const regResult = pattern.test(name);
    if (!regResult) {
      Message.warning(t('knowledgenew.input-file-rule1-tips'));
      return false;
    }

    // 名称重复校验
    const queryRes = await getFolderList({
      folderId: parentFolderId,
      fullTree: false,
    });
    if (queryRes.status) {
      const isExist = queryRes.data?.children.find(
        (folder: any) => folder.name === name
      );
      if (isExist) {
        Message.warning(t('knowledgenew.folder-name-exists'));
        return false;
      }
    }
    return true;
  };

  // 检查文件名称是否正确
  const checkFileName = async (name: string, parentFolderId: string) => {
    if (!name) {
      Message.warning(t('knowledgenew.input-file-name-tips'));
      return false;
    }

    // 名称长度限制
    if (name.length > 200) {
      Message.warning(t('knowledgenew.file-name-length-no-exceed'));
      return false;
    }

    // 名称重复校验
    const queryRes = await getFolderList({
      folderId: parentFolderId,
      fullTree: false,
    });
    if (queryRes.status) {
      const isExist = queryRes.data?.fileList.find(
        (file: any) => file.name === name
      );
      if (isExist) {
        Message.warning(t('knowledgenew.file-name-exists'));
        return false;
      }
    }
    return true;
  };

  // 重命名文件/文件夹
  const renameFileOrFolder = async (record: Node, newName: string) => {
    let flag = false;
    try {
      let res: any;
      if (record.type === 'folder') {
        const param: any = {
          folderId: record.id,
          name: newName,
        };
        res = await renameFolder(param);
      } else {
        const param: any = {
          fileId: record.id,
          name: newName,
        };
        res = await renameFile(param);
      }

      if (res.status) {
        Message.success(t('knowledgenew.rename-success'));
        flag = true;
      } else {
        console.error('重命名失败：', res.message);
      }
    } catch (error) {
      console.error('重命名失败：', error);
    }
    return flag;
  };

  // 下载文件
  const downloadBtnClick = (record: Node) => {
    Message.info(t('knowledgenew.file-downloading'));
    fileDownload({ fileToken: record.ossToken || '' }).then((res: any) => {
      download({ name: record.name }, res.data);
    });
  };

  // 删除文件夹/文件
  const deleteFolderOrFile = async (record: Node) => {
    let flag = false;
    try {
      if (record.type === 'folder') {
        // 删除文件夹
        const res = await deleteFolder({ folderId: record.id || '' });
        if (res.status) {
          Message.success(t('knowledgenew.delete-folder-success'));
          flag = true;
        }
      } else {
        // 删除文件
        const res = await deleteFile({ fileId: record.id || '' });
        if (res.status) {
          Message.success(t('knowledgenew.delete-file-success'));
          flag = true;
        }
      }
    } catch (error) {
      flag = false;
      console.error('Delete folder or file error:', error);
    }
    return flag;
  };

  // 获取图标
  const getFileIcon = (type: string) => {
    let fileIcon;
    switch (type) {
      case '文件夹':
        fileIcon = folderIcon;
        break;
      case 'PDF':
        fileIcon = pdfIcon;
        break;
      case 'DOC':
        fileIcon = docIcon;
        break;
      default:
        fileIcon = folderIcon;
    }
    return fileIcon;
  };

  // 搜索
  const querySearchFile = async (data: any) => {
    tableLoading.value = true;
    let files = [];
    try {
      const param: any = {
        fileName: data.fileName,
        kbId: data.kbId,
        pageParam: {},
      };
      const res = await getSearchFile(param);
      if (res.status) files = res.data?.list || [];
      tableLoading.value = false;
    } catch (error) {
      Message.error(t('knowledgenew.search-fail'));
    } finally {
      tableLoading.value = false;
    }
    return files;
  };

  return {
    folderList,
    fileList,
    tableLoading,
    queryFolderContent,
    // sortByUpdateDate,
    downloadBtnClick,
    getFileIcon,
    checkFolderName,
    checkFileName,
    renameFileOrFolder,
    querySearchFile,
    deleteFolderOrFile,
  };
}
