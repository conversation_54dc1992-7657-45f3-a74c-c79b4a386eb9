<template>
  <div v-if="visible" ref="uploadPanelRef" class="upload-panel">
    <div class="panel-header">
      <span class="title">{{ title }}</span>
      <icon-close
        style="
          float: right;
          margin-right: 0px;
          margin-top: 8px;
          cursor: pointer;
        "
        @click="close"
      />
    </div>
    <a-scrollbar style="height: 420px; overflow: auto">
      <div v-if="uploadFileList.length" class="panel-file-area">
        <div
          v-for="(item, index) in uploadFileList"
          :key="item.uid"
          class="panel-file-item"
        >
          <img
            :src="folderIcon"
            alt=""
            style="width: 30px; height: 30px; margin-top: 8px"
          />
          <div class="panel-file-content">
            <div class="file-name">{{ item.name }}</div>
            <div style="display: flex; margin-top: 6px">
              <div style="width: 190px">
                <a-progress
                  :percent="(item.percent / 100).toFixed(1)"
                  size="large"
                ></a-progress>
              </div>
              <span
                style="
                  display: inline-block;
                  margin-left: 12px;
                  font-weight: lighter;
                  flex: 1;
                  text-align: center;
                "
                >{{ fileStatus[String(item.state)] }}</span
              >
            </div>
          </div>
          <div class="panel-operation">
            <icon-pause-circle
              v-if="item.state === 2"
              style="margin-left: 8px"
              :size="18"
              @click="pauseUpload(index)"
            />
            <icon-play-circle
              v-if="item.state === 3"
              style="margin-left: 8px"
              :size="18"
              @click="restartUpload(index)"
            />
            <icon-delete
              style="margin-left: 8px"
              :size="18"
              @click="cancelUpload(index)"
            />
          </div>
        </div>
      </div>
      <div v-else class="panel-empty-area">
        <a-empty description="暂无文件上传" />
      </div>
    </a-scrollbar>

    <div class="panel-footer">
      <a-upload
        action="/"
        multiple
        :show-file-list="false"
        :auto-upload="false"
        style="width: 100%"
        :file-list="uploadList"
        @change="fileChange"
      >
        <template #upload-button>
          <a-button style="width: 100%">
            <template #icon>
              <icon-share-external :size="16" />
            </template>
            继续上传
          </a-button>
        </template>
      </a-upload>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineEmits, defineProps, ref, watch, nextTick, toRaw } from 'vue';
  import { useUploadFileStore, useKnowledgeBaseStore2 } from '@/store';

  import { fileStatus } from '@/directionary/file-upload';
  import folderIcon from '@/assets/images/knowledge-base/folder.svg?url';
  import { storeToRefs } from 'pinia';

  const uploadPanelRef: any = ref(null);

  const uploadList = ref([]);
  const uploadStore = useUploadFileStore();
  const KnowledgeBaseStore2 = useKnowledgeBaseStore2();
  const { uploadFileList } = storeToRefs(uploadStore);
  const { selectedKeys } = storeToRefs(KnowledgeBaseStore2);

  const emits = defineEmits([
    'update:visible',
    'uploadSingleSuccess',
    'finish',
  ]);
  const props = defineProps({
    visible: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '文件传输列表',
    },
    files: {
      type: Array,
      default() {
        return [];
      },
    },
    position: {
      type: Object,
      default() {
        return {
          top: 100,
          left: 600,
        };
      },
    },
    // 是否显示上传成功的提示信息
    showSuccessMsg: {
      type: Boolean,
      default: true,
    },
  });

  const pauseUpload = (index: number) => {
    uploadStore.pauseUpload(uploadFileList.value[index]);
  };
  const restartUpload = (index: number) => {
    uploadStore.resumeUpload(uploadFileList.value[index]);
  };
  const cancelUpload = (index: number) => {
    uploadStore.cancelSingle(uploadFileList.value[index]);
  };

  const close = () => {
    emits('update:visible', false);
  };

  watch(
    () => uploadFileList.value.length,
    (val) => {
      if (!val) {
        emits('finish');
        close();
      }
    }
  );

  const uploadSuccessCb = (file: any) => {
    emits('uploadSingleSuccess', file);
  };

  // 文件上传
  const uploadFileHandle = (list: any) => {
    const fileList = list.map((file: any) => {
      const uploadFile = {
        file,
        name: file.name,
        percent: 0,
        status: 'init',
        uid: `${Date.now().toString()}-${file.name}`,
        model: 'knowledgeBase',
        parentId: file.parentId || selectedKeys.value?.[0] || '', // 增加parentId字段，用于上传成功后保存到知识库对应文件夹
        uploadSuccessCb,
      };
      return uploadFile;
    });

    uploadStore.handleUploadFile([...fileList], props.showSuccessMsg);
  };
  watch(
    () => props.files,
    (val) => {
      if (val?.length) {
        uploadFileHandle(val);
      }
    }
  );

  const fileChange = (fileList: any) => {
    const list = fileList.map((item: any) => item.file);

    uploadFileHandle(list);
    uploadList.value = [];
  };

  const setPosition = () => {
    const poi = toRaw(props.position);
    if (uploadPanelRef.value && poi.top && poi.top * 1 === poi.top) {
      uploadPanelRef.value.style.top = `${poi.top}px`;
    }
    if (uploadPanelRef.value && poi.left && poi.left * 1 === poi.left) {
      uploadPanelRef.value.style.left = `${poi.left}px`;
    }
    if (uploadPanelRef.value && poi.bottom && poi.bottom * 1 === poi.bottom) {
      uploadPanelRef.value.style.bottom = `${poi.bottom}px`;
    }
    if (uploadPanelRef.value && poi.right && poi.right * 1 === poi.right) {
      uploadPanelRef.value.style.right = `${poi.right}px`;
    }
  };
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        nextTick(() => {
          setPosition();
        });
      }
    },
    {
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  .upload-panel {
    width: 400px;
    height: 500px;
    position: fixed;
    top: 100px;
    right: 400px;
    z-index: 1100;
    background-color: white;
    box-shadow: 4px 4px 4px 4px #c3c3c3;
    border-radius: 6px;
    padding: 12px;
    padding-top: 6px;
    overflow: hidden;
    .panel-header {
      height: 30px;
      line-height: 30px;
      //border: 1px solid red;
      font-size: 15px;
      font-weight: bold;
      font-family: '宋体 PingFang SC-Medium';
    }
    .panel-file-area {
      height: 420px;
      //border: 1px dashed #c3c3c3;
      .panel-file-item {
        height: 70px;
        border-bottom: 1px solid rgb(229, 230, 235);
        display: flex;
        .panel-file-content {
          width: 270px;
          overflow: hidden;
          height: 60px;
          //border: 1px solid red;
          margin-top: 8px;
          margin-left: 8px;
          .file-name {
            height: 25px;
            line-height: 25px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        .panel-operation {
          display: flex;
          margin-top: 8px;
          //border: 1px solid red;
          align-content: center;
          align-items: center;
          cursor: pointer;
          flex: 1;
        }
      }
    }
    .panel-footer {
      //border: 1px solid red;
      height: 35px;
      margin-top: 5px;
    }
    .panel-empty-area {
      height: 420px;
      display: flex;
      align-content: center;
      align-items: center;
    }
  }
</style>
