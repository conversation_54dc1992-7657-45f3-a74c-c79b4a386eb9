export default {
  'file-manage.standard-name': 'Standard Name',
  'file-manage.parent-folder-name': 'Parent Folder Name',
  'file-manage.transition-status': 'Transition Status',
  'file-manage.file-status': 'File Status',
  'file-manage.regenerator': 'Updated by',
  'file-manage.describe': 'Describe',
  'file-manage.last-updated-time': 'Last Updated Time',
  'file-manage.nullify': 'Nullify',
  'file-manage.cancellations': 'Void Cancellations',
  'file-manage.cancel-nullify': 'Cancel Nullify',
  'file-manage.cancel-and-invalidate-the-file':
    'Cancel and invalidate the file',
  'file-manage.generate-file':
    'This operation will generate a new file version.',
  'file-manage.set-current-version':
    'Are you sure you want to set V{version} to the current version?',
  'file-manage.batch-download': 'Batch Download',
  'file-manage.wait-downloading': 'Please wait while the file is downloading',
  'file-manage.writeable': 'Writeable',
  'file-manage.readable': 'Readable',
  'file-manage.lock': 'Lock',
  'file-manage.editing': 'Editing the current file',
  'file-manage.reconvert': 'Reconvert',
  'file-manage.in-conversion': 'In Conversion',
  'file-manage.in-queue': 'In Queue',
  'file-manage.not-starts': 'Have Not Started',
  'file-manage.is-locked-file': 'Whether to unlock the file {name} ？',
  'file-manage.information': 'Information',
  'file-manage.cant-unlocked-file':
    'You do not have permission to unlock the file {name}',
  'file-manage.name-too-long': 'The name cannot exceed {count} characters',
  'file-manage.name-exclude':
    'The name cannot contain any of the following characters: \\/:*?" <>|',
  'file-manage.name-exclude-2':
    'The name cannot contain any of the following characters: \\/:*?"<>|" "',
  'file-manage.name-duplication': 'Name Duplication',
  'file-manage.bound-standard': 'Bound Standard',
  'file-manage.system-generation': 'System Generation',
  'file-manage.name': 'Name',
  'file-manage.exit-search': 'Exit search',
  'file-manage.path': 'Location directory',
  'file-manage.file-path': 'File Path',
  'file-manage.standard': 'Standard',
  'file-manage.size': 'Size',
  'file-manage.description': 'Description',
  'file-manage.update-date': 'Update Date',
  'file-manage.operation': 'Operation',
  'file-manage.naming-standard-renamed': 'Naming Standard Renamed',
  'file-manage.temporary-file': 'Temporary File',
  'file-manage.not-complete':
    'Model conversion is not complete, please compare versions later',
  'file-manage.no-difference':
    'There is no difference between the baseline model and the comparison model',
  'file-manage.version': 'Version',
  'file-manage.converting': 'Converting',
  'file-manage.success': 'Success',
  'file-manage.failed': 'Failed',
  'file-manage.uploaded': 'Uploaded',
  'file-manage.current-version': 'Current',
  'file-manage.meta-property-setting': 'Meta Property Setting',
  'file-manage.standard-list': 'Standard List',
  'file-manage.setting': 'Setting',
  'file-manage.temporary': 'Temporary',
  'file-manage.share-record': 'Share Record',
  'file-manage.share-file': 'Share File',
  'file-manage.share-type': 'Share Type',
  'file-manage.visible-to-anyone': 'Visible To Anyone',
  'file-manage.need-to-extract-code': 'Need To Extract Code',
  'file-manage.need-login': 'Login required (CDex account required to view)',
  'file-manage.need-login-text': 'Login required',
  'file-manage.need-place-input': 'Please input',
  'file-manage.long-time': 'Long-Term Effective',
  'file-manage.24H-lose': 'It will fail within 24 hours',
  'file-manage.became-invalid': 'Lost Efficacy',
  'file-manage.after-failure': 'Day After Failure',
  'file-manage.permanent': 'Permanent',
  'file-manage.7d': '7Days',
  'file-manage.30d': '30Days',
  'file-manage.24h': '24H',
  'file-manage.generate-link': 'Generate Link',
  'file-manage.period-of-validity': 'Period Of Validity',
  'file-manage.encryption-mode': 'Encryption Mode',
  'file-manage.secretKey': 'Secret Key',
  'file-manage.share-record-list': 'Share Record List',
  'file-manage.select-target-folder': 'Select Target Folder',
  'file-manage.download': 'Download Source Files',
  'file-manage.move': 'Move',
  'file-manage.edit': 'Edit',
  'file-manage.convert': 'Convert',
  'file-manage.convert-successful': 'Conversion successful',
  'file-manage.document-format': 'Document format',
  'file-manage.document-format-errMsg': 'Please select document format',
  'file-manage.submit-review': 'Submit for Review',
  'file-manage.create-cover-letter': 'create enclosure',
  'file-manage.share': 'Share',
  'file-manage.close': 'Close',
  'file-manage. are-you-sure-you-want-to-cancel-the-file':
    'After invalidating the file, no operation will be allowed, including viewing, downloading, etc. Do you want to continue？',
  'file-manage.upload': 'Upload File',
  'file-manage.delete': 'Delete',
  'file-manage.view-by': 'View by',
  'file-mange.display-folder': 'Display folder',
  'file-mange.not-display-folders': 'Not display folders',
  'file-manage.confirm-delete-file': 'Delete This',
  'file-manage.confirm-nullify-file':
    'After invalidating the file, no operation will be allowed, including viewing, downloading, etc. Do you want to continue',
  'file-manage.file': 'File',
  'file-manage.numerical-order': 'Numerical Order',
  'file-manage.file-name': 'File Name',
  'file-manage.file-share-link': 'Link',
  'file-manage.file-share-file-link': 'File Link',
  'file-manage.file-code': 'Code',
  'file-manage.copy-link': 'Copy Link',
  'file-manage.copy-link-and-code': 'Copy links and extract codes',
  'file-manage.copy-success': 'Successful replication',
  'file-manage.copy-error': 'Replication failure',
  'file-manage.file-share-type': 'Type',
  'file-manage.file-share-date': 'Share Date',
  'file-manage.folder': 'Folder',
  'file-manage.attachment': 'Attachment',
  'file-manage.add-folder': 'Add Folder',
  'file-manage.arrange-order': 'Arrange Order',
  'file-manage.rename-folder': 'Rename Folder',
  'file-manage.download-source': 'Download Source File',
  'file-manage.rollback': 'Rollback',
  'file-manage.copy': 'Copy',
  'file-manage.create': 'Create',
  'file-manage.base-version': 'Base Version',
  'file-manage.compare-version': 'Compare Version',
  'file-manage.create-time': 'Create Time',
  'file-manage.select': 'Select',
  'file-manage.confirm-delete':
    'Are you sure you want to delete this version comparison?',
  'file-manage.version-history': 'Version History',
  'file-manage.version-compare': 'Version Compare',
  'file-manage.search-attributes': 'Search Attributes',
  'file-manage.reset': 'Reset',
  'file-manage.search': 'Search',
  'file-manage.property-settings': 'Property Settings',
  'file-manage.enter-name': 'Enter Name',
  'file-manage.metadata-setting': 'Metadata Setting',
  'file-manage.successfullySetMetadata': 'Successfully set metadata!',
  'file-manage.confirm': 'Confirm',
  'file-manage.copy-all': 'Copy All',
  'file-manage.successfully-copied': 'Successfully copied',
  'file-manage.failed-to-copy': 'Failed to copy',
  'file-manage.upload-file': 'Upload File',
  'file-manage.upload-folders': 'Upload Folders',
  'file-manage.cancel': 'Cancel',
  'file-manage.uploadSuccess': 'Uploaded successfully!',
  'file-manage.uploadFailed': 'Upload failed!',
  'file-manage.all-files': 'No file | Total 1 file | Total {count} files',
  'file-manage.files-upload-failed':
    'The file {name} upload failed. The file has been ignored in this submission',
  'file-manage.network-disconnected': 'Network disconnected',
  'file-manage.releaseFiles': 'Release files',
  'file-manage.submit-success': 'Submission successful',
  'file-manage.cannot-upload-the-same-file': 'Cannot upload the same file',
  'file-manage.no-spaces-are-allowed-in-the-file':
    'No spaces are allowed in the file name',
  'file-manage.all-files-failed': 'All files failed to upload',
  'file-manage.upload-text': 'Click or drag files here to upload',
  'file-manage.release': 'Release files',
  'file-manage.readOnly': 'Read-only',
  'file-manage.locked': 'Locked',
  'file-manage.writable': 'Writable',
  'file-manage.downloadSource': 'Download source file',
  'file-manage.reConvert': 'Reconvert',
  'file-manage.naming-standard-error': 'Naming Standard Error',
  'file-manage.delivery-standard-error': 'Delivery Standard Error',
  'file-manage.delivery-criteria-renamed': 'Delivery Criteria Renamed',
  'file-manage.checking': 'Checking',
  'file-manage.check-status': 'Check Status',
  'file-manage.naming-standard': 'Naming Standard',
  'file-manage.delivery-criteria': 'Delivery Criteria',
  'file-manage.detail': 'Detail',

  'file-manage.initiator': 'Initiator',
  'file-manage.share/deliver-name': 'Share/deliver Name',
  'file-manage.initiation-time': 'Initiation Time',
  'file-manage.file-count': 'File Count',
  'file-manage.check-number-failed-files': 'Failed files',
  'file-manage.view': 'View',
  'file-manage.resubmit': 'Resubmit',

  'file-manage.shared-temporary-area': 'Shared Temporary Area',
  'file-manage.deliver-temporary-area': 'Deliver Temporary Area',
  'file-manage.shared-file': 'Shared File',
  'file-manage.Delivery-file': 'Delivery File',
  'file-manage.delete-file': 'Delete the file?',
  'file-manage.delete-temporary': 'Do you want to delete the temporary record?',
  'file-manage.return': 'Return',
  'file-manage.resubmit-temporary': 'Do you want to resubmit?',
  'file-manage.export-report': 'Export Report',
  'file-manage.inspection-report': 'Inspection Report',
  'file-manage.export-failure': 'Export Failure',
  'file-manage.in-process': 'In Process',
  'file-manage.deleted-files-exist':
    'Deleted files exist. Delete the files from the staging area and submit them again',
  'file-manage.task-name': 'Task Name',
  'file-manage.delete-last-file':
    'The current record is the last record of this share. After deletion, this share will also be deleted from the temporary area. Do you want to continue?',
  'file-manage.table-download': 'Download',
  'file-manage.no-download-file': 'No File Downloaded',
  'file-manage.no-upload-file': 'No File Uploaded',

  'file-manage.team': 'Belonging team',
  'file-manage.attatchments': 'Attachment List',
  'file-manage.attatchment-type': 'Attachment type',
  'file-manage.reset-serach': 'Reset',
  'file-manage.batch-save': 'Batch Save As',
  'file-manage.item': 'Matter',
  'file-manage.meeting': 'Meeting',
  'file-manage.save': 'Save As',
  'file-manage.go-detail': 'Go to details',
  'file-manage.arrange-order-date': 'Date',
};
