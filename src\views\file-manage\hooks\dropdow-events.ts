import i18n from '@/locale/index';
import useFileManageStore from '@/store/modules/file-manage/index';
import { nextTick } from 'vue';
import { Modal, Message, Notification } from '@arco-design/web-vue';
import { HttpResponse } from '@/api/interceptor';
import { useThrottleFn } from '@vueuse/core';
import { download } from '@/utils/file';
import {
  FileAndFolderNodeMessage,
  FileMessage,
  FolderMessage,
} from '@/api/tree-folder';
import { batchDownloadInfo, fileZipDownload } from '@/api/file';
import {
  FileVersion,
  addChildFolder,
  updateFolder,
  updateFile,
  fileRollback,
  fileDownload,
  FileAndFolderMessage,
  addFile,
  fileShare,
  getFileChildrenAll,
  moveFileAndFolder,
  getCode,
  wpsRefresh,
} from '../api';
import { getNodeById, isFolderRepeated } from './utils';
import qs from 'query-string';
import useUserStore from '@/store/modules/user';
import { decode, encode } from 'js-base64';
import { getWpsAppCodeProject } from '@/api/wps';
// import { setWpsToken } from '@/utils/auth';

const userStore = useUserStore();

const { t } = i18n.global;
const store = useFileManageStore();

// wps预览/编辑（record:文件数据 linkType:类型<edit编辑 preview预览> modifier:针对分享增加的固定参数（可选））
export async function wpsViewHandle(
  record: any,
  linkType: string,
  modifier?: string
) {
  const param = {
    redirectUri: window.origin,
  };
  try {
    const { data } = await getCode(param);
    if (!data?.code) return;
    const fileSource = (await getWpsAppCodeProject({})).data;
    const paramNew = {
      code: data?.code,
      fileSource,
      linkType,
      modifier: userStore?.username || modifier,
      fileId: record?.fileId || record?.id,
      name: record?.name,
    };

    const queryParams = qs.stringify(paramNew);
    const url = `https://cdex.ccccltd.cn:8000/wpsView?${queryParams}`;
    window.open(url);
  } catch (error) {
    console.log(error);
  }
}

// wps转换
export async function wpsConvertHandle(record: any) {
  const type = record.name.split('.')[record.name.split('.').length - 1];
  store.setConvertModal(true, record, type);
}

// 添加文件夹请求
const addFolderRequest = useThrottleFn(
  async (
    currentFile: FileMessage,
    inputElement: HTMLInputElement,
    divElement: HTMLElement
  ) => {
    const inputValue = inputElement.value;
    const param: FolderMessage = {
      name: inputValue,
      parentId: currentFile.id,
      projectId: currentFile.projectId,
      teamId: currentFile.teamId,
      type: currentFile.type,
    };
    // 名称为空校验
    if (inputValue === '') {
      divElement.remove();
      return;
    }
    // 禁用字符校验
    // eslint-disable-next-line no-useless-escape
    // const pattern = /^[^\\/:\*\?"<>\|\s]+$/;
    const pattern = /^[^\\/:*?"<>|]+$/;
    const regResult = pattern.test(inputValue);
    if (!regResult) {
      inputElement.focus();
      Message.warning(t('file-manage.name-exclude-2'));
      return;
    }
    // 名称重复校验
    const repeated = await isFolderRepeated(currentFile, inputValue);
    if (repeated) {
      inputElement.focus();
      Message.error(t('file-manage.name-duplication'));
      return;
    }
    // 新增文件夹接口
    const res: any = await addChildFolder(param);
    const result = res as HttpResponse<any>;
    if (result.code === 8000000) {
      Message.success(t('file-manage.success'));
      divElement.remove();
      store.getTreeChild(currentFile, false);
      store.setCurrentFolder(currentFile);
    }
  },
  1000
);

// 添加文件夹按钮
export function addFolder(
  currentFile: FileMessage,
  currentElement: Element | null
) {
  if (!currentElement) return;
  // 在父元素下添加输入框
  const parentElem = currentElement.parentNode;
  const newDiv = document.createElement('div');
  newDiv.style.border = '1px solid rgb(var(--primary-6))';
  newDiv.style.padding = ' 4px 12px';
  const newElem = document.createElement('input');
  const textElement = currentElement.getElementsByClassName(
    'arco-tree-node-indent'
  );
  // 计算输入框位置和宽度
  const marginLeft = Math.min(textElement[0].clientWidth + 46, 100);
  newDiv.style.marginLeft = `${marginLeft}px`;
  newDiv.style.width = `calc(100% - ${marginLeft + 24}px)`;
  // 设置input样式
  newElem.style.width = '100%';
  newElem.style.border = '0';
  newElem.style.outline = '0';
  newDiv.appendChild(newElem);
  parentElem?.insertBefore(newDiv, currentElement.nextSibling);
  // input失焦触发事件
  newElem.addEventListener('blur', () => {
    addFolderRequest(currentFile, newElem, newDiv);
  });
  newElem.addEventListener(
    'keypress',
    useThrottleFn((event) => {
      if (event.key === 'Enter') {
        addFolderRequest(currentFile, newElem, newDiv);
      }
    }, 200)
  );
  // 输入框自动聚焦
  nextTick(() => {
    newElem.focus();
  });
}

// 文件夹重命名
export function renameFolder(currentFile: FileAndFolderNodeMessage) {
  store.setTreeRenameValue(currentFile.name || '');
  nextTick(() => {
    store.setRenameKey(currentFile.key || '');
  });
}

// 文件夹重命名请求
export const renameFolderRequest = useThrottleFn(
  async (
    currentFile: FileAndFolderNodeMessage,
    renameInput: HTMLInputElement
  ) => {
    // 名称没有变化或者名称为空表示退出编辑
    if (
      store.treeRenameValue === store.treeRenameBackup ||
      store.treeRenameValue === ''
    ) {
      store.setRenameKey(-1);
      store.setTreeRenameValue('');
      return;
    }
    // 禁用字符校验
    // eslint-disable-next-line no-useless-escape
    // const pattern = /^[^\\/:\*\?"<>\|\s]+$/;
    const pattern = /^[^\\/:*?"<>|]+$/;
    const regResult = pattern.test(store.treeRenameValue);
    if (!regResult) {
      renameInput.focus();
      Message.warning(t('file-manage.name-exclude')); // 允许空格
      return;
    }
    // 名称重复校验
    const parentMessage: any = getNodeById(
      store.treeData,
      currentFile.parentId || ''
    );
    const repeated = await isFolderRepeated(
      parentMessage,
      store.treeRenameValue
    );
    if (repeated) {
      renameInput.focus();
      Message.error(t('file-manage.name-duplication'));
      return;
    }

    const param: FolderMessage = {
      ...currentFile,
      name: encode(store.treeRenameValue),
    };
    delete param.sunFolders;
    delete param.children;
    delete param.files;
    delete param.path;
    const res: any = await updateFolder(param);
    const result = res as HttpResponse<any>;
    if (result.code === 8000000) {
      Message.success(t('file-manage.success'));
      store.setTreeRenameValue('');
      store.getTableData(parentMessage, false);
      await store.getTreeChild(parentMessage, false);
      if (store.renameKey === currentFile.key) {
        param.name = decode(param.name);
        store.setCurrentFolder(param);
      }
      store.setRenameKey(-1);
    }
  },
  1000
);

/** 改变tree目录排序 */
export function order(currentFile: FileAndFolderNodeMessage, orderBy: string) {
  currentFile.children?.sort(
    (a: FileAndFolderNodeMessage, b: FileAndFolderNodeMessage) => {
      let result = 0;
      if (orderBy === 'date') {
        const valueA = new Date(a.createDate || '').getTime();
        const valueB = new Date(b.createDate || '').getTime();
        result = valueB - valueA;
      } else {
        result =
          (a.name?.localeCompare(b?.name || '') || 0) *
          (orderBy === 'asc' ? 1 : -1);
      }
      return result;
    }
  );
}

/** 显示版本列表抽屉 */
export function showVersions(currentFile: FileAndFolderNodeMessage) {
  store.setCurrentFIle(currentFile);
  store.getFileVersionData();
  store.setVersionDrawer(true);
}

/** 版本回退 */
export function rollback(version: string) {
  Modal.warning({
    title: t('file-manage.set-current-version', { version }),
    content: t('file-manage.generate-file'),
    width: 464,
    onOk: async () => {
      const fileSource = localStorage.getItem('wpsAppCode') || '';
      wpsRefresh({ fileId: store.currentFile?.id, version, fileSource });
      const res: any = await fileRollback(store.currentFile?.id, version);
      const result = res as HttpResponse<any>;
      if (result.code === 8000000) {
        store.getFileVersionData();
        store.getTableData(store.currentFolder || {}, false);
      }
    },
  });
}

/** 版本复制请求 */
async function versionCopyRequest(
  treeDataPromise: () => Promise<any>
): Promise<boolean> {
  const folderId: string = await treeDataPromise();
  const currentVersion = store.currentVersion || {};
  const fileMessage = {
    id: currentVersion?.fileId,
    name: currentVersion?.name,
    projectId: currentVersion?.projectId,
    teamId: currentVersion?.teamId,
    folderId,
    fileToken: currentVersion?.fileToken,
    description: currentVersion?.description,
    size: currentVersion?.size,
    type: currentVersion?.type,
    version: currentVersion?.version,
  };
  const res: any = await addFile('/cde-collaboration/file/save', fileMessage);
  const result = res as HttpResponse<any>;
  if (result.code === 8000000) {
    Message.success(t('file-manage.success'));
    return true;
  }
  return false;
}

/** 版本复制按钮 */
export function versionCopy(fileVersion: FileVersion) {
  store.setMoveModal(true);
  store.setCurrentVersion(fileVersion);
  store.setSendMove(versionCopyRequest);
}

/** 版本下载按钮 */
export async function versionDownload(fileVersion: FileVersion) {
  const res: any = await fileDownload(fileVersion);
  download({ name: fileVersion.name || '' }, res.data);
}

/** 文件/文件夹移动请求 */
async function moveRequest(
  treeDataPromise: () => Promise<any>
): Promise<boolean> {
  const parentId: string = await treeDataPromise();
  if (!parentId) {
    Message.error(t('file-manage.select-target-folder'));
    return false;
  }
  const currentFileOrFolder: FileAndFolderMessage = store.currentFile || {};
  const fileIds: string[] = [];
  const folderIds: string[] = [];
  if (currentFileOrFolder.isFileOrFolder === 0) {
    folderIds.push(currentFileOrFolder.id as string);
  } else {
    fileIds.push(currentFileOrFolder.id as string);
  }
  const res: any = await moveFileAndFolder(fileIds, folderIds, parentId);
  const result = res as HttpResponse<any>;
  if (result.code === 8000000) {
    Message.success(t('file-manage.success'));
    store.getTableData(store.currentFolder || {}, false);
    store.getTreeChild(
      getNodeById(
        store.treeData,
        store.currentFolder?.id as string
      ) as FileAndFolderNodeMessage
    );
    store.getTreeChild(
      getNodeById(store.treeData, parentId) as FileAndFolderNodeMessage,
      false
    );
    return true;
  }
  return false;
}
/** 批量移动请求 */
async function batchMoveRequest(
  treeDataPromise: () => Promise<any>
): Promise<boolean> {
  const parentId: string = await treeDataPromise();
  if (!parentId) {
    Message.error(t('file-manage.select-target-folder'));
    return false;
  }
  const fileIds: string[] = [];
  const folderIds: string[] = [];
  store.checkTableData.forEach((item) => {
    if (item.isFileOrFolder === 1) {
      fileIds.push(item.id as string);
    } else {
      folderIds.push(item.id as string);
    }
  });
  const res: any = await moveFileAndFolder(fileIds, folderIds, parentId);
  const result = res as HttpResponse<any>;
  if (result.code === 8000000) {
    Message.success(t('file-manage.success'));
    store.getTableData(store.currentFolder || {}, false);
    store.getTreeChild(
      getNodeById(
        store.treeData,
        store.currentFolder?.id as string
      ) as FileAndFolderNodeMessage
    );
    return true;
  }
  return false;
}

/** 文件/文件夹移动按钮 */
export function move(isBatch = false) {
  store.setMoveModal(true);
  if (isBatch) {
    store.setSendMove(batchMoveRequest);
  } else {
    store.setSendMove(moveRequest);
  }
}

/**
 * 根据父文件夹id获取父文件夹
 */
function getParentFolder(treeData: any, folderId: any, parentFolder: any) {
  let parent: any;
  if (treeData.length) {
    treeData.forEach((ele: any) => {
      if (ele.id === folderId) {
        parent = parentFolder;
      } else if (!parent && ele.children?.length)
        parent = getParentFolder(ele.children, folderId, ele);
    });
  }
  return parent;
}

function parseFolder(treeData: any, folderId: string) {
  let result: any;
  treeData.forEach((e: any) => {
    const folder = getParentFolder(e.children, folderId, e);
    if (folder) result = folder;
  });
  return result;
}

/** 文件/文件夹批量删除按钮 */
export function Delete(data: any, type?: string) {
  store
    .Delete(data)
    .then((res: any) => {
      if (res.code === 8000000) {
        Notification.success({
          id: 'delete',
          title: 'Success',
          content: t('file-manage.success'),
        });

        const currentFolder = parseFolder(store.treeData, data?.folderIds[0]);

        // 判断此处删除 是否在被删除的文件夹中
        if (data.folderIds[0] === store.currentFolder?.id) {
          store.getTreeChild(currentFolder || {}, false);
          store.getTableData(currentFolder || {}, false);
          store.fileTreeRef.nodeClick([], {
            node: currentFolder,
          });
        } else if (type) {
          // 判断是表格中的删除
          store.getTreeChild(store.currentFolder || {}, false);
          store.getTableData(store.currentFolder || {}, false);
        } else {
          // 其他
          store.getTreeChild(currentFolder || {}, false);
          store.getTableData(store.currentFolder || {}, false);
        }
      }
    })
    .catch((error) => {
      console.error('请求错误', error);
    });
}

/** 文件/文件夹单个作废事件 */
export function Nullify(row: any) {
  let contentText = ''; // 需要国际化
  if (row.abandon === 1) {
    contentText = '取消作废成功';
  } else {
    contentText = '作废成功';
  }
  store.Nullify([row.id]).then((res: any) => {
    if (res.code === 8000000) {
      Notification.success({
        id: 'nullify',
        title: 'Success',
        content: contentText,
      });
      store.getTreeChild(store.currentFolder || {}, false);
      store.getTableData(store.currentFolder || {}, false);
    }
  });
}

// table 重命名按钮点击事件
export function rename(index: number, recode: FileAndFolderMessage) {
  store.setRenameIndex(index); // 设置选中记录rowIndex
  store.setRenameKey(recode.id || ''); // 设置选中记录id
}

export function renameRequest(record: FileAndFolderNodeMessage) {
  let api;
  if (record.isFileOrFolder === 0) {
    record.name = encode(record.name);
    record.path = encode(record.path);
    api = updateFolder;
  } else {
    api = updateFile;
  }

  delete record.isFileOrFolder;
  return api(record);
}

// 上传文件
export function upload(currentFolder: FolderMessage, uploadType: number) {
  store.setUploadModal(true, uploadType);
  if (currentFolder instanceof PointerEvent) {
    // 点击列表上方按钮
    store.setSelectedFolder(store.currentFolder);
  } else {
    // 点击左侧树按钮
    store.setSelectedFolder(currentFolder);
  }
  return currentFolder;
}

// 下载单文件
async function handleSingleFile(file: any) {
  const res: any = await fileDownload(file);
  download(file, res.data);
}

// 下载压缩包
async function handleZipFile(fileList: any) {
  // 获取批量下载文件信息
  const res: any = await batchDownloadInfo({ fileList });
  if (res.status) {
    const { data } = res;
    let zipFileName = `${t('file-manage.batch-download')}.zip`;

    const downloadRes: any = await fileZipDownload(data);
    // 获取文件名称
    const disposition = downloadRes.headers['content-disposition'];
    if (disposition && disposition.includes('filename=')) {
      const [, encodeFilename] = disposition.split('filename=');
      zipFileName = decodeURI(encodeFilename);
    }
    download({ name: zipFileName }, downloadRes.data);
  } else {
    Message.error(res.message);
  }
}

// 下载源文件按钮点击事件
export async function downloadSource(currentFile: FileAndFolderNodeMessage) {
  Message.info(t('file-manage.wait-downloading'));
  if (currentFile.isFileOrFolder === 1) {
    // 文件
    handleSingleFile(currentFile);
  } else {
    // 文件夹
    const fileList = [
      { fileId: currentFile.id, fileType: currentFile.isFileOrFolder },
    ];
    handleZipFile(fileList);
  }
  return currentFile;
}

// tableHead 批量下载按钮点击事件
export async function batchDownload() {
  Message.info(t('file-manage.wait-downloading'));
  // 只选了一个文件，调用下载文件接口
  if (
    store.checkTableData.length === 1 &&
    store.checkTableData[0].isFileOrFolder === 1
  ) {
    handleSingleFile(store.checkTableData[0]);
    return;
  }
  // 批量下载
  const fileList = store.checkTableData.map((item) => {
    return {
      fileId: item.id,
      fileType: item.isFileOrFolder,
    };
  });
  handleZipFile(fileList);
}

function formatFileList(currentFile: any, fileList: any[]) {
  fileList.push(...currentFile.files);
  if (currentFile.sunFolders) {
    currentFile.sunFolders.forEach((item: any) => {
      if (item.files.length > 0) {
        currentFile.files.push(...item.files);
      }
      formatFileList(item, fileList);
    });
  }
}

// 附函/审阅
export async function openModel(
  type: string,
  currentFile: FileAndFolderNodeMessage
) {
  if (currentFile.isFileOrFolder) {
    // 文件
    const data = {
      id: currentFile.id,
      teamId: currentFile.teamId,
      files: [
        {
          fileId: currentFile.id,
        },
      ],
    };
    if (type === 'approve') store.setCheckModal(true, data);
    else store.setEnclosureModal(true, data);
  } else {
    // 文件夹
    const res: any = await getFileChildrenAll(currentFile.id as string);
    if (res.status) {
      const data = {
        id: currentFile.id,
        teamId: currentFile.teamId,
        files: [],
      };
      formatFileList(res.data, data.files);
      if (type === 'approve') {
        store.setCheckModal(true, data);
      } else store.setEnclosureModal(true, data);
    }
  }
}
interface defaultApprove {
  id: string;
  files: any[];
}

export async function batchApprove() {
  const promiseList: Promise<any>[] = [];
  const data: defaultApprove = {
    id: 'batchApprove',
    files: [],
  };
  store.checkTableData.forEach((item) => {
    if (item.isFileOrFolder === 1) {
      data.files.push({
        fileId: item.id,
      });
    } else {
      promiseList.push(getFileChildrenAll(item.id as string));
    }
  });
  if (promiseList.length) {
    const res = await Promise.all(promiseList);
    res.forEach((item) => {
      if (item.status) {
        formatFileList(item.data, data.files);
      }
    });
  }
  store.setCheckModal(true, data);
}

export async function batchCreateBypass() {
  const promiseList: Promise<any>[] = [];
  const data: defaultApprove = {
    id: 'batchBypass',
    files: [],
  };
  store.checkTableData.forEach((item) => {
    if (item.isFileOrFolder === 1) {
      data.files.push({
        fileId: item.id,
      });
    } else {
      promiseList.push(getFileChildrenAll(item.id as string));
    }
  });
  if (promiseList.length) {
    const res = await Promise.all(promiseList);
    res.forEach((item) => {
      if (item.status) {
        formatFileList(item.data, data.files);
      }
    });
  }
  store.setEnclosureModal(true, data);
}

export async function share(currentFile: FileAndFolderNodeMessage) {
  const params: any = {
    checkType: 0,
    packageType: 0,
    shareType: 1,
    shareLinkDtoList: [
      {
        fileId: currentFile.id,
        fileType: currentFile.isFileOrFolder,
      },
    ],
  };
  const res: any = await fileShare(params);
  if (res.status) {
    const shareLinkUrl = res.data[0].shareLink;
    const link = `${
      window.location.origin
    }/share-download?uuid=${shareLinkUrl.slice(
      -(shareLinkUrl.lastIndexOf('/') - 1)
    )}`;
    res.data[0].shareLink = link;
    store.setShareModal(true, false, res.data);
  } else {
    Message.error(res.message);
  }
}
export async function batchSharing() {
  const shareLinkDtoList = store.checkTableData.map((item) => {
    return {
      fileId: item.id,
      fileType: item.isFileOrFolder,
    };
  });

  const params: any = {
    checkType: 0,
    packageType: 0,
    shareType: 1,
    shareLinkDtoList,
  };

  const res: any = await fileShare(params);

  if (res.status) {
    res.data.forEach((item: any) => {
      const shareLinkUrl = item.shareLink;
      const link = `${
        window.location.origin
      }/share-download?uuid=${shareLinkUrl.slice(
        -(shareLinkUrl.lastIndexOf('/') - 1)
      )}`;
      item.shareLink = link;
    });
    store.setShareModal(true, true, res.data);
  } else {
    Message.error(res.message);
  }
}
export async function batchNullify() {
  store.setNullifyModal(true); // 显示作废弹框
}
