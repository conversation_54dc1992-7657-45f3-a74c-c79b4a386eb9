import initDXY from '@/utils/xbase/utils/addScript';
import useModelToolsStore from '@/store/modules/model-viewer/index';

declare const DX: any;

const store = useModelToolsStore();

export default class TreeViewer {
  private app: any;

  private options: any;

  private viewer: any;

  private componentTreeKey = 'COMPONENT_TREE';

  async render(BaseViewer: any, renderPath: string) {
    try {
      if (!('DX' in window)) await initDXY();

      this.app = BaseViewer.app;
      this.viewer = BaseViewer.viewer;
      this.options = BaseViewer.options;

      this.options = {
        previewToken: BaseViewer.options.accessToken.Authorization,
        baseUrl: `${window.origin}/x_base_api`,
        renderPath,
        ...BaseViewer.options,
      };

      this.initializeToolbar();
      this.initializeComponentTree();
      const versionCompare = ['base-viewer', 'compare-viewer'];
      // 版本比对模式隐藏toolBar
      if (versionCompare.includes(BaseViewer.elementId)) {
        BaseViewer.options.mainToolbar = [];
      }
    } catch (e) {
      if (e) console.log(e.toString());
    }
  }

  private initializeToolbar() {
    const toolbar = this.app.options.mainToolbar;
    toolbar.splice(4, 0, 'custom_attribute');
    // 语义模型预览时 删除DOM元素中的属性面板对话框 调用自定义的语义属性面板
    // const semanticModel = ['ifc', 'dgn', 'rvt'];
    const [_, pathExtension] = this.options.renderPath.split('.');
    console.log('[ pathExtension ] >', pathExtension);
    // if (semanticModel.includes(pathExtension)) {
    //   const panels = document.querySelectorAll('.dxy-panel.dxy-hidden');
    //   if (panels[1]) {
    //     panels[1].remove();
    //   }
    this.DXCommands();
    // }

    // 移动端构件树隐藏
    if (window.innerWidth > 768) {
      toolbar.splice(0, 0, this.componentTreeKey);
    }

    this.app.viewer.createCustomButton({
      key: this.componentTreeKey,
      hasActive: true,
      title: '构件树',
      update: false,
      className: 'xicon-member-tree',
      handle: (v: boolean) => {
        store.setModalTreeIsShow(v);
      },
    });

    this.app.viewer.setToolbarItems(toolbar);
  }

  private initializeComponentTree() {
    const TreeViewerWithModal = new window.DX.XComponent.TreeViewer(
      '.tree-container',
      {
        previewToken: this.options.previewToken,
        baseUrl: this.options.baseUrl,
        renderPath: this.options.renderPath,
        menuKey: this.componentTreeKey,
        dragAreaClass: 'contenter',
        app: this.app,
        onClose: () => {},
      }
    );
  }

  // DX.Commands 默认命令集合
  private DXCommands() {
    this.app.commandManager.commands
      .get(DX.Commands.SHOW_PROPERTIES) // 开启或关闭属性面板
      .on(DX.Events.COMMAND_TOGGLED, (status: boolean) => {
        if (status) {
          store.setPropertiesPanelShow(true);
        } else {
          store.setPropertiesPanelShow(false);
        }
      });
  }
}
