<template>
  <a-row class="create-schedule">
    <!-- 左侧日程列表（会议 事项） -->
    <a-col :span="8" class="calendar-list">
      <a-row class="calendar-list-head">
        <a-col class="detail-time" :span="12">{{
          dayjs(currentDate).format('YYYY年MM月DD日')
        }}</a-col>
        <a-col :span="12" align="right">
          <!-- <icon-plus class="plus-button" /> -->
        </a-col>
      </a-row>
      <schedule-list
        v-if="allScheduleData"
        class="schedule-list"
        type="create"
        :data="allScheduleData"
        @refresh="getScheduleHandle"
      ></schedule-list
    ></a-col>
    <a-col :span="16">
      <div class="add-container">
        <Category
          ref="categoryRef"
          :type="type"
          @refresh-list="getScheduleHandle"
        />
      </div>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
  import { ref, watch, provide } from 'vue';
  import scheduleList from './component/scheduleList.vue';
  import Category from './component/category.vue';
  import dayjs from 'dayjs';
  import { getPersonalSchedule } from '@/views/schedule/component/calendar/api';
  import { useUserStore, userScheduleStore } from '@/store';

  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
  });

  const today = new Date();
  const currentDate = dayjs(today).format('YYYY-MM-DD');
  const allScheduleData: any = ref();
  const userStore = useUserStore();

  const scheduleStore = userScheduleStore();

  // scheduleStore.setScheduleId('create');

  // 获取当天跟当前用户相关的事项、会议数据
  const getScheduleHandle = async () => {
    const param = {
      endTime: currentDate,
      pageNo: 1,
      pageSize: 99999,
      startTime: currentDate,
      // teamId: userStore.teamId === 'global' ? '' : userStore.teamId,
    };
    const { data } = await getPersonalSchedule(param);
    // scheduleStatus为null时表示该数据状态未修改  则使用scheduleAutoStatus（自动设计过期时间）得状态  否则使用scheduleStatus
    data.list.forEach((item: any) => {
      item.scheduleStatus =
        item.scheduleStatus === null
          ? item.scheduleAutoStatus
          : item.scheduleStatus;
    });
    allScheduleData.value = data.list;
  };
  getScheduleHandle();

  const categoryRef: any = ref(null);

  watch(
    () => props.type,
    (val) => {
      setTimeout(() => {
        categoryRef.value.categoryChange(val);
      }, 100);
    },
    {
      immediate: true,
    }
  );
  watch(
    () => userStore.teamId,
    () => {
      getScheduleHandle();
    },
    {
      immediate: true,
    }
  );

  // 会议事项公用列表新增跳转到新建页面
  const grandparentMethod = (data: any) => {
    console.log('新增数据后刷新');
    getScheduleHandle();
    provide('grandparentMethod', grandparentMethod);
  };
  provide('grandparentMethod', grandparentMethod);
</script>

<style scoped lang="less">
  .create-schedule {
    // padding-right: 20px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    overflow: hidden;
    .calendar-list {
      border-right: 1px solid #d9d9d9;
    }

    .calendar-list-head {
      background-color: #fff;
      padding: 16px 20px;
      border-bottom: 1px solid #ededed;
      .detail-time {
        font-weight: bold;
        font-size: 20px;
      }
    }
    .schedule-list {
      height: calc(100vh - 236px);
    }
  }
  .container {
    border: 1px solid #efefef;

    padding: 20px;
    background-color: rgb(248, 250, 250);
    .add-container {
      background-color: #fff;
      height: calc(100vh - 80px);
      padding: 20px;
    }
    height: 100vh;
  }
  .plus-button {
    cursor: pointer;
  }
</style>
