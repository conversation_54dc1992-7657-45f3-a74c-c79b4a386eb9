export default {
  'project-setting.please-enter': '请输入',
  'project-setting.add-subordinate-team': '添加下级团队',
  'project-setting.project': '项目',
  'project-setting.type': '类型',
  'project-setting.team-settings': '团队设置',
  'project-setting.project-plan': '项目计划',
  'project-setting.process-settings': '流程设置',
  'project-setting.project-information': '项目信息',
  'project-setting.design-information': '设计信息',
  'project-setting.project-template-information': '项目模板信息',
  'project-setting.project-name': '项目名称',
  'project-setting.template-name': '模板名称',
  'project-setting.project-code': '项目编码',
  'project-setting.project-location': '项目位置',
  'project-setting.project-type': '项目类型',
  'project-setting.model-engine': '模型引擎',
  'project-setting.X-Base-model-engine': 'X-Base模型引擎',
  'project-setting.BIM-Base-model-engine': 'BIM-Base模型引擎',
  'project-setting.road-level': '道路等级',
  'project-setting.coordinate-system': '坐标系',
  'project-setting.elevation-datum': '高程基准',
  'project-setting.design-load': '设计载荷',
  'project-setting.safety-level': '安全等级',
  'project-setting.environmental-category': '环境类别',
  'project-setting.seismic-grade': '地震等级',
  'project-setting.project-template': '项目模版',
  'project-setting.project-time': '项目时间',
  'project-setting.integrated-product-application': '统建产品应用',
  'project-setting.project-overview': '项目概况',
  'project-setting.edit': '编辑',
  'project-setting.cancel': '取消',
  'project-setting.ok': '确定',
  'project-edit-success': '编辑成功',
  'project-add-success': '添加成功',
  'project-setting.project-name-errMsg': '请输入项目名称',
  'project-setting.project-name-length-errMsg': '最多可以输入255个字符',
  'project-setting.name-exclude': '名称不能包含下列任何字符：\\/:*?"<>|',
  'project-setting.model-engine-errMsg': '请选择模型引擎',
  'project-setting.project-code-errMsg': '请输入项目编码',
  'project-setting.project-code-english-number-errMsg': '只能输入英文和数字',
  'project-setting.project-location-errMsg': '请输入项目位置',
  'project-setting.project-type-errMsg': '请选择项目类型',
  'project-setting.coordinate-system-errMsg': '请选择坐标系',
  'project-setting.elevation-reference-errMsg': '请选择高程基准',
  'project-setting.environmental-category-errMsg': '请选择环境类别',
  'project-setting.earthquake-magnitude-errMsg': '请选择地震等级',
  'project-setting.project-date-errMsg': '请选择项目日期',
  'project-setting.max-length-errMsg': '最多不超过500字',
  'project-setting.road-level-errMsg': '请选择道路等级',
  'project-setting.team-name': '团队',
  'project-setting.search': '查询',
  'project-setting.clear': '清空',
  'project-setting.matrix-of-responsibilities': '责任分工矩阵',
  'project-setting.team-list': '团队列表',
  'project-setting.add-team': '添加团队',
  'project-setting.color': '颜色',
  'project-setting.name': '名称',

  'project-setting.location-change': '位置变更',
  'project-setting.top': '置顶',
  'project-setting.bottom': '置底',
  'project-setting.view': '查看',
  'project-setting.setting': '设置',
  'project-setting.move-up': '上移',
  'project-setting.move-down': '下移',
  'project-setting.team': '团队',
  'project-setting.team-members': '团队成员',
  'project-setting.team-permission-config': '团队权限配置',
  'project-setting.team-relationship': '下级团队从属关系',
  'project-setting.manage': '管理',
  'project-setting.share': '共享',
  'project-setting.team-task-description': '团队任务描述',
  'project-setting.teamName': '团队名称',
  'project-setting.teamMembers': '团队成员',
  'project-setting.permission-level': '权限级别',
  'project-setting.not-active': '未激活',
  'project-setting.delete': '删除',
  'project-setting.noData-title': '暂无任务分工',

  'project-setting.team-name-errMsg': '请输入团队名称',
  'project-setting.max-width-errMsg': '最多可以输入241个字符',
  'project-setting.sequence-adjustment': '顺序调整',
  'project-setting.team-admins': '团队管理员',
  'project-setting.team-manage': '团队管理',
  'project-setting.member-count': '成员人数',
  'project-setting.operations': '操作',
  'project-setting.index': '序号',
  'project-setting.professional-attributes': '专业属性',
  'project-setting.attribute-list': '属性列表',
  'project-setting.return': '返回',
  'project-setting.unit': '单位',
  'project-setting.example-value': '示例值',
  'project-setting.description': '描述',
  'project-setting.please-select-team-color': '请选择团队颜色',
  'project-setting.community': '共享',

  'project-setting.schematic-diagram-of-milestone-nodes': '里程碑节点示意图',
  'project-setting.project-start': '项目启动',
  'project-setting.project-end': '项目结束',
  'project-setting.start-end-time': '计划结束时间',
  'project-setting.description-of-deliverables': '交付物描述',
  'project-setting.create-milestones': '创建里程碑',
  'project-setting.time': '里程碑时间',
  'project-setting.view-diagram': '查看图示',
  'project-setting.reset': '清空',
  'project-setting.is-delete': '是否删除该项目',
  'project-setting.create': '创建',
  'project-setting.milestones': '里程碑',
  'project-setting.dialog-title': '里程碑',
  'project-setting.end-time': '结束时间',
  'project-setting.describe': '交付物描述',
  'project-setting.end-time-err-msg': '结束时间必填',
  'project-setting.milestones-name-err-msg': '里程碑名称必填',
  'project-setting.milestones-name': '里程碑名称',
  'project-setting.describe-err-msg': '交付物描述必填',

  'project-setting.sequence-number': '序号',
  'project-setting.process-template-name': '名称',
  'project-setting.process-status': '流程状态',
  'project-setting.node-number': '节点数',
  'project-setting.process-template-description': '流程模版说明',
  'project-setting.creator': '创建人',
  'project-setting.updater': '更新人',
  'project-setting.create-time': '创建时间',
  'project-setting.update-time': '更新时间',
  'project-setting.operation': '操作',
  'project-setting.process-list': '流程列表',
  'project-setting.add-process-template': '添加流程模版',
  'project-setting.is-release-process-template': '是否确认发布流程模版？',
  'project-setting.is-delete-process-template': '是否确认删除流程模版？',
  'project-setting.release': '发布',

  'project-setting.flow-template-name': '流程模版',
  'project-setting.please-enter-name': '请输入名称',
  'project-setting.flow-template-description': '说明',
  'project-setting.please-enter-description': '请输入说明',
  'project-setting.please-select-team': '请选择团队',
  'project-setting.general': '通用',
  'project-setting.add-node': '新增节点',
  'project-setting.auditor': '审核人',
  'project-setting.node-name': '节点名称',
  'project-setting.flow-node-configuration': '流程节点配置',
  'project-setting.loop-cardinality': '流程节点配置',
  'project-setting.signer-count': '设置会签人数',
  'project-setting.please-enter-signer-count': '请输入会签人数',
  'project-setting.enter-signer-count': '请输入会签人数',
  'project-setting.completion-condition': '完成条件',
  'project-setting.please-enter-completion-condition': '请输入完成条件',
  'project-setting.enter-completion-condition': '请输入完成条件',
  'project-setting.is-concurrent': '是否顺序执行',
  'project-setting.create-process-template': '创建流程模板',
  'project-setting.edit-process-template': '编辑流程模版',
  'project-setting.view-process-template': '查看流程模版',
  'project-setting.please-complete-node-name-or-auditor-info':
    '请完善节点名称或审核人信息！',
  'project-setting.please-complete-basic-info': '请完善基本信息！',
  'project-setting.create-success': '创建成功！',
  'project-setting.update-success': '修改成功！',
  'project-setting.add-success': '添加成功',
  'project-setting.edit-success': '编辑成功',
  'project-setting.published': '已发布',
  'project-setting.unpublished': '未发布',
  'project-setting.start': '发起',
  'project-setting.end': '结束',
  'project-setting.node': '节点',

  'project-setting.roadbed': '路基',
  'project-setting.road_surface': '路面',
  'project-setting.bridge': '桥梁',
  'project-setting.beam_bridge': '梁式桥',
  'project-setting.arch_bridge': '拱式桥',
  'project-setting.cable-stayed_bridge': '斜拉桥',
  'project-setting.suspension_bridge': '悬索桥',
  'project-setting.tunnel': '隧道',

  'project-setting.deliver': '交付',
  'project-setting.add_task': '添加任务',
  'project-setting.delate_task': '删除任务',
  'project-setting.team-task-name-errMsg': '请选择团队名称',
  'project-setting.team-start-end-time': '开始结束时间',
  'project-setting.team-start-end-time-errMsg': '请选择开始结束时间',
  'project-setting.task-description': '任务描述',
  'project-setting.task-description-errMsg': '请输入任务描述',
  'project-setting.dismantling-tasks': '拆解任务',
  'project-setting.main-tasks': '主任务',
  'project-setting.subtasks': '子任务',
  'project-setting.milestone-information': '里程碑信息',
  'project-setting.team-tasks': '团队任务',
  'project-setting.confirm-deletion': '确认删除？',
  'project-setting.deletion-hint':
    '当前任务包含子任务，如果删除将同子任务一并删除，是否继续？',
  'project-setting.milestone-deletion-hint':
    '当前里程碑存在任务信息，如果删除将同任务一并删除，是否继续？',
  'project-setting.add-task-error':
    '团队任务结束时间应小于等于里程碑的结束时间',
  'project-setting.add-dismantling-task-error':
    '拆解任务结束时间应小于等于所拆解一级任务的结束时间',
  'project-setting.application-expand': '展开全部',

  'project-setting.custom-content': '自定义内容',
  'project-setting.all-audit-required': '需全部审核',
  'project-setting.one-audit-required': '仅一人审核',
};
