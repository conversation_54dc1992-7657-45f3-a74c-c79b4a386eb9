const errorMessages = {
  // 模型管理服务-错误码
  '10370000':
    '模型管理服务相关的接口请求参数错误，需要检查参数是否符合API文档说明中的要求填写',
  '10370001': '上传的单个文件超过大小限制了',
  '10370002': '装配模型超过平台限制',
  '10370003': '上传的模型文件格式暂不支持',
  '10370004': '传模型时保存数据失败了',
  '10370005': '上传模型时请求接口超时',
  '10370006': '获取模型列表数据时出现错误',
  '10370007': '当模型被删除后，获取单个模型数据会返回该错误',
  '10370008':
    '当前模型的状态已经是转换中或排队中、模型类型为装配模型、请求的转换参数不支持当前格式时，会返回该错误',
  '10370009': '当前模型发起转换操作失败',
  '10370010':
    '当前模型的状态已经是转换成功或失败后不支持终止转换操作，会返回该错误',
  '10370011': '当前模型发起终止转换操作失败',
  '10370012':
    '装配模型或者普通模型转换中不支持获取转换详情，只有转换成功或者失败才能获取模型转换详情',
  '10370013': '获取模型的转换详情数据时失败了',
  '10370014': '模型名称包含了不允许的字符',
  '10370015':
    '装配模型时存在子模型的状态或者部分格式不允许装配，只有转换成功的部分子模型才允许装配',
  '10370016': '装配模型保存数据失败了',
  '10370017': '删除装配模型的子模型数据失败',
  '10370018': '编辑装配模型时保存数据失败了',
  '10370019': '由于资源数据出现了问题现在源文件失败',
  '10370020': '转换中的模型是不支持删除的',
  '10370021': '获取模型设置的预览参数错误',
  '10370022': '设置模型设置的预览参数',
  '10370023': '获取模型属性数据失败',
  '10370024': '根据构件ID获取构件属性失败',
  '10370025': '获取模型属性树状数据失败',
  '10370026': '当前文件发起转换操作失败',
  '10370027': '搜索模型树中匹配的数据失败',
  '10370028': '获取模型的状态失败',
  '10370029': '获取装配模型与子模型的源数据失败',
  '10370030': '无格式转换服务',
  '10370031': '当前格式转换服务不支持该文件格式',
  '10370033': '当前资源绑定的group_id与传值不匹配',

  // 资产管理服务-错误码
  '10381001': '未找到指定资产文件或数据记录',
  '10381002': '资产名称不能为空',
  '10381003': '资产名称不能包含特殊字符',
  '10381004': '资产名称不能包含空格',
  '10381005': '资产文件类型不正确',
  '10381006': '资产文件上传超时',
  '10381007': '资产文件上传失败',
  '10381008': '资产文件上传类型不正确',
  '10381009': '单个资产文件大小超过限制',
  '10381010': '获取应用已使用存储空间失败',
  '10381011': '更新已使用存储空间失败',
  '10381012': '资产类型错误，仅支持影像、地形、倾斜摄影',
  '10381013': '资产创建失败',
  '10381014': '没有权限操作此资产',
  '10381015': '批量删除资产文件失败',
  '10381016': '终止资产转换失败',
  '10381017': '资产当前状态不允许终止转换',
  '10381018': '获取资产详情失败',
  '10381019': '资产转换请求失败',
  '10381020': '资产当前状态不允许转换',
  '10381021': '获取资产列表失败',
  '10381022': '资产ID不正确',
  '10381023': '资产ID列表包含重复值',
  '10381024': '请求参数不正确，类型错误、值错误或参数缺失',
  '10381025': '装配资产不能被转换',
  '10381026': '含有无效的资产id',
  '10381027': '装配资产文件大小超过上限',
  '10381028': '根据装配资产id获取关联自资产失败',
  '10381029': '资产装配/编辑失败',
  '10381030': '资产状态错误',
  '10381031': '资产类型错误',
  '10381032': '装配资产不能参与装配',
  '10381033': '装配资产名称为空',
  '10381034': '获取bim资产模型属性失败',
  '10381035': '获取bim资产构件属性失败',
  '10381036': '无格式转换服务',
  '10381037': '模型源文件中无可用的ODX文件',
  '10381038': '下载BIM资产失败',
  '10381039': '当前资源绑定的group_id与传值不匹配',

  // 构件管理服务-错误码
  '10440000':
    '构件管理服务相关的接口请求参数错误，需要检查参数是否符合API文档说明中的要求填写',
  '10440001': '上传的构件超过大小限制',
  '10440002': '不允许构件名称包含特殊字符',
  '10440003': '当前构件状态不允许查看构件转换详情',
  '10440004': '不允许构件发起转换',
  '10440005': '构件暂不支持当前文件格式',
  '10440006': '保存构件信息时失败',
  '10440007': '文件已删除或者ID错误时，获取单个构件信息失败',
  '10440008': '调用转换接口失败',
  '10440009':
    '获取构件的转换结果和参数失败（注意：此条与10440003重复描述，但错误码不同，保留原描述）',
  '10440010': '获取构件列表数据失败',
  '10440011': '构件转换终止失败',
  '10440012': '当前构件状态不允许终止转换',
  '10440013': '删除构件失败',
  '10440014': '当前状态不允许删除',
  '10440015': '无格式转换服务',
  '10440016': '获取预览配置失败',
  '10440017': '设置预览配置失败',
  '10440018': '下载构件失败',
  '10440019': 'groupId与构件上传时保存的groupId不一致',

  // 场景管理服务-错误码
  '10390001': '创建/更新场景失败',
  '10390002': '创建、更新场景时，提交的ids中含有无效的id',
  '10390003': '创建、更新场景时，提交的ids中含有重复的id',
  '10390004': '场景不存在',
  '10390005': '删除场景失败',
  '10390006': '获取场景列表失败',
  '10390007': '场景重命名失败',
  '10390008': '根据模型id获取场景列表失败',
  '10390009': '根据资产id获取场景列表失败',
  '10390010': '参数错误',
  '10390011': '获取资产总大小失败',
  '10390012': '获取模型总大小失败',
  '10390013': '超出场景文件大小限制',
  '10390014': '当前资源绑定的group_id与传值不匹配',

  // 文件管理服务-错误码
  '10580000':
    '文件管理服务相关的接口请求参数错误，需要检查参数是否符合API文档说明中的要求填写',
  '10580001': '上传的单个文件超过大小限制了',
  '10580002': '上传的文件格式暂不支持',
  '10580003': '上传文件保存数据失败',
  '10580004': '上传文件时请求接口超时',
  '10580005': '获取文件列表数据时出现错误',
  '10580006': '当文件已删除或者ID错误时，获取单个文件数据会返回该错误',
  '10580007': '当前文件不支持发起转换，转换中的文件都不支持发起转换',
  '10580008': '当前文件发起转换操作失败',
  '10580009': '当前文件不处于转换中间状态时不支持进行转换终止',
  '10580010': '当前文件发起终止转换操作失败',
  '10580011': '转换中的文件不支持获取转换详情数据',
  '10580012': '获取文件的转换详情数据时失败',
  '10580013': '文件名称包含了不允许的字符',
  '10580014': '下载文件时失败',
  '10580015': '不支持删除文件，转换中间状态的文件不能进行删除',
  '10580016': '删除文件失败',
  '10580017': '请求参数中group_id与文件绑定的group_id数据不一致',

  // 视点管理服务-错误码
  '10041000': '请求参数不正确，类型错误、值错误或参数缺失',
  '10041001': '保存视点图片失败，可能是图片数据不正确或者文件权限问题',
  '10041002': '视点描述包含特殊字符',
  '10041003': '文件类型(仅支持模型)不支持创建视点',
  '10041004': '获取模型信息失败',
  '10041005': '创建视点失败',
  '10041006': '分页获取视点列表失败',
  '10041007': '获取视点详情失败',
  '10041008': '视点已被删除',
  '10041009': '视点所属文件已被删除',
  '10041010': '删除视点失败',
  '10041011': '文件状态(仅转换成功文件支持创建视点)不支持创建视点',
  '10041014': '当前资源绑定的group_id与传值不匹配',

  // 语义模型服务-错误码
  '10490001': '请求参数不正确，类型错误、值错误或参数缺失',
  '10490002': '系统异常，请稍后重试，如有疑问请联系管理员',
  '10490003': '名称不合法',
  '10490004': '模型源不存在！',
  '10490005': '不支持的语义转换取消',
  '10490006': '不支持的语义转换信息获取',
  '10490007': '不支持的语义模型装配信息获取',
  '10490008': '不支持的语义模型装配',
  '10490009': '单体模型大小不能超过xGB',
  '10490010': '装配模型大小不能超过xGB',
  '10490011': '构件正在修改中',
  '10490012': '语义模型正在导出中',
  '10490013': '不支持的语义模型类型导出',
  '10490014': '语义装配模型名称已存在',
  '10490015': '语义模型名称已存在',
  '10490016': '该构件暂不支持数据写入',

  // 标签管理服务-错误码
  '10570000': '参数有误',
  '10570001': '创建标签失败',
  '10570002': '提交的id无效',
  '10570003': '提交的id重复',
  '10570004': '标签不存在',
  '10570005': '删除标签失败',
  '10570006': '绑定标签失败',
  '10570007': '资源文件id有误',
  '10570008': '根据资源文件信息获取绑定的标签失败',
  '10570009': '获取标签列表失败',
  '10570010': '更新标签失败',
  '10570011': '解绑标签失败',
  '10570012': '标签名称已存在',
  '10570013': '当前资源绑定的group_id与传值不匹配',

  // 内容关联管理服务-错误码
  '10590000': '参数有误',
  '10590001': '资源不存在',
  '10590002': '资源当前状态不可创建关联数据',
  '10590003': '无权限操作',
  '10590004': '无效的资源类型',
  '10590005': 'source字段值大小超出最大值限制（100MB）',
  '10590006': '创建失败',
  '10590007': '含有无效的id',
  '10590008': '含有重复的id',
  '10590009': '删除失败',
  '10590010': '更新失败',
  '10590011': '获取列表失败',
  '10590012': '删除的数据不存在',
  '10590013': '更新的数据不存在',

  // 构件树服务-错误码
  '10550000':
    '构件树服务相关的接口请求参数错误，需要检查参数是否符合API文档说明中的要求填写',
  '10550001': '获取装配信息出现错误',
  '10550002': '获取所有实例的Guid列表出现错误',
  '10550003': '获取实例的包围盒出现错误',
  '10550004': '生成类型树或空间树出现错误',
  '10550005': '生成WBS编码树出现错误',
  '10550006': '生成专业树出现错误',
  '10550007': '生成材料结构树出现错误',
  '10550008': '生成施工流水段树出现错误',
  '10550009': '更新材料结构树出现错误',
  '10550010': '更新施工流水段树出现错误',
  '10550011': '更新空间树出现错误',
  '10550015': '查询任务状态出现错误',
  '10550016':
    '结构树类型参数错误，需要检查当前接口支持的结构树类型与参数是否一致',
  '10550017': '结构树参数错误，传入的节点Id在指定树中未找到',
  '10550018': '模型格式不支持，需要生成的树类型暂不支持此模型格式',

  // 模型检查服务-错误码
  '10260001': 'level字段非法',
  '10260002': '请求参数不正确，类型错误、值错误或参数缺失',
  '10260003': '项目检查失败',
  '10260004': '创建集合失败',
  '10260005': '创建项目失败',
  '10260006': '创建语句失败',
  '10260007': '删除集合失败',
  '10260008': '删除项目失败',
  '10260009': '删除语句失败',
  '10260010': '获取集合信息失败',
  '10260011': '获取集合列表失败',
  '10260012': '获取检查结果失败',
  '10260013': '获取项目信息失败',
  '10260014': '获取项目列表失败',
  '10260015': '根据名称获取语句失败',
  '10260016': '获取语句信息失败',
  '10260017': '获取语句列表失败',
  '10260018': '更新集合失败',
  '10260019': '更新项目信息失败',
  '10260020': '更新语句信息失败',
  '10260021': '语句校验失败',
  '10260022': '语句名称已存在',
  '10260023': '集合名称已存在',
  '10260027': '项目名称已存在',
  '10260028': '源文件已损坏无法查看检查结果',
  '10260029': '无可执行规则',
  '10260030': '无可执行模型',
  '10260031': '验证条件验证失败',
  '10260032': '文件类型错误',
  '10260033': '上传文件名称已存在',
  '10260034': '文件格式错误',
  '10260035': '必填项不得为空',
  '10260036': '存在重复的条件名称',
  '10260037': '处理失败',
  '10260038': '存在数据超过导入限制',
  '10260039': '存在“值类型”错误',
  '10260040': '存在“值类型”和“条件值”不匹配',
  '10260041': '限制导入999条数据',
  '10260042': '方案已存在',
  '10260043': '验证条件已存在',
  '10260044': '方案类型不匹配',
  '10260045': '无可执行的验证条件方案',
  '10260046': '文件上传失败',
  '10260047': '文件类型错误',
  '10260048': '文件格式错误',
  '10260049': '规则名称不得为空或重复',
  '10260050': '导出检查结果失败',
  '10260051': '生成规则失败',

  // 图谱管理服务-错误码
  '10330004': '无操作权限',
  '10330005': '（已重复，见10330019）生成子图失败（旧）',
  '10330007': '节点属性类型与属性值不符',
  '10330008': '本体节点名称已存在',
  '10330009': '节点名称已存在',
  '10330010': '名称不合法',
  '10330011': '属性已存在',
  '10330012': '关系类型名称重复',
  '10330013': '参数校验失败',
  '10330014': '新建节点失败',
  '10330015': '新建节点关系失败',
  '10330016': '新建本体节点失败',
  '10330017': '新建本体节点关系失败',
  '10330018': '新建关系类型失败',
  '10330019': '子图创建失败（覆盖10330005）',
  '10330029': '获取关系类型列表失败',
  '10330030': '获取子图失败',
  '10330031': '获取子图列表失败',
  '10330038': '获取关系类型信息失败',
  '10330041': '获取节点信息失败',
  '10330042': '获取本体节点信息失败',
  '10330043': '获取本体节点列表失败',
  '10330045': '选取节点重复',
  '10330046': '当node_type为1时render_path为必填',
  '10330047': '获取分类失败',
  '10330048': '服务处理失败',
  '10330049': '场景未找到',

  // 权限管理服务-错误码
  '10400000':
    '角色权限服务相关的接口请求参数错误，需要检查参数是否符合文档说明中的要求',
  '10400001': '角色名称包含不允许的特殊字符',
  '10400002': '角色描述中包含不允许的特殊字符',
  '10400003': '保存角色数据失败',
  '10400004': '获取角色列表数据失败',
  '10400005': '获取单个角色详情数据失败', // 注意：原数据中两个10400005，这里假设第二个是正确的描述
  '10400006': '更新角色信息时失败',
  '10400007': '角色已经处于激活状态，不能再进行激活的操作',
  '10400008': '更新角色状态为激活或者禁用时失败',
  '10400009': '角色已经处于禁用状态，不能再进行禁用的操作',
  '10400010': '当数据不存在或者已删除时，删除角色时会失败',
  '10400011': '向角色批量绑定用户时失败',
  '10400012': '获取某个角色中绑定的用户列表数据失败',
  '10400013': '向解绑角色与用户关系时失败', // 注意：原数据中为“向解绑角与色用户关系时失败”，已修正
  '10400014': '路由的名称已存在。一个应用中每个路由的名称是唯一的',
  '10400015': '添加一个新的路由失败了',
  '10400016': '获取路由列表失败',
  '10400017': '如果一个路由已被删除后则获取路由详细信息失败',
  '10400018': '更新路由信息失败',
  '10400019': '如果路由已经被删除时则删除路由失败',
  '10400020': '获取某个角色与路由的设置的权限关系时失败了',
  '10400021': '编辑角色与路由的权限关系失败',
  '10400022': '校验权限的接口url与name至少填一个，如果都为空则报此错误',
  '10400023':
    '角色权限更新等部分接口需要批量传路由id时要保证路由id不能重复传入',
  '10400024': '要设置的路由父ID不能在是当前路由的子级',
  '10400025': '角色总数量已达上限',
  '10400028': '当前资源绑定的group_id与传值不匹配',

  // 碰撞检查服务-错误码
  '10350001': '请求参数不正确，类型错误、值错误或参数缺失',
  '10350002': '系统异常，请稍后重试，如有疑问请联系管理员',
  '10350003': '名称不合法',
  '10350004': '存在重名的检查名称',
  '10350005': '检查已完成，操作失败',
  '10350006': '当前版本仅支持单个文件进行检查',

  // 模型对比服务-错误码
  '10460001': '请求参数不正确，类型错误、值错误或参数缺失',
  '10460002': '系统异常，请稍后重试，如有疑问请联系管理员',
  '10460003': '名称不合法',
  '10460004': '模型对比已完成，操作失败（如取消操作）',
  '10460005': '存在重名的检查名称',
  '10460006': '不可选择两个相同的模型进行对比',

  // 构件集服务-错误码
  '10220001': '请求参数不正确，类型错误、值错误或参数缺失',
  '10220002': '构件集名称已存在',
  '10220003': '构件集名称包含特殊字符',
  '10220004': '名称长度超过限制',
  '10220005': '系统异常，请稍后重试，如有疑问请联系管理员',
  '10220006': '构件集不存在',
  '10220007': '构件集搜索中，无法进行保存',
  '10220008': '构件集搜索失败，无法进行保存',
  '10220009': '构件集搜索结果无法重复保存',
  '10220010': '当前文件有未完成的搜索进程',
  '10220011': '构件集搜索条件缺少类别',
  '10220012': '构件集搜索条件类别无效',
  '10220013': '构件集搜索仅支持3个条件',
  '10220014': '构件集搜索每个条件仅支持3个表达式',
  '10220015': '存在重名的构件属性',
  '10220016': '搜索条件属性值超长',
  '10220017': '属性值不能为空',
  '10220018': '属性值仅支持数字',
  '10220019': '无效的模型路径',
  '10220020': '部分构件集不存在',
  '10220021': '构件集必须包含构件',

  // 数据高级搜索服务-错误码
  '10200001': '系统异常，请稍后重试，如有疑问请联系管理员',
  '10200002': '请求参数不正确，类型错误、值错误或参数缺失',
  '10200003': '搜索记录名称已存在',
  '10200004': '搜索记录名称包含特殊字符',
  '10200005': '名称长度超过限制',
  '10200006': '搜索记录不存在',
  '10200010': '当前应用有未完成的搜索进程',
  '10200011': '存在重名的构件属性',
  '10200012': '处理中，禁止编辑',
  '10200013': '该模型已被删除',

  // 数据标准化服务-错误码
  '10170001': '入参校验失败',
  '10170002': '系统异常',
  '10170003': '标准已经发布',
  '10170004': '标准已经卸载',
  '10170005': '上传文件名称已存在',
  '10170006': '属性不允许存在子集',
  '10170007': '获取标准失败',
  '10170008': '获取构件规则失败',
  '10170009': '获取模型数据失败',
  '10170010': '标准条目编码已经存在',
  '10170011': '标准已归档版本不允许编辑',
  '10170012': '标准已归档版本不允许发布',
  '10170013': '标准版本已存在',
  '10170014': '移动失败',
  '10170015': '标准不允许删除',
  '10170016': '编码已存在',
  '10170017': '名称已存在',
  '10170018': '名称不能为空',
  '10170019': '编码不能为空',
  '10170020': '版本不能为空',
  '10170021': '分类或者构件不能为空',
  '10170022': '属性名称不能为空',
  '10170023': '属性值不能为空',
  '10170024': '名称不合法',
  '10170025': '编码不合法',
  '10170027': '描述包含非法字符',
  '10170028': '请选择正确的标准结构',
  '10170029': '分类标准编码不允许为中文',
  '10170030': '交付标准编码不允许为中文',
  '10170031': '编码重复',
  '10170032': '该名称已经存在',
  '10170033': '标准映射规则为空',
  '10170034': '该标准暂未发布',
  '10170035': '公共标准不允许编辑',
  '10170036': '标准不允许复制',
  '10170037': '标准不允许升级',
  '10170038': '升版前后版本标准文件名称不一致',
  '10170039': '规则数超出最大值',
  '10170040': '规则存在相同的条件',
  '10170041': '文件格式错误',
  '10170042': '名称长度超过上限',
  '10170043': '编码长度超过上限',
  '10170045': '标准未找到',
  '10170046': '规则未找到',
  '10170047': '属性字典未找到',
  '10170048': '标准映射未找到',
  '10170049': '公共标准不允许发布',
  '10170050': '标准上传失败',
  '10170051': '上传失败',
  '10170052': '文件类型错误',
  '10170053': '上传文件名称已存在',
  '10170054': '文件格式错误',
  '10170055': '必填项不得为空',
  '10170056': '存在重复的数据',
  '10170057': '存在数据超过导入限制',
  '10170058': '存在 “属性类型/单位”错误',
  '10170059': '找不到输入的枚举属性名称，请先在属性字典中维护该字段',
  '10170060': '缺省值类型与属性类型不匹配',
  '10170061': '限制导入999条数据',

  // 服务模块-通用错误码
  '10001000': '请求头中获取token无效，或者解析token失败',
  '10001001': '当前应用未购买该服务',
  '10001002': '应用被删除后，查找应用数据失败',
  '10001003': '应用中该服务状态没有生效、被禁用、已过期都会提示该错误',
  '10001004':
    '未获取到某个资源的操作权限，例如输入别的应用的对应资源时会提示该错误',
  '10001005': '上传的资源大小超过了应用剩余的存储空间',
  '10001006': '获取应用下的服务信息失败',
  '10001007': '请求太频繁',
  '10001008': '获取应用的token失败，可能是appId或appKey错误',
};

export default function showErrorMessage(errorCode: string) {
  const errorMessage = `${(errorMessages as any)[errorCode]}` || 'error';
  return errorMessage;
}
// showErrorMessage("10580000"); // 显示文件管理服务相关的接口请求参数错误
