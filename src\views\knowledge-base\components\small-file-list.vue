<template>
  <div class="base-content">
    <div class="base-header">
      <span class="title">{{ $t('knowledge.personal-knowledge') }}</span>
      <!-- TODO 共享个人知识库 -->
      <!-- <template v-else>
        <a-space>
          <shareOpt class="btn" />
        </a-space>
      </template> -->
    </div>
    <div>
      <span class="count-info">
        {{ $t('knowledge.knowledge') }}{{ fileCnt
        }}{{ $t('knowledge.content') }}/{{ getFileSize(usedStorage) }}
      </span>
    </div>

    <a-divider :margin="8" />

    <div class="file-list">
      <div class="small-container">
        <div v-if="isSearch" class="search-box">
          <a-input-search
            v-model="searchVal"
            style="width: 250px"
            :placeholder="$t('knowledge.need-place-input')"
            allow-clear
            @change="searchHandle"
            @search="searchHandle"
            @keydown.enter="searchHandle"
          />
          <close @click="closeSearch"></close>
        </div>
        <div v-else class="opt-list">
          <div style="flex: 1; height: 100%">
            <a-space v-show="canGoBack" :size="4">
              <ArrowLeft
                :class="canGoBack ? 'has-pointer' : 'disable-btn'"
                @click="goBack"
              />
              <span class="back-text">{{
                $t('knowledge.go-backgo-back')
              }}</span>
            </a-space>
          </div>
          <a-space size="medium">
            <addFolder class="light-btn" @click="createFolderBtnClick" />
            <!-- :class="isAdding ? 'disable-btn' : ''" -->
            <uploadOpt class="light-btn" @click="uploadClick" />
            <search class="light-btn" @click="searchClick" />
          </a-space>
        </div>
        <!-- <div style="overflow: hidden; margin-top: 4px"> -->
        <a-spin
          :loading="tableLoading"
          style="height: calc(100% - 24px); width: 100%"
        >
          <div class="small-file-list">
            <a-space
              v-if="!tableData.length"
              :size="16"
              direction="vertical"
              class="empty-container"
            >
              <EmptyFolder />
              <span>{{ $t('knowledge.no-result') }}</span>
            </a-space>
            <div v-for="record in tableData" :key="record.id" class="file-name">
              <img
                :src="getFileIcon(record.type)"
                style="width: 28px; height: 28px"
              />
              <!-- <div class="name-box">
                <span
                  :title="item.name"
                  class="has-pointer"
                  @click="handleOpenFileOrFolder(item)"
                  >{{ item.name }}</span
                >
              </div> -->
              <div class="name-box">
                <div
                  v-if="getDisplayMode(record).showText"
                  class="name-text-wrapper"
                >
                  <span
                    :title="record.name"
                    class="has-pointer name-text"
                    @click="handleOpenFileOrFolder(record)"
                    >{{ record.name }}</span
                  >
                </div>
                <div
                  v-if="getDisplayMode(record).showInput"
                  class="input-wrapper"
                >
                  <a-input
                    ref="newFolderNameRef"
                    v-model="record.name"
                    :placeholder="$t('knowledge.input-file-name-tips')"
                    allow-clear
                    class="name-input"
                    @blur="handleCreateOrRenameFolder(record)"
                    @press-enter="($event.target as any)?.blur()"
                  />
                </div>
              </div>
            </div>
          </div>
        </a-spin>
        <!-- </div> -->
      </div>
    </div>
    <a-upload
      action="/"
      :show-file-list="false"
      :auto-upload="false"
      style="display: none"
      @change="beforeUpload"
    >
      <template #upload-button>
        <div ref="uploadRef"></div>
      </template>
    </a-upload>
    <UploadPanel
      v-model:visible="uploadModalVisible"
      :files="selectedFileList"
      :position="{
        top: 270,
        left: 800,
      }"
      @upload-single-success="uploadSuccessCb"
      @finish="selectedFileList = []"
    ></UploadPanel>
  </div>
</template>

<script lang="ts" setup>
  import { computed, nextTick, ref, watch } from 'vue';
  import { storeToRefs } from 'pinia';
  import { Message } from '@arco-design/web-vue';
  import { useKnowledgeBaseStore, useUploadFileStore } from '@/store';
  import { getFileSize } from '@/utils/file';
  import { Node, CustomFileItem } from '../types';
  import getFileType from '../utils';
  import useFolderContent from '../composables/useFolderContent';
  import useUIState from '../composables/useUIState';
  import acceptFileType from '@/config/accept-file-types.json';
  import { createFolder, saveFile } from '../api';
  import addFolder from '@/assets/images/knowledge-base/add-folder.svg';
  import uploadOpt from '@/assets/images/knowledge-base/upload.svg';
  import ArrowLeft from '@/assets/images/knowledge-base/arrow-left-small.svg';
  import EmptyFolder from '@/assets/images/knowledge-base/empty-folder.svg';
  import search from '@/assets/images/knowledge-base/search.svg';
  import close from '@/assets/images/knowledge-base/close.svg';
  // import shareOpt from '@/assets/images/knowledge-base/share-opt.svg'; // TODO 分享个人知识库
  import UploadPanel from '@/components/upload-panel/index.vue';
  import i18n from '@/locale/index';

  const { t } = i18n.global;

  const knowledgeBaseStore = useKnowledgeBaseStore();
  const { folderId, fileCnt, usedStorage, currentIndex, history, baseRootId } =
    storeToRefs(knowledgeBaseStore);

  const {
    tableData,
    openFileOrFolder,
    getFileIcon,
    tableLoading,
    queryFolderContent,
    getSearchFilehandle,
    checkFolderName,
  } = useFolderContent();

  const { getItemState, clearUIState } = useUIState();

  // 是否可后退
  const canGoBack = computed(() => currentIndex.value > 0);

  // 导航方法
  const goBack = () => {
    if (currentIndex.value > 0) {
      const newFolderId = history.value[currentIndex.value - 1].id;
      knowledgeBaseStore.changePath(newFolderId, currentIndex.value - 1);
    }
  };

  // 显示控制的计算属性
  const getDisplayMode = computed(() => {
    return (record: Node) => {
      const state = getItemState(record.id);
      return {
        showText: !state.isEditing,
        showInput: state.isEditing,
      };
    };
  });

  const newFolderNameRef = ref<Array<HTMLInputElement | null>>([]);

  // 是否正在添加文件夹，每次只能新建一个文件夹
  const isAdding = ref(false);

  // 新建文件夹处理
  const createFolderBtnClick = () => {
    // if (isAdding.value) {
    //   Message.warning('请先完成当前文件夹的创建！');
    //   return;
    // }
    const newId = Date.now().toString();
    const newFolder = {
      id: newId,
      name: '',
      parentId: folderId.value,
      type: '文件夹',
      updateDate: '',
      isFolder: true,
      isNew: true,
    };

    tableData.value.unshift(newFolder);
    const state = getItemState(newId);
    state.isEditing = true;
    isAdding.value = true;
    nextTick(() => {
      if (newFolderNameRef.value.length) {
        newFolderNameRef.value[0]?.focus();
      }
    });
  };

  // 文件夹创建
  const handleCreateOrRenameFolder = async (item: Node) => {
    const name = item.name.trim();
    const isNameValid = await checkFolderName(name);
    if (!isNameValid) {
      return;
    }

    try {
      if (item.isNew) {
        const res = await createFolder({
          name,
          parentId: item.parentId,
        });

        if (res.status) {
          const state = getItemState(item.id);
          state.isEditing = false;
          Message.success(t('knowledge.create-folder-success-msg'));
          isAdding.value = false;
          await queryFolderContent(() => clearUIState());
        }
      }
    } catch (error) {
      Message.error(t('knowledge.operate-fail-msg'));
      console.error('Folder operation error:', error);
    }
  };

  // 打开文件夹/文件
  const handleOpenFileOrFolder = (item: Node) => {
    openFileOrFolder(item);
  };

  // 监听 folderId 变化时也需要清空状态
  watch(
    () => folderId.value,
    async (nValue) => {
      if (nValue) {
        await queryFolderContent(() => clearUIState());
      }
    },
    {
      immediate: true,
    }
  );

  /** 上传文件begin */
  const uploadFileStore = useUploadFileStore();

  // 上传文件列表
  const uploadModalVisible = ref(false);
  const uploadRef = ref(null);
  // 上传文件列表
  const selectedFileList = ref([]);
  const fileSizeLimit = 20; // 单位MB

  const uploadClick = () => {
    if (uploadFileStore.uploadFileList.length) {
      uploadModalVisible.value = true;
    } else {
      uploadRef.value?.click();
    }
  };
  // 上传前校验
  const beforeUpload = (fileArr: any) => {
    const files = fileArr.map((fileItem: any) => {
      const fileA = fileItem.file;
      const suffix = fileA.name.split('.').pop()?.toLowerCase() || '';
      if (!acceptFileType.includes(suffix)) {
        Message.error(
          `${t('knowledge.unsupport-upload')} ${suffix} ${t(
            'knowledge.types-file'
          )}！`
        );
        return false;
      }
      // // 文件大小验证
      if (fileA.size > fileSizeLimit * 1024 * 1024) {
        Message.error(
          `${t('knowledge.file-size-no-exceed')} ${fileSizeLimit}MB！`
        );
        return false;
      }
      return fileA;
    });

    selectedFileList.value = files.filter((item: any) => {
      let result = false;
      if (item) result = true;
      return result;
    });
    if (!uploadModalVisible.value && selectedFileList.value.length)
      uploadModalVisible.value = true;
  };
  // 文件保存逻辑
  const handleSaveFile = async (fileItem: CustomFileItem) => {
    try {
      const params = {
        fileSize: fileItem.size,
        folderId: folderId.value,
        name: fileItem.name,
        ossToken: fileItem.fileToken || '',
        type: getFileType(fileItem.name),
      };

      const res = await saveFile(params);
      if (res.status) {
        await queryFolderContent(() => clearUIState());
        // Message.success('文件上传成功！');
        knowledgeBaseStore.setNeedRefresh(true);
      }
    } catch (error) {
      Message.error(t('knowledge.save-folder-fail'));
      console.error('Save file error:', error);
    }
  };
  // 单个文件上传成功回调
  const uploadSuccessCb = (file: any) => {
    handleSaveFile(file);
  };

  /** 上传文件end */

  const isSearch = ref(false);
  const searchVal = ref(''); // 搜索值

  // 搜索按钮
  const searchClick = () => {
    searchVal.value = '';
    isSearch.value = true;
  };

  // 搜索文件
  const searchHandle = () => {
    const data = {
      fileName: searchVal.value,
      personBaseId: baseRootId.value,
    };
    getSearchFilehandle(data);
  };

  // 关闭搜索
  const closeSearch = async () => {
    isSearch.value = false;
    await queryFolderContent(() => clearUIState());
  };
</script>

<style scoped lang="less">
  .base-content {
    position: relative;
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .base-header {
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        margin-right: 20px;
        font-size: 20px;
        font-weight: 500;
        line-height: 24px;
      }
    }
  }

  .disable-btn {
    cursor: not-allowed !important;
  }

  :deep(.has-pointer) {
    cursor: pointer;
  }

  .count-info {
    display: inline-block;
    font-size: 14px;
    color: #86909c;
    line-height: 21px;
  }

  .file-list {
    flex: 1;
    overflow: hidden;
  }

  .small-container {
    height: 100%;
    overflow: hidden;

    .opt-list {
      margin-bottom: 4px;
      height: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .back-text {
        color: #4e5969;
        line-height: 20px;
      }

      .light-btn {
        color: #86909c;
        cursor: pointer;
      }
    }
  }

  .file-name {
    height: 54px;
    display: flex;
    align-items: center;

    .name-box {
      position: relative;
      height: 32px;
      flex: 1;
      margin-right: 20px;
      margin-left: 13px;

      .name-text-wrapper,
      .input-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      .name-text {
        display: block;
        width: 100%;
        height: 100%;
        font-size: 16px;
        color: #4e5969;
        line-height: 32px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .name-text:hover {
        color: #3366ff;
      }
    }
  }

  .empty-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    span {
      font-size: 14px;
      line-height: 24px;
      color: #4e5969;
    }
  }

  .question-box {
    margin-top: 20px;
    min-height: 60px;
  }

  .small-file-list {
    height: 100%;
    overflow: auto;
  }

  :deep(.arco-upload) {
    height: 16px;
  }

  .search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      cursor: pointer;
    }
    :deep(.arco-input-search) {
      height: 28px;
    }
    :deep(.arco-input-wrapper) {
      background-color: #fff;
      border: 1px solid #c9cdd4;
      border-radius: 8px !important;
    }
  }
</style>
