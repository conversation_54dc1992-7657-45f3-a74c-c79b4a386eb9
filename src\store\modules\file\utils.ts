import { FolderMessage } from '@/api/tree-folder';
import i18n from '@/locale';

const global = i18n.global;

export const defaultTreeData = [
  {
    name: 'WIP',
    id: 'WIP',
    idPath: '/WIP',
    children: [],
  },
  {
    name: 'Shared',
    id: 'Shared',
    idPath: '/SHARED',
    children: [],
  },
  {
    name: 'Published',
    id: 'Published',
    idPath: '/PUBLISHED',
    children: [],
  },
  {
    name: 'CommunalSpace',
    id: 'COMMUNALSPACE',
    idPath: '/COMMUNALSPACE',
    children: [],
  },
];

export function initSessionStorageData(key: string, defaultVal: any = []) {
  const data = sessionStorage.getItem(key);
  if (data) {
    return JSON.parse(data);
  }
  return defaultVal;
}

export function setSessionStorageData(key: string, val: any) {
  sessionStorage.setItem(key, JSON.stringify(val));
}
// 宽度优先遍历，需改成不破坏proxy的方式
export function getFolderById(
  rootTree: FolderMessage[],
  id: string
): FolderMessage {
  const parentFolder = JSON.parse(JSON.stringify(rootTree));
  if (!parentFolder) return {}; // 空树情况处理

  let result = {}; // 存储遍历结果
  const queue = parentFolder; // 初始化队列，放入根节点

  while (queue.length > 0) {
    const currentNode = queue.shift(); // 取出队列第一个节点

    // 将当前节点的所有子节点加入队列
    if (currentNode.children) {
      currentNode.children.forEach((item: any) => {
        queue.push(item);
      });
    }

    if (currentNode.id === id) {
      queue.length = 0;
      result = currentNode;
    }
  }

  return result;
}

const i18PathMap = new Map<string, 'englishPath' | 'path'>()
  .set('zh-CN', 'path')
  .set('en-US', 'englishPath');

function getNameList(record: FolderMessage) {
  const path = record[i18PathMap.get(global.locale)!] || record.path;
  return path?.split('/') || [];
}

export function getBreadcrumbs(folder: any) {
  if (folder.idPath) {
    const idList = folder.idPath.split('/');
    idList.shift();

    const nameList = getNameList(folder);
    nameList.shift();
    return idList.map((item: string, index: number) => {
      return {
        name: nameList[index],
        id: item,
      };
    });
  }
  return [];
}
