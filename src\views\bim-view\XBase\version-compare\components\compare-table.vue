<template>
  <div class="compare-list-box">
    <a-card
      ref="cardList"
      :title="$t('model-viewer.compareListTitle')"
      class="list-card"
    >
      <template #extra>
        <icon-close class="icon-close" @click="closeList" />
      </template>
      <a-card hoverable :bordered="true">
        <a-tabs default-active-key="1" @change="changeTab">
          <a-tab-pane :key="1" :title="$t('model-viewer.compareInfo.add')">
          </a-tab-pane>
          <a-tab-pane :key="2" :title="$t('model-viewer.compareInfo.delete')">
          </a-tab-pane>
          <a-tab-pane :key="3" :title="$t('model-viewer.compareInfo.update')">
          </a-tab-pane>
        </a-tabs>
        <div class="compare-card-box">
          <a-table
            stripe
            row-key="guid"
            :scroll="{
              y: 400,
            }"
            :scrollbar="true"
            :bordered="false"
            :columns="columnsDiff"
            :data="diffData"
            :pagination="false"
            style="height: 400px"
            @row-click="recordDisable"
          >
            <template #result="{ record }">
              <span
                v-if="record.result === $t('model-viewer.errorResult')"
                style="color: red"
                >{{ record.result }}</span
              >
              <span v-else style="color: green">{{ record.result }}</span>
            </template>
          </a-table>
          <div style="margin-top: 15px; margin-bottom: -10px">
            <a-switch
              v-model="switchValue"
              type="round"
              size="small"
              @change="switchChange"
            >
              <template #checked-icon>
                <icon-check />
              </template>
              <template #unchecked-icon>
                <icon-close />
              </template>
            </a-switch>
            <span class="switchText">{{
              $t('model-viewer.showDiffOnly')
            }}</span>
          </div>
          <a-table
            key="property_group_name"
            stripe
            :scroll="{
              y: 300,
            }"
            :scrollbar="true"
            :bordered="false"
            :columns="columnsProps"
            :data="compareInfo.propInfo"
            :pagination="false"
            show-empty-tree
            :default-expand-all-rows="true"
            :hide-expand-button-on-empty="true"
            style="margin-top: 20px; height: 300px"
          >
            <template #propertyName="{ record }">
              {{ record.property_group_name || record.property_name }}
            </template>
            <template #updateBase="{ record }">
              <span v-if="record.update || tabValue === 2" style="color: red">
                {{ record.base_property_value }}</span
              >
              <span v-else>{{ record.base_property_value }}</span>
            </template>
            <template #updateNew="{ record }">
              <span v-if="record.update || tabValue === 1" style="color: red">
                {{ record.new_property_value }}</span
              >
              <span v-else>{{ record.new_property_value }}</span>
            </template>
          </a-table>
        </div>
      </a-card>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch, toRaw, watchEffect } from 'vue';
  import { useRoute } from 'vue-router';
  import { Column } from '@/views/design/components/table-column';
  import useModelToolsStore from '@/store/modules/model-viewer/index';
  import { ViewerType } from '@/utils/xbase/type';
  import {
    GetElementldsByGuids,
    GetXBaseModelDiffContent,
    GetXBaseModelDiffProperty,
  } from '../api';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const toolStore = useModelToolsStore();
  const modelToolsStore = useModelToolsStore();
  const route = useRoute();
  const diffData = ref<object[]>([]);
  const tabValue = ref<string | number>(1);
  const curProp = ref();
  const switchValue = ref<boolean>(false);
  const lastDataId = ref<string>('');
  let baseViewer: ViewerType;
  let compareViewer: ViewerType;
  const props = defineProps({
    modelDiffId: {
      type: String,
      default: '',
    },
    viewers: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  interface CompareInfo {
    addName: string;
    deleteName: string;
    updateName: string;
    addData: object[];
    deleteData: object[];
    updateData: object[];
    propInfo: object[];
  }

  const compareInfo = reactive<CompareInfo>({
    addName: t('model-viewer.compareInfo.add'),
    deleteName: t('model-viewer.compareInfo.delete'),
    updateName: t('model-viewer.compareInfo.update'),
    addData: [],
    deleteData: [],
    updateData: [],
    propInfo: [],
  });

  const columnsDiff = computed<Column[]>(() => {
    return [
      {
        title: t('model-viewer.componentName'),
        dataIndex: 'name',
        ellipsis: true,
        tooltip: true,
        align: 'left',
      },
      {
        title: t('model-viewer.componentType'),
        dataIndex: 'category',
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
      {
        title: t('model-viewer.componentID'),
        dataIndex: 'guid',
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
    ];
  });

  const columnsProps = computed<Column[]>(() => {
    return [
      {
        title: t('model-viewer.propertyName'),
        dataIndex: 'property_name',
        ellipsis: true,
        tooltip: true,
        slotName: 'propertyName',
        align: 'left',
      },
      {
        title: t('model-viewer.basePropertyValue'),
        dataIndex: 'base_property_value',
        slotName: 'updateBase',
        ellipsis: true,
        tooltip: true,
        align: 'left',
      },
      {
        title: t('model-viewer.newPropertyValue'),
        dataIndex: 'new_property_value',
        ellipsis: true,
        slotName: 'updateNew',
        tooltip: true,
        align: 'left',
      },
    ];
  });

  const changeTab = (key: string | number) => {
    compareInfo.propInfo = [];
    curProp.value = {};

    tabValue.value = key;
    if (key === 1) {
      diffData.value = compareInfo.addData;
    } else if (key === 2) {
      diffData.value = compareInfo.deleteData;
    } else {
      diffData.value = compareInfo.updateData;
    }
  };

  const isUpdate = (data: any) => {
    data.children.forEach((item: any) => {
      if (item.base_property_value !== item.new_property_value) {
        item.update = true;
      }
    });
  };

  const recordDisable = async (row: any) => {
    curProp.value = row;
    const res = await GetXBaseModelDiffProperty({
      modelDiff: row.model_diff_content_id,
      onlyDiff: switchValue.value,
      groupId: route.query.projectId,
    });
    res.data.list = JSON.parse(
      JSON.stringify(res.data.list).replace(/"list"/g, '"children"')
    );

    baseViewer?.clearObjectsColor([`'${row.guid}'`]);
    compareViewer?.clearObjectsColor([`'${row.guid}'`]);
    compareInfo.propInfo = res.data.list;
    lastDataId.value = row.guid;

    const result = await GetElementldsByGuids({
      render_path: row.render_path,
      guids: [`'${row.guid}'`],
    });
    const elementId = Object.values(result.data.guid_element_id)[0];
    switch (tabValue.value) {
      case 1:
        compareViewer?.selectEntities(elementId);
        compareViewer?.getEntitiesBBoxAsync([elementId]).then((bbox: any) => {
          baseViewer?.gotoByBBox(bbox);
        });

        break;
      case 2:
        baseViewer?.selectEntities(elementId);
        baseViewer?.getEntitiesBBoxAsync([elementId]).then((bbox: any) => {
          compareViewer?.gotoByBBox(bbox);
        });
        break;
      case 3:
        compareInfo.propInfo.forEach((item) => {
          isUpdate(item);
        });
        baseViewer?.selectEntities(elementId);
        compareViewer?.selectEntities(elementId);
        break;
      default:
        break;
    }
  };
  const switchChange = () => {
    recordDisable(curProp.value);
  };
  const closeList = () => {
    baseViewer?.showAll();
    compareViewer?.showAll();
    baseViewer?.toHome();
    compareViewer?.toHome();
    compareInfo.propInfo = [];
    modelToolsStore.setCompareInfoShow(false);
  };

  const init = async () => {
    const contentParams = {
      modelDiffId: props.modelDiffId,
      groupId: route.query.projectId,
      pageSize: 1000, // 每页显示条数，默认为100，最小1，最大1000
    };
    const conentRes = await GetXBaseModelDiffContent(contentParams);
    compareInfo.addData = [];
    compareInfo.deleteData = [];
    compareInfo.updateData = [];
    conentRes.data.list.forEach((item: any) => {
      switch (item.change_type) {
        case 1:
          item.new_component.model_diff_content_id = item.model_diff_content_id;
          compareInfo.addData.push(item.new_component);
          break;
        case 2:
          item.base_component.model_diff_content_id =
            item.model_diff_content_id;
          compareInfo.deleteData.push(item.base_component);
          break;
        case 3:
          item.base_component.model_diff_content_id =
            item.model_diff_content_id;
          compareInfo.updateData.push(item.base_component);
          break;
        default:
          break;
      }
    });
    compareInfo.addName = `${t('model-viewer.compareInfo.add')}(${
      compareInfo.addData.length
    })`;
    compareInfo.deleteName = `${t('model-viewer.compareInfo.delete')}(${
      compareInfo.deleteData.length
    })`;
    compareInfo.updateName = `${t('model-viewer.compareInfo.update')}(${
      compareInfo.updateData.length
    })`;
    diffData.value = compareInfo.addData;
  };

  watch(
    () => props.modelDiffId,
    (val) => {
      if (val) {
        init();
      }
    }
  );

  watchEffect(async () => {
    toolStore.setCompareInfoShow(true);

    baseViewer = await toRaw(props.viewers.baseViewer);
    compareViewer = await toRaw(props.viewers.compareViewer);
  });
</script>

<style lang="less" scoped>
  .compare-list-box {
    width: 800px;
    overflow: hidden;
    box-shadow: 0 0 8px rgb(0 0 0 / 15%);
    flex-shrink: 0;
    .list-card {
      height: 100%;
      border-top: 0;
      border-bottom: 0;
    }
    .compare-filter-box {
      width: 100%;
      font-size: 20px;
      border-bottom: 1px solid var(--color-neutral-3);
      svg {
        cursor: pointer;
      }
      .header {
        display: flex;
        align-items: center;
        padding: 8px 12px;
      }
      .body {
        padding: 8px 12px;
        :deep(.arco-form-item) {
          margin-bottom: 0;
        }
      }
    }
    .icon-close {
      cursor: pointer;
    }
    .compare-card-box {
      height: 100%;
      overflow-y: scroll;
      padding: 0px 20px 20px 20px;
      flex: 1;
    }
    :deep(.list-card > .arco-card-body) {
      height: calc(100% - 46px);
      padding: 0;
      display: flex;
      flex-direction: column;
    }
    :deep(.arco-card + .arco-card) {
      margin-top: 12px;
    }
    :deep(.arco-card-actions) {
      border-top: 1px solid var(--color-neutral-3);
      margin-top: 0;
      padding: 8px 12px;
    }
  }
  :deep(.arco-table-tr) {
    cursor: pointer;
  }
  .switchText {
    position: relative;
    font-size: 14px;
    margin-left: 5px;
    top: 1px;
  }
</style>
