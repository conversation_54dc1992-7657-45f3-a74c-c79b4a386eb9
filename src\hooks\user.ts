import { useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';

import { useUserStore } from '@/store';
import { slashToDot } from '@/utils/index';
import { getUserId } from '@/utils/auth';

export default function useUser() {
  const router = useRouter();
  const userStore = useUserStore();
  const logout = async (logoutTo?: string) => {
    await userStore.logout();

    // 保存需要的缓存值(采用排除删除缓存的方法  会出无法删除所有缓存的问题 暂未找到原因 故采用先存 再删所有缓存  再添加需要的缓存的方法)
    const userId = getUserId() || '';
    const keysToKeep = [
      `work_last_project_${userId}`,
      'work_companyId',
      'work_user_id',
    ];
    const preservedData: any = {};
    keysToKeep.forEach((key) => {
      const value = localStorage.getItem(key);
      if (value !== null) {
        preservedData[key] = value;
      }
    });

    // 清空所有缓存
    localStorage.clear();
    sessionStorage.clear();

    // 重新设置需要的缓存
    Object.entries(preservedData).forEach(([key, value]) => {
      localStorage.setItem(key, value);
    });

    const { currentRoute } = router;
    Message.success('登出成功');
    router.push({
      path: '/login',
      // query: {
      //   ...currentRoute.value.query,
      //   from: 'login',
      //   redirect: slashToDot(currentRoute.value.path) as string,
      // },
    });
  };
  return {
    logout,
  };
}
