import { AppRouteRecordRaw } from '../types';

const DASHBOARD: AppRouteRecordRaw = {
  path: '/dashboard',
  name: 'dashboard',
  component: () => import('@/views/dashboard/index.vue'),

  meta: {
    locale: 'menu.dashboard',
    requiresAuth: true,
    allowProjectTemplate: true,
    icon: 'icon-apps',
    order: 3,
    globalMode: ['work'],
    showTemplate: true, // 当是项目模版时是否展示
  },
};

export default DASHBOARD;