import axios from 'axios';
import { download } from '@/utils/file';

export interface attachRecipient {
  createBy: string;
  createDate: string;
  id: string;
  email: string;
  name: string;
}
export interface enclosureData {
  id: string;
  title: string;
  projectId: string;
  state: number;
  createBy: string;
  receivers: string;
  createDate: string;
  message: string;
  file: string;
  attachRecipientList?: Array<attachRecipient>;
  [propsName: string]: any;
}

export interface FileInfo {
  createBy?: string;
  createDate?: string;
  deleteFlag?: number;
  description?: string;
  fileId?: string;
  fileToken?: string;
  folderId?: string;
  id?: string;
  isLocked?: boolean;
  name?: string;
  projectId?: string;
  size?: string;
  teamId?: string;
  type?: string;
  updateBy?: string;
  updateDate?: string;
  version?: string;
}

export interface enclosureGetParams extends Partial<enclosureData> {
  pageNo: number;
  pageSize: number;
}

export interface enclosureListRes {
  list: enclosureData[];
  total: number;
  message: string;
}

export interface attachSave {
  projectId: string;
  fileIds: Array<string>;
  message: string;
  receivers: Array<string>;
  title: string;
}

export interface ApplyParams {
  product: string;
}

export interface ApproveParams {
  id: string;
  status: number;
  approvalProduct?: string;
}

export const applyApplication = (data: ApplyParams) => {
  return axios.post('/cde-collaboration/user/register-blue', data);
};

export function queryApproveList(params: any) {
  return axios.get('/cde-collaboration/user/register-blue-page', {
    params,
  });
}

export function approveApply(data: any) {
  return axios.post('/cde-collaboration/user/register-blue-approve', data);
}

export function deleteApply(data: any) {
  return axios.delete(`/cde-collaboration/user/applyUser/${data}`);
}

export function getEnclosureInfo(params: any) {
  return axios.get<enclosureListRes>('/cde-collaboration/attach/detail', {
    params,
  });
}

/** 应用申请数据删除 */
export function ApplyDlete(applyId: string) {
  return axios.post<string>(
    `/cde-collaboration/user/deleteApplyUser?applyId=${applyId}`
  );
}

export const getDirectoryFileInfo = (directory: any) => {
  let total = 0;
  let fileIDList: Array<string> = [];
  if (directory.children?.length) {
    directory.children.forEach((item: any) => {
      if (item.isFileOrFolder === 1) {
        total += 1;
        fileIDList.push(item.id);
      } else {
        total += getDirectoryFileInfo(item).total;
        fileIDList = fileIDList.concat(getDirectoryFileInfo(item).fileIDList);
      }
    });
  }
  return { total, fileIDList };
};
