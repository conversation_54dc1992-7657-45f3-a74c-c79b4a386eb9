<template>
  <a-modal
    :visible="convertModal.visible"
    :width="450"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @ok="submit"
  >
    <template #title>{{ $t('file-manage.convert') }}</template>
    <div class="content">
      <a-spin style="display: block; height: 100%" :loading="loading">
        <a-form ref="formRef" :model="form" label-align="left">
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item
                field="endFormat"
                :rules="[
                  {
                    required: true,
                    message: $t('file-manage.document-format-errMsg'),
                  },
                ]"
                :label="$t('file-manage.document-format')"
                label-col-flex="80px"
              >
                <a-select
                  v-model="form.endFormat"
                  :placeholder="$t('check.please-select')"
                >
                  <a-option
                    v-for="template in formatOption"
                    :key="template.value"
                    :value="template.value"
                    :label="template.value"
                  ></a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-spin>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch, computed } from 'vue';
  import { useI18n } from 'vue-i18n';
  import useFileManageStore from '@/store/modules/file-manage/index';
  import { storeToRefs } from 'pinia';
  import { Message } from '@arco-design/web-vue';
  import {
    wpsConvert,
    setConvertStatus,
    getConvertSeek,
    getDownloadUrl,
    getCode,
  } from '../api';
  import { useUserStore } from '@/store';
  import { getWpsAppCodeProject } from '@/api/wps';

  const userStore = useUserStore();
  const userInfo: any = computed(() => {
    return userStore.userInfo;
  });
  const { t } = useI18n();

  const loading: any = ref(false);

  const form: any = ref({
    endFormat: '',
  });

  const formatOption: any = ref([]);

  const store = useFileManageStore();
  const { convertModal } = storeToRefs(store);

  const handleCancel = () => {
    store.setConvertModal(false);
  };

  const formRef = ref<any>(null);

  // 前端限制一些不可转换的格式
  const convertBlacklist: any = ['ofd', 'html'];

  // 获取对应文件可转换的格式(text、pdf只能转pdf)
  const getConvertSeekHandle = async () => {
    if (convertModal.value.type === 'txt') {
      formatOption.value = [{ value: 'pdf' }];
    } else {
      const { data } = await getConvertSeek({
        fileId: convertModal.value.record.id,
      });

      const formatOptionData: any = data.filter(
        (value: any) =>
          !convertBlacklist.includes(value) && !value.startsWith('zip')
      );

      formatOption.value = formatOptionData.map((format: any) => ({
        value: format,
      }));
    }
  };

  // 修改转换状态
  const setConvertStatusHandle = async () => {
    const param = {
      fileId: convertModal.value.record.id,
      fileStatus: 100,
    };
    await setConvertStatus(param);
  };

  // 转换
  const submit = async () => {
    const result = await formRef?.value?.validate();
    const downloadUrl = await getDownloadUrl({
      fileToken: convertModal.value.record?.fileToken,
    });
    const paramCode = {
      redirectUri: window.origin,
    };
    await getCode(paramCode);
    if (!result) {
      loading.value = true;
      const fileSource = (await getWpsAppCodeProject({})).data;
      const param = {
        endFormat: form.value.endFormat,
        fileId: convertModal.value.record.id,
        fileName: convertModal.value.record.name,
        fileSource,
        fileUrl: downloadUrl.data,
        userId: userInfo.value.username,
      };
      try {
        const res: any = await wpsConvert(param);
        if (res.status) {
          Message.success(t('file-manage.convert-successful'));
          // 修改转换状态
          await setConvertStatusHandle();
          store.setConvertModal(false);
          store.getTreeChild(store.currentFolder || {}, false);
          store.getTableData(store.currentFolder || {}, false);
        }
      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    }
  };
  watch(
    () => convertModal.value.visible,
    (val) => {
      if (val) getConvertSeekHandle();
    }
  );
</script>
