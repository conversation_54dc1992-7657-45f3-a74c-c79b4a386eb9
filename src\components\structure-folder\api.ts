import axios from 'axios';
// 计算文件夹内文件的数量
export const getDirectoryFileCounts = (folder: any) => {
  // 初始化文件数量
  let fileCount = 0;

  // 累加当前文件夹的文件数量
  if (folder.files) {
    fileCount += folder.files.length;
  }

  // 遍历子文件夹，并递归计算文件数量
  if (folder.children && folder.children.length > 0) {
    // eslint-disable-next-line no-restricted-syntax
    for (const child of folder.children) {
      fileCount += getDirectoryFileCounts(child);
    }
  }

  // 返回总文件数量
  return fileCount;
};

export const getDirectoryFileIds = (directory: any[]): any[] => {
  const total: any[] = [];
  if (directory.length) {
    directory.forEach((e) => {
      if (e.isFileOrFolder === 1) {
        total.push(e.id);
        // console.log('id: ', e.id);
      } else if (e.isFileOrFolder === 0 && e.children.length) {
        total.push(...getDirectoryFileIds(e.children));
      }
    });
  }

  return total;
};

export const getCheckList = (params: any) => {
  return axios.get('/cde-collaboration/review/list', { params });
};
export default null;
