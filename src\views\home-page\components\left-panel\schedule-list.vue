<template>
  <div class="project-list">
    <div class="search-bar">
      <a-row :gutter="16" style="margin-bottom: 16px">
        <a-col :flex="1">
          <table-title :title="$t('home.my-schedule')"></table-title>
        </a-col>
        <a-col :flex="'600px'" style="text-align: right">
          <a-space :size="8">
            <a-button @click="goToLastSevenDays">{{
              $t('home.last-seven-days')
            }}</a-button>
            <!-- 时间选择器 -->
            <a-range-picker
              v-model="dateStr"
              :placeholder="[$t('home.start-date'), $t('home.end-date')]"
              style="width: 300px"
              allow-clear
              @change="onDateChange"
            />
            <!-- 新建会议按钮 -->
            <a-button type="outline" @click="goCreate('meeting')">
              <img
                src="@/assets/images/home-page/<EMAIL>"
                alt="会议图标"
                style="width: 14px; height: 14px; margin-right: 8px"
              />
              {{ $t('home.new-meeting') }}
            </a-button>

            <!-- 新建事项按钮 -->
            <a-button type="outline" @click="goCreate('matters')">
              <img
                src="@/assets/images/home-page/<EMAIL>"
                alt="事项图标"
                style="width: 14px; height: 14px; margin-right: 8px"
              />
              {{ $t('home.new-item') }}
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>
    <div class="content">
      <div class="table-container">
        <a-table
          style="height: 100%"
          :data="scheduleList"
          stripe
          :bordered="false"
          :scroll="{ x: '100%', y: 'calc(100% - 361px)' }"
          :pagination="pageConfig"
          class="schedule-table"
          @page-change="pageChange"
          @page-size-change="pageSizeChange"
        >
          <template #empty>
            <div class="empty-wrapper">
              <img src="@/assets/images/schedule/schedule-bg.png" alt="" />
              <div>{{ $t('home.no-data') }}</div>
            </div>
          </template>
          <template #columns>
            <a-table-column
              :title="$t('home.index')"
              :width="80"
              align="left"
              :ellipsis="true"
              :tooltip="true"
            >
              <template #cell="{ rowIndex }">
                {{
                  (pageConfig.pageSize ? pageConfig.pageSize : 10) *
                    ((pageConfig.current ? pageConfig.current : 1) - 1) +
                  (rowIndex + 1)
                }}
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('home.title')"
              :width="260"
              align="left"
              :ellipsis="true"
              :tooltip="true"
            >
              <template #cell="{ record }">
                <span class="clickable-title" @click="goSchedule(record)">
                  {{ record.title }}
                </span>
              </template>
            </a-table-column>

            <a-table-column
              :title="$t('home.details')"
              :width="200"
              align="left"
              :ellipsis="true"
              :tooltip="true"
              data-index="content"
            >
            </a-table-column>

            <a-table-column
              :title="$t('home.type')"
              :width="120"
              align="left"
              :ellipsis="true"
              :tooltip="true"
            >
              <template #cell="{ record }">
                <span
                  :class="[
                    'type-badge',
                    record.type === 'meeting' ? 'meeting-type' : 'item-type',
                  ]"
                >
                  <img
                    :src="record.type === 'meeting' ? meetingIcon : itemIcon"
                    alt="类型图标"
                    style="width: 16px; height: 16px; margin-right: 8px"
                    class="type-icon"
                  />
                  {{
                    record.type === 'meeting'
                      ? $t('home.meeting')
                      : $t('home.item')
                  }}
                </span>
              </template>
            </a-table-column>

            <a-table-column
              :title="$t('home.status')"
              data-index="scheduleStatus"
              :width="110"
              align="left"
              :ellipsis="true"
              :tooltip="true"
            >
              <template #cell="{ record }">
                <!--会议： 0-未开始，1-进行中，2-已完成 -->
                <!--事项： 1-进行中，2-已完成，3已关闭 -->
                <span
                  :class="[
                    'status-badge',
                    !record.scheduleStatus
                      ? 'status-not-started'
                      : record.scheduleStatus === '1'
                      ? 'status-in-progress'
                      : 'status-completed',
                  ]"
                >
                  <img
                    v-if="!record.scheduleStatus"
                    class="status-icon"
                    :src="status0Img"
                    alt=""
                  />
                  <img
                    v-if="record.scheduleStatus === '1'"
                    class="status-icon"
                    :src="status1Img"
                    alt=""
                  />
                  <img
                    v-if="record.scheduleStatus === '2'"
                    class="status-icon"
                    :src="status2Img"
                    alt=""
                  />
                  <img
                    v-if="record.scheduleStatus === '3'"
                    class="status-icon"
                    :src="statusCloseImg"
                    alt=""
                  />
                  <!-- <img
                    :src="record.type === 'meeting' ? meetingIcon : itemIcon"
                    alt="类型图标"
                    style="width: 16px; height: 16px; margin-right: 8px"
                    class="type-icon"
                  /> -->
                  {{
                    $t(
                      statusOptions[record.type][
                        record.type === 'meeting' && !record.scheduleStatus
                          ? '0'
                          : String(record.scheduleStatus)
                      ] || ''
                    )
                  }}
                </span>
              </template>
            </a-table-column>
            <a-table-column
              :title="$t('home.deadline')"
              data-index="planEndTime"
              :width="200"
              align="left"
              :ellipsis="true"
              :tooltip="true"
            >
            </a-table-column>
            <a-table-column
              :title="$t('home.organizer')"
              data-index="chargePersonFullName"
              :width="150"
              align="left"
              :ellipsis="true"
              :tooltip="true"
            >
            </a-table-column>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref } from 'vue';
  import { PaginationProps } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  import moment from 'moment';
  import TableTitle from '@/components/table-title/index.vue';
  import { QueryPersonalScheduleParams, getPersonalSchedule } from './api';
  import meetingIcon from '@/assets/images/home-page/meeting-icon.png';
  import itemIcon from '@/assets/images/home-page/item-icon.png';
  import { AxiosResponseCustom, Paging } from '@/types/global';
  import { useUserStore, useGlobalModeStore } from '@/store';
  import { useI18n } from 'vue-i18n';
  import status0Img from '@/assets/images/meeting/status0.png';
  import status1Img from '@/assets/images/meeting/status1.png';
  import status2Img from '@/assets/images/meeting/status2.png';
  import statusCloseImg from '@/assets/images/meeting/status-close.png';

  const { t } = useI18n();

  const globalModeStore = useGlobalModeStore();
  const userStore = useUserStore();
  const statusOptions: any = {
    meeting: {
      '0': 'schedule.notStarted',
      '1': 'schedule.inProgress',
      '2': 'schedule.completed',
    },
    item: {
      '1': 'schedule.inProgress',
      '2': 'schedule.completed',
      '3': 'schedule.closed',
    },
  };
  // const { t } = useI18n();
  // 设置默认时间范围为当天和明天
  const dateStr = ref([
    moment().format('YYYY-MM-DD'), // 当天
    moment().add(6, 'day').format('YYYY-MM-DD'), // 明天
  ]); // 时间区间绑定的值

  const router = useRouter();

  const scheduleList = ref<object[]>([]);

  const pageConfig: PaginationProps = reactive({
    showTotal: true,
    showMore: false,
    showJumper: true,
    showPageSize: true,
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    total: 100,
  });

  const getList = () => {
    const params: QueryPersonalScheduleParams = {
      endTime: dateStr.value[1] || '', // 从 dateStr 中获取结束时间
      startTime: dateStr.value[0] || '', // 从 dateStr 中获取开始时间
      pageNo: pageConfig.current || 1,
      pageSize: pageConfig.pageSize || 20,
    };
    getPersonalSchedule(params).then(
      (res: AxiosResponseCustom<Paging<any>, any>) => {
        if (res.code === 8000000) {
          scheduleList.value = res.data.list || [];
          // 测试没数据的展示
          // scheduleList.value = [];
          // 测试多条数据
          // const list = res.data.list || []
          // 先生成一个长度为20的数组，fill进去同一个 list 引用，再 flat 一次，得到 20 份拼接后的新数组
          // scheduleList.value = Array(20).fill(list).flat();
          // console.log('获取日程数据成功', scheduleList.value);
          pageConfig.total = res.data.total || scheduleList.value.length || 10;
        }
      }
    );
  };
  const onDateChange = (dates: [string, string]) => {
    console.log('选择的时间区间:', dates);
    // 在这里处理筛选逻辑，例如调用接口重新加载列表数据
    getList();
  };
  const pageSizeChange = (size: number): void => {
    pageConfig.pageSize = size;
    getList();
  };
  const pageChange = (current: number): void => {
    pageConfig.current = current;
    getList();
  };
  // 跳转新建会议事项
  const goCreate = (val: any) => {
    userStore.setProjectTemplate('0');
    globalModeStore.changeGlobalMode('work');
    router.push({
      name: 'schedule',
      query: {
        editType: 'create',
        type: val,
      },
    });
  };
  // 跳转事项会议详情
  const goSchedule = (val: any) => {
    userStore.setProjectTemplate('0');
    globalModeStore.changeGlobalMode('work');
    router.push({
      name: 'schedule',
      query: {
        editType: 'edit',
        type: val.type === 'meeting' ? 'meeting' : 'matters',
        id: val.type === 'meeting' ? val.scheduleSubId : val.id,
      },
    });
  };
  const goToLastSevenDays = () => {
    const startDate = moment().subtract(6, 'day').format('YYYY-MM-DD');
    const endDate = moment().format('YYYY-MM-DD');
    dateStr.value = [startDate, endDate];
    getList();
  };
  onMounted(() => {
    getList();
  });
</script>

<style scoped lang="less">
  .project-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    //border: 1px solid red;
    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
  }
  .table-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .schedule-table {
    height: 100%;
    :deep(.arco-table-container) {
      height: 100%;
      background-color: var(--color-bg-2);
    }
    :deep(.arco-table-body) {
      height: calc(100% - 0px);
    }
    :deep(.arco-table-pagination) {
      margin-top: 16px;
      padding: 0;
      background-color: var(--color-bg-2);
    }
    :deep(.arco-table-th) {
      background-color: var(--color-fill-2);
      font-weight: 500;
    }
    // :deep(.arco-table-tr) {
    //   &:hover {
    //     background-color: var(--color-fill-2);
    //   }
    // }
  }
  .search-title {
    display: inline-block;
    width: 50px;
    height: 22px;
    font-size: 14px;
    color: #000000;
    line-height: 22px;
  }
  .search-bar {
    margin-top: 8px;
    position: relative;
    .opt-btn {
      position: absolute;
      top: 0;
      right: 0;
    }
    :deep(.arco-picker-size-medium) {
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #c9cdd4;
    }
  }
  .name {
    color: rgb(var(--arcoblue-6));
    cursor: pointer;
  }
  .type-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0px 8px;
    height: 24px;
    border-radius: 4px;
    font-size: 14px;
    white-space: nowrap; /* 防止文字换行 */
  }
  .meeting-type {
    color: #f77234; /* 橙色 */
    background-color: #fff3e8;
  }

  .item-type {
    color: #3491fa; /* 蓝色 */
    background-color: #e6f7ff;
  }
  .type-icon {
    width: 16px;
    height: 16px;
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    height: 24px;
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    padding: 0 8px;
    justify-content: center;
    .status-icon {
      width: 16px;
      height: 16px;
      margin-right: 2px;
    }
  }
  .status-not-started {
    color: #ff4d4f;
    background-color: #ffece8;
  }
  .status-in-progress {
    color: #3366ff;
    background-color: #e8f2ff;
  }

  .status-completed {
    color: #86909c; /* 灰色文字 */
    background-color: #e5e6eb;
  }

  .empty-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    img {
      display: block;
      width: 140px;
      height: 140px;
    }
    div {
      margin-top: 16px;
      color: #4e5969;
    }
  }
  :deep(.arco-table-content .arco-scrollbar:nth-child(2)) {
    height: 100%;
  }
  :deep(.arco-table-header + .arco-scrollbar-track-direction-horizontal) {
    bottom: -380px;
  }
</style>
