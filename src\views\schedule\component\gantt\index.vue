<template>
  <div class="schedule-gantt">
    <div class="gantt-title">
      <div class="search-wrap">
        <a-input-search
          v-model="searchText"
          @keyup.enter="search"
          @search="search"
          placeholder="请输入名称搜索"
        ></a-input-search>
      </div>
      <div class="btns">
        <a-button type="text" status="normal" @click="addMatter">
          <template #icon>
            <icon-plus />
          </template>
          新增
        </a-button>
        <a-button style="margin-left: 18px" type="text" status="normal" @click="handleBatchImport">
          <template #icon>
            <icon-import />
          </template>
          批量导入
        </a-button>
      </div>
      <div class="gantt-scales-type">
        <a-radio-group
          v-model="scaleType"
          type="button"
          @change="scaleTypeChange"
        >
          <a-radio value="day">天</a-radio>
          <a-radio value="week">周</a-radio>
          <a-radio value="month">月</a-radio>
          <a-radio value="quarter">季度</a-radio>
        </a-radio-group>
      </div>
    </div>
    <div class="schedule-gantt-content">
      <div class="schedule-list">
        <a-table
          row-key="id"
          v-if="taskList.length"
          :columns="columns"
          :data="taskList"
          :bordered="{ cell: true, wrapper: true }"
          :expanded-keys="tableExpandKeys"
          table-layout-fixed
          :pagination="false"
          :scrollbar="false"
          @expanded-change="expandChange"
          :scroll="scroll"
          height="100%"
          ref="tableRef"
        >
          <template #index="{ rowIndex }">
            {{ rowIndex > 0 ? rowIndex : '' }}
          </template>
          <template #name="{ record }">
            <a-tooltip :content="record.name">
              <div class="task-name-wrap" @click="nodeClick(record)" v-if="record.name">
                <img :src="computeUrl(record)" alt="" width="16" height="16" />
                <span class="col-task-name">{{ record.name || '' }}</span>
              </div>
            </a-tooltip>
          </template>
          <template #status="{ record }">
            <div class="task-status-wrap">
              <span
                v-if="record.status"
                class="task-status-prefix"
                :style="{
                  backgroundColor:
                    taskStatusMap[record.status]?.color || '#86909C',
                }"
              ></span>
              <span v-if="record.status" class="task-status-text">{{
                taskStatusMap[record.status]?.title || ''
              }}</span>
            </div>
          </template>
        </a-table>
      </div>
      <div id="schedule-gantt"></div>
      <div class="empty-area" v-if="!taskList.length">
        <a-empty description="暂无事项"></a-empty>
      </div>
    </div>
  </div>
  <!-- 批量导入 -->
  <batchImport
    ref="batchImportRef"
    v-model:visible="batchImportVisible"
    :project-id="currentProjectId"
    :schedule-panel-id="projectCalendarId"
    @refresh="search"
  />
  <!-- 创建事项 -->
  <createDrawer
    v-model:visible="createDrawerVisible"
    v-model:drawerLoading="drawerLoading"
    :type="type"
    @refresh="search"
  ></createDrawer>
</template>

<script setup lang="ts">
  import genOptions, { scalesTypeMap, genMarkLine } from './options';
  import {
    onMounted,
    ref,
    onUnmounted,
    computed,
    nextTick,
    toRaw,
    watch,
  } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useI18n } from 'vue-i18n';
  import { getGanttMatterList, getGanttLinks, getGanttCounts } from '../../api';
  import { getProjectPanel } from '@/views/create-schedule/api';
  import { useRoute, useRouter } from 'vue-router';
  import CMatter from '@/assets/images/schedule/c-matter.png';
  import FMatter from '@/assets/images/schedule/f-matter.png';
  import Milstone from '@/assets/images/schedule/milstone.png';
  import batchImport from '../gannt-drawer/components/batchImport.vue';
  import createDrawer from '../gannt-drawer/components/createDrawer.vue';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';
  import { agendaDetail } from '@/views/create-schedule/api';
  // import { createProgressBar } from './progressBarCtl'

  // #region 批量导入参数
  const batchImportVisible = ref(false);
  const userId = getUserId() || '';
  const currentProjectId = ref(getLocalstorage(`last_project_${userId}`) || '');
  const projectCalendarId = ref(''); // 日程数据
  // 获取日历数据
  const getPanelListHandle = async () => {
    const projectParam = {
      projectId: currentProjectId.value,
    };
    const res = await getProjectPanel(projectParam);
    projectCalendarId.value = res.data?.id || '';
  };

  // 批量导入
  const handleBatchImport = () => {
    batchImportVisible.value = true;
  };
  // #endregion
  // #region 新建事项
  const createDrawerVisible = ref(false);
  const matterData = ref({});
  const type = ref('new');
  const addMatter = () => {
    createDrawerVisible.value = true;
    matterData.value = {};
    type.value = 'new';
  };
  // 查询事项详情
  // 设置事项数据（通过id查询事项详情数据）
  const setMatterData = async (val: any) => {
    const { data } = await agendaDetail(val);
    data.status = data.agendaStatus; // 为使用统一字段
    matterData.value = data;
  };
  // 点击树节点事件
  const nodeClick = async (record: any) => {
    if (record.type === 'task') {
      await setMatterData(record.id);
      type.value = 'edit';
      createDrawerVisible.value = true;
    }
  };
  // #endregion
  const { t, locale } = useI18n();
  const scroll = {
    y: 'calc(100vh - 240px)',
  };
  const tableRef = ref(null);
  const route = useRoute();

  const scaleType = ref('day');
  const scaleTypeChange = (type: string) => {
    updateGantt();
  };

  // 绘制甘特图
  const createGantt = (options: any) => {
    const el = document.getElementById('schedule-gantt');
    if (el) {
      const ganttInstance = new VTableGantt.Gantt(el, options);
      const tableEL = tableRef?.value?.$el?.querySelector('.arco-table-body');
      window['ganttInstance'] = ganttInstance;
      // 甘特图滚动时 左侧表格同步滚动
      window['ganttInstance'].on('scroll', (options: any) => {
        try {
          // console.log('scroll: ', options)
          tableEL.scrollTop = options.scrollTop;
        } catch (e) {
          console.log(e.toString());
        }
      });
      // window['ganttInstance'].on('click_task_bar', (options:any)=>{
      //   try{
      //     const taskBarRect = window['ganttInstance']?.getTaskBarRelativeRect(options.index);
      //     console.log('click_task_bar: ', options, )
      //     createProgressBar(taskBarRect, options.record, options.index)
      //   }catch (e){
      //     console.log(e.toString())
      //   }
      // })
    }
  };

  const searchText = ref('');
  const columns = computed(() => {
    const constColumn = [
      {
        title: t('prjMember.column.index'),
        dataIndex: 'index',
        slotName: 'index',
        width: 80,
        align: 'center',
      },
      {
        title: t('schedule.gantt.table.name'),
        dataIndex: 'name',
        slotName: 'name',
        align: 'left',
      },
      {
        title: t('schedule.gantt.table.status'),
        dataIndex: 'status',
        slotName: 'status',
        align: 'center',
        width: 80,
      },
    ];
    return constColumn;
  });
  const taskList = ref([]); // 左侧表格的数据
  const ganttRecords: any = ref([]); // 用于绘制甘特图的数据
  const tableExpandKeys = ref([]);
  const ganttLinks = ref([]);
  const taskCount = ref('')

  const computeUrl = (record: any) => {
    if (record.isMilestone) return Milstone;
    else {
      if (record.children) return FMatter;
      return CMatter;
    }
  };
  const taskStatusMap = ref({
    [String(1)]: {
      title: '进行中',
      color: '#3366FF',
    },
    [String(2)]: {
      title: '已完成',
      color: '#33B737 ',
    },
    [String(3)]: {
      title: '已关闭',
      color: '#86909C',
    },
  });
  const parseGanttRecords = (list: any) => {
    const result: any[] = [];
    if (list.length) {
      list.forEach((item: any) => {
        const newItem = JSON.parse(JSON.stringify(item));
        if (newItem.children) {
          newItem.hasChildren = true;
          delete newItem.children;
        }
        result.push(newItem);
        if (
          tableExpandKeys.value.length &&
          tableExpandKeys.value.includes(item.id)
        ) {
          result.push(...parseGanttRecords(item.children));
        }
      });
    }
    return result;
  };

  // 表格展开时 更新右侧甘特图数据
  const expandChange = (keys: any) => {
    tableExpandKeys.value = keys;
    ganttRecords.value = parseGanttRecords(taskList.value);
    updateGantt();
  };
  const updateGantt = () => {
    const rows = toRaw(ganttRecords.value);
    if (rows.length) {
      if (window['ganttInstance'])
        window['ganttInstance']?.updateOption(
          genOptions(
            toRaw(ganttRecords.value),
            scaleType.value,
            ganttLinks.value
          )
        );
      else {
        createGantt(
          genOptions(
            toRaw(ganttRecords.value),
            scaleType.value,
            ganttLinks.value
          )
        );
      }
      window['ganttInstance'].scrollLeft += 500;
    } else {
      window['ganttInstance']?.release();
      window['ganttInstance'] = null;
    }
  };

  // 获取项目的任务汇总数据
  const getTaskCounts = () => {
    const params = {
      projectId: route.params.projectId
    }
    return getGanttCounts(params).then((res:any)=>{
      if(res.status){
        taskCount.value = res.data || '';
      }
    })
  }
  const parseTaskList = (list: []) => {
    const result: any = [];
    if (list.length) {
      list.forEach((item: any) => {
        const ganttItem: any = {
          id: item.milestoneId || item.scheduleDetailId,
          name: item.milestoneName || item.agendaName,
          isMilestone: item.milestoneId ? 1 : 0,
          type: item.milestoneId ? 'milestone' : 'task',
          status: item.agendaStatus || '',
          start: item.planStartTime || item.milestoneEndTime,
          end: item.planEndTime || '',
          progress: item.rateProgress ? item.rateProgress : 0,
        };
        if (item.children?.length) {
          ganttItem.children = parseTaskList(item.children);
        }
        result.push(ganttItem);
      });
    }
    return result;
  };
  // 获取项目下任务列表
  const getTaskList = async () => {
    if(!searchText.value){
      taskCount.value = '';
      await getTaskCounts();
    }
    const record:any = toRaw(taskCount.value);
    const countRecord = {
      id: record.projectId,
      start: record.earliestStartTime,
      end: record.latestEndTime,
      progress: record.averageProgress,
      // progress: 20,
      name: '',
      status: '',
      isCount: true
    }

    const params = {
      keyName: searchText.value || '',
      projectId: route.params.projectId,
      pageNo: 1,
      pageSize: 99999,
    };
    if (params.projectId) {
      tableExpandKeys.value = [];
      return getGanttMatterList(params).then((res) => {
        if (res.status) {
          const list = parseTaskList(res.data || []);
          taskList.value = !searchText.value ? [countRecord, ...list] : list;
          ganttRecords.value = !searchText.value ? [countRecord, ...parseGanttRecords(list)] : parseGanttRecords(list)
          if (list.length === 1 && list[0]['isMilestone'] > 0) {
            tableRef.value?.expand(list[0]['id']);
          }
        }
      });
    }
  };
  // 获取任务间关联关系数据
  const getLinks = () => {
    const params = {
      projectId: route.params.projectId,
    };
    if (params.projectId) {
      return getGanttLinks(params).then((res) => {
        if (res.status) {
          // const list = parseTaskList(res.data || []);
          ganttLinks.value =
            res?.data?.map((item: any) => {
              return {
                linkedFromTaskKey: item.sourceId,
                linkedToTaskKey: item.targetId,
                type: item.relyTypeEnglish,
                // type: 'start_to_finish',
              };
            }) || [];
        }
      });
    }
  };
  const search = async () => {
    await getLinks();
    await getTaskList();
    nextTick(() => {
      updateGantt();
    });
  };

  const init = async () => {
    await search();
  };
  watch(
    () => ganttRecords.value,
    (val) => {
      if (val.length && !window['ganttInstance']) {
        nextTick(() => {
          createGantt(
            genOptions(
              toRaw(ganttRecords.value),
              scaleType.value,
              ganttLinks.value
            )
          );
        });
      }
    }
  );
  // 左侧表格区域滚动时，右侧甘特图同步滚动
  const wheelListener = function (options: any) {
    try {
      const classNames = Array.from(options.srcElement.classList);
      if (classNames.length) {
        const el = document
          .getElementsByClassName('schedule-list')[0]
          ?.getElementsByClassName(classNames[0]);
        if (el.length) {
          window['ganttInstance'].scrollTop += options.deltaY;
        }
      }
    } catch (e) {
      console.log(e.toString());
    }
  };
  onMounted(() => {
    init();
    document.addEventListener('wheel', wheelListener);
    getPanelListHandle();
  });
  onUnmounted(() => {
    window['ganttInstance']?.release();
    window['ganttInstance'] = null;
    document.removeEventListener('wheel', wheelListener);
  });
</script>

<style scoped lang="less">
  :deep(.arco-table-th) {
    height: 81px !important;
    font-weight: 500;
    font-size: 14px;
  }
  :deep(.arco-table-tr) {
    height: 40px !important;
  }
  :deep(.arco-table-cell) {
    padding: 0px;
    padding-left: 8px;
    padding-right: 8px;
  }
  :deep(.arco-table-body) {
    overflow-y: hidden !important;
  }
  .schedule-gantt {
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    .schedule-gantt-content {
      margin-top: 16px;
      height: calc(100% - 65px);
      width: 100%;
      display: flex;
      position: relative;
      .schedule-list {
        width: 400px;
        //border: 1px solid red;
        position: absolute;
        z-index: 150;
        .task-status-wrap {
          display: flex;
          justify-content: center;
          align-content: center;
          align-items: center;
          .task-status-prefix {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #86909c;
            border-radius: 4px;
            margin-right: 6px;
          }
          .task-status-text {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #4e5969;
          }
        }
        .task-name-wrap {
          display: flex;
          align-items: center;
          .col-task-name {
            display: inline-block;
            width: calc(100% - 22px);
            margin-left: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
          }
        }
      }
      #schedule-gantt {
        width: calc(100% - 400px);
        height: 100%;
        //border: 1px solid red;
        position: absolute;
        left: 400px;
        z-index: 1;
      }
      .empty-area {
        //border: 1px solid red;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .gantt-title {
      display: flex;
      height: 35px;
      //border: 1px solid red;
      margin-top: 15px;
      :deep(.arco-input-wrapper) {
        border-radius: 4px !important;
      }
      .search-wrap {
        margin-left: 16px;
        width: 360px;
      }
      .btns {
        margin-left: 18px;
        :deep(.arco-btn-size-medium){
          padding: 0px;
        }
      }
      .gantt-scales-type {
        position: absolute;
        right: 20px;
        :deep(.arco-radio-group-button) {
          border-radius: 4px !important;
        }
      }
    }
  }
</style>
