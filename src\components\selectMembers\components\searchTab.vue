<template>
  <div class="user-select-search-tab">
    <div class="member-list">
      <a-checkbox-group
        v-model="selectedMemberIds"
        style="width: 100%"
        direction="vertical"
        @change="changeTeamMemberSearch"
      >
        <a-checkbox v-for="item in memberList" :key="item.id" :value="item.id">
          {{ item?.userFullname }}
          <a-tooltip
            v-if="item.orgList"
            :content="
              item.orgList[0]?.pathName
                ?.replace('.根节点.', '')
                .replaceAll('.', '/')
            "
          >
            <span class="pathName">{{
              item.orgList[0]?.pathName
                ?.replace('.根节点.', '')
                .replaceAll('.', '/')
            }}</span>
          </a-tooltip>
        </a-checkbox>
      </a-checkbox-group>
    </div>
    <div v-if="paginationConfig.total > 0" class="member-pagination">
      <a-button size="mini" @click="closeSearch">{{ $t('selectMembers.close') }}</a-button>
      <a-pagination
        :total="paginationConfig.total"
        :page-size="100"
        show-total
        @change="pageChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getMemberList } from '@/api/selectMember';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const props = defineProps({
    selectedMember: {
      type: Array,
      default() {
        return [];
      },
    },
  });
  const emits = defineEmits(['change', 'close']);
  const closeSearch = () => {
    emits('close');
  };

  const selectedMemberIds: any = ref([]);
  const memberList: any = ref([]);

  const paginationConfig = reactive({
    pageNo: 1,
    total: 0,
  });

  let searchValue = '';
  const search = async (value: string) => {
    searchValue = value;
    const param = {
      searchValue: value,
      pageSize: 100,
      pageNo: paginationConfig.pageNo,
    };
    const res = await getMemberList(param);
    memberList.value = res?.data?.list || [];
    paginationConfig.total = res?.data?.total || 0;
  };
  const pageChange = (pageNo: any) => {
    paginationConfig.pageNo = pageNo;
    search(searchValue);
  };

  let allSelectMembers: any = [];
  const changeTeamMemberSearch = (list: any) => {
    if (list.length > allSelectMembers.length) {
      const allIds = allSelectMembers.map((e: any) => e.id);
      const a = list.filter((e: any) => {
        return !allIds.includes(e);
      });
      a.forEach((id: string) => {
        const member = memberList.value.find((e: any) => e.id === id);
        if (member) allSelectMembers.push(member);
      });
    } else {
      allSelectMembers = allSelectMembers.filter((e: any) => {
        return list.includes(e.id);
      });
    }
    emits('change', allSelectMembers);
  };

  onMounted(() => {
    if (props.selectedMember) {
      allSelectMembers = [...props.selectedMember];
      selectedMemberIds.value = props.selectedMember.map((e: any) => e.id);
    }
  });
  watch(
    () => props.selectedMember,
    (val) => {
      allSelectMembers = [...val];
      selectedMemberIds.value = val?.map((e: any) => e.id) || [];
    },
    {
      immediate: true,
    }
  );
  defineExpose({
    search,
  });
</script>

<style scoped lang="less">
  .user-select-search-tab {
    position: relative;
    margin-top: 12px;
    height: calc(100% - 12px);
    //border: 1px solid red;
    overflow: hidden;
    .member-list {
      height: calc(100% - 50px);
      overflow: auto;
    }
    .member-pagination {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      //border: 1px solid red;
      margin-top: 10px;
    }
  }
  :deep(.arco-checkbox) {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    margin: 0px;
    padding: 0px;
    height: 50px;
    //border: 1px solid red;
  }
  :deep(.arco-icon-hover) {
    position: absolute;
    top: 10px;
  }
  :deep(.arco-checkbox-label) {
    margin-left: 24px !important;
    display: inline-block;
    //display: flex;
    //align-items: center;
    //align-content: center;
    //justify-content: space-between;
    width: 100%;
    height: 100%;
  }
  .pathName {
    top: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    //white-space: nowrap;
    //margin-left: 20px;
    color: #7e8592;
    width: calc(100% - 120px);
    //border: 1px solid red;
    text-overflow: ellipsis;
    overflow: hidden;
    position: absolute;
    right: 4px;
    height: 50px;
    line-height: 24px;
    padding-top: 2px;
  }
</style>
