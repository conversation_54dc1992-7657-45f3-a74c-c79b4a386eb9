import axios from 'axios';

// 查询所有键值
export function getAllKeyValue(params: any) {
  return axios.get<any>(`/sys-system/dictionary/list`, { params });
}

// 新增键值
export function addKeyValue(data: any) {
  return axios.post<any>('/sys-system/dictionary', data);
}

// 更新键值
export function updateKeyValue(data: any) {
  return axios.put<any>('/sys-system/dictionary', data);
}

// 删除键值
export function deleteKeyValue(params: any) {
  return axios.delete<any>(`/sys-system/dictionary`, { params });
}
