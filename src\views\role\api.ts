import type { HttpResponse } from '@/api/interceptor';
import axios from 'axios';

// 角色分页列表接口
export function getRolePages(
  params: Role.Api.PageRoleParam
): Promise<HttpResponse<GetPageRes<Role.Api.PageRoleDto[]>>> {
  return axios.get('/sys-system/roles/page', { params });
}

// id获取角色详情
export function getRoleById(
  roleId: string
): Promise<HttpResponse<Role.Api.RoleDto>> {
  return axios.get('/sys-system/role', { params: { id: roleId } });
}

// 新增-编辑角色接口
export function addRole(data: Role.Api.RoleDto): Promise<HttpResponse<null>> {
  return axios.post('/sys-system/role', data);
}

// 编辑角色接口
export function updateRole(
  data: Role.Api.RoleDto
): Promise<HttpResponse<null>> {
  return axios.put('/sys-system/role', data);
}

// 删除角色接口
export function deleteRole(roleId: string): Promise<HttpResponse<null>> {
  return axios.delete('/sys-system/role', { params: { id: roleId } });
}

// 获取菜单按钮树
export function getBtnTree(): Promise<HttpResponse<RoleMenu.Api.MenuTree[]>> {
  return axios.get('/sys-system/menu/button/tree', {});
}

// 获取所有接口
export function getAllRequest(): Promise<HttpResponse<Role.Api.RequestDto[]>> {
  return axios.get('/sys-system/request/list', {});
}

// 获取角色下用户列表
export function getUsersInRole(
  roleId: string
): Promise<HttpResponse<GetPageRes<Role.Api.UserDto[]>>> {
  return axios.get('/sys-system/role/users/page', {
    params: {
      roleId,
      pageNo: 1,
      pageSize: 999,
    },
  });
}
