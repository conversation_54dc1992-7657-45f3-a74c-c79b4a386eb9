import axios from 'axios';
import { Paging } from '@/types/global';

export interface IssuesParams {
  fileId: string;
  projectId: string;
  title: string;
  state?: number;
  pageNo: number;
  pageSize: number;
  createUser?: string;
}
export interface IssueFileItem {
  id: string;
  dimensionString: string;
  picToken: string;
}

export interface IssuePoint {
  issueId: string;
  dimensionString: string;
}

interface fileDetail {
  fileId: string;
}

export interface IssuesDetail {
  id: string;
  title: string;
  creater: string;
  createDate: string;
  state: number;
  message: string;
  issueFileItems: IssueFileItem[];
  issueFileList: fileDetail[];
  picUrl: string;
  createBy: string;
  issueRecipientList: any[];
  stage: number;
  type: number;
  publishStatus: number;
}

export interface IssueLog {
  id: string;
  createDate: string;
  createUsername: string;
  picType: number;
  reply: string;
  picToken: string;
}

export interface ShowIssueParam {
  issuePointList: IssuePoint[];
  issueList: IssuesDetail[];
}

export interface PictureItem {
  fileId: string;
  picToken: string;
}
export interface IssueFormInter {
  id: string;
  reply: string;
  status: number;
  items: PictureItem[];
  picToken: string;
  projectId: string;
}

interface issueStatus {
  issueId: number;
  publishStatus: number;
}

// 查询问题列表-分页
export function queryissuesList(params: IssuesParams) {
  return axios.get<Paging<IssuesDetail>>('/cde-collaboration/issue/list', {
    params,
  });
}

// 查询问题操作日志
export function queryissueLog(bizId: string) {
  return axios.get<Paging<IssueLog>>('/cde-collaboration/operlog/list', {
    params: {
      bizId,
      pageNo: 1,
      pageSize: 9999,
    },
  });
}

export function replyIssue(parmas: IssueFormInter) {
  return axios.post('/cde-collaboration/issue/add-pic', parmas);
}

// 设置问题发布状态
export function setPublishedStatus(data: issueStatus) {
  return axios.post('/cde-collaboration/issue/updatepublishstatus', data);
}
