<template>
  <a-collapse expand-icon-position="right" :show-expand-icon="true">
    <FileCollapseItem
      v-for="file in files"
      :key="(file as fileObj).key"
      :file="(file as fileObj)"
      :source-file="(file as fileObj)"
      @file-change="fileChange"
    ></FileCollapseItem>
  </a-collapse>
</template>

<script lang="ts" setup>
  import { defineProps } from 'vue';
  import FileCollapseItem from './file-collapse-item.vue';
  import { cloneDeep } from 'lodash';

  interface fileObj {
    id?: string | number;
    key?: string | number;
  }

  const props = defineProps({
    files: {
      type: Array,
      default() {
        const fileList: fileObj[] = [];
        return fileList;
      },
    },
  });

  const emits = defineEmits(['update:files']);
  const fileChange = (file: fileObj) => {
    const copyFiles = cloneDeep(props.files);
    props.files.forEach((item: any, index: number) => {
      if (item.key === file.key) {
        copyFiles.splice(index, 1);
      }
    });
    emits('update:files', copyFiles);
  };
</script>

<style scoped></style>
