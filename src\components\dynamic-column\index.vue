<template>
  <a-trigger
    trigger="click"
    :unmount-on-close="false"
    :popup-translate="[-95, -10]"
  >
    <icon-settings :size="14" style="cursor: pointer" />
    <template #content>
      <div class="card-basic">
        <div class="content">
          <a-space
            fill
            style="justify-content: flex-start; padding: 4px 8px"
            v-for="item in columnsView"
          >
            <a-checkbox
              :value="item.dataIndex"
              v-model="item.isSelected"
              @change="() => handleChange(item)"
            ></a-checkbox>
            <div class="title">{{ item.title }}</div>
          </a-space>
        </div>
        <a-divider style="margin: 4px" />
        <div class="footor">
          <a-button
            style="width: 100%"
            size="small"
            type="outline"
            @click="reset"
            >重置</a-button
          >
        </div>
      </div>
    </template>
  </a-trigger>
</template>

<script lang="ts" setup name="DynamicColumn">
  import { computed, ComputedRef, inject, Ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { ViewColumn } from './hooks';

  const { t } = useI18n();
  const columns = inject<Ref<ViewColumn[]>>('allColumns')!;

  const columnsView: ComputedRef<ViewColumn[]> = computed(() => {
    return columns.value.map((column) => {
      return { ...column, title: t(column.title!) };
    });
  });

  function handleChange(column: ViewColumn) {
    columns.value = columns.value.map((item) => {
      if (item.dataIndex === column.dataIndex) {
        return { ...item, isSelectd: column.isSelected };
      }
      return item;
    });
  }

  function reset() {
    columns.value = columns.value.map((item) => {
      item.isSelected = item.defaultSelected;
      return item;
    });
  }
  reset();
</script>

<style scoped lang="less">
  .card-basic {
    width: 160px;
    background-color: var(--color-bg-popup);
    border-radius: 4px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
    .content {
      padding: 10px;
      height: 132px;
      overflow: auto;
      .title {
        margin-left: 10px;
        font-size: 14px;
        font-weight: 500;
        width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .footor {
      padding: 8px 12px;
    }
  }
</style>
