<template>
  <div class="cde-bim-viewer">
    <NavBar :file-name="fileName">
      <template #toolBar>
        <ToolBar
          :reset-tool="resetToolFlag"
          :tool-group="toolGroup"
          @change-tool="changeTool"
          @close-tool="closeTool"
        ></ToolBar>
      </template>
    </NavBar>
    <div class="bim-content">
      <div class="viewer-box viewer-box-color">
        <div id="modelViewer">
          <XBaseViewer
            v-if="engine === 'XBase' && modelFile"
            :model-file="modelFile"
            :viewer-type="viewerType"
            @get-x-base-instance="getXBaseInstance"
          ></XBaseViewer>
        </div>
        <a-alert v-if="alertShow" class="alert-box">
          {{ $t('model-collaboration.click-add-pin') }}
          <template #action>
            <a-space>
              <a-button size="mini" type="dashed" @click="alertClose">{{
                $t('model-collaboration.cancel')
              }}</a-button>
              <a-button size="mini" type="primary" @click="alertSubmit">{{
                $t('model-collaboration.complete')
              }}</a-button>
            </a-space>
          </template>
        </a-alert>
      </div>
      <div id="tool-wrap" ref="toolWrap">
        <component
          :is="currentComponent"
          v-if="showToolComponent"
          v-bind="componentProps"
          @close="closeTool"
        ></component>
      </div>
      <Ai v-if="route.name === 'bimView'" class="ai"></Ai>
    </div>
    <!-- <floatingBall /> -->
  </div>
</template>

<script setup>
  import { ref, shallowRef, reactive, onMounted, onBeforeUnmount } from 'vue';
  import NavBar from './components/navbar/index.vue';
  import XBaseViewer from './XBase/viewer.vue';
  import {
    getFileInfoById,
    getModelInfoById,
    getFileVersionInfoById,
  } from './api';
  import floatingBall from '@/components/Ai/components/floatingBall/index.vue';
  import { useRoute } from 'vue-router';
  import { engineType } from '@/utils/BIM-Engine/dictionary/common';
  import ToolBar from './components/toolbar/index.vue';
  import { parseViewerEngine } from '@/utils/BIM-Engine/XBase/utils/engine-options';
  import Ai from '@/components/Ai/index.vue';
  import {
    changeToolFn,
    listenerToolChange,
    removeToolListener,
  } from '@/utils/bim-view-tool-listener';
  import useModelToolsStore from '@/store/modules/model-viewer/index';
  import { storeToRefs } from 'pinia';

  const store = useModelToolsStore();
  const { alertShow } = storeToRefs(store);
  const isPc = !(window.innerWidth <= 768);
  const route = useRoute();
  const { query } = route;
  const modelFile = ref('');
  const fileName = ref('');
  const engine = ref(engineType[query.engine] || 'XBase');
  const viewerType = ref(query.viewerType || '');
  // 预览工具栏分组
  const toolGroup = ref(
    route.query.type ? `${route.query.type}Tools` : 'defaultTools'
  );
  const toolWrap = ref('');
  const resetToolFlag = ref(false);
  const init = async () => {
    // todo 此处暂只处理了大象云的文件逻辑，后续重构构力引擎时，需要做兼容处理
    const res = query.version
      ? await getFileVersionInfoById({
          id: query.idStr,
          version: query.version,
        })
      : await getFileInfoById(query.idStr);
    if (res.status) {
      modelFile.value = res.data || '';
      fileName.value = res.data.name;
      const tooltype = parseViewerEngine(fileName.value);
      if (tooltype && toolGroup.value === 'defaultTools') {
        toolGroup.value = `d2Tools`;
      }
    }
  };

  init();

  // 用户预览工具栏弹窗所需数据
  const componentProps = {
    viewer: null, // 实例化对象
    modelData: null, // 模型数据
  };

  const setMouseStyle = () => {
    const element = document.getElementById('Xbase-viewer');
    if (element) {
      element.style.cursor = 'default';
    }
  };

  /**
   * 弹窗关闭
   */
  const alertClose = () => {
    store.setAlertShow(false);
    setMouseStyle();
    try {
      componentProps.viewer?.clearMarker(['issue']); // 移除创建的新建标签
      componentProps.viewer?.stopListenToAddMarker(); // 移除打点监听事件
      componentProps.viewer?.viewer.clearEntitiesColor(); // 清除所有自定义构件颜色
    } catch (error) {}
  };

  /**
   * 弹窗提交
   */
  const alertSubmit = () => {
    store.setAlertShow(false);
    setMouseStyle();
    store.setAlertSubmit(true);
  };

  const showToolComponent = ref(false);
  const currentComponent = shallowRef('');
  const setToolComponentWidth = (width) => {
    toolWrap.value.style.width = `${width}px`;
  };
  const changeTool = (tool) => {
    if (tool.visible) {
      showToolComponent.value = true;
      setToolComponentWidth(tool.width);
      currentComponent.value = tool.component;
      console.log('[ tool.component ] >', tool.component);
    } else {
      setToolComponentWidth(0);
      showToolComponent.value = false;
    }
  };
  const closeTool = () => {
    showToolComponent.value = false;
    currentComponent.value = null;
    setToolComponentWidth(0);
    resetToolFlag.value = true;
    setTimeout(() => {
      resetToolFlag.value = false;
    }, 200);
    alertClose();
  };

  // 获取大象云实例
  const getXBaseInstance = (viewer, data) => {
    componentProps.viewer = viewer;
    componentProps.modelData = data;
  };

  // 通过发布订阅模式监听工具展示， 用于在其他入口打开工具(非工具栏打开)
  listenerToolChange(changeTool);
  // changeToolFn('issueList'); // 通过唯一id打开工具

  onBeforeUnmount(() => {
    removeToolListener(); // 移除监听
  });
</script>

<style scoped lang="less">
  .cde-bim-viewer {
    width: 100%;
    height: 100vh;
    overflow-y: hidden;
    //border: 1px solid red;
  }

  #modelViewer {
    height: 100%;
  }
  .bim-content {
    position: relative;
    width: 100%;
    height: calc(100% - 64px);
    display: flex;
    //border: 1px solid black;
    .viewer-box {
      //width: 100%;
      flex: 1;
      position: relative;
      overflow: hidden;
    }
    .viewer-box-color {
      // 字体色
      :deep(
          .obv-viewer
            .model-section-panel
            .section-header
            .active-section-header
        ),
      :deep(.obv-viewer .model-tree-tab-item),
      :deep(.obv-viewer .obv-tree-infinite),
      :deep(.obv-viewer .obv-property),
      :deep(.property-title),
      :deep(.obv-viewer .obv-radio-group .obv-radio),
      :deep(.obv-panel .obv-panel-title),
      :deep(.obv-panel-container),
      :deep(.model-section-panel .section-footer .section-footer-item:hover),
      :deep(
          .obv-viewer
            .obv-radio-group
            .obv-radio-button
            .obv-radio-button__inner
        ) {
        color: #1d2129 !important;
      }
      // 浅色背景
      :deep(.obv-panel-container),
      :deep(.obv-viewer .obv-tree-infinite),
      :deep(.obv-viewer .obv-property) {
        background-color: #fff !important;
      }

      // 深色背景
      :deep(.property-title),
      :deep(.obv-panel .obv-panel-title) {
        background-color: #f2f3f5 !important;
      }

      // 边框颜色
      :deep(.obv-viewer .properties-search-panel .properties-search-header),
      :deep(.section-header),
      :deep(.section-footer),
      :deep(.obv-panel .obv-panel-title) {
        border-color: #f2f3f5 !important;
      }

      // 小字/按钮颜色
      :deep(.obv-viewer .obv-panel .obv-panel-close:hover),
      :deep(.obv-viewer .obv-panel .obv-panel-up-down:hover),
      :deep(
          .obv-viewer
            .properties-search-panel
            .properties-search-header
            .properties-match-type
        ),
      :deep(
          .obv-viewer
            .properties-search-panel
            .properties-search-header
            .properties-search-tips
        ),
      :deep(.my-select) {
        color: #8f8e8e !important;
      }
      :deep(.obv-viewer .obv-radio-group-border),
      :deep(
          .obv-viewer
            .obv-radio-group
            .obv-radio-button
            .obv-radio-button__inner
        ) {
        border-color: #8f8e8e;
      }
      :deep(
          .obv-viewer
            .obv-radio-group
            .obv-radio-button.is-active
            > .obv-radio-button__inner
        ) {
        color: #fff !important;
      }
    }

    .alert-box {
      z-index: 99999;
      position: absolute;
      top: 20px;
      left: calc(50% - 175px);
      width: 350px;
      white-space: nowrap;
    }
  }
  #tool-wrap {
    width: 0px;
    //border: 1px solid red;
  }
  .ai {
    position: absolute;
    bottom: 0;
    top: 0;
    right: 0;
  }
</style>
