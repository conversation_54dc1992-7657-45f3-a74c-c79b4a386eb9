<template>
  <div v-if="visible" ref="transmitPanelRef" class="upload-panel">
    <div class="close-box">
      <icon-close @click="close" />
    </div>
    <a-tabs :default-active-key="transmitType">
      <a-tab-pane key="upload" :title="t('file-manage.uploaded')">
        <a-scrollbar style="height: 420px; overflow: auto">
          <div v-if="uploadFileList.length" class="panel-file-area">
            <div
              v-for="(item, index) in uploadFileList"
              :key="item.uid"
              class="panel-file-item"
            >
              <img
                :src="folderIcon"
                alt=""
                style="width: 30px; height: 30px; margin-top: 8px"
              />
              <div class="panel-file-content">
                <div class="file-name">{{ item.name }}</div>
                <div style="display: flex; margin-top: 6px">
                  <div style="width: 190px">
                    <a-progress
                      :percent="roundToOneDecimal(item.percent / 100)"
                      size="large"
                    ></a-progress>
                  </div>
                  <span
                    style="
                      display: inline-block;
                      margin-left: 12px;
                      font-weight: lighter;
                      flex: 1;
                      text-align: center;
                    "
                    >{{ fileStatus[String(item.state)] }}</span
                  >
                </div>
              </div>
              <div class="panel-operation">
                <icon-pause-circle
                  v-if="item.state === 2"
                  style="margin-left: 8px"
                  :size="18"
                  @click="pauseUpload(index)"
                />
                <icon-play-circle
                  v-if="item.state === 3"
                  style="margin-left: 8px"
                  :size="18"
                  @click="restartUpload(index)"
                />
                <icon-delete
                  style="margin-left: 8px"
                  :size="18"
                  @click="cancelUpload(index)"
                />
              </div>
            </div>
          </div>
          <div v-else class="panel-empty-area">
            <a-empty :description="t('file-manage.no-upload-file')" />
          </div>
        </a-scrollbar>
      </a-tab-pane>
      <a-tab-pane key="download" :title="t('file-manage.table-download')">
        <a-scrollbar style="height: 420px; overflow: auto">
          <div v-if="downloadList.length" class="panel-file-area">
            <div
              v-for="item in downloadList"
              :key="item.id"
              class="panel-file-item"
            >
              <img
                :src="folderIcon"
                alt=""
                style="width: 30px; height: 30px; margin-top: 8px"
              />
              <div class="panel-file-content">
                <div class="file-name">{{ item.name }}</div>
                <div style="display: flex; margin-top: 6px">
                  <div style="width: 290px">
                    <a-progress
                      :percent="roundToOneDecimal(item.percent / 100)"
                      size="large"
                    ></a-progress>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="panel-empty-area">
            <a-empty :description="t('file-manage.no-download-file')" />
          </div>
        </a-scrollbar>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import { defineEmits, defineProps, ref, watch, nextTick, toRaw } from 'vue';
  import { useUploadFileStore } from '@/store';

  import { fileStatus } from '@/directionary/file-upload';
  import folderIcon from '@/assets/images/knowledge-base/folder.svg?url';
  import { storeToRefs } from 'pinia';
  import useFileStore from '@/store/modules/file';
  import { useI18n } from 'vue-i18n';

  const transmitPanelRef: any = ref(null);
  const { t } = useI18n();

  const uploadStore = useUploadFileStore();
  const { uploadFileList } = storeToRefs(uploadStore);

  const fileStore = useFileStore();
  const { downloadList } = storeToRefs(fileStore);

  const emits = defineEmits(['update:visible', 'finish']);
  const props = defineProps({
    visible: {
      type: Boolean,
      default: true,
    },
    position: {
      type: Object,
      default() {
        return {
          top: 100,
          right: 30,
        };
      },
    },
    transmitType: {
      type: String,
      default: 'upload',
    },
  });

  function roundToOneDecimal(num: number) {
    return Math.round(num * 10) / 10;
  }

  // 暂停上传
  const pauseUpload = (index: number) => {
    uploadStore.pauseUpload(uploadFileList.value[index]);
  };
  // 恢复上传
  const restartUpload = (index: number) => {
    uploadStore.resumeUpload(uploadFileList.value[index]);
  };

  // 取消上传
  const cancelUpload = (index: number) => {
    uploadStore.cancelSingle(uploadFileList.value[index]);
  };

  const close = () => {
    emits('update:visible', false);
  };

  watch(
    () => uploadFileList.value.length,
    (val) => {
      if (!val) {
        emits('finish');
        close();
      }
    }
  );

  const setPosition = () => {
    const poi = toRaw(props.position);
    if (transmitPanelRef.value && poi.top && poi.top * 1 === poi.top) {
      transmitPanelRef.value.style.top = `${poi.top}px`;
    }
    if (transmitPanelRef.value && poi.left && poi.left * 1 === poi.left) {
      transmitPanelRef.value.style.left = `${poi.left}px`;
    }
    if (transmitPanelRef.value && poi.bottom && poi.bottom * 1 === poi.bottom) {
      transmitPanelRef.value.style.bottom = `${poi.bottom}px`;
    }
    if (transmitPanelRef.value && poi.right && poi.right * 1 === poi.right) {
      transmitPanelRef.value.style.right = `${poi.right}px`;
    }
  };
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        nextTick(() => {
          setPosition();
        });
      }
    },
    {
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  .upload-panel {
    width: 400px;
    height: 500px;
    position: fixed;
    top: 100px;
    right: 400px;
    z-index: 1100;
    background-color: white;
    box-shadow: 0px 0px 10px 2px #c3c3c3;
    border-radius: 6px;
    padding: 12px;
    padding-top: 6px;
    overflow: hidden;
    .close-box {
      position: absolute;
      z-index: 100;
      top: 15px;
      right: 12px;
      cursor: pointer;
    }
    .panel-file-area {
      height: 420px;
      //border: 1px dashed #c3c3c3;
      .panel-file-item {
        height: 70px;
        border-bottom: 1px solid rgb(229, 230, 235);
        display: flex;
        .panel-file-content {
          width: 270px;
          overflow: hidden;
          height: 60px;
          //border: 1px solid red;
          margin-top: 8px;
          margin-left: 8px;
          .file-name {
            height: 25px;
            line-height: 25px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        .panel-operation {
          display: flex;
          margin-top: 8px;
          //border: 1px solid red;
          align-content: center;
          align-items: center;
          cursor: pointer;
          flex: 1;
        }
      }
    }
    .panel-empty-area {
      height: 420px;
      display: flex;
      align-content: center;
      align-items: center;
    }
  }
</style>
