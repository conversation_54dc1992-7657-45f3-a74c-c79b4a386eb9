<template>
  <div class="userCenter">
    <a-row style="margin-bottom: 20px">
      <a-col :flex="1">
        <table-title :title="$t('role.role-list')"></table-title>
      </a-col>
    </a-row>

    <div
      class="arco-row arco-row-align-start arco-row-justify-start"
      style="margin-bottom: 20px"
    >
      <div class="arco-col" style="flex: 1 1 0%">
        <a-row>
          <a-col :span="14">
            <a-input
              v-model="searchValueParam"
              placeholder="请输入角色名称、角色编码进行查询"
              @keyup.enter="search"
              allow-clear
              @clear="reset"
            />
          </a-col>
        </a-row>
      </div>

      <div
        data-v-04aea224=""
        class="arco-col"
        style="flex: 0 0 86px; text-align: right"
      >
        <a-space
          ><a-button type="outline" @click="search">
            <template #icon> <icon-search /> </template
            >{{ $t('list.options.btn.search') }}</a-button
          >
          <a-button type="outline" @click="reset"
            ><template #icon><icon-loop /> </template
            >{{ $t('list.options.btn.reset') }}</a-button
          >
          <a-divider direction="vertical" :margin="10" />
          <a-button type="primary" @click="handelAddRole">{{
            $t('role.add-roles')
          }}</a-button>
        </a-space>
      </div>
    </div>

    <a-divider :margin="20" style="margin-top: 4px" />
    <div class="table-box">
      <a-table
        stripe
        row-key="id"
        :loading="loading"
        :scroll="scroll"
        :pagination="pagination"
        :columns="columns"
        :data="RoleList"
        :bordered="false"
        table-layout-fixed
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #isGeneral="{ record }">
          <span v-if="record.isGeneral" style="color: green">通用</span>
          <span v-else style="color: #999">不通用</span>
        </template>

        <!-- 编辑、查看、删除 -->
        <template #operation="{ record }">
          <a-button type="text" size="small" @click="updateRole(record.id)">
            {{ $t('table.opt.edit') }}</a-button
          >
          <a-button type="text" size="small" @click="viewRole(record.id)">{{
            $t('user-center.table.view')
          }}</a-button>
          <a-popconfirm
            content="
              确定删除这个角色吗？
            "
            type="info"
            @ok="handleDelete(record)"
          >
            <a-button type="text" size="small"> 删除 </a-button>
          </a-popconfirm>
        </template>
      </a-table>
    </div>
    <AddRoles
      v-if="addDialogVisible"
      v-model:visible="addDialogVisible"
      :title="dialogTitle"
      :select-id="selectId"
      :current-title="currentTitle"
      @refresh="refreshData"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, onMounted, reactive } from 'vue';
  import AddRoles from './components/add-roles.vue';
  import TableTitle from '@/components/table-title/index.vue';
  import { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import { useI18n } from 'vue-i18n';
  import { useUserStore } from '@/store';
  import { getRolePages, deleteRole } from './api';
  import useLocale from '@/hooks/locale';
  import { Message } from '@arco-design/web-vue';
  import { userInfoType } from '../user-center/api';

  const scroll = {
    y: 'calc(100vh -350px)',
  };

  const { t } = useI18n();
  const userStore = useUserStore();
  // 登录用户id
  const userId = computed(() => {
    return userStore.id;
  });
  // 用户权限信息
  const userInfo = ref<userInfoType>({});
  // 搜索框form对象
  const formModel = ref<Role.Api.RoleDto>();
  //
  const searchValueParam = ref('');
  // 展开dialog的标题
  const dialogTitle = ref('');
  //  当前弹框标识符
  const currentTitle = ref('');
  // 获取表格数据loading状态
  const loading = ref(false);
  // 新增控制展开
  const addDialogVisible = ref(false);

  const pagination = reactive({
    showJumper: true,
    showTotal: true,
    showPageSize: true,
    current: 1,
    pageSize: 20,
    total: 0,
    pageSizeOptions: [20, 50, 100],
    pageSizeChangeResetCurrent: true,
  });
  // 编辑带入的数据
  const selectId = ref('');
  // table的data
  const RoleList = ref<Role.Api.PageRoleDto[]>([]);
  const columns = computed<TableColumnData[]>(() => [
    {
      title: '角色名称',
      dataIndex: 'name',
      minWith: 120,
      align: 'left',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '角色编码',
      dataIndex: 'code',
      minWith: 120,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '是否通用',
      dataIndex: 'isGeneral',
      slotName: 'isGeneral',
      align: 'left',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      align: 'left',
      minWidth: 180,
    },
    {
      title: '操作',
      align: 'center',
      slotName: 'operation',
      width: 160,
    },
  ]);
  // 获取table数据
  const getTableData = async (params: Role.Api.PageRoleParam) => {
    const { data } = await getRolePages(params);
    RoleList.value = data.list || [];
    pagination.total = data.total || 0;
    loading.value = false;
  };

  // 弹框关闭刷新数据
  const refreshData = () => {
    pagination.current = 1;
    search();
  };
  // 查询
  const search = () => {
    if (loading.value && userInfo.value?.admin !== '0') {
      return;
    }
    loading.value = true;
    const params = {
      pageNo: pagination.current || 1,
      pageSize: pagination.pageSize || 20,
      searchValue: searchValueParam.value,
    };
    getTableData(params);
  };
  // 重置
  const reset = () => {
    // formModel.value = {};
    searchValueParam.value = '';
    pagination.current = 1;
    search();
  };
  // table当前页变化
  const onPageChange = (val: any) => {
    pagination.current = val;
    search();
  };
  // table每页条数变化
  const onPageSizeChange = (val: any) => {
    pagination.pageSize = val;
    search();
  };

  // 新增角色
  const handelAddRole = () => {
    selectId.value = '';
    dialogTitle.value = '添加角色';
    currentTitle.value = 'add';
    addDialogVisible.value = true;
  };
  // 查看角色
  const viewRole = (id: any) => {
    selectId.value = id;
    dialogTitle.value = '查看角色';
    currentTitle.value = 'view';
    addDialogVisible.value = true;
  };
  // 编辑角色
  const updateRole = (id: string) => {
    selectId.value = id;
    dialogTitle.value = '编辑角色';
    currentTitle.value = 'edit';
    addDialogVisible.value = true;
  };

  async function handleDelete(record: Role.Api.PageRoleDto) {
    await deleteRole(record.id);
    Message.success('删除成功！');
    search();
  }

  onMounted(async () => {
    search();
  });
</script>

<script lang="ts">
  export default {
    name: 'UserCenter',
  };
</script>

<style scoped lang="less">
  .userCenter {
    padding: 20px;
    border: none;
    height: 100%;
    .table-box {
      // height: calc(100% - 230px);
      height: calc(100% - 125px);
    }
    :deep(.arco-table) {
      height: 100%;
    }
  }
  :deep(.arco-btn-size-small) {
    padding: 0 5px !important;
  }
  :deep(.arco-form-item-label) {
    width: 100px;
  }
  :deep(.arco-col-8) {
    padding: 0 8px;
  }
  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    white-space: nowrap !important;
  }
  .border-box {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 20px;
  }
</style>
