import type {
  RouteLocationNormalizedGeneric,
  RouteLocationNormalizedLoadedGeneric,
  Router,
} from 'vue-router';
import NProgress from 'nprogress';
import {
  useUserStore,
  usePrjPermissionStore,
  useGlobalModeStore,
} from '@/store';
import { isLogin } from '@/utils/auth'; // progress bar
import { getUserName } from '@/utils/auth';
import {
  getBtnPermissionOfProject,
  getBtnPermissionOfSys,
} from '@/api/modules/user';
import usePermissionStore from '@/store/modules/permission';

// 监听路由跳转时设置GlobalMode 用于浏览器前进后退按钮
function changeGlobalMode(path: any, mode: any) {
  const globalModeStore = useGlobalModeStore();
  if (path !== '/setup') {
    // 系统设置不进行修改
    globalModeStore.changeGlobalMode(mode);
  }
}

// const prjPermissionStore = usePrjPermissionStore();

export default function setupPermissionGuard(router: Router) {
  router.beforeEach(async (to, _, next) => {
    const projectId = (to.params?.projectId ||
      to.query?.projectId ||
      '') as string;
    const source = (to.query?.source || '') as string;
    // 如果有项目id或者是标准相关页面  设置未项目空间
    if (projectId || to.path.includes('/standard/')) {
      changeGlobalMode(to.path, 'project');
      if (projectId) {
        // 获取当前项目下的权限
        setTimeout(() => {
          const prjPermissionStore = usePrjPermissionStore();
          if (source !== 'knowledge') {
            prjPermissionStore.setPermission(projectId);
          }
        }, 100);
      }
    } else changeGlobalMode(to.path, 'work');
    const userStore = useUserStore();
    const userName = getUserName();
    if (isLogin() && !userStore.id && userName) await userStore.info();
    // 受控模式下的按钮权限
    handleControlledPermission(to, _);

    // 只有在有项目的情况下才需要设置权限
    next();
    NProgress.done();
  });
}

async function handleControlledPermission(
  to: RouteLocationNormalizedGeneric,
  _: RouteLocationNormalizedLoadedGeneric
) {
  if (!to.meta.requiresAuth) {
    return;
  }
  const permissionStore = usePermissionStore();

  if (to.path.includes('/project/')) {
    // 在项目空间下就取项目权限
    const projectId = to.params.projectId as string;
    permissionStore.setProjectPremission(projectId);
  } else {
    // 不在项目空间取全局权限
    permissionStore.setPublicPermission();
  }
}
