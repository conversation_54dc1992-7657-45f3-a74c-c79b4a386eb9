<template>
  <a-modal
    v-model:visible="visible"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="cancel"
  >
    <template #title> {{ $t('standard-setting.add-attribute') }} </template>
    <template #footer>
      <div class="flexBox">
        <span
          ><icon-exclamation-circle />{{ $t('standard-setting.go-to')
          }}<span class="tipsText" @click="toUrl"
            >{{ $t('standard-setting.properties-tab') }} </span
          >{{ $t('standard-setting.to-create-properties') }}</span
        >
        <a-button type="primary" @click="formSubmit">{{
          $t('standard-setting.add')
        }}</a-button>
      </div>
    </template>
    <a-spin style="width: 100%" :loading="loading">
      <div class="content">
        <div style="margin-bottom: 16px"
          >{{
            $t(
              'standard-setting.select-existing-property-to-add-naming convention.'
            )
          }}
          <!-- <router-link to="/" class="tipsText">了解详情</router-link> -->
        </div>
        <a-form ref="formRef" :model="attributeForm">
          <a-row :gutter="12">
            <a-col :span="24">
              <a-form-item
                label-col-flex="150px"
                field="attributeId"
                :label="$t('standard-setting.attribute')"
                :rules="[
                  {
                    required: true,
                    message: $t('standard-setting.please-select-attribute'),
                  },
                ]"
                :validate-trigger="['change', 'input']"
              >
                <a-select
                  v-model="attributeForm.attributeId"
                  class="mode-select"
                >
                  <a-option
                    v-for="item of option"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name"
                  />
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <!-- <a-row :gutter="12">
            <a-col :span="24">
              <a-form-item
                label-col-flex="150px"
                field="receivers"
                label="是否允许属性为空"
                :rules="[{ required: false, message: '是否允许属性为空' }]"
              >
                <a-select
                  v-model="attributeForm.emptyStatus"
                  class="mode-select"
                >
                  <a-option
                    v-for="item of emptyStatusOption"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name"
                  />
                </a-select>
              </a-form-item>
            </a-col>
          </a-row> -->
        </a-form>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { useRoute, useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { defineEmits, ref, reactive } from 'vue';
  import useLoading from '@/hooks/loading';
  import { queryMetasList } from '@/views/standard-manage/standard-list/metas/api';
  import { addAttribute, optionInter, enclosureFormInter } from '../api';
  import { useI18n } from 'vue-i18n';
  import { useUserStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';

  const { t } = useI18n();
  const formRef = ref<any>();
  const visible = ref(false);

  const option = ref<optionInter[]>([]);
  const emptyStatusOption = ref<optionInter[]>([]);
  const attributeForm = reactive<enclosureFormInter>({
    attributeId: '',
    emptyStatus: '',
  });
  emptyStatusOption.value = [
    {
      id: '0',
      name: t('standard-setting.not'),
    },
    {
      id: '1',
      name: t('standard-setting.yes'),
    },
  ];
  const { loading, setLoading } = useLoading(false);

  const route = useRoute();
  const router = useRouter();

  const emits = defineEmits(['refresh']);
  const cancel = () => {
    attributeForm.attributeId = '';
    attributeForm.emptyStatus = '';
    visible.value = false;
  };

  const toUrl = () => {
    localStorage.setItem('standard-default-tab', 'attribute');
    router.push(`/standard/list`);
  };

  // 绑定属性提交
  const formSubmit = async (done: any) => {
    const res = await formRef.value?.validate();
    if (!res) {
      const params: any = {
        attributeId: attributeForm.attributeId,
        emptyStatus: attributeForm.emptyStatus,
        standardId: route.query.id,
        newSort: 0,
      };
      setLoading(true);
      const saveRes: any = await addAttribute(params);
      if (saveRes.status) {
        Message.success(saveRes.message);
        emits('refresh');
        cancel();
      } else {
        Message.error(saveRes.message);
      }
      setLoading(false);
    }
  };

  const userStore = useUserStore();
  const userId = getUserId() || '';
  const projectId = getLocalstorage(`last_project_${userId}`) || '';

  const alertShow = async () => {
    try {
      const resData: any = await queryMetasList({
        pageNo: 1,
        pageSize: 999,
        groupId: userStore.admin === 0 ? '0' : projectId,
      });
      const resData2: any = await queryMetasList({
        pageNo: 1,
        pageSize: 999,
        groupId: '1',
      });
      option.value = [...resData2.data.list, ...resData.data.list];
      if (resData.status) {
        visible.value = true;
      }
    } catch (err) {
      console.log(err);
    } finally {
      visible.value = true;
    }
  };
  defineExpose({
    alertShow,
  });
</script>

<script lang="ts">
  export default {
    name: 'CreateAttribute',
    inheritAttrs: false,
  };
</script>

<style scoped lang="less">
  .title {
    position: relative;

    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }

    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }

    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  .file-list-wrap {
    margin-top: 16px;
  }

  .tipsText {
    color: #3366ff;
    text-decoration: none;
    cursor: pointer;
  }

  .flexBox {
    display: flex;
    justify-content: space-between;
  }

  :deep(.arco-select-view),
  :deep(.arco-textarea-wrapper),
  :deep(.arco-input-wrapper),
  :deep(.arco-picker),
  :deep(.arco-input-tag),
  :deep(.arco-select-view-single),
  :deep(.arco-textarea),
  :deep(.arco-form-item-content-wrapper) {
    background-color: #fff;
    border-radius: 8px;
  }
  :deep(.arco-form-item-content-wrapper) {
    border: 1px solid #c9cdd4 !important;
  }
</style>
