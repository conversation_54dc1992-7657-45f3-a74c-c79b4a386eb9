<template>
  <div class="drawer-container" :style="`width:${curWidth}px`">
    <div class="content-container">
      <iframe
        id="pks"
        style="
          position: absolute;
          top: 0;
          height: 100%;
          width: 100%;
          background: linear-gradient(180deg, #c5d3ff 0%, #ffffff 100%);
          border: none;
        "
      ></iframe>
      <div class="button-group">
        <div class="attach"
          ><img src="@/assets/images/model-view/ai-attach.png" alt="" /><span
            >指定文件</span
          ></div
        >
        <div class="classified"
          ><img
            src="@/assets/images/model-view/ai-classified.png"
            alt=""
          /><span>一键归类</span></div
        >
        <div class="rename"
          ><img src="@/assets/images/model-view/ai-rename.png" alt="" /><span
            >一键命名</span
          ></div
        >
      </div>
    </div>
  </div>
</template>

<script setup>
  import { defineProps, watch, ref, onMounted } from 'vue';
  import XkOpenaiChatUi from './api/xkchat-openai-ui';
  import useAiStore from '@/store/modules/ai/index';
  import { storeToRefs } from 'pinia';
  import api from './api/interface';
  import router from '@/router';

  const aiStore = useAiStore();
  const { aiModuleVisible } = storeToRefs(aiStore);
  /**
   * width:内容区域的宽度
   */
  const props = defineProps({
    width: {
      type: Number,
      default: 464,
    },
  });
  const curWidth = ref(0);
  const animationInterval = ref(null);
  const handleAnimation = function (status) {
    animationInterval.value = requestAnimationFrame(() => {
      if (status) {
        curWidth.value += Number((props.width / 4).toFixed());
      } else {
        curWidth.value -= Number((props.width / 4).toFixed());
      }
      if (curWidth.value >= props.width || curWidth.value <= 0) {
        // eslint-disable-next-line no-unused-expressions
        curWidth.value <= 0
          ? (curWidth.value = 0)
          : (curWidth.value = props.width);
        cancelAnimationFrame(animationInterval.value);
      } else {
        handleAnimation(aiModuleVisible.value);
      }
    });
  };

  const getAccessToken = async () => {
    const result = await api.getAiToken();
    return result.data;
  };
  const initAi = async () => {
    if (window.location.pathname.includes('/share-download')) return;
    const accessToken = await getAccessToken();
    // eslint-disable-next-line no-new
    new XkOpenaiChatUi({
      assistantId: 'jg03awq41gh56bos88j9',
      token: accessToken,
      dom: document.getElementById('pks'),
    });
  };
  onMounted(() => {
    initAi();
  });
  watch(aiModuleVisible, (newVal) => {
    handleAnimation(newVal);
  });
</script>

<style lang="less" scoped>
  .drawer-container {
    overflow: hidden;
    .content-container {
      position: relative;
      display: block;
      height: 100%;
      min-width: 464px;
      //background-color: rgba(15, 37, 41, 0.72);
      transition: transform 1s ease;
    }
    .content-visible--right {
      right: 0;
    }
    .mask-visible {
      display: block;
      opacity: 1;
    }
  }
  .button-group {
    display: flex;
    position: absolute;
    bottom: 97px;
    left: 24px;
    border-radius: 30px;
    padding: 0 15px;
    .attach,
    .classified,
    .rename {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #3366ff;
      border: 1px solid #3366ff;
      border-radius: 20px;
      padding: 3px 7px;
      margin-right: 5px;
      cursor: pointer;
      img {
        width: 14px;
        height: 14px;
      }
      span {
        margin-left: 4px;
      }
    }
  }
</style>
