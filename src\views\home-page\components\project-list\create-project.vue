<template>
  <a-modal
    :visible="visible"
    :title="$t('dashboard.create-roject')"
    width="50%"
    :mask-closable="false"
    :ok-loading="submitBtnLoading"
    draggable
    :esc-to-close="false"
    @cancel="cancel"
    @ok="submitData"
  >
    <template #title> {{ $t('dashboard.create-roject') }} </template>
    <div class="content">
      <a-form ref="formRef" :model="formData" label-align="left" :rules="rules">
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item
              field="name"
              :label="$t('dashboard.project-name')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
              :rules="[
                {
                  required: true,
                  message: $t('dashboard.project-name-errMsg'),
                },
              ]"
            >
              <remove-spaces-input
                v-model="formData.name"
                :placeholder="$t('dashboard.please-enter')"
                :max-length="currentLocale === 'en-US' ? 255 : 100"
                show-word-limit
              />
              <!-- <a-input
                v-model="formData.name"
                :placeholder="$t('dashboard.please-enter')"
              /> -->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="code"
              :label="$t('dashboard.project-code')"
              label-col-flex="70px"
              :rules="[
                {
                  required: true,
                  message: $t('dashboard.project-code-errMsg'),
                },
                {
                  match: /^[0-9A-Za-z]*$/g,
                  message: $t('dashboard.project-code-number-errMsg'),
                },
              ]"
              :validate-trigger="['change', 'input']"
            >
              <remove-spaces-input
                v-model="formData.code"
                :placeholder="$t('dashboard.please-enter')"
                :max-length="currentLocale === 'en-US' ? 255 : 100"
                show-word-limit
              />
              <!-- <a-input
                v-model="formData.code"
                :placeholder="$t('dashboard.please-enter')"
              /> -->
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item
              field="position"
              :label="$t('dashboard.project-location')"
              label-col-flex="70px"
              :rules="[
                {
                  required: true,
                  message: $t('dashboard.project-location-errMsg'),
                },
              ]"
              :validate-trigger="['change', 'input']"
            >
              <remove-spaces-input
                v-model="formData.position"
                :placeholder="$t('dashboard.please-enter')"
                :max-length="currentLocale === 'en-US' ? 255 : 100"
                show-word-limit
              />
              <!-- <a-input
                v-model="formData.position"
                :placeholder="$t('dashboard.please-enter')"
              /> -->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="type"
              :label="$t('dashboard.project-type')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
            >
              <a-select
                v-model="formData.type"
                :placeholder="$t('dashboard.please-select')"
              >
                <a-option
                  v-for="type in projectTypes"
                  :key="type.value + type.label"
                  :value="type.value"
                  :label="type.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item
              field="modelEngine"
              :label="$t('dashboard.model-engine')"
              label-col-flex="70px"
              :rules="[
                {
                  required: true,
                  message: $t('dashboard.project-engine-errMsg'),
                },
              ]"
              :validate-trigger="['change', 'input']"
            >
              <a-select
                v-model="formData.modelEngine"
                :placeholder="$t('dashboard.please-select')"
              >
                <a-option
                  value="XBase"
                  :label="$t('dashboard.elephant-cloud-model-engine')"
                ></a-option>
                <!-- 护网结束之前隐藏构力引擎选项 -->
                <!-- <a-option
                  value="BimBase"
                  :label="$t('dashboard.constructive-force-model-engine')"
                ></a-option> -->
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="roadLevel"
              :label="$t('dashboard.road-level')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
            >
              <a-select
                v-model="formData.roadLevel"
                :placeholder="$t('dashboard.please-select')"
              >
                <a-option
                  v-for="type in projectRoadlevel"
                  :key="type.value + type.label"
                  :value="type.value"
                  :label="type.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item
              field="coordinate"
              :label="$t('dashboard.coordinate-system')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
            >
              <a-select
                v-model="formData.coordinate"
                :placeholder="$t('dashboard.please-select')"
              >
                <a-option
                  v-for="coordinate in projectCoordinate"
                  :key="coordinate.value + coordinate.label"
                  :value="coordinate.value"
                  :label="coordinate.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item
              field="elevation"
              :label="$t('dashboard.height-datum')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
            >
              <a-select
                v-model="formData.elevation"
                :placeholder="$t('dashboard.please-select')"
              >
                <a-option
                  v-for="elevation in projectElevation"
                  :key="elevation.value + elevation.label"
                  :value="elevation.value"
                  :label="elevation.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item
              field="loadDesign"
              :label="$t('dashboard.design-load')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
            >
              <a-select
                v-model="formData.loadDesign"
                :placeholder="$t('dashboard.please-select')"
              >
                <a-option
                  v-for="type in projectDesignLoad"
                  :key="type.value + type.label"
                  :value="type.value"
                  :label="type.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item
              field="safetyLevel"
              :label="$t('dashboard.safety-level')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
            >
              <a-select
                v-model="formData.safetyLevel"
                :placeholder="$t('dashboard.please-select')"
              >
                <a-option
                  v-for="type in projectSecurityLevel"
                  :key="type.value + type.label"
                  :value="type.value"
                  :label="type.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item
              field="environmentType"
              :label="$t('dashboard.environment-category')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
            >
              <a-select
                v-model="formData.environmentType"
                :placeholder="$t('dashboard.please-select')"
              >
                <a-option
                  v-for="environmentType in projectEnvironmentType"
                  :key="environmentType.value + environmentType.label"
                  :value="environmentType.value"
                  :label="environmentType.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item
              field="magnitude"
              :label="$t('dashboard.seismic-level')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
            >
              <a-select
                v-model="formData.magnitude"
                :placeholder="$t('dashboard.please-select')"
              >
                <a-option
                  v-for="magnitude in projectMagnitude"
                  :key="magnitude.value + magnitude.label"
                  :value="magnitude.value"
                  :label="magnitude.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item
              field="application"
              :label="$t('dashboard.integrated-product-application')"
              label-col-flex="70px"
            >
              <a-select
                v-model="formData.application"
                :placeholder="$t('dashboard.please-select')"
                multiple
                :max-tag-count="1"
                allow-search
                allow-clear
              >
                <a-option
                  v-for="item in integratedProductApplication"
                  :key="`${item?.value}_${item?.label}`"
                  :value="item?.value"
                  :label="item?.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <!-- 隐藏项目模板 -->
          <a-col :span="12">
            <a-form-item
              field="content"
              :label="$t('dashboard.project-template')"
              label-col-flex="70px"
            >
              <a-select
                v-model="formData.protemId"
                :placeholder="$t('dashboard.please-select')"
                allow-clear
              >
                <a-option
                  v-for="template in projectTemplate"
                  :key="`${template?.id}_${template?.name}`"
                  :value="template?.id"
                  :label="template?.name"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="date"
              :label="$t('dashboard.project-time')"
              label-col-flex="70px"
            >
              <a-range-picker
                v-model="projectDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="timeChange"
              />
              <!--              <a-input v-model="formData.time" style="width: 182px" />-->
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item
              field="participaUnit"
              :label="$t('dashboard.participating-units')"
              label-col-flex="70px"
              :validate-trigger="['change', 'input']"
            >
              <remove-spaces-input
                v-model="formData.participaUnit"
                :placeholder="$t('dashboard.please-enter')"
                :max-length="currentLocale === 'en-US' ? 255 : 100"
                show-word-limit
              />
              <!-- <a-input
                v-model="formData.participaUnit"
                :placeholder="$t('dashboard.please-enter')"
              /> -->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="projectPhase"
              :label="$t('dashboard.project-phase')"
              label-col-flex="70px"
              :rules="[
                {
                  required: true,
                  message: $t('dashboard.project-phase-errMsg'),
                },
              ]"
            >
              <a-select
                v-model="formData.projectPhase"
                :placeholder="$t('dashboard.please-select')"
                allow-clear
              >
                <a-option
                  v-for="item in projectPhase"
                  :key="`${item?.value}_${item?.label}`"
                  :value="item?.value"
                  :label="item?.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="12">
            <a-form-item
              field="projectStatus"
              :label="$t('dashboard.project-status')"
              label-col-flex="70px"
            >
              <a-select
                v-model="formData.projectStatus"
                :placeholder="$t('dashboard.please-select')"
                allow-clear
              >
                <a-option
                  v-for="item in projectStatus"
                  :key="`${item?.value}_${item?.label}`"
                  :value="item?.value"
                  :label="item?.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="projectProperties"
              :label="$t('dashboard.project-properties')"
              label-col-flex="70px"
              :rules="[
                {
                  required: true,
                  message: $t('dashboard.project-pro-errMsg'),
                },
              ]"
            >
              <a-select
                v-model="formData.projectProperties"
                :placeholder="$t('dashboard.please-select')"
                allow-clear
              >
                <a-option
                  v-for="item in projectProperties"
                  :key="`${item?.value}_${item?.label}`"
                  :value="item?.value"
                  :label="item?.label"
                ></a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item
              field="description"
              :label="$t('dashboard.project-overview')"
              label-col-flex="70px"
            >
              <a-textarea
                v-model="formData.description"
                :placeholder="$t('dashboard.please-enter')"
                :max-length="currentLocale === 'en-US' ? 2000 : 1000"
                show-word-limit
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import {
    projectPhase,
    projectStatus,
    projectTypes,
    projectCoordinate,
    projectElevation,
    projectMagnitude,
    projectEnvironmentType,
    projectRoadlevel,
    projectDesignLoad,
    projectSecurityLevel,
    projectProperties,
    integratedProductApplication,
  } from '@/directionary/project';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import {
    getProjectList,
    ProjectListParams,
    createProject,
    ProjectItem,
  } from '@/api/project';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';

  const { currentLocale } = useLocale();
  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['update:visible', 'submit']);
  const formRef = ref<FormInstance>();
  // 项目模版
  const projectTemplate = ref<{ id?: string; name?: string }[]>([]);
  // 获取所有项目模版
  const getList = () => {
    const params: ProjectListParams = {
      pageNo: 1,
      pageSize: 100000,
      projectType: 1,
    };
    getProjectList(params).then((res: any) => {
      if (res.code === 8000000) {
        projectTemplate.value = res.data?.list || [];
      }
    });
  };

  const removeSpace = (value: any, event: any) => {
    event.target.value = value.trim();
  };

  const formData = ref({
    name: '',
    modelEngine: 'XBase',
    type: '',
    protemId: '',
    code: '',
    description: '',
    date: [],
    position: '',
    coordinate: '',
    elevation: '',
    environmentType: '',
    magnitude: '',
    roadLevel: '',
    loadDesign: '',
    safetyLevel: '',
    participaUnit: '',
    projectPhase: '',
    projectStatus: '',
    projectProperties: '',
    application: [],
  });
  const submitBtnLoading = ref(false);
  const projectDate = ref([]);
  const timeChange = (time: any) => {
    formData.value.date = time;
    // projectDate.value = time || [];
  };
  const cancel = () => {
    emits('update:visible', false);
  };
  // eslint-disable-next-line consistent-return
  const submitData = async (): Promise<void> => {
    const result = await formRef?.value?.validate();
    if (!result) {
      const params = {
        name: formData.value.name || '',
        modelEngine: formData.value.modelEngine || '',
        type: formData.value.type || '',
        protemId: formData.value.protemId || '',
        code: formData.value.code || '',
        description: formData.value.description || '',
        planStart: projectDate?.value[0] || '',
        planEnd: projectDate?.value[1] || '',
        position: formData.value.position || '',
        coordinate: formData.value.coordinate || '',
        elevation: formData.value.elevation || '',
        environmentType: formData.value.environmentType || '',
        magnitude: formData.value.magnitude || '',
        roadLevel: formData.value.roadLevel || '',
        loadDesign: formData.value.loadDesign || '',
        safetyLevel: formData.value.safetyLevel || '',
        participaUnit: formData.value.participaUnit || '',
        projectPhase: formData.value.projectPhase || '',
        projectStatus: formData.value.projectStatus || '',
        projectProperties: formData.value.projectProperties,
        application: formData.value.application.toString(),
      };
      submitBtnLoading.value = true;
      createProject(params as unknown as ProjectItem)
        .then((res: any) => {
          if (res.code === 8000000) {
            Message.success(res.message);
            cancel();
            emits('submit', true);
          }
          submitBtnLoading.value = false;
        })
        .catch((e) => {
          if (e) {
            submitBtnLoading.value = false;
          }
        });
    }
  };
  const init = () => {
    formData.value = {
      name: '',
      modelEngine: 'XBase',
      type: '',
      protemId: '',
      code: '',
      description: '',
      date: [],
      position: '',
      coordinate: '',
      elevation: '',
      environmentType: '',
      magnitude: '',
      roadLevel: '',
      loadDesign: '',
      safetyLevel: '',
      participaUnit: '',
      projectPhase: '',
      projectStatus: '',
      projectProperties: '',
      application: [],
    };
    formRef.value?.clearValidate();
    getList();
  };
  watch(props, (value) => {
    if (value.visible) {
      init();
    }
  });
</script>

<style scoped>
  :deep(.arco-form-item-label) {
    width: 110px;
  }
</style>
