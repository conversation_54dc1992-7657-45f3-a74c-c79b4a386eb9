<template>
  <div class="menuAi list">
    <div class="left">
      <a-button type="outline" long size="small" @click="createSession">
        <template #icon> <icon-plus /> </template>
        {{ $t('ai.new.chat') }}
      </a-button>
      <a-input-search
        v-model="searchVal"
        style="margin-top: 12px"
        :placeholder="$t('ai.search')"
        allow-clear
        @blur="searchHandle"
        @press-enter="($event.target as any)?.blur()"
        @search="searchHandle"
      />
      <a-spin
        :loading="recordLoading"
        style="width: 100%; height: calc(100% - 80px); margin-top: 4px"
      >
        <div v-if="recordList.length === 0" class="empty-record">
          <EmptyFolder></EmptyFolder>
          <span>{{ $t('knowledgenew.empty-content') }}</span>
        </div>
        <div v-else class="record-list">
          <div
            v-for="(item, index) in recordList"
            :key="item.id"
            :class="[
              'record-card',
              selectedRecord?.id === item.id ? 'record-card-select' : '',
            ]"
            @click="changeRecord(item)"
            @mouseenter="handleMouseEnter(item)"
            @mouseleave="handleMouseLeave(item)"
          >
            <div class="time"
              >{{ item.updateDate }}
              <a-space
                v-if="
                  getRecordUIState(item.id).showOptions &&
                  !getRecordUIState(item.id).isEditing
                "
                :size="12"
              >
                <icon-edit
                  :size="16"
                  @click.stop="handleEditRecord(item, index)"
                />
                <a-popconfirm
                  :content="$t('ai.confirm.delete')"
                  @ok="handleDeleteRecord(item)"
                  @popup-visible-change="popupVisibleChange($event, item)"
                  @click.stop
                >
                  <icon-delete :size="16" />
                </a-popconfirm>
              </a-space>
              <a-space v-if="getRecordUIState(item.id).isEditing" :size="12">
                <icon-check :size="16" @click.stop="handleRenameRecord(item)" />
                <icon-close :size="16" @click.stop="handleCancelRename(item)" />
              </a-space>
            </div>
            <div class="title">
              <a-typography-paragraph
                v-show="!getRecordUIState(item.id).isEditing"
                :ellipsis="{
                  rows: 1,
                  showTooltip: true,
                }"
              >
                <span>{{ item.chatSessionName }}</span>
              </a-typography-paragraph>

              <div v-show="getRecordUIState(item.id).isEditing">
                <a-input
                  ref="editInputRef"
                  v-model="item.chatSessionName"
                  :max-length="20"
                  show-word-limit
                  size="small"
                  @click.stop
                />
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
    <div class="right">
      <div ref="dialogContainer" class="dialog-container"> </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, nextTick, onMounted, onUnmounted } from 'vue';
  import useAIChat from '@/hooks/aiChat';
  import {
    getAgentRecordList,
    renameAgentRecord,
    deleteAgentRecord,
  } from '@/views/dashboard/api';
  import useAIRecordUIState from '@/views/knowledge-base/composables/useAIRecordUIState';
  import { Message } from '@arco-design/web-vue';
  import { useRoute } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import useSessionStorageWatcher from '@/hooks/useSessionStorageWatcher';
  import EmptyFolder from '@/assets/images/knowledge-base/empty-folder.svg';

  const { t } = useI18n();
  const {
    genAIToken,
    initAIPage,
    setSelectedRecordSession,
    clearSelectedRecordSession,
  } = useAIChat();

  // 搜索框输入内容
  const searchVal = ref('');

  const route = useRoute();
  console.log(route, 4447);

  const props = defineProps({
    agentId: {
      type: String,
      default: 'xz33285jrxhvfo4gqrto',
    },
  });

  const recordLoading = ref(false); // 历史记录列表loading

  // AI对话页面
  const dialogContainer = ref<HTMLElement | null>(null);

  const showAIPage = async () => {
    await genAIToken();
    nextTick(() => {
      const res = initAIPage(
        props.agentId,
        'agentSession',
        dialogContainer.value,
        null,
        '请输入您的问题'
      );
      if (!res.status) {
        console.error(res.message);
      }
    });
  };

  const emptyRecord = {
    agentId: '',
    chatSessionId: '',
    chatSessionName: '',
    chatSessionStatus: 0,
    chatSessionType: 0,
    createBy: '',
    createDate: '',
    id: '',
    updateBy: '',
    updateDate: '',
    userId: '',
    deleteFlag: 0,
  };

  const selectedRecord = ref<any>();

  // 切换对话
  const changeRecord = (item: any) => {
    selectedRecord.value = item;
    setSelectedRecordSession(
      'agentSession',
      item.agentId,
      item.chatSessionId,
      '请输入您的问题'
    );
  };

  // 新增对话
  const createSession = () => {
    // 去掉判断，直接生成链接，解决在空对话提问后，点击新增对话，无法切换到新对话的问题
    // if (selectedRecord.value.agentId) {
    showAIPage();
    selectedRecord.value = emptyRecord;
    clearSelectedRecordSession();
    // }
  };

  const { getRecordUIState, clearRecordUIState } = useAIRecordUIState();
  // 对话记录列表
  const recordList = ref<Array<any>>([]);
  const queryAgentRecord = (id: string) => {
    recordLoading.value = true;
    const params: any = {
      agentId: id,
      pageNo: 1,
      pageSize: 100,
      keyword: searchVal.value,
    };
    getAgentRecordList(params)
      .then((res) => {
        if (res.status) {
          recordList.value = res?.data?.list || [];
          clearRecordUIState();
        }
      })
      .finally(() => {
        recordLoading.value = false;
      });
  };

  const editInputRef = ref<Array<HTMLInputElement | null>>([]);

  // 修改编辑对话的处理函数
  const handleEditRecord = (item: any, index: number) => {
    // console.log('item: ', item);
    const state = getRecordUIState(item.id);
    state.oldName = item.chatSessionName;
    state.isEditing = true;

    nextTick(() => {
      (editInputRef.value[index] as HTMLInputElement)?.focus();
    });
  };

  // 删除对话
  const handleDeleteRecord = async (item: any) => {
    // selectedRecord.value = item;
    const res = await deleteAgentRecord({
      chatSessionId: item.chatSessionId,
      chatSessionName: item.chatSessionName,
    });
    if (res.status) {
      Message.success(t('ai.delete.success'));
      // 如果删除的是当前对话，则切换到空白对话
      if (selectedRecord.value?.chatSessionId === item.chatSessionId) {
        selectedRecord.value = emptyRecord;
        clearSelectedRecordSession();
        showAIPage();
      }
      queryAgentRecord(props.agentId); // 查询历史对话
    }
  };

  const handleMouseLeave = (item: any) => {
    const state = getRecordUIState(item.id);
    if (!state.isEditing && !state.isDeleting) {
      state.showOptions = false;
    }
  };

  // 鼠标悬停事件处理
  const handleMouseEnter = (item: any) => {
    const state = getRecordUIState(item.id);
    if (!state.showOptions) {
      state.showOptions = true;
    }
  };
  // 历视搜索
  const searchHandle = () => {
    queryAgentRecord(props.agentId);
  };
  searchHandle();

  // 删除确认弹窗处理
  const popupVisibleChange = (visible: boolean, record: any) => {
    const state = getRecordUIState(record.id);
    state.isDeleting = visible;
    if (!visible && !state.isEditing) {
      state.showOptions = false;
    }
  };

  // 重命名
  const handleRenameRecord = async (item: any) => {
    const state = getRecordUIState(item.id);
    state.isEditing = false;
    const res = await renameAgentRecord({
      chatSessionId: item.chatSessionId,
      chatSessionName: item.chatSessionName,
    });
    if (res.status) {
      Message.success(t('ai.rename.success'));
      queryAgentRecord(props.agentId); // 查询历史对话
    }
  };
  // 取消重命名
  const handleCancelRename = (item: any) => {
    const state = getRecordUIState(item.id);
    state.isEditing = false;
    item.chatSessionName = state.oldName;
  };

  // 监听新对话问答结束，刷新历史记录
  const { startWatching, stopWatching } = useSessionStorageWatcher(
    'xkAiIsSessionEnd',
    (newValue: string, oldValue: string) => {
      console.log(newValue, ' ', oldValue);
      if (newValue === 'true') {
        queryAgentRecord(props.agentId); // 查询历史对话
      }
    }
  );

  // 进入和退出页面都需要清空sessionStorage的当前选中记录信息
  onMounted(() => {
    showAIPage();
    clearSelectedRecordSession();
    sessionStorage.removeItem('xkAiIsSessionEnd');
    startWatching();
  });

  onUnmounted(() => {
    clearSelectedRecordSession();
    stopWatching();
  });
</script>

<style lang="less" scoped>
  .menuAi {
    width: 100%;
    height: 100%;
    display: flex;
    > div {
      padding: 20px;
    }
    .left {
      width: 260px;
      border-right: 1px solid #e5e6eb;
      overflow: auto;
      .arco-btn {
        border-radius: 8px;
      }
    }
    .right {
      flex: 1;
    }
  }

  .list {
    .agent-card {
      padding: 12px 0;
      min-height: 72px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #ededed;
      cursor: pointer;

      svg {
        margin-top: -1px;
      }

      .agent-text {
        flex: 1;
        margin-left: 9px;
        overflow: hidden;

        .title {
          margin-bottom: 4px;
          font-weight: 500;
          font-size: 16px;
          color: #1d2129;
          line-height: 24px;
        }
      }

      .count-box {
        padding: 0 6px;
        height: 18px;
        background: #fff3e8;
        border-radius: 18px;
        display: flex;
        align-items: center;
        span {
          font-size: 12px;
          font-weight: 500;
          color: #f99057;
          line-height: 12px;
        }
      }
    }

    .record-card {
      margin-top: 4px;
      padding: 8px;
      height: 60px;
      cursor: pointer;

      .time {
        font-size: 14px;
        color: #86909c;
        line-height: 22px;
        display: flex;
        justify-content: space-between;
      }

      .title {
        font-size: 16px;
        color: #1d2129;
        line-height: 22px;
      }
    }

    .record-card:first-of-type {
      margin-top: 0;
    }

    .record-card-select {
      border-radius: 8px;
      background-color: #e8f2ff;
    }
  }

  .record-list {
    height: 100%;
    overflow: auto;
    // margin-top: 4px;
  }

  .empty-record {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 129px 0 0;

    span {
      margin-top: 16px;
      font-size: 16px;
      color: #4e5969;
      line-height: 24px;
    }
  }

  .dialog-container {
    // background-color: red;
    height: 100%;
    width: 100%;
    // AI样式覆盖
    :deep(.chat-input-content) {
      .content-edit-box {
        border: none !important;
      }
    }
    :deep(.tiptap) {
      outline: none;
    }
    :deep(.foot-box-action .default-css) {
      border-radius: 26px !important;
    }
    :deep(.arco-list-bordered) {
      border: none;
    }
    :deep(.main-begin .main-view-main) {
      height: 0;
    }
    :deep(.reanoning-content li) {
      margin-left: 16px;
    }
    // footer换行，高度调整
    :deep(.foot-box-text) {
      height: 30px !important;
    }
  }
</style>
