export interface MarkerConfig {
  id?: string;
  size?: number[];
  icon?: string;
  name?: string;
  position?: number[];
}

export interface MarkerType {
  addMarker: (
    pageX: number,
    pageY: number,
    config?: MarkerConfig
  ) => Promise<any>;
  addMarkerBy3DPosition: (markers: MarkerConfig[]) => void;
  clearMarker: (ids?: string[]) => void;
  onMarkerClick: (func: (data: string) => void) => void;
}

export interface ViewerParams {
  elementId?: string;
  viewerType?: string;
  fileId?: string;
  renderPath?: string;
  toolBar?: any;
}

export interface ViewerType {
  elementId: string;
  marker: MarkerType;
  rander: () => Promise<void>;
  getModelId: () => string;
  getCamera: () => number[];
  setCamera: (position: number[]) => void;
  reRender: () => void;
  modelTreeReady: (func: () => void) => void;
  setEntitiesColor: (ids: string[], custom?: boolean) => void;
  clearEntitiesColor: (ids: string[]) => void;
  selectEntities: (id: string, isBase64: boolean) => void;
  gotoByIds: (ids: string[]) => void;
  gotoByComponentIds: (ids: string[]) => void;
  isolateEntities: (ids: string[]) => void;
  showAll: () => void;
  gotoByBBox: (bbox: []) => void;
  getEntitiesBBoxAsync: (ids: string[]) => any;
  toHome: () => void;
  getEntityChildrenIds: (id: string) => any;
  hyalineEntities: (ids: string[]) => any;
  searchEntityIdsByProps: (searchList: any[]) => Promise<any>;
  get2dViewInfo: () => any;
  entitySelectedEvent: (func: (data: any) => void) => void;
  entityDeselectedEvent: (func: any) => void;
  rotateModel: (data: any) => void;
  setEnableSelection: (value: any) => void;
  clearSelectedEntities: () => any;
  modelPointPicked: (func: () => void) => void;
  getSpecifiedComponentProperty: (value: any) => void;
}
