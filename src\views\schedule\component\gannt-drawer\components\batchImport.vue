<template>
  <a-modal
    :visible="visible"
    :closable="true"
    :mask-closable="false"
    width="420px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title> 批量导入 </template>
    <div class="import-body">
      <div class="import-row">
        <div class="name">请下载该模板进行填写</div>
        <a-button download type="outline" size="small" @click="handleDownload"
          >下载模板</a-button
        >
      </div>
      <div class="import-row">
        <div class="name">上传批量导入事项附件</div>
        <a-upload
          action="/"
          accept=".xlsx"
          :limit="1"
          :show-file-list="false"
          v-model:file-list="fileList"
        >
          <template #upload-button>
            <a-button type="outline" size="small">上传附件</a-button>
          </template>
        </a-upload>
      </div>
      <div v-show="fileList.length" class="import-file-list">
        <div class="import-file-item">
          <xlsxIcon />
          <span class="import-file-name">{{ fileList[0]?.name }}</span>
          <a-button
            type="text"
            size="mini"
            @click="removeFile()"
            style="margin-left: 8px"
          >
            <iconDelete />
          </a-button>
        </div>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, h } from 'vue';
  import { storeToRefs } from 'pinia';
  import { Message } from '@arco-design/web-vue';
  import { Notification, Button, Space } from '@arco-design/web-vue';
  import xlsxIcon from '@/assets/images/knowledge-base/excel.svg';
  import iconDelete from '@/assets/images/matter/delete.svg';
  import { importAgenda } from '@/views/create-schedule/api';

  const fileList = ref<any[]>([]);
  const emit = defineEmits(['update:visible', 'refresh']);
  const props = defineProps({
    projectId: {
      type: String,
      default: '',
    },
    schedulePanelId: {
      type: String,
      default: '',
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });
  async function handleOk() {
    try {
      // 提交逻辑
      if (fileList.value.length === 0) {
        Message.warning('请上传文件');
        return;
      }
      const formData = new FormData();
      console.log(fileList.value[0], '1111111');
      formData.append('file', fileList.value[0].file);
      formData.append('projectId', props.projectId);
      formData.append('schedulePanelId', props.schedulePanelId);
      const res = await importAgenda(formData);
      if (res.status) {
        if (res.data?.errorMessage) {
          let notificationInstance: ReturnType<
            typeof Notification.error
          > | null = null;
          notificationInstance = Notification.error({
            title: '导入失败',
            content: res.data?.errorMessage, // 支持 HTML 换行
            duration: 0,
            closable: true,
            footer: () =>
              h(
                Space,
                {},
                {
                  default: () => [
                    h(
                      Button,
                      {
                        type: 'primary',
                        size: 'small',
                        onClick: () =>
                          notificationInstance && notificationInstance.close(),
                      },
                      { default: () => '确定' }
                    ),
                  ],
                }
              ),
          });
        } else if (res.data?.successCount >= 0 && res.data?.failedCount === 0) {
          Message.success('导入成功');
          emit('update:visible', false);
          emit('refresh');
          fileList.value = [];
        } else {
          emit('update:visible', false);
          emit('refresh');
          fileList.value = [];
          let notificationInstance: ReturnType<
            typeof Notification.warning
          > | null = null;
          notificationInstance = Notification.warning({
            title: `导入成功${res.data.successCount}条，导入失败${res.data.failedCount}条，失败原因如下：`,
            content: () =>
              h(
                'div',
                {},
                [res.data.warning, res.data?.repetition]
                  .filter(Boolean)
                  .map((item) => h('div', item))
              ),
            duration: 0,
            closable: true,
            footer: () =>
              h(
                Space,
                {},
                {
                  default: () => [
                    h(
                      Button,
                      {
                        type: 'primary',
                        size: 'small',
                        onClick: () =>
                          notificationInstance && notificationInstance.close(),
                      },
                      { default: () => '确定' }
                    ),
                  ],
                }
              ),
          });
        }
      }
    } catch (error) {
      console.log('导入失败');
    }
  }
  function handleCancel() {
    emit('update:visible', false);
    fileList.value = [];
  }
  function handleDownload() {
    const url = '/事项导入模板.xlsx'; // 这里使用相对路径，假设文件在 public 文件夹中
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', '事项导入模板.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link); // 移除链接元素
  }
  function removeFile() {
    fileList.value = [];
  }
</script>

<style scoped>
  .import-body {
    padding: 8px 0 0 0;
  }
  .import-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .name {
      margin-right: 8px;
    }
  }
  .import-file-list {
    margin-top: 8px;
  }
  .import-file-item {
    display: flex;
    align-items: center;
    background: #f7f8fa;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
  }
  .import-file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  :deep(.arco-upload-wrapper) {
    width: 30% !important;
  }
</style>
