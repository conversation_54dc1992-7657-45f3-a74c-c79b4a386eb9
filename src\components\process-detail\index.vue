<template>
  <a-modal
    :visible="visible"
    :width="800"
    :mask-closable="false"
    draggable
    :esc-to-close="false"
    @cancel="cancel"
    @ok="cancel"
  >
    <template #title> {{ $t('task.process-detail') }} </template>
    <div class="content">
      <div class="task-info">
        <div class="title">
          <div class="text">
            <img
              src="@/assets/images/check/<EMAIL>"
              alt=""
              style="width: 17px; height: 17px"
            />
            <span class="text-font">{{ $t('task.process-detail') }}</span>
          </div>
        </div>
        <div class="info-content">
          <div class="info-row">
            <span class="info-row-label">{{ $t('task.name') }}：</span>
            <span class="info-row-text">{{ taskDetail?.formName || '' }}</span>
          </div>
          <div class="info-row">
            <span class="info-row-label"
              >{{ $t('task.process-templates') }}：</span
            >
            <span class="info-row-text">{{
              taskDetail?.formExtObj?.processName || ''
            }}</span>
          </div>
          <div class="info-row">
            <span class="info-row-label">{{ $t('task.file') }}：</span>
            <div class="change-logo">
              <icon-menu
                style="margin-right: 5px; cursor: pointer"
                :style="`color:${folderType == 0 ? '#0e80e7' : ''}`"
                size="20"
                @click="changeFolderType(0)"
              />
              <icon-folder
                style="cursor: pointer"
                :style="`color:${folderType == 1 ? '#0e80e7' : ''}`"
                size="20"
                @click="changeFolderType(1)"
              />
            </div>
            <div
              v-show="folderType === 1"
              style="display: flex; flex-direction: column"
            >
              <FolderNav
                ref="folderNavRef"
                :nav-info="navInfo"
                @click-nav="clickNav"
              ></FolderNav>
              <StructureFolder :file="files" @file-change="fileChange" />
            </div>
            <div
              v-if="formInfo?.fileList?.length && folderType === 0"
              :class="`${
                fileListExpandStatus ? 'file-list-expand' : ''
              } file-list`"
            >
              <div
                v-for="(item, index) in formInfo?.fileList"
                :key="item.fileId"
                :class="`${index % 3 === 0 ? 'file-item-0' : ''} file-item`"
              >
                <file-image
                  :file-name="item.name"
                  :is-file="item.isFileOrFolder === 0 ? false : true"
                  style="
                    width: 40px;
                    height: 40px;
                    border-radius: 4px;
                    margin-top: 6px;
                    margin-left: 12px;
                  "
                />
                <div class="file-text">
                  <div class="file-name" @click="modelView(item)">
                    {{ item.name }}</div
                  >
                  <div style="position: relative">
                    <span class="file-version">V{{ item.version }}</span>
                    <span
                      v-if="!modelElse.includes(item.name.split('.')[1])"
                      class="icon-delete"
                      @click="downloadFile(item)"
                      ><icon-download size="14"
                    /></span>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="fileListExpandVisible && folderType === 0"
              class="expend-btn"
              @click="changeListExpand"
              ><icon-double-down v-if="!fileListExpandStatus" />
              <icon-double-up v-else /><span
                v-if="!fileListExpandStatus"
                style="
                  font-size: 12px;
                  display: inline-block;
                  margin-left: 5px;
                  line-height: 20px;
                "
                >{{ $t('task.view-more') }}</span
              ><span
                v-else
                style="
                  font-size: 12px;
                  display: inline-block;
                  margin-left: 5px;
                  line-height: 20px;
                "
                >{{ $t('task.pack-up') }}</span
              ></div
            >
          </div>
        </div>
      </div>
      <a-divider style="margin-top: 24px"></a-divider>
      <div class="process-history">
        <div class="title">
          <div class="text">
            <img
              src="@/assets/images/check/<EMAIL>"
              alt=""
              style="width: 17px; height: 17px"
            />
            <span class="text-font">{{ $t('task.processing-records') }}</span>

            <!-- v-if="taskDetail[taskDetail.length - 1]?.taskState === 0" -->
            <a-button
              v-if="
                processHistoryList[processHistoryList.length - 1]?.taskState ===
                  '0' &&
                defaultKey &&
                showType === 0
              "
              type="primary"
              class="uploadBtn"
              size="mini"
              @click="showUploadDialog"
            >
              {{ $t('task.upload-attachment') }}
            </a-button>
          </div>
          <!--          <div class="file-count">-->
          <!--            <span>总计：{{ fileCounts || 0 }} 个文件</span>-->
          <!--            <a-button type="text" @click="treeFolderVisible = true">-->
          <!--              添加文件-->
          <!--            </a-button>-->
          <!--          </div>-->
        </div>
        <div class="table-wrap">
          <a-table :data="processHistoryList" :pagination="false">
            <template #columns>
              <a-table-column
                :title="$t('task.index')"
                align="center"
                :width="60"
                :ellipsis="true"
                :tooltip="true"
              >
                <template #cell="{ rowIndex }">
                  {{ rowIndex + 1 }}
                </template>
              </a-table-column>
              <a-table-column
                :title="$t('task.node-name')"
                align="center"
                data-index="taskName"
                :width="100"
                :ellipsis="true"
                :tooltip="true"
              ></a-table-column>
              <a-table-column
                :title="$t('task.operator')"
                data-index="id"
                align="center"
                :width="80"
                :ellipsis="true"
                :tooltip="true"
              >
                <template #cell="{ record }">
                  <span>{{
                    record.assigneeName || record.assignee || ''
                  }}</span>
                </template>
              </a-table-column>
              <a-table-column
                :title="$t('task.approval-result')"
                data-index="processState"
                align="center"
                :width="90"
                :ellipsis="true"
                :tooltip="true"
              >
                <template #cell="{ record }">
                  <div class="">
                    <a-tag
                      :color="TaskApproveStateColor[record.approveState]"
                      >{{ record.approveStateName }}</a-tag
                    >
                  </div>
                </template>
              </a-table-column>
              <a-table-column
                :title="$t('task.approval-opinion')"
                data-index="comment"
                align="center"
                :width="90"
                :ellipsis="true"
                :tooltip="true"
              ></a-table-column>
              <a-table-column
                v-if="defaultKey"
                :title="$t('task.attachment-info')"
                data-index="files"
                align="center"
                :width="155"
                :ellipsis="true"
              >
                <template #cell="{ record }">
                  <!-- showType是审核状态 taskState==='0'是代办状态 -->
                  <a-space
                    v-if="showType === 0 && record.taskState === '0'"
                    wrap
                    type="primary"
                    size="mini"
                  >
                    <template v-if="record.attachFile.length">
                      <a-tag
                        v-for="item of record.attachFile"
                        :key="item"
                        :closable="showType === 0"
                        @click="downloadFile(item)"
                        @close="deleteFile(item)"
                      >
                        <a-tooltip :content="item.attachFileName || item.name">
                          <span class="attachment-info">
                            {{ item.attachFileName || item.name }}</span
                          >
                        </a-tooltip>
                      </a-tag>
                    </template>
                  </a-space>
                  <!-- 其他位置有文件展示文件名，没文件不展示 -->
                  <a-space
                    v-for="item of record.attachFile"
                    v-else
                    :key="item"
                    class="attachment"
                    :title="$t('task.click-to-download-the-file')"
                    @click="downloadFile(item)"
                  >
                    <a-tooltip :content="item.attachFileName">
                      <span style="width: 126px" class="attachment-info">{{
                        item.attachFileName
                      }}</span>
                    </a-tooltip>
                  </a-space>
                </template>
              </a-table-column>
              <a-table-column
                :title="$t('task.approval-time')"
                data-index="approveDate"
                align="center"
                :width="180"
                :ellipsis="true"
                :tooltip="true"
              ></a-table-column>
            </template>
          </a-table>
        </div>
      </div>
      <div v-if="showType === 0" class="check-comment">
        <span class="comment-title">{{ $t('task.approval-opinion') }}</span>
        <div class="comment-text">
          <a-textarea v-model="checkComment"></a-textarea>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="process-footer">
        <div 
          v-if="showType === 0 && (props.type === 'share' || props.type === 'deliver') && !showMarquee.includes(projectCode)" 
          class="process-footer-left"
        >
          <span class="process-footer-label">{{ $t('task.sync-push-to-jjt') }}</span>
          <a-checkbox v-model="pushToAuthor">{{ $t('task.submitter') }}</a-checkbox>
        </div>
        <div class="process-footer-right">
          <a-button
            v-if="showType === 0"
            :loading="refuseBtnLoading"
            :disabled="agreeBtnLoading"
            @click="refuseProcess"
            class="footer-btn"
          >{{ $t('task.reject') }}</a-button>
          <a-button
            v-if="showType === 0"
            :loading="agreeBtnLoading"
            :disabled="refuseBtnLoading"
            type="primary"
            @click="agreeProcess"
            class="footer-btn"
          >{{ $t('task.agree') }}</a-button>
          <a-button v-if="showType === 1" type="primary" @click="cancel" class="footer-btn">{{
            $t('task.determine')
          }}</a-button>
        </div>
      </div>
    </template>
  </a-modal>
  <uploadTheSpecifiedFolder
    v-model:visible="uploadModalvisable"
    @upload-single-success="uploadSingleSuccessHandle"
    @upload-complete="uploadComplete"
    @select-complete="selectComplete"
  />

  <!-- 图片预览 -->
  <imgViewer v-if="imgViewModal.visible" :view-modal="imgViewModal"></imgViewer>
</template>

<script lang="ts" setup>
  import {
    defineEmits,
    defineProps,
    watch,
    ref,
    computed,
    inject,
    Ref,
    reactive,
  } from 'vue';
  import {
    getProcessHistory,
    passProcess,
    rejectProcess,
    removeAttach,
    getFormInfo,
    deleteAttachFile,
    getDetailTree,
    getSharedOrDeliveryTree,
  } from '@/api/process';
  import { Message } from '@arco-design/web-vue';
  import { TaskApproveStateColor } from '@/directionary/process';
  import { fileDownload } from '@/api/file';
  import { download } from '@/utils/file';
  import FileImage from '@/views/projectSpace/home/<USER>/image-file.vue';
  import { modelElse } from '@/utils/BIM-Engine/XBase/format';
  import uploadTheSpecifiedFolder from '@/components/uploadTheSpecifiedFolder/index.vue';
  import { useI18n } from 'vue-i18n';
  import modelViewBim from '@/utils/common/view';
  import { useRoute } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import imgViewer from '@/components/imgView/index.vue';
  import StructureFolder from '@/components/structure-folder/index.vue';
  import FolderNav from '@/components/structure-folder/folder-nav.vue';
  import useUploadFileStore from '@/store/modules/upload-file/index';
  import { addMergaFile } from '@/api/upload-file';
  import { last } from 'lodash';
  import imgJson from '@/config/imgType.json';
  import { queryProjectDetail } from './api';


  const imgViewModal = reactive({
    visible: false,
    title: '',
    fileToken: ' ',
  });

  const route = useRoute();

  const uploadFileStore = useUploadFileStore();
  const { fileArr } = storeToRefs(uploadFileStore);
  const selectFolderObj = ref({});

  const { t } = useI18n();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    taskDetail: {
      type: Object,
      default() {
        return {};
      },
    },
    showType: {
      type: Number,
      default: 0, // 0 审批 1 查看
    },
    formConfig: {
      type: Object,
      default() {
        return {};
      },
    },
    type: {
      type: String,
      required: true,
    },
  });
  const defaultKey = inject('defaultKey') as Ref; // 接受
  const emits = defineEmits(['update:visible', 'agreed', 'refused']);
  const cancel = () => {
    emits('update:visible', false);
  };

  const processHistoryList = ref([]);
  // 获取流程历史处理记录
  const getHistory = (): string => {
    const params = {
      bizId: props.taskDetail?.formBizId || '',
    };
    if (!params.bizId) {
      return '';
    }
    getProcessHistory(params).then((res: any) => {
      if (res.code === 8000000) {
        processHistoryList.value = res.data || [];
      }
    });
    return '';
  };

  const refuseBtnLoading = ref(false);
  const agreeBtnLoading = ref(false);
  const checkComment = ref('');
  const pushToAuthor = ref(false);
  // 同意 流程
  const agreeProcess = (): string => {
    const params: any = {
      id: props.taskDetail.formBizId,
      taskId: props.taskDetail.taskId,
      comment: checkComment.value,
    };
    if (props.type === 'share' || props.type === 'deliver') {
      params.pushToAuthor = pushToAuthor.value;
    }
    if (!props.formConfig.passUrl) {
      Message.error(t('task.missing-form-configuration'));
      return '';
    }
    agreeBtnLoading.value = true;
    passProcess(props.formConfig.passUrl, params)
      .then((res: any) => {
        if (res.code === 8000000) {
          Message.success(res.message);
          emits('agreed', true);
          cancel();
        }
        agreeBtnLoading.value = false;
      })
      .catch((e) => {
        if (e) {
          agreeBtnLoading.value = false;
        }
      });
    // cancel();
    return '';
  };
  // 拒绝流程
  const refuseProcess = (): string => {
    const params: any = {
      id: props.taskDetail.formBizId,
      taskId: props.taskDetail.taskId,
      comment: checkComment.value,
    };
    if (props.type === 'share' || props.type === 'deliver') {
      params.pushToAuthor = pushToAuthor.value;
    }
    if (!params.comment) {
      Message.error(t('task.please-enter-approval-opinion'));
      return '';
    }
    if (!props.formConfig.passUrl) {
      Message.error(t('task.missing-form-configuration'));
      return '';
    }
    refuseBtnLoading.value = true;
    rejectProcess(props.formConfig.refuseUrl, params)
      .then(async (res: any) => {
        if (res.code === 8000000) {
          // const removeParams = {
          //   processInstanceId: props.taskDetail?.processInstanceId,
          // };
          const removeParams = new URLSearchParams();
          removeParams.append(
            'processInstanceId',
            props.taskDetail?.processInstanceId
          );
          await removeAttach(removeParams);
          Message.success(res.message);
          emits('refused', true);
          cancel();
        }
        refuseBtnLoading.value = false;
      })
      .catch((e) => {
        if (e) {
          refuseBtnLoading.value = false;
        }
      });
    return '';
  };

  // 查询交付 审阅 共享 表单的详情
  const files = ref({});
  const navInfo = ref({});
  const formInfo = ref<any>({});
  const getFormDetail = (): string => {
    // 初始化 params 对象
    const params: any = {
      id: props.taskDetail.formBizId,
    };

    if (!params.id) {
      Message.error(t('task.unable-get-form-details'));
      return '';
    }
    getFormInfo(props.formConfig.infoUrl, params).then((res: any) => {
      if (res.status) {
        formInfo.value = res.data || {};
        // 根据不同的 formKey 设置 params 的属性
        switch (formInfo.value.formKey) {
          case 'cde_collaborate_6923afa9cc27e806c661132f23921009':
            params.nameType = 'shared';
            break;
          case 'cde_delivery_2bc617d2ae38589f5a26efe29e1cb8e2':
            params.nameType = 'publish';
            break;
          default:
            params.flag = 'review';
            break;
        }
        // 根据不同的 formKey 调用相应的函数并处理响应
        const fetchDetails =
          formInfo.value.formKey ===
          'cde_review_c2ea22fdc8f9dc71e6beede1302d5d1f'
            ? getDetailTree(params)
            : getSharedOrDeliveryTree(params);
        fetchDetails.then((res2) => {
          // 更新人工数据结构
          const data = {
            children: res2.data,
            files: [],
            name: '全部文件',
          };
          files.value = data;
          navInfo.value = data;
        });
      }
    });

    return '';
  };

  const downloadFile = (file: any) => {
    file.name = file.attachFileName ? file.attachFileName : file.name;
    if (file.fileToken) {
      fileDownload(file).then((res: any) => {
        if (res && res.data?.size) {
          download(file, res.data);
        }
      });
    } else {
      Message.error(t('task.fileToken-missing'));
    }
  };

  const fileListExpandVisible = computed((): boolean => {
    let result = false;
    const len = formInfo?.value?.fileList?.length || 0;
    if (len > 6) {
      result = true;
    }
    return result;
  });
  const fileListExpandStatus = ref(false);
  const changeListExpand = () => {
    if (fileListExpandStatus.value) {
      fileListExpandStatus.value = false;
    } else {
      fileListExpandStatus.value = true;
    }
  };
  // 添加文件上传
  const uploadModalvisable = ref(false);
  const uploadFiles = ref<any>([]);
  interface fileInterfaces {
    name: string;
    url: string;
    fileId: string;
  }
  const showUploadDialog = () => {
    uploadModalvisable.value = true;
  };
  const uploadSingleSuccessHandle = async (val: any) => {
    const paramsBase = {
      fileToken: val.fileToken,
      name: val.name,
      processInstanceId: props.taskDetail?.processInstanceId,
      projectId: route.params?.projectId,
      size: val.size,
      taskId: props.taskDetail?.taskId,
      type: 'attach',
    };

    let params;
    if (
      formInfo.value.formKey ===
      'cde_collaborate_6923afa9cc27e806c661132f23921009'
    ) {
      params = {
        ...paramsBase,
        collaborateId: props.taskDetail?.formBizId,
      };
    } else {
      params = {
        ...paramsBase,
        deliveryId: props.taskDetail?.formBizId,
      };
    }
    await addMergaFile(params);
  };

  const uploadComplete = () => {
    const hisObj = processHistoryList.value;
    getHistory();
    processHistoryList.value = hisObj;

    uploadModalvisable.value = false;
  };

  const selectComplete = (selectFolderObjA: any) => {
    selectFolderObj.value = { ...selectFolderObjA };
    if (fileArr.value.length > 0) {
      uploadFileStore.projectId = route.params.projectId as string;
      uploadFileStore.handleUploadFile(selectFolderObj.value, 0);
    }
  };

  const deleteFile = async (file: fileInterfaces) => {
    try {
      const params = {
        fileId: file.fileId,
      };
      await deleteAttachFile(params);
      uploadFiles.value = uploadFiles.value.filter(
        (item: fileInterfaces) => item.name !== file.name
      );
      Message.success(t('task.delete-file-success'));
    } catch (e) {
      // Message.error(e.data);
    }
  };

  const init = () => {
    checkComment.value = '';
    processHistoryList.value = [];
    fileListExpandStatus.value = false;
    formInfo.value = {};
    getFormDetail();
    getHistory();
  };
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        init();
        pushToAuthor.value = false;
      }
    }
  );

  const modelView = async (record: any) => {
    const type = last(record.name.split('.')) as string;
    const isImgType: boolean = imgJson.includes(type);
    // 图片预览
    if (isImgType) {
      imgViewModal.visible = true;
      imgViewModal.title = record.name || '';
      imgViewModal.fileToken = record.fileToken || '';
    } else {
      const needParams = {
        version: record.version,
        isUpdateStatus: true,
      };
      modelViewBim(record, route.params.projectId as string, needParams);
    }
  };

  const folderNavRef = ref<any>(null);
  const fileChange = (file) => {
    console.log(file, 'file-----------------');
    files.value = file;
    folderNavRef.value.addNav(file);
  };
  const clickNav = (folder) => {
    files.value = folder;
  };
  const folderType = ref(0);
  const changeFolderType = (type) => {
    folderType.value = type;
  };
  // 添加projectCode变量和showMarquee数组
  const projectCode = ref('');
  const showMarquee = ['CDex001'];
  
  // 获取项目详情数据
  const getProjectDetail = async () => {
    const projectTempId = route.params.projectId as string;
    if (!projectTempId || projectTempId === 'undefined') return;
    
    try {
      const { data, status } = await queryProjectDetail({
        id: projectTempId,
      });
      
      if (status) {
        projectCode.value = data.code;
      }
    } catch (error) {
      console.error('获取项目详情失败:', error);
    }
  };
  
  // 获取项目详情
    getProjectDetail();
</script>

<style scoped lang="less">
  .arco-upload-wrapper {
    width: auto;
  }
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 14px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .uploadBtn {
    margin-left: 20px;
  }
  @keyframes fileListExpand {
    0% {
      max-height: 120px;
    }
    50% {
      max-height: 160px;
    }
    100% {
      max-height: 200px;
    }
  }
  .info-content {
    .info-row {
      margin-top: 8px;
      display: flex;
      position: relative;
      .info-row-label {
        display: inline-block;
        width: 80px;
        text-align: right;
        color: var(--color-text-3);
        white-space: nowrap;
      }
      .info-row-text {
        display: inline-block;
        margin-left: 8px;
      }
      .file-list {
        display: flex;
        flex-wrap: wrap;
        //border: 1px solid red;
        max-width: 650px;
        max-height: 120px;
        overflow: hidden;
        position: relative;
      }
      .file-list-expand {
        max-height: fit-content;
        animation: fileListExpand 0.2s ease-out;
      }
      .expend-btn {
        position: absolute;
        height: 30px;
        //border: 1px solid red;
        display: flex;
        align-content: center;
        align-items: center;
        bottom: -28px;
        left: calc(50% - 20px);
        z-index: 99;
        font-size: 20px;
        color: rgb(var(--arcoblue-3));
        cursor: pointer;
        &:hover {
          color: rgb(var(--arcoblue-6));
        }
      }

      .file-item {
        margin-top: 4px;
        height: 56px;
        //border-bottom: 1px solid var(--color-border);
        position: relative;
        width: 195px;
        background: var(--color-fill-2);
        border-radius: 6px;
        margin-left: 16px;
        padding-top: 3px;
        &:first-child {
          margin-left: 8px;
        }
        //line-height: 56px;
        //border: 1px solid red;
        .file-text {
          float: right;
          width: calc(100% - 70px);
          //border: 1px solid red;
          margin-right: 10px;
          margin-top: 2px;
          .file-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 22px;
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN, serif;
            font-weight: 400;
            line-height: 22px;
            cursor: pointer;
            color: rgb(var(--primary-6));
          }
          .file-version {
            //height: 20px;
            background: #e8fffb;
            border-radius: 4px;
            opacity: 1;
            border: 1px solid #0fc6c2;
            //padding: 4px;
            padding-left: 6px;
            padding-right: 6px;
            color: #0fc6c2;
            font-size: 12px;
            margin-top: 4px;
            line-height: 20px;
          }
        }
        .icon-delete {
          position: absolute;
          right: 0;
          top: 0px;
          cursor: pointer;
        }
      }
      .file-item-0 {
        margin-left: 8px;
      }
    }
  }
  .table-wrap {
    margin-top: 16px;
    .attachment-info {
      width: 95px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .check-comment {
    display: flex;
    margin-top: 16px;
    .comment-title {
      display: inline-block;
      width: 80px;
      text-align: left;
    }
    .comment-text {
      flex: 1;
      //margin-left: 12px;
    }
  }
  .attachment {
    font-size: 13px;
    color: #351cdd;
    cursor: pointer;
    margin-right: 5px;
    display: flex;
    flex-wrap: wrap;
  }
  .nav-top {
    display: flex;
  }
  .change-logo {
    position: absolute;
    right: 0;
  }

  .process-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .process-footer-left {
    display: flex;
    align-items: center;
  }
  .process-footer-label {
    margin-right: 8px;
  }
  .process-footer-right {
    display: flex;
    align-items: center;
  }
  .footer-btn {
    margin-right: 12px;
  }
  .footer-btn:last-child {
    margin-right: 0;
  }
</style>
