<template>
  <a-modal
    :visible="visible"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="cancel"
  >
    <template #title>
      {{ asmName || $t('model-collaboration.file') }}
    </template>
    <template #footer>
      <a-button @click="cancel">{{
        $t('model-collaboration.cancel')
      }}</a-button>
    </template>
    <div class="content">
      <a-table
        :columns="columns"
        :scroll="{ y: tableHeight }"
        :data="filesData"
        :pagination="PageConfigs"
        @page-change="pageChange"
        @page-size-change="pageSizeChange"
      >
        <template #name="{ record }">
          <file-image
            :file-name="record.name"
            :is-file="true"
            style="position: relative; margin-right: 8px; top: 5px"
          />

          <span
            style="color: rgb(var(--primary-6)); cursor: pointer"
            @click="modelView(record)"
            >{{ record.name }}</span
          >
        </template>
        <template #version="{ record }">
          {{ `V${record.version}` }}
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import { modelall } from '@/utils/BIM-Engine/XBase/format';
  import { Column } from '@/views/design/components/table-column';
  import { useI18n } from 'vue-i18n';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import { Message } from '@arco-design/web-vue';
  import getFileTypeStatus from '@/utils/BIM-Engine/XBase/utils/status-info';
  import modelViewBim from '@/utils/common/view';

  const { t } = useI18n();
  const route = useRoute();
  const router = useRouter();
  const tableHeight = ref(window.innerHeight - 400);
  const projectStore = usePrjPermissionStore();

  const columns = computed<Column[]>(() => {
    return [
      {
        title: t('model-collaboration.name'),
        dataIndex: 'name',
        slotName: 'name',
        width: 350,
        ellipsis: true,
        tooltip: true,
      },
      {
        title: t('model-collaboration.version'),
        dataIndex: 'version',
        slotName: 'version',
        align: 'center',
      },
      // {
      //   title: '更新者',
      //   dataIndex: 'updateUserName',
      // },
    ];
  });

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    asmName: {
      type: String,
      default: '',
    },
    filesData: {
      type: Array<any>,
      default: () => {
        return [];
      },
    },
  });

  // 分页
  const PageConfigs: any = reactive({
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
  });
  const pageChange = (val: number) => {
    PageConfigs.current = val;
  };
  const pageSizeChange = (val: number) => {
    PageConfigs.pageSize = val;
  };

  const modelView = async (record: any) => {
    const needParams = {
      version: 1,
    };
    modelViewBim(record, route.params.projectId as string, needParams);
    // const type = record.name.split('.')[record.name.split('.').length - 1];
    // // if (modelall.includes(type))
    // //   modelViewer({
    // //     record,
    // //     projectId: route.params.projectId as string,
    // //     version: 1,
    // //   });
    // // 大象云预览
    // if (projectStore.modelEngine === 'XBase') {
    //   if (record.status === 0 || (record.status === 3 && type === 'dwg')) {
    //     const url = router.resolve({
    //       path: `/bim-view`,
    //       query: {
    //         idStr: record.fileId,
    //         projectId: route.params.projectId as string,
    //         version: 1,
    //       },
    //     }).href;
    //     window.open(url);
    //   } else {
    //     const statusInfo = getFileTypeStatus(type, record.status.toString());
    //     Message.error(statusInfo);
    //   }
    // }
  };
  const emits = defineEmits(['update:visible', 'refresh']);
  const cancel = () => {
    emits('update:visible', false);
  };
</script>

<style scoped lang="less">
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .file-list-wrap {
    margin-top: 16px;
  }
</style>
