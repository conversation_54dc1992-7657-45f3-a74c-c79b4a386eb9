import { getXBaseToken } from '@/utils/auth';
import axios from 'axios';

export interface mergeParams {
  pageNo: number;
  pageSize: number;
}

export interface fileData {
  createDate: string;
  fileId: string;
  fileToken: string;
  folderId: string;
  graphicEngineInfo: string;
  id: string;
  isFileOrFolder: number;
  key: string;
  name: string;
  projectName: string;
  relationFileId: string;
  title: string;
  type: string;
  updateDate: string;
  version: number;
}

// 查询列表-分页
export function clashList(params: any) {
  return axios.get('/api/open/v1/clash/list', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

export function queryClashList(params: any) {
  return axios.get('/cde-collaboration/file/list', {
    params,
  });
}

export function getFileInfoById(id?: string) {
  return axios.get('/cde-collaboration/file/detail', {
    params: {
      id,
    },
  });
}

// 碰撞检测走大象云接口
export const createXBaseModelClash = (data: any) => {
  return axios({
    method: 'POST',
    url: '/api/open/v1/clash/clash',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
};
// 碰撞检测后端接口
export const createXBaseModelClashNew = (data: any) => {
  return axios({
    method: 'POST',
    url: '/cde-collaboration/xbase/clash/clash',
    data,
  });
};

export function GetXBaseSemanticInfo(params: any) {
  return axios.get('/api/open/v1/semantic-model/info', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

export function GetXBaseModelCategory(params: any) {
  return axios.get('/api/open/v1/clash/model-category', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

export function GetXBaseAssemblyChildren(params: any) {
  return axios.get('/api/open/v1/semantic-model/assembly/children', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

export function GetXBaseModeltree(params: any) {
  return axios.get(' /api/open/v1/semantic-model/modeltree', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}

// CDE创建碰撞检测
export function addMergaCrashCheck(data: any) {
  return axios.post('/cde-collaboration/file/crash-check', data);
}

export function queryTeamList(params: any) {
  return axios.get('/cde-collaboration/team/list', {
    params,
  });
}

// 文件删除
export function deleteFile(data: any) {
  return axios.post('/cde-collaboration/file/batch-delete/file', data);
}
