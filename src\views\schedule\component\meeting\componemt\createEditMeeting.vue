<template>
  <div class="meeting-content" :style="heightStyle">
    <a-form
      ref="formRef"
      :model="form"
      layout="vertical"
      class="form-style"
      :disabled="props.type === 'view' || hasNoEditPermission"
    >
      <a-row>
        <a-col :span="colSpan">
          <a-form-item
            field="planStartTime"
            :label="$t('schedule.meeting.startTime')"
            :validate-trigger="['change', 'blur']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.startTime.placeholder'),
              },
            ]"
          >
            <a-date-picker
              v-model="form.planStartTime"
              show-time
              format="YYYY-MM-DD HH:mm"
              :time-picker-props="{
                step: {
                  hour: 1,
                  minute: 5,
                },
              }"
              :disable="props.type === 'view' ? true : false"
              :placeholder="t('schedule.meeting.startTime.placeholder')"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="colSpan">
          <a-form-item
            field="planEndTime"
            :label="t('schedule.meeting.endTime')"
            :validate-trigger="['change', 'blur']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.endTime.placeholder'),
              },
            ]"
          >
            <a-date-picker
              v-model="form.planEndTime"
              :disabled-date="disabledEndDate"
              :disabled-time="disabledEndTime"
              show-time
              format="YYYY-MM-DD HH:mm"
              :time-picker-props="{
                step: {
                  hour: 1,
                  minute: 5,
                },
              }"
              :disable="props.type === 'view' ? true : false"
              :placeholder="$t('schedule.meeting.endTime.placeholder')"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="colSpan">
          <a-form-item
            v-if="globalMode === 'work' && !form.projectId"
            field="organizer"
            :label="$t('schedule.meeting.organizer')"
            :validate-trigger="['change', 'blur']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.organizer.placeholder'),
              },
            ]"
          >
            <a-input-tag
              v-model="form.organizer"
              :placeholder="$t('schedule.meeting.organizer.placeholder')"
              @focus="getUserFocus('organizer')"
              ><template #prefix> <icon-user-add /> </template
            ></a-input-tag>
          </a-form-item>
          <!-- 项目空间下选择组织人 -->
          <a-form-item
            v-else
            field="organizer"
            :label="$t('schedule.meeting.organizer')"
            :validate-trigger="['change', 'blur']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.organizer.placeholder'),
              },
            ]"
          >
            <a-select
              v-model="form.organizer"
              allow-search
              :placeholder="$t('schedule.meeting.organizer.placeholder')"
              @change="handleOrganizerChange"
            >
              <a-option
                v-for="item in projectUserList"
                :key="item.username"
                :value="item.username"
              >
                {{ item.name }}
              </a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="colSpanLong">
          <a-form-item
            v-if="globalMode === 'work' && !form.projectId"
            field="participants"
            :label="$t('schedule.meeting.participant')"
            :validate-trigger="['change', 'blur', 'select']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.participant.placeholder'),
              },
            ]"
          >
            <a-tooltip
              :disabled="!participants.userFullname"
              :content="participants.userFullname || undefined"
            >
              <a-input-tag
                v-model="form.participants"
                :max-tag-count="props.type === 'view' ? 6 : 1"
                :placeholder="$t('schedule.meeting.participant.placeholder')"
                @focus="getUserFocus('participants')"
                ><template #prefix> <icon-user-add /> </template
              ></a-input-tag>
            </a-tooltip>
            <!-- <a-input-tag
              v-else
              v-model="form.participants"
              :max-tag-count="props.type === 'view' ? 6 : 1"
              :placeholder="$t('schedule.meeting.participant.placeholder')"
              @focus="getUserFocus('participants')"
              ><template #prefix> <icon-user-add /> </template
            ></a-input-tag> -->
          </a-form-item>
          <a-form-item
            v-else
            field="participants"
            :label="$t('schedule.meeting.participant')"
            :validate-trigger="['change', 'blur', 'select']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.participant.placeholder'),
              },
            ]"
          >
            <a-tooltip
              :disabled="!participants.userFullname"
              :content="participants.userFullname || undefined"
            >
              <a-select
                v-model="form.participants"
                multiple
                allow-search
                :max-tag-count="1"
                :placeholder="$t('schedule.meeting.participant.placeholder')"
                @change="handleParticipantsChange"
              >
                <a-option
                  v-for="item in projectUserList"
                  :key="item.username"
                  :value="item.username"
                >
                  {{ item.name }}
                </a-option>
              </a-select>
            </a-tooltip>
            <!-- <a-select
              v-else
              v-model="form.participants"
              multiple
              allow-search
              :max-tag-count="1"
              :placeholder="$t('schedule.meeting.participant.placeholder')"
              @change="handleParticipantsChange"
            >
              <a-option
                v-for="item in projectUserList"
                :key="item.username"
                :value="item.username"
              >
                {{ item.name }}
              </a-option>
            </a-select> -->
          </a-form-item>
        </a-col>
        <a-col
          v-if="
            (globalMode === 'work' && form.projectId) ||
            globalMode === 'project'
          "
          :span="8"
        >
          <a-form-item
            field="teamId"
            :label="$t('schedule.meeting.team')"
            :validate-trigger="['change', 'blur']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.team.placeholder'),
              },
            ]"
          >
            <a-select
              v-model="form.teamId"
              allow-search
              allow-clear
              :placeholder="$t('schedule.meeting.team.placeholder')"
            >
              <a-option
                v-for="item in teamList"
                :key="item.id"
                :value="item.id"
              >
                {{ i18TeamName(item) }}
              </a-option>
            </a-select>
          </a-form-item></a-col
        >
        <a-col :span="colSpan">
          <a-form-item
            field="meetingFormat"
            :label="$t('schedule.meeting.type')"
            :validate-trigger="['change', 'blur']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.type.placeholder'),
              },
            ]"
          >
            <a-select
              v-model="form.meetingFormat"
              :placeholder="$t('schedule.meeting.type.placeholder')"
            >
              <a-option
                v-for="(item, index) in meetingWay"
                :key="index"
                :value="item.id"
              >
                {{ $t(item.label) }}
              </a-option>
            </a-select></a-form-item
          ></a-col
        >
        <a-col :span="colSpan"
          ><a-form-item
            class="meeting-location"
            field="location"
            :label="$t('schedule.meeting.location')"
            :validate-trigger="['change', 'blur']"
            :rules="[
              {
                required: false,
                message: $t('schedule.meeting.location.placeholder'),
              },
            ]"
          >
            <a-input
              v-model="form.location"
              :placeholder="
                props.type === 'view' || hasNoEditPermission
                  ? ''
                  : $t('schedule.meeting.location.placeholder')
              "
            ></a-input> </a-form-item
        ></a-col>
        <a-col v-if="type !== 'view'" :span="colSpan"
          ><a-form-item
            field="repeatType"
            :label="$t('schedule.meeting.repeat')"
            :validate-trigger="['change', 'blur', 'select']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.repeat.placeholder'),
              },
            ]"
          >
            <a-select
              v-model="form.repeatType"
              :placeholder="$t('schedule.meeting.repeat.placeholder')"
              @change="changeRepeatType"
            >
              <a-option
                v-for="item in repeatedOption"
                :key="item.id"
                :value="item.id"
              >
                {{ $t(item.label) }}
              </a-option>
            </a-select>
          </a-form-item></a-col
        >
        <!-- 修改重复逻辑有问题 暂时隐藏 -->
        <!-- 重复方式为重复得则展示结束时间 -->
        <a-col v-if="form.repeatType !== 0" :span="colSpan"
          ><a-form-item
            field="repeatEndTime"
            :label="$t('schedule.meeting.endAt')"
            :validate-trigger="['change', 'blur', 'select']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.endAt.placeholder'),
              },
            ]"
          >
            <a-date-picker
              v-model="form.repeatEndTime"
              show-time
              format="YYYY-MM-DD HH:mm"
              style="width: 100%"
            /> </a-form-item
        ></a-col>
        <!-- 编辑情况下如果会议创建人不是当前用户则不展示 -->
        <a-col
          v-if="
            !form.projectId &&
            (type === 'edit'
              ? propsCopyData.createBy === userStore.username
              : type === 'new')
          "
          :span="colSpan"
          ><a-form-item
            field="schedulePanelId"
            :label="$t('schedule.meeting.calendar')"
            :validate-trigger="['change', 'blur']"
            :rules="[
              {
                required: true,
                message: $t('schedule.meeting.calendar.placeholder'),
              },
            ]"
          >
            <a-select
              v-model="form.schedulePanelId"
              :placeholder="$t('schedule.meeting.calendar.placeholder')"
            >
              <a-option
                v-for="item in calendarOptionList"
                :key="item.id"
                :value="item.id"
              >
                {{ item.panelName }}
              </a-option>
            </a-select>
          </a-form-item></a-col
        >
      </a-row>
    </a-form>

    <!-- 描述 -->
    <div class="described">
      <div class="block">
        <customIconDescribe class="describe-icon" />
        <span class="block-span"> {{ $t('schedule.description') }}</span>
      </div>

      <a-textarea
        v-model="form.content"
        :disabled="props.type === 'view' || hasNoEditPermission"
        :placeholder="
          props.type === 'view' || hasNoEditPermission
            ? ''
            : $t('schedule.placeholder')
        "
        style="margin-bottom: 20px"
      />
    </div>

    <!-- 附件 -->
    <div class="annex">
      <div class="block">
        <customIconAttachment class="describe-icon" />
        <span class="block-span"> {{ $t('schedule.attachment') }}</span>
      </div>
      <div v-if="hasFileEditPermission" style="margin: 0 0 20px 0">
        <a-button type="text" class="file-upload" @click="uploadHandle(11)">{{
          $t('schedule.uploadAttachment')
        }}</a-button>
      </div>
      <div v-if="annexList.length && annexList[0]?.fileName" class="fileList">
        <a-row v-for="item in annexList" :key="item.id">
          <a-col :span="19">
            <file-image
              :file-name="item.name || item.fileName"
              :is-file="true"
            />
            <span class="fileName" @click="wpsView(item, 'preview')">{{
              item.fileName
            }}</span>
          </a-col>
          <a-col :span="5" align="right">
            <a-space>
              <a-tooltip
                v-if="route.params.projectId"
                :content="$t('calendar.save-as')"
              >
                <icon-save
                  v-if="userStore.username === item.createBy"
                  size="16"
                  @click="preMoveSetting(item)"
                />
              </a-tooltip>

              <a-tooltip :content="$t('schedule.meeting.download')"
                ><icon-download
                  v-show="type !== 'new'"
                  size="16"
                  @click="downLoadFile(item)"
                />
              </a-tooltip>

              <a-tooltip
                v-if="
                  item.id &&
                  isWpsFile(item.fileName) &&
                  userStore.username === item.createBy
                "
                :content="$t('schedule.edit')"
              >
                <icon-edit size="16" @click="wpsView(item, 'edit')"
              /></a-tooltip>
              <a-tooltip
                v-if="userStore.username === item.createBy"
                :content="$t('schedule.delete')"
              >
                <a-popconfirm
                  :content="$t('schedule.delete.attachment')"
                  @ok="delAnnex(item, 0)"
                >
                  <icon-delete size="16" />
                </a-popconfirm>
              </a-tooltip>
            </a-space>
          </a-col> </a-row
      ></div>
    </div>

    <!-- AI赋能 -->

    <div class="AI-empo">
      <div class="ai-title">
        <customIconBardLine class="describe-icon" />

        <span class="block-span"> {{ $t('schedule.meeting.ai') }}</span>
      </div>

      <!-- 生成会议方案在新增的时候就有  -->
      <div class="minutes">
        <a-space class="file-disabled"
          ><a-button
            :disabled="!hasFileEditPermission"
            type="text"
            class="file-upload"
            @click="addMeetingNotifyHandle"
            >{{ $t('schedule.meeting.ai.generate') }}</a-button
          >
          <a-button
            :disabled="!hasFileEditPermission"
            type="text"
            class="file-upload"
            @click="uploadHandle(13)"
            >{{ $t('schedule.meeting.ai.upload') }}</a-button
          ></a-space
        >
        <a-spin dot :loading="notifyLoading">
          <div
            v-if="notifyList.length && notifyList[0]?.fileName"
            class="fileList"
            ><a-row v-for="item in notifyList" :key="item.id">
              <a-col :span="19">
                <file-image
                  :file-name="item.name || item.fileName"
                  :is-file="true"
                />
                <span class="fileName" @click="wpsView(item, 'preview')">{{
                  item.fileName
                }}</span>
                <span v-if="item.aiFileConvertStatus"
                  >({{ statusText(item.aiFileConvertStatus) }})</span
                >
              </a-col>
              <a-col :span="5" align="right">
                <a-space>
                  <a-tooltip
                    v-if="route.params.projectId"
                    :content="$t('calendar.save-as')"
                  >
                    <icon-save
                      v-if="userStore.username === item.createBy"
                      size="16"
                      @click="preMoveSetting(item)"
                    />
                  </a-tooltip>

                  <a-tooltip :content="$t('schedule.meeting.download')">
                    <icon-download
                      v-show="type !== 'new'"
                      size="16"
                      @click="downLoadFile(item)"
                    />
                  </a-tooltip>

                  <a-tooltip
                    v-if="
                      item.id &&
                      isWpsFile(item.fileName) &&
                      userStore.username === item.createBy
                    "
                    :content="$t('schedule.edit')"
                  >
                    <icon-edit size="16" @click="wpsView(item, 'edit')" />
                  </a-tooltip>

                  <a-tooltip
                    v-if="userStore.username === item.createBy"
                    :content="$t('schedule.delete')"
                  >
                    <a-popconfirm
                      :content="$t('schedule.delete.plan')"
                      @ok="delAnnex(item, 1)"
                    >
                      <icon-delete size="16" style="vertical-align: middle" />
                    </a-popconfirm>
                  </a-tooltip>
                </a-space>
              </a-col> </a-row
          ></div>
        </a-spin>
      </div>
      <div
        v-if="props.type === 'edit' || props.type === 'view'"
        class="programme"
      >
        <a-space class="file-disabled">
          <a-button
            type="text"
            class="file-upload"
            :disabled="!hasFileEditPermission"
            @click="addMeetingMinutesHandle"
            >{{ $t('schedule.meeting.ai.summary') }}</a-button
          >
          <a-button
            :disabled="!hasFileEditPermission"
            type="text"
            class="file-upload"
            @click="uploadHandle(12)"
            >{{ $t('schedule.meeting.ai.upload.summary') }}</a-button
          >
        </a-space>
        <a-spin dot :loading="minutesLoading">
          <div
            v-if="minutesList.length && minutesList[0]?.fileName"
            class="fileList"
            ><a-row v-for="item in minutesList" :key="item.id">
              <a-col :span="19">
                <file-image
                  :file-name="item.name || item.fileName"
                  :is-file="true"
                />
                <span class="fileName" @click="wpsView(item, 'preview')">{{
                  item.fileName
                }}</span>
                <span v-if="item.aiFileConvertStatus"
                  >({{ statusText(item.aiFileConvertStatus) }})</span
                >
              </a-col>
              <a-col :span="5" align="right">
                <a-space>
                  <a-tooltip
                    v-if="route.params.projectId"
                    :content="$t('calendar.save-as')"
                  >
                    <icon-save
                      v-if="userStore.username === item.createBy"
                      size="16"
                      @click="preMoveSetting(item)"
                    />
                  </a-tooltip>

                  <a-tooltip :content="$t('schedule.meeting.download')"
                    ><icon-download
                      v-show="type !== 'new'"
                      size="16"
                      @click="downLoadFile(item)"
                  /></a-tooltip>

                  <a-tooltip
                    v-if="
                      item.id &&
                      isWpsFile(item.fileName) &&
                      userStore.username === item.createBy
                    "
                    :content="$t('schedule.edit')"
                    ><icon-edit size="16" @click="wpsView(item, 'edit')"
                  /></a-tooltip>

                  <a-tooltip
                    v-if="hasFileEditPermission && props.type !== 'view'"
                    :content="$t('schedule.meeting.ai.todo')"
                  >
                    <icon-plus-circle size="16" @click="addMatterHandle(item)"
                  /></a-tooltip>

                  <a-tooltip
                    v-if="userStore.username === item.createBy"
                    :content="$t('schedule.delete')"
                  >
                    <a-popconfirm
                      :content="$t('schedule.delete.meeting')"
                      @ok="delAnnex(item, 2)"
                    >
                      <icon-delete size="16" />
                    </a-popconfirm>
                  </a-tooltip>
                </a-space>
              </a-col> </a-row
          ></div>
        </a-spin>
      </div>
    </div>

    <!-- 快速生成代办 -->
    <div class="todo">
      <div class="block">
        <div>
          <customIconListIndefinite class="describe-icon" />
          <span class="block-span"> {{ $t('schedule.meeting.add.todo') }}</span>
          <icon-plus
            v-if="type !== 'view' && !hasNoEditPermission"
            @click="addAgencyHandle"
          />
        </div>
        <a-spin dot :loading="matterLoading">
          <div class="agencyList">
            <a-row
              v-for="(item, index) in agencyList"
              :key="index"
              style="position: relative"
            >
              <a-col :span="8">
                <a-input
                  v-model="item.title"
                  :disabled="props.type === 'view' || hasNoEditPermission"
                  :placeholder="$t('schedule.meeting.add.todo.placeholder')"
                  @blur="editMatterHandle(item)"
                ></a-input
              ></a-col>
              <a-col :span="8">
                <a-date-picker
                  v-model="item.planEndTime"
                  show-time
                  format="YYYY-MM-DD HH:mm"
                  :time-picker-props="{
                    step: {
                      hour: 1,
                      minute: 5,
                    },
                  }"
                  :readonly="
                    type === 'view' || hasNoEditPermission ? true : false
                  "
                  :placeholder="$t('schedule.selectDeadline')"
                  :disabled-date="disabledDate"
                  :disabled-time="disabledDateTime"
                  :class="{ 'hide-suffix-icon': type === 'view' }"
                  @change="handleDateChange(item, index)"
                  style="width: 100%"
                />
              </a-col>
              <a-col :span="6">
                <a-input-tag
                  v-if="globalMode === 'work' && !form.projectId"
                  v-model="item.member"
                  :disabled="props.type === 'view' || hasNoEditPermission"
                  :max-tag-count="1"
                  :placeholder="$t('schedule.selectPerson')"
                  @focus="getUserFocus(index)"
                />
                <a-select
                  v-else
                  v-model="item.member"
                  :disabled="props.type === 'view' || hasNoEditPermission"
                  multiple
                  allow-search
                  bordered
                  :max-tag-count="1"
                  :placeholder="$t('schedule.selectPerson')"
                  @change="handleUserChange(item, index)"
                >
                  <a-option
                    v-for="items in projectUserList"
                    :key="items.username"
                    :value="items.username"
                  >
                    {{ items.name }}
                  </a-option>
                </a-select>
              </a-col>
              <a-col v-if="!hasNoEditPermission" :span="1" align="right">
                <icon-delete
                  v-if="item.id"
                  @click="deleteHandle(index, item)"
                  size="16"
                  style="vertical-align: middle"
                />
                <a-popconfirm
                  v-else
                  :content="$t('schedule.delete.todo')"
                  @ok="delgencyHandle(index, item)"
                >
                  <icon-delete size="16" style="vertical-align: middle" />
                </a-popconfirm>

                <span
                  v-show="type !== 'new' && 'scheduleDetailId' in item"
                  class="icon-arrow-right"
                  @click="beforeJumpMeeting(item)"
                  >{{ $t('calendar.go-to') }}</span
                >
              </a-col>
            </a-row>
          </div>
        </a-spin>
      </div>
    </div>

    <!-- 评论 -->
    <div v-if="props.type !== 'new'" class="comments">
      <div class="block">
        <customIconComment class="describe-icon" />
        <span class="block-span"> {{ $t('schedule.comment') }}</span>
      </div>

      <div v-if="!comments.length" class="noData">
        <img :src="commentsBgImg" alt="" />
        <div>{{ $t('schedule.noComment') }}</div>
      </div>
      <a-list v-else>
        <a-list-item v-for="(item, index) in comments" :key="{ index }">
          <a-row>
            <a-col :span="20"
              ><a-list-item-meta
                :title="`${item?.createBy}：${item?.description}`"
                :description="item?.createDate"
              >
                <template #avatar>
                  <a-avatar
                    shape="circle"
                    :size="32"
                    style="background-color: rgb(var(--primary-6))"
                  >
                    <span>{{ item?.createBy.substring(0, 1) }}</span>
                  </a-avatar>
                </template>
              </a-list-item-meta></a-col
            >
            <a-col
              v-if="item.updateBy === userStore.username"
              :span="4"
              align="right"
            >
              <a-popconfirm
                :content="$t('schedule.delete.comment')"
                @ok="deleteCommentHandle(item)"
              >
                <icon-delete size="16" />
              </a-popconfirm>
            </a-col>
          </a-row>
        </a-list-item>
      </a-list>

      <div v-if="props.type !== 'new'" class="comments-input">
        <a-textarea
          v-model="form.description"
          :placeholder="$t('schedule.placeholder.comment')"
          :auto-size="{ minRows: 3, maxRows: 3 }"
          class="comment-input"
          @keydown.enter="saveCommentHandle"
        />
      </div>
    </div>
  </div>
  <div v-if="type !== 'view'" class="comment">
    <a-col :span="12">
      <a-space>
        <a-checkbox
          v-if="
            !hasNoEditPermission &&
            route.params.projectId !== '1925370190686588929' &&
            companyId === '100000'
          "
          v-model="isSentJJT"
          >{{ $t('calendar.push-to-Jiaojiantong') }}</a-checkbox
        >
      </a-space>
    </a-col>
    <a-col :span="12" align="right">
      <s-space>
        <a-button
          v-if="!hasNoEditPermission"
          type="primary"
          @click="submitHandle"
          >{{
            type === 'edit'
              ? $t('schedule.matter.saveChanges')
              : $t('schedule.createMeeting')
          }}</a-button
        >
      </s-space>
    </a-col>
  </div>

  <!-- 选择人员 -->
  <select-members
    v-if="selUserVisible"
    v-model:visible="selUserVisible"
    :limit="limitVal"
    :data="memberData"
    @select-member="selectMember"
  ></select-members>

  <!-- 附件上传 -->
  <uploadTheSpecifiedFolder
    v-model:visible="uploadVisible"
    @upload-single-success="uploadSingleSuccessHandle"
    @upload-complete="uploadComplete"
    @select-complete="selectComplete"
  />

  <a-modal
    :visible="repeatVisible"
    @ok="repeatChangeHandle"
    @cancel="handleCancel"
  >
    <template #title> {{ $t('schedule.meeting.edit.repeat') }} </template>
    <div>
      <a-radio-group v-model="form.repeatChange" direction="vertical">
        <a-radio :value="0">{{ $t('schedule.meeting.edit.current') }}</a-radio>
        <a-radio :value="1">{{
          $t('schedule.meeting.edit.currentAndAfter')
        }}</a-radio>
        <!-- <a-radio :value="2">所有重复会议</a-radio> -->
      </a-radio-group>
    </div>
  </a-modal>
  <a-modal
    :visible="delVisible"
    @ok="deleteSaveHandle"
    @cancel="handleDeleteCancel"
  >
    <template #title> {{ $t('schedule.delete') }} </template>
    <div>
      <div>{{
        `${deleteMatterMsg || ''} ${$t('schedule.delete.confirm')}`
      }}</div>
    </div>
  </a-modal>

  <a-modal
    :visible="jumpDialog"
    :simple="true"
    :esc-to-close="false"
    :mask-closable="false"
    @ok="jumpDialogOk"
    @cancel="jumpDialogCancle"
  >
    <span
      >{{ $t('calendar.save-modifications') }}
      <icon-close :size="16" class="close-icon" @click="jumpDialog = false" />
    </span>
  </a-modal>

  <move-dialog
    v-model:visible="moveModal"
    :title="$t('file-manage.select-target-folder')"
    :ok-function="beforeMoveOkHandler"
    :show-type="[]"
    :dis-check-hierarchy="[1]"
    check-type="single"
    output-type="id"
    is-clear-key
  ></move-dialog>
</template>

<script lang="ts" setup>
  import {
    ref,
    watch,
    defineProps,
    inject,
    computed,
    onMounted,
    toRef,
    defineExpose,
    Ref,
  } from 'vue';
  import selectMembers from '@/components/selectMembers/index.vue';
  import uploadTheSpecifiedFolder from '@/components/uploadTheSpecifiedFolder/index.vue';
  import { storeToRefs } from 'pinia';
  import useUploadFileStore from '@/store/modules/upload-file/index';
  import { cloneDeep, range } from 'lodash';
  import {
    createMeeting,
    getProjectList,
    saveComment,
    saveBatchMaater,
    editMatter,
    addMeetingNotify,
    addMeetingMinutes,
    setFiletokenText,
    meetAiAnalyze,
    addAttachments,
    removeAttachments,
    getAiAnaly,
    deleteComment,
    queryTeamList,
    getProjectUsers,
    getMeetingDetail,
    getProjectPanel,
    addMergaFile,
  } from '../api';
  import { getPanelList } from '../../calendar/api';
  import { deleteMatterFlag, deleteMatter } from '../../../api';

  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore, userScheduleStore, useGlobalModeStore } from '@/store';
  import { wpsViewHandle } from '@/hooks/wps';
  import FileImage from '@/components/uploadTheSpecifiedFolder/components/image-file.vue';
  import { downloadSource } from '@/components/file-image/hooks/dropdow-events';
  import dayjs from 'dayjs';
  import wpsJson from '@/config/wpsType.json';
  import customIconDescribe from '@/assets/images/matter/bar-chart-horizontal-line1.svg';
  import customIconAttachment from '@/assets/images/matter/attachment-line1.svg';
  import customIconComment from '@/assets/images/matter/message-2-line.svg';
  import customIconBardLine from '@/assets/images/meeting/bard-line.svg';
  import customIconListIndefinite from '@/assets/images/meeting/list-indefinite.svg';
  import commentsBgImg from '@/assets/images/schedule/comments-bg.png';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';
  import { useI18n } from 'vue-i18n';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';
  import {
    isBasicInfoChanged,
    isChildrenChanged,
    isCommentChanged,
  } from '../utils';
  import MoveDialog from '@/components/tree-folder/index.vue';
  import { AttachTableView } from '@/store/modules/file/types';
  import useFileStore from '@/store/modules/file/index';
  import { moveAttachment } from '@/views/projectSpace/file/api';

  const { t } = useI18n();
  const { i18TeamName } = useI18nHandleName();

  const companyId = computed(() => userStore.companyId);
  const fileStore = useFileStore();
  const moveModal = ref<boolean>(false);
  const activeTab = inject('activeTab') as Ref;
  const handleJump = inject('handleJump') as (type: string, id: string) => void;

  const jumpDialog = ref(false); // 待办事项跳转确认框是否显示
  const jumpData = ref<any>({}); // 待办事项跳转数据

  const userId = getUserId() || '';
  const globalModeStore = useGlobalModeStore();
  const globalMode = computed(() => globalModeStore.getGlobalMode);
  const currentProjectId = ref(getLocalstorage(`last_project_${userId}`) || '');
  const scheduleStore = userScheduleStore();
  const { scheduleId, currentProjectScheduleId } = storeToRefs(scheduleStore);

  const repeatVisible = ref(false);
  const delVisible = ref(false);

  const meetingStatus = ref(false);

  const propsData: any = ref(null);

  const isSentJJT = ref(false); // 是否发送交建通

  const userStore = useUserStore();

  const heightStyle = {
    height: 'calc(100vh - 398px)',
  };

  const form: any = ref({
    repeatType: 0,
    repeatChange: 0,
    planStartTime: '',
  });
  const currentDate = dayjs().format('YYYY-MM-DD'); // 当前日期
  const calendarOption: any = ref([]);
  const projectCalendarOption: any = ref([]);
  const calendarOptionList = computed(() => {
    return form.value.projectId
      ? projectCalendarOption.value
      : calendarOption.value;
  });
  const props = defineProps({
    data: {
      type: Object,
      default: null,
    },
    type: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  });

  const colSpan = props.type === 'view' ? 12 : 8;
  const colSpanLong = props.type === 'view' ? 24 : 8;
  // 禁选日期
  const disabledDate = (current: Date) => {
    const currentDate = dayjs(current);

    if (props.type === 'new') {
      // 创建事项时禁选当天之前的日期
      return currentDate.isBefore(dayjs(), 'day');
    }

    if (props.type === 'edit') {
      // 原始 createDate 字符串
      const createDateStr = form.value?.createDate;
      if (!createDateStr) return false; // 如果没有 createDate，则不禁用任何日期（根据需求调整）

      // 将 createDate 转换为 dayjs 对象，并设置时间为午夜（如果只比较日期部分）
      const createDateStartOfDay = dayjs(createDateStr).startOf('day');

      // 禁用 createDate 之前的日期
      return currentDate.isBefore(createDateStartOfDay, 'day');
    }

    // 如果 type 既不是 'new' 也不是 'edit'，则不禁用任何日期（根据需求调整）
    return false;
  };
  // 禁选时间
  const disabledDateTime = (dates: Date) => {
    const selectedDate = dayjs(dates);
    const now = dayjs();

    const isToday = selectedDate.isSame(now, 'day');
    const createDate = dayjs(form.value?.createDate);
    const isSameDayEdit = selectedDate.isSame(createDate, 'day');

    const disabledTime = {
      disabledHours: () => [],
      disabledMinutes: (selectedHour: number) => [],
      disabledSeconds: () => [],
    };

    if (props.type === 'new' && isToday) {
      const currentHour = now.hour();
      const currentMinute = now.minute();

      return {
        ...disabledTime,
        disabledHours: () => range(0, currentHour + 1),
        disabledMinutes: (selectedHour: number) => {
          // 只有当前小时才禁掉前面的分钟
          return selectedHour === currentHour ? range(0, 59) : [];
        },
      };
    }

    if (props.type === 'edit' && isSameDayEdit) {
      const editHour = createDate.hour();
      const editMinute = createDate.minute();
      return {
        ...disabledTime,
        disabledHours: () => range(0, editHour),
        disabledMinutes: (selectedHour: number) => {
          return selectedHour === editHour ? range(0, editMinute) : [];
        },
      };
    }

    return disabledTime;
  };
  // 判断是否是wps文件
  const isWpsFile = (fileName: any) => {
    const type = fileName.split('.')[fileName.split('.').length - 1];
    const isWpsType: boolean = wpsJson.includes(type);
    return isWpsType;
  };
  // 定义接口
  interface ProjectUser {
    id: string;
    name: string;
    username: string;
  }
  // 项目下的人员列表

  const projectUserList = ref<ProjectUser[]>([]);
  const getProjectUserList = async (projectId: string) => {
    const params = {
      pageNo: 1,
      pageSize: 999999,
      projectId,
    };
    try {
      const res = (await getProjectUsers(params)) as any;
      if (res.code === 8000000) {
        projectUserList.value = deduplication(res.data?.list, 3) || [];
      }
    } catch (error) {
      console.error('获取项目人员列表失败:', error);
    }
  };
  // 数组去重
  const deduplication = (data: any, type: any) => {
    if (type === 1)
      return data?.filter(
        (item: any, index: any, self: any) =>
          index === self.findIndex((t: any) => t.id === item.id)
      );
    if (type === 2)
      return data?.filter(
        (item: any, index: any, self: any) =>
          index ===
          self.findIndex((t: any) => t.userFullName === item.userFullName)
      );
    return data?.filter(
      (item: any, index: any, self: any) =>
        index === self.findIndex((t: any) => t.username === item.username)
    );
  };

  // 获取个人日历数据
  const getPanelListHandle = async () => {
    const param = {
      companyId: '100000',
    };
    const { data } = await getPanelList(param);
    calendarOption.value = data;
    // 默认展示我的日历
    const mineData = calendarOption.value?.filter((item: any) => {
      return item?.defaultFlag === 1;
    });
    // form.value.schedulePanelId = mineData[0].id || '';
    form.value.schedulePanelId = scheduleId.value || mineData[0].id;
  };
  // 获取当前项目下的项目日历
  const getProjectPanelList = async (id: string) => {
    const param = {
      projectId: id,
    };
    const { data } = await getProjectPanel(param);
    projectCalendarOption.value = [data] || [];
    // 项目下的日历如果有，就只有一个
    // form.value.schedulePanelId = mineData[0].id || '';
    form.value.schedulePanelId =
      currentProjectScheduleId.value.toString() ||
      projectCalendarOption.value[0].id;
    console.log('项目日历赋值', currentProjectScheduleId.value);
  };

  // 保存评论
  const saveCommentHandle = async () => {
    if (!form.value.description.trim()) {
      Message.warning(t('schedule.comment.placeholder'));
      return;
    }
    const param = {
      businessId: mettingId.value,
      description: form.value.description,
      type: 2,
    };
    const res = await saveComment(param);
    if (res.status) {
      Message.success(t('schedule.comment.success'));
      form.value.description = '';
      const { data } = await getMeetingDetail(props.data.id);
      // 评论回显
      comments.value = data?.comments || [];
      scheduleStore.setSummaryData(data);
    }
  };

  // wps预览
  const wpsView = (record: any, type: string) => {
    if (record.id) {
      record.name = record.fileName;
      // const action = hasNoEditPermission.value ? 'preview' : 'edit'
      wpsViewHandle(record, type, 'admin');
    } else {
      Message.info('请保存后查看');
    }
  };

  // 获取团队列表数据
  const teamList: any = ref([]);
  // 查询列表数据
  const getTeamList = async (projectId: string) => {
    const params: any = {
      pageNo: 1,
      pageSize: 999999,
      projectId,
    };
    queryTeamList(params)
      .then((res) => {
        teamList.value = res.data.list || [];
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const uploadFileStore = useUploadFileStore();

  const uploadVisible = ref(false); // 上传文件弹窗
  const uploadComplete = () => {
    uploadVisible.value = false; // 取消按钮关闭对话框
  };

  const { fileArr, loading, uploadFileList, hasTokenFile } =
    storeToRefs(uploadFileStore);
  const selectFolderObj = ref({});
  const route = useRoute();

  const selectComplete = (selectFolderObjA: any) => {
    selectFolderObj.value = { ...selectFolderObjA };
    if (fileArr.value.length > 0) {
      uploadFileStore.projectId = route.params.projectId as string;
      uploadFileStore.handleUploadFile(selectFolderObj.value, 0);
    }
  };

  const uploadType = ref(11);
  const uploadHandle = (val: any) => {
    uploadType.value = val;
    uploadVisible.value = true;
  };
  // 通用延时函数
  const sleep = (ms: number) => {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  };
  const uploadSingleSuccessHandle = async (val: any) => {
    const data = {
      fileName: val.name || '',
      fileSize: val.size || '',
      token: val.fileToken || '',
      type: uploadType.value,
    };

    if (props.type === 'edit' || props.type === 'view') {
      const param = {
        fileName: val.name || '',
        fileSize: val.size || '',
        token: val.fileToken || '',
        type: uploadType.value,
      };
      // 编辑时 增加文件需要绑定文件到会议上
      addAttachments(props.data?.id, param);
      await sleep(1000);
      const { data: datas } = await getMeetingDetail(props.data.id);
      if (uploadType.value === 11)
        // 附件回显
        annexList.value = datas.attachments.filter((item: any) => {
          return item.type === 11;
        });
      // annexList.value.push(data); // 带 token 的上传文件
      else if (uploadType.value === 13)
        // 会议方案
        notifyList.value = datas.attachments.filter((item: any) => {
          return item.type === 13;
        });
      // 会议纪要
      minutesList.value = datas.attachments.filter((item: any) => {
        return item.type === 12;
      });
    } else if (uploadType.value === 11)
      annexList.value.push(data); // 带 token 的上传文件
    else if (uploadType.value === 13) notifyList.value.push(data);
    else minutesList.value.push(data);
  };

  // watch(loading, (val, oldVal) => {
  //   if (!val && oldVal) {
  //     if (uploadFileList.value.length === 0) {
  //       fileArr.value = [];
  //       uploadVisible.value = false;
  //       console.log('[ hasTokenFile.value ] >', hasTokenFile.value);

  //       if (props.type === 'edit') {
  //         hasTokenFile.value.forEach((item: any) => {
  //           const param = {
  //             fileName: item.fileName || '',
  //             fileSize: item.fileSize || '',
  //             token: item.fileToken || '',
  //             type: uploadType.value,
  //           };
  //           // 编辑时 增加文件需要绑定文件到会议上
  //           addAttachments(props.data?.id, param);
  //         });
  //       }

  //       if (uploadType.value === 11)
  //         annexList.value.push(...hasTokenFile.value); // 带 token 的上传文件
  //       else if (uploadType.value === 13)
  //         notifyList.value.push(...hasTokenFile.value);
  //       else minutesList.value.push(...hasTokenFile.value);
  //     }
  //   }
  // });

  watch(
    () => form.value.planStartTime,
    (newValue) => {
      if (
        newValue &&
        form.value.planEndTime &&
        dayjs(form.value.planEndTime).isBefore(newValue)
      ) {
        form.value.planEndTime = null; // 重置结束时间，如果它早于新的开始时间
      }
    }
  );

  const planStartTime = ref('');
  const planEndTime = ref('');
  // 修改时间
  const changeTime = (val: any) => {
    [planStartTime.value, planEndTime.value] = [
      dayjs(val[0]).format('YYYY-MM-DD HH:mm'),
      dayjs(val[1]).format('YYYY-MM-DD HH:mm'),
    ];
    form.value.time = [planStartTime.value, planEndTime.value];
  };

  // 会议方式
  const meetingWay = ref([
    { label: 'schedule.meeting.type.online', id: '0' },
    { label: 'schedule.meeting.type.offline', id: '1' },
  ]);

  // 重复类型
  const repeatedOption = ref([
    { label: 'schedule.meeting.repeat.type.none', id: 0 },
    { label: 'schedule.meeting.repeat.type.daily', id: 1 },
    { label: 'schedule.meeting.repeat.type.weekday', id: 2 },
    { label: 'schedule.meeting.repeat.type.weekly', id: 3 },
    { label: 'schedule.meeting.repeat.type.biweekly', id: 4 },
  ]);

  const selUserVisible: any = ref(false);

  // 组织人
  const organizerData: any = ref({
    id: '',
    name: '',
  });

  // 参与人
  const participants: any = ref({
    id: '',
    name: '',
  });

  const memberData = ref([]);
  const allMemeber: any = ref([]);
  const formRef = ref<any>();
  const agencyList: any = ref([]);

  // 选择人员
  const selectMember = (user: any) => {
    // 用于存选中的用户数据 并去重

    console.log(user, '1051');
    allMemeber.value.push(...user);
    console.log(allMemeber.value, '1049');
    allMemeber.value = deduplication(allMemeber.value, 1);

    if (setMemberType.value === 'organizer') {
      // 组织人
      organizerData.value = {
        id: user[0].id,
        name: user[0].userName,
        userName: user[0].userName,
        userFullname: user[0].userFullname,
      };
      form.value.organizer = user?.map((item: any) => item.userFullname); // 表单组织人展示
      formRef.value?.validateField('organizer');
    } else if (setMemberType.value === 'participants') {
      // 参与人
      form.value.participants = user?.map((item: any) => item.userFullname);
      participants.value = {
        id: user.map((item: any) => item.id),
        name: user?.map((item: any) => item.userName).join(','),
        userName: user?.map((item: any) => item.userName).join(','),
        userFullname: user?.map((item: any) => item.userFullname).join(','),
      };
      formRef.value?.validateField('participants');
    } else {
      // 事项待办中选人
      agencyList.value[setMemberType.value].member = user?.map(
        (item: any) => item.userFullname
      );
      agencyList.value[setMemberType.value].chargePersonId = user
        ?.map((item: any) => item.userName)
        .join(',');
      agencyList.value[setMemberType.value].chargePersonName = user
        ?.map((item: any) => item.userFullname)
        .join(',');
      const userInfo: any = [];
      user.forEach((item: any, index: any) => {
        userInfo.push({
          userFullname: item.userFullname,
          userFullName: item.userFullname,
          userName: item.userName,
          id: item.userName,
        });
      });
      agencyList.value[setMemberType.value].userInfo = userInfo;
      editMatterHandle(agencyList.value[setMemberType.value]);
    }
  };

  const emit = defineEmits(['update:title', 'refresh']);

  // 创建事项  创建会议前  调用批量创建事项(此处只添加新增的数据  对于已经存在的事项数据不会再添加)
  const agendaId: any = ref('');
  let existingAgendaId: any = [];

  // 对已有的事项编辑
  const editMatterHandle = async (record: any) => {
    if (record.id) {
      // const param = record;
      console.log(record, '1109:record');
      const param = {
        planEndTime: dayjs(record.planEndTime).format('YYYY-MM-DD HH:mm'),
        chargePersonId: record.chargePersonId,
        title: record.title,
        projectId: record.projectId,
        id: record.scheduleDetailId,
      };
      console.log(param, '1111:param');
      const res = await editMatter(param);
      // if (res.status) Message.success('编辑成功');
    }
  };

  const addMatter = async () => {
    const paramArr: any = [];

    // 添加已有事项id
    existingAgendaId = agencyList.value
      .filter((item: any) => item.id)
      .map((item: any) => item.id);

    console.log(agencyList.value, 8989);

    // 新增的事项数据
    const agencyListData = agencyList.value.filter(
      (item: any) => !('id' in item)
    );
    console.log(agencyListData, 78911);

    agencyListData.forEach((item: any) => {
      // const chargePersonId: any = [];
      // item.userInfoBoList.forEach((item: any) => {
      //   chargePersonId.push(item.userName);
      // });
      paramArr.push({
        agendaParam: {
          chargePersonId: item.chargePersonId, // 人员userName
          projectId: item.projectId,
          title: item.title,
          type: 'item',
          // planEndTime: dayjs(currentDate).format('YYYY-MM-DD HH:mm'),
          planEndTime: dayjs(item.planEndTime).format('YYYY-MM-DD HH:mm'),
          schedulePanelId: form.value.schedulePanelId, // 日程看板id
        },
      });
    });

    console.log(paramArr, 7778);

    const matterIds: any = [];
    if (!paramArr?.length) {
      agendaId.value = [...existingAgendaId].join(',');
      return { status: true };
    }
    // 有新增的数据 则进行批量添加事项
    const res: any = await saveBatchMaater(paramArr);
    if (res.status) {
      res?.data?.forEach((item: any) => {
        matterIds.push(item.id);
      });
      agendaId.value = [...matterIds, ...existingAgendaId].join(',');
      // agendaId.value = matterIds.join(',');
    }
    return res;
  };

  // 重置
  const resetData = () => {
    form.value = {};
    annexList.value = [];
    notifyList.value = [];
    minutesList.value = [];
    agencyList.value = [];
    comments.value = [];
  };

  // 注入爷爷组件提供的方法
  const grandparentMethod = inject('grandparentMethod');

  // 调用爷爷组件的方法
  const callGrandparentMethod = () => {
    if (grandparentMethod) {
      grandparentMethod();
    }
  };

  const statusText = (val: any) => {
    switch (val) {
      case 'PENDING':
        return '排队中';
      case 'RUNNING':
        return '处理中';
      case 'SUCCEEDED':
        return '成功';
      case 'FAILED':
        return '失败';
      default:
        return '';
    }
  };

  // 生成会议方案
  const addAgencyfun = async (id: any) => {
    notifyLoading.value = true;
    const notifyRes = await addMeetingNotify(id);
    if (notifyRes.status) {
      if (notifyRes.data.attachments?.length) {
        notifyList.value = notifyRes.data.attachments.filter((item: any) => {
          return item.type === 13;
        });
        console.log(notifyList.value, 999);
        Message.success(t('schedule.meeting.ai.generate.success'));
      }
    } else {
      Message.error(t('schedule.meeting.ai.generate.error'));
    }
    notifyLoading.value = false;
  };

  // 创建会议
  const createMeetingHandle = async (isNotify?: any, noTips?: any) => {
    // 标题
    if (!props.title) {
      Message.info(t('schedule.inputTitle'));
      return false;
    }
    // 2.待办事项不能存在为空的数据
    const flag = agencyList.value.some(
      (item: any) => !item.title || !item?.member.length
    );
    if (flag) {
      Message.info(t('schedule.meeting.placeholder'));
      return false;
    }
    if (form.value.planStartTime > form.value.planEndTime) {
      Message.info(t('schedule.meeting.time.placeholder'));
      return false;
    }

    // 新建事项
    const matterRes: any = await addMatter();
    console.log(matterRes.status);
    if (!matterRes.status) return false;

    const res = await formRef.value?.validate();
    if (!res) {
      // 附件
      const attachments: any = [];
      annexList?.value.forEach((item: any) => {
        attachments.push({
          ...item,
          fileName: item.fileName || '',
          fileSize: item.fileSize || '',
          token: item.fileToken || item.token,
          type: 11,
        });
      });
      notifyList?.value.forEach((item: any) => {
        attachments.push({
          ...item,
          fileName: item.fileName || '',
          fileSize: item.fileSize || '',
          token: item.fileToken || item.token,
          type: 13,
        });
      });
      minutesList?.value.forEach((item: any) => {
        attachments.push({
          ...item,
          fileName: item.fileName || '',
          fileSize: item.fileSize || '',
          token: item.fileToken || item.token,
          type: 12,
        });
      });

      const param = JSON.parse(JSON.stringify(form.value));
      // 如果是项目空间，项目id默认传当前项目的，否则不传
      param.attachments = attachments;
      param.organizerId = organizerData.value.id; // 组织人id
      param.organizerUsername = organizerData.value.userName; // 组织人name
      param.participants = participants.value.userName; // 会议参与人
      // [param.planStartTime, param.planEndTime] = form.value.time;
      param.planStartTime = dayjs(param.planStartTime).format(
        'YYYY-MM-DD HH:mm:ss'
      );
      param.planEndTime = dayjs(param.planEndTime).format(
        'YYYY-MM-DD HH:mm:ss'
      );
      param.title = props.title;
      param.scheduleDetailId = propsData.value?.scheduleDetailId || '';
      param.agendaIds = agendaId.value;
      param.sendMsg = isSentJJT.value ? 1 : 0; // 发送交建通
      param.id = meetingId.value || '';

      if (props.type === 'edit') {
        param.status = meetingStatus.value ? 2 : props?.data?.status || 0;
        // 点击生成会议方案时如果是编辑状态 创建的会议为非草稿数据  新增状态为草稿会议
        param.draftStatus = 0;
      } else if (isNotify) param.draftStatus = 1;
      else param.draftStatus = 0;

      // else param.status = null;

      // 另存文件
      await saveFileSeparately();
      // 创建会议
      const res = await createMeeting(param);
      if (res.status) {
        // 点击按钮是生成会议方案时
        if (isNotify) {
          console.log('会议创建成功');
          const { data } = res;
          meetingId.value = data.id;
          // 回显
          propsData.value = data;

          await echoHandle(data);
          // 获取会议方案
          addAgencyfun(res.data.id);
        } else if (meetingId.value && props.type === 'edit') {
          // 编辑会议按钮
          if (noTips) {
            Message.success(t('schedule.calendar.edit'));
            emit('refresh', props.data?.id);
          }
        } else {
          // 创建会议按钮
          Message.success(t('schedule.calendar.create'));
          // 添加成功触发列表查询，不用区分个人空间还是项目空间，因为查询条件的判断是有的
          // 只需要触发列表查询即可
          emit('refresh', 'new');
          emit('update:title', ''); // 清空标题
          resetData();
          // callGrandparentMethod();
        }
        return true;
      }
      return true;
    }
    return false;
  };

  const changeRepeatType = () => {
    if (form.value.repeatType !== 0) {
      if (!form.value.repeatEndTime)
        form.value.repeatEndTime = form.value.planEndTime;
    } else form.value.repeatEndTime = '';
  };

  // 编辑状态 若重复方式为重复 打开弹窗选择类型  否则直接保存
  const changeRepeatTypeHandle = () => {
    // 切换重复方式
    if (form.value.repeatType !== 0 && props.type === 'edit') {
      form.value.repeatChange = 0;
      repeatVisible.value = true;
    } else createMeetingHandle(false, true);
  };

  // 将设置另存为指定团队文件夹的文件进行处理
  const saveFileSeparately = async () => {
    const combinedList = [...annexList.value, ...notifyList.value];

    const map = new Map();
    allSpecifiedFileData.value.forEach((item) => {
      map.set(item.token, {
        specifiedFolderId: item.specifiedFolderId,
        teamId: item.teamId,
      });
    });

    const updatedCombinedList = combinedList.map((item) => {
      const match = map.get(item.token);
      if (match) {
        return {
          ...item,
          ...match,
        };
      }
      return item;
    });

    // eslint-disable-next-line no-restricted-syntax
    for (const item of updatedCombinedList) {
      if (item.specifiedFolderId) {
        const params = {
          fileToken: item.token,
          folderId: item.specifiedFolderId,
          name: item.fileName,
          projectId: route.params.projectId,
          size: item.fileSize,
          teamId: item.teamId,
        };
        // eslint-disable-next-line no-await-in-loop
        await addMergaFile(params);
      }
    }
  };

  // 创建编辑会议
  const submitHandle = async () => {
    if (props.type === 'edit') {
      changeRepeatTypeHandle();
    }
    // 创建会议
    else {
      createMeetingHandle(false, true);
    }
  };

  // 附件
  const annexList: any = ref([]);
  // 会议通知
  const notifyList: any = ref([]);
  // 会议纪要
  const minutesList: any = ref([]);

  // 通过用户名查找对应的用户数据
  const nameSelectMember = (ids: any, objects: any) => {
    console.log(ids, '1375:参与人');
    console.log(objects, '1376:所有人员');
    if (!ids?.length || !objects?.length) return [];
    return objects.filter((obj: any) => ids.includes(obj.userFullname));
  };

  const matterLoading = ref(false);
  const notifyLoading = ref(false);
  const minutesLoading = ref(false);
  // 项目空间下根据会议纪要生成的待办，删除不在该项目下的人员
  const filterMembers = async (data: any) => {
    return data.filter((item: any) =>
      projectUserList.value.some((user: any) => user.username === item.userName)
    );
  };
  // 添加事项
  const addMatterHandle = async (val: any) => {
    if (val.aiFileConvertStatus !== 'SUCCEEDED') {
      Message.error(t('schedule.meeting.ai.todo.error'));
      return;
    }
    matterLoading.value = true;
    // 方式一 传token 获取文案 传文案事项数据
    // const param = {
    //   fileToken: val.token,
    // };
    // const { data } = await setFiletokenText(param);
    // const params = {
    //   prompt: data,
    //   // prompt:
    //   //   '事项1标题是:厦门白鹭西塔, 所属项目是中交项目 截止时间是2025年12月27日 分配人员：谢楚材;金可;廖川；事项2标题是:厦门白鹭西塔, 所属项目是中交项目 截止时间是2025年12月27日 分配人员：谢楚材;金可;廖川;杨轶凡;张颖飞;张宇',
    //   // prompt: data,
    // };
    // const matterData = await meetAiAnalyze(params);

    // //  方式二传token获取事项数据
    const param = {
      fileToken: [val.token],
    };
    const matterData = await getAiAnaly(param);

    matterData.data?.forEach(async (item: any) => {
      item.projectId = item.projectId ? item.projectId : form.value.projectId;
      item.userInfoBoList = deduplication(item.userInfoBoList, 2); // 先根据人名去重
      if (item.projectId && item.userInfoBoList) {
        console.log('userInfoBoList:', item.userInfoBoList); // 检查内容
        item.userInfoBoList = await filterMembers(item.userInfoBoList);
      }
      const chargePersonId: any = []; // 人员id
      const member: any = []; // 人员名称
      const userInfo: any = []; // 用户整体数据 用于回显
      item?.userInfoBoList?.forEach((items: any) => {
        chargePersonId.push(items.userName);
        if (item.projectId) {
          member.push(items.userName);
        } else {
          member.push(items.userFullName);
        }
        userInfo.push({
          userFullname: items.userFullName,
          userFullName: items.userFullName,
          userName: items.userName,
          id: items.userName,
        });
      });
      if (!form.value.projectId) {
        item.chargePersonId = chargePersonId.join(',');
      }
      item.member = member;
      item.userInfo = userInfo;
    });
    agencyList.value = matterData.data.concat(agencyList.value);
    console.log(agencyList.value, 8872);
    matterLoading.value = false;
  };

  const setMember = (data: any) => {
    data.users = data.users || [];

    // 添加回显人员数据
    let matterUser: any = [];
    //  事项人员数据
    data?.agendas?.forEach((item: any) => {
      matterUser = [...matterUser, ...item.agendaUser];
    });
    // 参与人
    const users = data.users || [];
    // 组织人
    const organizer = data.organizer || [];
    // 所有人员数据
    const allUser = [organizer, ...users, ...matterUser];
    // const allUsers = deduplication(allUser, 2);
    console.log(organizer, '1458');
    console.log(users, '1459');
    console.log(matterUser, '1460');
    allMemeber.value = allUser?.length > 0 ? deduplication(allUser, 1) : [];
    console.log(allMemeber.value, '1469');
  };

  // 回显
  const mettingId = ref('');
  const comments: any = ref([]);
  const echoHandle = async (data: any) => {
    // 处理一下teamId,如果返回的是0，就给处理成空
    if (data.teamId === 0) {
      data.teamId = '';
    }
    notifyList.value = [];
    annexList.value = [];
    notifyList.value = [];
    console.log('huixian');
    if (data.projectId) {
      await Promise.all([
        getProjectUserList(data.projectId),
        getTeamList(data.projectId),
      ]);
    }
    // if (!projectList.value?.length) await getProjectListHandle();
    if (!calendarOption.value?.length && props.type !== 'view')
      await getPanelListHandle();
    if (data.projectId && !projectCalendarOption.value?.length) {
      // 情况 1：有项目 ID 且项目面板为空
      console.log('有项目 ID 且项目面板为空', data.projectId);
      console.log('有项目 ID', !projectCalendarOption.value?.length);
      await getProjectPanelList(data.projectId);
    } else if (
      !data.projectId &&
      !calendarOption.value?.length &&
      props.type !== 'view'
    ) {
      // 情况 2：无项目 ID，且个人面板为空，且不是只读模式
      await getPanelListHandle();
    }
    mettingId.value = data.id; // 会议id

    form.value = {};

    if (!data.projectId) {
      setMember(data);
    }

    // 表单回显主体
    console.log(form.value, '会议回显前表单数据');
    form.value = data;
    console.log(data, '会议回显后表单数据');
    // form.value.time = [data.planStartTime, data.planEndTime];
    meetingStatus.value = data.status === 2;
    if (data.projectId) {
      // 组织人回显及数据获取
      // 根据username 去projectUserList里面查到对应的用户信息
      const user = projectUserList.value.filter(
        (item: any) => item.username === data.organizerUsername
      );
      if (data.organizerUsername) {
        organizerData.value = {
          id: user[0].id,
          name: user[0].name,
          userName: user[0].username,
          userFullname: user[0].name,
        };
        form.value.organizer = data.organizerUsername; // 组织人表单回显
      }
      // 参与人回显及数据获取
      // data.participants里包含了创建人，剔除掉，数据智能用对象里的username去下拉数据里匹配
      const filteredParticipants = projectUserList.value.filter((user) =>
        data.participants.some(
          (participant: any) => participant.username === user.username
        )
      );
      console.log(filteredParticipants, '参与人数据');
      participants.value = {
        id: filteredParticipants?.map((item: any) => item.id),
        name: filteredParticipants?.map((item: any) => item.name).join(','),
        userName: filteredParticipants
          ?.map((item: any) => item.username)
          .join(','),
        userFullname: filteredParticipants
          ?.map((item: any) => item.name)
          .join(','),
      };
      console.log(participants, '参与人处理之后数据');
      form.value.participants = filteredParticipants?.map(
        (item: any) => item.username
      ); // 参与人表单回显
    } else {
      // 组织人回显及数据获取
      if (data.organizerUsername) {
        organizerData.value = {
          id: data.organizer.id,
          userName: data.organizer.userName,
          userFullname: data.organizer.userFullname,
        };
        form.value.organizer = [data.organizer.userFullname]; // 组织人表单回显
      }
      // 参与人回显及数据获取
      form.value.participants = data.users?.map(
        (item: any) => item.userFullname
      ); // 参与人表单回显
      participants.value = {
        id: data.users?.map((item: any) => item.id),
        name: data.users?.map((item: any) => item.userName).join(','),
        userName: data.users?.map((item: any) => item.userName).join(','),
        userFullname: data.users
          ?.map((item: any) => item.userFullname)
          .join(','),
      };
    }

    // 评论回显
    comments.value = data?.comments || [];
    // 附件回显
    annexList.value = data.attachments.filter((item: any) => {
      return item.type === 11;
    });
    // 会议方案
    notifyList.value = data.attachments.filter((item: any) => {
      return item.type === 13;
    });
    // 会议纪要
    minutesList.value = data.attachments.filter((item: any) => {
      return item.type === 12;
    });
    // 待办事项回显
    agencyList.value = data.agendas;

    // 会议待办回显数据
    agencyList.value.forEach((item: any) => {
      const userInfo: any = [];
      const chargePersonId = item?.chargePersonId.split(',');
      const chargePersonName = item?.chargePersonName.split(',');
      chargePersonId.forEach((user: any, index: any) => {
        userInfo.push({
          userFullname: chargePersonName[index],
          userFullName: chargePersonName[index],
          userName: user,
          id: user,
        });
      });
      item.userInfo = userInfo;
      if (item.projectId) {
        item.member = chargePersonId;
      } else {
        item.member = chargePersonName;
      }
      console.log(item, '人员1559');
    });
  };

  // 获取焦点时触发选人弹窗
  const setMemberType = ref();
  const limitVal: any = ref(null);
  const getUserFocus = async (val: any) => {
    memberData.value = [];
    setMemberType.value = val;
    limitVal.value = null;

    if (val === 'organizer') {
      memberData.value = await nameSelectMember(
        form.value.organizer,
        allMemeber.value
      );
      limitVal.value = 1;
    } else if (val === 'participants') {
      memberData.value = nameSelectMember(
        form.value.participants,
        allMemeber.value
      );
    } else {
      console.log(agencyList.value[setMemberType.value].member, 1613);
      console.log(allMemeber.value, 1614);
      memberData.value = nameSelectMember(
        agencyList.value[setMemberType.value].member,
        allMemeber.value
      );
      console.log(memberData.value, '1626');
      // memberData.value = agencyList.value[setMemberType.value].userInfoBoList;
      // memberData.value?.forEach((item: any) => {
      //   item.id = item.userId;
      // });
      // console.log(memberData.value, 888);

      // memberData.value = agencyList.value[setMemberType.value].userInfo;
    }
    selUserVisible.value = true;
  };

  const meetingId: any = ref('');
  const propsCopyData: any = ref({});
  watch(
    () => props.data,
    (val: any) => {
      minutesLoading.value = false;
      notifyLoading.value = false;
      const copyPropsData = cloneDeep(props.data);
      if (props.type === 'edit') {
        // 编辑
        form.value.id = copyPropsData.id;
        meetingId.value = copyPropsData.id;
        propsData.value = copyPropsData;
        // 拷贝一份数据
        propsCopyData.value = JSON.parse(JSON.stringify(props.data));
        echoHandle(copyPropsData);
        heightStyle.height = 'calc(100vh - 303px)';
      } else if (props.type === 'new') {
        // 新增
        console.log('新增', copyPropsData);
        propsData.value = copyPropsData;
        form.value.id = null;
        if (globalMode.value === 'project') {
          form.value.projectId = currentProjectId.value;
          // 项目空间新增会议
          getProjectPanelList(form.value.projectId);
        }
        if (globalMode.value === 'work') {
          form.value.teamId =
            userStore.teamId === 'global' ? '' : userStore.teamId;
          // 个人空间新增会议
          getPanelListHandle();
        }
        heightStyle.height = 'calc(100vh - 303px)';
        // getProjectListHandle();

        meetingId.value = null;
      } else {
        // 预览
        form.value.id = copyPropsData.id;
        meetingId.value = copyPropsData.id;
        propsData.value = copyPropsData;
        // 拷贝一份数据
        propsCopyData.value = JSON.parse(JSON.stringify(props.data));
        echoHandle(copyPropsData);
        heightStyle.height = 'calc(100vh - 120px)';
      }
    },
    {
      immediate: true,
    }
  );
  // 创建人和参与人，组织人有权限编辑会议
  const hasNoEditPermission = computed(() => {
    const currentUser = userStore.username;
    const creator = propsCopyData.value.createBy;
    // 组织人
    const organizer = propsCopyData.value?.organizerUsername || '';
    const participants = propsCopyData.value?.participants || [];
    // 提取所有参与人的 username
    const participantUsernames = participants.map((p: any) => p.username);
    // 假设是username数组
    return (
      props.type === 'edit' &&
      currentUser !== creator &&
      currentUser !== organizer &&
      !participantUsernames.includes(currentUser)
    );
  });
  // 文件生成上传权限，组织人，会议人，参与人
  const hasFileEditPermission = computed(() => {
    const currentUser = userStore.username;
    const creator = propsCopyData.value.createBy;
    const organizer = propsCopyData.value?.organizerUsername || '';
    const participants = propsCopyData.value?.participants || [];
    // 提取所有参与人的 username
    const participantUsernames = participants.map((p: any) => p.username);
    // 假设是username数组
    return (
      props.type === 'new' ||
      currentUser === creator ||
      currentUser === organizer ||
      participantUsernames.includes(currentUser)
    );
  });
  // 添加生成代办
  const addAgencyHandle = () => {
    agencyList.value.push({
      value: '',
      member: globalMode.value === 'project' || form.value.projectId ? [] : '', // 人员回显值
      chargePersonId: '', // 人员提交得id
      chargePersonName: '', // 人员提交得id
      schedulePanelId: '', // 看板id // 会议选择的项目下人员
      projectId: form.value.projectId,
      planEndTime: '',
    });
    console.log('添加待办1651', agencyList.value);
  };
  // 删除生成代办
  const delgencyHandle = (index: any, item: any) => {
    agencyList.value = agencyList.value.filter((item: any, idx: any) => {
      return idx !== index;
    });
  };
  const nowRecord = ref(null); // 当前选择的数据
  const nowRecordIndex = ref(null); // 当前选择的数据
  // 删除确认框
  const deleteSaveHandle = async () => {
    const params = {
      scheduleDetailId: nowRecord.value.scheduleDetailId,
    };
    try {
      const res = await deleteMatter(params);
      if (res.status) {
        Message.success(t('schedule.delete.success'));
        agencyList.value = agencyList.value.filter((item: any, idx: any) => {
          return idx !== nowRecordIndex.value;
        });
        // emits('refresh');
        delVisible.value = false;
      }
    } catch (error) {
      console.log(error);
    }
  };
  const deleteMatterMsg = ref('');
  // 删除
  const deleteHandle = async (index: any, item: any) => {
    // item代表事项
    const params = {
      scheduleDetailId: item.scheduleDetailId,
    };
    try {
      const res = await deleteMatterFlag(params);
      if (res.status) {
        const [firstError] = res.data.err || []; // 使用数组解构，并处理 err 可能为 undefined 的情况
        deleteMatterMsg.value = firstError;
        console.log('[ firstError ] >', firstError);
      }
    } catch (error) {
      console.log(error);
    }
    nowRecord.value = item;
    nowRecordIndex.value = index;
    delVisible.value = true;
  };
  // 取消删除
  const handleDeleteCancel = () => {
    delVisible.value = false;
  };

  // 删除文件
  const lists = [annexList, notifyList, minutesList];
  const delAnnex = async (record: any, index: any) => {
    if (record?.createBy === userStore.username) {
      if (index >= 0 && index < lists.length) {
        const list = lists[index].value;
        const itemIndex = list.findIndex((item: any) => item.id === record.id);
        if (itemIndex !== -1) {
          list.splice(itemIndex, 1);
        }
      }
      // 编辑时删除文件 需要删除会议与文件的绑定
      await removeAttachments(record.id, props.data.id);
    } else {
      Message.info(t('schedule.meeting.delete'));
    }
  };

  // 下载文件
  const downLoadFile = (file: any) => {
    file.fileToken = file.token;
    downloadSource(file);
  };

  // 生成会议方案
  const addMeetingNotifyHandle = async () => {
    if (props.type === 'view') addAgencyfun(props.data.id);
    else await createMeetingHandle(true, false);
  };

  // 生成会议纪要
  const addMeetingMinutesHandle = async () => {
    // 生成之前需要调用一下保存
    // minutesLoading.value = true;
    // await createMeetingHandle(false, false);
    if (props.type === 'edit') {
      const saveResult = await createMeetingHandle(false, false);
      if (!saveResult) return; // 阻止后续执行
    }
    minutesLoading.value = true;
    const res = await addMeetingMinutes(props.data.id);
    if (res.status) {
      if (res?.data) {
        Message.success(t('schedule.meeting.ai.summary.success'));
        if (
          Array.isArray(res?.data?.attachments) &&
          res.data.attachments.length > 0
        ) {
          minutesList.value = res.data.attachments.filter((item: any) => {
            return item.type === 12;
          });
        }
        // echoHandle(data);
      }
      minutesLoading.value = false;
    } else {
      Message.error(t('schedule.meeting.ai.summary.error'));
      minutesLoading.value = false;
    }
  };

  // 确认框
  const repeatChangeHandle = async (item: any) => {
    repeatVisible.value = false;
    createMeetingHandle(false, true);
  };

  // 取消
  const handleCancel = () => {
    repeatVisible.value = false;
  };

  const disabledEndDate = (current: any) => {
    // 禁用开始时间之前的所有日期
    return (
      form.value.planStartTime &&
      current &&
      current < dayjs(form.value.planStartTime).startOf('day')
    );
  };
  const disabledEndTime = (selectedDate: any) => {
    if (
      !form.value.planStartTime ||
      !selectedDate ||
      !dayjs(selectedDate).isSame(dayjs(form.value.planStartTime), 'day')
    ) {
      return {
        disabledHours: () => [],
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }

    const startTime = dayjs(form.value.planStartTime);
    const selectedDateTime = dayjs(selectedDate);
    const startHour = startTime.hour();
    const startMinute = startTime.minute();

    const disabledHours = () => {
      const hours = [];
      for (let i = 0; i < 24; i++) {
        if (
          i < startHour ||
          (i === startHour && selectedDateTime.date() === startTime.date())
        ) {
          hours.push(i);
        }
      }
      return hours;
    };

    const disabledMinutes = (selectedHour: any) => {
      if (
        selectedHour !== startHour ||
        selectedDateTime.date() !== startTime.date()
      ) {
        return [];
      }

      const minutes = [];
      for (let i = 0; i < startMinute; i++) {
        minutes.push(i);
      }
      return minutes;
    };

    return {
      disabledHours,
      disabledMinutes,
      disabledSeconds: () => [],
    };
  };

  // 删除评论
  const deleteCommentHandle = async (item: any) => {
    const param = {
      username: item.updateBy,
      commentId: item.id,
    };
    console.log(param, 9999);
    const res = await deleteComment(param);
    if (res.status) {
      Message.success(t('schedule.delete.success'));
      const { data } = await getMeetingDetail(props.data.id);
      scheduleStore.setSummaryData(data);
      // 评论回显
      comments.value = data?.comments || [];
    }
  };
  // 切换组织人
  const handleOrganizerChange = (value: any) => {
    console.log('选中的组织人的username:', value);
    // 根据username 去projectUserList里面查到对应的用户信息
    const user = projectUserList.value.filter(
      (item: any) => item.username === value
    );
    console.log('选中的组织人的所有信息:', user);
    // 组织人
    organizerData.value = {
      id: user[0].id,
      name: user[0].name,
      userName: user[0].username,
      userFullname: user[0].name,
    };
    // console.log('选择完组织人之后保存下来的数据', organizerData.value);
    form.value.organizer = organizerData.value.userName; // 表单组织人展示
    // console.log('选择完组织人之后表单绑定的数据', form.value.organizer);
    formRef.value?.validateField('organizer');
  };
  // 选择参与人
  const handleParticipantsChange = (usernames: any) => {
    console.log('选中的参与人的username组成的数组:', usernames);
    // 根据usernames 去projectUserList里面查到对应的用户信息
    const users = projectUserList.value.filter((item: any) =>
      usernames.includes(item.username)
    );
    console.log('选中的参与人的所有信息:', users);
    form.value.participants = users?.map((item: any) => item.username);
    participants.value = {
      id: users.map((item: any) => item.id),
      name: users?.map((item: any) => item.name).join(','),
      userName: users?.map((item: any) => item.username).join(','),
      userFullname: users?.map((item: any) => item.name).join(','),
    };
    formRef.value?.validateField('participants');
  };
  // 切换待办处选人
  const handleUserChange = (item: any, index: any) => {
    console.log('触发了待办里面的人员选择');

    agencyList.value[index].chargePersonId =
      agencyList.value[index].member.join(',');
    // 根据member数组里的username,去item.projectUserList里面查到对应的用户信息,存在list里面备用
    const matchingUsers = projectUserList.value.filter((user: any) =>
      agencyList.value[index].member.includes(user.username)
    );
    console.log(matchingUsers, '匹配到的人员');
    // 从matchingUsers里面提取出name,name组成的数组，放在item.chargePersonName里面
    agencyList.value[index].chargePersonName = matchingUsers
      .map((user: any) => user.name)
      .join(',');
    console.log('匹配的人员', agencyList.value[index]);
    if (item.id) {
      editMatterHandle(agencyList.value[index]);
    }
  };
  const handleDateChange = (item: any, index: number) => {
    console.log('选择的时间', item);
    if (item.id) {
      editMatterHandle(agencyList.value[index]);
    }
  };
  // 监听 currentProjectId 的变化
  watch(
    currentProjectId,
    (newValue) => {
      console.log('判断条件', typeof newValue !== 'string' || !newValue.trim());
      console.log('判断条件', newValue);
      // 只允许非空字符串
      if (typeof newValue !== 'string') return;
      if (
        !newValue ||
        newValue.trim() === '' ||
        newValue === 'undefined' ||
        newValue === 'null'
      )
        return;
      getProjectUserList(newValue);
    },
    { immediate: true } // 初始化时立即执行
  );

  // 表单验证
  const formValidation = async () => {
    // 标题
    if (!props.title) {
      Message.info(t('schedule.inputTitle'));
      return false;
    }
    // 2.待办事项不能存在为空的数据
    const flag = agencyList.value.some(
      (item: any) => !item.title || !item?.member.length
    );
    if (flag) {
      Message.info(t('schedule.meeting.placeholder'));
      return false;
    }
    if (form.value.planStartTime > form.value.planEndTime) {
      Message.info(t('schedule.meeting.time.placeholder'));
      return false;
    }

    const res = await formRef.value?.validate();
    if (res) return false;

    // 新建事项
    const matterRes: any = await addMatter();
    if (!matterRes.status) return false;

    return true;
  };

  const isMeetingChanged = () => {
    const detail = cloneDeep(props.data);
    if (isBasicInfoChanged(detail, props.title, form.value)) return true;

    if (isChildrenChanged(detail.agendas, agencyList.value)) return true;

    if (isCommentChanged(form.value)) return true;

    return false;
  };

  //  二次跳转事项确认框是否显示
  const beforeJumpMeeting = async (record: any) => {
    if (activeTab.value === 'meeting') {
      if (props.type === 'edit') {
        if (isMeetingChanged()) {
          jumpDialog.value = true;
          jumpData.value = record;
        } else {
          handleJump('matters', record.scheduleDetailId);
        }
      } else if (props.type === 'view') {
        handleJump('matters', record.scheduleDetailId);
      }
    } else {
      scheduleStore.setSummaryVisible(true);
      const data = {
        id: record.scheduleDetailId,
        type: 'matters',
      };
      await scheduleStore.setSummaryData(data);
    }
  };

  // 对话框跳转待办事项ok事件
  const jumpDialogOk = async () => {
    if (await formValidation()) {
      if (form.value.description === '') {
        await saveCommentHandle();
      }
      await submitHandle();
      handleJump('matters', jumpData.value.scheduleDetailId);
    } else {
      Message.info('请完善会议信息！');
    }
  };

  // 对话框跳转待办事项cancle事件
  const jumpDialogCancle = () => {
    handleJump('matters', jumpData.value.scheduleDetailId);
  };

  let beforeMoveOkHandler = (
    treeDataPromise: () => Promise<any>,
    teamId: string
  ) => Promise.resolve(true);

  function handleMoveParams(record: AttachTableView[]) {
    // 会议这里暂时只有单个文件另存 因此 moveParamList 赋为 []
    fileStore.setAttachMoveIds({
      attachmentFileIdList: [record?.id],
      moveParamList: [],
    });
  }

  const allSpecifiedFileData = ref<any>([]); // 存储所有指定另存文件夹的文件数据

  // 移动前的设置
  function preMoveSetting(record: any) {
    handleMoveParams(record);

    beforeMoveOkHandler = async function moveRequest(
      treeDataPromise: () => Promise<any>,
      teamIdString: () => string
    ): Promise<boolean> {
      const parentId: string = await treeDataPromise();
      const teamId: string = teamIdString();
      if (!parentId || !teamId) {
        Message.error(t('file-manage.select-target-folder'));
        return false;
      }
      const attachMoveIds = fileStore.attachMoveIds;

      if (props.type === 'new') {
        record.specifiedFolderId = parentId;
        record.teamId = teamId;
        allSpecifiedFileData.value.push(record);
        return true;
      }
      const result: any = await moveAttachment({
        folderId: parentId,
        ...attachMoveIds,
      });

      if (result.code === 8000000) {
        Message.success(t('file-manage.success'));
        return true;
      }

      return false;
    };
    moveModal.value = true;
  }

  onMounted(() => {
    if (globalMode.value === 'project' && teamList.value.length === 0) {
      getTeamList(currentProjectId.value);
    }
  });
</script>

<script lang="ts">
  export default {
    name: 'CategoryMeeting',
  };
</script>

<style lang="less" scoped>
  .meeting-content {
    position: relative;
    padding: 0 20px;
    // height: calc(100vh - 398px);
    overflow: auto;
    :deep(.arco-icon) {
      cursor: pointer;
    }
  }
  .form-style {
    gap: 20px;
    margin-bottom: 20px;
    margin-top: 16px;
  }

  :deep(.arco-form-item-layout-inline) {
    margin-right: 0;
  }

  .block {
    margin: 20px 0 8px 0;
    :deep(.arco-spin) {
      width: 100%;
      min-height: 50px;
    }
    .block-icon {
      width: 16px;
      height: 16px;
    }
    .block-span {
      font-size: 16px;
      // line-height: 24px;
      font-weight: 500;
      margin-right: 16px;
    }
  }
  .agencyList {
    width: 100%;
    :deep(.arco-row) {
      margin-bottom: 8px;
      align-items: center;
    }
    :deep(.arco-input-tag) {
      border: 1px solid #c9cdd4 !important;
    }
    :deep(.arco-select-view-single) {
      border: 1px solid #c9cdd4 !important;
    }
    :deep(.arco-select-view-multiple) {
      border: 1px solid #c9cdd4;
    }
  }

  .file-upload {
    border-radius: 4px;
    border: 1px solid #3366ff;
    // width: 108px;
    height: 26px;
  }
  .file-disabled {
    :deep(.arco-btn-disabled) {
      border: 1px solid rgb(148, 191, 255) !important;
    }
  }

  :deep(
      .arco-list-medium
        .arco-list-content-wrapper
        .arco-list-content
        .arco-list-item
    ) {
    padding: 0 0 12px 0;
    border: 0;
    border-bottom: 1px solid var(--color-neutral-3);
  }

  :deep(.arco-list-bordered) {
    border: 0;
  }
  :deep(.arco-col-8),
  :deep(.arco-col-12) {
    padding-right: 15px;
  }

  .comment {
    position: sticky;
    display: flex;
    height: 70px;
    line-height: 70px;
    left: 0;
    bottom: 0;
    border-top: 1px solid #d9d9d9;
    border-radius: 0 0 8px 8px;
    background-color: #fff;
    padding: 0 20px;
    align-items: center;
    :deep(.arco-checkbox) {
      vertical-align: middle;
    }
  }

  .fileList {
    :deep(.arco-row) {
      height: 36px;
      margin-bottom: 8px;
      background-color: #f7f8fa;
      border-radius: 8px;
      .arco-col {
        line-height: 36px;
        padding-left: 12px;
        background-color: #f7f8fa;
        padding-right: 8px;
        background-color: #f7f8fa;
        img {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          margin-right: 6px;
        }
        .arco-icon {
          cursor: pointer;
          font-size: 12px;
        }
      }
    }
  }

  .block-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    border-bottom: 1px solid #ddd;
    padding: 0 0 12px 0; /* 设置内边距：下16px */
    // :deep(.arco-input-focus) {
    //   border: none;
    // }
    // :deep(.arco-input-wrapper) {
    //   background-color: #fff;
    //   font-size: 20px;
    //   .arco-input {
    //     font-size: 20px;
    //   }
    // }
    .block-title {
      font-size: 20px;
      line-height: 30px;
      color: #1d2129;
      font-weight: 500;
    }
  }
  .comments {
    .comments-input {
      padding: 20px 0;
    }
    :deep(.arco-row) {
      align-items: center;
    }
    .noData {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      img {
        display: block;
        width: 120px;
        height: 112px;
      }
      div {
        margin-top: 16px;
        color: #4e5969;
      }
    }
  }

  .AI-empo {
    margin-top: 16px;
    .ai-title {
      .block-span {
        font-size: 16px !important;
        // line-height: 24px;
        font-weight: 500;
        margin-right: 16px;
      }
    }
    .programme {
      margin-bottom: 16px;
      > div {
        margin-top: 16px;
      }
    }
    .minutes {
      > div {
        margin-top: 16px;
      }
    }
  }
  :deep(.arco-spin) {
    width: 100%;
  }
  .fileName {
    display: inline-block;
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    max-width: calc(100% - 70px);
    vertical-align: middle;
    &:hover {
      color: #3366ff;
    }
  }
  .todo {
    .block {
      > div {
        margin-bottom: 8px;
      }
    }
    :deep(.arco-input-wrapper) {
      border: 1px solid #c9cdd4 !important;
    }
  }

  :deep(.arco-textarea[disabled]) {
    color: #86909c;
    -webkit-text-fill-color: unset;
    cursor: default;
  }

  :deep(.arco-textarea) {
    min-height: 74px;
    border: 1px solid #c9cdd4;
  }
  :deep(.arco-input-tag) {
    max-height: 30px;
  }
  :deep(.arco-textarea-wrapper) {
    border-radius: 8px;
  }
  :deep(.arco-picker),
  :deep(.arco-input-tag),
  :deep(.arco-select-view-single),
  :deep(.arco-textarea),
  :deep(.arco-form-item-content-wrapper) {
    background-color: #fff;
    border-radius: 8px;
  }

  :deep(.arco-form-item-content-wrapper) {
    border: 1px solid #c9cdd4 !important;
  }
  .agencyList {
    :deep(.arco-picker) {
      border: 1px solid #c9cdd4 !important;
    }
  }
  :deep(.arco-select-view),
  :deep(.arco-input-wrapper) {
    border-radius: 8px !important;
    background-color: #fff;
  }
  .describe-icon {
    width: 20px;
    height: 20px;
    vertical-align: middle;
  }
  .meeting-location {
    :deep(.arco-input-wrapper) {
      border: none !important;
    }
  }
  // 查看事项特殊样式
  :deep(.hide-suffix-icon .arco-picker-suffix-icon) {
    visibility: hidden;
  }
  :deep(.arco-textarea[disabled]) {
    cursor: not-allowed !important;
  }
  :deep(.arco-picker input[readonly]) {
    cursor: not-allowed !important;
  }
  // 日期选择器
  :deep(.arco-picker input) {
    color: #1d2129;
  }

  .icon-arrow-right {
    position: absolute;
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    color: #3366ff;
    white-space: nowrap;
    vertical-align: middle;
    margin-left: 8px;
    margin-top: 1px;
  }

  .close-icon {
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
  }
</style>
