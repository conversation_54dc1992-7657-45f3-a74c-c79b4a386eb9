<template>
  <a-modal
    :visible="visible"
    :title="$t('prjMember.table.opt.invite.register')"
    :width="676"
    :unmount-on-close="true"
    :ok-loading="loading"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    :footer="props.type === 'view' ? null : true"
    @cancel="handleCancel"
    @ok="handleBeforeOk"
  >
    <a-form ref="memberRef" :model="form" :disabled="props.type === 'view'">
      <a-row>
        <a-form-item
          field="name"
          :label="$t('prjMember.member-name')"
          validate-trigger="input"
          :rules="[
            {
              required: true,
              message: $t('please-enter'),
            },
          ]"
        >
          <a-input v-model="form.name" :placeholder="$t('please-enter')" />
        </a-form-item>
        <a-form-item
          field="phone"
          :label="$t('prjMember.phone-number')"
          :rules="[
            { required: true, message: $t('login.form.telPlaceholder') },
            {
              pattern: /^(\+\d{1,3})?\d{7,13}$/,
              message: $t('login.form.telInvalid'),
            },
          ]"
          :validate-trigger="['blur', 'change']"
        >
          <a-input
            v-model="form.phone"
            :placeholder="$t('login.form.telPlaceholder')"
            :maxlength="18"
          >
          </a-input>
        </a-form-item>
        <a-form-item
          field="email"
          :label="$t('prjMember.email')"
          :rules="[
            { required: true, message: $t('login.form.emailPlaceholder') },
            {
              pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
              message: $t('login.form.emailInvalid'),
            },
          ]"
          :validate-trigger="['blur', 'change']"
        >
          <a-input
            v-model="form.email"
            :placeholder="$t('login.form.emailPlaceholder')"
            :maxlength="30"
          >
          </a-input>
        </a-form-item>
        <div class="tips">{{
          $t('prjMember.activation-link-will-be-sent-via-sms-and-email')
        }}</div>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch, inject } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { Message } from '@arco-design/web-vue';
  import { invitationGenerate, invitationEdit } from '@/api/member';
  import useLoading from '@/hooks/loading';
  import { useRoute } from 'vue-router';

  const form: any = ref({});

  const { t } = useI18n();
  const { loading, setLoading } = useLoading(false);

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      // default: {},
    },
  });
  const emit = defineEmits(['update:visible', 'refresh']);

  const route = useRoute();

  const handleCancel = () => {
    emit('update:visible', false);
  };

  // 添加新用户
  const addNewUser = async () => {
    // 使用正则表达式验证手机号格式和长度
    const phoneRegex = /^(\+\d{1,3})?\d{7,13}$/;
    if (!phoneRegex.test(form.value.phone)) {
      // 校验手机号格式
      if (!form.value.phone.startsWith('+') && form.value.phone.length !== 11) {
        // 如果没有国际区号且长度不是11位，提示手机号长度错误
        Message.error('请输入正确的手机号');
        console.error('请输入正确的手机号');
      } else {
        // 其他格式错误
        Message.error(t('login.form.telInvalid'));
      }
      return null;
    }
    // 验证码为邮箱
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(form.value.email)) {
      Message.error('请输入正确的邮箱');
      return null;
    }

    setLoading(true);

    // 新增
    let res = null;
    if (props.type === 'create') {
      const params: any = {
        projectId: route.params?.projectId,
        days: 7,
        registerUrl: `${window.location.origin}/work/login?isRegister=1`,
        userInfoList: [
          {
            email: form.value.email,
            name: form.value.name,
            phone: form.value.phone,
          },
        ],
      };
      res = await invitationGenerate(params);
    } else {
      // 编辑
      const params: any = {
        email: form.value.email,
        userFullname: form.value.name,
        phone: form.value.phone,
        id: form.value.id,
      };
      res = await invitationEdit(params);
    }
    return res;
  };

  const memberRef = ref<any>();
  // 刷新邀请记录
  const handleData: any = inject('handleData');

  const handleBeforeOk = async () => {
    const res = await memberRef.value?.validate();
    if (!res) {
      const resData: any = await addNewUser();
      if (resData.status) {
        setLoading(false);
        if (props.type === 'create') {
          Message.success(
            `${t('prjMember.activation-link')} ${resData.data.success} ${t(
              'prjMember.sent-successfully'
            )}, ${resData.data.failure} ${t('prjMember.send-failure')}`
          );
        } else Message.success('编辑成功');
        emit('update:visible', false);
        handleData();
        // emit('refresh');
      }
    }
  };

  watch(
    () => props.visible,
    (n) => {
      if (props.type === 'create') form.value = {};
      else {
        form.value = props.data;
        form.value.name = props.data?.userFullname;
      }
    },
    {
      immediate: true,
    }
  );
</script>

<script lang="ts">
  export default {
    name: 'AddInvited',
  };
</script>

<style scoped lang="less">
  .user-item {
    width: 190px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .tips {
    margin-left: 132px;
    color: #999;
  }
</style>
