<template>
  <a-modal
    :visible="props.visible"
    :title="props.title"
    :width="900"
    :unmount-on-close="true"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-row :gutter="24">
      <a-col :span="12" class="detailTitle">
        <img src="@/assets/images/icon-list.png" alt="" width="16" />
        <span class="titleContent">{{
          $t('user-center.user-detail')
        }}</span></a-col
      >
      <a-col :span="12" class="detailTitle"
        ><img src="@/assets/images/icon-list.png" alt="" width="16" />
        <span class="titleContent">{{
          $t('user-center.product-permissions')
        }}</span></a-col
      >
    </a-row>
    <a-form ref="memberRef" :model="formData" auto-label-width>
      <div class="contentWrap">
        <div style="margin-right: 12px; flex: 1">
          <a-row>
            <a-form-item
              field="name"
              :label="$t('user-center.search.name')"
              validate-trigger="input"
              :disabled="
                (props.currentTitle === 'edit' &&
                  formData.accountStateCode === '1') ||
                props.currentTitle === 'view'
              "
              :rules="[
                {
                  required: true,
                  message: $t('user-center.name-required'),
                },
              ]"
            >
              <remove-spaces-input
                v-model="formData.name"
                :placeholder="$t('user-center.please-enter-name')"
              />
            </a-form-item>
          </a-row>
          <a-row>
            <a-form-item
              field="phone"
              :label="$t('user-center.search.phone')"
              validate-trigger="input"
              :disabled="
                (props.currentTitle === 'edit' &&
                  formData.accountStateCode === '1') ||
                props.currentTitle === 'view'
              "
              :rules="[
                {
                  required: true,
                  message: $t('user-center.phone-required'),
                },
                {
                  match: /^(\+\d{1,3})?\d{7,13}$/, //正则替换  *匹配大陆港澳台
                  message: $t('login.form.telInvalid'),
                },
              ]"
            >
              <remove-spaces-input
                v-model="formData.phone"
                :placeholder="$t('user-center.please-enter-phone')"
                @change="phoneChange"
              />
            </a-form-item>
          </a-row>
          <a-row>
            <a-form-item
              field="email"
              :label="$t('user-center.search.email')"
              validate-trigger="input"
              :disabled="
                (props.currentTitle === 'edit' &&
                  formData.accountStateCode === '1') ||
                props.currentTitle === 'view'
              "
              :rules="[
                {
                  required: true,
                  message: $t('prjMember.email-required'),
                },
                {
                  match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, //邮箱宽松正则替换
                  message: $t('userSetting.form.email.error'),
                },
              ]"
            >
              <remove-spaces-input
                v-model="formData.email"
                :placeholder="$t('user-center.please-enter-email')"
              />
            </a-form-item>
          </a-row>
          <a-row>
            <a-form-item
              field="account"
              :label="$t('user-center.account')"
              validate-trigger="input"
              :disabled="true"
            >
              <remove-spaces-input
                v-model="formData.accountNumber"
                :placeholder="$t('user-center.please-enter-account')"
              />
            </a-form-item>
          </a-row>
          <a-row>
            <a-form-item
              field="secondaryUnit"
              :label="$t('user-center.search.secondary-unit')"
              validate-trigger="change"
              :disabled="
                !(
                  props.currentTitle === 'add' &&
                  props.unit === '' &&
                  userAdmin
                )
              "
              :rules="[
                {
                  required: true,
                  message: $t('secondary-unit.required'),
                },
              ]"
            >
              <a-select
                v-model="formData.secondaryUnit"
                allow-clear
                :placeholder="
                  props.currentTitle === 'view' ? '' : $t('please-select')
                "
                @change="secondaryUnitChange"
              >
                <a-option
                  v-for="item of secondaryUnitOptions"
                  :key="item.id"
                  :value="item.id"
                  :label="item.content?.name"
                />
              </a-select>
            </a-form-item>
          </a-row>
          <a-row>
            <a-form-item
              field="subsidiary"
              :label="$t('user-center.search.subsidiary')"
              validate-trigger="change"
              :disabled="props.currentTitle === 'view'"
            >
              <a-select
                v-model="formData.subsidiary"
                allow-clear
                :placeholder="
                  props.currentTitle === 'view' ? '' : $t('please-select')
                "
              >
                <a-option
                  v-for="item in subsidiaryOptions"
                  :key="item.id"
                  :value="item.id"
                  :label="item.content?.name"
                />
              </a-select>
            </a-form-item>
          </a-row>
          <a-row style="height: 32px">
            <a-form-item
              field="job"
              :label="$t('user-center.job')"
              validate-trigger="input"
              :disabled="props.currentTitle === 'view'"
            >
              <a-input
                v-if="props.currentTitle === 'view'"
                v-model="formData.post"
              />
              <remove-spaces-input
                v-else
                v-model="formData.post"
                :placeholder="$t('please-enter')"
              />
            </a-form-item>
          </a-row>
        </div>
        <div style="margin-left: 12px; flex: 1">
          <div class="detailProduct">
            <!-- <div class="rightTitle">中交BImbo平台</div> -->
            <a-form-item
              field="apply"
              :disabled="
                props.currentTitle === 'edit' || props.currentTitle === 'view'
              "
              hide-label
            >
              <a-checkbox-group
                v-model="formData.application"
                direction="vertical"
                :default-value="['1']"
              >
                <a-checkbox value="1">CDex BIM协同管理系统</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
          </div>
        </div>
      </div>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { useI18n } from 'vue-i18n';
  import { useUserStore } from '@/store';
  import {
    MemberRecord,
    queryUnitTree,
    unitTreeData,
    queryUserDetail,
    updateUserDetail,
    addUser,
  } from '../api';

  const { t } = useI18n();
  const userStore = useUserStore();
  const userAdmin = computed(() => {
    return userStore.admin === 0;
  });
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    unit: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    selectId: {
      type: String,
      default: '',
    },
    currentTitle: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits(['update:visible', 'refresh']);
  const formData = ref<MemberRecord>({});
  const phoneChange = (val: string) => {
    formData.value.accountNumber = val;
  };
  // 绑定form的ref
  const memberRef = ref<FormInstance>();

  const handleBeforeOk = async () => {
    if (props.currentTitle === 'view') {
      emit('update:visible', false);
    } else {
      const res = await memberRef.value?.validate();
      if (!res) {
        const orgObj: unitTreeData =
          (formData.value.subsidiary
            ? subsidiaryOptions.value?.find((item) => {
                return item.id === formData.value.subsidiary;
              })
            : secondaryUnitOptions.value?.find(
                (item) => item.id === formData.value.secondaryUnit
              )) || {};
        const obj = {
          email: formData.value.email,
          name: formData.value.name,
          orgName: orgObj.content?.name || '',
          orgNo: orgObj.content?.orgNo || '',
          pathName: orgObj.content?.pathName || '',
          pathNo: orgObj.content?.pathNo || '',
          phone: formData.value.phone,
          post: formData.value.post,
          username: formData.value.accountNumber,
        };
        if (props.currentTitle === 'add') {
          const res = await addUser({ ...obj });
          if (res) {
            Message.success(t('user-center.success'));
          }
        } else if (props.currentTitle === 'edit') {
          await updateUserDetail({ ...obj, userId: props.selectId });
        }
        emit('update:visible', false);
        emit('refresh');
      }
    }
  };
  const handleCancel = () => {
    emit('update:visible', false);
  };
  // 二级单位下拉框列表数据
  const secondaryUnitOptions = ref<unitTreeData[]>();
  // 子公司下拉框数据
  const subsidiaryOptions = ref<unitTreeData[]>([]);
  // 获取单位树数据
  const getUnitTree = async () => {
    const { data } = await queryUnitTree(true);
    secondaryUnitOptions.value = data[0]?.children || [];
  };
  // 二级单位选值变化
  const secondaryUnitChange = (val: any) => {
    formData.value.subsidiary = '';
    subsidiaryOptions.value =
      secondaryUnitOptions.value?.find((item) => item.id === val)?.children ||
      [];
  };
  // 获取用户详情
  const getUserDetail = async () => {
    const { data } = await queryUserDetail(props.selectId);
    formData.value = {
      name: data.name || '',
      phone: data.phone || '',
      email: data.email || '',
      accountNumber: data.accountNumber || '',
      secondaryUnit: data.pathNo?.split('.')[3] || '',
      subsidiary: data.pathNo?.split('.')[4] || '',
      post: data.post || '',
      accountStateCode: data.accountStateCode || '',
    };
    secondaryUnitChange(data.pathNo?.split('.')[3] || '');
    // 如果二级单位已被删除，构造一个回显数据
    if (
      data.pathNo?.split('.')[3] &&
      !secondaryUnitOptions.value?.find(
        (item) => item.id === data.pathNo?.split('.')[3]
      )
    ) {
      secondaryUnitOptions.value = [
        {
          content: { name: data.pathName?.split('.')[3] },
          id: data.pathNo?.split('.')[3],
        },
      ];
    }
    // 如果子公司已被删除，不展示
    if (
      data.pathNo?.split('.')[4] &&
      subsidiaryOptions.value?.find(
        (item) => item.id === data.pathNo?.split('.')[4]
      )
    ) {
      formData.value.subsidiary = data.pathNo?.split('.')[4] || '';
    }
  };
  onMounted(async () => {
    await getUnitTree();
    if (props.currentTitle === 'edit' || props.currentTitle === 'view') {
      getUserDetail();
    } else if (props.currentTitle === 'add') {
      formData.value.secondaryUnit = props.unit;
      // 如果二级单位已被删除，不回显
      if (
        props.unit &&
        !secondaryUnitOptions.value?.find((item) => item.id === props.unit)
      ) {
        formData.value.secondaryUnit = '';
      }
      secondaryUnitChange(props.unit);
    }
  });
</script>

<script lang="ts">
  export default {
    name: 'AddMember',
  };
</script>

<style lang="less" scoped>
  .detailTitle {
    display: flex;
    padding-bottom: 16px;
    font-size: 16px;
    align-items: center;
    color: #1d2129;
    .titleContent {
      margin-left: 6px;
    }
  }
  .contentWrap {
    display: flex;
    .detailProduct {
      width: 100%;
      height: 100%;
      border: 1px solid #e5e6eb;
      padding: 16px 15px;
      box-sizing: border-box;
      .rightTitle {
        font-size: 16px;
        font-weight: medium;
      }
    }
  }
</style>
