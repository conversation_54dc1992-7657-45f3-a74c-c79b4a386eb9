<template>
  <div class="container">
    <div class="person-base-title base-title" @click="openBase(personBase)">
      <div class="icon-bg"><PersonBaseNew /></div>
      <span class="text">{{ t('knowledgenew.personal-base') }}</span>
    </div>
    <div class="share-base">
      <div class="share-base-title base-title">
        <div class="icon-bg"><ShareBaseNew /></div>
        <span class="text">{{ t('knowledgenew.shared-base') }}</span>
      </div>
      <a-spin :loading="loading" style="height: calc(100% - 60px); width: 100%">
        <div class="share-base-content">
          <a-collapse v-model:active-key="activeKey" :bordered="false">
            <a-collapse-item key="1" :header="t('knowledgenew.I-created')">
              <template #expand-icon="{ active }">
                <icon-down v-if="active" />
                <icon-right v-else />
              </template>
              <template #extra>
                <a-tooltip :content="t('knowledgenew.create-btn')">
                  <icon-plus
                    class="add-base"
                    @click.stop="showCreateDialog = true"
                  />
                </a-tooltip>
              </template>
              <div
                v-for="item in shareOwnerBases"
                :key="item.id"
                class="base-card"
                @click="openBase(item)"
              >
                <div class="card-top">
                  <img
                    v-if="item.picUrl"
                    :src="
                      '/work/api/sys-storage/download_image?f8s=' + item.picUrl
                    "
                  />
                  <DefaultCover v-else />

                  <div class="text-container">
                    <a-typography-paragraph
                      :ellipsis="{
                        rows: 1,
                        showTooltip: true,
                      }"
                      class="text-title"
                      >{{ item.name }}
                    </a-typography-paragraph>
                    <a-typography-paragraph
                      :ellipsis="{
                        rows: 2,
                        showTooltip: true,
                      }"
                      class="text-desc"
                      >{{ item.description }}
                    </a-typography-paragraph>
                  </div>
                </div>
                <a-divider
                  :margin="8"
                  style="margin-top: 14px; border-bottom: 1px solid #d9d9d9"
                />
                <div class="card-bottom">
                  <span class="username">{{
                    t('knowledgenew.created-by', {
                      name: item.creatorName || '',
                    })
                  }}</span>
                  <span
                    v-if="item.owner || item.share"
                    class="share-btn"
                    @click.stop="shareBase(item)"
                    >{{ t('knowledgenew.share') }}</span
                  >
                </div>
              </div>
            </a-collapse-item>
            <a-collapse-item key="2" :header="t('knowledgenew.I-joined')">
              <template #expand-icon="{ active }">
                <icon-down v-if="active" />
                <icon-right v-else />
              </template>
              <div
                v-for="item in shareJoinBases"
                :key="item.id"
                class="base-card"
                @click="openBase(item)"
              >
                <div class="card-top">
                  <img
                    v-if="item.picUrl"
                    :src="
                      '/work/api/sys-storage/download_image?f8s=' + item.picUrl
                    "
                  />
                  <DefaultCover v-else />

                  <div class="text-container">
                    <a-typography-paragraph
                      :ellipsis="{
                        rows: 1,
                        showTooltip: true,
                      }"
                      class="text-title"
                      >{{ item.name }}
                    </a-typography-paragraph>
                    <a-typography-paragraph
                      :ellipsis="{
                        rows: 2,
                        showTooltip: true,
                      }"
                      class="text-desc"
                      >{{ item.description }}
                    </a-typography-paragraph>
                  </div>
                </div>
                <a-divider
                  :margin="8"
                  style="margin-top: 14px; border-bottom: 1px solid #d9d9d9"
                />
                <div class="card-bottom">
                  <span class="username">{{
                    t('knowledgenew.created-by', {
                      name: item.creatorName || '',
                    })
                  }}</span>
                  <span
                    v-if="item.owner || item.share"
                    class="share-btn"
                    @click.stop="shareBase(item)"
                    >{{ t('knowledgenew.share') }}</span
                  >
                </div>
              </div>
            </a-collapse-item>
          </a-collapse>
          <div
            v-if="
              init &&
              shareOwnerBases.length === 0 &&
              shareJoinBases.length === 0
            "
            class="empty-base"
          >
            <EmptyBase class="image" />
            <span>{{ t('knowledgenew.no-base-created-or-joined') }}</span>
          </div>
        </div>
      </a-spin>
    </div>
    <BaseInfoDialog
      v-model:visible="showCreateDialog"
      @submit="getKnowledgeBaseList"
    />
    <ShareBaseDialog
      v-model:visible="showShareDialog"
      :data="selectedBaseInfo"
    />
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import i18n from '@/locale/index';
  import { useKnowledgeBaseStore2 } from '@/store';
  import { getBaseList } from '../../api';
  import { KnowledgeBaseRecord } from '../../types';
  import BaseInfoDialog from './base-info-dialog.vue';
  import ShareBaseDialog from './share-base-dialog.vue';
  import PersonBaseNew from '@/assets/images/knowledge-base/person-base-new.svg';
  import ShareBaseNew from '@/assets/images/knowledge-base/share-base-new.svg';
  import EmptyBase from '@/assets/images/knowledge-base/empty-base.svg';
  import DefaultCover from '@/assets/images/knowledge-base/default-cover.svg';

  const { t } = i18n.global;

  const knowledgeBaseStore2 = useKnowledgeBaseStore2();

  const router = useRouter();
  const init = ref(false); // 是否初始化完成
  const loading = ref(false);

  // 选中打开的/分享的知识库信息
  const selectedBaseInfo = ref();

  const personBase = ref();
  const shareOwnerBases = ref<KnowledgeBaseRecord[]>([]); // 我创建的
  const shareJoinBases = ref<KnowledgeBaseRecord[]>([]); // 我加入的

  // 折叠面板展开项
  const activeKey = ref<string[]>([]);

  const initActiveKey = () => {
    if (init.value) {
      if (shareOwnerBases.value.length) {
        activeKey.value.push('1');
      }
      if (shareJoinBases.value.length) {
        activeKey.value.push('2');
      }
    }
  };

  // 打开知识库
  const openBase = (base: KnowledgeBaseRecord) => {
    knowledgeBaseStore2.initData();
    knowledgeBaseStore2.setBaseInfo(base);
    let routeName = 'personalBase';
    if (base.type === 'SHARED') {
      routeName = 'sharedBase';
    }
    router.push({
      name: routeName,
      params: {
        kbId: base.id,
        rootFolderId: base.folderVO?.id,
      },
    });
  };

  // 查询知识库列表
  const getKnowledgeBaseList = async () => {
    loading.value = true;
    shareOwnerBases.value = [];
    shareJoinBases.value = [];
    try {
      const res = await getBaseList({
        pageParam: { pageNo: 1, pageSize: 1000 },
      });

      if (res.status) {
        res.data.list.forEach((base: KnowledgeBaseRecord) => {
          if (base.type !== 'PERSONAL') {
            if (base.owner) {
              shareOwnerBases.value.push(base);
            } else {
              shareJoinBases.value.push(base);
            }
          } else {
            personBase.value = base;
          }
        });
        init.value = true;
        initActiveKey();
      }
    } catch (err) {
      console.error(err);
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    getKnowledgeBaseList();
  });

  // 创建共享知识库
  const showCreateDialog = ref(false);

  // 打开分享弹窗
  const showShareDialog = ref(false);
  const shareBase = (base: KnowledgeBaseRecord) => {
    selectedBaseInfo.value = base;
    showShareDialog.value = true;
  };
</script>

<style scoped lang="less">
  .container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .base-title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;

    .icon-bg {
      padding: 6px;
      width: 32px;
      height: 32px;
      border-radius: 4px;
    }

    .text {
      margin-left: 12px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 24px;
    }
  }

  .person-base-title {
    margin-bottom: 10px;
    // border: 1px solid #d9d9d9;
    // border-radius: 8px;
    // background: linear-gradient(180deg, #ffffff 0%, #f2f7ff 59%, #e8f2ff 100%);
    background-image: url('../../../../assets/images/knowledge-base/person-base-bg.png');
    cursor: pointer;
    .icon-bg {
      background: rgb(89 203 252 / 25%);
    }
  }

  .share-base-title {
    border-bottom: 1px solid #d9d9d9;
    border-radius: 8px 8px 0 0;
    .icon-bg {
      background: rgba(108, 108, 234, 0.25);
    }
  }

  .share-base {
    flex: 1;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    background-image: url('../../../../assets/images/knowledge-base/share-base-bg.png');
    background-repeat: no-repeat;
    overflow: hidden;
  }

  .share-base-content {
    padding-bottom: 10px;
    height: 100%;
    overflow: auto;
  }

  .list-container {
    padding: 14px 16px 6px;
    .title {
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
    }
  }

  .add-base {
    font-size: 16px;
    color: #4e5969;
    cursor: pointer;
  }
  .add-base:hover {
    background-color: #f2f3f5;
    border-radius: 2px;
  }

  .empty-base {
    display: flex;
    flex-direction: column;
    align-items: center;
    .image {
      margin: 78px 0 13px;
    }
    span {
      color: #4e5969;
      line-height: 24px;
    }
  }
  .base-card {
    margin-bottom: 16px;
    padding: 16px 16px 12px 16px;
    height: 136px;
    background: linear-gradient(175deg, #ffffff 0%, #e8f2ffad 100%);
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    cursor: pointer;
  }
  .base-card:last-of-type {
    margin-bottom: 0;
  }
  .card-top {
    display: flex;
    img,
    svg {
      margin-top: 2px;
      margin-right: 10px;
      width: 60px;
      height: 60px;
      border-radius: 8px;
    }
  }

  .text-container {
    flex: 1;
    .text-title {
      margin-bottom: 0;
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
      line-height: 24px;
    }
    .text-desc {
      margin-bottom: 0;
      height: 40px;
      font-size: 14px;
      color: #4e5969;
      line-height: 20px;
    }
  }
  .card-bottom {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 20px;

    .username {
      color: #4e5969;
    }

    .share-btn {
      color: #3366ff;
    }
  }

  // 折叠面板样式覆盖
  :deep(.arco-collapse) {
    border-left: none;
    border-right: none;
  }
  :deep(.arco-collapse-item) {
    border-bottom: none;
  }
  :deep(.arco-collapse-item-header) {
    padding-top: 12px;
    padding-bottom: 12px;
    border-bottom: none;
  }
  :deep(.arco-collapse-item-content) {
    padding-left: 16px;
    padding-right: 16px;
    background-color: white;
  }
  :deep(.arco-collapse-item-header-title) {
    font-size: 16px;
  }
  :deep(.arco-collapse-item-header-left) {
    padding-right: 16px;
  }
  :deep(.arco-collapse-item-content-box) {
    padding: 0;
  }
</style>
