<template>
  <div class="home-page">
    <div class="content">
      <div class="tab-buttons">
        <button
          :class="{ active: activeTab === '1' }"
          @click="changeTab('1')"
          >{{ $t('home.tab.home') }}</button
        >
        <button
          v-show="companyId"
          :class="{ active: activeTab === '2' }"
          @click="changeTab('2')"
          >{{ $t('home.tab.project-list') }}</button
        >
        <button
          v-permission = "$btn.system.projectTemplate"
          v-show="companyId"
          :class="{ active: activeTab === '3' }"
          @click="changeTab('3')"
          >{{ $t('dashboard.project-template') }}</button
        >
      </div>
      <div class="tab-content">
        <div v-if="activeTab === '1'">
          <div class="home-page-layout">
            <!-- 左侧展示区域 -->
            <LeftPanel />
            <!-- 右侧ai对话区域 -->
            <RightAIChat />
          </div>
        </div>
        <div v-if="activeTab === '2'">
          <ProjectList />
        </div>
        <div v-if="activeTab === '3'">
          <ProjectTemplate />
        </div>
                <!-- <a-tab-pane
          key="3"
          :title="$t('dashboard.project-template')"
          style="height: 100%"
        >
          <ProjectTemplate></ProjectTemplate>
        </a-tab-pane> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, computed, watch } from 'vue';
  import LeftPanel from './components/left-panel/index.vue';
  import RightAIChat from './components/right-chat.vue';
  import ProjectList from './components/project-list/project-list.vue';
  import ProjectTemplate from './components/project-template/project-template.vue';
  import { useUserStore } from '@/store';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  // const globalModeStore = useGlobalModeStore();
  const userStore = useUserStore();
  const companyId = computed(() => userStore.companyId);

  const activeTab = ref('1');
  // 切换的时候缓存activeTab值，项目默认是'1'
  const changeTab = (value: string) => {
    if (value) {
      activeTab.value = value;
      sessionStorage.setItem('homePageActiveTab', value);
    }
  };

  // 从缓存中获取activeTab值
  const init = () => {
    const tab = sessionStorage.getItem('homePageActiveTab');
    if (!companyId.value) {
      activeTab.value = '1';
      changeTab(activeTab.value);
      return;
    }
    if (tab) {
      activeTab.value = tab;
    }
    changeTab(activeTab.value);
  };

  watch(companyId, () => {
    init();
  });
  onMounted(() => {
    // globalModeStore.changeGlobalMode('work');
    init();
  });
</script>

<style lang="less" scoped>
  .home-page {
    width: 100%;
    height: 100%;
    // overflow: hidden;
    // height: 100vh;
    width: 100vw;
    background-color: #f5f6fb;
  }
  .content {
    padding: 22px 20px 20px;
    margin-left: 20px;
    border-radius: 16px 0 0 16px;
    height: 100vh;
    background-color: white;
  }
  :deep(.arco-tabs-nav-tab-list) {
    .arco-tabs-tab {
      margin: 0 18px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
  .tab-buttons {
    font-size: 16px;
    display: flex;
    margin-bottom: 18px;
    button {
      color: #4e5969;
      height: 36px;
      text-align: center;
      margin-right: 12px;
      border-radius: 100px 100px 100px 100px;
      padding: 0 16px;
      border: none;
      background-color: transparent;
      cursor: pointer;

      &.active {
        background-color: #f2f3f5;
        color: #3366ff;
      }
    }
  }
  .home-page-layout {
    display: flex;
    height: 100vh;
    overflow: hidden;
  }
  .tab-content {
    overflow: hidden;
    border-radius: 4px;
    margin-top: -1px;
  }
</style>
