import { mergeConfig } from 'vite';
import baseConfig from './vite.config.base';
// import configCompressPlugin from './plugin/compress';
import configVisualizerPlugin from './plugin/visualizer';
import configArcoResolverPlugin from './plugin/arcoResolver';
import configStyleImportPlugin from './plugin/styleImport';
import configImageminPlugin from './plugin/imagemin';
// import { resolve } from 'path';

export default mergeConfig(
  {
    mode: 'production',
    plugins: [
      // configCompressPlugin('gzip'),
      configVisualizerPlugin(),
      configArcoResolverPlugin(),
      configStyleImportPlugin(),
      configImageminPlugin(),
    ],
    build: {
      rollupOptions: {
        // 资源分类打包
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
        },
      },
      // rollupOptions: {
      //   output: {
      //     manualChunks: {
      //       arco: ['@arco-design/web-vue'],
      //       chart: ['echarts', 'vue-echarts'],
      //       vue: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'vue-i18n'],
      //     },
      //   },
      // },
      chunkSizeWarningLimit: 2000,
    },
  },
  baseConfig
);
