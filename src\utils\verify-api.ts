// 校验URL放行
import type { AxiosRequestConfig } from 'axios';
import { sign, cMsg, ignore } from '@/directionary/request-list';

function urlPattern(url = '') {
  let nurl = url.replaceAll('*', '(.)*').replaceAll('(.)*(.)', '*(.)');
  nurl += '$';
  return nurl;
}

export function verifyUrl(config: AxiosRequestConfig, type: string) {
  if (!config) return false;
  const array = type === 'sign' ? sign : cMsg;
  const url = config.url || '';
  return array.find((item:any) => {
    // return new RegExp(urlPattern(item)).test(url);
    return item === url;
  });
}
export function verifyRequest(config: AxiosRequestConfig) {
  const url = config.url || '';
  return !ignore.find((item:any) => {
    return (
      new RegExp(urlPattern(item.url)).test(url) &&
      (item.httpMethod === '*' || item.httpMethod === config.method) &&
      item.scope !== 'res'
    );
  });
}

export function verifyResponse(config: AxiosRequestConfig) {
  const url = config.url || '';
  return !ignore.find((item:any) => {
    return (
      new RegExp(urlPattern(item.url)).test(url) &&
      (item.httpMethod === '*' || item.httpMethod === config.method) &&
      item.scope !== 'req'
    );
  });
}
