<template>
  <div style="margin: auto; width: 60%; padding: 20px">
    <a-card
      v-for="(item, index) in fileList"
      :key="index"
      :title="
        index === 0
          ? $t('model-collaboration.modelA')
          : $t('model-collaboration.modelB')
      "
      :bordered="false"
      style="width: 100%; text-align: left"
    >
      <template #extra>
        <a-link
          :disabled="setTreeDisabled(index)"
          @click="setTreeFolderVisible(index)"
          >{{ $t('model-collaboration.selectFile') }}</a-link
        >
      </template>
      <div v-if="item?.graphicEngineInfo">
        <file-image
          :file-name="item.name"
          :is-file="true"
          style="width: 40px; height: 40px; border-radius: 4px; margin-top: 6px"
        />
        <div class="file-text">
          <div class="file-name"
            >{{ $t('model-collaboration.fileName') }}: {{ item.name }}</div
          >
          <span class="file-version"
            >{{ $t('model-collaboration.version') }}:
            {{ 'V' + item.version }}</span
          >
        </div>
        <span class="icon-delete" @click="delFile(index)"
          ><icon-delete size="18"
        /></span>
      </div>
    </a-card>

    <TreeFolder
      v-model:visible="treeFolderVisible"
      check-type="single"
      :title="$t('model-collaboration.selectFile')"
      :ok-function="fileChange"
      :show-type="['modelFile']"
      :checked-data="fileIdList"
      :show-sys-folder="[3]"
      is-clear-key
    ></TreeFolder>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import TreeFolder from '@/components/tree-folder/index.vue';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import useUserStore from '@/store/modules/user';
  import { useRoute } from 'vue-router';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const fileList: any = ref([{}, {}]);
  // 当前所选文件下下标
  const fileListNow: any = ref(0);

  const teamId = ref<string>();
  const emit = defineEmits(['change']);

  const treeFolderVisible = ref(false);

  // 设置选择文件按钮禁用
  const setTreeDisabled = (index: any) => {
    // 两个模型的选择按钮 第一个未选择 第二个不允许选
    if (index === 1) {
      if (
        Object.keys(fileList.value[0])?.length === 0 &&
        Object.keys(fileList.value[1])?.length === 0
      )
        return true;
      return false;
    }
    return false;
  };

  // 判断是否是语义模型
  const isSemanticModel = (data: any) => {
    // const allowFile = ['rvt', 'ifc', 'dgn', 'nwd']; // 碰撞检测支持的类型
    const allowFile = ['rvt', 'ifc', 'dgn']; // 碰撞检测支持的类型
    const parts = data.name.split('.');
    const type = parts[parts.length - 1];
    if (allowFile.includes(type)) return true;
    Message.error(
      `${data.name} ${t('model-collaboration.not-a-semantic-model')} `
    );
    return false;
  };

  // 打开选择文件弹窗
  const setTreeFolderVisible = (val: any) => {
    treeFolderVisible.value = true;
    fileListNow.value = val;
  };

  const fileChange = async (data: () => Promise<any>) => {
    const files = await data();
    if (isSemanticModel(files) || files.name.split('.')[1] === 'asm') {
      fileList.value[fileListNow.value] = files || undefined;
      emit('change', {
        changed: false,
        files: fileList.value[fileListNow.value],
        teamId: teamId.value,
        number: fileListNow.value,
      });
    }
  };

  const route = useRoute();
  const projectStore = usePrjPermissionStore();
  const userStore = useUserStore();
  const projectId = (route?.params.projectId || '') as string;
  const teamStorageKey = `first-${projectId}-${userStore.id}`;

  const currentTeamId = computed(() => {
    let result = '';
    const localStorageId = localStorage.getItem(teamStorageKey);
    const isMyTeam = projectStore.teamList.findIndex(
      (item) => item.id === localStorageId
    );
    if (projectStore.teamList.length === 0) {
      localStorage.removeItem(teamStorageKey);
    } else if (localStorageId && isMyTeam !== -1) {
      result = localStorageId;
    } else {
      result = projectStore.teamList[0].id;
      localStorage.setItem(teamStorageKey, result);
    }
    return result;
  });

  const getIdByFileList = (fileList2: any[]) => {
    const ids: any[] = [];
    fileList2?.forEach((item) => {
      if ('folderId' in item) {
        ids.push(item.id);
      }
      if (item.children) {
        ids.push(...getIdByFileList(item.children));
      }
    });
    return ids;
  };

  // 文件树回显
  const fileIdList = computed(() => {
    const data = { ...fileList.value[fileListNow.value] };
    const result = getIdByFileList([data]);
    return result;
  });

  // 文件删除
  const delFile = (delIndex: number) => {
    // 若第二个文件已选择 第一个文件禁止删除
    if (Object.keys(fileList.value[1])?.length !== 0 && delIndex === 0) {
      Message.error('模型B已选择，模型A禁止删除');
      return;
    }
    fileList.value[delIndex] = {};
    // 若删除第一个 下一步禁用
    const disabledValue = Object.keys(fileList.value[0]).length === 0;
    emit('change', { changed: disabledValue, delIndex });
  };
</script>

<style scoped lang="less">
  .file-text {
    float: right;
    width: calc(100% - 75px);
    //border: 1px solid red;
    margin-right: 20px;
    margin-top: 2px;
    .file-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 22px;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN, serif;
      font-weight: 400;
      color: var(--color-text-1);
      line-height: 22px;
    }
    .file-version {
      //height: 20px;
      background: #e8fffb;
      border-radius: 4px;
      opacity: 1;
      border: 1px solid #0fc6c2;
      //padding: 4px;
      padding-left: 6px;
      padding-right: 6px;
      color: #0fc6c2;
      font-size: 12px;
      margin-top: 4px;
      line-height: 20px;
    }
  }
  .icon-delete {
    position: absolute;
    right: 0;
    top: 70px;
    cursor: pointer;
  }
</style>
