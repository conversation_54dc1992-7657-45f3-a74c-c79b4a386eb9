<template>
  <div class="file-tree">
    <div class="tree-header">
      <a-input-search
        v-model="searchValue"
        size="small"
        :placeholder="$t('knowledgenew.search-placeholder')"
        allow-clear
        @blur="searchFile"
        @press-enter="($event.target as any)?.blur()"
        @search="searchFile"
      />
      <FolderAddBtns
        v-if="hasUploadPermission || hasEditPermission"
        :is-root="true"
        :node-data="allData"
        @events-handle="eventsHandler"
      ></FolderAddBtns>
    </div>
    <div class="tree-content">
      <a-spin :loading="loading" style="width: 100%; height: 100%">
        <div v-if="treeData.length === 0" class="empty-file">
          <EmptyFolder></EmptyFolder>
          <span>{{ $t('knowledgenew.empty-content') }}</span>
        </div>
        <a-tree
          ref="folderTreeRef"
          v-model:selected-keys="selectedKeys"
          :data="treeData"
          :field-names="{
            key: 'id',
            title: 'name',
          }"
          default-expand-all
          block-node
          size="large"
        >
          <template #switcher-icon="node">
            <IconDown v-if="!node.isLeaf" :size="16" style="color: #86909c" />
          </template>
          <template #icon="node">
            <img
              :src="getFileIcon(node.node.type)"
              style="width: 20px; height: 20px"
            />
          </template>
          <template #title="nodeData">
            <a-input
              v-if="nodeData.isNewFolder"
              ref="addInputRef"
              v-model="newFolderName"
              :max-length="255"
              style="width: 100%"
              :placeholder="$t('knowledgenew.input-placeholder')"
              allow-clear
              @click.stop.prevent
              @blur="addFolderRequest(nodeData)"
              @press-enter="($event.target as any)?.blur()"
            />
            <a-input
              v-else-if="nodeData.isEdit"
              ref="editInputRef"
              v-model="newFolderName"
              :max-length="
                nodeData.type === 'folder' ? 255 : 200 - fileExt?.length
              "
              style="width: 100%"
              :placeholder="$t('knowledgenew.input-placeholder')"
              allow-clear
              @click.stop.prevent
              @blur="handleRename(nodeData)"
              @press-enter="($event.target as any)?.blur()"
            />
            <div v-else class="title-container">
              <div class="tree-title-text">
                <span @click="editFile(nodeData)">{{ nodeData.name }}</span>
              </div>
              <div v-if="nodeData.type === 'folder'" class="tree-title-desc">
                {{
                  $t('knowledgenew.folder-item-count', {
                    count: nodeData.childFileCnt || 0,
                  })
                }}
                <a-divider
                  direction="vertical"
                  :margin="8"
                  style="border-color: #c9cdd4; font-size: 16px"
                />{{ getDate(nodeData.updateDate) }}
              </div>
              <div v-else class="tree-title-desc">
                {{
                  $t(`knowledgenew.file-status-${nodeData.aiStatus}`) ||
                  $t('knowledgenew.file-status-default')
                }}
                <a-divider
                  direction="vertical"
                  :margin="8"
                  style="border-color: #c9cdd4; font-size: 16px"
                />{{ getDate(nodeData.updateDate) }}
              </div>
            </div>
          </template>
          <template #extra="nodeData">
            <div
              v-if="
                !nodeData.isNewFolder &&
                !nodeData.isEdit &&
                selectedKeys.includes(nodeData.id)
              "
              class="folder-file-extra"
            >
              <a-space :size="12">
                <FolderAddBtns
                  v-if="
                    nodeData.type === 'folder' &&
                    (hasUploadPermission || hasEditPermission)
                  "
                  :node-data="nodeData"
                  @events-handle="eventsHandler"
                ></FolderAddBtns>
                <MoreBtns
                  v-if="hasEditPermission"
                  :node-data="nodeData"
                  @events-handle="eventsHandler"
                ></MoreBtns>
              </a-space>
            </div>
          </template>
        </a-tree>
      </a-spin>
    </div>
    <a-upload
      action="/"
      multiple
      :show-file-list="false"
      :auto-upload="false"
      :file-list="selectedFileList"
      @change="beforeUpload"
    >
      <template #upload-button>
        <div ref="uploadBtnRef"></div>
      </template>
    </a-upload>
    <UploadPanel
      v-model:visible="uploadProcessModalVisible"
      :files="selectedFileList"
      :position="{
        top: 210,
        right: 70,
      }"
      :show-success-msg="false"
      @upload-single-success="uploadSuccessCb"
      @finish="selectedFileList = []"
    ></UploadPanel>
    <ImportFromDisk
      v-model:visible="importFileVisible"
      :parent="currentData"
      @refresh="treeRefresh"
    />
    <MoveFileModal
      v-model:visible="moveFileVisible"
      :files="moveFiles"
      @move-file-success="moveFileSuccess"
      @refresh="treeRefresh"
    ></MoveFileModal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, nextTick, ref, toRaw, onMounted } from 'vue';
  import { useThrottleFn } from '@vueuse/core';
  import { useI18n } from 'vue-i18n';
  import { storeToRefs } from 'pinia';
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { useUploadFileStore, useKnowledgeBaseStore2 } from '@/store';
  import { wpsViewHandle } from '@/hooks/wps';
  import UploadPanel from '@/components/upload-panel/index.vue';
  import FolderAddBtns from './folder-add-btns.vue';
  import MoveFileModal from './move-file-modal.vue';
  import ImportFromDisk from './import-from-disk.vue';
  import MoreBtns from './more-btns.vue';
  import useFolderContent from '../../composables/useFolderContent';
  import getFileType from '../../utils';
  import acceptFileType from '../../json/knowledge-accept-file-types.json';
  import { createFolder, saveFile, getBaseList } from '../../api';
  import { Node, CustomFileItem } from '../../types';
  import EmptyFolder from '@/assets/images/knowledge-base/empty-folder.svg';

  const { t } = useI18n();
  const uploadFileStore = useUploadFileStore();
  const knowledgeBaseStore2 = useKnowledgeBaseStore2();
  const { isEditing, selectedKeys, baseInfo } =
    storeToRefs(knowledgeBaseStore2);

  const {
    checkFolderName,
    checkFileName,
    renameFileOrFolder,
    queryFolderContent,
    querySearchFile,
    downloadBtnClick,
    getFileIcon,
    deleteFolderOrFile,
  } = useFolderContent();

  const route = useRoute();
  const { kbId } = route.params;

  const folderTreeRef = ref<any>();

  // 是否有查看权限
  const hasViewPermission = computed(() => {
    return (
      baseInfo.value?.type === 'PERSONAL' ||
      baseInfo.value?.owner ||
      baseInfo.value?.view
    );
  });

  // 是否有上传权限
  const hasUploadPermission = computed(() => {
    return (
      baseInfo.value?.type === 'PERSONAL' ||
      baseInfo.value?.owner ||
      baseInfo.value?.upload
    );
  });

  // 是否有编辑权限
  const hasEditPermission = computed(() => {
    return (
      baseInfo.value?.type === 'PERSONAL' ||
      baseInfo.value?.owner ||
      baseInfo.value?.edit
    );
  });

  // 知识库全量数据
  const allData = ref<Node>();
  // 树展示数据（根文件夹不展示）
  const treeData = computed(() => {
    return allData.value?.children || [];
  });

  // TODO 是否默认展开全部？
  const folderDataReady = ref(false);
  const loading = ref(false);

  // 初始化，查询知识库内容
  const init = async () => {
    loading.value = true;
    allData.value = await queryFolderContent();
    loading.value = false;
    if (allData.value?.childFileCnt) {
      knowledgeBaseStore2.setHasFile(true);
    } else {
      knowledgeBaseStore2.setHasFile(false);
    }
    folderDataReady.value = true;
  };

  onMounted(() => {
    init();
  });

  // 刷新数据
  async function treeRefresh() {
    loading.value = true;
    allData.value = await queryFolderContent();
    loading.value = false;
  }

  // 搜索
  const searchValue = ref(''); // 本次搜索的值，与上次不同时，才触发搜索
  const oldSearchValue = ref(''); // 上次搜索的值

  const searchFile = async () => {
    selectedKeys.value = [];
    if (
      searchValue.value.trim() &&
      searchValue.value?.trim() !== oldSearchValue.value
    ) {
      loading.value = true;
      const fileList = await querySearchFile({
        fileName: searchValue.value,
        kbId: kbId as string,
      });
      loading.value = false;
      if (allData.value) {
        allData.value.children = fileList;
        oldSearchValue.value = searchValue.value?.trim();
      }
    } else if (!searchValue.value && oldSearchValue.value) {
      oldSearchValue.value = '';
      treeRefresh();
    }
  };

  // 文件夹重命名
  const newFolderName = ref('');
  const fileExt = ref(''); // 文件后缀
  const addInputRef = ref();
  const editInputRef = ref();

  // 操作对象
  const currentData = ref();

  // 添加文件夹-新增节点
  const handleAddFolder = (folder: Node) => {
    if (isEditing.value) {
      Message.info(t('knowledgenew.handle-current-edit'));
      return;
    }
    knowledgeBaseStore2.setIsEditting(true);

    newFolderName.value = '';

    const item = {
      id: 'add',
      name: '',
      parentId: folder.id || '',
      type: 'folder',
      childFileCnt: 0,
      isNewFolder: true,
    };
    if (folder.children) {
      folder.children.push(item);
    } else {
      folder.children = [item];
    }
    currentData.value = folder;
    nextTick(() => {
      folderTreeRef.value.expandNode(folder.id);
    }).then(() => {
      addInputRef.value.focus();
    });
  };

  // 添加文件夹
  const addFolderRequest = useThrottleFn(async (item: Node) => {
    delete item.id;
    const name = newFolderName.value.trim();

    // 名称为空校验
    if (name === '') {
      if (currentData.value && currentData.value.children) {
        currentData.value.children.pop();
      }
      knowledgeBaseStore2.setIsEditting(false);
      Message.info(t('knowledgenew.cancel-create-folder'));
      return;
    }

    const isNameValid = await checkFolderName(name, item.parentId);
    if (!isNameValid) {
      return;
    }

    try {
      const res = await createFolder({
        name,
        parentId: item.parentId,
      });
      if (res.status) {
        Message.success(t('knowledgenew.create-success'));
        knowledgeBaseStore2.setIsEditting(false);
        treeRefresh();
      }
    } catch (error) {
      Message.error(t('knowledgenew.operate-fail-msg'));
      console.error('Folder operation error:', error);
    }
  }, 1000);

  // 查询知识库信息
  const getKnowledgeBaseInfo = async () => {
    try {
      const res = await getBaseList({
        pageParam: { pageNo: 1, pageSize: 1000 },
      });

      if (res.status) {
        res.data.list?.find((base: any) => {
          if (base.id === kbId) {
            knowledgeBaseStore2.setBaseInfo(base);
            return true;
          }
          return false;
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  /** 上传文件begin */
  const uploadProcessModalVisible = ref(false);
  const uploadBtnRef = ref<HTMLDivElement | null>(null);
  // 上传文件列表
  const selectedFileList = ref([]);
  const fileSizeLimit = 30; // 单位MB
  // 是否是根节点上传
  const isRootOpt = ref(false);

  // 上传文件
  function handleUpload(data: Node) {
    if (data.root) {
      isRootOpt.value = true;
    } else {
      isRootOpt.value = false;
    }
    if (uploadFileStore.uploadFileList.length) {
      uploadProcessModalVisible.value = true;
    } else {
      uploadBtnRef.value?.click();
    }
  }

  // 上传前校验
  // eslint-disable-next-line consistent-return
  const beforeUpload = (fileArr: any, fileObj: any) => {
    // 校验的逻辑放到循环外，避免校验失败多次提示
    const { file } = fileObj;
    const suffix = file.name.split('.').pop()?.toLowerCase() || '';
    // 文件类型验证
    if (!acceptFileType.includes(suffix)) {
      Message.error(t('knowledgenew.upload-file-type-limit'));
      fileObj.checkFailed = true;
      return false;
    }
    // 文件大小验证
    if (file.size > fileSizeLimit * 1024 * 1024) {
      Message.error(
        `${t('knowledgenew.file-size-no-exceed')} ${fileSizeLimit} MB！`
      );
      fileObj.checkFailed = true;
      return false;
    }
    // 文件名称验证
    if (file.name.length > 200) {
      Message.error(t('knowledgenew.file-name-length-no-exceed'));
      fileObj.checkFailed = true;
      return false;
    }
    if (file.name.includes(' ')) {
      Message.error(t('knowledgenew.file-name-no-space'));
      fileObj.checkFailed = true;
      return false;
    }

    const files = fileArr.map((fileItem: any) => {
      if (fileItem.checkFailed) {
        return null;
      }
      const fileA = fileItem.file;
      // 为文件添加parentId属性
      if (isRootOpt.value) {
        fileA.parentId = allData.value?.id;
      } else {
        fileA.parentId = selectedKeys.value.length ? selectedKeys.value[0] : '';
      }
      return fileA;
    });
    selectedFileList.value = files.filter((item: any) => {
      let result = false;
      if (item) result = true;
      return result;
    });

    if (!uploadProcessModalVisible.value && selectedFileList.value.length)
      uploadProcessModalVisible.value = true;
  };

  // 文件保存逻辑
  const handleSaveFile = async (fileItem: CustomFileItem) => {
    try {
      const params = {
        fileSize: fileItem.size,
        folderId: fileItem.folderId,
        name: fileItem.name,
        ossToken: fileItem.fileToken || '',
        type: getFileType(fileItem.name),
      };

      const res = await saveFile(params);
      if (res.status) {
        treeRefresh();
        // 上传了第一个文件，重新查询知识库信息
        getKnowledgeBaseInfo();
      }
    } catch (error) {
      Message.error(t('knowledgenew.save-file-fail'));
      console.error('Save file error:', error);
    }
  };

  // 单个文件上传成功回调
  const uploadSuccessCb = (file: any) => {
    handleSaveFile(file);
  };
  /** 上传文件end */

  // 导入
  const importFileVisible = ref(false);

  /** 移动begin */
  const moveFileVisible = ref(false);
  const moveFiles = ref<Array<Node>>([]);
  const moveFile = (record: Node) => {
    moveFiles.value = [toRaw(record)];
    moveFileVisible.value = true;
  };
  const moveFileSuccess = async () => {
    treeRefresh();
    moveFiles.value = [];
  };
  /** 移动end */

  // 重命名文件/文件夹
  const handleRenameInput = (node: Node) => {
    if (isEditing.value) {
      Message.info(t('knowledgenew.handle-current-edit'));
      return;
    }
    knowledgeBaseStore2.setIsEditting(true);

    if (node.type === 'folder') {
      // 文件夹直接赋值
      newFolderName.value = node.name;
      fileExt.value = '';
    } else {
      // 文件需要截取后缀
      const lastDotIndex = node.name.lastIndexOf('.');
      if (lastDotIndex !== -1) {
        newFolderName.value = node.name.slice(0, lastDotIndex);
        fileExt.value = node.name.slice(lastDotIndex);
      } else {
        newFolderName.value = node.name;
        fileExt.value = '';
      }
    }
    node.isEdit = true;
    nextTick(() => {
      editInputRef.value.focus();
    });
  };

  // 重命名文件/文件夹
  const handleRename = useThrottleFn(async (node: Node) => {
    // 名称没有变化或者名称为空表示退出编辑
    if (
      node.name === newFolderName.value + fileExt.value ||
      newFolderName.value === ''
    ) {
      node.isEdit = false;
      Message.info(t('knowledgenew.cancel-rename'));
      knowledgeBaseStore2.setIsEditting(false);
      return;
    }

    // 名称校验
    if (node.type === 'folder') {
      // 校验文件夹名称
      const isNameValid = await checkFolderName(
        newFolderName.value,
        node.parentId
      );
      if (!isNameValid) {
        return;
      }
    } else {
      // 校验文件名称
      const isNameValid = await checkFileName(
        newFolderName.value + fileExt.value,
        node.parentId
      );
      if (!isNameValid) {
        return;
      }
    }

    const res = await renameFileOrFolder(
      node,
      newFolderName.value + fileExt.value
    );
    if (res) {
      newFolderName.value = '';
      fileExt.value = '';
      node.isEdit = false;
      knowledgeBaseStore2.setIsEditting(false);
      treeRefresh();
    }
  }, 1000);

  // wps编辑文件
  const editFile = (node: Node) => {
    if (node.type !== 'folder') {
      if (hasEditPermission.value) {
        wpsViewHandle(node, 'edit', 'admin');
      } else if (hasViewPermission.value) {
        wpsViewHandle(node, 'preview', 'admin');
      }
    }
  };

  // 确认删除
  const handleDelete = async (node: Node) => {
    const res = await deleteFolderOrFile(node);
    if (res) {
      treeRefresh();
    }
  };

  function eventsHandler(eventType: string, data?: any) {
    switch (eventType) {
      case 'add-folder':
        handleAddFolder(data);
        break;
      case 'upload':
        handleUpload(data);
        break;
      case 'import':
        currentData.value = data;
        importFileVisible.value = true;
        break;
      case 'move':
        moveFile(data);
        break;
      case 'rename':
        handleRenameInput(data);
        break;
      case 'download':
        downloadBtnClick(data);
        break;
      case 'delete':
        handleDelete(data);
        break;
      default:
        break;
    }
  }

  // 获取文件解析状态
  // const getFileStatus = (status: string) => {
  //   let text = '';
  //   switch (status) {
  //     case 'SUCCESS':
  //       text = '解析完成';
  //       break;
  //     case 'INIT':
  //       text = '解析中...';
  //       break;
  //     default:
  //       text = '解析中...';
  //   }
  //   return text;
  // };

  // 格式化更新日期
  const getDate = (dateStr: string) => {
    const date = new Date(dateStr);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，转为两位数字
    const day = String(date.getDate()).padStart(2, '0'); // 日期转为两位数字

    // 获取当前日期
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    const yearDiff = currentYear - year;
    // 根据年份差决定输出格式
    if (yearDiff === 0) {
      // 当年，只显示月/日
      return `${month}/${day}`;
    }
    // 显示 年份/月/日
    return `${year}/${month}/${day}`;
  };
</script>

<style scoped lang="less">
  .file-tree {
    width: 100%;
    overflow: hidden;
    .tree-content {
      padding: 8px 0 20px;
      height: calc(100% - 28px);
      overflow: auto;
    }
  }

  .empty-file {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 129px 0 0;

    span {
      margin-top: 16px;
      font-size: 16px;
      color: #4e5969;
      line-height: 24px;
    }
  }

  .tree-header {
    display: flex;
    padding: 0 20px;
    height: 28px;
  }

  .folder-file-extra {
    // width: 44px;
    margin-top: -16px;
    height: 14px;
    line-height: 14px;
  }

  .title-container {
    padding: 12px 0 8px;
    height: 54px;
  }
  .tree-title-text {
    margin-bottom: 4px;
    min-width: 100px;
    line-height: 14px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .tree-title-desc {
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 16px;
    color: #86909c;
  }

  // 树样式覆盖
  :deep(.arco-tree-node) {
    height: 54px;
    padding-left: 20px;
    padding-right: 20px;
  }
  :deep(.arco-tree-node-switcher) {
    margin-top: -16px;
    width: 16px;
    height: 16px;
  }
  :deep(.arco-tree-node-icon) {
    margin-top: -16px;
    margin-right: 6px;
    width: 20px;
    height: 20px;
  }
  :deep(.arco-tree-node-title) {
    padding: 0;
    min-width: 120px;
  }
  :deep(.arco-tree-node-title:hover) {
    background-color: #e8f2ff;
  }
  :deep(.arco-tree-node-title-text) {
    min-width: 100px;
    flex: 1;
  }
  :deep(.arco-tree-node-selected) {
    background-color: #e8f2ff;
  }
  :deep(.arco-tree-node:hover) {
    background-color: #e8f2ff;
  }
</style>
