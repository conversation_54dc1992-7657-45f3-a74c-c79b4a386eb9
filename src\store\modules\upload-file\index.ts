// 文件上传，需要先
import { defineStore } from 'pinia';
import {
  UploadFile,
  TaskArrItem,
  FileObj,
  CurrentFolder,
  InTaskArrItem,
} from './types';
import md5 from 'js-md5';
import { reactive } from 'vue';
import {
  uploadFile,
  mergeChunk,
  queryUploadInfo,
  queryUploadId,
  queryFileBlockList,
} from '@/api/upload-file';
import { Message } from '@arco-design/web-vue';
import i18n from '@/locale/index';
// import { encode } from 'js-base64';

const { t } = i18n.global;
const useUploadFileStore = defineStore('uploadFile', {
  state: (): any => {
    return {
      fileArr: [], // 文件选择列表（原始数据，不存fileToken）
      uploadFileList: [], // 文件上传列表（只有正在上传的）
      chunkSize: 15 * 1024 * 1024,
      groupToken: '',
      e9y: '',
      maxRequest: 6,
      etagObj: {},
      loading: false,
    };
  },
  getters: {
    getGlobalMode(state: UploadFile) {
      return state.fileArr;
    },
  },
  actions: {
    handleFileArr(fileList: any, type: number) {
      if (type === 0) {
        // 创建一个新数组来存储将要添加到fileArr中的文件
        const filesToAdd: any[] = [];

        // 遍历fileList中的每个文件
        fileList.forEach((file: any) => {
          // 检查fileArr中是否已经存在具有相同name的文件
          const fileExists = this.fileArr.some(
            (existingFile) => existingFile.name === file.name
          );
          if (!fileExists) {
            // 如果不存在，则将其添加到filesToAdd数组中
            filesToAdd.push(file);
          }
        });

        // 使用filesToAdd数组更新fileArr
        this.fileArr = this.fileArr.concat(filesToAdd);

        if (filesToAdd.length < fileList.length) {
          Message.error(t('file-manage.cannot-upload-the-same-file'));
        }
      } else {
        this.fileArr = fileList;
      }
    },
    // 生成文件 hash（web-worker）
    useWorker(file: any) {
      interface WorkerMessageType {
        fileHash: string;
        fileChunkList: any[]; // 或者更具体的类型，‌比如 { etag: string; number: number }[]
      }
      return new Promise((resolve) => {
        const worker = new Worker(
          new URL('/public/worker/hash-worker.js', import.meta.url)
          // {
          //   type: 'module',
          // }
        );
        worker.postMessage({ file, chunkSize: this.chunkSize });
        worker.onmessage = (e) => {
          const { fileHash, fileChunkList } = e.data as WorkerMessageType;
          if (fileHash) {
            resolve({
              fileHash,
              fileChunkList,
            });
          }
        };
      });
    },

    // 设置单个文件上传已完成
    async finishTask(
      item: any,
      fileToken: any,
      index: any,
      showSuccessMsg = true,
      successCallback: ((params: any) => void) | null = null
    ) {
      if (item.state !== 2) return;
      item.percent = 100;
      // 4是上传完成
      item.state = 4;
      const params: any = {
        fileToken,
        folderId: item.parentId,
        name: item.fileName,
        size: item.fileSize,
        description: item.description,
        uid: item.uid,
        webkitRelativePath: item.webkitRelativePath,
      };
      if (item.uploadSuccessCb && typeof item.uploadSuccessCb === 'function')
        item.uploadSuccessCb(params);
      if (this.fileArr.length) this.fileArr[index].status = 4;
      this.uploadFileList.some((file, i) => {
        if (item.uid === file.uid) {
          // setTimeout(() => {
          this.uploadFileList.splice(i, 1);
          if (this.uploadFileList.length === 0) {
            this.loading = false;
            if (showSuccessMsg) {
              Message.success(t('提交成功')); // 提交成功
            }
            if (successCallback) {
              successCallback(params);
            }
          }
          // }, 200);
          return true;
        }
        return false;
      });
    },

    // 改变fileArr中的文件状态
    changeFileArrStatus(taskArrItem: any, state: number) {
      this.fileArr.some((file: any) => {
        if (taskArrItem.fileName === file.name) {
          file.status = state;
          return true;
        }
        return false;
      });
    },
    // 全部暂停
    pauseAllUpload() {
      // 4是成功 6是失败
      this.uploadFileList.forEach((taskArrItem) => {
        this.pauseUpload(taskArrItem);
      });
    },
    // 暂停上传（是暂停剩下未上传的）, index: any
    pauseUpload(taskArrItem: any, elsePause = true) {
      // elsePause为true就是主动暂停，为false就是请求中断
      // 4是成功 6是失败  如果不是成功或者失败状态，
      this.changeFileArrStatus(taskArrItem, 3);
      if (![4, 6].includes(taskArrItem.state)) {
        // 3是暂停，5是中断
        if (elsePause) {
          taskArrItem.state = 3;
        } else {
          taskArrItem.state = 5;
        }
      }
      taskArrItem.errNumber = 0;

      // 取消还在请求中的所有接口
      if (taskArrItem.whileRequests.length > 0) {
        taskArrItem.whileRequests.forEach((itemB: any) => {
          if (itemB.cancel) {
            itemB.cancel();
          } else {
            itemB.cancel = '';
          }
        });
      }
      taskArrItem.allChunkList.forEach((itemB: any) => {
        if (itemB.cancel) {
          itemB.cancel();
        } else {
          itemB.cancel = '';
        }
      });
    },

    // 继续上传全部文件
    resumeAllUpload() {
      this.uploadFileList.forEach((taskArrItem, index) => {
        this.resumeUpload(taskArrItem, index);
      });
    },
    // 继续上传
    async resumeUpload(taskArrItem: any, index: any) {
      this.changeFileArrStatus(taskArrItem, 2);
      // 2为上传中
      taskArrItem.state = 2;
      if (taskArrItem.resultType === 0) {
        // 极速秒传的情况下，网速不好需要暂停
        await this.finishTask(
          taskArrItem,
          taskArrItem.fileToken,
          taskArrItem.i
        );
        return;
      }
      // 把刚才暂停的正在上传中所有切片放到待上传切片列表中
      taskArrItem.allChunkList.push(...taskArrItem.whileRequests);
      taskArrItem.whileRequests = [];
      this.uploadSignleFile(taskArrItem, index);
    },

    // 取消单个
    async cancelSingle(taskArrItem: TaskArrItem) {
      this.pauseUpload(taskArrItem);
      // 取消上传后列表删除该文件
      this.uploadFileList = this.uploadFileList.filter(
        (itemB) => itemB.fileName !== taskArrItem.fileName
      );
      if (!this.uploadFileList.length) {
        this.loading = false;
      }
    },

    // 全部取消
    cancelAll() {
      // for (const item of this.uploadFileList) {
      //   this.pauseUpload(item);
      // }
      this.uploadFileList.forEach((item) => {
        this.pauseUpload(item);
      });

      this.uploadFileList = [];
      this.loading = false;
    },

    // 获取文件的md5Key
    getMd5Key(fileObj: FileObj) {
      // 文件名+文件大小+最后修改时间
      const key = fileObj.name + fileObj.size + fileObj.lastModifiedDate;

      return new Promise((res) => {
        const md5Key = md5(key);
        res(md5Key);
      });
    },

    // 调取合并接口处理所有切片
    async handleMerge(
      taskArrItem: TaskArrItem,
      curFile: any,
      index: number,
      showSuccessMsg = true,
      successCallback: ((params: any) => void) | null = null
    ) {
      const { fileName } = taskArrItem;
      // const { fileName, fileHash } = taskArrItem;
      const res = await mergeChunk({
        e9y: this.e9y,
        fileName: taskArrItem.fileName,
        fileSize: taskArrItem.fileSize,
        groupToken: this.groupToken,
        info: {},
        list: this.etagObj[fileName],
        md5: curFile.md5,
        objectName: curFile.objectName,
        uploadId: curFile.uploadId,
      });
      //  如果合并成功则标识该文件已经上传完成

      if (res && res.code === 8000000) {
        // 设置文件上传状态
        taskArrItem.fileToken = res.data.fileToken;
        this.finishTask(
          taskArrItem,
          res.data.fileToken,
          index,
          showSuccessMsg,
          successCallback
        );
        console.log('文件合并成功！');
      } else {
        // 否则暂停上传该文件
        this.pauseUpload(taskArrItem, true);
        console.log('文件合并失败！');
      }
      // 最后赋值文件切片上传完成个数为0
      taskArrItem.finishNumber = 0;
    },

    // 更新单个文件进度条
    signleFileProgress(
      needObj: any,
      taskArrItem: any,
      piecewiseProgress: any
    ): void {
      // 即使是超时请求也是会频繁的返回上传进度的,所以只能写成完成一片就添加它所占百分之多少,否则会造成误会
      if (!taskArrItem.loadedArr) taskArrItem.loadedArr = [];
      taskArrItem.loadedArr[needObj.chunk] =
        piecewiseProgress.loaded / piecewiseProgress.total;
      // 分片上传百分比总和
      const allPiecewiseProgress: number = taskArrItem.loadedArr.reduce(
        (accumulator: number, currentValue: number) =>
          accumulator + currentValue,
        0
      );
      const allProgress = taskArrItem.loadedArr.length;

      const newProgress = Number(
        (allPiecewiseProgress / allProgress) * 99
      ).toFixed(2);
      const oldProgress = taskArrItem.percent;

      const percent =
        oldProgress * 100 > Number(newProgress) * 100
          ? oldProgress
          : newProgress;
      taskArrItem.percent = percent;

      this.fileArr.some((item) => {
        if (item.name === taskArrItem.fileName) {
          item.percent = percent;
          item.status = 2;
          return true;
        }
        return false;
      });
    },

    // 单个文件上传
    uploadSignleFile(taskArrItem: any, index: any) {
      // 如果没有需要上传的切片 / 正在上传的切片还没传完，就不做处理

      if (
        taskArrItem.allChunkList.length === 0 ||
        taskArrItem.whileRequests.length > 0 ||
        taskArrItem.state === 3
      ) {
        return;
      }
      // 找到文件处于处理中/上传中的 文件列表（是文件而不是切片）
      const isTaskArrIng = this.uploadFileList.filter(
        (itemB) => itemB.state === 1 || itemB.state === 2
      );

      // 实时动态获取并发请求数,每次调请求前都获取一次最大并发数
      // 浏览器同域名同一时间请求的最大并发数限制为6
      // 例如如果有3个文件同时上传/处理中，则每个文件切片接口最多调 6 / 3 == 2个相同的接口
      this.maxRequest = Math.ceil(6 / isTaskArrIng.length);

      // 从数组的末尾开始提取 maxRequest 个元素。
      const whileRequest = taskArrItem.allChunkList.slice(-this.maxRequest);

      // 设置正在请求中的个数
      taskArrItem.whileRequests.push(...whileRequest);
      //  如果总请求数大于并发数
      if (taskArrItem.allChunkList.length > this.maxRequest) {
        // 则减去并发数
        taskArrItem.allChunkList.splice(-this.maxRequest);
      } else {
        // 否则总请求数置空,说明已经把没请求的全部放进请求列表了，不需要做过多请求
        taskArrItem.allChunkList = [];
      }

      // 单个分片请求
      const uploadChunk = async (needObj: any) => {
        const fd = new FormData();
        const {
          e9y,
          id,
          name,
          type,
          lastModifiedDate,
          size,
          uploadId,
          objectName,
          chunk,
          file,
          chunkNumber,
          md5: md5Info,
        } = needObj;

        fd.append('e9y', e9y);
        fd.append('id', id);
        fd.append('name', name);
        fd.append('type', type);
        fd.append('lastModifiedDate', lastModifiedDate);
        fd.append('size', size);
        fd.append('uploadId', uploadId);
        fd.append('objectName', objectName);
        fd.append('chunk', chunk);
        fd.append('file', file);

        const res = await uploadFile(
          fd,
          (onCancelFunc: any) => {
            // 在调用接口的同时，相当于同时调用了传入的这个函数，又能同时拿到返回的取消方法去赋值
            needObj.cancel = onCancelFunc;
          },
          (progressEvent) => {
            needObj.finish = true;
            this.signleFileProgress(needObj, taskArrItem, progressEvent); // 更新进度条
          }
        );
        // 先判断是不是处于暂停还是取消状态
        // 你的状态都已经变成暂停或者中断了,就什么都不要再做了,及时停止
        if (taskArrItem.state === 3 || taskArrItem.state === 5) {
          return;
        }

        // 请求异常,或者请求成功服务端返回报错都按单片上传失败逻辑处理,.then.catch的.catch是只能捕捉请求异常的
        if (!res || res.code === -1) {
          taskArrItem.errNumber++;
          // 超过3次之后直接上传中断
          if (taskArrItem.errNumber > 3) {
            console.log('切片上传失败超过三次了');
            this.pauseUpload(taskArrItem, false); // 上传中断
          } else {
            console.log('切片上传失败还没超过3次');
            uploadChunk(needObj); // 失败了一片,继续当前分片请求
          }
        } else if (res.code === 8000000) {
          // 上传成功记录分片记录
          if (!this.etagObj[name]) this.etagObj[name] = [];
          this.etagObj[name].push({
            etag: res.data,
            number: chunk + 1,
          });

          // 单个文件上传失败次数大于0则要减少一个
          // taskArrItem.errNumber > 0 ? taskArrItem.errNumber-- : 0;
          if (taskArrItem.errNumber > 0) {
            taskArrItem.errNumber -= 1;
          } else {
            taskArrItem.errNumber = 0;
          }
          // 单个文件切片上传成功数+1
          taskArrItem.finishNumber++;
          // 单个切片上传完成
          // needObj.finish = true;
          // this.signleFileProgress(needObj, taskArrItem); // 更新进度条
          // 上传成功了就删掉请求中数组中的那一片请求
          taskArrItem.whileRequests = taskArrItem.whileRequests.filter(
            (item: any) => item.chunk !== needObj.chunk
          );

          // 如果单个文件最终成功数等于切片个数
          if (taskArrItem.finishNumber === chunkNumber) {
            // 全部上传完切片后就开始合并切片
            const obj = {
              md5Info,
              chunkNumber,
              objectName,
              uploadId,
            };
            this.handleMerge(taskArrItem, obj, index);
          } else {
            // 如果还没完全上传完，则继续上传
            this.uploadSignleFile(taskArrItem, index);
          }
        }
      };

      // // 开始单个上传
      // for (const item of whileRequest) {
      //   uploadChunk(item);
      // }
      whileRequest.forEach((item: any) => {
        uploadChunk(item);
      });
    },

    // 扁平化数组
    flattenFolders(folder: CurrentFolder): any[] {
      const mapFolderToResult = (currentFolder: CurrentFolder) => ({
        id: currentFolder.id,
        name: currentFolder.name,
        path: currentFolder.path,
        projectId: currentFolder.projectId,
        teamId: currentFolder.teamId,
        type: currentFolder.type,
        parentId: currentFolder.parentId,
        sysType: currentFolder.sysType,
      });

      return [
        mapFolderToResult(folder),
        ...folder.folderAllDtoList.flatMap(this.flattenFolders),
      ];
    },

    /**
     * 上传文件
     * @param fileList 文件列表
     * @param successOption 成功回调
     * @returns
     */
    async handleUploadFile(
      fileList?: [],
      successOption?: boolean | ((params: any) => void)
    ) {
      // file有值说明是单独上传
      // @ts-ignore
      const files: any = [...fileList];
      // 空文件返回
      if (files.length === 0) return;
      this.loading = true;

      // 处理successOption参数
      let showSuccessMsg = true;
      let successCallback: ((params: any) => void) | null = null;

      if (typeof successOption === 'boolean') {
        showSuccessMsg = successOption;
      } else if (typeof successOption === 'function') {
        showSuccessMsg = false; // 有回调函数时默认不显示系统提示
        successCallback = successOption;
      }

      // return
      // 多文件
      // eslint-disable-next-line no-restricted-syntax
      Array.from(files).forEach(async (item: any, i) => {
        const fileItem = item.file;
        // 单个上传文件
        // 这里要注意vue2跟vue3不同，
        // 如果在循环 + await中，如果把一个普通对象push进一个响应式数组
        // 直接修改原对象可能不会触发vue的DOM视图更新（但最终值会改变）
        // 所以这里用了reactive做响应式代理
        const inTaskArrItem: InTaskArrItem = reactive({
          // id: new Date() + i, // 因为forEach是同步，所以需要用指定id作为唯一标识
          state: 0, // 0是什么都不做,1文件处理中,2是上传中,3是暂停,4是上传完成,5上传中断，6是上传失败
          fileHash: '',
          fileName: fileItem.name,
          name: fileItem.name,
          fileSize: fileItem.size,
          allChunkList: [], // 所有请求的数据
          whileRequests: [], // 正在请求中的请求个数,目前是要永远都保存请求个数为6
          finishNumber: 0, // 请求完成的个数
          errNumber: 0, // 报错的个数,默认是0个,超多3个就是直接上传中断
          percent: 0, // 单个文件上传进度条
          cancel: null, // 用于取消切片上传接口
          description: item.description,
          uid: item.uid,
          model: item.model,
          parentId: item.parentId,
          webkitRelativePath: fileItem.webkitRelativePath,
          uploadSuccessCb: item.uploadSuccessCb,
        });
        this.uploadFileList.push(inTaskArrItem);
        // 如果不使用reactive，就得使用以下两种方式
        // inTaskArrItem = this.uploadFileList[i]
        // this.uploadFileList[i].state = 2
        // 开始处理解析文件
        inTaskArrItem.state = 1;
        if (this.fileArr[i]) {
          // 上传单个文件时this.fileArr为空
          this.fileArr[i].status = 'uploading'; // 更改ui组件的状态
        }

        if (fileItem.size === 0) {
          // 文件大小为0直接上传失败
          inTaskArrItem.state = 6;
          this.fileArr[i].status = 'error'; // 更改ui组件的状态
          // 上传中断
          this.pauseUpload(inTaskArrItem, false);
        }
        console.log('文件开始解析');

        // getFileKey(fileItem);
        // 计算文件hash
        // eslint-disable-next-line no-await-in-loop
        const { fileHash, fileChunkList } = (await this.useWorker(
          fileItem
        )) as { fileHash: string; fileChunkList: any[] };

        console.log(fileHash, '文件hash计算完成');

        // 解析完成开始上传文件
        let baseName = '';
        // 查找'.'在fileName中最后出现的位置
        const lastIndex = fileItem.name.lastIndexOf('.');
        // 如果'.'不存在，则返回整个文件名
        if (lastIndex === -1) {
          baseName = fileItem.name;
        }
        // 否则，返回从fileName开始到'.'前一个字符的子串作为文件名（不包含'.'）
        baseName = fileItem.name.slice(0, lastIndex);

        // 这里要注意！可能同一个文件，是复制出来的，出现文件名不同但是内容相同，导致获取到的hash值也是相同的
        // 所以文件hash要特殊处理
        inTaskArrItem.fileHash = `${fileHash}${baseName}`;
        if (inTaskArrItem.state === 1) {
          inTaskArrItem.state = 2;
        }
        this.changeFileArrStatus(inTaskArrItem, 2);
        // 上传之前要检查服务器是否存在该文件
        this.getMd5Key(fileItem).then(async (md5Value: any) => {
          // md5Obj.value[index] = md5Value;
          const data = {
            fileName: fileItem.name,
            fileSize: fileItem.size,
            md5: md5Value,
            g9s: this.groupToken,
          };
          queryUploadInfo(data)
            .then(async (res) => {
              if (!res.status) {
                // 异常状态：文件类型不符合后端限制等情况 TODO
              } else if (res.data.resultType === 0) {
                // 极速秒传
                inTaskArrItem.resultType = res.data?.resultType;
                inTaskArrItem.fileToken = res.data.sysOssVO.fileToken;
                inTaskArrItem.i = i;
                await this.finishTask(
                  inTaskArrItem,
                  res.data.sysOssVO.fileToken,
                  i,
                  showSuccessMsg,
                  successCallback
                );
              } else if (res.data.resultType === 2) {
                try {
                  // 断点续传
                  const params = {
                    objectName: res.data.bigFileVO.objectName,
                    uploadId: res.data.bigFileVO.uploadId,
                    endPointKey: this.e9y,
                  };
                  const result = await queryFileBlockList(params);
                  // 过滤需要上传的列表
                  result.data.some((item1: { partNumber: any }) => {
                    return fileChunkList.some(
                      (item2: { chunk: number }, index2: any) => {
                        if (item1.partNumber === item2.chunk + 1) {
                          fileChunkList.splice(index2, 1);
                          return true;
                        }
                        return false;
                      }
                    );
                  });
                  inTaskArrItem.allChunkList = fileChunkList.map(
                    (item3: { chunkFile: any }, index3: any) => {
                      return {
                        e9y: this.e9y,
                        // id: "WU_FILE_0",
                        name: fileItem.name,
                        // type: "application/x-msdownload",
                        lastModifiedDate: fileItem.lastModifiedDate,
                        size: this.chunkSize,
                        uploadId: res.data.bigFileVO.uploadId,
                        objectName: res.data.bigFileVO.objectName,
                        chunk: index3,
                        chunkNumber: fileChunkList.length,
                        file: item3.chunkFile,
                        md5: md5Value,
                      };
                    }
                  );
                  this.uploadSignleFile(inTaskArrItem, i);
                } catch (err) {
                  console.log(err);
                }
              } else {
                // 分片上传 resultType为1
                try {
                  const params = {
                    e9y: this.e9y,
                    fileName: fileItem.name,
                    md5: md5Value,
                  };
                  const result = await queryUploadId(params);
                  inTaskArrItem.allChunkList = fileChunkList.map(
                    (item2: { chunkFile: any }, index2: any) => {
                      return {
                        e9y: this.e9y,
                        // id: "WU_FILE_0",
                        name: fileItem.name,
                        // type: "application/x-msdownload",
                        lastModifiedDate: fileItem.lastModifiedDate,
                        size: this.chunkSize,
                        uploadId: result.data.uploadId,
                        objectName: result.data.objectName,
                        chunk: index2,
                        chunkNumber: fileChunkList.length,
                        file: item2.chunkFile,
                        md5: md5Value,
                      };
                    }
                  );
                  this.uploadSignleFile(inTaskArrItem, i);
                } catch (e) {
                  console.log(e);
                }
              }
            })
            .catch((e) => {
              console.log(e, 'secondPass');
            });
        });
      });
    },
  },
});

export default useUploadFileStore;
