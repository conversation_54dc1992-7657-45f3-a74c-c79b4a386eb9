<template>
  <div>
    <div v-if="fileArr.length" class="upload-list">
      <!-- 已选文件 -->
      <div v-for="fileObj in fileArr" :key="fileObj.id" class="item">
        <div class="name-row">
          <file-image :file-name="fileObj.name" :is-file="true" />
          <div class="name">{{ fileObj.name }}</div>
          <div>
            <a-progress
              v-if="fileObj.status === 2 && fileObj.percent < 100"
              class="percentage"
              :percent="(fileObj.percent || 0) / 100"
              size="mini"
            />
            <!-- fileObj.percent === 100&& !enginePercentages[index] -->
            <span v-if="fileObj.status === 3" style="color: #7e8592"
              >暂停中...
            </span>
            <span
              v-if="fileObj.status === 2 && fileObj.percent < 100"
              style="color: blue"
              >上传中...
            </span>
            <!-- <a-spin /> 加载效果-->
            <!-- fileObj.percent === 100 && enginePercentages[index] -->
            <img
              v-if="fileObj.percent === 100"
              style="width: 16px; height: 16px"
              :src="uploadedIcon"
            />
            <icon-delete
              v-if="
                fileObj.status !== 2 ||
                (fileObj.status === 3 && fileObj.percent === 0)
              "
              style="cursor: pointer"
              @click="deleteUpload(fileObj)"
            />
          </div>
        </div>
        <div class="discription-row">
          <div class="label" style="width: 78px">说明</div>

          <a-textarea
            v-model="fileObj.description"
            :disabled="disabled"
            :max-length="255"
            placeholder="请输入"
            allow-clear
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  // import { Message } from '@arco-design/web-vue';
  import { storeToRefs } from 'pinia';
  import uploadedIcon from '@/assets/images/file-manager/uploaded.png';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import useUploadFileStore from '@/store/modules/upload-file/index';
  // import { useUploadFileStore } from "@/store/index";
  import { ref, onMounted, watch } from 'vue';

  const uploadFileStore = useUploadFileStore();
  const { fileArr } = storeToRefs(uploadFileStore);

  // watch(fileArr, (newVal) => {
  //   console.log(newVal);
  //   const newFileArr = newVal.filter(
  //     (item: any, index: any, self: any) =>
  //       index === self.findIndex((t: any) => t.name === item.name)
  //   );
  //   debugger;
  //   uploadFileStore.fileArr = newFileArr;
  // });
  function deleteUpload(fileObj: any) {
    fileArr.value.some((item: any, i: number) => {
      if (item.name === fileObj.name) {
        fileArr.value.splice(i, 1);
        console.log(uploadFileStore.fileArr, '删除文件');
        return true;
      }
      return false;
    });
  }
  const disabled = ref(false);
  onMounted(() => {
    console.log(22222);
  });
</script>

<style lang="less" scoped>
  /* 覆盖文件列表默认样式 */
  .upload-list {
    width: 100%;
    margin-top: 24px;
    max-height: 362px;
    overflow: auto;

    .item {
      margin-top: 16px;
      padding: 8px 12px;
      background-color: var(--color-fill-1);
      border-radius: 4px;
      overflow: hidden;

      .name-row {
        display: flex;
        align-items: center;

        img {
          width: 32px;
          height: 32px;
          margin-right: 12px;
          border-radius: 2px;
        }

        .name {
          flex: 1;
          padding-right: 19px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        }

        .percentage {
          width: 16px;
          height: 16px;

          :deep(.arco-progress-type-line) {
            display: block;
          }

          img,
          svg {
            width: 100%;
            height: 100%;
          }
          svg {
            cursor: pointer;
          }
        }
      }

      .discription-row {
        display: flex;
        margin-top: 8px;

        .label {
          display: inline-block;
          width: 44px;
          margin-top: 5px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        }

        :deep(.arco-textarea-wrapper) {
          flex: 1;
          border-radius: 4px;
        }
      }
    }

    .item:first-of-type {
      margin-top: 0;
    }
  }
</style>
