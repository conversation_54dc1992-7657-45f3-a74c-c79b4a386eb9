export default {
  'model-collaboration.total': '总计',
  'model-collaboration.cancel': '取消',
  'model-collaboration.create': '创建合模文件',
  'model-collaboration.please-input': '请输入',
  'model-collaboration.selectTeam': '请选择团队',
  'model-collaboration.select-file': '请选择文件',
  'model-collaboration.description': '描述',
  'model-collaboration.file': '文件',
  'model-collaboration.addFiles': '添加文件',
  'model-collaboration.merge-models': '合模',
  'model-collaboration.search': '查询',
  'model-collaboration.clear': '清空',
  'model-collaboration.index': '序号',
  'model-collaboration.name': '名称',
  'model-collaboration.modelSource': '模型来源',
  'model-collaboration.creator': '创建人',
  'model-collaboration.createTime': '创建时间',
  'model-collaboration.modelFile': '模型文件',
  'model-collaboration.singleModelFile': '单个模型文件',
  'model-collaboration.twoModelFile': '两个模型文件',
  'model-collaboration.modelA': '模型A',
  'model-collaboration.modelB': '模型B',
  'model-collaboration.selectFile': '选择文件',
  'model-collaboration.fileName': '文件名',
  'model-collaboration.version': '版本',
  'model-collaboration.generateInTeam': '生成在团队',
  'model-collaboration.please-select': '请选择',
  'model-collaboration.totalFiles': '文件数: ',
  'model-collaboration.model': '模型',
  'model-collaboration.fileNameRequired': '请输入名称',

  'model-collaboration.collision-check': '碰撞检查',
  'model-collaboration.check-name': '检查名称',
  'model-collaboration.collisionDetection': '碰撞检测',
  'model-collaboration.createNewCollisionDetection': '新建碰撞检测',

  'model-collaboration.files-source': '文件来源',
  'model-collaboration.creation-time': '创建时间',
  'model-collaboration.team': '团队',
  'model-collaboration.add': '添加',
  'model-collaboration.success': '成功',
  'model-collaboration.conversion-was-not-successful':
    '未转换成功,无法进行合模',
  'model-collaboration.not-support-merge-model':
    '不支持语义模型和非语义模型文件合模',
  'model-collaboration.select-multi-model': '请至少选择两个三维模型文件',
  'model-collaboration.unable-select3d': '无法进行合模，请选择三维模型文件',
  'model-collaboration.unable-reselect': '无法进行合模，请重新选择或上传',
  'model-collaboration.unselect-file': '未选择文件',

  'model-collaboration.back': '上一步',
  'model-collaboration.next': '下一步',
  'model-collaboration.confirm': '确认',
  'model-collaboration.first-step': '第一步',
  'model-collaboration.second-step': '第二步',
  'model-collaboration.third-step': '第三步',
  'model-collaboration.select-model': '选择模型',
  'model-collaboration.select-range': '选择范围',
  'model-collaboration.set-up-parameters': '设置参数',
  'model-collaboration.model-tree-presentation': '模型树展示',
  'model-collaboration.generated-position': '生成在团队：',
  'model-collaboration.unable-collision':
    '该模型无法进行碰撞检查，请重新选择和上传',
  'model-collaboration.at-least-one': '请至少选择一个',
  'model-collaboration.Collision-type': '碰撞类型',
  'model-collaboration.hard-collision': '硬碰撞',
  'model-collaboration.Gap-collision': '间隙碰撞',
  'model-collaboration.tolerance-tip':
    '提示：当物体间距小于等于（≤）碰撞公差时报告间隙碰撞，只能输入数字，且不能是0开头',
  'model-collaboration.Tolerance-collision': '碰撞公差',
  'model-collaboration.Checking-Rules': '检查规则',
  'model-collaboration.forbid-same-model': '禁止同一模型内构件参与碰撞',
  'model-collaboration.vertical-tip':
    '勾选后：“单模型内部”及“合模文件内的原模型内部”将不进行碰撞，仅合模文件内的原模型之间碰撞',
  'model-collaboration.forbid-same-layer': '禁止同一层内构件参与碰撞',
  'model-collaboration.forbid-same-class': '禁止某一类构件参与碰撞',
  'model-collaboration.Select-component-category': '请选择构件类别',
  'model-collaboration.forbid-collision': '禁止某两类构件之间参与碰撞',
  'model-collaboration.Select-first-type': '请选择第一类构件',
  'model-collaboration.Select-second-type': '请选择第二类构件',
  'model-collaboration.Validation-failure': '校验失败',
  'model-collaboration.conversion-was-not-successful-no-clash':
    '未转换成功,无法进行碰撞检测',
  'model-collaboration.dgn-files-are-not-supported':
    '两个模型碰撞暂不支持dgn文件',
  'model-collaboration.not-a-semantic-model': '不是语义模型，无法进行碰撞检测',
  'model-collaboration.operate': '操作',
  'model-collaboration.delete': '删除',
  'model-collaboration.delete-title': '确认删除',
  'model-collaboration.delete-content': '确认删除此文件?',
};
