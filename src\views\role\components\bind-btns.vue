<template>
  <a-space
    v-if="!isView"
    style="justify-content: flex-start"
    fill
    align="start"
  >
    <div style="border-right: 1px solid #eee">
      <div class="title">菜单列表</div>
      <a-scrollbar style="height: calc(100vh - 398px); overflow: auto">
        <a-tree
          v-if="treeData.length"
          style="width: 300px"
          v-model:selected-keys="selectedKeys"
          multiple
          :data="treeData"
          show-line
          :default-expand-all="true"
          default-expand-selected
          @select="handleNodeSelect"
        >
        </a-tree>
      </a-scrollbar>
    </div>

    <div style="padding-left: 20px">
      <div class="title">按钮列表</div>
      <a-scrollbar style="height: calc(100vh - 398px); overflow: auto">
        <div
          v-for="btnView in buttonsView"
          :key="btnView.key"
          style="margin-bottom: 20px"
        >
          <a-space fill style="margin-bottom: 6px">
            <div class="menu-title">{{ btnView.title }}</div>
            <a-checkbox
              :model-value="btnView.checkedAll"
              :indeterminate="btnView.indeterminate"
              @change="(val:boolean)=>{handleChangeAll(val, btnView)}"
              >全选
            </a-checkbox>
          </a-space>
          <a-space direction="vertical">
            <a-checkbox-group
              v-model="btnView.checkedBtns"
              @change="(val:string[]) => handleBtnsChange(val, btnView)"
            >
              <a-checkbox
                :key="btns.code"
                :value="btns.id"
                v-for="btns in btnView.buttons"
                >{{ btns.name }}</a-checkbox
              >
            </a-checkbox-group>
          </a-space>
        </div>
      </a-scrollbar>
    </div>
  </a-space>
  <a-space v-else>
    <div style="padding-left: 20px">
      <div class="title">按钮列表</div>
      <a-scrollbar
        style="height: calc(100vh - 398px); overflow: auto; width: 100%"
      >
        <div
          v-for="btnView in buttonsView"
          :key="btnView.key"
          style="margin-bottom: 20px"
        >
          <a-space fill style="margin-bottom: 6px">
            <div class="menu-title">{{ btnView.title }}</div>
          </a-space>
          <a-space direction="vertical">
            <a-checkbox-group disabled v-model="btnView.checkedBtns">
              <a-checkbox
                :key="btns.code"
                :value="btns.id"
                v-for="btns in btnView.buttons"
                >{{ btns.name }}</a-checkbox
              >
            </a-checkbox-group>
          </a-space>
        </div>
      </a-scrollbar>
    </div>
  </a-space>
</template>

<script lang="ts" setup>
  import { nextTick, onMounted, PropType, ref, toRefs } from 'vue';
  import { getBtnTree } from '../api';

  // 当前选中的树节点key数组
  const selectedKeys = ref<string[]>([]);
  // 全部叶子节点菜单数组
  const allLeafMenus = ref<RoleMenu.Model.MenuTree[]>([]);
  // 右侧展示的菜单-按钮结构 数组
  const buttonsView = ref<RoleMenu.Model.BtnsView[]>([]);

  const props = defineProps({
    defaultChecked: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    isView: {
      type: Boolean,
      default: false,
    },
  });

  const treeData = ref<RoleMenu.Model.MenuTree[]>([]);

  // 宽度优先递归菜单树
  function dfsRecursive(node: RoleMenu.Api.MenuTree): RoleMenu.Model.MenuTree {
    const modelTree: RoleMenu.Model.MenuTree = {
      ...node,
      isLeaf: !(node.children && node.children.length),
      key: node.id,
      children: [],
    };
    if (!modelTree.isLeaf) {
      modelTree.children = node.children.map((child) => dfsRecursive(child));
    } else {
      allLeafMenus.value.push(modelTree);
    }
    return modelTree;
  }

  // 选中菜单带出按钮
  function handleNodeSelect(
    keys: string[],
    data: {
      selected: boolean;
      node: RoleMenu.Model.MenuTree;
    }
  ) {
    if (data.selected) {
      if (data.node.isLeaf)
        buttonsView.value.push({
          ...data.node,
          checkedAll: true,
          checkedBtns: data.node.buttons.map((btn) => btn.id),
          indeterminate: false,
        });
    } else {
      buttonsView.value = buttonsView.value.filter(
        (item) => item.key !== data.node.key
      );
    }
  }

  function handleChangeAll(val: boolean, btnsView: RoleMenu.Model.BtnsView) {
    btnsView.checkedAll = val;
    btnsView.indeterminate = false;

    if (val) {
      btnsView.checkedBtns = btnsView.buttons.map((btn) => btn.id);
    } else {
      btnsView.checkedBtns = [];
    }
  }
  function handleBtnsChange(val: string[], btnsView: RoleMenu.Model.BtnsView) {
    btnsView.checkedBtns = val;
    if (val.length !== btnsView.buttons.length) {
      btnsView.indeterminate = true;
    } else {
      btnsView.checkedAll = true;
      btnsView.indeterminate = false;
    }
  }

  function getBtnIds(): string[] {
    return buttonsView.value.reduce((accumulator: string[], val) => {
      return accumulator.concat(val.checkedBtns);
    }, []);
  }

  const { defaultChecked } = toRefs(props);
  function getTreeData() {
    getBtnTree().then((res) => {
      treeData.value = res.data.map((item) => dfsRecursive(item));
      // 新增时直接返回
      if (!defaultChecked.value || defaultChecked.value.length === 0) {
        return;
      }

      // 编辑回显逻辑
      allLeafMenus.value.forEach((item) => {
        if (
          item.buttons &&
          item.buttons.findIndex((btn) =>
            defaultChecked.value.includes(btn.id)
          ) !== -1
        ) {
          selectedKeys.value.push(item.key);
          const viewObj = {
            ...item,
            checkedAll: true,
            checkedBtns: item.buttons
              .filter((btn) => defaultChecked.value.includes(btn.id))
              .map((btn) => btn.id),
            indeterminate: false,
          };
          if (viewObj.checkedBtns.length !== viewObj.buttons.length) {
            viewObj.indeterminate = true;
            viewObj.checkedAll = false;
          } else {
            viewObj.checkedAll = true;
            viewObj.indeterminate = false;
          }
          buttonsView.value.push(viewObj);
        }
      });
    });
  }
  onMounted(() => {
    getTreeData();
  });

  defineExpose({ getBtnIds });
</script>
<style scoped lang="less">
  .title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
  .menu-title {
    line-height: 32px;
    font-size: 14px;
    font-weight: bold;
    color: #444;
  }
  :deep(.arco-checkbox-label) {
    color: #666;
  }
</style>
