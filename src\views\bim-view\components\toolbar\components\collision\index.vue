<template>
  <div class="compare-list-box">
    <a-card :title="$t('model-viewer.partStructure')" class="list-card">
      <template #extra>
        <icon-close class="icon-close" @click="closeList" />
      </template>
      <a-card hoverable :bordered="true" style="height: 100%; width: 100%">
        <a-tabs v-model="tabvalue" default-active-key="1" @change="changeTab">
          <template #extra>
            <a-button type="primary" @click="exportFile">{{
              $t('model-viewer.exportFile')
            }}</a-button>
          </template>
          <a-tab-pane
            :key="1"
            :title="`${$t('model-viewer.all')}(${collisionNum[0]})`"
          >
          </a-tab-pane>
          <a-tab-pane
            :key="2"
            :title="`${$t('model-viewer.hardCollision')}(${collisionNum[1]})`"
          >
          </a-tab-pane>
          <a-tab-pane
            :key="3"
            :title="`${$t('model-viewer.gapCollision')}(${collisionNum[2]})`"
          >
          </a-tab-pane>
        </a-tabs>
        <div class="compare-card-box">
          <a-table
            stripe
            :scroll="{
              y: 666,
            }"
            :scrollbar="true"
            :bordered="false"
            :columns="columnsAll"
            :data="dataSource"
            :pagination="false"
            show-empty-tree
            :hide-expand-button-on-empty="true"
            :loading="tableLoading"
            @row-click="gotoEntity"
          >
            <template #objectA="{ record }">
              {{ record.clash_a?.name }}
            </template>
            <template #objectB="{ record }">
              {{ record.clash_b?.name }}
            </template>
            <template #type="{ record }">
              {{
                record.type === 1
                  ? $t('model-viewer.hardCollision')
                  : $t('model-viewer.gapCollision')
              }}
            </template>
          </a-table>
        </div>
      </a-card>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, toRaw } from 'vue';
  import { Column } from '@/views/design/components/table-column';
  import useModelToolsStore from '@/store/modules/model-viewer/index';
  import { storeToRefs } from 'pinia';
  import { GetXBaseClashInfo, GetExportFile } from './api';
  import { useRoute } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import createUrl from '@/utils/file-blob';

  const emits = defineEmits(['close']);
  const { t } = useI18n();
  const toolStore = storeToRefs(useModelToolsStore());
  const route = useRoute();

  const tableLoading = ref(false);

  const props = defineProps({
    modelData: {
      type: Object,
      // default: {},
    },
    viewer: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  const viewer = toRaw(props.viewer);
  const graphicEngineInfo = JSON.parse(props.modelData?.graphicEngineInfo);

  const columnsAll = computed<Column[]>(() => {
    return [
      {
        title: t('model-viewer.collisionID'),
        dataIndex: 'id',
        ellipsis: true,
        tooltip: true,
        align: 'left',
      },
      {
        title:
          route?.query?.modelNumber === '2'
            ? `${t('model-viewer.model')}A-${t(
                'model-viewer.collisionObject1'
              )}`
            : t('model-viewer.collisionObject1'),
        dataIndex: 'objectA',
        slotName: 'objectA',
        ellipsis: true,
        tooltip: true,
        align: 'left',
      },
      {
        title:
          route?.query?.modelNumber === '2'
            ? `${t('model-viewer.model')}B-${t(
                'model-viewer.collisionObject2'
              )}`
            : t('model-viewer.collisionObject2'),
        dataIndex: 'objectB',
        slotName: 'objectB',
        ellipsis: true,
        tooltip: true,
        align: 'left',
      },
      {
        title: t('model-viewer.collisionType'),
        dataIndex: 'type',
        slotName: 'type',
        ellipsis: true,
        tooltip: true,
        align: 'left',
      },
    ];
  });

  const dataSource = ref<any>([]);
  const allData = ref<any>([]);
  const type1Data = ref<any>([]);
  const type2Data = ref<any>([]);
  const collisionNum = ref<number[]>([0, 0, 0]);
  const tabvalue = ref(1);
  const lastObjectData = ref<any>([]);

  const changeTab = (key: string | number) => {
    switch (key) {
      case 1:
        dataSource.value = allData.value;
        tabvalue.value = 1;
        break;
      case 2:
        dataSource.value = type1Data.value;
        tabvalue.value = 2;
        break;
      case 3:
        dataSource.value = type2Data.value;
        tabvalue.value = 3;
        break;
      default:
        break;
    }
  };

  // 通过path查找对应的模型index
  const searchIndex = (value: any, arr: any) => {
    let idx: any = null;
    arr.forEach((item: any, index: any) => {
      if (value === item[0]) idx = index;
    });
    return idx;
  };

  const gotoEntity = (row: any) => {
    // 两个模型碰撞使用装配定位
    if (route?.query?.modelNumber === '2') {
      const pathData = viewer.getPathMapIndex(); // 获取专配模型数据
      pathData.sort((a: any, b: any) => a[1] - b[1]); // 有时候装配模型顺序并不是正确的  需要从对应的模型第二个元素（下标）来
      const indexA = searchIndex(row.clash_a.path, pathData);
      const indexB = searchIndex(row.clash_b.path, pathData);

      const typeA: any = row.clash_b.path.split('/')[3].split('.')[1];
      const typeB: any = row.clash_b.path.split('/')[3].split('.')[1]; // 模型装配定位构件id 需要在id前加index(此处模型a对应的index为0 模型b对应index为1) ifc文件id需要添加''

      const clashAId =
        typeA === 'ifc'
          ? btoa(`${indexA}.'${row.clash_a.id}'`)
          : btoa(`${indexA}.${row.clash_a.id}`);
      const clashBId =
        typeB === 'ifc'
          ? btoa(`${indexB}.'${row.clash_b.id}'`)
          : btoa(`${indexB}.${row.clash_b.id}`);
      const colors = [
        [255, 54, 0],
        [0, 54, 255],
      ];
      viewer.collisionGotoByComponentIds([clashAId, clashBId], colors);
    } else {
      // 一个模型碰撞使用正常定位
      viewer.clearEntitiesColor(lastObjectData.value);
      lastObjectData.value = [`'${row.clash_a.id}'`, `'${row.clash_b.id}'`];
      viewer.isolateEntities([`'${row.clash_a.id}'`, `'${row.clash_b.id}'`]);
      viewer.gotoByIds([`'${row.clash_a.id}'`, `'${row.clash_b.id}'`]);
      const clashAState: any = [[`'${row.clash_a.id}'`, [220, 54, 46]]];
      viewer.setEntitiesColor(clashAState, true);
      const clashBState: any = [[`'${row.clash_b.id}'`, [0, 77, 220]]];
      viewer.setEntitiesColor(clashBState, true);
    }
  };

  const getClashInfo = async () => {
    tableLoading.value = true;
    const params = {
      clash_id: graphicEngineInfo?.clashId,
      page_num: 1,
      page_size: 1000,
      type: 3,
      group_id: route.query.projectId,
    };
    const allres: any = await GetXBaseClashInfo({ ...params });
    if (allres.code === 0) {
      allData.value = allres.data.list;
      collisionNum.value[0] = allres.data.total;
      collisionNum.value[1] = allres.data.total_hard;
      collisionNum.value[2] = allres.data.total_clearance;

      type1Data.value = allData.value.filter((item: any) => {
        return item.type === 1;
      });
      type2Data.value = allData.value.filter((item: any) => {
        return item.type === 2;
      });
      tableLoading.value = false;
    }
    dataSource.value = allData.value;
  };
  const closeList = () => {
    viewer.clearEntitiesColor(lastObjectData.value);
    viewer.showAll();
    toolStore.collisionInfoShow.value = false;
    emits('close');
  };

  const exportFile = async () => {
    const formData = new URLSearchParams();
    formData.append('clashId', graphicEngineInfo?.clashId);
    formData.append('fileId', route.query.idStr?.toString() || '');
    formData.append('projectId', route.query.projectId?.toString() || '');
    // 1硬碰撞，2间隙碰撞 3全部。
    console.log(tabvalue.value);
    let typesStr = '3';
    if (tabvalue.value === 2) {
      typesStr = '1';
    } else if (tabvalue.value === 3) {
      typesStr = '2';
    }
    formData.append('type', typesStr);
    const res = await GetExportFile(formData);
    createUrl(res.data, '碰撞检测报告.docx');
  };

  getClashInfo();
</script>

<style lang="less" scoped>
  .compare-list-box {
    width: 800px;
    overflow: hidden;
    box-shadow: 0 0 8px rgb(0 0 0 / 15%);
    flex-shrink: 0;
    .list-card {
      height: 100%;
      border-top: 0;
      border-bottom: 0;
    }

    .icon-close {
      cursor: pointer;
    }
    .compare-card-box {
      height: 100%;
      overflow-y: scroll;
      flex: 1;
    }
    :deep(.list-card > .arco-card-body) {
      height: calc(100% - 46px);
      padding: 0;
      display: flex;
      flex-direction: column;
    }
    :deep(.arco-card + .arco-card) {
      margin-top: 12px;
    }
    :deep(.arco-card-actions) {
      border-top: 1px solid var(--color-neutral-3);
      margin-top: 0;
      padding: 8px 12px;
    }
  }
  :deep(.arco-table-tr) {
    cursor: pointer;
  }
</style>
