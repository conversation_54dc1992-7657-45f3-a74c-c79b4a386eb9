import { AppRouteRecordRaw } from '../types';

const FILE: AppRouteRecordRaw = {
  path: 'file',
  name: 'file',
  component: () => import('@/views/projectSpace/file/index.vue'),
  props: (route: any) => ({
    type: route.query.type,
    file: route.query.file,
  }),
  meta: {
    locale: 'menu.cdex.file',
    requiresAuth: true,
    icon: 'icon-file',
    order: 1,
    showAI: true,
    globalMode: ['project'],
  },
};

export default FILE;
