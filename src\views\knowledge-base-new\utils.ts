export default function getFileType(fileName: string) {
  let type = '';
  const suffix = fileName.split('.').pop()?.toLowerCase() || '';
  if (suffix === 'docx' || suffix === 'doc') {
    type = 'DOC';
  } else if (suffix === 'pdf') {
    type = 'PDF';
  }
  return type;
}
export function extractSourceArray(checkedRows: any[]) {
  return checkedRows.map((row) => ({
    sourceFileId: row.folderId ? row.id ?? row.fileId : null,
    sourceFolderId: !row.folderId ? row.id ?? row.folderId : null,
    sourceProjectId: row.projectId ?? null,
    sourceTeamId: row.teamId ?? null,
  }));
}
