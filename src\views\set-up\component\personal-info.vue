<template>
  <div class="personal-info-container">
    <div class="personal-info-content">
      <a-row>
        <!-- <h2 class="personal-info-title">{{ t('personal.info.title') }}</h2> -->
        <table-title
          :title="$t('personal.info.title')"
          class="personal-info-title"
        ></table-title>
      </a-row>
      <a-row>
        <a-col :span="8">
          <!-- 头像上传组件 -->
          <div class="avatar-upload">
            <a-upload
              :action="null"
              :auto-upload="false"
              :file-list="avatarFile ? [avatarFile] : []"
              :show-file-list="false"
              accept="image/*"
              @change="handleChange"
              @progress="handleProgress"
            >
              <template #upload-button>
                <div class="arco-upload-trigger">
                  <img
                    v-if="avatarFile && avatarFile.url"
                    :src="avatarFile.url"
                    :size="80"
                    class="clickable-avatar avatar-img"
                    style="width: 120px; height: 120px"
                  />
                  <img
                    v-else-if="UserInfoForm.createUserParam.avatarToken"
                    :src="
                      '/work/api/sys-storage/download_image?f8s=' +
                      UserInfoForm.createUserParam.avatarToken
                    "
                    class="clickable-avatar avatar-img"
                    style="width: 120px; height: 120px"
                  />
                  <a-avatar v-else :size="120" class="clickable-avatar">
                    {{
                      UserInfoForm.createUserParam.userFullname?.substring(
                        0,
                        1
                      ) || ''
                    }}
                  </a-avatar>
                </div>
              </template>
              <!-- 保持原有的编辑遮罩和进度条 -->
              <div class="avatar-edit-mask">
                <icon-edit />
              </div>
              <a-progress
                v-if="
                  avatarFile &&
                  avatarFile.status === 'uploading' &&
                  avatarFile.percent < 100
                "
                :percent="avatarFile.percent"
                type="circle"
                size="mini"
                :style="{
                  /* 原有样式 */
                }"
              />
            </a-upload>
          </div>
        </a-col>
      </a-row>
      <a-row>
        <a-form :model="UserInfoForm" layout="vertical" class="userInfoForm">
          <a-row :gutter="40">
            <a-row>
              <a-col :span="8.2">
                <a-form-item
                  field="userFullname"
                  :label="t('personal.info.fullname')"
                  label-col-flex="100px"
                  class="userInfoFormItem"
                >
                  <a-input
                    v-model="UserInfoForm.createUserParam.userFullname"
                    :placeholder="t('personal.info.fullname.placeholder')"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8.2">
                <a-form-item
                  field="角色"
                  :label="t('personal.info.role')"
                  label-col-flex="80px"
                  class="userInfoFormItem"
                >
                  <a-select
                    v-model="UserInfoForm.userExtraInfo.userRole"
                    :placeholder="t('personal.info.role.placeholder')"
                    allow-clear
                  >
                    <a-option
                      v-for="(value, index) in roleOptions"
                      :key="index"
                      :value="value"
                    >
                      {{ value }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="8.2">
                <a-form-item
                  field="email"
                  :label="t('personal.info.email')"
                  label-col-flex="80px"
                  class="userInfoFormItem"
                  disabled
                >
                  <a-input
                    v-model="UserInfoForm.createUserParam.email"
                    :placeholder="t('personal.info.email.placeholder')"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8.2">
                <a-form-item
                  field="phone"
                  :label="t('personal.info.phone')"
                  label-col-flex="80px"
                  class="userInfoFormItem"
                  disabled
                >
                  <a-input
                    v-model="UserInfoForm.createUserParam.phone"
                    :placeholder="t('personal.info.phone.placeholder')"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="8.2">
                <a-form-item
                  field="language"
                  :label="t('personal.info.language')"
                  label-col-flex="80px"
                  class="userInfoFormItem"
                >
                  <a-select
                    v-model="currentLanguage"
                    :placeholder="t('personal.info.language.placeholder')"
                    allow-clear
                  >
                    <a-option value="zh-CN">{{
                      t('personal.info.language.zh')
                    }}</a-option>
                    <a-option value="en-US">{{
                      t('personal.info.language.en')
                    }}</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8.2">
                <a-form-item
                  field="userNo"
                  :label="t('personal.info.account')"
                  label-col-flex="80px"
                  class="userInfoFormItem"
                  disabled
                >
                  <a-input
                    v-model="UserInfoForm.createUserParam.userNo"
                    :placeholder="t('personal.info.account.placeholder')"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-row>
        </a-form>
      </a-row>

      <div v-if="ccccPermission === '1'" class="permission">
        <span>{{ $t('userSetting.jiao-jian-authority') }}</span>
        <a-checkbox-group v-model="receiveMode" direction="vertical" :max="1">
          <a-checkbox :value="1">{{
            $t('userSetting.project-members')
          }}</a-checkbox>
          <a-checkbox :value="2">{{
            $t('userSetting.all-members')
          }}</a-checkbox>
          <a-checkbox :value="3">{{
            $t('userSetting.specified-user')
          }}</a-checkbox>
        </a-checkbox-group>
        <a-input-tag
          v-model="form.member"
          :max-tag-count="4"
          class="member-tag"
          :placeholder="$t('please-select')"
          @focus="
            () => {
              selUserVisible = true;
            }
          "
        />
      </div>

      <div class="bottom">
        <a-button class="common-button" @click="handleClose" type="outline">{{
          t('personal.info.reset')
        }}</a-button>
        <a-button type="primary" class="common-button" @click="handleSubmit">{{
          t('personal.info.save')
        }}</a-button>
      </div>
    </div>
  </div>
  <select-members
    v-model:visible="selUserVisible"
    :data="memberData"
    :hide-group="true"
    @select-member="selectMember"
  ></select-members>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted, computed, watch } from 'vue';
  import { useUserStore } from '@/store';
  import { useI18n } from 'vue-i18n';
  import {
    getRoleList,
    getCDexWorkUserInfoApi,
    changeCDexWorkUserInfoApi,
    getCompanyUserInfo,
  } from '@/api/user';
  import { Message } from '@arco-design/web-vue';
  import { IconEdit } from '@arco-design/web-vue/es/icon';
  import useUploadFileStore from '@/store/modules/upload-file/index';
  import {
    uploadFile,
    mergeChunk,
    queryUploadInfo,
    queryUploadId,
    addMergaFile,
    queryFileBlockList,
    uploadFolder,
  } from '@/api/upload-file';
  import useLocale from '@/hooks/locale';
  import local from '@/utils/localstorage';
  import selectMembers from '@/components/selectMembers/index.vue';
  import TableTitle from '@/components/table-title/index.vue';


  const { t } = useI18n();

  const receiveMode = ref<any>([]);
  const form: any = ref({});
  const memberData = ref([]);
  const selUserVisible = ref(false);

  const userStore = useUserStore();
  const username = computed(() => userStore.username);
  const companyId = computed(() => userStore.companyId);
  // 接口节流限制
  const canSendUploadFile = ref(true);

  const { changeLocale, currentLocale } = useLocale();

  // 使用独立的 ref 来管理语言选择
  const currentLanguage = computed({
    get: () => currentLocale.value,
    set: (value) => {
      changeLocale(value);
    },
  });

  // 修改语言值的映射
  const languageMap = {
    简体中文: 'zh-CN',
    English: 'en-US',
  };
  const reverseLanguageMap = {
    'zh-CN': '简体中文',
    'en-US': 'English',
  };

  // 使用 reactive 定义 form
  const UserInfoForm = reactive({
    createUserParam: {
      userFullname: '',
      email: '',
      phone: '',
      userName: '', // 必传
      avatarToken: '',
      userNo: '',
    },
    userExtraInfo: {
      allowInfoList: [],
      language: 'zh-CN',
      userRole: '',
      // userType: '',
      fid: '', // 必传
      receiveMode: null,
    },
  });

  // 添加头像文件状态
  const avatarFile = ref();

  // 定义 roleOptions
  const roleOptions = reactive([]);

  // 加载角色列表
  const loadRoleList = async () => {
    try {
      const { data } = await getRoleList();
      // console.log("roleList", data);
      roleOptions.splice(0, roleOptions.length, ...data);
    } catch (err) {
      console.log('roleListErr', err);
      if (typeof err === 'string') {
        Message.error(err);
      } else {
        Message.error(t('personal.info.role.load.failed'));
      }
    }
  };

  // 加载用户信息
  const loadCDexWorkUserInfo = async () => {
    try {
      const params = {
        userName: username.value,
      };
      const result = await getCDexWorkUserInfoApi(params);
      const createUserParam = result?.data?.createUserParam;
      const userExtraInfo = result?.data?.userExtraInfo;
      // console.log(result,111111)

      if (userExtraInfo.allowInfoList?.length > 0) {
        const fullnames = userExtraInfo.allowInfoList.map(
          (item: { allowUserFullname: string }) => item.allowUserFullname
        );
        const usernames = userExtraInfo.allowInfoList.map(
          (item: { allowUserName: string }) => item.allowUserName
        );
        const ids = userExtraInfo.allowInfoList.map(
          (item: { allowUserId: string }) => item.allowUserId
        );

        form.value.member = fullnames;
        // 使用 map 方法组合成对象数组
        memberData.value = fullnames.map((fullname: string, index: number) => ({
          id: ids[index],
          userName: usernames[index],
          userFullname: fullname,
        }));
        UserInfoForm.userExtraInfo.allowInfoList = ids.map((id: string) => ({
          allowUserId: id,
        }));
      }
      receiveMode.value =
        userExtraInfo?.receiveMode !== 0 ? [userExtraInfo?.receiveMode] : [];

      UserInfoForm.userExtraInfo.fid = userExtraInfo?.fid || '';
      UserInfoForm.createUserParam.userName = createUserParam?.userName || '';
      // 头像
      UserInfoForm.createUserParam.avatarToken =
        createUserParam?.avatarToken || '';

      UserInfoForm.userExtraInfo.userRole = userExtraInfo?.userRole || '';
      UserInfoForm.createUserParam.userFullname =
        createUserParam?.userFullname || '';
      UserInfoForm.createUserParam.email = createUserParam?.email || '';
      UserInfoForm.createUserParam.phone = createUserParam?.phone || '';
      UserInfoForm.createUserParam.userNo =
        createUserParam?.userNo || t('personal.info.account.unbind');

      // 根据当前语言环境设置语言显示
      UserInfoForm.userExtraInfo.language =
        currentLocale.value === 'zh-CN' ? '简体中文' : 'English';
    } catch (err) {
      // console.log("getCDexWorkUserInfoApi", err);
      if (typeof err === 'string') {
        Message.error(err);
      }
    }
  };

  // 角色列表筛选逻辑,暂无需要
  // const roleList = () => {
  //   loadCDexWorkUserInfo();
  //   console.log('点击筛选');
  //   // todo:在这里添加调用获取角色接口
  // };

  // 保存fileToken的方法
  const saveFileToken = (token) => {
    // 这里替换为实际的用户信息存储逻辑
    UserInfoForm.createUserParam.avatarToken = token;
    console.log(
      '头像上传成功-----UserInfoForm.createUserParam.avatarToken',
      UserInfoForm.createUserParam.avatarToken
    );
    // Message.success('头像上传成功');
  };

  const upLoadFile = async (file) => {
    if (!canSendUploadFile.value) {
      Message.error(t('setup.frequent.request.message'));
      return;
    }
    canSendUploadFile.value = false;
    const uploadFileStore = useUploadFileStore();

    try {
      // 1. 计算文件MD5
      const md5Value = await uploadFileStore.getMd5Key(file);

      // 2. 调用secondPass接口验证文件状态
      const secondPassRes = await queryUploadInfo({
        fileName: file.name,
        fileSize: file.size,
        md5: md5Value,
        g9s: uploadFileStore.groupToken,
        e9y: uploadFileStore.e9y, // 补充必要参数
      });

      // 3. 根据返回结果处理不同情况
      let uploadId = '';
      let objectName = '';
      let uploadedChunks = []; // 新增：存储已上传分片列表

      if (secondPassRes.data.resultType === 0) {
        // 极速秒传
        saveFileToken(secondPassRes.data.sysOssVO.fileToken);
        return;
      }

      if (secondPassRes.data.resultType === 2) {
        // 断点续传逻辑
        uploadId = secondPassRes.data.bigFileVO.uploadId;
        objectName = secondPassRes.data.bigFileVO.objectName;

        // 新增：获取已上传分片列表
        const uploadedRes = await queryFileBlockList({
          uploadId,
          objectName,
          endPointKey: uploadFileStore.e9y,
        });
        uploadedChunks = uploadedRes.data.map((item) => item.partNumber);
      } else {
        // 新文件上传逻辑
        const uploadIdRes = await queryUploadId({
          e9y: uploadFileStore.e9y,
          fileName: file.name,
          md5: md5Value,
          g9s: uploadFileStore.groupToken,
        });
        uploadId = uploadIdRes.data.uploadId;
        objectName = uploadIdRes.data.objectName;
      }

      // 4. 分片上传 (修改过滤逻辑)
      const { chunkSize } = uploadFileStore;
      console.log('chunkSize', chunkSize, uploadFileStore.chunkSize);
      console.log('file.size', file.size);
      const chunkCount = Math.ceil(file.size / chunkSize);
      const chunkPromisesResult = [];
      console.log('chunkCount', chunkCount);
      // 生成所有分片的Promise数组

      const chunkPromises = Array.from(
        { length: chunkCount },
        (__, chunkIndex) => {
          // 新增：检查分片是否已存在
          if (uploadedChunks.includes(chunkIndex + 1)) {
            console.log(`分片 ${chunkIndex} 已存在，跳过上传`);
            chunkPromisesResult.push({
              etag: 'pre-uploaded', // 使用预上传标记
              number: chunkIndex + 1,
            });
            return Promise.resolve();
          }

          const chunk = file.slice(
            chunkIndex * chunkSize,
            (chunkIndex + 1) * chunkSize
          );

          const formData = new FormData();
          formData.append('e9y', uploadFileStore.e9y);
          formData.append('uploadId', uploadId);
          formData.append('objectName', objectName);
          formData.append('chunk', chunkIndex);
          formData.append('chunkNumber', chunkCount);
          formData.append('file', chunk);
          formData.append('lastModifiedDate', file.lastModifiedDate);
          formData.append('size', file.size);

          return uploadFile(formData).then((res) => {
            const params = {
              etag: res.data,
              number: chunkIndex + 1,
            };
            chunkPromisesResult.push(params);
            console.log('分片上传完成 chunkIndex:', chunkIndex, res);
            return res;
          });
        }
      );
      const uploadResults = await Promise.all(chunkPromises);
      console.log('全部分片上传完成', uploadResults);
      console.log(
        'chunkPromisesResult',
        chunkPromisesResult.length,
        chunkPromisesResult
      );
      console.log(
        'chunkPromisesResult',
        chunkPromisesResult.sort((a, b) => a.number - b.number)
      );

      // 5. 合并分片需要调整 (新增验证逻辑)
      const mergeRes = await mergeChunk({
        e9y: uploadFileStore.e9y,
        fileName: file.name,
        fileSize: file.size,
        groupToken: uploadFileStore.groupToken,
        list:
          chunkPromisesResult.length > 1
            ? chunkPromisesResult.sort((a, b) => a.number - b.number)
            : chunkPromisesResult,
        md5: md5Value,
        objectName,
        uploadId,
      });
      console.log('mergeRes', mergeRes);

      // 6. 存储最终的fileToken
      saveFileToken(mergeRes.data.fileToken); // 根据实际接口返回修改
      console.log('mergeRes.data.fileToken', mergeRes.data.fileToken);
    } catch (error) {
      console.error('文件上传失败:', error);
      // Message.error(t('personal.info.avatar.upload.failed'));
    } finally {
      canSendUploadFile.value = true;
    }
  };

  // 修改处理函数
  const handleChange = async (_, currentFile) => {
    console.log('currentFile', currentFile);
    avatarFile.value = currentFile.file;
    avatarFile.value.url = currentFile.url;
    // console.log("avatarFile", avatarFile.value);
  };

  const handleProgress = (currentFile) => {
    // console.log("22222222222222222222222222222");
    console.log('上传进度', currentFile);
    avatarFile.value = currentFile;
  };

  // 处理提交逻辑
  const handleSubmit = async () => {
    if (!UserInfoForm.userExtraInfo.userRole) {
      Message.info(t('personal.info.role.required'));
      return;
    }

    if (avatarFile.value) {
      // 判断文件格式
      const validImageTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = avatarFile.value.type;

      // 检查文件扩展名作为备用验证
      const fileName = avatarFile.value.name.toLowerCase();
      const validExtensions = ['.jpg', '.jpeg', '.png'];
      const hasValidExtension = validExtensions.some((ext) =>
        fileName.endsWith(ext)
      );

      if (!validImageTypes.includes(fileType) && !hasValidExtension) {
        Message.error(t('personal.info.avatar.format.error'));
        return;
      }
    }

    try {
      if (avatarFile.value) {
        await upLoadFile(avatarFile.value);
      }
      // console.log("upLoadRes", upLoadRes);
      // const submitData = {
      //   ...UserInfoForm,
      //   userExtraInfo: {
      //     ...UserInfoForm.userExtraInfo,
      //     language: reverseLanguageMap[UserInfoForm.userExtraInfo.language]
      //   }
      // };
      // const changeUserInfoRes = await changeCDexWorkUserInfoApi(submitData);
      UserInfoForm.userExtraInfo.receiveMode =
        receiveMode.value.length === 0 ? 0 : Number(receiveMode.value);
      const changeUserInfoRes = await changeCDexWorkUserInfoApi(UserInfoForm);
      if (changeUserInfoRes?.status) {
        console.log('changeUserInfoResStatus成功', changeUserInfoRes);
        userStore.avatarToken = UserInfoForm.createUserParam.avatarToken;
        Message.success(changeUserInfoRes.message);
      }
      // else {
      //   console.log('changeUserInfoResStatus失败', changeUserInfoRes);
      //   Message.error(
      //     changeUserInfoRes.message || t('personal.info.update.failed')
      //   );
      // }
      console.log('changeUserInfoRes', changeUserInfoRes);
      // console.log('表单提交', UserInfoForm);
    } catch (error) {
      console.log('表单提交失败', error);
      if (typeof error === 'string') {
        Message.error(t('personal.info.submit.failed'));
      }
    }
  };

  // 处理重置逻辑
  const handleClose = () => {
    loadCDexWorkUserInfo();
    // console.log('重置');
  };

  const ccccPermission = ref<string>(''); // 添加交建通权限状态变量
  /**
   * 获取用户交建通权限
   */
  const getCcccPermissions = async () => {
    if (companyId.value === '100000') {
      const res = await getCompanyUserInfo({
        userName: username.value,
      });
      ccccPermission.value = res.data.companyUserType;
    }
  };

  // 获取选择成员
  const selectMember = (user: any) => {
    memberData.value = user;
    form.value.member = user?.map((item: any) => item.userFullname);

    const allowList = memberData.value.map((item: { id: string }) => ({
      allowUserId: item.id,
    }));
    UserInfoForm.userExtraInfo.allowInfoList = allowList;
  };

  // 页面加载时执行
  onMounted(() => {
    getCcccPermissions();
    loadCDexWorkUserInfo();
    loadRoleList();
  });

  // 监听语言变化
  watch(currentLanguage, (newValue) => {
    if (newValue) {
      UserInfoForm.userExtraInfo.language = reverseLanguageMap[newValue];
    }
  });
</script>

<style scoped lang="less">
  .personal-info-container {
    height: 100%;
    width: 100%;
    overflow: auto;
    position: relative;
    padding: 20px 20px;
    box-sizing: border-box;
    min-width: 1000px;
  }

  .personal-info-content {
    background-color: #fff;
    border-radius: 8px;
    width: 100%;
    height: 100%;
    // padding: 20px;
  }

  .personal-info-title {
    padding: 0;
  }

  /* 修改表单布局相关样式 */
  .userInfoForm {
    width: 100%;
    box-sizing: border-box;
    // padding-left: 20px;

    .userInfoFormItem {
      width: 100%;
      // margin-bottom: 20px;
      margin-right: 20px;
    }
    .userInfoFormItem input {
      height: 36px;
      border-radius: 4px;
      border: 1px solid #c9cdd4;
      text-align: left;
      padding: 0 10px;
    }

    :deep(.arco-form-item-label) {
      font-size: 16px;
    }

    :deep(.arco-form-item-wrapper-col) {
      width: 400px;
      height: 34px;
      border: 1px solid #c9cdd4;
      border-radius: 4px;
      overflow: hidden;
    }

    :deep(.arco-input-wrapper) {
      border: none !important;
      background-color: transparent;
    }
    :deep(.arco-select-view) {
      background-color: transparent;
    }
    :deep(.arco-select-view-focus) {
      border: none !important;
      outline: none !important;
    }
    :deep(.arco-input-focus) {
      border: none;
    }
  }

  .avatar-upload {
    margin: 20px 0 20px 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    /* 改为左对齐 */
    width: 180px;
  }

  .permission {
    position: relative;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    line-height: 36px;
    margin-bottom: 24px;

    .member-tag {
      position: absolute;
      width: 405px;
      left: 420px;
      bottom: 0;
    }
  }

  .arco-checkbox {
    width: 370px;
  }

  /* 修改底部按钮样式 */
  .bottom {
    // padding: 20px 0;
    margin-bottom: 24px;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    align-items: center;
  }

  .common-button {
    height: 32px;
    font-size: 14px;
    padding: 0 16px;
    border-radius: 4px;
    margin-right: 20px;
  }

  .common-button:disabled {
    background-color: #f5f5f5;
    border: none;
    cursor: not-allowed;
  }

  /* 确保select和input样式一致 */
  :deep(.arco-select) {
    width: 100%;
  }

  :deep(.arco-select-view) {
    height: 36px;
    border-radius: 4px;
  }

  :deep(.arco-form-item) {
    margin-bottom: 24px;
  }

  :deep(.arco-row) {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .avatar-container {
    position: relative;
    width: 80px;
    height: 80px;
    cursor: pointer;
  }

  .avatar-edit-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .avatar-container:hover .avatar-edit-mask {
    opacity: 1;
  }

  .clickable-avatar {
    width: 100%;
    height: 100%;
  }

  .avatar-img {
    /* width: 120px;
  height: 120px; */
    border-radius: 50%;
    object-fit: cover;
    display: block;
  }

  .arco-upload-trigger {
    :deep(.arco-avatar) {
      background-color: #e9eefa;
    }
  }
</style>
