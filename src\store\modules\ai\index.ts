import { defineStore } from 'pinia';
import { AiState } from './types';

const useAiStore = defineStore('ai', {
  state: (): AiState => {
    return {
      aiModuleVisible: false,
      aiInstance: null, // AI实例，第一次加载AI对话使用new xkForAichatsdk拿到实例，后续使用aiInstance.updateOptions更新
    };
  },

  getters: {
    // appCurrentSetting(state: AppState): AppState {
    //   return { ...state };
    // },
    // appDevice(state: AppState) {
    //   return state.device;
    // },
    // appAsyncMenus(state: AppState): RouteRecordNormalized[] {
    //   return state.serverMenu as unknown as RouteRecordNormalized[];
    // },
  },

  actions: {},
});

export default useAiStore;
