import {
  FolderMessage,
  FileAndFolderNodeMessage,
  FileAndFolderMessage,
} from '@/api/tree-folder';

export interface FileVersion {
  createBy?: string;
  createDate?: string;
  deleteFlag?: number;
  description?: string;
  fileId?: string;
  fileToken?: string;
  folderId?: string;
  id?: string;
  isLocked?: boolean;
  name?: string;
  projectId?: string;
  size?: string;
  teamId?: string;
  type?: string;
  updateBy?: string;
  updateDate?: string;
  updateName?: string;
  version?: string;
}

interface FilterForm {
  /** 文件名称 */
  name: string;
}

interface shareModal {
  visable: boolean;
  isBatch: boolean;
}
interface wpsViewModal {
  visible: boolean;
  title: string;
  url: string;
}
interface imgViewModal {
  visible: boolean;
  title: string;
  fileToken: string;
}
interface convertModal {
  visible: boolean;
  record: any;
  type: string;
}

interface shareFileModal {
  visible: boolean;
  type?: string;
}

interface nullifyModal {
  visable: boolean;
}
interface selectedNode {
  children: Array<any>;
  createDate: string;
  files: Array<any>;
  id: string;
  isFileOrFolder: number;
  key: string;
  name: string;
  parentId: string;
  projectId: string;
  sunFolders: Array<any>;
  sysType: number;
  teamId: string;
  title: string;
  type: string;
}

interface ObjectType {
  [key: string]: any;
}

interface fileLinkModalIsShowType {
  visible: boolean;
}

export interface shareFormType {
  expiryDate: string;
  checkType: number;
  encryptionMode: string;
  secretkey: string;
}

export interface shareLinkType {
  uuid: string;
  extractedCode?: string;
}

export interface AttachmentFileDto {
  attachmentId: string;
  attachmentName: string;
  attachmentToken: string;
  attachmentType: string;
  correlationId: string;
  fileId: string;
  fileSize: number;
  parentId: string;
  planEndTime: string;
  planStartTime: string;
  projectId: string;
  scheduleDetailId: string;
  scheduleTitle: string;
  teamId: string;
  teamName: string;
  type: string;
  updateBy: string;
  updateByName: string;
  updateDateAttachment: string;
  updateDateItemOrMeeting: string;
}

export interface AttachmentDto {
  folderId: string;
  title: string;
  type: string;
  scheduleDetailId: string;
  updateDateItemOrMeeting: string;
  updateByName: string;
  teamId: string;
  child: AttachmentDto[] | null;
  attachmentFileMap: { attachmentFileInfoList: AttachmentFileDto[] } | null;
}

export interface FileState {
  currentFolder: FolderMessage;
  currentIdPath: string;
  checkTableKeys: string[];
  checkTableData: FileAndFolderMessage[];
  /** 右侧列表数据 */
  tableData: FileAndFolderMessage[];
  /** 展开的树节点 */
  expandedKeys: string[];
  /** 选中的树节点 */
  selectedKeys: string[];
  /** 左侧目录树数据 */
  treeData: FileAndFolderNodeMessage[];
  /** 面包屑数据 */
  breadcrumbList: { name: string; id: string }[];
  // 图片预览弹窗开关
  imgViewModal: imgViewModal;
  /** 判断当前选择文件夹是否包含sysType */
  hiddenSlot: number;
  /** 右侧列表加载状态 */
  tableLoading: boolean;
  newFolderName: string;
  allTreeData: FolderMessage[];
  moveIds: { fileIds: string[]; folderIds: string[] };
  downloadList: any[];
  selectedTableRowkeys: string[];
  isAbandon: boolean;
  /**当前会议或者事项 */
  currentAttach: AttachTableView;
  attachMoveIds: AttachMoveIds;
  attachPathList: { name: string; id: string }[];
}

export interface AttachTableView {
  id: string;
  isFolder: boolean;
  teamName: string;
  updateName: string;
  updateTime: string;
  detailId: string;
  type: string;
  fileToken?: string;
  title: string;
  child: AttachmentDto[];
  attachmentList: AttachmentFileDto[];
}

export interface AttachMoveParams {
  attachmentFileIdList: string[];
  folderId: string;
  moveParamList: {
    directoryId: string;
    type: string;
  }[];
}

export type AttachMoveIds = Omit<AttachMoveParams, 'folderId'>;

/**
 * SearchState
 * 1 正在搜索状态
 * 2 点击按钮退出搜索状态
 * 3 强制退出搜索状态
 */
export type SearchState = '1' | '2' | '3';
