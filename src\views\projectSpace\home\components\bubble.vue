<template>
  <div
    class="bubble"
    :style="
      'left: ' +
      bubblePos[0] +
      '; top: ' +
      bubblePos[1] +
      '; display: ' +
      bubbleShow
    "
  >
    <div class="cont">
      <div v-if="bubbleObj.name" class="line">
        {{ bubbleObj.name }}
      </div>
      <div v-if="!bubbleObj.name" class="line">
        <span
          >{{ bubbleObj.shareName ? $t('design.share') : $t('design.delivery')
          }}{{ $t('design.name') }}</span
        >
        {{ bubbleObj.shareName || bubbleObj.deliveryName }}
      </div>
      <div v-if="!bubbleObj.name" class="line">
        <span
          >{{ bubbleObj.shareName ? $t('design.share') : $t('design.delivery')
          }}{{ $t('design.people') }}</span
        >
        {{ bubbleObj.user }}
      </div>
      <div v-if="!bubbleObj.name" class="line">
        <span
          >{{ bubbleObj.shareName ? $t('design.share') : $t('design.delivery')
          }}{{ $t('design.time') }}</span
        >
        {{ bubbleObj.time }}
      </div>
      <div v-if="!bubbleObj.name" class="line">
        <span>{{ $t('design.process-state') }}</span>
        <a-tag
          v-if="bubbleObj.status != undefined"
          bordered
          size="small"
          :color="ProcessStatusColor[bubbleObj.status]"
          style="margin-left: 4px"
        >
          {{ ProcessStateMap[bubbleObj.status] || '' }}</a-tag
        >
      </div>
    </div>
    <div class="triangle"></div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, watch, ref, nextTick } from 'vue';
  import { ProcessStatusColor, ProcessStateMap } from '@/directionary/process';
  import useLocale from '@/hooks/locale';
  import { BubbleObject } from '../api';
  import { useAppStore } from '@/store';

  const app = useAppStore();

  const menuSwitch = computed(() => {
    return app.menuSwitch;
  });
  const props = defineProps({
    bubbleObject: {
      type: Object,
      default() {
        return {};
      },
    },
    bubblePosition: {
      type: Object,
      required: true,
    },
  });
  const { currentLocale } = useLocale();

  const bubbleObj = ref<BubbleObject>({});
  const bubblePos = ref(['1', '1']);

  const bubbleShow = computed(() => {
    return bubblePos.value[0] !== '0' && bubblePos.value[1] !== '0'
      ? 'block'
      : 'none';
  });

  watch(props.bubbleObject, (val) => {
    bubbleObj.value = val;
  });
  watch(props.bubblePosition, (val) => {
    bubblePos.value = val as string[];
  });

  const bubbleMarginLeft = ref('0px');
  watch(
    currentLocale,
    () => {
      nextTick(() => {
        const leftWrap = document.getElementsByClassName('left-wrap');
        if (leftWrap) {
          bubbleMarginLeft.value = `${271 + leftWrap[0].clientWidth}px`;
        } else {
          bubbleMarginLeft.value = `471px`;
        }
      });
    },
    {
      immediate: true,
    }
  );

  watch(menuSwitch, (val) => {
    nextTick(() => {
      const leftWrap = document.getElementsByClassName('left-wrap');
      if (val) {
        bubbleMarginLeft.value = `${251 + leftWrap[0].clientWidth}px`;
      } else {
        bubbleMarginLeft.value = `477px`;
      }
    });
  });
</script>

<style lang="less" scoped>
  .bubble {
    position: absolute;
    z-index: 101;
    transform: translateX(-100%);
    margin-left: v-bind(bubbleMarginLeft);
    margin-top: 148px;
    white-space: nowrap;
    transition: opacity 0.3s ease-in-out;
    .triangle {
      width: 0;
      height: 0;
      border: 6px solid transparent;
      border-top-color: #ffffff;
      margin-left: calc(100% - 32px);
    }
    .cont {
      min-width: 50px;
      display: block;
      line-height: 24px;
      padding: 8px 12px;
      background-color: #ffffff;
      border-radius: 4px;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
      .line > span:first-child {
        display: inline-block;
        color: #4e5969;
        text-align: right;
        margin-right: 8px;
      }
    }
  }
  .triangle {
    fill: #86909c;
    stroke: #86909c;
    stroke-width: 4;
  }
</style>
