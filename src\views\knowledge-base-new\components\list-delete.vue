<template>
  <div class="list-page">
    <PathAndOperations
      type="personal-list"
      @new-person-item-added="onNewItemAdded"
      @person-handle-upload="handleUpload(0)"
      @person-handle-download="handleDownload"
    />
    <div class="list">
      <a-spin ref="spin" :loading="tableLoading" class="folder-table-panel">
        <a-table
          ref="folderTable"
          :columns="columns"
          :data="personalCombinedList"
          :scroll="{ x: '100%', y: 'calc(100% - 16px)' }"
          :row-selection="{
            type: 'checkbox',
            showCheckedAll: true,
            onlyCurrent: true,
          }"
          row-key="id"
          :pagination="{
            showTotal: true,
            showPageSize: true,
            showJumper: true,
            defaultPageSize: 10,
            pageSizeOptions: [10, 20, 50, 100],
          }"
          @selection-change="selectionChange"
        >
          <template #name="{ record }">
            <div class="table-name" @click="onSelect(record)">
              <file-image
                :file-name="record.name ?? ''"
                :is-sysFile="false"
                :is-file="!!record.folderId"
                style="margin-right: 8px"
              />
              <a-tooltip
                v-if="!record.isAdd && !record.isEdit"
                :content="record.name"
              >
                <span
                  style="
                    display: inline-block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  "
                  class="file-name"
                  >{{ record.name }}</span
                >
              </a-tooltip>
              <div v-else-if="record.isAdd" class="file-name">
                <a-input
                  ref="addInputRef"
                  v-model="record.name"
                  style="width: 95%"
                  @blur="addFolderRequest(record)"
                  @keydown.enter="addFolderRequest(record)"
                />
              </div>
              <div
                v-else-if="record.isEdit && !record.folderId"
                class="file-name"
              >
                <a-input
                  ref="editInputRef"
                  v-model="record.name"
                  style="width: 95%"
                  @blur="addFolderRequest(record)"
                  @keydown.enter="addFolderRequest(record)"
                />
              </div>
            </div>
          </template>
          <!-- 转换状态 -->
          <template #status="{ record }">
            <span>{{
              record.fileToken &&
              !isPicFile(record) &&
              isRequiredConversion(record)
                ? formatStatus(record.status, record.isCombination, record)
                : ''
            }}</span>
          </template>
          <template #size="{ record }">
            {{ record.size ? getFileSize(record.size) : '' }}
          </template>
          <template #updateDate="{ record }">
            {{ record.updateDate || '' }}
          </template>
          <template #optional="{ record }">
            <a-button
              v-if="
                !isNeedToConvert(record.status, record.isCombination) &&
                record.folderId &&
                isRequiredConversion(record)
              "
              type="text"
              size="small"
              @click="reConvert(record)"
            >
              {{ t('file-manage.reconvert') }}
            </a-button>
            <a-button type="text" size="small" @click="handleRename(record)">
              重命名
            </a-button>
          </template>
        </a-table>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import { ref, toRefs, computed } from 'vue';
  import i18n from '@/locale/index';
  import PathAndOperations from './path-and-operations.vue';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import {
    isPicFile,
    isRequiredConversion,
    isWpsFile,
    isNeedToConvert,
    isSysFolder,
    isTopFolder,
  } from '@/views/projectSpace/file/utils';
  import { reConvertApi } from '@/views/projectSpace/file/api';
  import { getFileSize } from '@/utils/file';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';

  const { t } = i18n.global;
  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { personal, personalCombinedList } = storeToRefs(knowledgeBaseNewStore);
  const { currentFolder, folderList, breadcrumb, projectId, tableLoading } =
    toRefs(personal.value);

  const props = defineProps({
    path: {
      type: Array,
      default() {
        return [];
      },
    },
  });
  const columns = computed(() => {
    return [
      {
        title: t('file-manage.name'),
        dataIndex: 'name',
        slotName: 'name',
        sortable: {
          sortDirections: ['ascend', 'descend'],
        },
        width: 300,
        fixed: 'left',
      },
      {
        title: t('file-manage.size'),
        width: 90,
        dataIndex: 'size',
        slotName: 'size',
      },
      {
        title: t('file-manage.transition-status'),
        dataIndex: 'status',
        slotName: 'status',
        width: 140,
      },
      {
        title: t('file-manage.update-date'),
        width: 180,
        dataIndex: 'updateDate',
        slotName: 'updateDate',
      },
      {
        title: t('file-manage.operation'),
        slotName: 'optional',
        titleSlotName: 'optionalTitle',
        width: 160,
        fixed: 'right',
      },
    ];
  });
  function selectionChange(rowkeys: string[]) {
    knowledgeBaseNewStore.setSelectedByRowKeys(rowkeys);
    // fileStore.setSelectedTableRowkeys(rowkeys);
  }
  const formatStatus = (status: number, type: number, record: any) => {
    if (type === 2) {
      // 碰撞检查
      if ([-1].includes(status)) return t('file-manage.failed');
      if ([2].includes(status)) return t('file-manage.success');
      if ([0, 1].includes(status)) return t('file-manage.in-conversion');
    } else if (type === 1) {
      return t('file-manage.success');
    } else {
      if (isWpsFile(record)) return t('');
      // 普通模型文件
      if ([-7, -2, -1].includes(status)) return t('file-manage.failed');
      if ([0, 3].includes(status)) return t('file-manage.success');
      if ([-3, 1, 4].includes(status)) return t('file-manage.in-conversion');
      if (status === 2) return t('file-manage.in-queue');
      if (!status) return t('file-manage.not-starts'); // 新增模型状态--未开始
    }
    return '';
  };
  const reConvert = async (record: any) => {
    const params = {
      fileId: record.id,
    };
    const res: any = await reConvertApi(params);
    if (res.code === 8000000) {
      Message.info(t('file-manage.reconvert'));
      knowledgeBaseNewStore.getfiles();
    }
  };
  const handleRename = (item: any) => {
    // 重命名
    // 如果是新增状态，直接返回
    if (item.isAdd) {
      return;
    }
    item.isEdit = true;
    item.popupVisible = !item.popupVisible;
    nextTick(() => {
      const inputEdits = editInputRef.value;
      // 在 v-for 中 ref 会生成一个数组
      if (inputEdits && inputEdits.length) {
        inputEdits[inputEdits.length - 1].focus();
      }
    });
  };
  // 点击进入下一文件夹
  const onSelect = (item: any) => {
    // 如果点击文件夹，进入下一文件夹，如果点击文件，则进入预览文件
    if (!item.folderId) {
      // 文件夹点击事件，进入下一层
      knowledgeBaseNewStore.setPersonCurrentFolder(item);
      knowledgeBaseNewStore.pushBreadcrumb(item);
      knowledgeBaseNewStore.getPersonalFolder('personal');
      knowledgeBaseNewStore.getfiles('personal');
      return;
    }
    const needParams = {};
    if (item.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: item.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      Object.assign(needParams, params);
    }

    modelViewBim(item, projectId as string, needParams);
  };
  // 添加文件夹请求
  const addFolderRequest = useThrottleFn(async (item: any) => {
    const name = item.folderId ? item.filename : item.name;
    // 如果名称为空则认为撤销新增，删除列表中的该项
    if (name.trim() === '') {
      const index = folderList.value.findIndex((i: any) => i === item);
      if (index !== -1) {
        folderList.value.splice(index, 1);
      }
      // 可显示提示信息，例如：Message.info('撤销新增');
      return;
    }

    // 禁用字符校验，正则：不允许出现 \ / : * ? " < > |
    const pattern = /^[^\\/:*?"<>|]+$/;
    if (!pattern.test(name)) {
      Message.warning(t('file-manage.name-exclude-2'));
      return;
    }
    if (item.isAdd) {
      // 调用新增文件夹接口
      const res: any = await addChildFolder(item);
      if (res.code === 8000000) {
        Message.success(t('file-manage.success'));
        // 新增成功后取消 isAdd 标识
        item.isAdd = false;
      } else {
        Message.error(res.data);
      }
    }
    if (item.isEdit) {
      const param: any = {
        ...item,
        name: !item.folderId
          ? encode(item.name)
          : item.filename + item.filetype,
      };
      delete param.path;
      const res: any = item.folderId
        ? await updateFile(param)
        : await updateFolder(param);
      if (res.code === 8000000) {
        Message.success(t('file-manage.success'));
        item.isEdit = false;
      }
    }
    knowledgeBaseNewStore.getPersonalFolder('personal');
    knowledgeBaseNewStore.getfiles('personal');
  }, 1000);
</script>

<style scoped lang="less">
  .list-page {
    position: absolute;
    height: calc(100% - 40px);
    width: 100%;
  }

  .list {
    height: calc(100% - 64px);
    overflow: auto;
  }
  :deep(.arco-table-content .arco-scrollbar:nth-child(2)) {
    height: 100%;
  }
  :deep(.arco-table-header + .arco-scrollbar-track-direction-horizontal) {
    display: none;
  }
  .folder-table-panel {
    width: 100%;
    height: 100%;
    padding: 0 12px;
  }
  :deep(.arco-table-container) {
    height: calc(100% - 40px);
  }
  .file-name {
    cursor: pointer;
    transition: color 0.2s;
  }
  .file-name:hover {
    color: rgb(22, 93, 255);
  }
</style>
