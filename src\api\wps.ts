import axios from 'axios';
import { FolderMessage, FileMessage } from '@/api/tree-folder';

export interface FileAndFolderMessage extends FolderMessage, FileMessage {
  /** 0:folder 1:file */
  isFileOrFolder?: number;
}

export interface FileVersion {
  createBy?: string;
  createDate?: string;
  deleteFlag?: number;
  description?: string;
  fileId?: string;
  fileToken?: string;
  folderId?: string;
  id?: string;
  isLocked?: boolean;
  name?: string;
  projectId?: string;
  size?: string;
  teamId?: string;
  type?: string;
  updateBy?: string;
  updateDate?: string;
  updateName?: string;
  version?: string;
}

// 查询wpsurl
export function getWpsUrl(params: any) {
  return axios.get('/cde-collaboration/wps/link', { params });
}
// wps刷新版本
export function wpsRefresh(params: any) {
  return axios.get<any>(`/wps-system/wps/refresh`, {
    params,
    baseURL: '/wps',
  });
}
// wps转换
export function wpsConvert(params: any) {
  return axios.get('/wps-system/wps/async/convert', {
    params,
    baseURL: '/wps',
  });
}

// 获取可转换的格式
export function getConvertSeek(params: any) {
  return axios.get('/cde-collaboration/wps/convertSeek', { params });
}

// 获取可转换的格式
export function getCode(params: any) {
  return axios.get('/cde-collaboration/wps_system/code', { params });
}

// work获取系统对应的应用code
export function getWpsAppCodeWork(params: any) {
  return axios.get('/cde-work/wps_system/fileSoucre', { params });
}

// bim获取系统对应的应用code
export function getWpsAppCodeProject(params: any) {
  return axios.get('/cde-collaboration/wps_system/fileSoucre', { params });
}
