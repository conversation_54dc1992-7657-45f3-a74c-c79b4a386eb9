import { Message } from '@arco-design/web-vue';
import axios from 'axios';

export function download(file: any, blobs: Blob) {
  const blob: File = new File([blobs], '', {
    type: 'application/octet-stream',
  });
  if ('download' in document.createElement('a')) {
    const downloadElement = document.createElement('a');
    let href = '';
    if (window.URL) {
      href = window.URL.createObjectURL(blob);
    } else {
      href = window.webkitURL.createObjectURL(blob);
    }
    downloadElement.href = href;
    downloadElement.download = file.name || file.fileName;
    document.body.appendChild(downloadElement);
    downloadElement.click();
    if (window.URL) {
      window.URL.revokeObjectURL(href);
    } else {
      window.webkitURL.revokeObjectURL(href);
    }
    document.body.removeChild(downloadElement);
    URL.revokeObjectURL(href); // 释放 Blob
  } else if (
    'msSaveBlob' in navigator &&
    typeof navigator.msSaveBlob === 'function'
  ) {
    navigator.msSaveBlob(blob, file.name || file.fileName);
  }
}

export function fileDownload(file: any) {
  return axios.get('/sys-storage/download', {
    params: {
      f8s: file.fileToken,
    },
    responseType: 'blob',
    timeout: 0,
  });
}

// 下载单文件
async function handleSingleFile(file: any) {
  const res: any = await fileDownload(file);
  download(file, res.data);
}

// 下载源文件按钮点击事件
export async function downloadSource(currentFile: any) {
  Message.info('文件下载中，请稍候');
  // 文件
  handleSingleFile(currentFile);
  return currentFile;
}
