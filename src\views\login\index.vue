<template>
  <div class="container">
    <div class="content">
      <div id="contentInner" class="content-inner">
        <div class="login-title">
          <img class="logo-image" alt="logo" :src="logo" />
          <div class="login-title-text">CDex</div>
        </div>
        <!-- 若要保存Form数据，增加keep-alive -->
        <LoginForm
          v-if="loginMethod === LoginMethods.password"
          @change-login="changeLoginMethod"
        />
        <ForgetForm
          v-if="loginMethod === LoginMethods.forget"
          @change-login="changeLoginMethod"
        />
        <ChangePhone
          v-if="loginMethod === LoginMethods.changePhone"
          @change-login="changeLoginMethod"
        />
        <Register
          v-if="loginMethod === LoginMethods.register"
          :company-code="props.companyCode"
          @change-login="changeLoginMethod"
        />
        <Invite
          v-if="loginMethod === LoginMethods.invite"
          :invitation-id="props.invitationId"
          @change-login="changeLoginMethod"
        />
        <CompleteInformation
          v-if="loginMethod === LoginMethods.completeInfo"
          @change-login="changeLoginMethod"
          @completeInfoSuccess="onCompleteInfoSuccess"
        />
      </div>
      <div id="footer" class="footer">
        <Footer />
      </div>
    </div>
    <div class="banner"></div>
    <AccountSwitcherModal
      v-model:show-modal="showModal"
      :should-load="true"
      @select="handleAccountSelected"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, onBeforeMount, onMounted, provide } from 'vue';
  import { useRouter } from 'vue-router';
  import CryptoJS from 'crypto-js';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import Footer from '@/components/footer/index.vue';
  import logo from '@/assets/images/logo-signUp.png';
  import { dotToSlash } from '@/utils/index';
  import {
    setToken,
    clearToken,
    clearWpsToken,
    setWpsToken,
    setUserName,
  } from '@/utils/auth';
  import { getSocialInfoByToken } from '@/api/user';
  import { useUserStore } from '@/store';
  import LoginForm from './components/login-form.vue';
  import ForgetForm from './components/forget-form.vue';
  import ChangePhone from './components/change-phone.vue';
  import LoginMethods from './constant';
  import { getWpsToken, getWpsAppCode } from './api';
  import Register from './components/register-form.vue';
  import Invite from './components/invite-form.vue';
  import CompleteInformation from './components/complete-information-form.vue';
  import AccountSwitcherModal from '@/components/AccountSwitcherModal/AccountSwitcherModal.vue';

  const showModal = ref(false);
  const accessTokenVariable = ref('');
  const othersQueryVariable = ref();
  const redirectVariable = ref('');
  const router = useRouter();
  const userStore = useUserStore();
  const { t } = useI18n();
  const props = defineProps<{
    isRegister?: string;
    companyCode?: string;
    inviteCompany?: string;
    invitationId?: string;
  }>();

  // 存储完善信息返回的数据
  const completeInfoResultData = ref<any>(null);
  // 完善信息成功回调
  const onCompleteInfoSuccess = (data: any) => {
    completeInfoResultData.value = data;
  };

  const loginMethod = ref(LoginMethods.password);
  if (router.currentRoute.value.query.loginMethod === 'completeInfo') {
    loginMethod.value = LoginMethods.completeInfo;
  }

  const changeLoginMethod = (method: LoginMethods) => {
    loginMethod.value = method;
  };

  // 设置wpstoken
  const setWpsTokenHandle = async () => {
    const tokenParam = {
      grant_type: 'client_credentials',
      client_id: 'fusion',
      client_secret: 'fusion_secret',
    };
    const tokenData: any = await getWpsToken(tokenParam);
    setWpsToken(tokenData.access_token);
  };

  // 添加处理账号选择的方法
  const handleAccountSelected = async ({
    account,
    isSameScene,
  }: {
    account: any;
    isSameScene: boolean;
  }) => {
    try {
      // 优先使用完善信息返回的数据
      if (completeInfoResultData.value) {
        const data = completeInfoResultData.value;
        // 检查是否已经在完善信息组件中执行过token设置等操作
        if (!data.isFromCompleteInfo) {
          // 如果没有执行过，则在这里执行
          await setToken(data.access_token);
          setUserName(data.userName);
          userStore.userName = data.userName;
          await userStore.info();
          if (userStore.username) userStore.addToProject();
        }
        completeInfoResultData.value = null;
      } else {
        const res: any = await getSocialInfoByToken(accessTokenVariable.value);
        await setToken(accessTokenVariable.value);
        setUserName(res.userName);
        await userStore.info();
        if (userStore.username) userStore.addToProject();
      }

      // 根据isSameScene决定跳转路径
      const targetPath = isSameScene
        ? dotToSlash(redirectVariable.value) || '/home-page'
        : '/home-page';

      router
        .push({
          path: targetPath,
          query: {
            ...othersQueryVariable.value,
          },
        })
        .then(() => {
          // 路由跳转成功后关闭弹窗
          showModal.value = false;
        })
        .catch((err) => {
          console.error('路由跳转失败', err);
          // 即使路由跳转失败也关闭弹窗
          showModal.value = false;
          Message.error(t('login.navigation.failed'));
        });

      // Message.success(t('login.form.login.success'));
    } catch (err) {
      clearToken();
      clearWpsToken();
      if (typeof err === 'string') {
        Message.error(err);
      }
    }
  };

  // 提供 openShowModal 方法给子组件
  const openShowModal = () => {
    showModal.value = true;
  };
  provide('openShowModal', openShowModal);

  // 扫码登录成功会重定向回登录页并携带access_token参数
  onBeforeMount(async () => {
    console.log('props:', props);
    console.log('inviteCompany:', props.inviteCompany);
    console.log('invitationId:', props.invitationId);

    if (props.isRegister === '1') {
      loginMethod.value = LoginMethods.register;
    }
    if (props.inviteCompany === '1') {
      console.log('切换到邀请页面');
      loginMethod.value = LoginMethods.invite;
    }
    // const accessToken = route.params.access_token;
    const { redirect, accessToken, errorMsg, ...othersQuery } =
      router.currentRoute.value.query;
    if(!!redirect){
      userStore.setProjectTemplate('0');
    }

    accessTokenVariable.value = accessToken as string;
    othersQueryVariable.value = othersQuery;
    redirectVariable.value = redirect as string;
    setWpsTokenHandle();
    if (errorMsg) {
      // 扫码登录异常
      const errStrArray = CryptoJS.enc.Base64.parse(errorMsg as string);
      const errStr = CryptoJS.enc.Utf8.stringify(errStrArray);
      Message.error(errStr);
    }
    if (accessToken) {
      setToken(accessToken as string);
      // showModal.value = true;
      if (userStore.username) userStore.addToProject();
      //todo：增加判断
      console.log(userStore.userFullname, 'userStore.userFullname222222222222222222222222');
      console.log(userStore.phone, 'userStore.phone111111111111111111111111');
      console.log(userStore.email, 'userStore.email33333333333333333333333333333');  
      if (!userStore.userFullname || !userStore.phone || !userStore.email) {
        changeLoginMethod(LoginMethods.completeInfo);
      }
    }
  });
</script>

<style lang="less" scoped>
  .container {
    display: flex;
    height: 100vh;

    .banner {
      width: 65%;
      height: 100%;
      background: url('@/assets/images/login-bg2.png') no-repeat;
      background-size: 100% 100% !important;
      background-size: cover;
    }

    .content {
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      background: #ffffff;
      .login-title {
        display: flex;
        justify-content: center;
        align-items: center;
        align-content: center;
        height: 80px;
        //border: 1px solid red;
        margin-bottom: 32px;
        .login-title-text {
          flex: 1;
          font-family: DingTalk JinBuTi, DingTalk JinBuTi;
          font-weight: 400;
          font-size: 60px;
          color: #222f56;
          line-height: 65px;
          margin-left: 6px;
          white-space: nowrap;
        }
      }
    }

    .footer {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 100%;
    }
    :deep(.arco-input-wrapper) {
      .arco-input-prefix {
        color: rgb(var(--primary-6));
      }
    }
    :deep(.arco-btn-text[type='button']) {
      padding: 0;
    }
  }

  .logo-image {
    display: inline-block;
    width: 80px;
    height: 80px;
  }

  :deep(.login-form-wrapper) {
    padding-left: 0;
    width: 100%;
  }
  .content-inner {
    width: 100%;
    padding-left: 110px;
    padding-right: 110px;
  }
  .login-form {
    &-password-actions {
      display: flex;
      justify-content: space-between;
    }
  }
</style>
