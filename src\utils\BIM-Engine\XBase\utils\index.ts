import viewerTypeMap from '@/utils/BIM-Engine/XBase/dictionary/viewer-type';

// 根据文件拓展名判断预览时的viewerType
export const parseViewerType = (fileName: string) => {
  const parts = fileName.split('.');
  const extension = parts[parts.length - 1]; // 获取扩展名
  let viewerType = 'model';
  const keys = Object.keys(viewerTypeMap);
  keys.forEach((key: string) => {
    // @ts-ignore
    if (viewerTypeMap[key].includes(extension)) viewerType = key;
  });
  return viewerType;
};
export default null;
