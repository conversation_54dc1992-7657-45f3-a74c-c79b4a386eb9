<template>
  <div class="top-section">
    <!-- 左侧模块 -->
    <div class="project-info">
      <div class="project-info-content">
        <div class="pro-name">
          <div
            v-if="currentProject?.id"
            ref="dropdownRef"
            style="cursor: pointer"
          >
            <a-dropdown
              class="home-dropdow-custom"
              position="lt"
              @select="proChange"
              @popup-visible-change="projectListShow"
            >
              <div class="name-wrap">
                <a-tooltip :content="currentProject?.name">
                  <div class="name-text">{{
                    currentProject?.name || $t('home.no-project-data')
                  }}</div>
                </a-tooltip>
                <icon-caret-down />
              </div>

              <template #content>
                <div class="project-list-wrap">
                  <div class="project-search">
                    <a-input-search
                      v-model="searchValue"
                      allow-clear
                      :placeholder="$t('navbar.logo.enter')"
                      @search="searchHandle"
                      @clear="searchHandle"
                      @keydown.enter="searchHandle"
                    />
                  </div>
                  <div class="scroll-wrap" @scroll="projectScroll">
                    <a-spin v-if="loading" dot> </a-spin>
                    <div class="pro-items">
                      <a-doption
                        v-for="item in projectList.filter((e) => {
                          return e?.isTemplate === 0;
                        })"
                        :id="`pro-${item.id}`"
                        :key="item.id"
                        :value="item"
                        >{{ item.name }}</a-doption
                      >
                    </div>
                  </div>
                </div>
              </template>
            </a-dropdown>
          </div>
        </div>
        <ul v-if="currentProject?.id" class="project-details">
          <li>
            <i class="icon project-code-icon"></i>
            <span class="label">{{ $t('home.project-code') }}：</span>
            <span class="value project-id">{{
              currentProject?.code || '-'
            }}</span>
          </li>
          <li>
            <i class="icon project-phase-icon"></i>
            <span class="label">{{ $t('home.project-phase') }}：</span>
            <span class="value project-phase">
              <span class="status-dot"></span>
              {{ ProjectPhaseMaps[currentProject.projectPhase] || '-' }}
            </span>
          </li>
          <li>
            <i class="icon project-type-icon"></i>
            <span class="label">{{ $t('home.project-type') }}：</span>
            <span
              v-if="ProjectTypeMaps[currentProject.type]"
              class="value project-type"
            >
              <span class="type-box">
                <i class="type-icon"></i>
                {{ ProjectTypeMaps[currentProject.type] }}
              </span>
            </span>
            <span v-else class="">-</span>
          </li>
          <li>
            <i class="icon project-time-icon"></i>
            <span class="label">{{ $t('home.project-time') }}：</span>
            <span
              v-if="currentProject.planStart?.split(' ')[0]"
              class="value project-time"
            >
              {{ currentProject.planStart?.split(' ')[0] || '' }} ~
              {{ currentProject.planEnd?.split(' ')[0] || '' }}
            </span>
            <span v-else>-</span>
          </li>
        </ul>
      </div>
      <div class="stats">
        <div class="stat-item">
          <i class="icon sharing-icon"></i>
          <span class="label">{{ $t('home.my-sharing') }}</span>
          <span class="value sharing-value">{{
            myWorkCount.collaborateTaskCount || 0
          }}</span>
        </div>
        <div class="stat-item">
          <i class="icon delivery-icon"></i>
          <span class="label">{{ $t('home.my-delivery') }}</span>
          <span class="value delivery-value">{{
            myWorkCount.deliveryTaskCount || 0
          }}</span>
        </div>
        <div class="view-link" @click="navigateToProject"
          >{{ $t('home.enter-view') }} >></div
        >
      </div>
    </div>
    <!-- 右侧模块 -->
    <div class="knowledge-info">
      <div class="text-content">
        <div class="title">{{ $t('home.knowledge-saved') }}</div>
        <div class="file-count">
          <span class="count">{{ knowledgeBaseCount }}</span>
          {{ $t('home.individual') }}{{ $t('home.file') }}
        </div>
      </div>
      <div class="view-link" @click="navigateToKnowledgeBase"
        >{{ $t('home.enter-view') }} >></div
      >
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, watch, nextTick } from 'vue';
  import {
    getProjectList,
    ProjectListParams,
    ProjectItem,
  } from '@/api/project';
  import { ProjectTypeMaps, ProjectPhaseMaps } from '@/directionary/project';
  import { useUserStore, useGlobalModeStore } from '@/store';
  import { useRoute, useRouter } from 'vue-router';
  import { getMyWorks, getKnowledgeBaseCnt } from './api';
  import { setLocalstorage, getLocalstorage } from '@/utils/localstorage';
  import { queryProjectDetail } from '@/views/project-setting/projectNew/api';
  import { getUserId } from '@/utils/auth';

  const userId = getUserId() || '';
  const router = useRouter();
  // const route: any = useRoute();
  const userStore = useUserStore();
  const globalModeStore = useGlobalModeStore();
  const userInfo: any = computed(() => {
    return userStore.userInfo;
  });
  const createEmptyProject = (): ProjectItem => ({
    name: '',
    type: '',
    id: '',
    description: '',
    code: '',
    protemId: '',
    planStart: '',
    planEnd: '',
    position: '',
    coordinate: 0,
    elevation: 0,
    environmentType: 0,
    magnitude: 0,
    projectStatus: 0,
    participaUnit: '', // 同上建议改名
    projectPhase: 0,
  });
  const currentProjectId = ref(getLocalstorage(`last_project_${userId}`) || '');
  const currentProject = ref(createEmptyProject());
  // 判断从本地获取的currentProjectId有没有，有就查询详情设置当前项目信息然后查项目列表，没有就直接查询项目列表取第一项

  // const currentProjectId = undefined;
  const companyId = computed(() => userStore.companyId);
  // 项目下拉回显值
  const proName = ref('');
  const searchValue = ref('');
  // 项目列表
  const projectList = ref<ProjectItem[]>([]);
  const projectListCopy = ref<ProjectItem[]>([]);

  // 我的共享，我的交付
  const myWorkCount = ref({
    deliveryTaskCount: 0,
    collaborateTaskCount: 0,
  });
  // 知识库数量
  const knowledgeBaseCount = ref(0);
  // 获取我的共享，我的交付
  const getWorksCount = () => {
    const params = {
      projectId: currentProjectId.value || '',
    };
    getMyWorks(params)
      .then((res) => {
        if (res.status) {
          const { deliveryTaskCount, collaborateTaskCount } = res.data;
          myWorkCount.value = { deliveryTaskCount, collaborateTaskCount };
        } else {
          myWorkCount.value = {
            deliveryTaskCount: 0,
            collaborateTaskCount: 0,
          };
        }
      })
      .catch((e) => {
        if (e) {
          myWorkCount.value = {
            deliveryTaskCount: 0,
            collaborateTaskCount: 0,
          };
        }
      });
  };
  const pageSize = 50;
  const allPage = ref(1);
  const loading = ref(false);
  let index = 1;
  // 判断当前获取的项目是否在项目列表中
  function isCurrentProjectInListFn() {
    if (!currentProjectId.value) return false;
    return projectListCopy.value.some(
      (item) => item.id === currentProjectId.value
    );
  }
  // 获取项目列表
  const getList = async (isSearch?: any) => {
    index = 1;
    const params: ProjectListParams = {
      pageNo: 1,
      pageSize,
      projectType: 0,
      name: searchValue.value,
    };
    loading.value = true;
    // 接着查询项目列表
    try {
      const res = await getProjectList(params);
      if (res.status) {
        allPage.value = Math.ceil(res.data.total / pageSize);
        // 深拷贝一份项目数据
        projectListCopy.value =
          JSON.parse(JSON.stringify(res.data?.list)) || [];
        projectList.value = JSON.parse(JSON.stringify(res.data?.list)) || [];
        // 如果当前有选择的项目数据  则过滤掉当前项目数据
        if (!isSearch && currentProjectId.value && isCurrentProjectInListFn()) {
          projectList.value = projectList.value.filter(
            (item: any) => item.id !== currentProjectId.value
          );
        }

        projectList.value = projectList.value || [];
        loading.value = false;
        // 这里不仅要判断不存在的时候，设置默认项目列表第一个，而是切换集团，项目id存在，但是不在该集团下
        if (
          !currentProjectId.value ||
          (currentProjectId.value && !isCurrentProjectInListFn())
        ) {
          currentProjectId.value = projectList.value[0]?.id;
          currentProject.value = projectList.value[0] || createEmptyProject();
          projectList.value = projectList.value.filter(
            (item: any) => item.id !== currentProjectId.value
          );
          setLocalstorage(`last_project_${userId}`, currentProjectId.value);
        }
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
    }
  };
  const dropdownRef = ref<HTMLElement | null>(null);
  const navigateToProject = (event: MouseEvent) => {
    userStore.setProjectTemplate('0');
    const target = event.target as HTMLElement;
    // 判断点击的目标是否在下拉组件内
    if (dropdownRef.value && dropdownRef.value.contains(target)) {
      // 如果点击的是下拉组件内的内容，则不触发父组件的逻辑
      return;
    }
    console.log('跳转到项目详情', localStorage.getItem('companyId'));
    // companyId有值代表选择的是公司账号，没值选择的是个人账号，个人账号没有项目可以跳转
    if (companyId.value && currentProject.value.id) {
      console.log('跳转到项目详情1');
      const path = `project/${currentProject.value.id}/home`;
      router.push({
        path,
      });
      globalModeStore.changeGlobalMode('project');
      // globalModeStore.changeGlobalMode('project');
      setLocalstorage(`last_project_${userId}`, currentProject.value.id);
    }
  };
  // 切换项目
  const proChange = (project: any) => {
    currentProjectId.value = project.id;
    console.log('切换项目', project);
    currentProject.value = project;
    setLocalstorage(`last_project_${userId}`, currentProjectId.value);
  };
  // 当下拉菜单的显示状态发生改变时触发
  const projectListShow = async (visible: boolean) => {
    index = 1;
    if (visible) {
      searchValue.value = '';
      projectList.value = [];
      await getList();
      if (currentProject.value) projectList.value.unshift(currentProject.value);
      nextTick(() => {
        const ele: any = document.getElementById(
          `pro-${currentProjectId.value}`
        );
        // eslint-disable-next-line no-unused-expressions
        ele ? (ele.style.backgroundColor = 'rgba(229, 230, 235, 0.5)') : '';
        ele?.scrollIntoView({ behavior: 'instant', block: 'center' });
      });
    }
  };
  // 获取知识库已保存文件数量
  const getKnowledgeBaseCount = () => {
    getKnowledgeBaseCnt()
      .then((res) => {
        if (res.status) {
          knowledgeBaseCount.value = res.data.fileCnt;
        } else {
          knowledgeBaseCount.value = 0;
        }
      })
      .catch((e) => {
        if (e) {
          knowledgeBaseCount.value = 0;
        }
      });
  };
  // 跳转到知识库
  const navigateToKnowledgeBase = () => {
    userStore.setProjectTemplate('0');
    router.push({
      name: 'knowledgeBase',
    });
    globalModeStore.changeGlobalMode('work');
  };
  // 上拉加载数据

  const addProjectList = async (pageNo: number) => {
    const params = {
      pageNo,
      pageSize: 50,
      projectType: 0,
      name: searchValue.value,
    };
    const res: any = await getProjectList(params);
    if (res.status && res?.data?.list) {
      projectList.value.push(...res.data.list);
    }
  };
  const projectScroll = (event: any) => {
    const { scrollTop, clientHeight, scrollHeight } = event.target;
    const threshold = 60;
    // 当前翻页小于总页数 并且上次数据已经请求渲染完了后 翻动到底部 加载下页数据
    if (index < allPage.value && !loading.value) {
      if (scrollTop + clientHeight >= scrollHeight - threshold) {
        index++;
        addProjectList(index);
      }
    }
  };

  const searchHandle = async () => {
    await getList(true);
  };

  const init = async () => {
    if (
      currentProjectId.value &&
      projectList.value.length > 0 &&
      isCurrentProjectInListFn()
    ) {
      console.log('从本地获取的currentProjectId', currentProjectId.value);
      const paramsNow = {
        id: currentProjectId.value,
      };
      currentProject.value = (await queryProjectDetail(paramsNow)).data;
      getWorksCount();
    }
  };
  // 监听 currentProject 的变化（可选）
  watch(
    () => currentProjectId.value,
    async () => {
      if (
        companyId.value &&
        currentProjectId.value &&
        projectList.value.length > 0 &&
        isCurrentProjectInListFn()
      ) {
        console.log('currentProjectId 变化了11111:', currentProjectId.value);
        init();
        if (currentProject.value)
          projectList.value.unshift(currentProject.value);
      }
    },
    {
      immediate: true,
    }
  );
  // 监听 companyId 的变化
  watch(companyId, async () => {
    console.log('companyId 变化了:', companyId.value);
    if (companyId.value) {
      await getList();
      init();
    } else {
      projectList.value = [];
      currentProject.value = createEmptyProject();
    }
  });
  onMounted(async () => {
    if (companyId.value) {
      await getList();
      init();
    }
    getKnowledgeBaseCount();
  });
</script>

<style scoped lang="less">
  .top-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;

    .project-info {
      background-image: url('@/assets/images/home-page/Group <EMAIL>');
      width: 55.8%;
      height: 254px;
      border-radius: 8px 8px 8px 8px;
      margin-right: 20px;
      background-size: 100% 100%; /* 让背景图片覆盖整个容器 */
      background-position: center; /* 图片居中显示 */
      background-repeat: no-repeat; /* 防止图片重复 */
      padding: 32px 0 26px 20px;
      display: flex;
      flex-direction: column; /* 垂直布局 */
      justify-content: space-between;
      transition: color 0.2s;
      .project-info-content {
        margin-left: 12px;
      }
      .pro-name {
        font-size: 24px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 500;
        color: #1d2129;
        //line-height: 22px;
        display: flex;
        align-items: center;
        align-content: center;
        //border: 1px solid red;
        .name-text {
          display: inline-block;
          max-width: 340px;
          //border: 1px solid red;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .knowledge-info {
      display: flex;
      flex-direction: column; /* 垂直布局 */
      justify-content: space-between; /* 上下内容分布 */
      align-items: flex-start; /* 左对齐 */
      background-image: url('@/assets/images/home-page/Group <EMAIL>');
      width: 44.8%;
      height: 254px;
      border-radius: 8px 8px 8px 8px;
      background-size: 100% 100%; /* 让背景图片覆盖整个容器 */
      background-position: center; /* 图片居中显示 */
      background-repeat: no-repeat; /* 防止图片重复 */
      padding: 28px 0 26px 32px;

      .text-content {
        display: flex;
        flex-direction: column; /* 垂直布局 */
        gap: 25px; /* 标题和文件数量之间的间距 */
      }
      .title {
        font-size: 20px;
        color: #1d2129;
        font-weight: 500;
      }
      .file-count {
        font-size: 14px; /* 文件数量字体大小 */
        color: #4e5969; /* 文件数量字体颜色 */
        font-weight: 500;
        .count {
          font-size: 40px; /* 数字字体大小 */
          color: #1d2129; /* 数字颜色 */
          margin-right: 4px; /* 数字与“个文件”的间距 */
        }
      }
    }
  }
  .view-link {
    font-size: 14px; /* 链接字体大小 */
    color: #3366ff; /* 链接字体颜色 */
    font-weight: 400; /* 链接字体加粗 */
    cursor: pointer; /* 鼠标变为手型 */
    text-decoration: none; /* 去掉下划线 */

    &:hover {
      text-decoration: underline; /* 鼠标悬停时显示下划线 */
    }
  }
  .project-list-wrap {
    //border: 1px solid red;
    width: 300px;
    height: 200px;
    overflow: hidden;

    .project-search {
      //border: 1px solid black;
      padding: 12px;
      padding-bottom: 4px;
    }
    .scroll-wrap {
      height: 150px;
      //border: 1px solid blue;
      overflow: hidden;
      .pro-items {
        height: 100%;
        overflow: auto;
      }
    }
  }
  .project-details {
    list-style: none; /* 去掉 ul 默认样式 */
    padding: 0;
    margin: 0;
    margin-top: 12px;
    margin-bottom: 16px;
    li {
      display: flex;
      align-items: center;
      line-height: 22px; /* 设置行高 */
      margin: 6px 0; /* 设置上下间距 */
      font-size: 14px; /* 设置字体大小 */
      color: #4e5969; /* 设置字体颜色 */

      .icon {
        display: inline-block;
        width: 14px; /* 图标宽度 */
        height: 14px; /* 图标高度 */
        margin-right: 4px; /* 图标和文字的间距 */
        background-size: contain;
        background-repeat: no-repeat;
      }
      .value {
        color: #1d2129;
        font-size: 12px;
        font-weight: 500;
      }
      .project-phase {
        display: flex;
        align-items: center;
        color: #00b42a;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 4px;
          background-color: #00b42a; /* 绿色圆点 */
        }
      }
      .project-type {
        display: flex;
        align-items: center;

        .type-box {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 0 8px;
          height: 20px; /* 固定高度 */
          border-radius: 2px; /* 圆角 */
          border: 1px solid #0fc6c2; /* 边框颜色 */
          background-color: #e6fffb; /* 背景颜色 */
          color: #0fc6c2; /* 文字颜色 */
          font-size: 12px; /* 字体大小 */
          font-weight: 500; /* 字体加粗 */

          .type-icon {
            width: 11px;
            height: 10px;
            margin-right: 4px; /* 图标与文字的间距 */
            background-image: url('@/assets/images/home-page/<EMAIL>'); /* 替换为实际图标路径 */
            background-size: contain;
            background-repeat: no-repeat;
          }
        }
      }
      .project-time {
        font-size: 16px;
      }
      .project-id {
        font-size: 16px;
      }
      /* 各个图标的背景图片 */
      .project-code-icon {
        background-image: url('@/assets/images/home-page/<EMAIL>'); /* 替换为实际图标路径 */
      }
      .project-phase-icon {
        background-image: url('@/assets/images/home-page/<EMAIL>'); /* 替换为实际图标路径 */
      }
      .project-type-icon {
        background-image: url('@/assets/images/home-page/<EMAIL>'); /* 替换为实际图标路径 */
      }
      .project-time-icon {
        background-image: url('@/assets/images/home-page/<EMAIL>'); /* 替换为实际图标路径 */
      }
    }
  }
  .stats {
    display: flex;
    gap: 12px; /* 两个模块之间的间距 */
    align-items: center;
    padding: 0 !important;

    .stat-item {
      display: inline-flex; /* 使用 inline-flex，让宽度根据内容自适应 */
      align-items: center;
      justify-content: space-between;
      background: linear-gradient(
          30deg,
          #eaf2fe 0%,
          rgba(223, 238, 255, 0.68) 100%
        ),
        #ffffff; /* 背景渐变 */
      height: 40px; /* 固定高度 */
      border-radius: 40px; /* 圆角 */
      padding: 8px 12px; /* 内边距 */
      border: 1px solid #ffffff;

      &:hover {
        border: 1px solid #165dff;
        box-shadow: 0px 4px 4px 0px rgba(217, 217, 217, 0.5);
      }
      .icon {
        display: inline-block;
        width: 24px; /* 图标宽度 */
        height: 24px; /* 图标高度 */
        margin-right: 8px; /* 图标和文字的间距 */
        background-size: contain;
        background-repeat: no-repeat;
      }

      .sharing-icon {
        background-image: url('@/assets/images/home-page/<EMAIL>'); /* 替换为实际图标路径 */
      }

      .delivery-icon {
        background-image: url('@/assets/images/home-page/<EMAIL>'); /* 替换为实际图标路径 */
      }

      .label {
        font-size: 14px; /* 字体大小 */
        color: #4e5969; /* 字体颜色 */
        margin-right: 8px; /* 标签和数字的间距 */
        white-space: nowrap; /* 防止文字换行 */
        font-weight: 400;
      }

      .value {
        font-size: 16px; /* 数字字体大小 */
        font-weight: bold;
        white-space: nowrap; /* 防止数字换行 */

        /* 数字颜色 */
        &.sharing-value {
          color: #53c8ee; /* 我的共享数字颜色 */
        }

        &.delivery-value {
          color: #78d16b; /* 我的交付数字颜色 */
        }
      }
    }
  }
</style>

<style>
  .home-dropdow-custom {
    left: 275px !important;
    top: 210px !important;
  }
  .name-wrap {
    display: flex;
    align-items: center;
  }
</style>
