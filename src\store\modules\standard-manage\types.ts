export interface standardManage {
  treeDataList: any;
  mapData: any;
  tableData: any;
  selTreeData: any;
  professionalList: any;
  precisionData: any;
  unit: any;
  dataType: any;
  selStandardVisible: any;
  fileId: string;
  standardId: string;
  bindStandardData: any;
  fileData: any;
  isbatch: any;
  batchData: any;
  bindStandEditData: any;
  tableLoading: boolean;
  infoCategory: any;
  standardListData: any;
  nowStandard: any;
  nowStandardId: string;
  standardClassId: any;
  treeSelectList: any;
  treeSelectedKey: string;
  resultDetail: any;
  precisionResData: any;
  standPrecision: any;
  treeLoading: boolean;
}
