<template>
  <div class="applyAppplicationModal">
    <!-- 申请弹窗组件 -->
    <a-modal
      v-model:visible="visible"
      :title="$t('apply-application.cccc-bim-application')"
      :width="600"
      :modal-style="{ padding: 0 }"
      @cancel="handleCancel"
      @ok="handleOk"
      @before-ok="handleBeforeOk"
    >
      <!-- 加载状态 -->
      <a-spin
        :size="30"
        :tip="$t('apply-application.loading')"
        :loading="spanLoading"
      >
        <div class="content">
          <div class="content-inner">
            <div class="form-wrapper">
              <div style="margin-left: 10px">
                <!-- 产品使用说明 -->
                <div class="font-style">{{
                  $t('apply-application.productUse')
                }}</div>
                <!-- 错误提示 -->
                <span
                  v-show="status.product === 'error'"
                  style="color: red; font-size: 12px; font-weight: normal"
                  >{{ $t('apply-application.selectProduct') }}</span
                >
                <!-- 产品选择树 -->
                <a-tree
                  v-model:checked-keys="product"
                  :checkable="true"
                  :data="filteredProducts"
                  :checked-strategy="'child'"
                  :default-checked-keys="defaultChecked ? allProductKeys : []"
                  @check="onCheck"
                />
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { useI18n } from 'vue-i18n';
  import secondaryUnitList from '../secondaryUnitList.json';

  const { t } = useI18n();

  // 定义props
  const props = defineProps({
    selectProducts: {
      type: String,
      default: '',
    },
    defaultChecked: {
      type: Boolean,
      default: false
    }
  });

  // 弹窗显示状态
  const visible = ref(false);
  // 加载状态
  const spanLoading = ref(false);
  // 选中的产品key数组
  const product = ref<string[]>([]);
  // 产品树数据
  const products = ref(secondaryUnitList.children);
  // 表单验证状态
  const status = ref({
    product: '', // 产品选择状态
  });

  // 将传入的selectProducts字符串转换为数组
  const selectProductList = computed(() => {
    if (!props.selectProducts) return [];
    return props.selectProducts.split(',').map(item => item.trim());
  });

  // 过滤产品树，只显示selectProductList中包含的产品
  const filteredProducts = computed(() => {
    if (selectProductList.value.length === 0) {
      return products.value;
    }

    // 深拷贝产品树数据
    const clonedProducts = JSON.parse(JSON.stringify(products.value));
    
    // 过滤每个树干下的节点
    return clonedProducts.filter(category => {
      // 过滤每个树干下的子节点
      if (category.children && category.children.length > 0) {
        category.children = category.children.filter(item => 
          selectProductList.value.includes(item.key)
        );
        // 如果树干下没有子节点，则不显示该树干
        return category.children.length > 0;
      }
      return false;
    });
  });

  // 获取所有可选的产品key
  const allProductKeys = computed(() => {
    const keys: string[] = [];
    filteredProducts.value.forEach(category => {
      if (category.children && category.children.length > 0) {
        category.children.forEach(item => {
          keys.push(item.key);
        });
      }
    });
    return keys;
  });

  // 监听visible变化，当弹窗打开时，如果是审批模式，自动选中所有产品
  watch(
    visible,
    (newValue) => {
      if (newValue && props.defaultChecked) {
        // 如果弹窗打开且需要默认全选，则选中所有产品
        product.value = [...allProductKeys.value];
      } else if (newValue && !props.defaultChecked) {
        // 如果弹窗打开且不需要默认全选，则清空选中
        product.value = [];
      }
    }
  );

  // 定义事件
  const emit = defineEmits(['ok', 'cancel']);

  /**
   * 处理产品选择变化
   * @param checkedKeys 选中的产品key数组
   */
  const onCheck = (checkedKeys: string[]) => {
    product.value = checkedKeys;
    if (checkedKeys.length > 0) {
      status.value.product = '';
    }
  };

  /**
   * 处理确认按钮点击
   * 提交产品申请
   */
  const handleBeforeOk = () => {
    if (product.value.length === 0) {
      status.value.product = 'error';
      return false;
    }
    return true;
  };

  const handleOk = () => {
    // 将选中的产品数据传递给父组件
    emit('ok', product.value.join(','));
    visible.value = false;
    return true;
  };

  /**
   * 处理取消按钮点击
   */
  const handleCancel = () => {
    emit('cancel');
    product.value = [];
    visible.value = false;
  };

  // 暴露给父组件的属性和方法
  defineExpose({
    visible, // 弹窗显示状态
    spanLoading, // 加载状态
    product, // 选中的产品
    products, // 产品树数据
    status, // 表单验证状态
  });
</script>

<style scoped lang="less">
  // .applyAppplicationModal {
  //   max-height: 60vh;
  //   overflow-y: auto;
  // }
  .content-inner {
    max-width: 100%;
  }
  /* 标题样式 */
  .font-style {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
  }
</style>