import axios from 'axios';
import { fileZipDownload, fileDownload } from '@/api/file';
import { download } from '@/utils/file';
import { getSign } from '@/utils/request-sign';

export interface downloadInfo {
  fileName: string;
  fileSize: string;
  singleFileToken?: string;
}

export function getShareInfo(params: any) {
  return axios.get('/cde-collaboration/sharelink/getdownloadinfo', {
    params,
  });
}

// 下载压缩包
export async function shareFileZipDownload(
  fileName: string,
  forestNodeList: any
) {
  const res: any = await fileZipDownload(forestNodeList);
  download({ name: `${fileName}.zip` || '' }, res.data);
}

// 下载单文件
export async function shareFileDownload(fileName: string, fileToken: string) {
  const res: any = await fileDownload({ fileToken, name: fileName });
  download({ name: fileName || '' }, res.data);
}

// 文件详情
export function getFileInfoById(id?: string) {
  return axios.get('/cde-collaboration/file/detailAuth', {
    params: {
      id,
    },
  });
}

// 获取分享权限的 token
export const getTokenWithTest = (data: any) => {
  return axios.post('/cde-collaboration/treed/getToken', data);
};

// 本地服务获取token
export function getXbaseTokenTest(data: any) {
  return axios.post('/cde-collaboration/xbase/getXbaseToken', data);
}

// 获取客户端token
export function getTokenWithTreed(data?: any) {
  return axios.get('/cde-collaboration/treed/cdeToken', {
    params: getSign(data),
  });
}
