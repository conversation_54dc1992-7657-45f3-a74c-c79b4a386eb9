<template>
  <a-drawer
    width="320px"
    :visible="true"
    :drawer-style="{ marginRight: '320px' }"
    @cancel="compareCancel"
  >
    <template #title>{{ $t('file-manage.version-compare') }}</template>
    <template #footer>
      <a-button type="primary" @click="createCompare">{{
        $t('file-manage.create')
      }}</a-button>
    </template>
    <a-spin style="height: calc(100% - 8px); width: 100%" :loading="loading">
      <div
        v-for="(item, index) in compareDataList"
        :key="item.id"
        class="card"
        @click="compareViewer(item)"
      >
        <div class="header">
          <div class="shape">
            <span class="index">{{ index + 1 }}</span>
          </div>
          <div class="state">
            <!-- 0: 对比成功，1：检查中，-2：对比失败 -->
            <span v-if="item.state === -2" style="color: #4e5969">{{
              $t('file-manage.failed')
            }}</span>
            <span v-else-if="item.state === 0" style="color: #43c243">{{
              $t('file-manage.success')
            }}</span>
            <span v-else-if="item.state === 1" style="color: #0090ff">{{
              $t('file-manage.converting')
            }}</span>
          </div>
        </div>

        <div class="body">
          <a-space direction="vertical" fill size="mini">
            <div
              >{{ $t('file-manage.base-version') }}：<span class="title"
                >V {{ item.baseVersion }}</span
              ></div
            >
            <div
              >{{ $t('file-manage.compare-version') }}：<span class="title"
                >V {{ item.compareVersion }}</span
              ></div
            >
            <div
              >{{ $t('file-manage.create-time') }}：<span>{{
                item.updateDate
              }}</span></div
            >
            <div class="delBtn" @click.stop>
              <a-popconfirm
                :content="$t('file-manage.confirm-delete')"
                @ok="deleteOk(item)"
              >
                <icon-delete :size="16" class="svg-button" />
              </a-popconfirm>
            </div>
          </a-space>
        </div>
      </div>
    </a-spin>
  </a-drawer>
  <a-modal
    v-model:visible="createVisible"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    :on-before-ok="handleBeforeOk"
    @cancel="createCancel"
  >
    <template #title>{{ $t('file-manage.create') }}</template>
    <a-form ref="createFormRef" :model="createForm">
      <a-form-item
        field="base"
        :label="$t('file-manage.base-version')"
        :rules="[{ required: true, message: $t('file-manage.select') }]"
        :validate-trigger="['change', 'input']"
      >
        <a-select
          v-model="createForm.base"
          :placeholder="$t('file-manage.select')"
          @change="(value: any) => selectChange(value, 0)"
        >
          <a-option
            v-for="version in fileVersionData"
            :key="version.id"
            :value="version.id"
            :label="`${$t('file-manage.version')}：V${version.version}`"
            :disabled="version.id === createForm.compare"
          ></a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="compare"
        :label="$t('file-manage.compare-version')"
        :rules="[{ required: true, message: $t('file-manage.select') }]"
        :validate-trigger="['change', 'input']"
      >
        <a-select
          v-model="createForm.compare"
          :placeholder="$t('file-manage.select')"
          @change="(value: any) => selectChange(value, 1)"
        >
          <a-option
            v-for="version in fileVersionData"
            :key="version.id"
            :value="version.id"
            :label="`${$t('file-manage.version')}：V${version.version}`"
            :disabled="version.id === createForm.base"
          ></a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { useI18n } from 'vue-i18n';
  import { ref, reactive, toRefs } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import useLoading from '@/hooks/loading';
  import { usePrjPermissionStore } from '@/store';
  import {
    XBaseModelCompare,
    addCompareData,
    getCompareList,
    deleteVersion,
  } from '../../api';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    fileVersionData: {
      type: Array as any,
      default() {
        return [];
      },
    },
  });

  const { fileVersionData } = toRefs(props);

  const emit = defineEmits(['update:visible']);

  const { t } = useI18n();
  declare const OBV: any;
  const { loading, setLoading } = useLoading(false);
  const router = useRouter();
  const projectStore = usePrjPermissionStore();

  const createVisible = ref<boolean>(false);
  const createFormRef = ref();
  const route = useRoute();
  const projectId = ref(route.params.projectId);
  const compareDataList = ref<any[]>();
  const baseInfo = ref();
  const compareInfo = ref();

  const createForm = reactive({
    base: '',
    compare: '',
  });
  const modelInfo = reactive({
    baseUrn: '',
    compareUrn: '',
    lodType: '',
    viewPairs: [] as string[],
    compareResult: '',
  });

  const compareCancel = () => {
    emit('update:visible', false); // 关闭抽屉
    createForm.base = '';
    createForm.compare = '';
  };
  const createCancel = () => {
    createVisible.value = false;
    createForm.base = '';
    createForm.compare = '';
  };
  const createCompare = () => {
    createVisible.value = true;
  };
  const getCompareListData = async () => {
    const params = {
      projectId: projectId.value,
      fileId: fileVersionData.value[0].fileId,
    };
    const res = await getCompareList(params);
    if (res.status) {
      compareDataList.value = res.data;
    }
  };
  const compareViewer = async (value: any) => {
    if (value.state === 0) {
      const url = router.resolve({
        path: '/bim-view/compare',
        query: { id: value.id, projectId: projectId.value },
      });
      window.open(url.href);
    } else if (value.state === -2) {
      Message.error(t('file-manage.failed'));
    } else if (value.state === 1) {
      Message.info(t('file-manage.converting'));
    }
  };

  const selectChange = (value: any, type: number) => {
    if (type)
      compareInfo.value = fileVersionData.value.find(
        (item: any) => item.id === value
      );
    else
      baseInfo.value = fileVersionData.value.find(
        (item: any) => item.id === value
      );
  };

  // 显示状态错误消息
  const showStatusMessage = (
    baseStatus: number | null,
    compareStatus: number | null
  ) => {
    if (baseStatus !== 0 && compareStatus !== 0) {
      Message.info('基准、对比版本未转换成功，无法对比！');
    } else if (baseStatus !== 0) {
      Message.info('基准版本未转换成功，无法对比！');
    } else if (compareStatus !== 0) {
      Message.info('对比版本未转换成功，无法对比！');
    }
  };

  /** 创建版本对比 */
  const createVersionComparison = async () => {
    const baseModelStatus = baseInfo.value?.status;
    const newModelStatus = compareInfo.value?.status;
    let res: any;
    if (baseModelStatus === 0 && newModelStatus === 0) {
      const semanticModelIds: string[] = [
        baseInfo.value?.graphicEngineInfo.split('|')[2],
        compareInfo.value?.graphicEngineInfo.split('|')[2],
      ];
      const currentTimestamp = Date.now();
      const params = {
        baseModelId: semanticModelIds[0],
        baseModelName: `${baseInfo.value?.name}_base`,
        // base_model_version: baseInfo.value?.version.toString(), // 只有数据资产有版本概念。查语义模型详情是否有这个值，没有的话不写。
        groupId: projectId.value,
        name: `${currentTimestamp}_${baseInfo.value.version}_${compareInfo.value.version}`,
        newModelId: semanticModelIds[1],
        newModelName: `${compareInfo.value?.name}_new`,
        // new_model_version: compareInfo.value?.version.toString(),
      };
      res = await XBaseModelCompare(params);
      if (res.status) {
        Message.success(res.message);
      } else {
        Message.error(res.message);
      }
    } else {
      showStatusMessage(baseModelStatus, newModelStatus);
    }
    return res;
  };

  /** 新增版本对比 */
  const newVersionComparison = async (modelDiffId: string) => {
    const semanticModelIds: string[] = [
      baseInfo.value?.graphicEngineInfo.split('|')[2],
      compareInfo.value?.graphicEngineInfo.split('|')[2],
    ];
    const XBaseParams = {
      baseHistoryId: baseInfo.value.id,
      baseUrn: semanticModelIds[0],
      baseVersion: baseInfo.value.version,
      compareHistoryId: compareInfo.value.id,
      compareUrn: semanticModelIds[1],
      compareVersion: compareInfo.value.version,
      fileId: fileVersionData.value[0].fileId,
      projectId: projectId.value,
      compareResult: modelDiffId,
      viewPair: JSON.stringify([
        baseInfo.value.graphicEngineInfo,
        compareInfo.value.graphicEngineInfo,
      ]),
    };
    const compareResult = await addCompareData(XBaseParams);
    if (compareResult.status) {
      getCompareListData();
    }
  };

  const sendCompare = async () => {
    const createStatus = await createVersionComparison();
    if (createStatus.status) {
      newVersionComparison(createStatus.data?.model_diff_id);
    }
  };
  const handleBeforeOk = async () => {
    await sendCompare();
    createForm.base = '';
    createForm.compare = '';
    emit('update:visible', false); // 关闭抽屉
  };
  getCompareListData();

  // 删除版本对比
  const deleteOk = async (data: any) => {
    await deleteVersion(data.id);
    getCompareListData();
  };
</script>

<style lang="less" scoped>
  .card {
    border-bottom: 1px solid var(--color-border);
    padding-bottom: 16px;
    position: relative;
    .header {
      display: flex;
      align-content: center;
      align-items: center;
      .shape {
        width: 36px;
        border-top: 0px;
        border-bottom: 18px solid #3366ff;
        border-right: 5px solid transparent;
        .index {
          display: inline-block;
          position: absolute;
          left: 10px;
          color: white;
        }
      }
      .state {
        position: absolute;
        right: 0px;
      }
    }

    .body {
      width: 288px;
      // height: 82px;
      height: auto;
      padding: 5px;
      background-color: rgba(51, 102, 255, 0.05);
      cursor: pointer;
      .delBtn {
        text-align: right;
      }
    }
  }

  .text {
    margin-left: 12px;
    flex: 1;
  }
  .title {
    font-size: 14px;
    color: #3366ff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 236px;
  }
</style>
