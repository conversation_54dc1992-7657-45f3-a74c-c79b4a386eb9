<template>
  <div class="matter-content">
    <div class="matter-content-top" :style="heightStyle">
      <!-- 基本信息 -->
      <div class="describe">
        <customIconFile class="describe-icon" />
        <span class="describe-span">{{ $t('schedule.basicInfo') }}</span>
      </div>

      <a-form
        ref="formRef"
        layout="vertical"
        :model="form"
        :disabled="hasNoEditPermission"
      >
        <a-row :gutter="20">
          <!-- <a-col v-if="globalMode === 'project'" :span="8">
            <a-form-item
              field="teamId"
              label="归属团队"
              :rules="[
                {
                  required: false,
                  message: '请选择归属团队',
                },
              ]"
              :validate-trigger="['change', 'blur']"
            >
              <a-select
                v-model="form.teamId"
                placeholder="请选择归属团队"
                allow-clear
                allow-search
                :disabled="type === 'view' ? true : false"
                style="width: 100%"
              >
                <a-option
                  v-for="item in teamList"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                />
              </a-select>
            </a-form-item>
          </a-col> -->
          <a-col :span="type !== 'view' ? 8 : 12">
            <a-form-item
              field="chargePersonId"
              :label="$t('schedule.assignedTo')"
              :rules="[
                {
                  required: type !== 'view' ? true : false,
                  message: t('schedule.selectAssignee'),
                },
              ]"
              :validate-trigger="['change', 'input']"
            >
              <!-- 个人空间下选人 -->
              <!-- 个人空间下可以看到所有的事项 -->
              <a-tooltip
                :disabled="!chargeUserFullname"
                :content="chargeUserFullname || undefined"
              >
                <a-select
                  v-if="globalMode === 'work' && !form.projectId"
                  v-model="form.chargePersonId"
                  :popup-visible="false"
                  :placeholder="t('schedule.selectPerson')"
                  allow-search
                  multiple
                  style="width: 100%"
                  :disabled="type === 'view' ? true : false"
                  :max-tag-count="1"
                  @focus="getUserFocus"
                >
                  <template v-if="type !== 'view'" #prefix>
                    <icon-user-add />
                  </template>
                  <a-option
                    v-for="item in memberList"
                    :key="item.userName"
                    :value="item.userName"
                    :label="item.userFullName"
                  />
                </a-select>
                <!-- 项目空间下选人 -->
                <a-select
                  v-else
                  v-model="form.chargePersonId"
                  :placeholder="t('schedule.selectPerson')"
                  allow-search
                  multiple
                  style="width: 100%"
                  :disabled="type === 'view' ? true : false"
                  :max-tag-count="1"
                  @change="handlechargePersonChange"
                >
                  <template v-if="type !== 'view'" #prefix>
                    <icon-user-add />
                  </template>
                  <a-option
                    v-for="item in projectMemberList"
                    :key="item.userName"
                    :value="item.userName"
                    :label="item.userFullName"
                  />
                </a-select>
              </a-tooltip>
            </a-form-item>
          </a-col>

          <a-col :span="type !== 'view' ? 8 : 12">
            <a-form-item
              field="planStartTime"
              :label="$t('schedule.matter.starttime')"
              :rules="[
                {
                  required: type !== 'view' ? true : false,
                  message: $t('schedule.selectStartTime'),
                },
              ]"
              :validate-trigger="['change', 'blur']"
            >
              <a-date-picker
                v-model="form.planStartTime"
                show-time
                format="YYYY-MM-DD HH:mm"
                :time-picker-props="{
                  step: {
                    hour: 1,
                    minute: 5,
                  },
                }"
                :readonly="type === 'view' ? true : false"
                :placeholder="t('schedule.selectStartTime')"
                :class="{ 'hide-suffix-icon': type === 'view' }"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

          <a-col :span="type !== 'view' ? 8 : 12">
            <a-form-item
              field="planEndTime"
              :label="$t('schedule.matter.deadline')"
              :rules="[
                {
                  required: type !== 'view' ? true : false,
                  message: $t('schedule.selectDeadline'),
                },
              ]"
              :validate-trigger="['change', 'blur']"
            >
              <a-date-picker
                v-model="form.planEndTime"
                show-time
                format="YYYY-MM-DD HH:mm"
                :time-picker-props="{
                  step: {
                    hour: 1,
                    minute: 5,
                  },
                }"
                :readonly="type === 'view' ? true : false"
                :placeholder="t('schedule.selectDeadline')"
                :disabled-date="disabledDate"
                :disabled-time="disabledDateTime"
                :class="{ 'hide-suffix-icon': type === 'view' }"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <!-- 所属里程碑，只有项目日历有 -->
          <a-col
            v-if="form.projectId && form.parentId === 0"
            :span="type !== 'view' ? 8 : 12"
          >
            <a-form-item
              field="milestoneId"
              :label="$t('schedule.milestoneId')"
              :rules="[
                {
                  required: false,
                  message: t('schedule.milestoneId-notips'),
                },
              ]"
              :validate-trigger="['change', 'blur']"
            >
              <a-select
                v-if="form.projectId && form.parentId === 0"
                v-model="form.milestoneId"
                :placeholder="t('schedule.selected-milestoneId')"
                allow-search
                style="width: 100%"
                :disabled="type === 'view' ? true : false"
                :max-tag-count="1"
                @change="handleChangeMilestone"
              >
                <a-option
                  v-for="item in allMilestoneList"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                /> </a-select
            ></a-form-item>
          </a-col>
          <!-- 进度 -->
          <a-col v-if="form.projectId" :span="type !== 'view' ? 12 : 24">
            <a-form-item
              field="rateProgress"
              class="rate-progress"
              :label="$t('schedule.rateProgress')"
              :rules="[
                {
                  required: false,
                  type: 'number',
                  min: 0,
                  max: 100,
                  message: $t('schedule.rateProgressRange'),
                },
              ]"
              :validate-trigger="['change', 'blur']"
            >
              <div style="display: flex; align-items: center; width: 100%">
                <a-slider
                  v-model="form.rateProgress"
                  :min="0"
                  :max="100"
                  :step="1"
                  :style="{ width: '100%' }"
                  :disabled="type === 'view' ? true : false"
                  :format-tooltip="(val:any) => `${val}%`"
                />
                <span style="margin-left: 12px; width: 40px; text-align: right"
                  >{{ form.rateProgress }}%</span
                >
              </div>
            </a-form-item>
          </a-col>
          <a-col
            v-if="
              !form.projectId &&
              (type === 'edit' ? scheduleVisible : type === 'new')
            "
            :span="type !== 'view' ? 8 : 12"
          >
            <a-form-item
              field="schedulePanelId"
              :label="$t('schedule.calendarBelong')"
              :validate-trigger="['change', 'blur']"
              :rules="[
                {
                  required: true,
                  message: $t('schedule.selectCalendar'),
                },
              ]"
            >
              <a-select
                v-model="form.schedulePanelId"
                :disabled="type === 'view' ? true : false"
                :placeholder="$t('schedule.selectCalendar')"
              >
                <a-option
                  v-for="item in calendarOption"
                  :key="item.id"
                  :value="item.id"
                  :label="item.panelName"
                />
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 描述 -->
      <div class="describe">
        <customIconDescribe class="describe-icon" />
        <span class="describe-span"> {{ $t('schedule.description') }}</span>
      </div>

      <a-textarea
        v-model="form.content"
        :placeholder="
          (!form.content && type === 'view') || hasNoEditPermission
            ? ''
            : $t('schedule.placeholder')
        "
        style="margin-bottom: 20px"
        :disabled="type === 'view' || hasNoEditPermission ? true : false"
      />

      <!-- 子事项清单 -->
      <a-spin :loading="spanLoading" dot>
        <div class="describe">
          <customIconSubevent class="describe-icon" />
          <span class="describe-span"> {{ $t('schedule.subTaskList') }}</span>
          <a-tooltip
            v-if="type !== 'view' && !hasNoEditPermission"
            :content="$t('schedule.aiSubTaskList')"
          >
            <img
              :src="customIconGroup"
              alt="暂无图片"
              class="ai-recognition"
              @click="aiRecognition('content')"
            />
          </a-tooltip>
          <a-button
            v-if="type !== 'view' && !hasNoEditPermission"
            type="text"
            style="margin-left: 12px; height: 24px"
            @click="addNode"
          >
            <template #default> {{ $t('schedule.add') }}</template>
            <template #icon>
              <icon-plus />
            </template>
          </a-button>
        </div>
      </a-spin>

      <div v-if="nodeData.length > 0" style="margin: 0 0 20px 0">
        <a-input-group
          v-for="(node, index) in nodeData"
          :key="index"
          class="node-input-group"
        >
          <span style="margin-right: 10px">{{ index + 1 }}. </span>
          <a-input
            v-model="node.title"
            style="width: calc(45% - 44px)"
            :disabled="type === 'view' || hasNoEditPermission ? true : false"
            :placeholder="$t('schedule.inputTitle')"
          />
          <a-date-picker
            v-model="node.planEndTime"
            show-time
            format="YYYY-MM-DD HH:mm"
            :time-picker-props="{
              step: {
                hour: 1,
                minute: 5,
              },
            }"
            :disabled="type === 'view' || hasNoEditPermission ? true : false"
            :placeholder="$t('schedule.selectDeadline')"
            :disabled-date="disabledDate"
            :disabled-time="disabledDateTime"
            :class="{ 'hide-suffix-icon': type === 'view' }"
            style="width: 25%; margin-left: 20px"
          />
          <a-select
            v-if="globalMode === 'work' && !form.projectId"
            v-model="node.chargePersonId"
            :popup-visible="false"
            :placeholder="$t('schedule.selectPerson')"
            allow-search
            style="width: 25%; margin-left: 20px"
            multiple
            :max-tag-count="1"
            :disabled="type === 'view' || hasNoEditPermission ? true : false"
            @focus="getSubUserFocus(true, index)"
          >
            <a-option
              v-for="item in subMemberList[index]"
              :key="item.userName"
              :value="item.userName"
              :label="item.userFullName"
            />
          </a-select>
          <!-- 存在归属项目时的选人组件 -->
          <a-select
            v-else
            v-model="node.chargePersonId"
            :placeholder="$t('schedule.selectPerson')"
            allow-search
            :style="{
              width: type === 'view' ? '51%' : '26%',
              marginLeft: '20px',
            }"
            multiple
            :max-tag-count="1"
            :disabled="type === 'view' || hasNoEditPermission ? true : false"
          >
            <a-option
              v-for="item in projectMemberList"
              :key="item.userName"
              :value="item.userName"
              :label="item.userFullName"
            />
          </a-select>

          <a-space>
            <a-popconfirm
              :popup-visible="activePopIndex === index"
              :content="$t('schedule.delete.confirm')"
              position="lt"
              @ok="nodeDelete(index)"
              @cancel="cancelDelete(index)"
            >
              <icon-delete
                v-show="type !== 'view' && !hasNoEditPermission"
                size="16"
                class="icon-delete"
                @click="beforeNodeDelete(index, node)"
              />
            </a-popconfirm>

            <span
              v-show="type !== 'new' && 'scheduleDetailId' in node"
              class="icon-arrow-right"
              @click="beforeJumpMatter(node, 'child')"
              >{{ $t('schedule.go-to') }}</span
            >
          </a-space>
        </a-input-group>
      </div>

      <!-- 附件 -->
      <div class="describe">
        <customIconAttachment class="describe-icon" />
        <span class="describe-span"> {{ $t('schedule.attachment') }}</span>
      </div>
      <div style="margin: 12px 0 20px 0">
        <a-button
          v-if="hasFileEditPermission"
          type="text"
          class="file-upload"
          @click="uploadVisible = true"
          >{{ $t('schedule.uploadAttachment') }}</a-button
        >
      </div>
      <FileItem
        :files="agendaFileList"
        :file-arr-list="fileArrList"
        :type="type"
        :has-file-edit-permission="hasFileEditPermission"
        @delete-file="deleteFileArr"
        @recognize-file="aiRecognition"
      />
      <!-- 事项依赖 -->
      <TaskDependency
        v-if="form.projectId"
        ref="taskDependencyRef"
        :schedule-detail-id="form.scheduleDetailId"
        :project-id="form.projectId"
        :type="type"
        uiType="noDrawer"
        :has-no-edit-permission="hasNoEditPermission"
        @success="handleSuccess"
      />
      <!-- 评论 -->
      <div v-if="type !== 'new'">
        <div class="describe">
          <customIconComment class="describe-icon" />
          <span class="describe-span"> {{ $t('schedule.comment') }}</span>
        </div>
        <a-list>
          <a-list-item v-for="(item, index) in commentList" :key="{ index }">
            <a-list-item-meta
              :title="`${item.createBy}：${item.description}`"
              :description="item.createDate"
            >
              <template #avatar>
                <a-avatar
                  shape="circle"
                  :size="32"
                  style="background-color: rgb(var(--primary-6))"
                >
                  <span>{{ item.createBy.substring(0, 1) }}</span>
                </a-avatar>
              </template>
            </a-list-item-meta>
            <template #actions>
              <a-popconfirm
                position="left"
                :content="$t('schedule.delete.comment')"
                @ok="deleteCommentHandle(item)"
              >
                <icon-delete
                  v-show="item.updateBy === userStore.username"
                  size="16"
                />
              </a-popconfirm>
            </template>
          </a-list-item>
          <template #empty>
            <div class="noData">
              <img :src="commentsBgImg" alt="" />
              <div>{{ $t('schedule.noComment') }}</div>
            </div>
          </template>
        </a-list>
        <a-textarea
          v-model="form.comment"
          :placeholder="$t('schedule.placeholder.comment')"
          :auto-size="{ minRows: 3, maxRows: 3 }"
          class="comment-input"
          @keydown.enter="postComment"
        />
      </div>
    </div>

    <!-- 事项 foot -->
    <div v-if="type !== 'view'" class="comment">
      <!-- 子事项状态均已完成时才显示 -->
      <!-- <a-checkbox
        v-if="type === 'edit' && allSubMatterStatu"
        v-model="matterChecked"
        style="bottom: -8px"
        >标记为完成</a-checkbox
      > -->
      <a-checkbox
        v-if="
          !hasNoEditPermission &&
          route.params.projectId !== '1925370190686588929' &&
          companyId === '100000'
        "
        v-model="CCCC"
        style="bottom: -8px"
        >{{ $t('schedule.delete.push-to-Jiaojiantong') }}</a-checkbox
      >
      <a-space style="float: right" v-if="!hasNoEditPermission">
        <a-button v-if="type === 'new'" type="primary" @click="createMatter">{{
          $t('schedule.createMatter')
        }}</a-button>
        <a-button v-if="type === 'edit'" type="primary" @click="editMatter">{{
          $t('schedule.matter.saveChanges')
        }}</a-button>
      </a-space>
    </div>
  </div>

  <select-members
    v-if="selUserVisible"
    v-model:visible="selUserVisible"
    :data="selUserData"
    @select-member="selectMember"
  ></select-members>

  <uploadTheSpecifiedFolder
    v-model:visible="uploadVisible"
    @upload-single-success="uploadSingleSuccessHandle"
    @upload-complete="uploadComplete"
    @select-complete="selectComplete"
  />

  <a-modal
    :visible="jumpDialog"
    :simple="true"
    :esc-to-close="false"
    :mask-closable="false"
    @ok="jumpDialogOk"
    @cancel="jumpDialogCancle"
  >
    <span
      >{{ $t('schedule.save-modifications') }}
      <icon-close :size="16" class="close-icon" @click="jumpDialog = false" />
    </span>
  </a-modal>
</template>

<script lang="ts" setup>
  import {
    computed,
    inject,
    onMounted,
    reactive,
    Ref,
    ref,
    watch,
    nextTick,
  } from 'vue';
  import useMilestoneApi from '@/views/create-schedule/composables/useMilestoneApi';
  import {
    addAgenda,
    addComment,
    agendaDetail,
    editAgendaAll,
    getAiIdentity,
    getPanelList,
    deleteComment,
    getProjectUsers,
    getProjectPanel,
    addMergaFile,
  } from '../api';
  import { useUserStore, userScheduleStore, useGlobalModeStore } from '@/store';
  import uploadTheSpecifiedFolder from '@/components/uploadTheSpecifiedFolder/index.vue';
  import { storeToRefs } from 'pinia';
  import useUploadFileStore from '@/store/modules/upload-file/index';
  import { useRoute } from 'vue-router';
  import selectMembers from '@/components/selectMembers/index.vue';
  import FileItem from '@/components/file-item/index.vue';
  import { Message } from '@arco-design/web-vue';
  import customIconFile from '@/assets/images/matter/file-list-2-line.svg';
  import customIconDescribe from '@/assets/images/matter/miaos.svg';
  import customIconAttachment from '@/assets/images/matter/attachment-line.svg';
  import customIconComment from '@/assets/images/matter/message-2-line.svg';
  import customIconSubevent from '@/assets/images/matter/zi-matter.svg';

  import customIconGroup from '@/assets/images/matter/Group 1321317016.png';
  import commentsBgImg from '@/assets/images/schedule/comments-bg.png';
  import dayjs from 'dayjs';
  import { cloneDeep, range } from 'lodash';
  import { getUserId } from '@/utils/auth';
  import { getLocalstorage } from '@/utils/localstorage';
  import { useI18n } from 'vue-i18n';
  import TaskDependency from './task-dependency.vue';

  const taskDependencyRef = ref();
  // 引入里程碑列表参数和请求里程碑方法
  const { allMilestoneList, getAllMilestoneList } = useMilestoneApi();
  const { t } = useI18n();
  const companyId = computed(() => userStore.companyId);

  const activeTab = inject('activeTab') as Ref;
  const handleJump = inject('handleJump') as (type: string, id: string) => void;

  const userId = getUserId() || '';
  const globalModeStore = useGlobalModeStore();
  const globalMode = computed(() => globalModeStore.getGlobalMode);
  const currentProjectId = ref(getLocalstorage(`last_project_${userId}`) || '');
  const scheduleStore = userScheduleStore();
  const { scheduleId, currentProjectScheduleId, summaryVisible } =
    storeToRefs(scheduleStore);
  const props = defineProps({
    categoryType: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    data: {
      // 编辑、查看回显的事项数据
      type: Object,
      default: null,
    },
  });
  // 计算属性来动态计算高度
  const heightStyle = computed(() => {
    if (props.type === 'view') {
      return { height: `calc(100vh - 128px)` };
    }
    if (props.categoryType === 'matter') {
      return { height: `calc(100vh - 370px)` };
    }
    return { height: `calc(100vh - 305px)` };
  });

  // 禁选日期
  const disabledDate = (current: any) => {
    // 禁用开始时间之前的所有日期
    return (
      form.planStartTime &&
      current &&
      current < dayjs(form.planStartTime).startOf('day')
    );
  };

  // 禁选时间
  const disabledDateTime = (selectedDate: any) => {
    if (
      !form.planStartTime ||
      !selectedDate ||
      !dayjs(selectedDate).isSame(dayjs(form.planStartTime), 'day')
    ) {
      return {
        disabledHours: () => [],
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }

    const startTime = dayjs(form.planStartTime);
    const selectedDateTime = dayjs(selectedDate);
    const startHour = startTime.hour();
    const startMinute = startTime.minute();

    const disabledHours = () => {
      const hours = [];
      for (let i = 0; i < 24; i++) {
        if (
          i < startHour ||
          (i === startHour && selectedDateTime.date() === startTime.date())
        ) {
          hours.push(i);
        }
      }
      return hours;
    };

    const disabledMinutes = (selectedHour: any) => {
      if (
        selectedHour !== startHour ||
        selectedDateTime.date() !== startTime.date()
      ) {
        return [];
      }

      const minutes = [];
      for (let i = 0; i < startMinute; i++) {
        minutes.push(i);
      }
      return minutes;
    };

    return {
      disabledHours,
      disabledMinutes,
      disabledSeconds: () => [],
    };
  };

  const matterChecked = ref(false); // 编辑事项标记为完成
  const CCCC = ref(false); // 是否推送至交建通
  const spanLoading = ref(false);
  const uploadFileStore = useUploadFileStore();
  const { fileArr, loading, uploadFileList, hasTokenFile } =
    storeToRefs(uploadFileStore);
  const userStore = useUserStore();
  const userPhone = computed(() => userStore.phone);
  const userName = computed(() => userStore.username);
  const uploadVisible = ref(false); // 上传文件弹窗
  const selectFolderObj = ref({});
  const route = useRoute();
  const agendaFileList = ref<any>([]); // 上传附件数据
  const fileArrList = ref<any>([]); // 文件选择列表（原始数据，不存fileToken，存在FILE对象）

  const uploadComplete = () => {
    uploadVisible.value = false; // 取消按钮关闭对话框
  };
  const uploadSingleSuccessHandle = (val: any) => {
    const data = {
      ...val,
      fileName: val.name || '',
      fileSize: val.size || '',
      token: val.fileToken || '',
    };
    agendaFileList.value.push(data);
  };
  const selectComplete = function (selectFolderObjA: any) {
    console.log('553:上传完成之后返回的数据', selectFolderObjA);
    selectFolderObj.value = { ...selectFolderObjA };
    if (fileArr.value.length > 0) {
      uploadFileStore.projectId = route.params.projectId as string;
      uploadFileStore.handleUploadFile(selectFolderObj.value, 0);
      fileArrList.value.push(...fileArr.value);
    }
  };

  const form = reactive({
    projectId: '', // 项目id
    parentId: 0, // 父事项的id
    milestoneId: '', // 所属里程碑
    rateProgress: 0, // 进度
    chargePersonId: [], // 负责人id
    scheduleDetailId: '',
    planStartTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 开始时间
    planEndTime: '', // 结束时间
    content: '', // 描述
    comment: '', // 评论
    schedulePanelId: '', // 日程id
  });

  // 触发上传完成事件
  watch(loading, (val, oldVal) => {
    if (!val && oldVal) {
      if (uploadFileList.value.length === 0) {
        uploadVisible.value = false;
      }
      if (props.type === 'view') {
        editMatter().then(() => {
          getAgendaDetail(); // 在 editMatter 执行完成后执行
        });
      }
    }
  });

  /** 删除已经上传附件 */
  const deleteFileArr = (file: any) => {
    // 找到目标元素的索引
    const index = agendaFileList.value.findIndex(
      (item: any) => item.fileToken === file.fileToken
    );
    // 如果找到了目标元素，使用 splice 删除
    if (index !== -1) {
      agendaFileList.value.splice(index, 1);
    }

    fileArrList.value = fileArrList.value?.filter((item: { uid: string }) => {
      // 检查 file 是否为对象并且具有 uid 属性
      return item.uid !== file.uid;
    });
  };
  // 数组去重
  const deduplication = (data: any, type: any) => {
    if (type === 1) {
      const uniqueList = data.reduce((acc, cur) => {
        if (!acc.some((item) => item.username === cur.username)) acc.push(cur);
        return acc;
      }, []);
      return uniqueList;
    }
    return data?.filter(
      (item: any, index: any, self: any) =>
        index ===
        self.findIndex((t: any) => t.userFullName === item.userFullName)
    );
  };
  /** 获取主事项归属项目下的成员列表 */
  const projectMemberList = ref<any[]>([]);
  const getProjectUserList = async (projectId: string) => {
    const params = {
      pageNo: 1,
      pageSize: 999999,
      projectId,
    };
    try {
      const res = (await getProjectUsers(params)) as any;
      if (res.code === 8000000) {
        // 根据人名去重
        const data = deduplication(res.data?.list || [], 1);
        projectMemberList.value = data?.map((element: any) => {
          // 创建一个新的对象，包含修改后的属性
          return {
            ...element,
            userName: element.username,
            userFullName: element.name,
            userFullname: element.name,
          };
        });
      }
      console.log('[ memberList.value ] >', projectMemberList.value);
    } catch (error) {
      console.error('获取项目人员列表失败:', error);
    }
  };

  const calendarOption: any = ref([]); // 日程数据
  const projectCalendarOption: any = ref([]); // 该项目日历数据

  // 获取日历数据
  const getPanelListHandle = async () => {
    const param = {
      companyId: '100000', // 中交默认id
    };
    const { data } = await getPanelList(param);
    calendarOption.value = data;
    console.log(scheduleId.value, '个人所有日历');

    const projectParam = {
      projectId: currentProjectId.value,
    };
    if (globalMode.value === 'project' || form.projectId) {
      const { data: datas } = await getProjectPanel(projectParam);
      projectCalendarOption.value = [datas];
    }
    if (props.type === 'new') {
      // 新建事项时默认展示我的日历
      const mineData = calendarOption.value?.filter((item: any) => {
        return item?.defaultFlag === 1;
      });
      // form.schedulePanelId = mineData[0].id || '';
      if (globalMode.value === 'work') {
        form.schedulePanelId = scheduleId.value || mineData[0].id;
        console.log(form.schedulePanelId, '个人新建事项绑定的日历');
      } else {
        // 项目空间下默认展示当前项目的日历
        form.schedulePanelId =
          currentProjectScheduleId.value.toString() ||
          projectCalendarOption.value[0]?.id;
      }
    }
  };

  const selUserVisible = ref(false); // 选人组件对话框是否可见

  const suballocation = ref<boolean>(false); // 判断是否是子事项的分配人
  const suballocationIndex = ref<number>(-1); // 存储子事项分配人的索引
  const subMemberList = reactive<any>([]); // 存储所有子事项分配人

  const selUserData = ref<any>(); // 用以回显给选人组件数据
  // 获取焦点时触发选人弹窗
  const getUserFocus = () => {
    selUserData.value = memberList.value;
    console.log('[ selUserData.value ] >', selUserData.value);
    selUserVisible.value = true;
  };

  // 获取子事项分配焦点触发选人弹窗
  const getSubUserFocus = (val: boolean, index: number) => {
    selUserVisible.value = true;
    suballocation.value = val; // 判断是否是子事项的分配人
    suballocationIndex.value = index;
    selUserData.value = subMemberList[index];
    console.log('[ selUserData.value ] >', selUserData.value);
  };

  const memberList = ref<any[]>([]); // 选中的成员
  // 获取选择成员
  const selectMember = async (user: any) => {
    console.log('[ user ] >', user);
    user?.forEach((item: any) => {
      item.userFullName = item?.userFullname;
    });
    if (suballocation.value) {
      suballocation.value = false;
      subMemberList[suballocationIndex.value] = user; // 存储对应子事项分配人
      nodeData.value[suballocationIndex.value].chargePersonId = user?.map(
        (item: any) => item.userName
      );
    } else {
      memberList.value = user;
      form.chargePersonId = user?.map((item: any) => item.userName);
      await formRef.value?.validateField('chargePersonId'); // 选择人员以后触发校验 从而改变选择器的 error 状态
    }
  };

  const nodeData = ref<any[]>([]);
  // 添加子事项
  const addNode = () => {
    nodeData.value.push({
      title: '', // 标题
      planEndTime: '', // 截止时间
      chargePersonId: [], // 分配人
    });
  };

  const indexMap = ref(new Map()); // 保存旧索引到新索引的映射
  // 更新映射表
  const rebuildIndexMap = () => {
    indexMap.value.clear();
    nodeData.value?.forEach((_, index) => {
      indexMap.value.set(index, index);
    });
  };

  // 二次删除确认框是否显示
  const activePopIndex = ref<number | null>(null);
  // 删除节点前校验
  const beforeNodeDelete = (index: number, record: any) => {
    if (!('scheduleDetailId' in record)) {
      nodeDelete(index);
    } else {
      activePopIndex.value = index; // 只显示当前行的 popconfirm
    }
  };

  function arraysEqualWithOrder(arr1: string[], arr2: string[]) {
    if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
    if (arr1.length !== arr2.length) return false;

    return arr1.every((val, idx) => val === arr2[idx]);
  }

  // 基本信息是否变化
  function isBasicInfoChanged(detail: any) {
    // 判断标题是否一致
    if (props.title !== detail.title) return true;

    // chargePersonId 比较（form 是数组，detail 是逗号分隔字符串）
    const formIds = form.chargePersonId || [];
    const detailIds = detail.chargePersonId
      ? detail.chargePersonId.split(',')
      : [];

    if (!arraysEqualWithOrder(formIds, detailIds)) {
      return true;
    }

    // 时间是否相同（年月日时分）
    const formatTime = (str) => (str || '').slice(0, 16);
    if (formatTime(form.planEndTime) !== formatTime(detail.planEndTime)) {
      return true;
    }
    if (formatTime(form.planStartTime) !== formatTime(detail.planStartTime)) {
      return true;
    }

    // 归属日历可见时是否相同
    if (
      scheduleVisible.value &&
      form.schedulePanelId !== detail.schedulePanelId
    ) {
      return true;
    }

    // content
    if ((form.content || '') !== (detail.content || '')) return true;

    return false;
  }

  // 子事项数据是否变化
  function isChildrenChanged(nodeData: any[], detailChildren: any[]) {
    // 长度不一致直接变化
    if (nodeData.length !== detailChildren.length) return true;

    const formatTime = (str) => (str || '').slice(0, 16);

    for (let i = 0; i < nodeData.length; i++) {
      const node = nodeData[i];
      const detail = detailChildren[i];
      // title 不一致
      if (node.title !== detail.title) return true;

      // 时间不一致（年月日时分）
      if (formatTime(node.planEndTime) !== formatTime(detail.planEndTime))
        return true;

      // chargePersonId 不一致（需忽略顺序，但字段整体顺序敏感）
      const nodeIds = node.chargePersonId || [];
      const detailIds = detail.chargePersonId.split(',') || [];
      if (!arraysEqualWithOrder(nodeIds, detailIds)) return true;
    }

    return false; // 没变化
  }

  // 附件信息是否变化
  function isAttachmentChanged(detail: any) {
    if (agendaFileList.value?.length !== detail.agendaFileList?.length)
      return true;

    const agendaFileListIds = agendaFileList.value.map(
      (item: { id: string }) => item.id
    );
    const detailAgendaFileListIds = detail.agendaFileList.map(
      (item: { id: string }) => item.id
    );

    if (!arraysEqualWithOrder(agendaFileListIds, detailAgendaFileListIds)) {
      return true;
    }

    return false; // 没变化
  }

  // 评论信息是否变化
  function isCommentChanged() {
    if (form.comment !== '') return true;
    return false;
  }

  // 校验事项数据是否有改动
  const isMatterChanged = () => {
    const detail = cloneDeep(matterDetail.value);
    // console.log('[ matterDetail.value ] >', matterDetail.value);

    if (isBasicInfoChanged(detail)) return true;

    if (isChildrenChanged(nodeData.value, detail.children)) return true;

    if (isAttachmentChanged(detail)) return true;

    if (isCommentChanged()) return true;

    return false;
  };

  const jumpDialog = ref(false); // 子事项跳转确认框是否显示
  const jumpData = ref<any>({}); // 跳转事项数据

  //  二次跳转事项确认框是否显示
  const beforeJumpMatter = async (record: any, type: string) => {
    if (activeTab.value === 'matters') {
      if (props.type === 'edit') {
        if (isMatterChanged()) {
          jumpDialog.value = true;
          jumpData.value = record;
          jumpData.value.jumpType = type;
        } else if (type === 'child') {
          emit('refresh', record.scheduleDetailId);
        } else {
          emit('refresh', record?.parentScheduleDetailId);
        }
      } else if (props.type === 'view') {
        if (type === 'child') {
          emit('refresh', record.scheduleDetailId);
        } else {
          emit('refresh', record?.parentScheduleDetailId);
        }
      }
    } else {
      scheduleStore.setSummaryVisible(true);
      const data = {
        id: record.scheduleDetailId,
        type: 'matters',
      };
      await scheduleStore.setSummaryData(data);
    }
  };

  // 二次跳转会议确认框是否显示
  const beforeJumpMeeting = (record: any) => {
    if (props.type === 'edit') {
      if (isMatterChanged()) {
        jumpDialog.value = true;
        jumpData.value = record;
        jumpData.value.jumpType = 'meeting';
      } else {
        handleJump('meeting', record.meetingId);
      }
    } else if (props.type === 'view') {
      handleJump('meeting', record.meetingId);
    }
  };

  // 对话框跳转子事项ok事件
  const jumpDialogOk = async () => {
    if (await formValidation()) {
      if (form.comment !== '') {
        await postComment();
      }
      await editMatter();

      if (jumpData.value.jumpType === 'child') {
        emit('refresh', jumpData.value.scheduleDetailId);
      } else if (jumpData.value.jumpType === 'main') {
        emit('refresh', jumpData.value?.parentScheduleDetailId);
      } else if (jumpData.value.jumpType === 'meeting') {
        handleJump('meeting', jumpData.value.meetingId);
      }
    } else {
      Message.info('请完善事项信息！');
    }
  };

  // 对话框跳转子事项cancle事件
  const jumpDialogCancle = () => {
    if (jumpData.value.jumpType === 'child') {
      emit('refresh', jumpData.value.scheduleDetailId);
    } else if (jumpData.value.jumpType === 'main') {
      emit('refresh', jumpData.value?.parentScheduleDetailId);
    } else if (jumpData.value.jumpType === 'meeting') {
      handleJump('meeting', jumpData.value.meetingId);
    }
  };

  // 取消删除节点
  const cancelDelete = (index: number) => {
    activePopIndex.value = null;
  };

  // 删除节点
  const nodeDelete = (index: number) => {
    // 直接使用当前索引删除元素
    nodeData.value.splice(index, 1);
    subMemberList.splice(index, 1);
    // 删除后关闭 popconfirm
    activePopIndex.value = null;
    // 删除后重新构建索引映射
    rebuildIndexMap();
  };

  // 评论成功以后将滚动条滚动到最底部
  const postCommentSuccess = async () => {
    const content = document.getElementsByClassName('matter-content-top');
    if (content.length > 0) {
      const container = content[0];
      container.scrollTop = container.scrollHeight;
      // scrollHeight：元素的完整内容高度，包括由于溢出而不可见的内容部分。
      // scrollTop：设置或获取元素的垂直滚动位置。
    }
  };

  /** 发表评论 */
  const postComment = async () => {
    if (form.comment) {
      const params = {
        businessId: matterDetail.value.scheduleDetailId, // 事项详情id 暂时写死
        description: form.comment, // 评论内容
        type: 1, // 1：事项评论 2：会议评论
      };
      const res = await addComment(params);
      if (res.status) {
        Message.success(t('schedule.comment.success'));
        form.comment = '';
        await getAgendaDetail('addComment');
        postCommentSuccess(); // 评论成功以后将滚动条滚动到最底部
      }
    } else {
      Message.warning(t('schedule.comment.placeholder'));
    }
  };

  // 创建事项——处理子事项清单数据
  const handleNodeData = () => {
    return nodeData.value.map((item: any) => ({
      ...item,
      planStartTime: dayjs().format('YYYY-MM-DD HH:mm'),
      planEndTime: item.planEndTime.slice(0, 16),
      chargePersonId: item.chargePersonId.toString(),
      projectId: form.projectId, // 项目id
      schedulePanelId: form.schedulePanelId, // 日历id
      type: 'item',
    }));
    // nodeData.value?.forEach((item: any) => {
    //   item.planEndTime = item.planEndTime.slice(0, 16);
    //   item.chargePersonId = item.chargePersonId.toString();
    //   item.projectId = form.projectId; // 项目id
    //   item.schedulePanelId = form.schedulePanelId; // 日历id
    //   item.type = 'item';
    // });
  };

  const formRef = ref();
  // 创建、编辑事项——主、子事项时间校验
  const subFormTimeValid = () => {
    const now = dayjs();
    const createDate = dayjs(matterDetail.value?.createDate);
    const allValid = [
      form.planEndTime,
      ...nodeData.value.map((item) => item.planEndTime),
    ].every((time) => {
      if (!time) return true; // 忽略未填写的时间
      return !dayjs(time).isBefore(props.type === 'new' ? now : createDate); // 如果早于当前时间就不合法
    });

    if (!allValid) {
      Message.info(
        props.type === 'new'
          ? t('schedule.deadline.error')
          : t('schedule.deadline.create.error')
      );
      return false;
    }

    return true;
  };

  // 表单验证
  const formValidation = async () => {
    // 检查标题是否为空
    if (!props.title) {
      Message.info(t('schedule.inputTitle'));
      return false; // 提前返回，避免后续逻辑执行
    }

    // 主事项表单校验
    const mainFormValid = await formRef.value?.validate(); // 假设 validate() 返回布尔值或抛出错误
    if (mainFormValid) {
      return false;
    }

    // 子事项表单校验
    const subFormValid = nodeData.value.every((item) => {
      // 检查 title 是否是非空字符串
      const hasTitle = item.title !== '';
      // 检查 planEndTime 是否是非空字符串
      const hasTime = item.planEndTime !== '';
      // 检查 chargePersonId 是否是非空数组（假设 chargePersonId 总是数组或未定义）
      const hasChargePersonId =
        Array.isArray(item.chargePersonId) && item.chargePersonId.length > 0;
      // 返回两个检查的结果
      return hasTitle && hasTime && hasChargePersonId;
    });

    if (!subFormValid) {
      Message.info(t('schedule.subTaskList.placeholder'));
      return false;
    }
    console.log(
      form.projectId,
      taskDependencyRef.value?.nodeData,
      'taskDependencyRef.value.hasEmptyEditingNode.value1111111111',
      taskDependencyRef.value
    );
    // 事项时间校验 返回最终验证结果
    return subFormTimeValid();
  };

  // 注入爷爷组件提供的方法
  const grandparentMethod = inject('grandparentMethod');

  // 调用爷爷组件的方法
  const callGrandparentMethod = () => {
    if (grandparentMethod) {
      grandparentMethod();
    }
  };

  // 创建事项成功以后清空表单
  const clearForm = () => {
    emit('update:title', ''); // 清空标题
    // 清空主事项表单
    form.chargePersonId.length = 0;
    form.comment = '';
    form.content = '';
    form.planStartTime = '';
    form.planEndTime = '';
    form.projectId = '';
    form.parentId = 0;
    form.milestoneId = ''; // 所属里程碑
    form.rateProgress = 0; // 进度
    form.schedulePanelId = '';
    form.scheduleDetailId = '';
    memberList.value.length = 0;
    // 清空子事项表单
    nodeData.value.length = 0;
    subMemberList.length = 0;
    // 清空上传附件列表
    agendaFileList.value.length = 0;
    // 清空文件选择列表（原始数据，不存fileToken，存在FILE对象）
    fileArrList.value.length = 0;
  };

  /** 将设置另存为指定团队文件夹的文件进行处理 */
  const saveFileSeparately = async () => {
    // eslint-disable-next-line no-restricted-syntax
    for (const item of agendaFileList.value) {
      if (item.specifiedFolderId) {
        const params = {
          fileToken: item.fileToken,
          folderId: item.specifiedFolderId,
          name: item.fileName,
          projectId: route.params.projectId,
          size: item.fileSize,
          teamId: item.teamId,
        };
        // eslint-disable-next-line no-await-in-loop
        await addMergaFile(params);
      }
    }
  };
  // 事项依赖新创建的保存成功，通知父组件刷新数据
  const handleSuccess = () => {
    console.log('2222222222222');
    emit('update:title', ''); // 清空标题
    // emit('refresh', 'new');
  };
  /** 创建事项 */
  const createMatter = async () => {
    if (await formValidation()) {
      await saveFileSeparately();
      const params = {
        agendaParam: {
          agendaFileList: agendaFileList.value,
          agendaStatus: matterChecked.value === true ? 2 : 1, // 任务状态：1 进行中 2已完成
          chargePersonId: form.chargePersonId.toString(), // 负责人id
          content: form.content,
          planStartTime: form.planStartTime.slice(0, 16),
          planEndTime: form.planEndTime.slice(0, 16),
          schedulePanelId: form.schedulePanelId, // 日历id 暂时写死
          ...(userStore.teamId !== 'global' && globalMode.value === 'work'
            ? { teamId: userStore.teamId }
            : {}),
          title: props.title,
          type: 'item',
          // 如果是项目空间，项目id默认传当前项目的，否则不传
          ...(globalMode.value === 'project' && currentProjectId.value
            ? { projectId: currentProjectId.value }
            : {}),
          milestoneId: form.milestoneId, // 所属里程碑id
          rateProgress: form.rateProgress, // 进度
          parentId: form.parentId, // 父事项id
        },
        children: handleNodeData(), // 处理子事项清单数据,
        pushNotification: CCCC.value,
      };
      const res: any = await addAgenda(params);
      if (res.status) {
        if (form.projectId) {
          taskDependencyRef.value?.saveAllDependency(res.data.scheduleDetailId);
        }
        Message.success(res.message);
        emit('update:title', ''); // 清空标题
        emit('refresh', 'new');
        // 保存事项依赖

        clearForm(); // 清空表单
        // callGrandparentMethod();
      }
    }
  };

  const emit = defineEmits(['update:title', 'refresh']);

  // 为了防止数据混乱，这里单独处理编辑事项时对子事项数据的处理
  const handleEditSubMatters = (type: string, data: any) => {
    if (type === 'add') {
      data?.forEach((item: any) => {
        item.planStartTime = dayjs().format('YYYY-MM-DD HH:mm');
        item.planEndTime = item.planEndTime.slice(0, 16);
        item.chargePersonId = item.chargePersonId.toString();
        item.projectId = form.projectId; // 项目id\
        item.pushNotification = CCCC.value;
        item.schedulePanelId = form.schedulePanelId; // 日历id
        item.teamId = userStore.teamId === 'global' ? null : userStore.teamId;
        item.type = 'item';
      });
    } else if (type === 'edit') {
      data?.forEach((item: any) => {
        item.planStartTime = item.planStartTime.slice(0, 16);
        item.planEndTime = item.planEndTime.slice(0, 16);
        item.chargePersonId = item.chargePersonId.toString();
        item.id = item.scheduleDetailId;
        item.pushNotification = CCCC.value;
      });
    }
  };

  // 获取子事项数据
  const getSubMatters = (type: string) => {
    const { children } = matterDetail.value;
    let result;
    if (type === 'add') {
      console.log('[ add ] >');
      // 新增的子事项数据——一定不存在 scheduleDetailId
      result = nodeData.value
        ?.filter((item: any) => !('scheduleDetailId' in item))
        .map((item) => ({ ...item })); // 创建浅拷贝
      handleEditSubMatters('add', result);
    } else if (type === 'edit') {
      console.log('[ edit ] >');
      // 修改的子事项数据——一定存在 scheduleDetailId
      result = nodeData.value
        ?.filter((item: any) => 'scheduleDetailId' in item)
        .map((item) => ({ ...item }));
      handleEditSubMatters('edit', result);
    } else if (type === 'delete') {
      console.log('[ delete ] >');
      // 提取 nodeData.value 中的 scheduleDetailId 集合
      const nodeDataScheduleDetailIds = nodeData.value
        ?.filter((item) => item.scheduleDetailId) // 过滤出有 scheduleDetailId 的项
        ?.map((item) => item.scheduleDetailId);

      // 找出 children 中 scheduleDetailId 不在 nodeDataScheduleDetailIds 中的项，并提取它们的 scheduleDetailId 集合
      result = children
        ?.filter(
          (child: { scheduleDetailId: string }) =>
            !nodeDataScheduleDetailIds.includes(child.scheduleDetailId)
        )
        ?.map((child: { scheduleDetailId: string }) => child.scheduleDetailId);
      handleEditSubMatters('delete', result);
    }
    return result;
  };

  // 编辑事项
  const editMatter = async () => {
    if (await formValidation()) {
      await saveFileSeparately();
      // 主事项数据
      const parentagenda = {
        agendaFileList: agendaFileList.value,
        agendaStatus: matterDetail.value.agendaStatus, // 任务状态：1 进行中 2已完成
        chargePersonId: form.chargePersonId.toString(), // 负责人id
        content: form.content,
        id: matterDetail.value.scheduleDetailId,
        planStartTime: form.planStartTime.slice(0, 16),
        planEndTime: form.planEndTime.slice(0, 16),
        projectId: form.projectId,
        pushNotification: CCCC.value,
        schedulePanelId: matterDetail.value.schedulePanelId, // 日历id 暂时写死
        teamId: matterDetail.value.teamId, // 团队id 如果没有则不用传 暂时写死
        title: props.title,
        type: matterDetail.value.type,
        milestoneId: form.milestoneId, // 所属里程碑id
        rateProgress: form.rateProgress, // 进度
        parentId: form.parentId, // 父事项id
      };
      // 是否推送交件通
      const pushNotification = CCCC.value;
      // 子事项数据新增
      const sonAgendaListAdd = getSubMatters('add');
      // 子事项数据修改
      const sonAgendaListEdit = getSubMatters('edit');
      // 子事项数据删除
      const sonAgendaListRemove = getSubMatters('delete');

      const params = {
        parentagenda,
        pushNotification,
        sonAgendaListAdd,
        sonAgendaListEdit,
        sonAgendaListRemove,
      };
      const res: any = await editAgendaAll(params);
      if (res.status === true) {
        if (props.type !== 'view') {
          Message.success(t('schedule.calendar.edit'));
        }
        // 对话框跳转时无需刷新当前事项数据
        if (!jumpDialog.value) {
          emit('refresh', matterDetail.value.scheduleDetailId);
        }
      } else {
        Message.error(t('schedule.calendar.edit.error'));
      }
    }
  };

  // 处理回显的主事项分配成员数据
  const handleMemberList = (data: any) => {
    console.log('data-项目1038', data);
    console.log('data-项目人员1039', projectMemberList.value);
    data?.chargePersonIdList?.forEach((item: any) => {
      item.userFullname = item.userFullName;
      item.id = item.userId;
    });
    memberList.value = data?.chargePersonIdList;
    const userIds = data.chargePersonId?.split(','); // 使用中文逗号分割
    form.chargePersonId = userIds;
  };
  // 计算属性：取出所有 userFullname 并组成字符串
  const chargeUserFullname = computed(() => {
    return memberList.value
      .map((member: any) => member.userFullname)
      .join(', ');
  });
  // 选择分配给
  const handlechargePersonChange = (usernames: any) => {
    console.log('选中的参与人的memberList组成的数组:', memberList);
    // 根据usernames 去projectUserList里面查到对应的用户信息
    const users = projectMemberList.value.filter((item: any) =>
      usernames.includes(item.username)
    );
    memberList.value = users;
    console.log('选中的分配给所有信息:', users);
  };
  const allSubMatterStatu = ref(false);
  const ensureArray = (value: any): any[] => {
    if (typeof value === 'string') {
      return value.split(',');
    }
    if (Array.isArray(value)) {
      return value;
    }
    return [];
  };
  // 处理回显的子事项清单数据
  const processNodeData = (data: any) => {
    console.log(data, '1049子事项回显数据');
    allSubMatterStatu.value = data.children.every(
      (item) => item.agendaStatus === 2
    ); // 获取所有子事项状态是否都为已完成

    data.children?.forEach((item: any, index: number) => {
      // 使用 index + 1 作为 subMemberList 的索引
      console.log('item.chargePersonId1056', item.chargePersonId);
      item.chargePersonId = ensureArray(item.chargePersonId);
      item.chargePersonIdList?.forEach((data: any) => {
        data.userFullname = data.userFullName;
        data.id = data.userId;
      });
      subMemberList[index] = item.chargePersonIdList;
    });
    nodeData.value = [...data.children];
  };

  const commentList = ref(); // 评论列表数据
  const scheduleVisible = ref(false); // 归属日历是否可见
  // 更新事项表单数据
  const updateMatterForm = async (data: any) => {
    const detail = cloneDeep(data);
    if (detail?.projectId) await getProjectUserList(detail?.projectId);
    console.log('[ detail ] >', detail);
    scheduleVisible.value = detail.createBy === userName.value;
    form.projectId = detail?.projectId;
    form.parentId = detail?.parentId || 0; // 父事项id
    form.scheduleDetailId = detail?.scheduleDetailId || ''; // 事项id
    form.milestoneId = detail?.milestoneId || ''; // 所属里程碑id
    form.rateProgress = detail?.rateProgress || 0; // 进度
    handleMemberList(detail); // 回显主事项分配成员
    form.planStartTime = detail?.planStartTime;
    form.planEndTime = detail?.planEndTime;
    form.schedulePanelId = detail?.schedulePanelId;
    form.content = detail?.content;
    processNodeData(detail); // 回显子事项清单
    commentList.value = detail?.commentList; // 回显评论列表数据
    agendaFileList.value = detail?.agendaFileList; // 回显上传附件数据
    agendaFileList.value?.forEach((item: any) => {
      item.fileToken = item.token;
    });
  };

  const matterDetail = ref(); // 保存事项详情数据
  // 查询事项详情
  const getAgendaDetail = async (type?: string) => {
    try {
      const res = await agendaDetail(matterDetail.value.scheduleDetailId);
      if (res.status) {
        matterDetail.value = res.data;
        if (type === 'addComment') {
          commentList.value = res.data?.commentList; // 发布评论仅回显评论列表数据
        } else {
          updateMatterForm(res.data);
        }
      }
    } catch (error) {
      // 错误处理
      console.error('获取事项详情失败:', error);
    } finally {
      // 无论成功或失败都要执行
    }
  };

  defineExpose({
    createMatter,
    beforeJumpMatter,
    beforeJumpMeeting,
  });

  onMounted(async () => {
    // await getProjectByUserphone(); // 获取归属项目
    await getPanelListHandle(); // 获取日程数据
    if (props.data && (props.type === 'edit' || props.type === 'view')) {
      matterDetail.value = props.data;
      updateMatterForm(props.data);
    }
    // 只要当前是项目空间下，进入到事项页面，不管是创建还是编辑，还是查看，都先获取一下里程碑数据
  });
  const propsCopyData: any = ref({}); // 备份一份详情数据
  watch(
    () => props.data,
    async (val: any) => {
      console.log('1095', props.type);
      // 存一份数据,需要使用详情里的projectId进行判断当前是否是项目绑定的会议
      // 备份一份详情数据
      propsCopyData.value = JSON.parse(JSON.stringify(props.data));
      if (props.type === 'edit' || props.type === 'view') {
        matterDetail.value = props.data;
        // 用编辑/查看详情里的 projectId 请求
        if (val?.projectId) {
          await getAllMilestoneList(val.projectId);
        }
        updateMatterForm(props.data);
      } else if (globalMode.value === 'project') {
        form.projectId = currentProjectId.value;
        form.parentId = 0;
        // 新建事项时默认查询当前项目的里程碑数据
        await getAllMilestoneList(currentProjectId.value);
      }
    },
    {
      immediate: true,
    }
  );
  // 过滤不在该项目下的 ai识别事项描述生成的待办人员
  const filterMembers = async (data: any) => {
    return data.filter((item: any) =>
      projectMemberList.value.some(
        (user: any) => user.userName === item.userName
      )
    );
  };
  // ai 识别事项生成子事项清单
  const aiRecognition = async (type: string, formData?: any) => {
    spanLoading.value = true;
    if (type === 'content' && !form.content) {
      Message.info(t('schedule.matter.description.placeholder'));
    } else {
      try {
        const params = {
          prompt: form.content,
        };
        const res = await getAiIdentity(type === 'content' ? params : formData);
        if (res.status) {
          const { data } = res;
          if (data?.length > 0) {
            // 遍历 data 数组，并将 title 和 chargePersonId 赋值给 nodeData.value
            data?.forEach(async (item: any, index: number) => {
              const regex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/;
              let planEndTime = '';
              if (regex.test(item?.planEndTime)) {
                planEndTime = item?.planEndTime;
              } else {
                planEndTime = form.planEndTime?.slice(0, 16);
              }
              // 需要过滤一下再描述里提到的人员不在该项目下，需要过滤掉
              if (form.projectId && item.userInfoBoList) {
                console.log('userInfoBoList:', item.userInfoBoList); // 检查内容
                item.userInfoBoList = await filterMembers(item.userInfoBoList);
              }
              const userNameArr = Array.isArray(item.userInfoBoList)
                ? item.userInfoBoList.map((user: any) => user.userName)
                : [];
              nodeData.value.push({
                title: item?.title || '', // 赋值标题
                planEndTime: planEndTime || '', // 赋值截止时间
                chargePersonId: userNameArr, // 赋值分配人
                content: item?.content || '', // 描述
              });

              item.userInfoBoList?.forEach((info: any) => {
                info.userFullname = info.userFullName;
                info.id = info.userId;
              });
              subMemberList.push(item.userInfoBoList);
            });
          }
        }
      } catch (error) {
        // 错误处理
        console.error('ai解析文本失败:', error);
      } finally {
        // 无论成功或失败都要执行
        spanLoading.value = false;
      }
    }
    spanLoading.value = false;
  };

  // 删除评论
  const deleteCommentHandle = async (record: any) => {
    const param = {
      username: record.updateBy,
      commentId: record.id,
    };
    const res = await deleteComment(param);
    if (res.status) {
      Message.success(t('schedule.delete.success'));
      emit('refresh', record.businessId);
      if (props.type === 'view') {
        getAgendaDetail();
      }
    }
  };
  // 手动选择所属里程碑数据
  const handleChangeMilestone = (value: string) => {
    form.milestoneId = value; // 更新里程碑id
  };
  watch(
    currentProjectId,
    (newValue) => {
      if (newValue) {
        console.log('1173:项目人员');
        // 这里是在项目空间下的新建，需要请求用户列表，编辑在回显那里会去请求
        if (globalMode.value === 'project') {
          getProjectUserList(newValue); // 当项目 ID 有值时请求项目用户列表
        }
      }
    },
    { immediate: true } // 初始化时立即执行
  );
  watch(
    nodeData,
    (newValue, oldValue) => {
      console.log('nodeData 发生变化1198:', { newValue, oldValue });
      // 在这里处理 nodeData 值变化的逻辑
      // 比如重新计算某些值或触发其他操作
    },
    { deep: true } // 深度监听，确保监听到对象内部属性的变化
  );
  // 只有创建人和参与人有权限编辑
  const hasNoEditPermission = computed(() => {
    const currentUser = userStore.username;
    const creator = propsCopyData.value.createBy;
    const chargePersonIdList = propsCopyData.value?.chargePersonIdList || [];
    // 提取所有参与人的 username
    const chargePersonIdListUserNames = chargePersonIdList.map(
      (p: any) => p.userName
    );
    // 假设是username数组
    return (
      props.type === 'edit' &&
      currentUser !== creator &&
      !chargePersonIdListUserNames.includes(currentUser)
    );
  });
  // 文件编辑权限
  const hasFileEditPermission = computed(() => {
    const currentUser = userStore.username;
    console.log('当前用户:', currentUser);
    console.log('创建人:', propsCopyData.value.createBy);
    const creator = propsCopyData.value.createBy;
    const chargePersonIdList = propsCopyData.value?.chargePersonIdList || [];
    // 提取所有参与人的 username
    const chargePersonIdListUserNames = chargePersonIdList.map(
      (p: any) => p.userName
    );
    // 假设是username数组
    return (
      props.type === 'new' ||
      currentUser === creator ||
      chargePersonIdListUserNames.includes(currentUser)
    );
  });
  watch(
    () => form.planStartTime,
    (newValue) => {
      if (
        newValue &&
        form.planEndTime &&
        dayjs(form.planEndTime).isBefore(newValue)
      ) {
        form.planEndTime = ''; // 重置结束时间，如果它早于新的开始时间
      }
    }
  );
</script>

<script lang="ts">
  export default {
    name: 'CategoryMatter',
  };
</script>

<style lang="less" scoped>
  .matter-content {
    position: relative;
    .matter-content-top {
      position: relative;
      padding: 16px 20px 20px 20px;
      overflow: auto;
    }
  }

  .describe {
    display: flex;
    align-items: center;
    height: 32px;
    .describe-icon {
      width: 20px;
      height: 20px;
    }
    .describe-span {
      font-size: 16px;
      line-height: 24px;
      font-weight: 500;
      color: #1d2129;
      margin-left: 8px;
    }
    .ai-recognition {
      margin-left: 12px;
      cursor: pointer;
      height: 22px;
    }
  }

  .file-upload {
    border-radius: 4px;
    border: 1px solid #3366ff;
    // width: 108px;
    height: 26px;
  }

  .icon-delete {
    cursor: pointer;
    color: red;
    margin-left: 20px;
  }

  .icon-arrow-right {
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    color: #3366ff;
    white-space: nowrap;
  }

  .noData {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    img {
      display: block;
      width: 120px;
      height: 112px;
    }
    div {
      margin-top: 16px;
      color: #4e5969;
    }
  }

  .comment {
    position: sticky;
    left: 0;
    width: 100%;
    bottom: 0;
    height: 72px;
    border-top: 1px solid #d9d9d9;
    border-radius: 0 0 8px 8px;
    padding: 20px;
    background-color: #fff;
    z-index: 999;
  }

  .comment-input {
    margin: 20px 0 0 0;
  }

  :deep(.arco-checkbox-label) {
    color: #4e5969;
  }

  :deep(.arco-list-bordered) {
    border: 0;
  }

  :deep(
      .arco-list-medium
        .arco-list-content-wrapper
        .arco-list-content
        .arco-list-item
    ) {
    padding: 0 0 12px 0;
    border: 0;
    border-bottom: 1px solid var(--color-neutral-3);
  }

  :deep(.arco-picker),
  :deep(.arco-input-tag),
  :deep(.arco-select-view-single),
  :deep(.arco-textarea),
  :deep(.arco-form-item-content-wrapper) {
    background-color: #fff;
    border-radius: 8px;
  }

  :deep(.arco-form-item-content-wrapper),
  :deep(.arco-input-wrapper),
  :deep(.arco-select-view:not(:last-child)) {
    border: 1px solid #c9cdd4 !important;
  }

  :deep(.arco-select-view),
  :deep(.arco-input-wrapper) {
    border-radius: 8px !important;
    background-color: #fff;
  }

  .node-input-group {
    width: 100%;
    margin: 10px 0 0 0;
    :deep(.arco-picker) {
      border: 1px solid #c9cdd4;
    }
  }

  :deep(.arco-textarea-wrapper) {
    border-radius: 8px;
  }

  :deep(.arco-textarea[disabled]) {
    // color: #1d2129;
    -webkit-text-fill-color: unset;
    cursor: not-allowed !important;
  }

  :deep(.arco-textarea) {
    min-height: 74px;
    border: 1px solid #c9cdd4;
  }

  // 多项选择器
  // :deep(
  //     .arco-select-view-multiple.arco-select-view-disabled .arco-select-view-tag
  //   ) {
  //   // color: #1d2129;
  //   background-color: #ffffff;
  // }
  :deep(.arco-select-view-multiple.arco-select-view-disabled:hover) {
    background-color: inherit;
    cursor: default;
  }
  :deep(.arco-select-view-disabled .arco-select-view-icon svg) {
    visibility: hidden;
  }

  // 单项选择器
  :deep(.arco-select-view-single.arco-select-view-disabled) {
    cursor: default;
    // color: #1d2129;
  }

  :deep(.arco-select-view-single.arco-select-view-disabled:hover) {
    background-color: inherit;
  }

  // 日期选择器
  :deep(.arco-picker input) {
    color: #1d2129;
  }

  // 查看事项特殊样式
  :deep(.hide-suffix-icon .arco-picker-suffix-icon) {
    visibility: hidden;
  }
  // :deep(.arco-picker input[readonly]) {
  //   color: #86909c;
  // }

  .close-icon {
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
  }
  :deep(.rate-progress .arco-form-item-content-wrapper) {
    border: none !important;
  }
  :deep(.rate-progress .arco-form-item-content) {
    padding-left: 10px;
  }
  :deep(.rate-progress .arco-slider-track:before) {
    height: 6px;
    border-radius: 6px;
  }
  :deep(.arco-slider-bar) {
    height: 6px;
    border-radius: 6px;
  }
</style>
