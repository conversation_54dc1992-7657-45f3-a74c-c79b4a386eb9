<template>
  <a-collapse-item
    :key="file.key"
    :header="file.name"
    style="background: var(--color-neutral-2)"
  >
    <template #expand-icon="{ active }">
      <icon-caret-up v-if="active" />
      <icon-caret-down v-else />
    </template>
    <template #extra>
      <a-tag size="small"
        >{{ getCounts(file) }} {{ $t('check.file-number') }}</a-tag
      >
    </template>
    <template v-if="file?.children?.length">
      <template v-for="item in file.children" :key="`${item.id}-${file.id}`">
        <a-collapse
          v-if="item.isFileOrFolder === 0 && item?.children?.length"
          expand-icon-position="right"
          :show-expand-icon="true"
          style="margin-top: 4px"
        >
          <FileCollapseItem
            :file="item"
            :source-file="sourceFile"
            @file-change="fileChange"
          ></FileCollapseItem>
        </a-collapse>
        <div v-if="item.isFileOrFolder === 1" class="file-item">
          <file-image
            :file-name="item.name"
            :is-file="item.isFileOrFolder === 0 ? false : true"
            style="
              width: 40px;
              height: 40px;
              border-radius: 4px;
              margin-top: 6px;
            "
          />
          <div class="file-text">
            <div class="file-name">{{ item.name }}</div>
            <span class="file-version">{{ 'V' + item.version }}</span>
          </div>
          <span class="icon-delete" @click="delFile(item)"
            ><icon-delete size="18"
          /></span>
        </div>
      </template>
    </template>
  </a-collapse-item>
</template>

<script lang="ts" setup>
  import { isEmpty } from 'lodash';
  import { getDirectoryFileCounts } from '../../api';
  import FileImage from '../image-file.vue';

  const props = defineProps({
    file: {
      type: Object,
      default() {
        return {};
      },
    },
    sourceFile: {
      type: Object,
      default() {
        return {};
      },
    },
  });

  const emits = defineEmits(['fileChange']);
  const fileChange = (file: any) => {
    emits('fileChange', file);
  };

  // 帮我写一个根据file的id，从props.sourceFile中找到file的父级
  const findParent = (fileId: any, fatherFile: any) => {
    let parent = {};
    fatherFile.children.forEach((item: any) => {
      if (item.id === fileId) {
        parent = fatherFile;
      } else if (item.children && item.children.length) {
        const find = findParent(fileId, item);
        parent = isEmpty(find) ? parent : find;
      }
    });
    return parent;
  };

  // 帮我写一个递归 删除props.sourceFile中的file,如果父级的children为空了，也要删除父级
  // 1. 找到父级
  // 2. 删除父级的children中的file
  // 3. 如果父级的children为空了，也要删除父级
  // 4. 递归
  const delParent = (file: any) => {
    const parent = findParent(file.id, props.sourceFile) as any;
    // 如果parent是空对象
    if (isEmpty(parent)) {
      fileChange(file);
      return;
    }
    const index = parent?.children.findIndex((item: any) => {
      return item.id === file.id;
    });
    if (index !== -1) parent?.children.splice(index, 1);
    if (!parent.children.length) {
      delParent(parent);
    }
  };

  const delFile = (file: any) => {
    delParent(file);
  };

  const getCounts = (file: any) => {
    return getDirectoryFileCounts(file);
  };

  defineExpose({
    props,
  });
</script>

<style scoped lang="less">
  ::v-deep .arco-collapse-item-header {
    background-color: var(--color-neutral-2);
  }
  .file-item {
    margin-top: 4px;
    height: 56px;
    border-bottom: 1px solid var(--color-border);
    position: relative;
    .file-text {
      float: right;
      width: calc(100% - 75px);
      //border: 1px solid red;
      margin-right: 20px;
      margin-top: 2px;
      .file-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 22px;
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN, serif;
        font-weight: 400;
        color: var(--color-text-1);
        line-height: 22px;
      }
      .file-version {
        //height: 20px;
        background: #e8fffb;
        border-radius: 4px;
        opacity: 1;
        border: 1px solid #0fc6c2;
        //padding: 4px;
        padding-left: 6px;
        padding-right: 6px;
        color: #0fc6c2;
        font-size: 12px;
        margin-top: 4px;
        line-height: 20px;
      }
    }
    .icon-delete {
      position: absolute;
      right: 0;
      top: 18px;
      cursor: pointer;
    }
  }
</style>
