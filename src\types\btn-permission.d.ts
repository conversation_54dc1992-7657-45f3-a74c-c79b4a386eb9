namespace Permission {
  interface Btn {
    home: {
      createProject: string;
      deleteProject: string;
    };
    system: {
      personalInfo: string;
      securitySettings: string;
      groupMember: string;
      organization: string;
      permissions: string;
      roles: string;
      projectTemplate: string,
    };
    project: {
      editProject: string;
      createMilestone: string;
      editMilestone: string;
      deleteMilestone: string;
      createMilestoneTask: string;
      updateMilestoneTask: string;
      deleteMilestoneTask: string;
      addProcess: string;
      editProcess: string;
      deleteProcess: string;
      sharing: string;
      delivery: string;
    };
    file: {
      upload: string;
      download: string;
      shared: string;
      move: string;
      delete: string;
      renameFolder: string;
      addFolder: string;
      editFile: string;
      reconvert: string;
      abandonFile: string;
    };
    team: {
      addMember: string;
      editTeam: string;
      deleteTeam1: string;
      deleteTeam2: string;
      addTeam: string;
      addSubTeam: string;
      updateMember: string;
      deleteMember: string;
      removeMember: string;
      importMember: string;
      exportMember: string;
      invitateMember: string;
      subordination: string;
    };
  }
}
