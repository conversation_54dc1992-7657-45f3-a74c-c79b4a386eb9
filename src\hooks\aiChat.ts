import { ref } from 'vue';
import { Message } from '@arco-design/web-vue';
import { storeToRefs } from 'pinia';
import { useAiStore } from '@/store';
import { getAiToken } from '@/views/dashboard/api';

export default function useAIChat() {
  const aiChatLink = ref('');

  const aiStore = useAiStore();
  const { aiInstance } = storeToRefs(aiStore);

  const genAILink = async (
    agentId: string,
    recordId?: string,
    developer: any,
    appId: any
  ) => {
    aiChatLink.value = '';
    let link = `https://c4ai.ccccltd.cn/agent/pagentplus/${agentId}/${
      recordId || ''
    }`;
    // let link = `https://cdex.ccccltd.cn/xkpro/agent/pagentplus/${agentId}/${
    //   recordId || ''
    // }`;
    const result = await getAiToken();

    if (!result.status || !result.data) {
      Message.error('获取AI token失败');
    } else {
      link += `?token=${result.data}`;
      // eslint-disable-next-line no-unused-expressions
      developer ? (link += `&developer=${developer}`) : '';
      // eslint-disable-next-line no-unused-expressions
      appId ? (link += `&appId=${appId}`) : '';
      aiChatLink.value = link;
    }
  };

  const knowledgeBaseChatLink = ref('');
  const genKnowledgeBaseAILink = async (agentId: string, recordId?: string) => {
    knowledgeBaseChatLink.value = '';
    let link = `https://c4ai.ccccltd.cn/xk/xkassistant/${agentId}/${
      recordId || ''
    }`;
    // let link = `https://cdex.ccccltd.cn/xkpro/xk/xkassistant/${agentId}/${
    //   recordId || ''
    // }`;
    const result = await getAiToken();
    if (!result.status || !result.data) {
      Message.error('获取AI token失败');
    } else {
      if (
        !localStorage.getItem('aitoken') ||
        result.data !== localStorage.getItem('aitoken')
      ) {
        // 如果token不存在或者不一致，则url需携带token
        localStorage.setItem('aitoken', result.data);
        link += `?token=${result.data}&agentId=${agentId}&placeholder=您可以基于知识库的内容提问`;
      }
      knowledgeBaseChatLink.value = `${link}${
        link.includes('placeholder')
          ? ''
          : '?placeholder=您可以基于知识库的内容提问'
      }`;
    }
  };
  const genKnowledgeBaseAILinkNoToken = (
    agentId: string,
    recordId?: string
  ) => {
    knowledgeBaseChatLink.value = '';
    const link = `https://c4ai.ccccltd.cn/xk/xkassistant/${agentId}/${
      recordId || ''
    }?placeholder=您可以基于知识库的内容提问`;
    knowledgeBaseChatLink.value = link;
  };

  /* sdk方式引入AI begin */
  // 获取AI token
  const genAIToken = async () => {
    const result = await getAiToken();
    if (!result.status || !result.data) {
      Message.error('获取AI token失败');
      console.error('获取AI token失败', result);
    } else if (
      !localStorage.getItem('aitoken') ||
      result.data !== localStorage.getItem('aitoken')
    ) {
      // 如果token不存在或者不一致，更新
      localStorage.setItem('aitoken', result.data);
    }
    // console.log('获取token完成');
  };

  // 初始化AI页面
  const initAIPage = (
    agentId: string,
    chatType: string,
    domContainer: HTMLElement | null,
    initFunction: any,
    placeholder = '',
    message = ''
  ) => {
    // console.log('初始化AI页面，agentId：', agentId);
    if ((window as any).xkForAichatsdk) {
      // (window as any).currentId = agentId;
      const params = {
        clientId: 'CDex', // 项目名称
        accessAiToken: localStorage.getItem('aitoken'),
        api: '/aiapi',
        type: chatType, // 对话类型：xkassistant（智能体）/chat（通用对话）
        onMounted: (router: any) => {
          if (
            chatType === 'xkassistant' &&
            initFunction &&
            typeof initFunction === 'function'
          ) {
            initFunction(router);
          }
        },
        container: domContainer,
        placeholder,
        message,
        isMenu: false, // 是否显示历史记录
        isUserFeedback: false, // 是否显示右上角反馈
        isUser: false, // 是否显示右上角人员信息
        currentId: agentId, // 当前智能体ID
      };
      console.log('ai 参数：', params);
      // if (aiInstance.value) {
      //   console.log('----updateOptions');
      //   aiInstance.value.updateOptions(params);
      // } else {
      //   console.log('----new');
      // eslint-disable-next-line new-cap
      aiInstance.value = new (window as any).xkForAichatsdk(params);
      // }
      return { status: true, message: 'success' };
    }
    return { status: false, message: 'AI sdk加载失败' };
  };

  // 设置当前选中历史记录
  const setSelectedRecordSession = (
    chatType: string,
    agentId: string,
    recordId: string,
    placeholder: string
  ) => {
    sessionStorage.setItem(
      'xkForAiDetailUrl',
      `/${chatType}/${agentId}/${recordId}`
    );
    // const router = aiInstance.value?.sdkRouter();
    // if (router) {
    //   debugger;
    //   router.push({
    //     path: `/${chatType}/${agentId}/${recordId}`,
    //     query: {
    //       placeholder,
    //     },
    //   });
    // }
  };

  // 清空当前选中历史记录
  const clearSelectedRecordSession = () => {
    sessionStorage.removeItem('xkForAiDetailUrl');
  };
  /* sdk方式引入AI end */

  // 测试环境知识库
  // const genKnowledgeBaseAILinkTest = async (
  //   agentId: string,
  //   recordId?: string
  // ) => {
  //   knowledgeBaseChatLink.value = '';
  //   const link = `http://************:10001/xk/xkassistant/oa1ee670habm0v1vlp3f/${
  //     recordId || ''
  //   }`;

  //   console.log('genKnowledgeBaseAILink: ', link);
  //   knowledgeBaseChatLink.value = link;
  // };

  return {
    aiChatLink,
    genAILink,
    knowledgeBaseChatLink,
    genKnowledgeBaseAILink,
    // genKnowledgeBaseAILinkTest,
    genKnowledgeBaseAILinkNoToken,
    genAIToken,
    initAIPage,
    setSelectedRecordSession,
    clearSelectedRecordSession,
  };
}
