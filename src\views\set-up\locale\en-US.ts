export default {
  'menu.setup': 'Settings',
  'setup.menu.personalInfo': 'Personal Info',
  'setup.menu.securitySettings': 'Security Settings',
  'setup.menu.memberManagement': 'Member Management',
  'setup.menu.groupManagement': 'Group Management',
  'setup.menu.groupMember': 'Group Member',
  'setup.menu.organization': 'Organization',
  'setup.menu.user': 'User Manage',
  'setup.menu.role': 'Role Manage',
  'setup.menu.permission': 'Permission Manage',
  'setup.logout': 'Logout',
  'setup.frequent.request.message': "It's too frequent, try again later.",
  'menu.user.setting': 'User Setting',
  'userSetting.title.baseinfo': 'Base Info',
  'userSetting.title.editPwd': 'Change Password',
  'userSetting.title.editPhone': 'Change Phone',
  'userSetting.title.editEmail': 'Change Email',
  'userSetting.title.bind-Phone': 'Bind phone Settings',
  'userSetting.title.bind-Email': 'Bind email address',
  'userSetting.form.oldPwd': 'Current Password',
  'userSetting.form.oldPwd.required': 'Current Password is required',
  'userSetting.form.newPwd': 'New Password',
  'userSetting.form.newPwd.required': 'New Password is required',
  'userSetting.form.enterPwd': 'Confirm Password',
  'userSetting.form.enterPwd.required': 'Confirm Password is required',
  'userSetting.form.placeholder.common': 'Please enter',
  'userSetting.save': 'Save',
  'userSetting.reset': 'Reset',
  'userSetting.password.edit.error': 'The two passwords entered do not match',
  'userSetting.password.edit.success': 'Password changed successfully',
  'userSetting.form.phone': 'Phone number',
  'userSetting.form.phone-new': 'New Phone number',
  'userSetting.form.captcha': 'Verification code',
  'userSetting.phone.edit.success': 'Phone number changed successfully',
  'userSetting.form.email': 'Email',
  'userSetting.form.email.required': 'Email is required',
  'userSetting.form.email.error': 'Email format is incorrect',
  'userSetting.email.edit.success': 'Email changed successfully',
  'userSetting.email.equity': 'Product permission application',
  'userSetting.password-validation':
    'The password must contain at least one lowercase letter, one uppercase letter, one number, and one special character, such as $ % ? ^ () = + ,.; :, etc.) and must be at least 8 characters in length',
  'userSetting.password-rule-error':
    'The password does not comply with the rules',
  // Member Management
  'member.management.title': 'Member Management',
  'member.management.add': 'Add Member',
  'member.management.name': 'Name',
  'member.management.account': 'Account',
  'member.management.role': 'Role',
  'member.management.department': 'Department',
  'member.management.status': 'Status',
  'member.management.operation': 'Operation',
  'member.management.edit': 'Edit',
  'member.management.delete': 'Delete',
  // Security Settings
  'security.settings.title': 'Security Settings',
  'security.settings.password': 'Change Password',
  'security.settings.oldPassword': 'Old Password',
  'security.settings.newPassword': 'New Password',
  'security.settings.confirmPassword': 'Confirm Password',
  'security.settings.phone': 'Bind Phone',
  'security.settings.email': 'Bind Email',
  'security.settings.twoFactor': 'Two-Factor Authentication',
  'security.settings.loginHistory': 'Login History',
  // Member Management additions
  'member.management.group': 'Group',
  'member.management.create': 'Create',
  'member.management.members': 'Members',
  'member.management.invite': 'Invite More Members',
  'member.management.organization': 'Organization',
  'member.management.external': 'External User',
  'member.management.remove': 'Remove',
  'member.management.confirm.remove': 'Confirm Remove',
  'member.management.confirm.remove.message':
    'Are you sure to remove {name} from the group?',
  'member.management.confirm.delete': 'Confirm Delete',
  'member.management.confirm.delete.message':
    'Are you sure to delete {teamName} group?',
  'member.management.add.group': 'Add New Group',
  'member.management.edit.group': 'Modify the group',
  'member.management.group.name': 'Group Name',
  'member.management.group.name.required': 'Group name is required',
  'member.management.group.name.placeholder': 'Please enter group name',
  'member.management.selected.members': 'Selected Members',
  'member.management.group.save.user.failed': 'Create a group first please!',
  'member.management.avatar': 'Avatar',
  'member.management.email': 'Email',
  'member.management.organizationType': 'Organization Type',
  'member.management.index': 'Index',
  'member.management.user': 'User',
  'common.cancel': 'Cancel',
  'common.confirm': 'Confirm',
  // Personal Information
  'personal.info.title': 'Personal Information',
  'personal.info.fullname': 'Full Name',
  'personal.info.fullname.placeholder': 'Please enter your full name',
  'personal.info.role': 'Role',
  'personal.info.role.placeholder': 'Please select your role',
  'personal.info.email': 'Email',
  'personal.info.email.placeholder': 'Please enter your email',
  'personal.info.phone': 'Phone',
  'personal.info.phone.placeholder': 'Please enter your phone number',
  'personal.info.language': 'Language',
  'personal.info.language.placeholder': 'Please select your language',
  'personal.info.language.zh': 'Chinese',
  'personal.info.language.en': 'English',
  'personal.info.account': 'CCCC Account',
  'personal.info.account.placeholder': 'Please bind your CCCC account',
  'personal.info.account.unbind': 'No CCCC account bound',
  'personal.info.reset': 'Reset',
  'personal.info.save': 'Save Settings',
  'personal.info.role.required': 'Please select a role type!',
  // Personal information related error prompts
  'personal.info.role.load.failed': 'Failed to load role list',
  'personal.info.avatar.upload.failed': 'Failed to upload avatar',
  'personal.info.update.failed': 'Failed to update personal information',
  'personal.info.submit.failed': 'Submission failed, please try again later',
  'personal.info.avatar.format.error':
    'Please upload a valid image format (jpg, jpeg, png supported)',
  // Login form related error prompts
  'login.form.captchaSent': 'Verification code sent successfully',
  'login.form.captchaFailed': 'Failed to send verification code',
  'login.form.captchaHoldOn': 'Please try again later',
  // Member management related error prompts
  'member.management.group.load.failed': 'Failed to load group list',
  'member.management.members.load.failed': 'Failed to load member list',
  'member.management.save.failed': 'Save failed',
  'member.management.data.load.failed': 'Failed to load data',
  'member.management.group.create.failed': 'Failed to create group',
  'member.management.group.update.failed': 'Failed to update group',
  'member.management.remove.success': 'Successfully removed from group',
  'member.management.delete.success': 'Successfully deleted group',
  'member.management.remove.failed': 'Failed to remove member',
  // Security settings related error prompts
  'userSetting.password.edit.failed': 'Failed to change password',

  'userSetting.jiao-jian-authority': 'Jiaojian Pass Authority',
  'userSetting.project-members':
    'Allow project members to send a notification to me',
  'userSetting.all-members': 'Everyone is allowed to send notifications to me',
  'userSetting.specified-user':
    'Allow designated users to send notifications to me',
};
