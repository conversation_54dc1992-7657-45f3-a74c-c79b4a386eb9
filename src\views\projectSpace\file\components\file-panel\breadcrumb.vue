<template>
  <a-breadcrumb :max-count="4">
    <template #separator>
      <icon-right />
    </template>
    <a-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="item.id">
      <a-tooltip :content="item.name" mini>
        <div class="breadcrumb-item" @click="handleClick(item, index)">{{
          item.name
        }}</div>
      </a-tooltip>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import useFileStore from '@/store/modules/file/index';
  import { getFolderById } from '@/store/modules/file/utils';
  import { defineEmits, watch } from 'vue';
  import { useI18n } from 'vue-i18n';

  const fileStore = useFileStore();

  const { locale } = useI18n();
  const { breadcrumbList, allTreeData } = storeToRefs(fileStore);
  const { setBreadcrumbList } = fileStore;

  watch(
    () => locale.value,
    () => {
      setBreadcrumbList(fileStore.currentFolder);
    }
  );

  const emits = defineEmits(['expendFolder', 'init']);

  function handleClick(item: { name: string; id: string }, index: number) {
    if (index === breadcrumbList.value.length - 1) return;
    const folder = getFolderById(allTreeData.value, item.id);
    if (folder.id) {
      emits('expendFolder', folder);
    } else {
      const list = breadcrumbList.value.slice(0, index + 1);
      const newPath = list.map((path) => path.id).join('/');
      fileStore.setCurrentIdPath(`/${newPath}`);
      emits('init');
    }
  }
</script>

<style lang="less" scoped>
  .breadcrumb-item {
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100px;
  }
  :deep(.arco-breadcrumb-item:last-child) {
    font-weight: 600;
  }
  :deep(.arco-breadcrumb-item-separator) {
    margin: 0;
  }
</style>
