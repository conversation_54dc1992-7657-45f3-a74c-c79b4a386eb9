<template>
  <div class="knowledge-base">
    <div class="header">
      <!-- <a-space size="medium">
        <icon-menu-unfold
          v-if="showMenu"
          :size="24"
          class="btn"
          @click="showMenu = false"
        />
        <icon-menu-fold
          v-else
          :size="24"
          class="btn"
          @click="showMenu = true"
        />
        <span></span>
      </a-space> -->
      <commonTabs v-model="tabKey" :tabs="[]"></commonTabs>
      <!-- <columnLine class="btn" @click="handleShowAIDialog" /> -->
      <div class="button-a" @click="handleShowAIDialog">
        <img src="@/assets/images/ai.gif" alt="" />
        {{ $t('knowledge.ai-assistant') }}
      </div>
    </div>
    <div class="main">
      <knowledge-base-tree v-if="showNavigation" />

      <div v-if="!showAIDialog" class="big-list-container">
        <BigFileList @send-question="handleSendQuestion" />
        <foldLeft
          v-if="showNavigation"
          class="fold-btn btn"
          @click="showNavigation = !showNavigation"
        />
        <foldRight
          v-else
          class="fold-btn btn"
          @click="showNavigation = !showNavigation"
        />
      </div>
      <template v-else>
        <div class="middle-panel">
          <div style="flex: 3; overflow: hidden">
            <SmallFileList :show-table="false" />
          </div>

          <div style="padding: 0 20px">
            <a-divider style="margin: 0 0 16px" />
          </div>
          <div style="flex: 2; padding: 0 20px; overflow: hidden">
            <div class="ai-header">
              <aiAhat />
              <span class="title">{{ $t('knowledge.ai-chat') }}</span>
            </div>
            <a-button type="outline" long size="small" @click="handleAddRecord">
              <template #icon> <icon-plus /> </template
              >{{ $t('knowledge.add-chat') }}</a-button
            >
            <a-spin
              :loading="aiLoading"
              style="width: 100%; height: calc(100% - 88px)"
            >
              <a-space
                v-if="!recordList.length"
                :size="16"
                direction="vertical"
                class="empty-container"
              >
                <EmptyFolder />
                <span>{{ $t('knowledge.no-result') }}</span>
              </a-space>
              <div class="record-list">
                <div
                  v-for="(item, index) in recordList"
                  :key="item.id"
                  :class="[
                    'record-card',
                    selectedRecord?.id === item.id ? 'record-card-select' : '',
                  ]"
                  @click="changeRecord(item)"
                  @mouseenter="handleMouseEnter(item)"
                  @mouseleave="handleMouseLeave(item)"
                >
                  <div class="time"
                    >{{ item.updateDate }}
                    <a-space
                      v-if="
                        getRecordUIState(item.id).showOptions &&
                        !getRecordUIState(item.id).isEditing
                      "
                      :size="12"
                    >
                      <icon-edit
                        :size="16"
                        @click.stop="handleEditRecord(item, index)"
                      />
                      <a-popconfirm
                        :content="$t('knowledge.confirm-delete-tips')"
                        @ok="handleDeleteRecord(item)"
                        @popup-visible-change="popupVisibleChange($event, item)"
                        @click.stop
                      >
                        <icon-delete :size="16" />
                      </a-popconfirm>
                    </a-space>
                    <a-space
                      v-if="getRecordUIState(item.id).isEditing"
                      :size="12"
                    >
                      <icon-check
                        :size="16"
                        @click.stop="handleRenameRecord(item)"
                      />
                      <icon-close
                        :size="16"
                        @click.stop="handleCancelRename(item)"
                      />
                    </a-space>
                  </div>
                  <div class="title">
                    <a-typography-paragraph
                      :ellipsis="{
                        rows: 1,
                        showTooltip: true,
                      }"
                    >
                      <span v-show="!getRecordUIState(item.id).isEditing">{{
                        item.chatSessionName
                      }}</span>
                    </a-typography-paragraph>

                    <div v-show="getRecordUIState(item.id).isEditing">
                      <a-input
                        ref="editInputRef"
                        v-model="item.chatSessionName"
                        :max-length="25"
                        show-word-limit
                        size="small"
                        @click.stop
                      />
                    </div>
                  </div>
                </div>
              </div>
            </a-spin>
          </div>

          <foldLeft
            v-if="showNavigation"
            class="fold-btn btn"
            @click="showNavigation = !showNavigation"
          />
          <foldRight
            v-else
            class="fold-btn btn"
            @click="showNavigation = !showNavigation"
          />
        </div>
        <div class="dialog-container">
          <iframe
            v-if="knowledgeBaseChatLink"
            ref="aiChatIframe"
            :src="knowledgeBaseChatLink"
            frameborder="0"
            height="100%"
            width="100%"
          ></iframe>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch, nextTick } from 'vue';
  import { storeToRefs } from 'pinia';
  import { Message } from '@arco-design/web-vue';
  import { useKnowledgeBaseStore } from '@/store';
  import KnowledgeBaseTree from './components/knowledge-base-tree.vue';
  import BigFileList from './components/big-file-list.vue';
  import SmallFileList from './components/small-file-list.vue';
  // import AIRecordList from './components/ai-record-list.vue';
  import useAIChat from '@/hooks/aiChat';
  import {
    getAgentRecordList,
    renameAgentRecord,
    deleteAgentRecord,
  } from '@/views/dashboard/api';
  import { ChatHistoryRecord } from './types';
  import useAIRecordUIState from './composables/useAIRecordUIState';
  import columnLine from '@/assets/images/dashboard/column-line.svg';
  import foldLeft from '@/assets/images/dashboard/fold-left.svg';
  import foldRight from '@/assets/images/dashboard/fold-right.svg';
  import aiAhat from '@/assets/images/knowledge-base/ai-chat.svg';
  import EmptyFolder from '@/assets/images/knowledge-base/empty-folder.svg';
  import commonTabs from '@/components/common-tabs/index.vue';
  import i18n from '@/locale/index';

  const tabKey = ref('knowledgeBase');

  const knowledgeBaseStore = useKnowledgeBaseStore();
  const { hasFile, baseRagId, isSearch } = storeToRefs(knowledgeBaseStore);

  const { getRecordUIState, clearRecordUIState } = useAIRecordUIState();

  const showMenu = ref(true);

  const { t } = i18n.global;

  // 是否展示知识库导航
  const showNavigation = ref(true);

  // 是否展示AI对话框
  const showAIDialog = ref(false);

  // 对话记录列表
  const recordList = ref<ChatHistoryRecord[]>([]);
  const aiLoading = ref(false);

  const { knowledgeBaseChatLink, genKnowledgeBaseAILink, genKnowledgeBaseAILinkNoToken } = useAIChat();

  // 查询历史对话
  const getHistoryRecord = () => {
    aiLoading.value = true;
    recordList.value = [];
    const params = {
      agentId: baseRagId.value,
      pageNo: 1,
      pageSize: 100,
    };
    getAgentRecordList(params)
      .then((res: any) => {
        if (res.status) {
          // console.log('recordList: ', res?.data?.list);
          recordList.value = res?.data?.list || [];
          clearRecordUIState();
        }
      })
      .finally(() => {
        aiLoading.value = false;
      });
  };

  const handleShowAIDialog = () => {
    if (!hasFile.value) {
      Message.warning(t('knowledge.knowledge-no-file-tips'));
      return;
    }
    if (!baseRagId.value) {
      Message.warning(t('knowledge.knowledge-creating-tips'));
      return;
    }
    showAIDialog.value = !showAIDialog.value;
    selectedRecord.value = '';
    if (showAIDialog.value) {
      genKnowledgeBaseAILink(baseRagId.value); // 获取AI对话链接
    }
    getHistoryRecord(); // 查询历史对话
  };

  watch(
    () => knowledgeBaseChatLink.value,
    (nValue: string) => {
      // console.log('chatLink change: ', knowledgeBaseChatLink.value);
      nextTick(() => {
        setupIframeHandler();
      });
    },
    {
      immediate: true,
    }
  );

  // 选中的对话
  const selectedRecord = ref();

  const changeRecord = (item: any) => {
    if (selectedRecord.value?.chatSessionId !== item.chatSessionId) {
      selectedRecord.value = item;
      genKnowledgeBaseAILinkNoToken(baseRagId.value, item.chatSessionId); // 获取AI对话链接
    }
  };

  // 新增对话
  const handleAddRecord = () => {
    // 去掉判断，直接生成链接，解决在空对话提问后，点击新增对话，无法切换到新对话的问题
    selectedRecord.value = undefined;
    genKnowledgeBaseAILink(baseRagId.value);
  };

  // 鼠标悬停事件处理
  const handleMouseEnter = (item: ChatHistoryRecord) => {
    const state = getRecordUIState(item.id);
    if (!state.showOptions) {
      state.showOptions = true;
    }
  };

  const handleMouseLeave = (item: ChatHistoryRecord) => {
    const state = getRecordUIState(item.id);
    if (!state.isEditing && !state.isDeleting) {
      state.showOptions = false;
    }
  };

  // 编辑对话名称input Ref
  const editInputRef = ref<Array<HTMLInputElement | null>>([]);

  // 修改编辑对话的处理函数
  const handleEditRecord = (item: ChatHistoryRecord, index: number) => {
    // console.log('item: ', item);
    const state = getRecordUIState(item.id);
    state.oldName = item.chatSessionName;
    state.isEditing = true;

    nextTick(() => {
      (editInputRef.value[index] as HTMLInputElement)?.focus();
    });
  };

  // 重命名
  const handleRenameRecord = async (item: ChatHistoryRecord) => {
    const state = getRecordUIState(item.id);
    state.isEditing = false;
    const res = await renameAgentRecord({
      chatSessionId: item.chatSessionId,
      chatSessionName: item.chatSessionName,
    });
    if (res.status) {
      Message.success(t('knowledge.rename-success'));
      getHistoryRecord(); // 查询历史对话
      selectedRecord.value = undefined;
      genKnowledgeBaseAILink(baseRagId.value);
    }
  };

  // 取消重命名
  const handleCancelRename = (item: ChatHistoryRecord) => {
    const state = getRecordUIState(item.id);
    state.isEditing = false;
    item.chatSessionName = state.oldName;
  };

  // 删除确认弹窗处理
  const popupVisibleChange = (visible: boolean, record: ChatHistoryRecord) => {
    const state = getRecordUIState(record.id);
    state.isDeleting = visible;
    if (!visible && !state.isEditing) {
      state.showOptions = false;
    }
  };

  // 删除对话
  const handleDeleteRecord = async (item: ChatHistoryRecord) => {
    const res = await deleteAgentRecord({
      chatSessionId: item.chatSessionId,
      chatSessionName: item.chatSessionName,
    });
    if (res.status) {
      Message.success(t('knowledge.delete-success'));
      getHistoryRecord(); // 查询历史对话
      // 删除的是当前对话，则切换到空白对话
      if (selectedRecord.value.chatSessionId === item.chatSessionId) {
        selectedRecord.value = undefined;
        genKnowledgeBaseAILink(baseRagId.value);
      }
    }
  };

  const aiChatIframe = ref<HTMLIFrameElement | null>(null);

  // 对话框输入框内容
  const question = ref('');

  // iframe加载完成，发送消息
  const setupIframeHandler = () => {
    if (aiChatIframe.value) {
      aiChatIframe.value.onload = () => {
        console.log('aiChatIframe load！');
        setTimeout(() => {
          console.log('send message: ', question.value);
          aiChatIframe.value?.contentWindow?.postMessage(
            {
              message: question.value || '',
              placeholder: '您可以基于知识库的内容提问'
            },
            'https://c4ai.ccccltd.cn'
          );
          question.value = '';

          // if(!selectedRecord.value && !question.value){
          //   aiChatIframe.value?.contentWindow?.postMessage(
          //     {
          //       message: '你好 请问你能干些什么',
          //       placeholder: '您可以基于知识库的内容提问'
          //     },
          //     'https://c4ai.ccccltd.cn'
          //   );
          // }
        }, 500);
      };
    }
  };

  const handleSendQuestion = (q: string) => {
    question.value = q;
    // console.log('question: ', question.value);
    showAIDialog.value = true;
    knowledgeBaseChatLink.value = '';
    nextTick(() => {
      genKnowledgeBaseAILink(baseRagId.value); // 获取AI对话链接
    });
    getHistoryRecord(); // 查询历史对话
  };

  watch(
    () => isSearch.value,
    (nValue) => {
      if (nValue) {
        showAIDialog.value = false;
      }
    },
    {
      immediate: true,
    }
  );

  onMounted(() => {});
</script>

<script lang="ts">
  export default {
    name: 'KnowledgeBase',
  };
</script>

<style scoped lang="less">
  .knowledge-base {
    background-color: #ffffff;
    border-radius: 16px 0 0 16px;
    padding: 16px 20px;
    border: none;
    display: flex;
    flex-direction: column;
    height: 100%;

    .header {
      margin: 0 0 24px 0px;
      height: 24px;
      display: flex;
      justify-content: space-between;

      span {
        font-weight: bold;
        font-size: 24px;
        color: #1d2129;
        line-height: 24px;
      }
    }

    .main {
      flex: 1;
      display: flex;
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      overflow: hidden;
    }

    .big-list-container {
      padding: 16px 0 20px;
      flex: 1;
      position: relative;
      overflow: hidden;
    }
  }

  .btn {
    color: #4e5969;
    cursor: pointer;
  }

  .button-a {
    width: 110px;
    height: 36px;
    border: 1px solid #d9d9d9;
    border-radius: 36px;
    background-color: #ffff;
    position: fixed;
    top: 92px;
    right: 20px;
    cursor: pointer;
    z-index: 100;
    display: flex;
    align-items: center;
    padding: 0 16px;
    color: #4e5969;
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }

  :deep(.has-pointer) {
    cursor: pointer;
  }

  .tree-box {
    height: calc(100% - 106px);
    overflow: auto;
  }

  .custom-node-title {
    color: #4e5969;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .root-title {
    line-height: 20px;
  }

  .leaf-title {
    font-size: 20px;
    font-weight: 500;
    line-height: 20px;
  }

  .middle-panel {
    position: relative;
    width: 320px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--color-neutral-3);
  }

  .ai-header {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    svg {
      margin-right: 8px;
    }
    .title {
      font-size: 20px;
      font-weight: 500;
      line-height: 24px;
      color: #1d2129;
    }
  }

  .empty-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    span {
      font-size: 14px;
      line-height: 24px;
      color: #4e5969;
    }
  }

  .record-list {
    margin-top: 4px;
    height: 100%;
    overflow: auto;
  }

  .record-card {
    margin-top: 4px;
    padding: 8px;
    height: 60px;
    cursor: pointer;

    .time {
      font-size: 14px;
      color: #86909c;
      line-height: 22px;
      display: flex;
      justify-content: space-between;
    }

    .title {
      font-size: 16px;
      color: #1d2129;
      line-height: 22px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .record-card:first-of-type {
    margin-top: 0;
  }

  .record-card-select {
    border-radius: 8px;
    background-color: #e8f2ff;
  }

  .fold-btn {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    margin: auto;
  }

  .dialog-container {
    flex: 1;
  }

  // 组件库样式覆盖
  :deep(.arco-tree-size-large) {
    .arco-tree-node-indent {
      display: inline-block;
      width: 16px;
    }
    .arco-tree-node-switcher {
      width: 18px;
      font-size: 18px;
    }
    .arco-tree-node-switcher {
      height: 40px;
    }
    .arco-tree-node-title {
      font-size: 16px;
      overflow: hidden;
    }
    .arco-tree-node-title {
      padding: 10px 0;
    }
  }

  :deep(.arco-tree-node-title-text) {
    margin-right: 2px;
    overflow: hidden;
  }

  :deep(.arco-tree-node) {
    margin: 4px 0;
  }
  :deep(.arco-tree-node:hover) {
    border-radius: 8px;
    background-color: var(--color-fill-2);
  }

  :deep(.arco-tree-node-selected) {
    border-radius: 8px;
    background-color: #e8f2ff;
    .custom-node-title {
      color: #1d2129;
    }
  }

  :deep(.arco-breadcrumb-item) {
    font-size: 16px;
    color: #86909c;
    line-height: 24px;
  }
  :deep(.arco-breadcrumb-item:last-of-type) {
    font-weight: 500;
    color: #1d2129;
  }

  :deep(.arco-btn-outline) {
    border-color: #a0c1ff;
    border-radius: 8px;
  }

  :deep(.arco-typography) {
    margin-bottom: 0;
    color: #4e5969;
  }

  :deep(.record-card-select) {
    .arco-typography {
      color: #1d2129;
    }
  }
</style>
