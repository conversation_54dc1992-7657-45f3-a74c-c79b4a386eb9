import type { HttpResponse } from '@/api/interceptor';
import axios from 'axios';

// 新增或编辑接口权限
export function addOrUpdateRequest(
  RequestDto: Permission.Api.RequestDto
): Promise<HttpResponse<null>> {
  return axios.post('/sys-system/request', RequestDto);
}

// 分页获取接口权限列表
export function getRequestList(
  params: string
): Promise<HttpResponse<GetPageRes<Permission.Api.PageRequestDto[]>>> {
  return axios.get('/sys-system/request/page', { params });
}

// 删除接口权限
export function deleteRequest(requestId: string): Promise<HttpResponse<null>> {
  return axios.delete('/sys-system/request', { params: { id: requestId } });
}

// 获取菜单下的按钮列表
export function getBtnList(
  menuId: string
): Promise<HttpResponse<Permission.Api.PageBtnDto[]>> {
  return axios.get('/sys-system/menu/buttons', { params: { menuId } });
}

// 根据ID获取按钮信息
export function getBtnInfo(
  btnId: string
): Promise<HttpResponse<Permission.Api.BtnDto>> {
  return axios.get('/sys-system/button', { params: { id: btnId } });
}

// 新增按钮权限
export function addBtn(
  BtnDto: Permission.Api.BtnDto
): Promise<HttpResponse<null>> {
  return axios.post('/sys-system/button', BtnDto);
}

// 更新按钮权限
export function updateBtnInfo(
  BtnDto: Permission.Api.BtnDto
): Promise<HttpResponse<null>> {
  return axios.put('/sys-system/button', BtnDto);
}

// 删除按钮权限
export function deleteBtn(btnId: string): Promise<HttpResponse<null>> {
  return axios.delete('/sys-system/button', { params: { id: btnId } });
}

// 获取菜单列表
export function getMenuList(
  parentId?: string
): Promise<HttpResponse<Permission.Api.PageMenuDto[]>> {
  return axios.get('/sys-system/menus', { params: { parentId } });
}

// 根据ID获取菜单信息
export function getMenuInfo(
  menuId: string
): Promise<HttpResponse<Permission.Api.MenuDto>> {
  return axios.get('/sys-system/menu', { params: { id: menuId } });
}

// 新增菜单权限
export function addMenu(
  menuDto: Permission.Api.MenuDto
): Promise<HttpResponse<null>> {
  return axios.post('/sys-system/menu', menuDto);
}

// 修改菜单权限
export function updateMenuInfo(
  menuDto: Permission.Api.MenuDto
): Promise<HttpResponse<null>> {
  return axios.put('/sys-system/menu', menuDto);
}

// 删除菜单权限
export function deleteMenu(menuId: string): Promise<HttpResponse<null>> {
  return axios.delete('/sys-system/menu', { params: { id: menuId } });
}
