<template>
  <div class="personal-base">
    <a-layout class="person-base-panel">
      <a-layout-sider
        :width="340"
        :resize-directions="['right']"
        class="file-tree-wrap"
      >
        <div class="tree-header">
          <TitleIcon style="margin-right: 8px" />
          <span>{{ t('knowledgenew.shared-base') }}</span>
          <a-space :size="12">
            <a-tooltip :content="t('knowledgenew.go-back')">
              <span>
                <BackIcon
                  style="cursor: pointer"
                  class="icon-hover-bg"
                  @click="goHome"
              /></span>
            </a-tooltip>
            <a-tooltip :content="t('knowledgenew.member')">
              <span>
                <MemberIcon
                  style="cursor: pointer"
                  class="icon-hover-bg"
                  @click="showMemberDialog = true"
                />
              </span>
            </a-tooltip>
            <a-tooltip :content="t('knowledgenew.share')">
              <span>
                <ShareIcon
                  v-if="hasSharePermission"
                  style="cursor: pointer"
                  class="icon-hover-bg"
                  @click="showShareDialog = true"
                />
              </span>
            </a-tooltip>
            <a-dropdown position="bl" @select="handleClickMoreBtn">
              <icon-more :size="16" style="color: #4e5969; cursor: pointer" />
              <template #content>
                <a-doption v-if="isOwner" value="edit">
                  <template #icon>
                    <icon-pen :size="16" style="color: #4e5969" />
                  </template>
                  <template #default>{{
                    t('knowledgenew.edit-info')
                  }}</template>
                </a-doption>
                <a-doption v-if="isOwner" value="permission">
                  <template #icon>
                    <RollIcon />
                  </template>
                  <template #default>{{
                    t('knowledgenew.permission-manage')
                  }}</template>
                </a-doption>
                <a-doption v-if="isOwner" value="delete">
                  <template #icon>
                    <icon-delete :size="16" style="color: #4e5969" />
                  </template>
                  <template #default>{{
                    t('knowledgenew.delete-base')
                  }}</template>
                </a-doption>
                <a-doption v-else value="quit">
                  <template #icon>
                    <icon-delete :size="16" style="color: #4e5969" />
                  </template>
                  <template #default>{{
                    t('knowledgenew.quit-base')
                  }}</template>
                </a-doption>
              </template>
            </a-dropdown>
          </a-space>
        </div>
        <div class="base-card">
          <div class="card-top">
            <img
              v-if="baseInfo?.picUrl"
              :src="
                '/work/api/sys-storage/download_image?f8s=' + baseInfo.picUrl
              "
            />
            <DefaultCover v-else />

            <div class="text-container">
              <a-typography-paragraph
                :ellipsis="{
                  rows: 1,
                  showTooltip: true,
                }"
                class="text-title"
                >{{ baseInfo?.name || '' }}
              </a-typography-paragraph>
              <div class="text-username-date">
                <span>{{
                  t('knowledgenew.created-by', {
                    name: baseInfo?.creatorName || '',
                  })
                }}</span>
                <span>{{ baseInfo?.updateDate?.slice(0, 10) }}</span>
              </div>
            </div>
          </div>

          <div v-if="baseInfo?.description" class="card-bottom">
            <a-typography-paragraph
              :ellipsis="{
                rows: 2,
                showTooltip: true,
              }"
              class="text-desc"
              >{{ baseInfo?.description || '' }}
            </a-typography-paragraph>
          </div>
          <a-divider
            :margin="0"
            style="margin-top: 16px; border-bottom: 1px solid #d9d9d9"
          />
        </div>
        <FileTree ref="fileTree" style="flex: 1"></FileTree>
      </a-layout-sider>
      <a-layout-content class="content-panel">
        <AIPanel v-model:refresh="refreshAI" />
      </a-layout-content>
    </a-layout>
    <MemeberDialog
      v-model:visible="showMemberDialog"
      :creator-username="baseInfo?.createBy"
    />
    <ShareBaseDialog
      v-model:visible="showShareDialog"
      :data="baseInfo as KnowledgeBaseRecord"
    />
    <BaseInfoDialog
      v-model:visible="showEditDialog"
      type="edit"
      :data="baseInfo as KnowledgeBaseRecord"
      @submit="handleUpdateBaseInfoSuccess"
    />
    <PermissionManageDialog
      v-model:visible="showPermissionDialog"
      :data="baseInfo as KnowledgeBaseRecord"
      @submit="getKnowledgeBaseInfo"
    />
    <a-modal
      v-model:visible="showDeleteDialog"
      :title="t('knowledgenew.prompt')"
      :width="400"
      :ok-text="t('knowledgenew.ok-text')"
      :mask-closable="false"
      :esc-to-close="false"
      :unmount-on-close="true"
      :ok-loading="deleteLoading"
      @ok="handleDelete"
      @cancel="showDeleteDialog = false"
    >
      <div>
        {{
          isOwner
            ? t('knowledgenew.delete-confirm-owner')
            : t('knowledgenew.delete-confirm-member')
        }}
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import { useI18n } from 'vue-i18n';
  import { Message } from '@arco-design/web-vue';
  import { useKnowledgeBaseStore2 } from '@/store';
  import FileTree from './file-tree.vue';
  import AIPanel from './ai-panel.vue';
  import MemeberDialog from './member-dialog.vue';
  import ShareBaseDialog from './share-base-dialog.vue';
  import BaseInfoDialog from './base-info-dialog.vue';
  import PermissionManageDialog from './permission-manage-dialog.vue';
  import { getBaseList, deleteBase, removeMember } from '../../api';
  import { KnowledgeBaseRecord } from '../../types';
  import TitleIcon from '@/assets/images/knowledge-base/title-icon.svg';
  import BackIcon from '@/assets/images/knowledge-base/back-icon.svg';
  import MemberIcon from '@/assets/images/knowledge-base/member-icon.svg';
  import ShareIcon from '@/assets/images/knowledge-base/share-icon.svg';
  import RollIcon from '@/assets/images/knowledge-base/roll-icon.svg';
  import DefaultCover from '@/assets/images/knowledge-base/default-cover.svg';

  const { t } = useI18n();

  const router = useRouter();
  const route = useRoute();
  const { kbId } = route.params; // 当前知识库ID

  const knowledgeBaseStore2 = useKnowledgeBaseStore2();
  const { baseInfo } = storeToRefs(knowledgeBaseStore2);

  const fileTree = ref();

  // 是否为知识库创建者
  const isOwner = computed(() => {
    return baseInfo.value?.owner;
  });

  // 是否有分享权限
  const hasSharePermission = computed(() => {
    return baseInfo.value?.owner || baseInfo.value?.share;
  });

  // 返回首页
  const goHome = () => {
    router.push({
      name: 'knowledgeBase',
    });
  };

  // 成员
  const showMemberDialog = ref(false);

  // 分享
  const showShareDialog = ref(false);

  // 修改资料
  const showEditDialog = ref(false);

  // 权限管理
  const showPermissionDialog = ref(false);

  // 删除/退出知识库
  const showDeleteDialog = ref(false);
  const deleteLoading = ref(false);
  const handleDelete = async () => {
    deleteLoading.value = true;
    try {
      if (isOwner.value) {
        const res = await deleteBase(baseInfo.value?.id || '');
        if (res.status) {
          Message.success(t('knowledgenew.delete-success'));
          goHome();
        }
      } else {
        const res = await removeMember({
          kbId: baseInfo.value?.id || '',
          userNames: [localStorage.getItem('work_user_name') || ''],
        });
        if (res.status) {
          Message.success(t('knowledgenew.quit-success'));
          goHome();
        }
      }
    } catch (err) {
      console.error(err);
    } finally {
      deleteLoading.value = false;
    }
  };

  // 选择下拉菜单
  const handleClickMoreBtn = (event: string) => {
    switch (event) {
      case 'edit':
        showEditDialog.value = true;
        break;
      case 'permission':
        showPermissionDialog.value = true;
        break;
      case 'delete':
        showDeleteDialog.value = true;
        break;
      case 'quit':
        showDeleteDialog.value = true;
        break;
      default:
        break;
    }
  };

  // 查询知识库信息
  const getKnowledgeBaseInfo = async () => {
    try {
      const res = await getBaseList({
        pageParam: { pageNo: 1, pageSize: 1000 },
      });

      if (res.status) {
        res.data.list?.find((base: any) => {
          if (base.id === kbId) {
            knowledgeBaseStore2.setBaseInfo(base);
            return true;
          }
          return false;
        });
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 是否刷新AI对话框
  const refreshAI = ref(false);

  // 更新知识库信息后事件
  const handleUpdateBaseInfoSuccess = (needRefreshAI: boolean) => {
    getKnowledgeBaseInfo();
    if (needRefreshAI) {
      refreshAI.value = true;
    }
  };

  onMounted(async () => {
    if (!baseInfo.value) {
      getKnowledgeBaseInfo();
    }
  });
</script>

<style scoped lang="less">
  .personal-base {
    flex: 1;
    overflow: hidden;
  }

  .tree-header {
    padding: 20px;
    height: 60px;
    display: flex;
    align-items: center;

    span {
      flex: 1;
      height: 20px;
      font-weight: 500;
      font-size: 18px;
      color: #1d2129;
      line-height: 20px;
    }
  }

  .base-card {
    margin-bottom: 16px;
    padding: 0 20px;
  }

  .card-top {
    display: flex;
    img,
    svg {
      margin-right: 12px;
      width: 60px;
      height: 60px;
      border-radius: 8px;
    }
  }

  .text-container {
    flex: 1;
    .text-title {
      margin-top: 8px;
      margin-bottom: 10px;
      height: 18px;
      font-weight: 500;
      font-size: 18px;
      color: #1d2129;
      line-height: 18px;
    }
    .text-username-date {
      display: flex;
      justify-content: space-between;
      color: #4e5969;
    }
  }

  .card-bottom {
    margin-top: 12px;
    height: 42px;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 20px;
  }

  .text-desc {
    margin-bottom: 0;
    font-size: 14px;
    color: #86909c;
    line-height: 21px;
  }

  .person-base-panel {
    border: 1px solid #d9d9d9;
    height: 100%;
    border-radius: 8px;
    .file-tree-wrap {
      border-radius: 8px;
      height: 100%;
      border-right: 1px solid #d9d9d9;
    }
    .content-panel {
      position: relative;
    }
  }

  .icon-hover-bg:hover {
    background: #e5e6eb;
    border-radius: 2px;
  }

  :deep(.arco-resizebox-trigger-icon-wrapper) {
    background-color: #eee;
  }
  :deep(.arco-layout-sider) {
    min-width: 340px;
    max-width: 600px;
  }

  :deep(.arco-layout-sider-children) {
    display: flex;
    flex-direction: column;
  }

  :deep(.arco-btn-size-small) {
    padding: 0 8px;
    border-radius: 4px;
    color: #3366ff;
  }
</style>
