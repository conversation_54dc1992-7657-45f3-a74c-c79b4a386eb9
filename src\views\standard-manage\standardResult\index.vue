<template>
  <div class="pageContainer">
    <div class="lefContent">
      <resultList
        v-if="modelFile.graphicEngineInfo"
        :viewers="viewers"
        :model-file="modelFile"
      />
    </div>
    <div class="rightContent">
      <div v-if="viewFlag" class="my-obv-viewer">
        <XBaseViewer
          v-if="engine === 'XBase' && modelFile && viewFlag"
          :model-file="modelFile"
          :viewer-type="viewerType"
          @get-x-base-instance="getBaseViewer"
        ></XBaseViewer>
      </div>

      <div class="detail">
        <div class="content-title">
          <div>
            <table-title
              style="display: inline-block; width: 100px"
              :title="$t('standard.detail')"
            ></table-title>
          </div>
        </div>
        <a-table
          :bordered="false"
          :columns="detailColumns"
          :data="resultDetail"
          height="210px"
          :scroll="{ y: '210px' }"
          :pagination="false"
        >
        </a-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import useStandardManageStore from '@/store/modules/standard-manage/index';
  import XBaseViewer from '@/views/bim-view/XBase/viewer.vue';
  import {
    getFileVersionInfoById,
    getFileInfoById,
  } from '@/views/bim-view/api';
  import resultList from './components/resultList.vue';
  import { useI18n } from 'vue-i18n';
  import TableTitle from '@/components/table-title/index.vue';

  const { t } = useI18n();

  const modelData: any = ref([]);
  const modelDetailData = ref([]);

  const viewFlag: any = ref(false);

  const modelFile: any = ref({});

  const viewers = ref<any>({
    baseViewer: {},
    compareViewer: {},
  });

  // 获取base大象云实例
  const getBaseViewer = (viewer: any, data: any) => {
    viewers.value.compareViewer = viewer;
  };

  const engine = ref('XBase');
  const viewerType = ref('');

  const route = useRoute();
  const fileName = ref('');

  const { query } = route;

  const detailColumns = computed<any[]>(() => [
    {
      title: t('standard.name'),
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: t('standard.result'),
      dataIndex: 'flag',
      slotName: 'flag',
      align: 'center',
    },
    {
      title: t('standard.detail'),
      dataIndex: 'propertyItemCheckInfo',
      // slotName: 'propertyItemCheckInfo',
      align: 'center',
    },
  ]);

  // 初始化模型视图
  const initView = async () => {
    viewFlag.value = false;
    // todo 此处暂只处理了大象云的文件逻辑，后续重构构力引擎时，需要做兼容处理
    const res = query.version
      ? await getFileVersionInfoById({
          id: query?.fileId,
          version: query.version || 1,
        })
      : await getFileInfoById(query?.fileId);
    if (res.status) {
      modelFile.value = res.data || '';
      fileName.value = res.data.name;
      setTimeout(() => {
        viewFlag.value = true;
      }, 100);
    }
  };

  const store = useStandardManageStore();
  const { resultDetail } = storeToRefs(store);

  const init = async () => {
    initView();
    [modelData.value] = modelDetailData.value;
  };

  onMounted(() => {
    init();
  });
</script>

<style scoped lang="less">
  .pageContainer {
    padding: 20px;
    background-color: #f5f6fb;
    display: flex;
    background-color: #f5f6fb;
    height: 100vh;
    // overflow: hidden;
    .lefContent {
      min-width: 440px;
      width: 440px;
      margin-right: 20px;
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
    }

    .rightContent {
      display: flex;
      flex: 1;
      flex-flow: column nowrap; /* 设置主轴为垂直方向 */
      box-sizing: border-box;

      .my-obv-viewer {
        flex: 1;
        border-radius: 8px;
        width: 100%;
        height: 500px;
        border-radius: 8px;
        overflow: hidden;
      }

      .detail {
        flex-direction: row;
        background-color: #fff;
        margin-top: 20px;
        padding: 20px;
        border-radius: 8px;
        .content-title {
          position: relative;
          margin-bottom: 16px;

          .btns {
            position: absolute;
            top: 0;
            right: 0;
          }

          .title-img {
            width: 20px;
            height: 20px;
          }

          .title-text {
            position: absolute;
            top: 0;
            left: 20px;
            display: inline-block;
            margin-left: 8px;
            color: #1d2129;
            font-weight: 600;
            font-size: 18px;
            line-height: 21px;
          }
        }
      }
      :deep(.arco-table-container) {
        height: 200px;
      }
    }
  }

  :deep(.table-title img) {
    vertical-align: middle;
  }
  :deep(#point) {
    display: none;
  }
</style>
