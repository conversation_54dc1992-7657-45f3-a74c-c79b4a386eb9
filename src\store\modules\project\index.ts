import { defineStore } from 'pinia';
import { Message } from '@arco-design/web-vue';
import { getProjectList } from '@/api/project';
import { ProjectState, ProjectListParams } from './types';
import i18n from '@/locale/index';

const { t } = i18n.global;

const useProjectStore = defineStore('project', {
  state: (): ProjectState => ({
    projectList: [],
    projectId: '',
  }),

  actions: {
    async setProjectList(params: ProjectListParams) {
      try {
        const res = await getProjectList(params);
        if (res.status) {
          this.projectList = res.data?.list || [];
        }
      } catch (err) {
        if (typeof err === 'string') {
          Message.error(err);
        }
      }
    },
  },
});

export default useProjectStore;
