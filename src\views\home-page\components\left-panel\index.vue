<template>
  <div class="container">
    <!-- 上面展示部分 -->
    <TopSection />
    <!-- 我的日程 -->
    <div class="schedule-list-wrapper">
      <scheduleList />
    </div>
  </div>
</template>

<script setup lang="ts">
  import TopSection from './top-section.vue';
  import scheduleList from './schedule-list.vue';
</script>

<style scoped lang="less">
  .container {
    display: flex;
    flex-direction: column; /* 垂直排列子元素 */
    flex: 1;
    overflow: hidden; /* 防止内容溢出 */
    margin-right: 20px;
  }
  .schedule-list-wrapper {
    height: calc(100vh - 442px);
    border: 1px solid #d9d9d9; /* Updated border color */
    border-radius: 8px 8px 8px 8px;
    margin-top: 20px;
    padding: 20px;
  }
</style>
