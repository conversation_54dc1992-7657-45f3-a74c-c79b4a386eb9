import { AppRouteRecordRaw } from '../types';

const PROJECTSETTING: AppRouteRecordRaw = {
  path: 'project-setting',
  name: 'project-setting',
  component: () => import('@/views/project-setting/index.vue'),
  meta: {
    locale: 'menu.project-setting',
    requiresAuth: true,
    allowProjectTemplate: true,
    icon: 'icon-settings',
    order: 100,
    showAI: true,
    globalMode: ['project'],
    // showTemplate: true, // 当是项目模版时是否展示
  },
};

export default PROJECTSETTING;
