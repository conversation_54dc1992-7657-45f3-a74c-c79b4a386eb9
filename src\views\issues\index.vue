<template>
  <div class="issues">
    <commonTabs v-model="tabKey" :tabs="[]"></commonTabs>
    <a-card class="general-card">
      <template #title>
        <a-row :gutter="16" style="margin-bottom: 16px">
          <a-col :flex="1">
            <table-title :title="$t('issues.issues')"></table-title>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="20">
            <div>
              <span class="search-title">{{ $t('issues.title') }}</span>
              <a-input
                v-model="searchParams.title"
                :style="{ width: '196px', marginLeft: '16px' }"
                :placeholder="$t('issues.please-enter')"
                allow-clear
                @search="search"
                @press-enter="search"
                @clear="search"
              />
            </div>
          </a-col>
          <a-col :span="4" style="text-align: right">
            <a-space :size="8">
              <a-button type="outline" @click="search">
                <template #icon> <icon-search /> </template
                >{{ $t('list.options.btn.search') }}</a-button
              >
              <a-button type="outline" @click="reset">
                <template #icon><icon-loop /> </template
                >{{ $t('list.options.btn.reset') }}</a-button
              >
            </a-space>
          </a-col>
        </a-row>
        <a-divider />
      </template>

      <a-row style="margin-bottom: 16px">
        <a-col>
          <a-space>
            <!-- <a-button type="outline" @click="exportAll"> 全部导出 </a-button> -->
            <a-button type="primary" @click="createIssuesDialog">
              {{ $t('issues.create-issues') }}
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-table
        v-table-height
        stripe
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="false"
        :scroll="scroll"
        :scrollbar="true"
        @page-change="onPageChange"
        @page-size-change="pageSizeChange"
      >
        <!-- <template #empty>
          <div class="list-empty" :style="'height:' + tableHeight + 'px'">
            <div class="empty-box">
              <img src="@/assets/images/file-manager/empty-file.png" />
              <div class="text">问题文件暂为空</div>
            </div>
          </div>
        </template> -->
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #title="{ record }">
          <a-link @click="issuesInfoView(record)"> {{ record.title }}</a-link>
        </template>
        <template #receivers="{ record }">
          {{
            record.issueRecipientList
              .map((item: any) => item.userName)
              .join('、')
          }}
        </template>
        <template #state="{ record }">
          <a-tag
            v-if="record.state == 0"
            color="red"
            bordered
            style="cursor: pointer"
            @click="detailTableShow(record)"
          >
            {{ $t('issues.unresolved') }}
          </a-tag>
          <a-tag
            v-if="record.state == 1"
            color="green"
            bordered
            style="cursor: pointer"
            @click="detailTableShow(record)"
          >
            {{ $t('issues.resolved') }}
          </a-tag>
          <a-tag
            v-if="record.state == 2"
            color="blue"
            bordered
            style="cursor: pointer"
            @click="detailTableShow(record)"
          >
            {{ $t('issues.processing') }}
          </a-tag>
        </template>
        <template #file="{ record }">
          <span
            style="cursor: pointer; color: #165dff"
            @click="clickNameHandle(record.issueFileList[0])"
            >{{
              record.issueFileList.map((item: any) => item.name).join('、')
            }}</span
          >
        </template>

        <template #publishStatus="{ record }">
          <a-switch
            v-model="record.publishStatus"
            :checked-value="1"
            :unchecked-value="0"
            size="small"
            :disabled="currentUser !== record.createBy && userStore.admin !== 0"
            @change="setStatus(record)"
          />
        </template>

        <template #operation="{ record }">
          <a-dropdown @select="(val) => downloadBcf(val, record)">
            <a-button type="text" size="small">{{
              $t('issues.download')
            }}</a-button>
            <template #content>
              <a-doption value="2.1">BCF 2.1</a-doption>
              <a-doption value="3.0" disabled>BCF 3.0</a-doption>
            </template>
          </a-dropdown>
          <a-button
            v-if="
              record.issueRecipientList.some(
                (recipient) => recipient.userId === userId
              )
            "
            type="text"
            size="small"
            @click="replyIssue(record)"
            >{{ $t('issues.reply-issue') }}
          </a-button>
        </template>
      </a-table>
    </a-card>
    <createIssues
      v-model:visible="createVisible"
      @refresh="updateData"
    ></createIssues>
    <issuesInfo
      v-model:visible="infoVisible"
      :details-info-row="detailsInfoRow"
      :reply-content="replyContent"
      @refresh="updateData"
    ></issuesInfo>
    <replyIssues
      v-model:visible="replyVisible"
      :reply-info-row="replyInfoRow"
      :reply-content="replyContent"
      @refresh="updateData"
    ></replyIssues>
    <detailTable
      v-model:visible="detailVisible"
      :detail-data="detailData"
    ></detailTable>

    <!-- 图片预览 -->
    <imgViewer
      v-if="imgViewModel.visible"
      :view-modal="imgViewModel"
    ></imgViewer>
  </div>
</template>

<script lang="ts" setup>
  import { useRoute } from 'vue-router';
  import { computed, ref, reactive, watch, provide } from 'vue';
  import useLoading from '@/hooks/loading';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import cloneDeep from 'lodash/cloneDeep';
  import { usePrjPermissionStore, useUserStore } from '@/store';
  import {
    queryissuesList,
    issuesData,
    issuesParams,
    queryissueLog,
    setPublishedStatus,
  } from './api';
  import createIssues from './components/create-issues.vue';
  import issuesInfo from './components/issues-info.vue';
  import replyIssues from './components/reply-issues.vue';
  import detailTable from './components/detailTable.vue';
  import GenerateIssueXML from '@/components/bcf/index';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import commonTabs from '@/components/common-tabs/index.vue';
  import TableTitle from '@/components/table-title/index.vue';
  import { getUserTeamsInPrj } from '@/views/projectSpace/home/<USER>';
  import { Message } from '@arco-design/web-vue';
  import modelViewBim from '@/utils/common/view';
  import imgViewer from '@/components/imgView/index.vue';
  import useFileStore from '@/store/modules/file/index';
  import { storeToRefs } from 'pinia';

  const fileStore = useFileStore();
  const imgViewModel = computed(() => fileStore.imgViewModal);
  const { hiddenSlot } = storeToRefs(fileStore);

  const tabKey = ref('issues');
  const currentUser = computed(() => userStore.username);

  const { t } = useI18n();
  // 国际化类型
  const { currentLocale } = useLocale();
  // 配置
  type Column = TableColumnData & { checked?: true };
  const { loading, setLoading } = useLoading(true);
  const scroll = {
    y: 'calc(100vh - 380px)',
  };

  // 数据
  const generateSearch = () => {
    return {
      title: '',
      // projectId: '',
    };
  };
  interface IssueLog {
    id: string;
    createDate: string;
    createUsername: string;
    picType: number;
    reply: string;
    picToken: string;
  }
  const route = useRoute();
  const { projectId }: any = route.params;
  const renderData = ref<issuesData[]>([]);
  const issueLogList = ref<IssueLog[]>([]);
  const createVisible = ref(false);
  const infoVisible = ref(false);
  const type = ref('add');
  const replyInfoRow = ref({});
  const detailsInfoRow = ref({});
  const replyVisible = ref(false);
  const detailVisible = ref(false);
  const searchParams = ref(generateSearch());
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);
  const detailData: any = ref([]);
  const replyContent = ref('');
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    total: 0,
  });
  if (route.query.issueItem) {
    infoVisible.value = true;
    detailsInfoRow.value = JSON.parse(route.query.issueItem);
  }
  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('issues.number'),
      dataIndex: 'index',
      slotName: 'index',
      width: 80,
    },
    {
      title: t('issues.title'),
      dataIndex: 'title',
      slotName: 'title',
      width: 320,
    },
    {
      title: t('issues.status'),
      dataIndex: 'state',
      slotName: 'state',
      align: 'left',
      width: 150,
    },
    {
      title: t('issues.sender'),
      dataIndex: 'creater',
      align: 'left',
    },
    {
      title: t('issues.recipient'),
      dataIndex: 'receivers',
      align: 'left',
      slotName: 'receivers',
    },
    {
      title: t('issues.creation-time'),
      dataIndex: 'createDate',
      align: 'left',
    },
    {
      title: t('issues.file'),
      dataIndex: 'file',
      slotName: 'file',
      align: 'left',
      ellipsis: true,
      tooltip: { position: 'left' },
      width: 200,
    },
    {
      title: '发布状态',
      dataIndex: 'publishStatus',
      align: 'left',
      slotName: 'publishStatus',
    },
    {
      title: t('issues.operation'),
      dataIndex: 'operation',
      align: 'left',
      slotName: 'operation',
    },
  ]);
  const fetchData = async (
    params: issuesParams = {
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      projectId,
    }
  ) => {
    setLoading(true);
    try {
      const { data } = await queryissuesList(params);
      renderData.value = data.list;
      pagination.total = data.total;
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  const teamList = ref<any[]>([]);
  provide('teamList', teamList);

  //  获取用户在当前项目下的teams和权限;
  const getTeamData = async () => {
    try {
      const res = await getUserTeamsInPrj(route.params.projectId as string);
      if (res.status) {
        teamList.value = res.data[0].teamList || [];
      }
    } catch (err) {
      console.log(err);
    } finally {
      // handle finally
    }
  };
  getTeamData();
  const userStore = useUserStore();
  // 当前用户id
  const userId = computed(() => {
    return userStore.cdeId;
  });

  const replyIssue = (record: any) => {
    getIssueLog(record.id);
    replyInfoRow.value = record;
    replyVisible.value = true;
  };

  // 操作记录
  const detailTableShow = async (record: any) => {
    try {
      const res = await queryissueLog(record.id);
      if (res.status) {
        detailData.value = res.data.list;
      }
    } catch (e) {
      console.log(e);
    }
    detailVisible.value = true;
  };
  const createIssuesDialog = () => {
    type.value = 'add';
    createVisible.value = true;
  };

  /**
   * 设置发布状态
   */
  const setStatus = async (data: any) => {
    try {
      const params = {
        issueId: data.id,
        publishStatus: data.publishStatus,
      };
      const res: any = await setPublishedStatus(params);
      if (res.status) {
        Message.success(res.message);
      }
    } catch (error) {
      console.log('[ error ] >', error);
    }
  };

  const downloadBcf = (val: any, row: any) => {
    if (val === '2.1') {
      GenerateIssueXML([row]);
    }
  };
  const getIssueLog = async (issueId: string) => {
    try {
      const res = await queryissueLog(issueId);
      if (res.status) {
        issueLogList.value = res.data.list;
        replyContent.value =
          issueLogList.value[issueLogList.value.length - 1].reply || '';
      }
    } catch (e) {
      console.log(e);
    }
  };
  const issuesInfoView = (row: any) => {
    getIssueLog(row.id);
    infoVisible.value = true;
    detailsInfoRow.value = row;
  };

  const reset = () => {
    searchParams.value.title = '';
    search();
  };

  const search = () => {
    fetchData({
      ...searchParams.value,
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      projectId,
    } as unknown as issuesParams);
  };
  const onPageChange = (pageNo: number) => {
    pagination.current = pageNo;
    fetchData({
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      projectId,
    });
  };
  const pageSizeChange = (pageSize: number): void => {
    pagination.pageSize = pageSize;
    fetchData({
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      projectId,
    });
  };
  const updateData = () => {
    search();
  };
  const tableHeight = ref(0);
  // table根据父组件计算空白高度
  const vTableHeight = {
    mounted(el: Element) {
      tableHeight.value = Math.max(
        (el.parentElement?.offsetHeight || 0) - 148,
        0
      );
    },
  };
  fetchData();

  async function clickNameHandle(record: any) {
    // 文件点击事件，查看文件
    let needParams: any = {
      noIssue: [1, 2, 3, 4].includes(hiddenSlot.value),
    };
    if (record.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: record.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      needParams = { ...params, ...needParams };
    }
    modelViewBim(record, route.params.projectId as string, needParams);
  }

  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );
</script>

<script lang="ts">
  export default {
    name: 'Issues',
  };
</script>

<style scoped lang="less">
  .issues {
    padding: 16px 20px;

    .card-title {
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
      img {
        position: relative;
        top: 3px;
        height: 20px;
      }
    }
    .search-title {
      height: 22px;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #1d2129;
      line-height: 22px;
    }
  }

  .list-empty {
    display: flex;
    align-items: center;
    .empty-box {
      width: 100%;
      text-align: center;
      height: 400px;
      .text {
        width: 100%;
        text-align: center;
        margin-top: -80px;
      }
    }
  }
  // :deep(.arco-table-tr) {
  //   cursor: pointer;
  // }
  :deep(.arco-table-container) {
    height: 100%;
  }
  :deep(.arco-card-header) {
    height: auto;
    padding: 20px 20px 0px 20px;
    border: none;
  }
  :deep(.arco-card-bordered) {
    border: none;
  }
  :deep(.arco-scrollbar-thumb-bar) {
    width: 0;
  }
  :deep(.arco-divider-horizontal) {
    margin: 20px 0;
  }

  .general-card {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
  }
</style>
