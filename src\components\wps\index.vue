<template>
  <a-modal
    :visible="wpsViewModal.visible"
    :ok-text="$t('model-viewer.close')"
    fullscreen
    hide-cancel
    :mask-closable="false"
    :esc-to-close="false"
    @ok="handleCancel"
    @cancel="handleCancel"
  >
    <template #title> {{ wpsViewModal?.title }} </template>
    <div id="preview"></div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { getXBaseToken } from '@/utils/auth';

  const props = defineProps({
    wpsViewModal: {
      type: Object,
      required: true,
    },
  });

  const emits = defineEmits(['update:visible']);

  const handleCancel = () => {
    emits('update:visible', false);
  };

  const init = async () => {
    const { OpenSDK }: any = window as any;
    const jssdk: any = OpenSDK.config({
      url: props.wpsViewModal.url,
      mount: document.getElementById('preview'),
    });
    // 设置 token
    jssdk.setToken({
      token: getXBaseToken() || '',
      timeout: 10 * 60 * 1000,
    });
  };
  setTimeout(() => {
    init();
  }, 100);
</script>

<script lang="ts">
  export default {
    name: 'WpsViewer',
    inheritAttrs: false,
  };
</script>

<style scoped>
  #preview {
    width: 100%;
    height: 100vh;
    overflow-x: hidden;
  }
</style>
