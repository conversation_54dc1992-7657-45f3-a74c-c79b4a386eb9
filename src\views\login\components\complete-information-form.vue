<template>
  <div class="complete-info-form-wrapper">
    <a-tabs
      v-model:active-key="activeKey"
      class="ant-tabs-center complete-info-tabs"
    >
      <a-tab-pane key="complete" :title="$t('login.complete.info')">
        <a-form
          ref="formRef"
          :model="infoForm"
          autocomplete="off"
          class="complete-info-form"
          layout="vertical"
        >
          <a-form-item
            field="userFullname"
            :rules="[
              { required: true, message: $t('login.form.userName.errMsg') },
            ]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-input
              v-model="infoForm.userFullname"
              :disabled="userFullnameDisabled"
              :placeholder="$t('login.form.userFullNamePlaceholder')"
            >
              <template #prefix>
                <img
                  src="@/assets/images/login/user-line.png"
                  class="input-icon"
                />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            field="phone"
            :rules="[
              { required: true, message: $t('login.form.telRequired') },
              {
                pattern: /^(\+\d{1,3})?\d{7,13}$/,
                message: $t('login.form.telInvalid'),
              },
            ]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-input
              v-model="infoForm.phone"
              :disabled="phoneDisabled"
              :placeholder="$t('login.form.telPlaceholder')"
              :maxlength="18"
            >
              <template #prefix>
                <img
                  src="@/assets/images/login/smartphone-line.png"
                  class="input-icon"
                />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            field="email"
            :rules="[
              { required: true, message: $t('login.form.emailPlaceholder') },
              {
                pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                message: $t('login.form.emailInvalid'),
              },
            ]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-input
              v-model="infoForm.email"
              :disabled="emailDisabled"
              :placeholder="$t('login.form.emailPlaceholder')"
              :maxlength="30"
            >
              <template #prefix>
                <img
                  src="@/assets/images/login/mail-line.png"
                  class="input-icon"
                />
              </template>
            </a-input>
          </a-form-item>
          <!-- 验证码校验方式 -->
          <a-form-item
            field="captcha"
            :label="$t('userSetting.form.captcha')"
            :rules="[
              { required: true, message: $t('login.form.captchaRequired') },
            ]"
            :validate-trigger="['blur']"
            hide-label
          >
            <a-select
              v-model="infoForm.phoneReceive"
              class="code-way"
              @click="changeCaptchaType"
            >
              <a-option value="1">{{
                $t('login.form.mobileCaptcha')
              }}</a-option>
              <a-option value="0">{{ $t('login.form.emailCaptcha') }}</a-option>
            </a-select>

            <a-divider
              direction="vertical"
              style="margin: 0 8px; height: 24px"
            />

            <a-input
              v-model="infoForm.captcha"
              :placeholder="$t('login.form.captchaPlaceholder')"
              :maxlength="50"
            >
              <template #append>
                <a-button
                  type="text"
                  :loading="captchaLoading"
                  @click="handleGetCaptcha"
                >
                  <span v-if="captchaCountDown === -2">{{
                    $t('login.form.getCaptcha')
                  }}</span>
                  <span v-else-if="captchaCountDown === -1">{{
                    $t('login.form.regainCaptcha')
                  }}</span>
                  <span v-else>{{ `${captchaCountDown}s` }}</span>
                </a-button>
              </template>
              <!-- <template #prefix>
                <img
                  src="@/assets/images/login/shield-keyhole-line.png"
                  class="input-icon"
                />
              </template> -->
            </a-input>
          </a-form-item>

          <a-form-item class="complete-info-button-group">
            <div class="complete-info-buttons">
              <a-button
                type="primary"
                html-type="submit"
                class="complete-info-primary-btn"
                @click="handleCompleteInfo"
                >确定</a-button
              >
              <!-- <a-button
                type="outline"
                class="complete-info-return-btn"
                @click="changeLogin(LoginMethods.password)"
                >返回登录</a-button
              > -->
            </div>
          </a-form-item>
        </a-form>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, computed, watch, inject } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { getSms, register, getPwdSms } from '@/api/modules/user';
  import { signup } from '@/api/sign-up';
  import { useI18n } from 'vue-i18n';
  import LoginMethods from '../constant';
  import checkPassWordFormat from '@/utils/password-validation';
  import pwdEncrypt from '@/utils/encryption/pwd';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { useRoute } from 'vue-router';
  import { useUserStore } from '@/store';
  import {
  setToken,
  setUserName,
} from '@/utils/auth';

  const userStore = useUserStore();

  // 是否是邀请激活
  const isInviteMode = ref(false);

  const { t } = useI18n();
  const activeKey = ref('complete'); // 默认选中注册
  const emit = defineEmits(['changeLogin', 'completeInfoSuccess']);

  interface FormState {
    userFullname: string;
    phone: string;
    email: string;
    captcha: string;
    phoneReceive: string;
    userNo: string;
  }

  // 取 store 值初始化，使用 computed 保证响应式
  const userFullnameFromStore = computed(
    () => userStore.userFullname || userStore.name || ''
  );
  const phoneFromStore = computed(() => userStore.phone || '');
  const emailFromStore = computed(() => userStore.email || '');
  const userNoStore = computed(() => userStore.userNo || '');

  const infoForm = reactive<FormState>({
    userFullname: userFullnameFromStore.value || '',
    phone: phoneFromStore.value || '',
    email: emailFromStore.value || '',
    userNo: userNoStore.value || '',
    captcha: '',
    phoneReceive: '1',
  });

  // 计算手机号、邮箱是否需要 disabled
  const userFullnameDisabled = computed(() => !!userFullnameFromStore.value);
  const phoneDisabled = computed(() => !!phoneFromStore.value);
  const emailDisabled = computed(() => !!emailFromStore.value);

  // 验证码相关状态
  const formRef = ref<FormInstance>();
  const captchaCountDown = ref(-2);
  const captchaLoading = ref(false);
  const captchaKey = ref('');

  const counter: any = ref(null);
  // 更新验证码倒计时
  const updateCaptchaCountDown = () => {
    captchaCountDown.value = 60;
    counter.value = setInterval(() => {
      if (captchaCountDown.value === 0) {
        clearInterval(counter.value);
        captchaCountDown.value = -1;
      } else {
        captchaCountDown.value--;
      }
    }, 1000);
  };

  // 修改验证码方式
  const changeCaptchaType = () => {
    clearInterval(counter.value);
    captchaCountDown.value = -2;
  };

  // 获取短信验证码
  const handleGetCaptcha = async () => {
    console.log('getSMSCaptcha called');
    if (captchaCountDown.value >= 0) {
      Message.warning(t('login.form.captchaHoldOn'));
      return;
    }

    try {
      // 验证码为手机号
      if (infoForm.phoneReceive === '1') {
        // 使用正则表达式验证手机号格式和长度
        const phoneRegex = /^(\+\d{1,3})?\d{7,13}$/;
        if (!phoneRegex.test(infoForm.phone)) {
          // 校验手机号格式
          if (!infoForm.phone.startsWith('+') && infoForm.phone.length !== 11) {
            // 如果没有国际区号且长度不是11位，提示手机号长度错误
            Message.error('请输入正确的手机号');
            console.error('请输入正确的手机号');
          } else {
            // 其他格式错误
            Message.error(t('login.form.telInvalid'));
          }
          return;
        }
      } else {
        // 验证码为邮箱
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(infoForm.email)) {
          Message.error('请输入正确的邮箱');
          return;
        }
      }

      // 发送验证码请求
      captchaLoading.value = true;
      const params = {
        phone: infoForm.phoneReceive === '1' ? infoForm.phone : '',
        // 手机号加phoneReceive 为1 则给手机发送验证码，否则给邮箱加phoneReceive 为0发送验证码
        email: infoForm.phoneReceive === '0' ? infoForm.email : '',
        phoneReceive: infoForm.phoneReceive,
      };
      const { data, status, message } = await getPwdSms(params);
      captchaKey.value = data;
      console.log('getSMSCaptcha response:', data, status, message?.message);
      if (!status) {
        console.error('getSMSCaptcha error:', message?.message);
        // Message.warning(message?.message);
      } else {
        console.log('getSMSCaptcha success:', data);
        updateCaptchaCountDown();
        Message.success(t('login.form.captchaSent'));
      }
    } catch (err) {
      if (typeof err === 'string') {
        Message.error(err);
      }
    } finally {
      captchaLoading.value = false;
      // updateCountDown();
    }
  };

  const changeLogin = (method: LoginMethods) => {
    console.log('changeLogin called with method:', method);
    emit('changeLogin', method);
  };

  // 注入 openShowModal 方法
  const openShowModal = inject('openShowModal') as (() => void) | undefined;

  // 表单提交处理
  const handleCompleteInfo = async () => {
    const values = infoForm;

    try {
      // 使用正则表达式验证手机号格式和长度
      const phoneRegex = /^(\+\d{1,3})?\d{7,13}$/;
      if (!phoneRegex.test(values.phone)) {
        // 校验手机号格式
        if (!values.phone.startsWith('+') && values.phone.length !== 11) {
          // 如果没有国际区号且长度不是11位，提示手机号长度错误
          Message.error('请输入正确的手机号');
          console.error('请输入正确的手机号');
        } else {
          // 其他格式错误
          Message.error(t('login.form.telInvalid'));
        }
        return;
      }

      // 使用正则表达式验证邮箱格式
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      // 验证邮箱格式
      if (values.email && !emailRegex.test(values.email)) {
        Message.error(t('login.form.emailInvalid'));
        console.error('邮箱格式不正确');
        return;
      }
      if (!captchaKey.value) {
        Message.warning(t('login.form.pleaseEnterCaptcha'));
        return;
      }

      console.log('formState:', values);
      console.log('captchaKey:', captchaKey.value);
      const completeInfoParams = {
        userFullname: values.userFullname,
        phone: values.phone,
        email: values.email,
        captcha: values.captcha,
        userNo: values.userNo,
        // 只有在 values.password 存在时才加密传递
        // ...(typeof values.password === 'string' && values.password
        //   ? { password: pwdEncrypt(values.password) }
        //   : {}),
        key: captchaKey.value,
        phoneReceive: infoForm.phoneReceive,
      };

      console.log('表单提交了');
      // 请求完善信息接口
      const completeInfoResult = await signup(completeInfoParams);
      if (completeInfoResult?.status) {
        Message.success(completeInfoResult?.data?.message || t('login.fill.userInfo.success'));
        if (completeInfoResult?.data) {
          // 立即执行token设置等操作
          await setToken(completeInfoResult.data.access_token);
          setUserName(completeInfoResult.data.userName);
          userStore.userName = completeInfoResult.data.userName;
          await userStore.info();
          if (userStore.username) userStore.addToProject();
          
          // 设置一个标志，表示已经执行过token设置等操作
          completeInfoResult.data.isFromCompleteInfo = true;
          
          // 将响应数据传输给父组件
          emit('completeInfoSuccess', completeInfoResult.data);
          if (openShowModal) openShowModal();
        }
        // 完善信息成功后跳转到首页
        changeLogin(LoginMethods.password); 
      }
    } catch (error) {
      console.error('完善信息失败:', error);
      // Message.error(
      //   error instanceof Error ? error.message : t('login.form.register.failed')
      // );
    }finally{
      infoForm.captcha = '';
      captchaKey.value = '';
    }
  };

  // 使用watch来监听companyCode的变化
  // watch(invitationCompanyCode, (newVal) => {
  //   if (newVal) {
  //     infoForm.invitationCode = newVal;
  //   }
  // });
  // const route = useRoute();

  // watch(
  //   () => route.params?.invitationId,
  //   async () => {
  //     if (
  //       route.query?.invitationId &&
  //       route.query?.followId &&
  //       route.query?.isRegister === '1'
  //     ) {
  //       // 是邀请激活页面 则获取激活用户信息 回显
  //       isInviteMode.value = true;
  //       const { data } = await getRegisterInvitation({
  //         invitationId: route.query?.invitationId,
  //         followId: route.query?.followId,
  //       });

  //       infoForm.userFullname = data.name;
  //       infoForm.phone = data?.phone;
  //       infoForm.email = data?.email;
  //       // infoForm.invitationCode = data?.invitationCode;
  //     } else isInviteMode.value = false;
  //   },
  //   {
  //     immediate: true,
  //   }
  // );
</script>

<style lang="less" scoped>
  .input-icon {
    width: 18px;
    height: 18px;
    vertical-align: -0.2em;
  }
  .complete-info-form-wrapper {
    width: 430px;
    padding-left: 16px;
  }

  :deep(.arco-form-item) {
    margin-bottom: 24px;
  }

  :deep(.arco-form-item-content) {
    background-color: transparent;
    border: 1px solid #c9cdd4;
    border-radius: 8px;
  }

  :deep(.arco-input-wrapper) {
    background-color: transparent;
  }
  :deep(.arco-input-append) {
    border: none;
    background-color: transparent;
    :deep(.arco-btn-text) {
      width: 100%;
      height: 100%;
      background-color: transparent;
    }
    :deep(.arco-btn-text):hover {
      background-color: transparent !important;
    }
  }

  :deep(.arco-btn-text[type='button']) {
    padding: 0;
  }

  .complete-info-form {
    &-password-actions {
      display: flex;
      justify-content: space-between;
    }
  }

  .complete-info-button-group {
    :deep(.arco-form-item-content) {
      background-color: transparent;
      border: none;
    }
    .complete-info-buttons {
      width: 100%;
      border: none;
      .complete-info-primary-btn {
        width: 100%;
        height: 38px;
      }

      .complete-info-return-btn {
        width: 100%;
        height: 45px;
        border: none;
      }
    }
  }

  .complete-info-tabs {
    width: 100%;
  }
  :deep(.code-way) {
    width: 126px !important;
    background-color: #ffff;
    border-radius: 8px !important;
  }
  :deep(.arco-tabs-nav-tab) {
    justify-content: center;
  }
</style>
