import axios from 'axios';
import { getXBaseToken } from '@/utils/auth';

export function getCompareList(params: any) {
  return axios.get('/cde-collaboration/file/version-compare', {
    params,
  });
}

export function GetXBaseClashInfo(params: any) {
  return axios.get('/api/open/v1/clash/content', {
    params,
    headers: {
      Authorization: `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
}
export function GetExportFile(params: any) {
  return axios.get('/cde-collaboration/xbase/clash/export', {
    params,
    responseType: 'blob',
  });
}
