<template>
  <!-- <div class="meeting-container"> -->
  <a-row class="meeting-container">
    <MeetingList
      ref="meetingListRef"
      type="meeting"
      :edit-type="type"
      :class="['meeting-left', { 'meeting-left-en': isEn }]"
      @select-data="selectData"
      @on-create="handleCreate"
    />
    <div v-if="type == 'edit' && !meetingData.title" class="noData">
      <img :src="scheduleBgImg" alt="" />
      <div>{{ t('schedule.noData') }}</div></div
    >
    <div v-else class="meeting-right">
      <div class="content">
        <div class="describe-box">
          <a-input
            v-model="title"
            class="title-input"
            :placeholder="t('schedule.inputTitle')"
            :disabled="type !== 'new' && !isRelatedToUser"
          ></a-input>
        </div>
        <MeetingDetail
          :key="type"
          :title="title"
          :type="type"
          :data="type === 'edit' ? meetingData : {}"
          @refresh="refreshHandle"
        />
      </div>
    </div>
  </a-row>
  <!-- 右侧详情区域 -->

  <!-- <div class="comment">
    <a-input class="comment-input" placeholder="请输入">
      <template #suffix>
        <a-avatar shape="circle">
          <img
            alt="avatar"
            src="https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/3ee5f13fb09879ecb5185e440cef6eb9.png~tplv-uwbnlip3yd-webp.webp"
          />
        </a-avatar>
      </template>
    </a-input>
  </div> -->
  <!-- </div> -->
</template>

<script lang="ts" setup>
  import MeetingList from '../scheduleList.vue';
  // import MeetingDetail from './component/meeting-detail.vue';
  import MeetingDetail from './componemt/createEditMeeting.vue';
  import { ref, watch, defineProps, computed } from 'vue';
  import scheduleBgImg from '@/assets/images/schedule/schedule-bg.png';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import { useUserStore } from '@/store';
  import { cloneDeep } from 'lodash';

  const { t } = useI18n();
  const { currentLocale } = useLocale();
  const isEn = computed(() => currentLocale.value === 'en-US');

  const userStore = useUserStore();
  const userName = computed(() => userStore.username);

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
    types: {
      type: String,
      default: 'edit', // 设置默认值
    },
  });
  const title = ref('');

  const meetingData: any = ref({});
  const type = ref('edit'); // 默认是编辑模式

  const isRelatedToUser = ref<boolean>(false);
  // 判断用户的查看权限
  const checkPermission = async (data: any) => {
    const detail = cloneDeep(data);

    // 判断 createBy 是否为该用户
    const isCreateByUser = detail.createBy === userName.value;

    // 判断组织人是否为该用户
    const isOrganizedByUser = detail.organizerUsername === userName.value;

    // 判断参与人中是否包含该用户
    const chargeList = detail.participants.map((item) => item.username);
    const isChargePerson = chargeList.includes(userName.value);

    // 合并判断
    isRelatedToUser.value =
      isCreateByUser || isOrganizedByUser || isChargePerson;
  };
  const selectData = (val: any) => {
    console.log('1111');
    type.value = 'edit';
    if (type.value === 'edit') {
      meetingData.value = val;
      title.value = val?.title || '';
      type.value = 'edit'; // 设置为编辑模式
    }
    checkPermission(val);
  };
  const meetingListRef = ref<any>(null);
  // 刷新数据
  const refreshHandle = (id: any) => {
    if (id === 'new') {
      meetingListRef.value.init();
      type.value = 'edit'; // 设置为编辑模式
      return;
    }
    meetingListRef.value.init(id);
  };

  // 监听有id则回显  用于日程的编辑跳转到对应的数据编辑
  watch(
    () => props.id,
    (val) => {
      setTimeout(() => {
        meetingListRef.value.init(val);
      }, 100);
    },
    { immediate: true }
  );
  watch(
    () => props.types,
    (newValue) => {
      console.log('监听到的类型100:', newValue);
      if (newValue === 'new') {
        type.value = 'new'; // 切换为创建模式
        meetingData.value = {}; // 清空数据
        title.value = ''; // 清空标题
      } else {
        type.value = 'edit'; // 切换为编辑模式
      }
    },
    { immediate: true } // 初始化时立即执行
  );
  const handleCreate = (data: { type: string; editType: string }) => {
    console.log('接收到的参数:', data);
    if (data.type === 'meeting' && data.editType === 'new') {
      type.value = 'new'; // 设置为创建模式
      meetingData.value = {}; // 清空数据
      title.value = ''; // 清空标题
    } else {
      type.value = 'edit'; // 设置为编辑模式
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'Meeting',
  };
</script>

<style lang="less" scoped>
  .meeting-container {
    display: flex;
    height: 100%;
    overflow: auto;
    overflow-x: auto; // 允许横向滚动
    min-width: 0; // 防止子项撑破容器
    flex-wrap: nowrap; // 保证子项不换行
    .meeting-left {
      width: calc(30% - 1px);
      min-width: 360px; // 中文的最小宽度
    }
    .meeting-left-en {
      min-width: 420px; // 英文时更宽
    }
    :deep(.arco-row) {
      height: 100%;
    }
    :deep(.arco-col) {
      height: 100%;
    }
    ::-webkit-scrollbar {
      width: 8px; /* 滚动条宽度 */
    }
    ::-webkit-scrollbar-thumb {
      background-color: #eeeeee; /* 滚动条滑块颜色 */
      border-radius: 8px; /* 滑块圆角 */
    }
  }

  .meeting-right {
    width: 70%;
    height: 100%;
    border: 1px solid #d9d9d9;
    border-bottom-right-radius: 8px;
    border-top-right-radius: 8px;
    // padding-top: 16px;
    background-color: #fff;
    // overflow-y: auto;
    .describe-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      border-bottom: 1px solid #ddd;
      // padding: 0 0 12px 0; /* 设置内边距：下16px */
      .title-input {
        font-size: 20px;
        background-color: #fff;
        color: #1d2129;
        border: none !important;
        height: 60px;
        line-height: 60px;

        :deep(.arco-input) {
          font-size: 20px;
          color: #1d2129;
        }
        :deep(.arco-input-wrapper) {
          background-color: #fff;
        }

        .arco-input-wrapper {
          padding: 0 20px 0 20px;
        }
      }
      .describe-title {
        font-size: 20px;
        line-height: 30px;
        color: #1d2129;
        font-weight: 500;
      }
    }
  }
  .noData {
    height: 100%;
    width: 70%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #d9d9d9;
    flex-direction: column;
    img {
      display: block;
      width: 140px;
      height: 140px;
    }
    div {
      margin-top: 16px;
      color: #4e5969;
    }
  }

  // .comment {
  //   position: absolute;
  //   display: flex;
  //   align-items: center;
  //   justify-content: space-between;
  //   right: 20px;
  //   width: calc(50% - 30px);
  //   bottom: 24px;
  //   border: 1px solid #d9d9d9;
  //   border-radius: 0 0 8px 8px;
  //   padding: 20px;
  //   background-color: #fff;
  //   z-index: 999;
  // }

  .describe-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    border-bottom: 1px solid #ddd;
    // padding: 0 0 12px 0; /* 设置内边距：下16px */
    // margin-bottom: 20px;
    .describe-title {
      font-size: 20px;
      line-height: 30px;
      color: #1d2129;
      font-weight: 500;
    }
    .title {
      font-size: 20px;
      background-color: #fff;
      color: #1d2129;
      border: none !important;
      height: 60px;
      line-height: 60px;
    }
    :deep(.arco-input) {
      font-size: 20px !important;
      color: #1d2129 !important;
    }
    :deep(.arco-input-wrapper) {
      background-color: #fff;
      // border: unset;
    }
  }
</style>
