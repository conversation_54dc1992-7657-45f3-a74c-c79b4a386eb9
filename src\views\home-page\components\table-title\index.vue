<template>
  <div class="table-title">
    <slot name="icon">
      <img :src="icon" alt="" />
    </slot>
    <img src="@/assets/images/table-title.png" alt="" />
    <slot
      ><span>{{ title }}</span></slot
    >
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    title: {
      type: String,
      default: '',
    },
    icon: {
      type: String,
      default: '@/assets/images/table-title.png', // 默认图片路径
    },
  });
</script>

<script lang="ts">
  export default {
    name: 'TableTitle',
  };
</script>

<style lang="less" scoped>
  .table-title {
    display: flex;
    align-items: center;
    line-height: 27px;
    font-size: 18px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    width: 100%;
    color: #1d2129;
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
</style>
