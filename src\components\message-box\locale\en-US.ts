export default {
  'messageBox.tab.title.message': 'Message',
  'messageBox.tab.title.notice': 'Notice',
  'messageBox.tab.title.todo': 'Todo',
  'messageBox.tab.button': 'empty',
  'messageBox.allRead': 'All Read',
  'messageBox.viewMore': 'View More',
  'messageBox.noContent': 'No Content',
  'messageBox.switchRoles': 'Switch Roles',
  'messageBox.userCenter': 'User Center',
  'messageBox.portalCenter': 'Portal Center',
  'messageBox.userSettings': 'User Settings',
  'messageBox.logout': 'Logout',
  'messageBox.switch.identity': 'Switch Identities',

  'messageBox.message': 'Message',
  'messageBox.all-types': 'All Types',
  'messageBox.review-notification': 'Review Notification',
  'messageBox.review-task': 'Review Task',
  'messageBox.delivery-notification': 'Delivery Notification',
  'messageBox.delivery-task': 'Delivery Task',
  'messageBox.collaboration-notification': 'Collaboration Notification',
  'messageBox.collaboration-task': 'Collaboration Task',
  'messageBox.collaboration-remind': 'Collaboration Remind',
  'messageBox.issue-notification': 'Issue Notification',
  'messageBox.issue-reply': 'Issue Reply',
  'messageBox.enclosure-notification': 'Enclosure Notification',
  'messageBox.meeting-notification': 'Meeting Notification',
  'messageBox.shared-notification': 'Shared Notification',
  'messageBox.meeting-news': 'Meeting Notification',
  'messageBox.select-all': 'Select All',
  'messageBox.batch-delete': 'Batch Delete',
  'messageBox.all-read': 'All Read',
  'messageBox.unread-messages': 'Unread Messages',
  'messageBox.read-messages': 'Read Messages',
  'messageBox.no-selected-messages': 'No selected messages',
  'messageBox.operation-success': 'Operation success!',
  'messageBox.other-notification': 'Other Notification',
  'messageBox.sender': 'Sender:',
  'messageBox.owning-project': 'Owning Project：',
  'messageBox.prompt': 'Prompt',
  'messageBox.modal-message':
    'You are about to jump to the other project page. Do you want to continue viewing this project?',
};
