<template>
  <div class="my-card">
    <div v-if="!personalCombinedList.length" class="empty-folder">
      <!-- 数据为空 -->
      <emptyFolderIcon style="width: 140px; height: 140px" />
      <div class="empty-folder-text">{{ t('knowledgenew.empty-content') }}</div>
      <a-button
        type="primary"
        @click="() => emit('handleList', 'upload', {})"
        >{{ t('cloud.upload-file') }}</a-button
      >
    </div>
    <!-- 文件/文件夹卡片 -->
    <div v-else class="card-list">
      <div
        v-for="item in personalCombinedList"
        :key="item.id"
        class="file-card"
        :class="{ selected: item.selected }"
      >
        <div>
          <div
            class="file-card-header hover-show"
            :class="{ 'always-show': item.selected }"
          >
            <a-checkbox v-model="item.selected" class="folder-checkbox" />
            <FolderActionDropdown
              :item="item"
              @action="handleAction"
            ></FolderActionDropdown>
          </div>
          <div class="icon-name">
            <div @click="debouncedSelect(item)">
              <file-image
                :file-name="item.name ?? ''"
                :is-sysFile="false"
                :is-file="!!item.folderId"
                class="folder-image"
              />
            </div>

            <a-tooltip
              v-if="!item.isAdd && !item.isEdit"
              :content="item.name || undefined"
            >
              <div class="file-name" @click="debouncedSelect(item)">{{
                item.name
              }}</div>
            </a-tooltip>
            <div v-else-if="item.isAdd" class="file-name">
              <a-input
                ref="addInputRef"
                class="folder-input"
                v-model="item.name"
                style="width: 95%"
                @blur="() => emit('handleList', 'add', item)"
                @keydown.enter="() => emit('handleList', 'add', item)"
              />
            </div>
            <div v-else-if="item.isEdit && !item.folderId" class="file-name">
              <a-input
                ref="editInputRef"
                class="folder-input"
                v-model="item.name"
                style="width: 95%"
                @blur="emit('handleList', 'rename', item)"
                @keydown.enter="emit('handleList', 'rename', item)"
              />
            </div>
            <div v-else-if="item.isEdit && item.folderId" class="file-name">
              <a-input
                ref="editInputRef"
                class="folder-input"
                v-model="item.filename"
                style="width: 95%"
                @blur="emit('handleList', 'rename', item)"
                @keydown.enter="emit('handleList', 'rename', item)"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 上传文件卡片 -->
      <div
        class="file-card upload-card"
        @click="() => emit('handleList', 'upload', {})"
      >
        <div class="file-icon">
          <uploadIcon class="has-pointer upload-icon" />
        </div>
        <div class="file-name">{{ t('cloud.upload-file') }}</div>
      </div>
    </div>
  </div>
  <MoveFileModal
    v-model:visible="moveFileVisible"
    :files="moveFiles"
    @move-file-success="moveFileSuccess"
    @refresh="queryFolderContent()"
  ></MoveFileModal>
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import { ref, toRefs, defineEmits, nextTick, toRaw } from 'vue';
  import i18n from '@/locale/index';
  import { useDebounceFn } from '@vueuse/core';
  import { reConvertApi } from '@/views/projectSpace/file/api';
  import { Message } from '@arco-design/web-vue';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import modelViewBim from '@/utils/common/view';
  import FolderActionDropdown from './folder-action-dropdown.vue';
  import MoveFileModal from './move-file-modal.vue';
  import FileImage from './image-file.vue';
  import uploadIcon from '@/assets/images/knowledge-base/upload2.svg';
  import emptyFolderIcon from '@/assets/images/knowledge-base/empty-folder1.svg';

  const { t } = i18n.global;
  const emit = defineEmits(['refreshFolder', 'handleList']);
  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { personal, personalCombinedList } = storeToRefs(knowledgeBaseNewStore);
  const { projectId } = toRefs(personal.value);

  // 点击进入下一文件夹
  const onSelect = (item: any) => {
    console.log(personal.value.projectId, 'projectId33333');
    // 如果点击文件夹，进入下一文件夹，如果点击文件，则进入预览文件
    if (!item.folderId) {
      // 文件夹点击事件，进入下一层
      knowledgeBaseNewStore.setPersonCurrentFolder(item);
      knowledgeBaseNewStore.pushBreadcrumb(item);
      knowledgeBaseNewStore.getPersonalFolder('personal');
      knowledgeBaseNewStore.getfiles('personal');
      return;
    }
    const needParams = {
      noIssue: false,
      source: 'knowledge',
    };
    if (item.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: item.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      Object.assign(needParams, params);
    }

    modelViewBim(item, personal.value.projectId, needParams);
  };
  // 防抖延迟 300ms 执行 onSelect 函数
  const debouncedSelect = useDebounceFn((item) => {
    // 这里是点击时要执行的操作
    onSelect(item);
  }, 300);
  const addInputRef = ref(null);
  const editInputRef = ref(null);
  // 添加文件夹
  const focusInput = () => {
    nextTick(() => {
      const inputs = addInputRef.value;
      console.log(inputs, 'inputs111');
      // 在 v-for 中 ref 会生成一个数组
      if (inputs && inputs.length) {
        inputs[inputs.length - 1].focus();
      }
    });
  };
  const handleRename = (item: any) => {
    // 重命名
    // 如果是新增状态，直接返回
    if (item.isAdd) {
      return;
    }
    item.isEdit = true;
    nextTick(() => {
      const inputEdits = editInputRef.value;
      // 在 v-for 中 ref 会生成一个数组
      if (inputEdits && inputEdits.length) {
        inputEdits[inputEdits.length - 1].focus();
      }
    });
  };
  /** 移动begin */
  const moveFileVisible = ref(false);
  const moveFiles = ref<Array<Node>>([]);
  const moveFile = (record: Node) => {
    moveFiles.value = [toRaw(record)];
    moveFileVisible.value = true;
  };
  const moveFileSuccess = async () => {
    // treeRefresh();
    // 刷新当前文件列表
    knowledgeBaseNewStore.getPersonalFolder('personal');
    knowledgeBaseNewStore.getfiles('personal');
    moveFiles.value = [];
  };
  const queryFolderContent = async () => {
    // treeRefresh();
    // 刷新当前文件列表
    knowledgeBaseNewStore.getPersonalFolder('personal');
  };
  const reConvert = async (record: any) => {
    const params = {
      fileId: record.id,
    };
    const res: any = await reConvertApi(params);
    if (res.code === 8000000) {
      Message.info(t('file-manage.reconvert'));
      knowledgeBaseNewStore.getfiles('personal');
    }
  };
  /** 移动end */
  // dropdown事件委托
  const handleAction = (record: any) => {
    switch (record.type) {
      case 'rename':
        if (record.item.folderId) {
          const idx = record.item.name.lastIndexOf('.');
          record.item.filename = record.item.name.slice(0, idx);
          record.item.filetype = record.item.name.slice(idx);
        }
        handleRename(record.item);
        break;
      case 'delete':
        knowledgeBaseNewStore.deleteItems([record.item], t, 'personal');
        break;
      // case 'fileEdit':
      //   wpsViewHandle(record.item, 'edit', 'admin');
      //   break;
      case 'popupVisible':
        record.item.selected = record.value;
        break;
      case 'move':
        // 文件/文件夹移动
        moveFile(record.item);
        break;
      case 'reConvert':
        // 重新转换
        reConvert(record.item);
        break;

      default:
    }
  };
  defineExpose({ focusInput });
</script>

<style scoped lang="less">
  .card-page {
    position: absolute;
    height: calc(100% - 40px);
    width: 100%;
  }
  :deep(.has-pointer) {
    cursor: pointer;
  }
  .my-card {
    height: calc(100% - 64px);
    width: 100%;
  }
  .card-list {
    height: 100%;
    overflow: auto;
    display: flex;
    flex-wrap: wrap; // 允许换行
    gap: 20px;
    align-content: flex-start; /* 关键 */
    .file-card {
      width: 133px;
      height: 130px;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 0;
      &:hover {
        background: #e8f2ff;
        border: 1px solid #d9d9d9;
        .hover-show {
          visibility: visible;
          opacity: 1;
        }
      }
      .hover-show.always-show {
        visibility: visible;
        opacity: 1;
      }
      .hover-show {
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      .file-card-header {
        width: 133px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 8px 0 8px;
      }
      .folder-image {
        width: 60px;
        height: 60px;
      }
      .file-icon {
        width: 60px;
        height: 60px;
        background: rgba(242, 243, 245, 0.8);
        border-radius: 8px 8px 8px 8px;
        border: 1px dashed #e5e6eb;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 22px;
        .upload-icon {
          width: 20px;
          height: 18px;
        }
      }
      .file-name {
        font-size: 14px;
        color: #1d2129;
        line-height: 22px;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏超出容器的内容 */
        text-overflow: ellipsis;
        max-width: 133px;
        width: 100%;
        display: block;
        word-break: keep-all;
        word-break: break-word;
        text-align: center;
        margin-top: 8px;
      }
    }
    .icon-name {
      display: flex;
      flex-direction: column; /* 上下排列 */
      align-items: center; /* 水平居中 */
    }
    .icon-name > div {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    .selected {
      background: #e8f2ff;
      border: 1px solid #d9d9d9;
    }
  }
  .empty-folder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; /* 垂直居中 */
    .empty-folder-text {
      font-size: 14px;
      color: #4e5969;
      margin: 24px 0 16px 0;
    }
  }
  .folder-input {
    height: 22px;
  }
</style>
