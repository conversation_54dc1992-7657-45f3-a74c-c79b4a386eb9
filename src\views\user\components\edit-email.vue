<template>
  <a-form
    ref="formRef"
    :model="formData"
    class="form"
    :label-col-props="{ span: 8 }"
    :wrapper-col-props="{ span: 16 }"
  >
    <a-form-item
      field="email"
      :label="$t('userSetting.form.email')"
      :rules="[
        {
          required: true,
          message: $t('userSetting.form.email.required'),
        },
        {
          match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, //邮箱宽松正则替换
          message: $t('userSetting.form.email.error'),
        },
      ]"
    >
      <a-input
        v-model="formData.email"
        :placeholder="$t('userSetting.form.placeholder.common')"
        allow-clear
      ></a-input>
    </a-form-item>

    <a-form-item>
      <a-space>
        <a-button type="primary" :loading="loading" @click="validate">
          {{ $t('userSetting.save') }}
        </a-button>
        <a-button type="secondary" @click="reset">
          {{ $t('userSetting.reset') }}
        </a-button>
      </a-space>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { updateEmail, EmailParams } from '@/api/user';
  import { Message } from '@arco-design/web-vue';
  import useLoading from '@/hooks/loading';
  import { useI18n } from 'vue-i18n';
  import { useUserStore } from '@/store';

  const { loading, setLoading } = useLoading();
  const { t } = useI18n();
  const formRef = ref<FormInstance>();
  const formData = ref<EmailParams>({
    email: '',
  });
  const userStore = useUserStore();
  const userName = computed(() => userStore.username);
  const userId = computed(() => userStore.id);

  const validate = async () => {
    if (loading.value) return;
    const res = await formRef.value?.validate();
    if (!res) {
      setLoading(true);
      try {
        const params: EmailParams = {
          ...formData.value,
          username: userName.value,
          id: userId.value,
        };
        const result = await updateEmail(params);
        if (result.status) {
          Message.success(t('userSetting.email.edit.success'));
          userStore.setInfo({ email: formData.value.email });
        }
      } catch (e: any) {
        // nothing to do here
      } finally {
        setLoading(false);
      }
    }
  };

  const reset = async () => {
    await formRef.value?.resetFields();
  };
</script>

<style scoped lang="less">
  .form {
    width: 540px;
    margin: 0 auto;
    :deep(.arco-form-item-content-flex) {
      justify-content: flex-end;
    }
  }
</style>
