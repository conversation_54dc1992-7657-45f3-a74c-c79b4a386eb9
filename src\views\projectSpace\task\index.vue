<template>
  <div class="task">
    <commonTabs v-model:model-value="defaultKey" :tabs="tabs" />
    <Share v-if="defaultKey === '1'" />
    <Deliver v-else-if="defaultKey === '2'" />
  </div>
</template>

<script lang="ts" setup>
  import { onBeforeMount, onBeforeUnmount, ref, provide, computed } from 'vue';
  import Share from './share/index.vue';
  import Deliver from './deliver/index.vue';
  import commonTabs from '@/components/common-tabs/index.vue';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';
  import { setLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';

  const { t } = useI18n();

  const tabs = computed(() => [
    {
      value: '1',
      label: t('task.share-audit'),
    },
    {
      value: '2',
      label: t('task.deliver-audit'),
    },
  ]);

  const defaultKey = ref('1');

  provide('defaultKey', defaultKey);

  const route = useRoute();

  const userId = getUserId() || '';

  onBeforeMount(() => {
    const type = route.query.type;
    const projectId = route.params.projectId as string;
    const key = `last_project_${userId}`;
    const oldProjectId = localStorage.getItem(key);
    if (type && projectId && oldProjectId !== projectId) {
      setLocalstorage(key, projectId);
    }
    if (type === 'delivery') {
      defaultKey.value = '2';
    } else if (type === 'share') {
      defaultKey.value = '1';
    } else {
      const defaultTabKey = localStorage.getItem('TaskDefaultTabKey');
      if (defaultTabKey) {
        defaultKey.value = defaultTabKey;
      } else {
        defaultKey.value = '1';
      }
    }
  });
  onBeforeUnmount(() => {
    localStorage.setItem('TaskDefaultTabKey', '');
  });
</script>

<style scoped lang="less">
  .task {
    width: 100%;
    height: 100%;
    padding: 16px 20px;
    overflow: hidden;
  }
</style>
