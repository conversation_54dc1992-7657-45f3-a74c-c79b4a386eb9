declare global {
  declare namespace File {
    type SelectedNode = {
      children: Array<any>;
      createDate: string;
      files: Array<any>;
      id: string;
      isFileOrFolder: number;
      key: string;
      name: string;
      parentId: string;
      projectId: string;
      sunFolders: Array<any>;
      sysType: number;
      teamId: string;
      title: string;
      type: string;
    };
  }
}
