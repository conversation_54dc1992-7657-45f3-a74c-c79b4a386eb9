<template>
  <div class="standTree">
    <a-input-search
      v-model="searchKey"
      style="margin-bottom: 8px"
      allow-clear
      @press-enter="searchHandle"
      @search="searchHandle"
      @clear="searchHandle"
    />

    <div class="tree-box">
      <a-spin :loading="treeLoading">
        <a-tree
          v-if="treeData.length"
          :expanded-keys="expandedKeys"
          :data="treeData"
          :field-names="{ title: 'name', children: 'childList', key: 'id' }"
          :default-expand-all="defaultExpandAll"
          :selected-keys="treeSelectedKey"
          @select="selectHanlde"
          @expand="onExpand"
        >
          <template #title="nodeData">
            <span class="name" :style="nameStyle(nodeData)">
              <a-tooltip
                :content="`${nodeData.name}(${nodeData.totalChildCnt || 0})`"
              >
                <span> {{ nodeData.name }}</span>
                <span v-if="nodeData.childList?.length"
                  >({{ nodeData.totalChildCnt || 0 }})</span
                >
              </a-tooltip>
            </span>
          </template>
          <template #icon="nodeData">
            <standardFolder v-if="nodeData.node.level === 0" />
            <standardFile v-else />
          </template>
          <template v-if="props.actionType" #extra="nodeData">
            <span class="btn-box">
              <a-button v-show="!operateVisible" type="text">
                <icon-edit @click="editHandle(nodeData)"
              /></a-button>
              <a-popconfirm content="确认删除吗？" @ok="delHandle(nodeData)">
                <a-button v-show="!operateVisible" type="text">
                  <icon-delete
                /></a-button>
              </a-popconfirm>
            </span>
          </template>
        </a-tree>
        <a-empty v-else />
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';

  import useStandardManageStore from '@/store/modules/standard-manage/index';
  import { storeToRefs } from 'pinia';
  import standardFolder from '@/assets/images/common/standardFolder.svg';
  import standardFile from '@/assets/images/common/standardFile.svg';

  const store = useStandardManageStore();
  const { treeDataList, treeSelectedKey, treeLoading } = storeToRefs(store);

  const originTreeData: any = ref(treeDataList.value);
  const defaultSelectedKey: any = ref('');
  const searchKey = ref('');
  const treeData = ref([]);
  const defaultExpandAll: any = ref(false);

  const emit = defineEmits(['update:visible', 'edit', 'del', 'select']);

  // 编辑树数据
  const editHandle = async (value: any) => {
    await store.setSelTreeData(value);
    emit('edit');
  };
  // 删除树数据
  const delHandle = async (value: any) => {
    await store.setSelTreeData(value);
    emit('del', value);
  };

  const expandedKeys: any = ref([]);

  // 展开收起
  const onExpand = (keys: any, data: any) => {
    if (!data.expanded) {
      expandedKeys.value = expandedKeys.value.filter(
        (item: any) => item !== data.node.id
      );
    } else if (!expandedKeys.value.includes(data.node.id)) {
      expandedKeys.value.push(data.node.id);
    }
  };

  // 节点数点击
  const selectHanlde = async (key: any, value: any) => {
    if (!key) return;
    store.setTreeSelectedKey(key[0]);

    // 点击添加展开功能
    if (!expandedKeys.value.includes(key[0])) {
      expandedKeys.value.push(key[0]);
    }

    defaultSelectedKey.value = value.selectedNodes[0].id;
    // 设置当前节点树数据
    await store.setSelTreeData(value.selectedNodes[0]);
    emit('select', value.selectedNodes[0]);

    // 后端所需数据(暂留)
    // childrenIds.value = [];
    // await getTreeChildrenIds(value.selectedNodes[0].children);
    // const nowDataId = value.selectedNodes[0].id;
  };

  defineExpose({
    selectHanlde,
  });

  const props: any = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      required: false,
    },
    type: {
      type: String,
      required: false,
    },
    actionType: {
      type: Boolean,
      required: false,
    },
    operateVisible: {
      type: Boolean,
      required: false,
    },
  });

  const nameStyle = (node: any) => {
    const bigWidth = props.actionType ? 196 : 240;
    if (node?.level === 0) return { maxWidth: `${bigWidth}px` };
    const width = bigWidth - node.level * 25;
    const maxWidth = width > 0 ? width : 25;
    return { maxWidth: `${maxWidth}px` };
  };

  // const filterNodes = (nodes: any) => {
  //   return nodes
  //     .filter((node: any) => node.childList && node.childList.length > 0)
  //     .map((node: any) => {
  //       return {
  //         ...node,
  //         childList: filterNodes(node.childList),
  //       };
  //     });
  // };

  watch(
    () => treeDataList.value,
    () => {
      searchKey.value = '';
      originTreeData.value = JSON.parse(JSON.stringify(treeDataList.value));
      treeData.value = originTreeData.value;
      // 如果是分类编码标准  隐藏叶子节点
      // if (props.actionType) {
      //   treeData.value = filterNodes(originTreeData.value);
      // }
    }
  );

  // 查找具有特定名称的节点
  function findNodesByName(name: any) {
    const result: any = [];
    // 递归函数
    function searchNodes(nodes: any, name: any) {
      nodes.forEach((node: any) => {
        // 检查当前节点名称是否匹配
        if (node.name.includes(name)) {
          result.push(node);
        } else if (node.childList && node.childList.length > 0) {
          searchNodes(node.childList, name);
        }
      });
    }

    // 从顶级节点开始搜索
    searchNodes(originTreeData.value, name);

    return result;
  }

  // 标准树搜索
  const searchHandle = () => {
    if (searchKey.value) {
      const foundNodes = findNodesByName(searchKey.value);
      treeData.value = foundNodes;
    } else {
      treeData.value = originTreeData.value;
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'StandardTree',
    inheritAttrs: false,
  };
</script>

<style lang="less" scoped>
  .standTree {
    width: 300px;
    padding: 20px 6px 20px 16px;
    height: 100%;
  }
  .tree-box {
    height: calc(100% - 30px);

    ::-webkit-scrollbar {
      width: 6px;
    }
  }
  :deep(.arco-tree) {
    // height: 100%;
    overflow-y: auto;

    .arco-space .arco-space-item {
      margin-bottom: 0;
    }
  }

  .arco-icon {
    opacity: 0;
  }

  :deep(.arco-tree-node) {
    .arco-tree-node-title {
      flex: 1;
    }

    .arco-btn-size-medium {
      padding: 0;
    }
  }

  :deep(.arco-space-item) {
    margin: 0;
  }
  :deep(.arco-tree) {
    height: 100%;
  }

  :deep(.arco-tree-node:hover),
  :deep(.arco-tree-node-selected) {
    background-color: #e8f2ff;

    .arco-tree-node-title {
      background-color: #ebf0ff;
    }
    .arco-space {
      box-sizing: border-box;
      padding-right: 6px;
    }
    .btn-box {
      background-color: #ebf0ff;
    }

    .arco-icon {
      opacity: 1;
    }
  }
  :deep(.arco-tree-node-title-text span) {
    font-size: 14px;
    color: #4e5969;
    vertical-align: middle;
  }
  :deep(.arco-tree-node-title-text .name) {
    display: inline-block;
    max-width: 180px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    // margin-right: 4px;
  }
  :deep(.arco-spin) {
    height: 100%;
    width: 100%;
  }
  .btn-box {
    display: inline-block;
    width: 44px;
    white-space: nowrap;
    :deep(.arco-btn) {
      margin-left: 4px;
    }
  }
</style>
