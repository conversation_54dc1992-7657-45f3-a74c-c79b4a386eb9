<template>
  <a-modal
    :visible="visible"
    :width="600"
    :title="t('cloud.import-from-knowledge-base')"
    title-align="start"
    :ok-text="t('knowledgenew.ok-text')"
    :mask-closable="false"
    :unmount-on-close="true"
    :ok-loading="btnLoading"
    :esc-to-close="false"
    class="import-file-dialog"
    @before-open="initData"
    @cancel="cancel"
    @ok="submitData"
  >
    <a-tabs v-model:active-key="activeTabKey" @change="changeTabs">
      <template #extra>
        <a-select
          v-if="activeTabKey === 2"
          v-model="sharedKnowledgeBaseId"
          allow-search
          :placeholder="t('cloud.import-placeholder')"
          @change="handleChange"
        >
          <a-option
            v-for="item in sharedKnowledgeBase"
            :key="item.id"
            :value="item.id"
          >
            {{ item.name }}
          </a-option>
        </a-select>
      </template>
      <a-tab-pane :key="1" :title="t('knowledgenew.personal-base')">
      </a-tab-pane>
      <a-tab-pane :key="2" :title="t('knowledgenew.shared-base')"> </a-tab-pane>
    </a-tabs>
    <a-input-search
      v-if="searchFileIsShow"
      v-model="searchValue"
      size="small"
      :placeholder="t('knowledgenew.search-placeholder')"
      allow-clear
      @blur="searchFile"
      @press-enter="($event.target as any)?.blur()"
      @search="searchFile"
      @clear="searchFile"
    />
    <div class="opt-line">
      <a-breadcrumb :max-count="3">
        <template #separator>
          <icon-right />
        </template>

        <a-breadcrumb-item
          v-for="(item, index) in currentPath"
          :key="item.id"
          class="has-pointer"
          @click="jumpToFolder(item, index)"
          >{{ item.name }}
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <a-table
      :columns="columns"
      :data="tableData"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      row-key="id"
      :loading="tableLoading"
      :pagination="false"
      :bordered="false"
      :scroll="{ x: '100%', y: '327px' }"
      @selection-change="selectionChange"
    >
      <template #name="{ record }">
        <div class="table-name">
          <FileImage
            :file-name="record.name ?? ''"
            :is-sys-file="false"
            :is-file="!record.isFolder"
            style="margin-right: 8px; width: 22px"
          />
          <a-tooltip :content="record.name">
            <span
              style="
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
              class="file-name"
              @click="onSelect(record)"
              >{{ record.name }}</span
            >
          </a-tooltip>
        </div>
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, defineEmits, defineProps, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { useKnowledgeBaseStore2 } from '@/store';
  import { PathNode } from '@/store/modules/knowledge-base/types';
  import { getUserId } from '@/utils/auth';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import {
    getChildFolderList,
    getFileList,
    FileAndFolderMessage,
  } from '@/api/tree-folder';

  // import { uploadFile, createBase } from '../../api';
  // import { getPrivateInit } from '../api';
  // import { KnowledgeBaseRecord } from '../../types';
  import { getFolderList, getBaseList, getSearchFile } from '../api';
  import { importFile } from './api';
  import { Message } from '@arco-design/web-vue';
  import { useKnowledgeBaseNewStore } from '@/store';
  import { storeToRefs } from 'pinia';
  import useFolderContent from '../composables/useFolderContent';

  const { querySearchFile } = useFolderContent();
  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { personal } = storeToRefs(knowledgeBaseNewStore);

  const { t } = useI18n();
  const userId = getUserId() || '';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
    },
    type: {
      type: String,
      default: 'add',
    },
  });
  const emits = defineEmits(['update:visible', 'submit']);

  const knowledgeBaseStore2 = useKnowledgeBaseStore2(); // 知识库
  const { folderId, history } = storeToRefs(knowledgeBaseStore2);

  const btnLoading = ref(false);
  const tableLoading = ref(false);
  const resultCount = ref(0);

  // 标签页
  const activeTabKey = ref(1);
  // 搜索
  const searchValue = ref('');

  // 个人知识库
  const personKnowledgeBase = ref<any[]>([]);
  // 共享知识库
  const sharedKnowledgeBase = ref<any[]>([]);

  const sharedKnowledgeBaseId = ref<string>('');
  // 当前显示路径
  const currentPath = ref<any[]>([]);
  const searchFileIsShow = computed(() => {
    return (
      activeTabKey.value === 1 ||
      (activeTabKey.value === 2 && sharedKnowledgeBaseId.value)
    );
  });

  // 初始化个人网盘信息（个人网盘相当于一个项目下的文件管理）
  async function initKnowledgeBase() {
    try {
      const res = await getPrivateInit();
      personalProjectId.value = res.data.projectId;
      personalRootFolder.value = {
        id: res.data.id,
        projectId: res.data.projectId,
        type: res.data.type,
        parentId: res.data.parentId,
        name: res.data.name,
      };
      knowledgeBaseStore2.openFolder({
        id: personalRootFolder.value.id,
        name: personalRootFolder.value.name,
      });
    } catch (e) {
      console.error('获取projectId失败', e);
    }
  }

  // table数据,个人知识库
  const folderList = ref<FileAndFolderMessage[]>([]);
  const fileList = ref<FileAndFolderMessage[]>([]);
  const currentFolderId = ref('');
  // 选中的文件
  const selectedKeys = ref<string[]>([]);
  const tableData = computed(() => {
    return [...folderList.value, ...fileList.value];
  });
  const selectionChange = (rowkeys: string[]) => {
    console.log('选中的行keys', rowkeys);
    selectedKeys.value = rowkeys;
    // 找到tableData里面，把selected设置为true
  };

  // 查询子文件夹
  const getPersonalFolder = async () => {
    tableLoading.value = true;
    const res = await getChildFolderList(
      personalProjectId.value,
      '',
      'NETDISK',
      folderId.value
    );
    if (res.status) {
      const list = (res.data?.list || []).map((item) => ({
        ...item,
        selected: false,
      }));
      folderList.value = list;
    }
    resultCount.value++;
    if (resultCount.value === 2) {
      tableLoading.value = false;
    }
  };

  // 查询子文件
  const getFiles = async () => {
    tableLoading.value = true;
    const res: any = await getFileList(folderId.value);
    if (res.status) {
      const list = (res.data?.list || []).map((item: any) => ({
        ...item,
        selected: false,
      }));
      fileList.value = list;
    }
    resultCount.value++;
    if (resultCount.value === 2) {
      tableLoading.value = false;
    }
  };
  // 查询知识库信息
  const getKnowledgeBaseInfo = async () => {
    try {
      const res = await getBaseList({
        pageParam: { pageNo: 1, pageSize: 1000 },
      });

      if (res.status) {
        // 个人知识库
        personKnowledgeBase.value = res.data.list.filter(
          (item: any) => item.type === 'PERSONAL'
        );
        currentFolderId.value =
          personKnowledgeBase.value.length > 0
            ? personKnowledgeBase.value[0].folderVO.id
            : '';
        currentPath.value = personKnowledgeBase.value.map((item: any) => ({
          id: item.folderVO.id,
          name: item.folderVO.name,
        }));
        sharedKnowledgeBase.value = res.data.list
          .filter((item: any) => item.type === 'SHARED')
          .map((item: any) => item);
      }
    } catch (err) {
      console.error(err);
    }
  };
  const getFolderListHandle = () => {
    const params = {
      folderId: currentFolderId.value,
      fullTree: false,
    };
    getFolderList(params).then((res) => {
      folderList.value = (res.data?.children || []).map((item: any) => ({
        ...item,
        isFolder: true,
      }));

      fileList.value = (res.data?.fileList || []).map((item: any) => ({
        ...item,
        isFolder: false,
      }));
    });
  };
  // 搜索文件
  const searchFile = async () => {
    selectedKeys.value = [];
    console.log(personKnowledgeBase.value, '个人知识库');
    if (searchValue.value.trim()) {
      fileList.value = await querySearchFile({
        fileName: searchValue.value,
        kbId:
          activeTabKey.value === 1
            ? personKnowledgeBase.value[0]?.id
            : sharedKnowledgeBaseId.value,
      });
      folderList.value = [];
    } else {
      getFolderListHandle();
    }
  };
  // 查询个人知识库文件
  const initPersonalKnowledgeFolder = async () => {
    await getKnowledgeBaseInfo();
    getFolderListHandle();
  };

  // 切换标签页
  const changeTabs = (key: number) => {
    currentPath.value = [];
    if (key === 1) {
      initPersonalKnowledgeFolder();
    } else {
      folderList.value = [];
      fileList.value = [];
      currentPath.value = [];
      // initProjectFolder();
    }
  };
  // 选择共享知识库
  const handleChange = (id: string) => {
    sharedKnowledgeBase.value.forEach((item: any) => {
      if (item.id === id) {
        currentFolderId.value = item.folderVO.id;
        currentPath.value = [
          {
            id: item.folderVO.id,
            name: item.folderVO.name,
          },
        ];
      }
    });
    getFolderListHandle();
  };
  // 数据初始化
  const initData = () => {
    initPersonalKnowledgeFolder();
  };

  // 点击面包屑的路径
  const jumpToFolder = (item: PathNode, index: number) => {
    currentPath.value = currentPath.value.slice(0, index + 1);
    currentFolderId.value = item.id;

    getFolderListHandle();
  };

  const columns = computed(() => {
    return [
      {
        title: t('file-manage.name'),
        dataIndex: 'name',
        slotName: 'name',
      },
    ];
  });

  // 点击进入下一文件夹
  const onSelect = (item: any) => {
    if (item.isFolder) {
      currentFolderId.value = item.id;
      currentPath.value.push(item);
      getFolderListHandle();
    } else {
      return;
    }
    console.log('点击进入下一文件夹', item);
    //
  };

  // 提交数据
  const submitData = async () => {
    try {
      btnLoading.value = true;
      const sourceFileIdList = fileList.value
        .filter((folder: any) => selectedKeys.value.includes(folder.id))
        .map((folder: any) => folder.id); // 返回 id 组成的数组
      const sourceFolderIdList = folderList.value
        .filter((folder: any) => selectedKeys.value.includes(folder.id))
        .map((folder: any) => folder.id); // 返回 id 组成的数组

      const params = {
        sourceFileIdList,
        sourceFolderIdList,
        targeFolderId: personal.value.currentFolder.id,
        targeProjectId: personal.value.currentFolder.projectId,
      };
      const submitRes = await importFile(params);
      if (submitRes.status) {
        Message.success('从知识库导入成功！');
        emits('update:visible', false);
        activeTabKey.value = 1; // 重置标签页为个人知识库
        searchValue.value = ''; // 清空搜索框
        knowledgeBaseNewStore.getPersonalFolder('personal');
        knowledgeBaseNewStore.getfiles('personal');
      }
      btnLoading.value = false;
    } catch (err) {
      btnLoading.value = false;
    }
  };

  const cancel = () => {
    activeTabKey.value = 1; // 重置标签页为个人知识库
    searchValue.value = ''; // 清空搜索框
    // 当前显示路径
    currentPath.value = [];
    emits('update:visible', false);
  };
</script>

<style scoped lang="less">
  .opt-line {
    padding: 16px 0;
    height: 64px;
    display: flex;
    align-items: center;
  }

  .table-name {
    display: flex;
  }

  :deep(.arco-tabs) {
    height: 100%;
  }
  :deep(.arco-tabs-content) {
    height: calc(100% - 40px);
    padding-top: 0;
  }
  :deep(.arco-tabs-tab) {
    font-size: 16px;
  }
  :deep(.arco-tabs-nav-type-line .arco-tabs-tab-title) {
    padding: 0;
    line-height: 24px;
  }
  :deep(.arco-tabs-tab-active, .arco-tabs-tab-active:hover) {
    color: #3366ff;
  }
  :deep(.arco-tabs-nav-type-line .arco-tabs-tab) {
    margin: 0 8px;
  }
  :deep(.arco-tabs-tab:not(:first-of-type)) {
    margin-left: 24px;
  }
</style>

<style lang="less">
  .import-file-dialog {
    .arco-modal-header {
      height: 52px;
    }
    .arco-modal-title {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
      line-height: 28px;
    }
    .arco-modal-body {
      padding: 8px 20px 20px;
    }

    // 搜索框样式覆盖
    .arco-input-wrapper {
      margin-top: 16px;
      background-color: #fff;
      border: 1px solid #c9cdd4 !important;
      border-radius: var(--border-radius-medium);
    }
  }
</style>
