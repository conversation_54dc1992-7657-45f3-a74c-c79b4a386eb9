<template>
  <div class="container">
    <a-row :gutter="16" style="margin-bottom: 16px">
      <a-col :flex="1">
        <table-title
          :title="$t('prjMember.unactivated-member-list')"
        ></table-title>
      </a-col>
    </a-row>
    <!-- <a-row :gutter="16">
      <a-col :flex="1">
        <a-form :model="formModel" label-align="left">
          <a-row :gutter="16">
            <a-col flex="312px">
              <a-form-item
                field="name"
                :label="$t('prjMember.search.name')"
                label-col-flex="60px"
              >
                <a-input
                  v-model="formModel.name"
                  :placeholder="$t('prjMember.search.name.placeholder')"
                  @keyup.enter="search"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <a-col :flex="'128px'" style="text-align: right">
        <a-space :size="8">
          <a-button type="outline" @click="search">
            <template #icon>
              <icon-search />
            </template>
            {{ $t('list.options.btn.search') }}</a-button
          >
          <a-button type="outline" @click="reset">
            <template #icon><icon-loop /> </template
            >{{ $t('list.options.btn.reset') }}</a-button
          >
        </a-space>
      </a-col>
    </a-row>
    <a-divider margin="20px 0 20px" /> -->

    <!-- <a-space>
      <a-button
        type="primary"
        style="margin-bottom: 16px"
        v-permission="$btn.team.invitateMember"
        @click="produceLinkHandle"
        >{{ $t('prjMember.table.opt.invite.register') }}</a-button
      >
      <a-button
        v-permission="$btn.team.importMember"
        type="outline"
        style="margin-bottom: 16px"
        @click="importDialogVisible = true"
        >{{ $t('prjMember.table.opt.import') }}</a-button
      >
    </a-space> -->

    <div class="table-box">
      <a-table
        stripe
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="columns"
        :data="renderData"
        :scroll="scroll"
        :bordered="false"
        table-layout-fixed
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #phone="{ record }">
          {{ phoneDesensitize(record.phone) }}
        </template>
        <template #email="{ record }">
          {{ emailDesensitize(record.email) }}
        </template>
        <template #userFullname="{ record }">
          <span class="blue-link" @click="viewHandle(record)">{{
            record.userFullname
          }}</span>
        </template>

        <template #status="{ record }">
          <span
            v-if="record.status === 1"
            class="accountState"
            style="color: #3c7eff"
          >
            <span
              ><a-badge color="#3C7EFF" />{{ $t('prjMember.successful') }}</span
            >
          </span>

          <span
            v-else-if="record.status === 2"
            class="accountState"
            style="color: #ff7d00"
          >
            <a-tooltip :content="record.errorText">
              <span
                ><a-badge color="#FF7D00" />{{ $t('prjMember.failure') }}</span
              >
            </a-tooltip>
          </span>
        </template>
        <template #operations="{ record }">
          <a-button type="text" size="small" @click="editHandle(record)">{{
            $t('table.opt.edit')
          }}</a-button>

          <a-button
            type="text"
            size="small"
            status="danger"
            @click="deleHandle(record)"
            >{{ $t('table.opt.remove') }}</a-button
          >

          <!-- <a-button
            type="text"
            size="small"
            status="danger"
            @click="deleHandle(record.userId)"
            >删除</a-button
          > -->
          <!-- <a-button type="text" size="small" @click="spendHandle(record.userId)"
            >发送链接</a-button
          > -->

          <a-popconfirm
            :content="$t('prjMember.confirm-send-link')"
            @ok="spendHandle(record)"
            @click.stop
          >
            <a-button type="text" size="small">{{
              $t('prjMember.send-link')
            }}</a-button>
          </a-popconfirm>
        </template>
      </a-table>
    </div>

    <invitedMember
      v-if="inviteVisible"
      v-model:visible="inviteVisible"
      :type="optionType"
      :data="invitedData"
      @refresh="search"
    />

    <memberDetail
      v-if="memeberVisible"
      v-model:visible="memeberVisible"
      :title="dialogTitle"
      :form="selectForm"
      :user-id="userId"
      :disabled="disabled"
      @refresh="search"
    />

    <UploadMembers
      v-if="importDialogVisible"
      v-model="importDialogVisible"
      @refresh="search"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, inject } from 'vue';
  import { useRoute } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import useLoading from '@/hooks/loading';
  import {
    getInvitedRecord,
    MemberListParams,
    MemberRecord,
    invitationDel,
    invitationGenerate,
  } from '@/api/member';

  import { Pagination } from '@/types/global';
  import { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import TableTitle from '@/components/table-title/index.vue';
  import { usePrjPermissionStore, useUserStore } from '@/store';
  import invitedMember from './components/invited-member.vue';
  import memberDetail from '../member-list/components/memberDetail.vue';
  import { Message } from '@arco-design/web-vue';
  import UploadMembers from './components/upload-members.vue';
  import Modal from '@arco-design/web-vue/es/modal';

  const importDialogVisible = ref(false);

  const scroll = {
    x: '100%',

    y: '100%',
  };

  type Column = TableColumnData & { checked?: true; userId?: string };

  const { t, locale } = useI18n();
  // 国际化类型
  const prjPermissionStore = usePrjPermissionStore();
  const userStore = useUserStore();
  // 当前用户id
  const userId = computed(() => {
    return userStore.id;
  });
  const { isPrjAdmin, teamList } = prjPermissionStore;

  // 列表表格展示
  const generateFormModel = () => {
    const initData: MemberRecord = {
      name: undefined,
      accountState: undefined,
    };
    return initData;
  };
  const { loading, setLoading } = useLoading(true);
  const renderData = ref<MemberRecord[]>([]);
  const formModel: any = ref(generateFormModel());
  const columns = computed<Column[]>(() => {
    const constColumn = [
      {
        title: t('prjMember.column.index'),
        dataIndex: 'index',
        slotName: 'index',
        width: 62,
        align: 'left',
      },
      {
        title: t('prjMember.column.name'),
        dataIndex: 'userFullname',
        slotName: 'userFullname',
        align: 'left',
      },
      {
        // accountState 1,"账号已激活";2,"账号未激活";3、"账号注销”；4:"账号未审批"
        title: t('prjMember.invitation-status'),
        dataIndex: 'status',
        slotName: 'status',
        width: 120,
        align: 'left',
      },
      {
        title: t('prjMember.column.phone'),
        dataIndex: 'phone',
        slotName: 'phone',
        align: 'left',
      },
      {
        title: t('prjMember.column.email'),
        dataIndex: 'email',
        slotName: 'email',
        align: 'left',
      },
      {
        title: t('prjMember.invitation-time'),
        width: 120,
        dataIndex: 'createDate',
        slotName: 'createDate',
        align: 'left',
      },
    ];
    return (
      isPrjAdmin
        ? [
            ...constColumn,
            {
              title: t('prjMember.column.opt'),
              dataIndex: 'operations',
              slotName: 'operations',
              width: 200,
              align: 'left',
            },
          ]
        : constColumn
    ) as Column[];
  });
  const basePagination: Pagination = {
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
  };
  const pagination = reactive({
    ...basePagination,
  });

  // 手机号脱敏
  const phoneDesensitize = (phone: string): string => {
    const reg = /(?<=\d{3})\d{4}(?=\d{4})/gi;
    return phone.replace(reg, '****');
  };

  // 邮箱号脱敏
  const emailDesensitize = (email: string): string => {
    const emailArr = email.split('@');
    const strStart = email.slice(0, 3);
    let midStr = '';
    for (let i = 0; i < emailArr[0].length - 3; i++) {
      midStr += '*';
    }
    const newEmail = `${strStart}${midStr}@${emailArr[1].toString()}`;
    return newEmail;
  };

  // 列表数据相关
  const route = useRoute();
  const projectId = ref<string>((route.params?.projectId as string) || '');
  const fetchData = async (
    params: MemberListParams = { pageNo: 1, pageSize: 20 }
  ) => {
    setLoading(true);
    try {
      const { current, ...rest } = params;
      const apiParams = {
        ...rest,
        pageNo: current || 1,
        projectId: projectId.value,
        userFullName: parentFormModel?.value?.name,
      };
      const { data } = await getInvitedRecord(apiParams);
      // 失败原因
      data.list.forEach((item: any) => {
        const errorText = item.error ? JSON.parse(item.error) : [];
        item.errorText = errorText.join(',');
      });
      renderData.value = data.list || [];
      pagination.current = apiParams.pageNo;
      pagination.pageSize = apiParams.pageSize;
      pagination.total = data.total;
    } catch (err) {
      // you can report use errorHandler or other
    } finally {
      setLoading(false);
    }
  };  
  // 获取父组件提供的 formModel
  const parentFormModel: any = inject('parentFormModel');
  
  // 修改 search 方法
  const search = () => {
    console.log('触发子组件search方法');
    console.log('触发子组件search方法', parentFormModel?.value);
    console.log('触发子组件search方法', parentFormModel?.value?.name);
  // search时重新回到第一页
  const { pageSize } = pagination;
  fetchData({
    pageSize,
    current: 1,
    userFullName: parentFormModel?.value?.name,
  });
  };

 const changeImportDialogVisible= () => {
  importDialogVisible.value = true;
 }

  const reset = () => {
    console.log("触发zi2组件的clear")
    formModel.value = generateFormModel();
    const { pageSize } = pagination;
    fetchData({ pageSize, current: 1 });
  };
  const onPageChange = (current: number) => {
    const { pageSize } = pagination;
    fetchData({ pageSize, current, ...formModel.value });
  };
  const onPageSizeChange = (pageSize: number) => {
    fetchData({ current: 1, pageSize, ...formModel.value });
  };
  fetchData();

  // 功能按钮相关
  const disabled = ref(false);
  const inviteVisible = ref(false);
  const memeberVisible = ref(false);
  const dialogTitle = ref('');
  const defaultSelected: MemberRecord = {
    username: '',
    name: '',
    email: '',
    phone: '',
    role: '',
    projectAdmin: 0,
    moduleVisible: '0,1',
  };
  const selectForm = ref<MemberRecord>({
    ...defaultSelected,
  });

  const detail = (record: MemberRecord) => {
    dialogTitle.value = t('prjMember.detail.title');
    disabled.value = true;
    memeberVisible.value = true;
    selectForm.value = { projectId: projectId.value, ...record };
    selectForm.value.name = record.userFullname;
  };

  const optionType = ref('create');
  const invitedData = ref({});
  // 邀请注册
  const produceLinkHandle = () => {
    optionType.value = 'create';
    inviteVisible.value = true;
  };
  // 编辑
  const editHandle = (record: any) => {
    optionType.value = 'edit';
    invitedData.value = record;
    inviteVisible.value = true;
  };
  // 查看
  const viewHandle = (record: any) => {
    optionType.value = 'view';
    invitedData.value = record;
    inviteVisible.value = true;
  };

  // const removeMembers = (id: string) => {
  //   // 禁止自己移除自己时
  //   if (id === userId.value) {
  //     Message.warning(t('prjMember.table.opt.remove.self'));
  //   }
  // };
  // 删除
  const deleHandle = async (record: any) => {
    // const res = await invitationDel(record.id);
    Modal.warning({
      title: t('prjMember.table.opt.remove'),
      content: t('prjMember.table.opt.remove.confirm'),
      closable: true,
      hideCancel: false,
      onOk: async () => {
        const res = await invitationDel(record.id);
        if (res.status) {
          Message.success(t('prjMember.table.opt.remove.success'));
          search();
        }
      },
    });
    // if (res.status) Message.success('删除成功');
  };
  // 发送链接
  const spendHandle = async (record: any) => {
    const params: any = {
      projectId: route.params?.projectId,
      days: 7,
      registerUrl: `${window.location.origin}/work/login?isRegister=1`,
      userInfoList: [
        {
          email: record.email,
          name: record.userFullname,
          phone: record.phone,
        },
      ],
    };
    const res = await invitationGenerate(params);
    if (res.status) {
      Message.success(
        `${t('prjMember.activation-link')} ${res.data.success} ${t(
          'prjMember.sent-successfully'
        )}, ${res.data.failure} ${t('prjMember.send-failure')}`
      );
      search();
    }
  };

  defineExpose({
    search,
    reset,
    produceLinkHandle,
    changeImportDialogVisible,
  });
</script>

<script lang="ts">
  export default {
    name: 'ProjectMember',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 20px;
    height: 100%;
  }
  .table-box {
    height: calc(100% - 40px);
    display: flex;
    justify-content: space-between;
    gap: 20px;
    :deep(.arco-table-container) {
      height: 100%;
    }
  }
  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
  :deep(.arco-btn-size-small) {
    padding: 0 6px;
  }
  :deep(.arco-link:hover) {
    background-color: transparent;
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }
  .blue-link {
    color: rgb(var(--primary-6));
    cursor: pointer;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .setting {
    display: flex;
    align-items: center;
    width: 200px;
    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }
  .mini-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
    margin-bottom: 2px;
  }
  .danger-text {
    color: rgb(var(--red-6));
    &:hover {
      color: rgb(var(--red-6));
    }
  }
  :deep(.arco-badge-status-dot) {
    margin-right: 4px;
  }
  .accountState {
    text-align: left;
  }
  :deep(.arco-form-item) {
    margin-bottom: 0;
  }
  :deep(.arco-select-view) {
    background-color: #fff;
    border: 1px solid #c9cdd4 !important;
  }

  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    color: #1d2129;
  }
</style>
