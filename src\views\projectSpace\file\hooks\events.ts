import { batchDownloadInfo, fileDownload, fileZipDownload } from '@/api/file';
import { Message, Notification } from '@arco-design/web-vue';
import i18n from '@/locale';
import { download } from '@/utils/file';
import { FileAndFolderNodeMessage, FolderMessage } from '@/api/tree-folder';
import { shareFileDownload } from '@/views/share-download/api';

const { t } = i18n.global;

// 下载单文件
export async function handleSingleFile(file: any) {
  const res: any = await fileDownload(file);
  if (file.fileToken) {
    download(file, res.data);
  } else {
    Message.error('未获取到文件参数');
  }
}
// 下载压缩包
export async function handleZipFile(fileList: any) {
  // 获取批量下载文件信息
  const res: any = await batchDownloadInfo({ fileList });
  if (res.status) {
    const { data } = res;
    let zipFileName = `${t('file-manage.batch-download')}.zip`;

    const downloadRes: any = await fileZipDownload(data);
    // 获取文件名称
    const disposition = downloadRes.headers['content-disposition'];
    if (disposition && disposition.includes('filename=')) {
      const [, encodeFilename] = disposition.split('filename=');
      zipFileName = decodeURI(encodeFilename);
    }
    download({ name: zipFileName }, downloadRes.data);
  } else {
    Message.error(res.message);
  }
}

export function downloadSource(crtFile: FileAndFolderNodeMessage) {
  Message.info(t('file-manage.wait-downloading'));

  if (crtFile.folderId) {
    // 文件
    handleSingleFile(crtFile).then();
  } else {
    // 文件夹
    const files = [{ fileId: crtFile.id, fileType: crtFile.isFileOrFolder }];
    handleZipFile(files).then();
  }
  return crtFile;
}

export async function batchDownload(record: any) {
  Message.info(t('file-manage.wait-downloading'));

  // 只选了一个文件，调用下载文件接口
  if (record.length === 1 && record[0].fileType === 1) {
    await shareFileDownload(record[0].fileName, record[0].fileToken);
    return;
  }
  // 批量下载
  const fileList = record.map((item: any) => {
    return {
      fileId: item.fileId,
      fileType: item.fileType,
    };
  });

  handleZipFile(fileList);
}

/**
 * 根据父文件夹id获取父文件夹
 */
export function getParentFolder(
  treeData: any,
  folderId: any,
  parentFolder: any
) {
  let parent: any;
  if (treeData.length) {
    treeData.forEach((ele: any) => {
      if (ele.id === folderId) {
        parent = parentFolder;
      } else if (!parent && ele.children?.length)
        parent = getParentFolder(ele.children, folderId, ele);
    });
  }
  return parent;
}

function parseFolder(treeData: any, folderId: string) {
  let result: any;
  treeData.forEach((e: any) => {
    const folder = getParentFolder(e.children, folderId, e);
    if (folder) result = folder;
  });
  return result;
}

// /** 文件/文件夹单个作废事件 */
// export function Nullify(row: any) {
//   let contentText = ''; // 需要国际化
//   if (row.abandon === 1) {
//     contentText = '取消作废成功';
//   } else {
//     contentText = '作废成功';
//   }
//   fileNullify([row.id]).then((res: any) => {
//     if (res.code === 8000000) {
//       Notification.success({
//         id: 'nullify',
//         title: 'Success',
//         content: contentText,
//       });
//     }
//   });
// }
