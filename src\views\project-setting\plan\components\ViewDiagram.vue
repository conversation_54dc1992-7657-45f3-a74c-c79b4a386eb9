<template>
  <a-modal :visible="visible" @before-ok="handleBeforeOk" width="800px">
    <template #title>{{ $t('project-setting.view-diagram') }}</template>
    <div
      id="main"
      style="
        height: 600px;
        width: 760px;
        border: 1px solid #ebebeb;
        border-radius: 2px;
      "
    >
      <vue3-tree-org
        :data="data"
        :horizontal="horizontal"
        :collapsable="collapsable"
        :label-style="style"
        :node-draggable="false"
        :scalable="true"
        :default-expand-level="1"
      >
        <template v-slot="{ node }">
          <div class="tree-org-node__text node-label">
            <div class="custom-content">{{ $t('project-setting.custom-content') }}</div>
            <div>{{ $t('project-setting.node-name') }}：{{ node.label }}</div>
            <div>{{ $t('project-setting.name') }}：{{ node.$$data.name }}</div>
          </div>
        </template>
        <!-- 自定义展开按钮 -->
        <template v-slot:expand="{ node }">
          <div>{{ node.children.length }}</div>
        </template>
      </vue3-tree-org>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:visible']);

  const handleBeforeOk = () => {
    emit('update:visible', false);
    return true;
  };
  const data = {
    id: 1,
    label: 'xxx科技有限公司',
    name: '111',
    children: [
      {
        id: 2,
        pid: 1,
        name: '222',
        label: '产品研发部',
        children: [
          { id: 6, pid: 2, label: '禁止编辑节点', disabled: true },
          { id: 8, pid: 2, label: '禁止拖拽节点', noDragging: true },
          { id: 10, pid: 2, label: '测试' },
        ],
      },
      {
        id: 3,
        pid: 1,
        label: '客服部',
        children: [
          { id: 11, pid: 3, label: '客服一部' },
          { id: 12, pid: 3, label: '客服二部' },
        ],
      },
      { id: 4, pid: 1, label: '业务部' },
    ],
  };
  const horizontal = true;
  const collapsable = true;
  const style = {
    background: '#fff',
    color: '#5e6d82',
  };
</script>

<style lang="less" scoped>
  .tree-org-node__text {
    width: 100px;
  }
</style>
