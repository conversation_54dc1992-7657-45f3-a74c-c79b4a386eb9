{"compilerOptions": {"module": "ES2020", "target": "ES2020", "moduleResolution": "node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "lib": ["es2021", "dom"], "skipLibCheck": true, "typeRoots": ["./node_modules/@types", "src/env.d.ts", "src/global.d.ts", "components.d.ts"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/types/**/*.d.ts"], "exclude": ["node_modules"]}