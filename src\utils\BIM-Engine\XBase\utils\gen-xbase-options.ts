import config from '@/config/xbase-config.json';
import { modelViewToken } from '@/utils/xbase/api';
import { parseViewerType } from './index';
import { parseViewerEngine } from './engine-options';

const getViewerToken = async (fileId: string, viewerType: string) => {
  // model（模型）、component（构件）、asset（资产）、scene（场景）
  const token = await modelViewToken({
    file_id: fileId,
    viewer_type: viewerType,
  });
  return {
    Authorization: `Bearer ${token.data.token}`,
  };
};

// 生成大象云初始化的options
const genXBaseOptions = async (
  file: any,
  defaultViewerType: string,
  type: string
) => {
  let [fileId, renderPath, semanticModel] = ['', '', ''];
  // 碰撞检测预览
  if (type === 'collision') {
    const { modelInfo, clashModelId } = JSON.parse(file.graphicEngineInfo);
    const graphicEngineInfo = JSON.parse(file.graphicEngineInfo);
    renderPath = graphicEngineInfo.renderPath || modelInfo.render_path;
    semanticModel = graphicEngineInfo.renderPath
      ? clashModelId
      : modelInfo.semantic_model_id;
  } else {
    // 其他模型预览
    const { graphicEngineInfo } = file;
    const arr = graphicEngineInfo.split('|');
    [fileId, renderPath, semanticModel] = arr;
    // 当存在语义模型ID时，使用语义模型预览
  }
  let viewerType =
    defaultViewerType ||
    (semanticModel ? 'semanticModel' : parseViewerType(file.name));
  // todo 目前只对语义模型做了特殊判断，其他都默认使用转换后的模型ID, 后续如果要拓展，需在此处做兼容处理

  // 此处为了兼容10月8日之前创建的语义模型，使用模型预览
  const timestamp = new Date(file.updateDate).getTime(); // 当前模型的创建时间
  const specificDate = new Date(Date.UTC(2024, 9, 8, 17, 0, 0)).getTime(); // 创建表示2024年10月8日下午五点的Date对象
  if (viewerType === 'semanticModel' && timestamp < specificDate) {
    viewerType = 'model';
  }

  const accessToken = await getViewerToken(
    viewerType === 'semanticModel' ? semanticModel : fileId,
    viewerType
  );

  // 获取渲染类型 engine 的值
  const viewerEngine = parseViewerEngine(file.name);
  const options = {
    staticHost: `${window.origin}${config.staticHost}`,
    // 设置服务域的地址
    serverHost: `${window.origin}/x_base_api/viewer`,
    // 添加token
    accessToken,
    engine: viewerEngine,
  };
  return options;
};

export default genXBaseOptions;
