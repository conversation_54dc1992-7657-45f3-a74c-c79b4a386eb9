import { createRouter, createWebHistory } from 'vue-router';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css';
import type { RouteRecordRaw } from 'vue-router';

import { workRoutes, projectRoutes } from './routes';
import { NOT_FOUND_ROUTE } from './routes/base';
import createRouteGuard from './guard';
import { useUserStore } from '@/store';
import { getUserName } from '@/utils/auth';

NProgress.configure({ showSpinner: false }); // NProgress Configuration

// @ts-ignore
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: () => {
      const username = getUserName();
      const redirectPath = username ? '/dashboard' : '/login';
      return { path: redirectPath };
    },
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      requiresAuth: false,
      allowProjectTemplate: true,
    },
    props: (route) => ({
      isRegister: route.query.isRegister,
      companyCode: route.query.companyCode,
      inviteCompany: route.query.inviteCompany,
      invitationId: route.query.invitationId,
    }),
  },
  // {
  //   path: '/bpmn-create',
  //   name: 'bpmnCreate',
  //   component: () =>
  //     import('@/views/project-setting/process/bpmn-create.vue'),
  //   meta: {
  //     requiresAuth: false,
  //   },
  // },
  {
    path: '/share-download',
    name: 'shareDownload',
    component: () => import('@/views/share-download/index_new.vue'),
    meta: {
      requiresAuth: false,
      allowProjectTemplate: true,
      name: 'shareDownload',
    },
  },
  {
    path: '/share-knowledge',
    name: 'shareKnowledge',
    component: () =>
      import('@/views/knowledge-base-new/share-knowledge/index.vue'),
    meta: {
      requiresAuth: false,
      allowProjectTemplate: true,
      name: 'shareKnowledge',
    },
  },
  {
    path: '/base-layout',
    name: 'baseLayout',
    component: () => import('@/layout/base-layout.vue'),
    meta: {
      requiresAuth: false,
    },
    children: [
      {
        path: '/home-page',
        name: 'homePage',
        component: () => import('@/views/home-page/index.vue'),
        meta: {
          requiresAuth: true,
          allowProjectTemplate: true,
        },
      },
      {
        path: '/approve-apply',
        name: 'approveApply',
        component: () => import('@/views/approve-apply/index.vue'),
        meta: {
          requiresAuth: false,
          allowProjectTemplate: true,
        },
      },
    ],
  },
  {
    path: '/layout',
    name: 'layout',
    component: () => import('@/layout/menu-layout.vue'),
    meta: {
      requiresAuth: false,
    },
    children: [
      ...workRoutes,
      {
        path: '/project/:projectId',
        name: 'project',
        component: () => import('@/layout/page-layout.vue'),
        meta: {
          requiresAuth: false,
          allowProjectTemplate: false,
        },
        children: [...projectRoutes],
      },
      {
        path: '/setup',
        name: 'setup',
        component: () => import('@/views/set-up/index.vue'),
        meta: {
          hideInMenu: true,
          requiresAuth: true,
          allowProjectTemplate: true,
        },
      },
    ],
  },
  {
    path: '/bim-view',
    name: 'bimView',
    component: () => import('@/views/bim-view/index.vue'),
    meta: {
      requiresAuth: true,
      // allowProjectTemplate: true,
    },
  },
  // 标准校验结果
  {
    path: '/standardResult',
    name: 'standardResult',
    component: () => import('@/views/standard-manage/standardResult/index.vue'),
    meta: {
      locale: 'menu.result',
      requiresAuth: true,
      roles: ['*'],
      hideInMenu: true,
      // allowProjectTemplate: true,
    },
  },
  {
    path: '/bim-view/compare',
    name: 'bimCompare',
    component: () => import('@/views/bim-view/XBase/version-compare/index.vue'),
    meta: {
      requiresAuth: true,
      // allowProjectTemplate: true,
    },
  },
  NOT_FOUND_ROUTE,
];

const router = createRouter({
  history: createWebHistory('/work'),
  routes,
  scrollBehavior() {
    return { top: 0 };
  },
});

createRouteGuard(router);

const projectTemplateWhiteSet = new Set([
  'homePage',
  'login',
  'knowledgeBase',
  'dashboard',
  'schedule',
  'setup',
  'shareKnowledge',
  'shareDownload',
]);

router.beforeEach((to, from, next) => {
  const userStore = useUserStore();
  // 只在有name的情况下做判断，防止无限重定向
  // console.log('跳转了', to.name);
  // console.log(
  //   '跳转了to.meta.allowProjectTemplate',
  //   to.meta.allowProjectTemplate
  // );
  if (
    userStore.projectTemplate === '1' &&
    to.name &&
    !to.meta.allowProjectTemplate
  ) {
    next({ name: 'homePage', replace: true });
    return;
  } else if (
    userStore.projectTemplate === '1' &&
    to.name &&
    projectTemplateWhiteSet.has(to.name as string)
  ) {
    // console.log(
    //   '判断更改projectTemplateWhiteSet',
    //   projectTemplateWhiteSet.has(to.name as string)
    // );
    userStore.setProjectTemplate('0');
  }
  next();
});

export default router;
