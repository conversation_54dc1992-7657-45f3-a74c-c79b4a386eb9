import axios from 'axios';
import { fileDownload } from '@/api/file';
import { download } from '@/utils/file';
import { Paging } from '@/types/global';

export interface issueRecipient {
  createBy: string;
  createDate: string;
  id: string;
  email: string;
  name: string;
}
export interface issuesData {
  id: string;
  title: string;
  projectId: string;
  state: number;
  createBy: string;
  receivers: string;
  createDate: string;
  message: string;
  file: string;
  issueRecipientList?: Array<issueRecipient>;
  [propsName: string]: any;
}

export interface issuesParams extends Partial<issuesData> {
  pageNo: number;
  pageSize: number;
}

export interface issuesListRes {
  list: issuesData[];
  total: number;
  message: string;
}

export interface issuesSave {
  projectId: string;
  fileIds: Array<string>;
  message: string;
  receivers: Array<string>;
  title: string;
}

export interface issuesStatus {
  issueId: string;
  status: string;
}

export interface FileInfo {
  createBy?: string;
  createDate?: string;
  deleteFlag?: number;
  description?: string;
  fileId?: string;
  fileToken?: string;
  folderId?: string;
  id?: string;
  isLocked?: boolean;
  name?: string;
  projectId?: string;
  size?: string;
  teamId?: string;
  type?: string;
  updateBy?: string;
  updateDate?: string;
  version?: string;
}
export interface PictureItem {
  fileId: string;
  picToken: string;
}
export interface IssueFormInter {
  id: string;
  reply: string;
  status: number;
  items: PictureItem[];
  picToken: string;
}
export interface IssueLog {
  id: string;
  createDate: string;
  createUsername: string;
  picType: number;
  reply: string;
  picToken: string;
}

interface issueStatus {
  issueId: number;
  publishStatus: number;
}

// 查询问题列表-分页
export function queryissuesList(params: issuesParams) {
  return axios.get<issuesListRes>('/cde-collaboration/issue/list', {
    params,
  });
}

// 创建问题
export function addIssue(data: issuesData) {
  const param: issuesData = { ...data };
  return axios.post<issuesListRes>('/cde-collaboration/issue/save', param);
}

// 更新问题状态
export function updateStatus(data: issuesStatus) {
  // const param: issuesStatus = { ...data };
  return axios.post('/cde-collaboration/issue/updatestatus', data);
}

// 下载
export async function issueFileDownload(fileInfo: FileInfo) {
  const res: any = await fileDownload(fileInfo);
  download({ name: fileInfo.name || '' }, res.data);
}
// 问题回复
export function replyIssue(parmas: IssueFormInter) {
  return axios.post('/cde-collaboration/issue/add-pic', parmas);
}
// 查询问题操作日志
export function queryissueLog(bizId: string) {
  return axios.get<Paging<IssueLog>>('/cde-collaboration/operlog/list', {
    params: {
      bizId,
      pageNo: 1,
      pageSize: 9999,
    },
  });
}
export const getDirectoryFileInfo = (directory: any) => {
  let total = 0;
  let fileIDList: Array<string> = [];
  if (directory.children?.length) {
    directory.children.forEach((item: any) => {
      if (item.isFileOrFolder === 1) {
        total += 1;
        fileIDList.push(item.id);
      } else {
        total += getDirectoryFileInfo(item).total;
        fileIDList = fileIDList.concat(getDirectoryFileInfo(item).fileIDList);
      }
    });
  }
  return { total, fileIDList };
};

// 设置问题发布状态
export function setPublishedStatus(data: issueStatus) {
  return axios.post('/cde-collaboration/issue/updatepublishstatus', data);
}
