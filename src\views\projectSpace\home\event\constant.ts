import { reactive } from 'vue';

const CONSTANT = reactive({
  YEAR_TIME_LENGTH: 31536000000,
  MONTH_TIME_LENGTH: 2678400000,
  WEEK_TIME_LENGTH: 604800000,
  DAY_TIME_LENGTH: 86400000,
  HOUR_TIME_LENGTH: 3600000,
  MINUTE_TIME_LENGTH: 60000,
  MAX_VIEW_PROPORTION: 0.9,
  LINE_HEIGHT: 28,
  LINE_MARGIN: 20,
  LINE_WEIGHT: 2,
  RECT_WIDTH: 25,
  CIRCLE_DIAMETER: 25,
  DRUM_WIDTH: 32,
  DRUM_HEIGHT: 25,
  MERGING_DISTANCE: 32,
  TIME_TEXT_SIZE: 14,
  BACKGROUND_COLOR_1: '#F5F8FB',
  BACKGROUND_COLOR_2: '#FFFFFF',
  MARK_BACKGROUND_COLOR: '#F53F3F',
  MARK_TEXT_COLOR: '#FFFFFF',
  MARK_TEXT_SIZE: 12,
});

export default CONSTANT;
