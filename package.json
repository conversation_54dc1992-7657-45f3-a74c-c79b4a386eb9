{"name": "cdex-work-frontend-new", "description": "CCCC Digital Base Frontend", "version": "1.0.0", "private": true, "author": "<EMAIL>", "license": "MIT", "scripts": {"dev": "rsbuild dev", "build": "rsbuild build"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@arco-design/web-vue": "^2.57.0", "@bpmn-io/properties-panel": "^3.23.0", "@braks/revue-draggable": "^0.4.3", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/vue3": "^6.1.15", "@popperjs/core": "^2.11.8", "@vueuse/core": "^12.5.0", "axios": "^0.24.0", "bpmn-js": "^17.9.2", "bpmn-js-properties-panel": "^5.22.0", "bpmn-moddle": "^8.0.1", "camunda-bpmn-moddle": "^7.0.1", "chbim": "0.2.62", "clipboard": "^2.0.11", "crypto-js": "^4.1.1", "dayjs": "^1.11.5", "echarts": "^5.5.1", "guid-typescript": "^1.0.9", "highcharts": "^11.4.7", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "js-md5": "^0.7.3", "js-web-screen-shot": "^1.9.9-rc.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.30.1", "mqtt": "^4.2.8", "nprogress": "^0.2.0", "pinia": "^2.0.23", "qrcode": "^1.5.3", "query-string": "^8.0.3", "sm-crypto": "^0.2.5", "sortablejs": "^1.15.0", "spark-md5": "^3.0.2", "timeline": "^3.4.1", "tus-js-client": "^4.1.0", "v-calendar": "^3.1.2", "vue": "^3.2.40", "vue-clipboard3": "^2.0.0", "vue-echarts": "^6.2.3", "vue-i18n": "^9.2.2", "vue-router": "^4.0.14", "vue-web-screen-shot": "^1.5.3", "vue3-tree-org": "^4.2.2", "vuedraggable": "^4.1.0", "x2js": "^3.4.4"}, "devDependencies": {"@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@rsbuild/core": "^1.3.22", "@rsbuild/plugin-babel": "^1.0.5", "@rsbuild/plugin-eslint": "^1.1.1", "@rsbuild/plugin-less": "^1.2.4", "@rsbuild/plugin-vue": "^1.0.7", "@rsbuild/plugin-vue-jsx": "^1.1.0", "@types/bpmn-moddle": "^5.1.6", "@types/crypto-js": "^4.1.1", "@types/js-md5": "^0.7.0", "@types/lodash": "^4.14.186", "@types/mockjs": "^1.0.7", "@types/mqtt": "^2.5.0", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@types/sm-crypto": "^0.3.0", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.40.0", "@typescript-eslint/parser": "^5.40.0", "@vitejs/plugin-vue": "^3.1.2", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/babel-plugin-jsx": "^1.2.2", "consola": "^2.15.3", "cross-env": "^7.0.3", "eslint": "^8.25.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.6.0", "husky": "^8.0.1", "less": "^4.1.3", "lint-staged": "^13.0.3", "mockjs": "^1.1.0", "postcss-html": "^1.5.0", "prettier": "^2.7.1", "rollup": "^3.9.1", "rollup-plugin-visualizer": "^5.8.2", "rsbuild-plugin-svg": "^0.0.2", "rsbuild-svg-sprite-loader": "^0.0.1", "stylelint": "^14.13.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "typescript": "^4.8.4", "unplugin-vue-components": "^0.22.8", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-resolve-externals": "^0.2.2", "vite-plugin-style-import": "1.4.1", "vite-svg-loader": "^3.6.0", "vue-tsc": "^1.0.14"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}}