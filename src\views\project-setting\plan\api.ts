import axios from 'axios';

// 里程碑主任务
export interface MilestoneRecordTeam {
  teamData: Array<{
    id?: string;
    projectId?: string | undefined;
    endTime: string;
    createBy?: string;
    createDate?: string;
    updateBy?: string;
    updateDate?: string;
    deleteFlag?: number;
    description?: string;
    milestoneId?: string | number;
    parentId?: string;
    startTime?: string;
    teamId?: string;
    type?: number;
    color: string;
    date: Array<string>;
  }>;
}
// 里程碑
export interface MilestoneRecord {
  id?: string;
  projectId?: string;
  name?: string;
  endTime: string;
  describe: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  milestoneId: string;
  startTime: string;
  teamId: number;
  date: Array<string>;
  taskTreeList: Array<MilestoneRecordTeam>;
}
// 里程碑传参
export interface parentDataeRecord {
  milestoneId: string;
  parentId?: string;
  projectId?: string;
  parentTeamId?: number;
}

export interface MilestoneRecordTeamItem {
  id?: string | number;
  projectId: string | undefined;
  endTime: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  description?: string;
  milestoneId?: string | number;
  parentId?: number;
  startTime?: string;
  teamId?: string | string;
  type?: number;
}

// 分页查询参数
export interface MilestoneSearchParams {
  pageNo: number;
  pageSize: number;
  projectId: string;
  name?: string;
  startTime?: string;
  endTime?: string;
}

// 分页查询参数
export interface TeamSearchParams {
  pageNo?: number;
  pageSize?: number;
  projectId: string;
  name?: string;
  teamId?: string;
}

// 查询团队列表
export function queryTeamList(params: TeamSearchParams) {
  return axios.get('/cde-collaboration/team/list', {
    params,
  });
}
// 查询一级或二级团队
export function getTeamList(params: TeamSearchParams) {
  return axios.get('/cde-collaboration/team/team_drop_down_box', {
    params,
  });
}

// 查询里程碑列表
export function queryMilestoneList(params: MilestoneSearchParams) {
  return axios.get('/cde-collaboration/milestone/list', {
    params,
  });
}
// 责任分工矩阵
export function queryMilestoneMatrix(params: MilestoneSearchParams) {
  return axios.get('/cde-collaboration/milestone/matrix', {
    params,
  });
}
// 新增里程碑
export function saveMilestone(params: MilestoneRecord) {
  return axios.post('/cde-collaboration/milestone/save', params);
}
// 新增里程碑主任务
export function saveMainTask(params: Array<MilestoneRecord>) {
  return axios.post('/cde-collaboration/milestone/saveMainTask', params);
}
// 编辑里程碑主任务
export function updateMainTask(params: Array<MilestoneRecord>) {
  return axios.post('/cde-collaboration/milestone/updateTask', params);
}

// 编辑里程碑
export function updateMilestone(params: MilestoneRecord) {
  return axios.post('/cde-collaboration/milestone/update', params);
}

// 删除里程碑
export function removeMilestone(id?: string) {
  return axios.delete(`/cde-collaboration/milestone/remove?id=${id}`);
}

// 删除任务
export function removeTask(id?: string) {
  return axios.delete(`/cde-collaboration/milestone/removeTask?id=${id}`);
}

// 查询第三方系统收件人
export function queryThirdRecipient(params: any) {
  return axios.get('/cde-collaboration/treed/delivery-person', {
    params,
  });
}
