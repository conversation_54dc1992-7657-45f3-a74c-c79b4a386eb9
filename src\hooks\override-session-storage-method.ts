// 封装setItem方法以触发自定义事件，只能执行一次
let isPatched = false;

const wrapSessionStorageMethods = () => {
  if (isPatched) return;
  isPatched = true;

  console.log('patchSessionStorage');
  const originalSetItem = sessionStorage.setItem;
  sessionStorage.setItem = (key, value) => {
    // 先更新值
    originalSetItem.call(sessionStorage, key, value);
    // 再触发事件
    window.dispatchEvent(new Event('cdex-session-storage-change'));
  };

  // 封装removeItem和clear方法
  const originalRemoveItem = sessionStorage.removeItem;
  sessionStorage.removeItem = (key: string) => {
    originalRemoveItem.call(sessionStorage, key);
    window.dispatchEvent(new Event('cdex-session-storage-change'));
  };

  const originalClear = sessionStorage.clear;
  sessionStorage.clear = () => {
    originalClear.call(sessionStorage);
    window.dispatchEvent(new Event('cdex-session-storage-change'));
  };
};

wrapSessionStorageMethods();
