<template>
  <a-modal
    :visible="visible"
    :width="600"
    :title="$t('knowledgenew.import-from-disk-title')"
    title-align="start"
    :ok-text="$t('knowledgenew.ok-text')"
    :mask-closable="false"
    :unmount-on-close="true"
    :ok-loading="btnLoading"
    :esc-to-close="false"
    class="import-file-dialog"
    @before-open="initData"
    @cancel="cancel"
    @ok="submitData"
  >
    <a-tabs v-model:active-key="activeTabKey" @change="changeTabs">
      <template v-if="activeTabKey === 2" #extra>
        <a-select
          v-model="selectedProjectId"
          :options="projectOptions"
          :field-names="{ value: 'projectId', label: 'projectName' }"
          :placeholder="$t('knowledgenew.select-project-placeholder')"
          :bordered="false"
          :style="{ width: '262px' }"
          @change="initProjectFolder"
        />
      </template>
      <a-tab-pane :key="1" :title="$t('knowledgenew.my-file')"> </a-tab-pane>
      <a-tab-pane :key="2" :title="$t('knowledgenew.project-file')">
      </a-tab-pane>
    </a-tabs>
    <a-input-search
      v-model="searchValue"
      size="small"
      :placeholder="$t('knowledgenew.search-placeholder')"
      allow-clear
      @blur="searchFile"
      @press-enter="($event.target as any)?.blur()"
      @search="searchFile"
    />
    <div class="breadcrumb-container">
      <a-breadcrumb :max-count="3">
        <template #separator>
          <icon-right />
        </template>
        <a-breadcrumb-item
          v-for="(item, index) in currentPath"
          :key="item.id"
          @click="jumpToFolder(item, index)"
          >{{ item.name }}
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <a-table
      v-model:selected-keys="selectedKeys"
      :columns="columns"
      :data="tableData"
      row-key="id"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      :loading="tableLoading"
      :pagination="false"
      :bordered="false"
      :scroll="{ x: '100%', y: '327px' }"
      @select-all="handleSelectAll"
    >
      <template #name="{ record }">
        <div class="table-name">
          <FileImage
            :file-name="record.name ?? ''"
            :is-sys-file="false"
            :is-file="record.type === 'file'"
            style="margin-right: 8px; width: 22px; height: 22px"
          />
          <a-typography-paragraph
            :ellipsis="{
              rows: 1,
              showTooltip: true,
            }"
            class="text-title"
            :class="record.type === 'file' ? 'file-name' : 'folder-name'"
            @click="onSelect(record)"
            >{{ record.name }}
          </a-typography-paragraph>
        </div>
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  import {
    computed,
    defineEmits,
    defineProps,
    PropType,
    ref,
    watch,
  } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { storeToRefs } from 'pinia';
  import { useKnowledgeBaseStore2 } from '@/store';
  import { PathNode } from '@/store/modules/knowledge-base/types';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import {
    getChildFolderList,
    getFileList,
    FileAndFolderMessage,
  } from '@/api/tree-folder';
  import { getSearchData } from '@/views/projectSpace/file/api';
  import { importProjectFile, getProjectSearchFile } from '../../api';
  import { getPrivateInit, getProjectFileList } from '../api';
  import { Node } from '../../types';
  import { Message } from '@arco-design/web-vue';
  import getFileType from '../../utils';

  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    parent: {
      type: Object as PropType<Node>,
      required: true,
    },
  });
  const emits = defineEmits(['update:visible', 'refresh']);

  const knowledgeBaseStore2 = useKnowledgeBaseStore2(); // 知识库
  const { folderId, history } = storeToRefs(knowledgeBaseStore2);

  const btnLoading = ref(false);
  const tableLoading = ref(false);
  const resultCount = ref(0);

  // 标签页
  const activeTabKey = ref(1);

  // 个人网盘项目id
  const personalProjectId = ref('');
  // 个人网盘根文件夹
  const personalRootFolder = ref();

  // 项目文件数据
  const projectOptions = ref<
    { projectId: string; projectName: string; folderVOList: [] }[]
  >([]);
  const selectedProjectId = ref('');

  // 初始化个人网盘信息（个人网盘相当于一个项目下的文件管理）
  async function initNetdiskInfo() {
    try {
      const res = await getPrivateInit();
      personalProjectId.value = res.data.projectId;
      personalRootFolder.value = {
        id: res.data.id,
        projectId: res.data.projectId,
        type: res.data.type,
        parentId: res.data.parentId,
        name: res.data.name,
      };
    } catch (e) {
      console.error('获取projectId失败', e);
    }
  }

  // table数据（我的文件、项目文件共用）
  const folderList = ref<FileAndFolderMessage[]>([]);
  const fileList = ref<FileAndFolderMessage[]>([]);
  const tableData = computed(() => {
    return [...folderList.value, ...fileList.value];
  });
  // 选中的文件
  const selectedKeys = ref<Array<string>>([]);
  // 能选择的文件数量，用于控制全选
  const disabledCount = ref(0);

  // 查询子文件夹
  // 注意我的文件和项目文件的projectId、type不同
  const getPersonalFolder = async () => {
    tableLoading.value = true;
    let projectId = personalProjectId.value;
    let type = 'NETDISK';
    if (activeTabKey.value === 2) {
      projectId = selectedProjectId.value;
      type = 'WIP';
    }

    const res = await getChildFolderList(projectId, '', type, folderId.value);
    if (res.status) {
      const list = (res.data?.list || []).map((item) => ({
        ...item,
        type: 'folder',
        disabled: true,
      }));
      folderList.value = list;
    }
    resultCount.value++;
    if (resultCount.value === 2) {
      tableLoading.value = false;
    }
  };

  // 查询子文件
  const getFiles = async () => {
    tableLoading.value = true;
    const res: any = await getFileList(folderId.value);
    if (res.status) {
      const list = (res.data?.list || []).map((item: any) => {
        let disabled = false;
        if (
          getFileType(item.name) !== 'DOC' &&
          getFileType(item.name) !== 'PDF'
        ) {
          disabled = true;
          disabledCount.value++;
        }
        return {
          ...item,
          type: 'file',
          disabled,
        };
      });

      fileList.value = list;
    }
    resultCount.value++;
    if (resultCount.value === 2) {
      tableLoading.value = false;
    }
  };

  // 查询我的文件数据
  const initPersonalFolder = async () => {
    if (!personalProjectId.value) {
      await initNetdiskInfo();
    }
    if (personalProjectId.value) {
      resultCount.value = 0;
      disabledCount.value = 0;
      folderList.value = [];
      fileList.value = [];
      selectedKeys.value = [];
      knowledgeBaseStore2.initHistory();
      // folderId变化会触发查询文件和文件夹
      knowledgeBaseStore2.openFolder({
        id: personalRootFolder.value.id,
        name: t('knowledgenew.personal-file'), // personalRootFolder.value.name,
      });
    }
  };

  // 查找项目文件数据
  const initProjectFolder = async () => {
    disabledCount.value = 0;
    knowledgeBaseStore2.initHistory();
    folderList.value = [];
    fileList.value = [];
    selectedKeys.value = [];
    tableLoading.value = true;
    const res = await getProjectFileList();
    if (res.status) {
      projectOptions.value = res.data;
      let projectTemp;
      if (selectedProjectId.value) {
        // 切换项目
        projectTemp = projectOptions.value.find((item: any) => {
          return item.projectId === selectedProjectId.value;
        });
      } else if (projectOptions.value.length) {
        // 初次加载，默认显示第一个项目的文件
        [projectTemp] = projectOptions.value;
        selectedProjectId.value = projectTemp.projectId;
      }
      if (projectTemp) {
        const folderAndFileList = projectTemp.folderVOList.map((item: any) => {
          let disabled = false;
          if (
            getFileType(item.fileName || item.folderName) !== 'DOC' &&
            getFileType(item.fileName || item.folderName) !== 'PDF'
          ) {
            disabled = true;
            disabledCount.value++;
          }
          return {
            ...item,
            id: item.fileId || item.folderId,
            name: item.fileName || item.folderName,
            disabled,
          };
        });
        folderList.value =
          folderAndFileList.filter((item) => {
            return item.type === 'folder';
          }) || [];
        fileList.value =
          folderAndFileList.filter((item) => {
            return item.type === 'file';
          }) || [];
        knowledgeBaseStore2.openFolder({
          id: projectTemp.projectId,
          name: projectTemp.projectName,
        });
      }
    }
    tableLoading.value = false;
  };

  // 搜索
  const isSearch = ref(false); // 是否在搜索中，搜索时会修改路径，此时fileId变化不能触发查询
  const searchValue = ref(''); // 本次搜索的值，与上次不同时，才触发搜索
  const oldSearchValue = ref(''); // 上次搜索的值

  // 初始化我的文件数据
  const initAndGetPersonalFolder = () => {
    // 查询文件夹和文件
    resultCount.value = 0;
    disabledCount.value = 0;
    folderList.value = [];
    fileList.value = [];
    selectedKeys.value = [];
    getPersonalFolder();
    getFiles();
  };
  const searchFile = async () => {
    if (
      searchValue.value?.trim() &&
      searchValue.value?.trim() !== oldSearchValue.value
    ) {
      tableLoading.value = true;
      folderList.value = [];
      fileList.value = [];
      disabledCount.value = 0;
      selectedKeys.value = []; // 搜索时清空已选中文件
      isSearch.value = true; // 搜索时的路径变化，不触发查询
      // 设置面包屑路径
      knowledgeBaseStore2.initHistory();
      let projectTemp;
      if (activeTabKey.value === 1) {
        // 我的文件
        knowledgeBaseStore2.openFolder({
          id: personalRootFolder.value.id,
          name: personalRootFolder.value.name,
        });
      } else {
        // 项目文件
        projectTemp = projectOptions.value.find((item: any) => {
          return item.projectId === selectedProjectId.value;
        });
        if (projectTemp) {
          knowledgeBaseStore2.openFolder({
            id: projectTemp.projectId,
            name: projectTemp.projectName,
          });
        }
      }

      // 调用接口查询数据
      try {
        let res;
        if (activeTabKey.value === 1) {
          // 我的文件
          const params = {
            folderIds: [personalRootFolder.value.id],
            pageParam: { pageNo: 1, pageSize: 9999 },
            search: searchValue.value?.trim(),
          };
          res = await getSearchData(params);
        } else if (projectTemp) {
          // 项目文件
          const fileIds: string[] = [];
          const folderIds: string[] = [];
          projectTemp.folderVOList.forEach((item: any) => {
            if (item.fileId) {
              fileIds.push(item.fileId);
            } else if (item.folderId) {
              folderIds.push(item.folderId);
            }
          });
          const params = {
            fileIds,
            folderIds,
            pageParam: { pageNo: 1, pageSize: 9999 },
            search: searchValue.value?.trim(),
          };
          res = await getProjectSearchFile(params);
        }

        if (res?.status) {
          oldSearchValue.value = searchValue.value?.trim();
          const list = (res.data?.list || []).map((item: any) => {
            let disabled = false;
            if (
              getFileType(item.name) !== 'DOC' &&
              getFileType(item.name) !== 'PDF'
            ) {
              disabled = true;
              disabledCount.value++;
            }
            return {
              ...item,
              type: item.fileToken ? 'file' : 'folder',
              disabled,
            };
          });
          fileList.value = list;
        }
      } catch (error) {
        console.error('搜索文件失败：', error);
      }
      isSearch.value = false;
      tableLoading.value = false;
    } else if (!searchValue.value && oldSearchValue.value) {
      oldSearchValue.value = '';
      if (activeTabKey.value === 1) {
        initAndGetPersonalFolder();
      } else {
        initProjectFolder();
      }
    }
  };

  // 切换标签页
  const changeTabs = (key: number) => {
    searchValue.value = '';
    oldSearchValue.value = '';
    if (key === 1) {
      initPersonalFolder();
    } else {
      selectedProjectId.value = '';
      initProjectFolder();
    }
  };

  // 数据初始化
  const initData = async () => {
    activeTabKey.value = 1;
    // 我的文件
    folderList.value = [];
    fileList.value = [];
    // 项目文件
    selectedProjectId.value = '';

    knowledgeBaseStore2.initHistory();
    // 查询个人网盘信息（projectId）
    await initNetdiskInfo();
    initPersonalFolder();
  };

  // 当前显示路径
  const currentPath = computed(() => {
    return history.value;
  });

  // 点击面包屑的路径
  const jumpToFolder = (item: PathNode, index: number) => {
    if (activeTabKey.value === 1) {
      // 我的文件
      knowledgeBaseStore2.changePath2(item.id, index);
      if (searchValue.value && index === 0) {
        // 搜索后，点击根路径，清空搜索条件，查询根路径数据
        searchValue.value = '';
        oldSearchValue.value = '';

        initAndGetPersonalFolder();
      }
    } else {
      // 项目文件
      searchValue.value = '';
      oldSearchValue.value = '';
      if (index === 0) {
        // 点击根路径
        initProjectFolder();
      } else {
        // 点击非根路径
        knowledgeBaseStore2.changePath2(item.id, index);
      }
    }
  };

  const columns = computed(() => {
    return [
      {
        title: t('knowledgenew.name-label'),
        dataIndex: 'name',
        slotName: 'name',
      },
    ];
  });

  // 全选
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleSelectAll = (checked: boolean) => {
    if (
      selectedKeys.value.length + disabledCount.value <
      fileList.value.length
    ) {
      selectedKeys.value = fileList.value.map((item) => {
        return item.id || '';
      });
    } else {
      selectedKeys.value = [];
    }
  };

  // 点击进入下一文件夹
  const onSelect = (item: any) => {
    if (item.type === 'folder') {
      knowledgeBaseStore2.openFolder({
        id: item.id || '',
        name: item.name,
      });
    }
  };

  // 监听 folderId 变化，即点击面包屑路径时，重新查询文件夹和文件
  watch(
    () => folderId.value,
    async (nValue) => {
      if (activeTabKey.value === 1) {
        // 我的文件
        if (nValue && !isSearch.value) {
          // 查询文件夹和文件
          initAndGetPersonalFolder();
        }
      } else if (nValue && currentPath.value.length !== 1) {
        // 项目文件（
        // 面包屑只有一个路径时表示项目，调用initProject Folder接口；
        // 有多个路径时表示文件夹，调用这两个接口
        initAndGetPersonalFolder();
      }
    },
    {
      immediate: true,
    }
  );

  // 提交数据
  const submitData = async () => {
    if (!selectedKeys.value.length) {
      Message.info(t('knowledgenew.please-select-file'));
      return;
    }
    try {
      btnLoading.value = true;
      const params = {
        fileIds: selectedKeys.value || [],
        folderId: props.parent.id || '',
      };
      const submitRes = await importProjectFile(params);
      if (submitRes.status) {
        Message.success(t('knowledgenew.import-success'));
        emits('update:visible', false);
        emits('refresh'); // 触发父组件查询
      }
      btnLoading.value = false;
    } catch (err) {
      console.log('导入失败：', err);
      btnLoading.value = false;
    }
  };

  const cancel = () => {
    emits('update:visible', false);
  };
</script>

<style scoped lang="less">
  .breadcrumb-container {
    padding: 16px 0;
    height: 54px;
    display: flex;
    align-items: center;
  }

  :deep(.has-pointer) {
    cursor: pointer;
  }

  .table-name {
    display: flex;
    .text-title {
      flex: 1;
      margin-bottom: 0;
    }
    .folder-name {
      cursor: pointer;
    }
    .folder-name:hover {
      color: #36f;
    }
  }

  :deep(.arco-tabs) {
    height: 100%;
  }
  :deep(.arco-tabs-content) {
    height: calc(100% - 40px);
    padding-top: 0;
  }
  :deep(.arco-tabs-tab) {
    font-size: 16px;
  }
  :deep(.arco-tabs-nav-type-line .arco-tabs-tab-title) {
    padding: 0;
    line-height: 24px;
  }
  :deep(.arco-tabs-tab-active, .arco-tabs-tab-active:hover) {
    color: #3366ff;
  }
  :deep(.arco-tabs-nav-type-line .arco-tabs-tab) {
    margin: 0 8px;
  }
  :deep(.arco-tabs-tab:not(:first-of-type)) {
    margin-left: 24px;
  }

  // 面包屑样式覆盖
  :deep(.arco-breadcrumb-item) {
    max-width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 22px;
    cursor: pointer;
  }

  :deep(.arco-breadcrumb-item:last-of-type) {
    cursor: auto;
  }

  :deep(.arco-breadcrumb-item-separator) {
    font-size: 12px;
    margin: 0;
  }

  :deep(.arco-breadcrumb) {
    flex: 1;
  }

  /* 表格标题滚动条bug修复 */
  :deep(.arco-table-container > .arco-scrollbar > .arco-scrollbar-track) {
    display: none;
  }
</style>

<style lang="less">
  .import-file-dialog {
    .arco-modal-header {
      height: 52px;
    }
    .arco-modal-title {
      font-size: 20px;
      font-weight: 500;
      color: #1d2129;
      line-height: 28px;
    }
    .arco-modal-body {
      padding: 8px 20px 20px;
    }

    // 搜索框样式覆盖
    .arco-input-wrapper {
      margin-top: 16px;
      background-color: #fff;
      border: 1px solid #c9cdd4 !important;
      border-radius: var(--border-radius-medium);
    }
  }
</style>
