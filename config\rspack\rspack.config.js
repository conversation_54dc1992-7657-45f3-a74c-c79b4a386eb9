// eslint-disable-next-line @typescript-eslint/no-var-requires
const { defineConfig } = require('@rspack/cli');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const rspack = require('@rspack/core')
// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path');

const isProduction = process.env.NODE_ENV === 'production';


module.exports = {
  entry: {
    main: './src/main.ts',
  },
  mode: isProduction ? 'production' : 'development',
  output: {
    clean: true,
    filename: pathData => {
      return pathData.chunk.name === 'main' ? '[name].js' : '[name]/[name].js';
    },
    path: path.resolve(__dirname, 'dist')
  },
  module: {
    rules: [
      {
        test: /\.less/,
        loader: ['css-loader', 'less-loader'],
        type: 'css/module',
      },
      {
        test: /\.css/,
        parser: {
          namedExports: false,
        },
        type: 'css/module',
      },
      {
        test: /\.vue/,
        loader: 'vue-loader',
      },
      {
        test: /\.ts/,
        loader: ['ts-loader'],
        type: 'css/module',
      },
    ],
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, './src'),
    },
    extensions: ['.js', '.jsx', '.vue', '.json', '.ts']
  },
  builtins: {
    html: [{template: path.resolve(__dirname, './index.html')}]
  },
  plugins: [
    new
    new rspack.HtmlRspackPlugin({template: path.resolve(__dirname, './index.html')})
  ],
  externals: {
    BMapGL: 'BMapGL',
  },
  devServer: {
    host: '0.0.0.0',
    port: 8080,
    proxy: [
      {
        context: ['/api'],
        pathRewrite: { '^/api': '' },
        changeOrigin: true,
      },
    ],
  }
};