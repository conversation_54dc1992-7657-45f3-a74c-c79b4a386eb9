import imgJson from '@/config/imgType.json';
import conversion from '@/config/requiredConversion.json';
import wpsJson from '@/config/wpsType.json';
import Clipboard from 'clipboard';
import { Message } from '@arco-design/web-vue';
import { ViewColumn } from '@/components/dynamic-column/hooks';

export const isNeedToConvert = (status: number, type: number) => {
  if (type === 2) {
    if (status === 2) return true;
    if ([0, 1].includes(status)) return true;
  } else if (type === 1) {
    return true;
  } else {
    // 普通模型文件
    if ([0, 3].includes(status)) return true;
    if ([-3, 1, 4].includes(status)) return true;
  }
  return false;
};

export const isWpsFile = (record: any) => {
  const type = record.name.split('.')[record.name.split('.').length - 1];
  const isWpsType: boolean = wpsJson.includes(type);
  return isWpsType;
};

// 判断wps和图片文件
export const isPicFile = (record: any) => {
  const type = record.name.split('.')[record.name.split('.').length - 1];
  const isImgType: boolean = imgJson.includes(type);
  return isImgType;
};

// 需要转换的列表则有转换图标
export const isRequiredConversion = (record: any) => {
  const type = record.name.split('.')[record.name.split('.').length - 1];
  const isConversion: boolean = conversion.includes(type);
  return isConversion;
};

// 数据节点过滤系统文件
export const filterSysTreeData = (treeData: any) => {
  treeData = treeData.filter((item: any) => {
    return !item.sysType;
  });
  return treeData;
};

// 处理树形数据结构
export const transformData = (input: any) => {
  function transformFolder(folder: any): any {
    const fileList: any = [];
    const folderList: any = [];
    const transformed: any = {
      fileId: folder.id,
      fileName: folder.name,
      fileType: 0,
      size: 0,
      version: 0,
      shareLinkDtoList: [],
      description: folder.description || '',
      sysType: folder.sysType,
    };

    if (folder.files?.length) {
      folder.files.forEach((file: any) => {
        fileList.push({
          fileId: file.fileId,
          fileName: file.name,
          fileType: 1,
          size: file.size,
          version: file.version,
          fileToken: file.fileToken,
          description: folder.description || '',
        });
      });
    }
    if (folder.folderAllDtoList?.length) {
      folder.folderAllDtoList.forEach((subFolder: any) => {
        folderList.push(transformFolder(subFolder));
      });
    }
    // 为了文件夹在前的排序
    transformed.shareLinkDtoList = folderList.concat(fileList);
    return transformed;
  }

  const output: any = {
    checkType: 0,
    packageType: 1,
    shareLinkDtoList: [],
    shareName: input.name,
    shareType: 0,
  };

  output.shareLinkDtoList.push(transformFolder(input));

  return output;
};

export const copy = (obj: string, linkText: string, hint?: any) => {
  const clipboard = new Clipboard(obj, {
    text: () => {
      return linkText;
    },
  });
  clipboard.on('success', () => {
    clipboard.destroy();
    Message.success(hint);
  });
  clipboard.on('error', () => {
    clipboard.destroy();
    Message.error(hint);
  });
};

export function isSysFolder(sysType?: number): boolean {
  if (sysType) {
    return [1, 2, 3, 4, 5].includes(sysType);
  }
  return false;
}

export function isTopFolder(folderId?: string): boolean {
  if (folderId) {
    return [
      'WIP',
      'Shared',
      'SHARED',
      'Published',
      'PUBLISHED',
      'COMMUNALSPACE',
    ].includes(folderId);
  }
  return false;
}

export function isCommunalFolder(folder: any) {
  return folder.id === 'COMMUNALSPACE';
}
export function isCommunalChildren(folder: any) {
  return folder.type === 'COMMUNALSPACE';
}

export function getFileColumns(): ViewColumn[] {
  return [
    {
      title: 'file-manage.name',
      dataIndex: 'name',
      slotName: 'name',
      sortable: {
        sortDirections: ['ascend', 'descend'],
      },
      width: 300,
      fixed: 'left',
      defaultSelected: true,
    },
    {
      title: 'file-manage.path',
      width: 200,
      dataIndex: 'path',
      slotName: 'path',
      defaultSelected: false,
    },
    {
      title: 'file-manage.version',
      width: 70,
      dataIndex: 'version',
      slotName: 'version',
      defaultSelected: true,
    },
    {
      title: 'file-manage.size',
      width: 90,
      dataIndex: 'size',
      slotName: 'size',
      defaultSelected: true,
    },
    {
      title: 'file-manage.file-status',
      dataIndex: 'isLocked',
      slotName: 'isLocked',
      width: 120,
      defaultSelected: true,
    },
    {
      title: 'file-manage.transition-status',
      dataIndex: 'status',
      slotName: 'status',
      width: 140,
      defaultSelected: true,
    },
    {
      title: 'file-manage.regenerator',
      dataIndex: 'updateUserName',
      slotName: 'updateUserName',
      width: 120,
      defaultSelected: true,
    },
    {
      title: 'file-manage.update-date',
      width: 180,
      dataIndex: 'updateDate',
      slotName: 'updateDate',
      defaultSelected: true,
    },
    {
      title: 'file-manage.standard-name',
      dataIndex: 'standardName',
      width: 140,
      defaultSelected: false,
    },
    {
      title: 'file-manage.describe',
      width: 180,
      dataIndex: 'description',
      slotName: 'description',
      defaultSelected: false,
    },
  ];
}
