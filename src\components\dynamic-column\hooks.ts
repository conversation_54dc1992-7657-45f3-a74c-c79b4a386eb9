import { computed, ComputedRef, provide, ref } from 'vue';
import { useI18n } from 'vue-i18n';

type TableColumn = Partial<{
  title: string;
  dataIndex: string;
  slotName: string;
  sortable: {
    sortDirections: string[];
  };
  width: number;
  fixed: string;
  align: string;
  titleSlotName: string;
}>;
export type ViewColumn = TableColumn & {
  defaultSelected: boolean;
  isSelected?: boolean;
};
type ActionColumn = TableColumn & {
  defaultSelected?: boolean;
  isSelected?: boolean;
};

type Props = {
  columns: ViewColumn[];
  actionColumn?: ActionColumn;
};

export function useDynamicColumns(props: Props) {
  const { t } = useI18n();
  const { columns, actionColumn } = props;

  const columnsRef = ref(columns);
  provide('allColumns', columnsRef);

  const columnsView: ComputedRef<(ViewColumn | ActionColumn)[]> = computed(
    () => {
      const cols = columnsRef.value.filter((column) => column.isSelected);
      if (actionColumn) {
        return [...cols, actionColumn].map((column) => {
          return { ...column, title: t(column.title!) };
        });
      } else {
        return cols.map((column) => {
          return { ...column, title: t(column.title!) };
        });
      }
    }
  );
  function setSelectStatusOfColumn(dataIndex: string, isSelected: boolean) {
    columnsRef.value.forEach((column) => {
      if (column.dataIndex === dataIndex) {
        column.isSelected = isSelected;
      }
    });
  }
  return {
    columnsView,
    setSelectStatusOfColumn,
  };
}
