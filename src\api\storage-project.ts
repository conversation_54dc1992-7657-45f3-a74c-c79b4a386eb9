import { useUserStore } from '@/store';
import { computed } from 'vue';
import {
  removeLocalstorageItem,
  getLocalstorage,
  setLocalstorage,
} from '@/utils/localstorage';

const userStore = useUserStore();

const userId = computed(() => {
  return userStore.id as string;
});

export function storeCurrentProjectId(projectId: string) {
  setLocalstorage('currentProjectId', projectId); // 存储当前项目id
}

export function storeCurrenttUserId() {
  setLocalstorage('currentUserId', userId.value); // 存储当前用户id
}
