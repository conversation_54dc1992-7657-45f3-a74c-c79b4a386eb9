<template>
  <div class="ai-input-container" :class="{ loading }">
    <div class="input-wrapper">
      <a-textarea
        ref="textareaRef"
        v-model="inputText"
        :placeholder="placeholder"
        :disabled="loading"
        :auto-size="{
          minRows: 2,
          maxRows: 7,
        }"
        @keydown.enter.exact.prevent="handleSubmit"
        @keydown.shift.enter.exact.prevent="addNewLine"
      ></a-textarea>

      <button class="send-button" :disabled="!canSubmit" @click="handleSubmit">
        <span v-if="loading" class="loading-icon"></span>
        <template v-else>
          <sendIcon v-if="canSubmit" />
          <sendDisableIcon v-else />
        </template>
      </button>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, nextTick } from 'vue';
  import sendIcon from '@/assets/images/knowledge-base/send.svg';
  import sendDisableIcon from '@/assets/images/knowledge-base/send-disable.svg';
  import i18n from '@/locale/index';

  const { t } = i18n.global;
  // t('knowledge.question-input-placeholder')

  const props = defineProps({
    placeholder: {
      type: String,
      default: '请输入您的问题（Shift+Enter换行）',
    },
    loading: Boolean,
  });

  const emit = defineEmits(['submit']);

  const inputText = ref('');
  const textareaRef = ref(null);

  // 是否允许提交
  const canSubmit = computed(() => {
    return inputText.value.trim() && !props.loading;
  });

  // 提交处理
  const handleSubmit = () => {
    if (!canSubmit.value) return;
    emit('submit', inputText.value.trim());
    inputText.value = '';
  };

  // 添加新行
  const addNewLine = () => {
    inputText.value += '\n';
  };
</script>

<style scoped lang="less">
  .ai-input-container {
    --primary-color: #1890ff;
    --border-color: #d9d9d9;
    --bg-color: #fff;
    max-width: 848px;
    margin: 0 auto;
    transition: all 0.3s ease;
  }

  .input-wrapper {
    position: relative;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-color);
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 15px 20px 56px 20px;
  }

  :deep(.arco-textarea-wrapper) {
    border: none !important;
    background-color: transparent !important;
  }

  :deep(.arco-textarea) {
    padding: 0 !important;
    min-height: 24px !important;
    font-size: 16px !important;
    line-height: 24px !important;
  }

  :deep(.arco-textarea-focus) {
    border-color: transparent !important;
  }

  .send-button {
    padding: 0;
    position: absolute;
    right: 20px;
    bottom: 13px;
    width: 34px;
    height: 34px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  .send-button:not(:disabled):hover {
    transform: scale(1.05);
    // box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  }

  .loading-icon {
    width: 18px;
    height: 18px;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* 加载状态样式 */
  .ai-input-container.loading .input-wrapper {
    opacity: 0.8;
  }

  /* 现代浏览器 */
  textarea::placeholder {
    color: #86909c;
    font-size: 16px;
    opacity: 1; /* 覆盖Firefox默认的透明度 */
  }

  /* WebKit浏览器（Chrome/Safari/Edge） */
  textarea::-webkit-input-placeholder {
    color: #86909c;
    font-size: 16px;
  }

  /* Firefox 18- */
  textarea:-moz-placeholder {
    color: #86909c;
    font-size: 16px;
    opacity: 1;
  }

  /* Firefox 19+ */
  textarea::-moz-placeholder {
    color: #86909c;
    font-size: 16px;
    opacity: 1;
  }

  /* IE10-11 */
  textarea:-ms-input-placeholder {
    color: #86909c;
    font-size: 16px;
  }
</style>
