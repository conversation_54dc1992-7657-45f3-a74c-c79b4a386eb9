import {
  getGuidByElementId,
  getModelComponentAttribute,
  GetPropertiesPanel,
} from '@/views/bim-view/api';
import { Base64 } from 'js-base64';
import useModelToolsStore from '@/store/modules/model-viewer/index';

const store = useModelToolsStore();

const semanticModes = ['ifc', 'dgn', 'rvt', 'nwd'];
let XBaseViewer: any;
let renderPath: string; // 获取文件路径
let fileExtension: string; // 获取文件扩展名
let projectId: string; // 项目 ID
let XBaseEngineInstance: any;

// 辅助函数：处理LOD模式下的选择ID
export const handleHlodSelection = async (data: any) => {
  const params = {
    render_path: renderPath,
    element_ids: data?.selectionIds,
  };
  const res = await getGuidByElementId(params);
  if (res?.data?.element_id_guid) {
    const decodedSelectionIds = Object.values(res.data.element_id_guid);
    return fileExtension === 'ifc'
      ? decodedSelectionIds.map((id) => id.slice(1, -1))
      : decodedSelectionIds;
  }
  return [];
};

// 辅助函数：处理非LOD模式下的选择ID
const handleNonHlodSelection = (data: any) => {
  return (
    data?.selectionIds.map((encodedId: string) => {
      const decodedId = Base64.decode(encodedId);
      return fileExtension === 'ifc' ? decodedId.slice(1, -1) : decodedId;
    }) || []
  );
};

// 辅助函数：获取属性面板数据
const fetchPropertiesPanelData = async (decodedSelectionIds: string[]) => {
  const params = {
    guids_info: [
      {
        render_path: renderPath,
        guids: decodedSelectionIds,
      },
    ],
    group_id: projectId,
  };

  try {
    const res = await GetPropertiesPanel(params);
    store.propertiesData = res?.data?.list || [];
  } catch (error) {
    console.error('获取属性面板数据失败:', error);
  }
};

const getASMGuids = (selectIds: string | any[], index: any) => {
  const guids: any = [];
  const { isHlod } = XBaseViewer;
  selectIds.forEach((guid: string) => {
    const atobGuid = isHlod ? guid : atob(guid);
    if (index === Number(atobGuid.slice(0, 1))) {
      guids.push(atobGuid.replace(/'/g, '').split('.')[1]);
    }
  });
  return guids;
};

const queryGuidByElementId = async (selectIds: string | any[]) => {
  if (XBaseViewer.isHlod && typeof selectIds[0] !== 'string') {
    const { data } = await getGuidByElementId({
      element_ids: selectIds,
      render_path: renderPath,
    });
    selectIds = data.element_id_guid
      ? Object.values(data.element_id_guid)
      : selectIds;
  }
  return selectIds;
};

const getGuidsInfo = async (selectIds: string | any[]) => {
  const path = renderPath;
  // eslint-disable-next-line no-unsafe-optional-chaining
  const suffix = path?.substring(path?.lastIndexOf('.') + 1);
  const { isHlod } = XBaseViewer;
  if (suffix === 'asm') {
    selectIds = await queryGuidByElementId(selectIds);
    const pathMapIndex = new Map();
    XBaseViewer.pathMapIndex.forEach((value: any, key: any) => {
      pathMapIndex.set(value, {
        render_path: key,
        guids: getASMGuids(selectIds, value),
      });
    });
    return Array.from(pathMapIndex.values()).filter(
      (item) => item.guids.length
    );
  }
  let guids: any = [];
  for (let i = 0; i < selectIds.length; i++) {
    if (isHlod) {
      guids.push(selectIds[i]);
    } else {
      guids.push(atob(selectIds[i]));
    }
  }
  guids = await queryGuidByElementId(guids);
  return [
    {
      render_path: path,
      guids: guids.map((item: string) => item.replace(/'/g, '')),
    },
  ];
};

// 辅助函数：获取文件后缀名
const getFileExtension = (path: string): string => {
  if (!path || typeof path !== 'string') return '';
  const parts = path.split('.');
  return parts.length > 1 ? parts.pop()! : '';
};

// 辅助函数：检查是否为语义模型
const isSemanticModel = (extension: string): boolean => {
  return semanticModes.includes(extension.toLowerCase());
};

// 主函数：获取 asm 构件属性数据
const setModelCode = async (
  data: any | any[],
  decodedSelectionIds?: string[]
): Promise<void> => {
  try {
    if (!data) return;

    const selectIds = Array.isArray(data) ? data : data.selectionIds;
    if (!selectIds?.length || !decodedSelectionIds?.length) return;

    const guidsInfo = await getGuidsInfo(selectIds);
    if (!guidsInfo?.length) return;

    const extension = getFileExtension(guidsInfo[0].render_path).toLowerCase();
    const firstSelectionId = decodedSelectionIds[0];

    if (isSemanticModel(extension)) {
      const res = await GetPropertiesPanel({
        guids_info: guidsInfo,
        group_id: projectId,
      });

      store.propertiesData = res?.data?.list || [];
    } else {
      const res = await getModelComponentAttribute({
        uid: btoa(firstSelectionId),
        path: guidsInfo[0].render_path, // 确保 renderPath 变量已定义或传递
        group_id: projectId,
      });

      store.propertiesData = res?.data?.categories || [];
    }
  } catch (error) {
    console.error('Error in setModelCode:', error);
    // 可以根据需要添加错误处理逻辑，如设置默认值或显示错误信息
    store.propertiesData = [];
  }
};

// 辅助函数：获取非语义模型构件属性数据
const fetchModelComponentData = async (decodedSelectionIds: string[]) => {
  console.log('[ decodedSelectionIds ] >', decodedSelectionIds);
  getModelComponentAttribute({
    uid: btoa(decodedSelectionIds[0]),
    path: renderPath,
    group_id: projectId,
  }).then((res: any) => {
    const { data } = res || {};
    const { list, categories } = data || {};

    store.propertiesData =
      fileExtension === 'ifc' ? list ?? [] : categories ?? [];
  });
};

// 主函数：处理选中的实体
export const handleEntitySelected = async (data: object) => {
  console.log('模型构件：', data);

  const { isHlod } = XBaseViewer; // 当模型转换开启lod情况下 isHlod 为 true 事件返回的是element id 否则返回的是 Guid

  let decodedSelectionIds: string[] = [];

  // 根据是否为LOD模式处理选择ID
  if (isHlod) {
    console.log('[ isHlod 为 true ] >');
    decodedSelectionIds = await handleHlodSelection(data);
  } else {
    console.log('[ isHlod 为 false ] >');
    decodedSelectionIds = handleNonHlodSelection(data);
  }

  // 将解码后的GUID存储到本地存储中
  localStorage.setItem('guids', JSON.stringify(decodedSelectionIds));

  // 如果文件扩展名属于语义模式，则获取属性面板数据
  if (semanticModes.includes(fileExtension)) {
    await fetchPropertiesPanelData(decodedSelectionIds);
  } else if (fileExtension === 'asm') {
    await setModelCode(data, decodedSelectionIds);
    // 可以在这里添加针对 'asm' 扩展名的处理逻辑
  } else if (fileExtension === 'clash') {
    // 可以在这里添加针对 'clash' 扩展名的处理逻辑
  } else {
    // 可以在这里添加针对其他扩展名的处理逻辑
    await fetchModelComponentData(decodedSelectionIds);
  }
};

// 生成大象云初始化的options
export const genPropertiesOptions = async (
  viewer: any,
  path: string,
  id: string,
  engineInstance: any
) => {
  XBaseViewer = viewer;
  renderPath = path;
  fileExtension = renderPath?.split('.').pop() as string;
  projectId = id;
  XBaseEngineInstance = engineInstance;
  XBaseEngineInstance.entitySelectedEvent(handleEntitySelected); // 模型构件选择事件
};
