<template>
  <div class="collision">
    <a-card class="general-card">
      <template #title>
        <a-row>
          <a-col :span="20">
            <div>
              <span class="search-title">{{
                $t('model-collaboration.name')
              }}</span>
              <a-input
                v-model="searchParams.fileName"
                :style="{ width: '196px', marginLeft: '16px' }"
                :placeholder="$t('model-collaboration.please-input')"
                allow-clear
                search-button
                @search="search"
                @press-enter="search"
                @clear="search"
              />
            </div>
          </a-col>
          <a-col :span="4" style="text-align: right">
            <a-space :size="8">
              <a-button type="primary" @click="search">{{
                $t('model-collaboration.search')
              }}</a-button>
              <a-button type="outline" @click="reset">{{
                $t('model-collaboration.clear')
              }}</a-button>
            </a-space>
          </a-col>
        </a-row>
      </template>
      <a-divider style="margin: 20px 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="17" class="card-title">
          <img src="@/assets/images/dashboard/<EMAIL>" />
          <span>{{ $t('model-collaboration.collisionDetection') }}</span>
        </a-col>
        <a-col :span="7">
          <a-space style="float: right">
            <a-button type="primary" @click="createCollisionDialog">
              {{ $t('model-collaboration.createNewCollisionDetection') }}
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-table-height
        stripe
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="false"
        :scroll="scroll"
        :scrollbar="true"
        @page-change="onPageChange"
        @page-size-change="pageSizeChange"
        @row-click="CollisionInfoView"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #name="{ record }">
          <a-link @click="modelView(record)"> {{ record.name }}</a-link>
        </template>
        <template #filesSource="{ record }">
          <span
            v-for="(item, index) in record.files"
            :key="index"
            class="sourceName"
            >{{ item?.name }}</span
          >
        </template>
        <!-- <template #filesSource="{ record }">
          {{ record.files.map((item: any) => item.name).join(' , ') }}
        </template> -->
        <template #handle="{ record }">
          <a-popconfirm
            :content="$t('model-collaboration.delete-content')"
            position="left"
            @ok="DeleteHandle(record)"
          >
            <a-button type="text" size="small">{{
              $t('model-collaboration.delete')
            }}</a-button>
          </a-popconfirm>
        </template>
      </a-table>
    </a-card>
    <CollisionModel
      v-if="createVisible"
      @refresh="updateData"
      @close="createVisible = false"
    ></CollisionModel>
    <fileTable
      v-model:visible="filesVisible"
      :files-data="filesData"
    ></fileTable>
  </div>
</template>

<script lang="ts" setup>
  import { useRoute, useRouter } from 'vue-router';
  import { computed, ref, reactive, watch, provide } from 'vue';
  import useLoading from '@/hooks/loading';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import cloneDeep from 'lodash/cloneDeep';
  import CollisionModel from './components/collision-model.vue';
  import { queryClashList, deleteFile } from './api';
  import { useI18n } from 'vue-i18n';
  import Modal from '@arco-design/web-vue/es/modal';
  import modelViewBim from '@/utils/common/view';
  import { Message } from '@arco-design/web-vue';
  import { getUserTeamsInPrj } from '@/views/projectSpace/home/<USER>';

  const { t } = useI18n();
  const router = useRouter();
  // 配置
  type Column = TableColumnData & { checked?: true };
  const { loading, setLoading } = useLoading(true);
  const projectStore = usePrjPermissionStore();
  const scroll = {
    y: 'calc(100vh - 280px)',
  };

  // 数据
  const generateSearch = () => {
    return {
      fileName: '',
    };
  };

  const route = useRoute();
  const { projectId }: any = route.params;
  const renderData = ref([]);
  const createVisible = ref(false);
  const filesVisible = ref(false);
  const filesData: any = ref([]);
  const infoVisible = ref(false);
  const detailsInfoRow = ref({});

  const searchParams = ref(generateSearch());
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  const pagination = reactive({
    current: 1,
    pageSize: 20,
    pageSizeOptions: [20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    total: 0,
  });
  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('model-collaboration.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 80,
    },
    {
      title: t('model-collaboration.name'),
      dataIndex: 'name',
      slotName: 'name',
      width: 300,
    },
    {
      title: t('model-collaboration.modelSource'),
      dataIndex: 'filesSource',
      slotName: 'filesSource',
      align: 'center',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('model-collaboration.createTime'),
      dataIndex: 'createDate',
      align: 'center',
    },
    {
      title: t('model-collaboration.operate'),
      align: 'center',
      width: 120,
      slotName: 'handle',
    },
  ]);

  // 清空
  const reset = () => {
    searchParams.value.fileName = '';
    search();
  };
  const fetchData = async () => {
    setLoading(true);
    try {
      const params = {
        ...searchParams.value,
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        isCombination: 2,
        projectId,
      };
      const { data } = await queryClashList(params);
      renderData.value = data.list;
      pagination.total = data.total;
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };
  const createCollisionDialog = () => {
    createVisible.value = true;
  };
  const CollisionInfoView = (row: any) => {
    infoVisible.value = true;
    detailsInfoRow.value = row;
  };

  const modelView = (record: any) => {
    // 若状态为null时 设置为 检查中
    record.status = record.status === null ? '1' : record.status;
    const param = {
      type: 'collision',
      engine: 0,
      modelNumber: record.files?.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
    };
    modelViewBim(record, route.params.projectId as string, param);
  };
  // const CollisionModelView = (record: any) => {
  //   const url = router.resolve({
  //     path: '/model-view',
  //     query: {
  //       idStr: record.id,
  //       name: record.name,
  //       projectId: route.params.projectId,
  //       type: 'collision',
  //       modelNumber: record.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
  //     },
  //   });
  //   // 打开新窗口
  //   window.open(url.href);
  // };

  const search = () => {
    fetchData();
  };
  const onPageChange = (pageNo: number) => {
    pagination.current = pageNo;
    fetchData();
  };
  const pageSizeChange = (pageSize: number): void => {
    pagination.pageSize = pageSize;
    fetchData();
  };
  const updateData = () => {
    search();
    createVisible.value = false;
  };
  const tableHeight = ref(0);
  // table根据父组件计算空白高度
  const vTableHeight = {
    mounted(el: Element) {
      tableHeight.value = Math.max(
        (el.parentElement?.offsetHeight || 0) - 148,
        0
      );
    },
  };
  fetchData();

  const DeleteHandle = async (record: any) => {
    const res = await deleteFile({
      fileIds: [record.id],
      folderIds: [],
      targetTeamId: record.teamId,
      txId: record.id,
    });
    if (res.status) {
      Message.success(t('delete-successful'));
      fetchData();
    }
  };

  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );

  const teamList = ref<any[]>([]);
  provide('teamList', teamList);

  //  获取用户在当前项目下的teams和权限;
  const getTeamData = async () => {
    try {
      const res = await getUserTeamsInPrj(route.params.projectId as string);
      if (res.status) {
        teamList.value = res.data[0].teamList || [];
      }
    } catch (err) {
      console.log(err);
    } finally {
      // handle finally
    }
  };
  getTeamData();
</script>

<script lang="ts">
  export default {
    name: 'Merge',
  };
</script>

<style scoped lang="less">
  .collision {
    .card-title {
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
      img {
        position: relative;
        top: 3px;
        height: 20px;
      }
    }
    .search-title {
      height: 22px;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #1d2129;
      line-height: 22px;
    }
  }

  .list-empty {
    display: flex;
    align-items: center;
    .empty-box {
      width: 100%;
      text-align: center;
      height: 400px;
      .text {
        width: 100%;
        text-align: center;
        margin-top: -80px;
      }
    }
  }
  :deep(.arco-table-tr) {
    cursor: pointer;
  }
  :deep(.arco-card-header) {
    height: auto;
    padding: 0;
    border: none;
  }
  :deep(.arco-card-body) {
    padding: 0;
  }
  :deep(.arco-card-bordered) {
    border: none;
  }
  :deep(.arco-scrollbar-thumb-bar) {
    width: 0;
  }
  .sourceName {
    margin-right: 16px;
  }
  :dep(.arco-divider-horizontal) {
    margin: 20px 0 !important;
  }
</style>
