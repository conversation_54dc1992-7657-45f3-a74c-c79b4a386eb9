<template>
  <div class="pageContainer">
    <commonTabs :tabs="[]"></commonTabs>
    <div class="stardard-sel">
      <!-- 标准下拉数据 -->
      <a-select
        v-model="nowStandardIdNew"
        style="max-width: 500px"
        :placeholder="$t('please-select')"
        class="custom-option"
        @change="selStandardHandle"
      >
        <a-option
          v-for="type in standardListData"
          :key="type.id"
          :value="type.id"
          :label="type.name"
        ></a-option>
      </a-select>
    </div>
    <div class="container-body">
      <div class="containerLeft">
        <a-button
          type="outline"
          class="add-btn"
          :disabled="!nowStandardIdNew || isSystemStandard"
          @click="addHandle"
        >
          <template #icon> <icon-plus /> </template
          >{{ $t('standard.new-node') }}</a-button
        >
        <div class="leftCont">
          <standard-tree
            ref="treeRef"
            :action-type="true"
            :operate-visible="isSystemStandard"
            @edit="editHandle"
            @del="delHandle"
            @select="selecHandle"
          ></standard-tree>
        </div>
      </div>
      <div class="containerRight">
        <div class="content">
          <table-title :title="$t('standard.classCodeStandard')"></table-title>

          <a-row style="margin: 20px 0">
            <a-col :span="12">
              <a-form
                :model="searchParams"
                label-align="left"
                layout="inline"
                auto-label-width
                class="search-area"
              >
                <a-form-item
                  field="name"
                  :label="$t('standard.name')"
                  content-class="item"
                >
                  <a-input
                    v-model="searchParams.name"
                    :placeholder="$t('please-enter')"
                    allow-clear
                    style="width: 200px"
                    @keypress.enter="searchHandle"
                    @clear="searchHandle"
                  />
                </a-form-item>
              </a-form>
            </a-col>
            <a-col :span="12">
              <a-space style="float: right">
                <a-button type="outline" @click="searchHandle"
                  ><icon-search /> &nbsp;{{ $t('list.options.btn.search') }}
                </a-button>
                <a-button type="outline" @click="reset">
                  <icon-refresh />&nbsp;{{ $t('list.options.btn.reset') }}
                </a-button>
              </a-space>
            </a-col>
          </a-row>
          <a-divider margin="0px 0 20px" />
          <a-space>
            <a-popconfirm
              :content="$t('table.opt.sure.delete')"
              type="warning"
              position="left"
              @ok="batchDelete"
            >
              <a-button
                type="outline"
                :disabled="isBatchDetlete"
                status="danger"
                ><template #icon> <icon-delete /> </template
                >{{ $t('table.opt.delete') }}</a-button
              >
            </a-popconfirm>
          </a-space>

          <div class="table-wrap">
            <a-table
              v-model:selected-keys="selectedKeys"
              :pagination="pagination"
              :loading="loading"
              row-key="id"
              :scroll="scroll"
              :columns="columns"
              :data="tableDataNew"
              :scrollbar="true"
              :row-selection="{
                type: 'checkbox',
                showCheckedAll: true,
                onlyCurrent: true,
              }"
              @selection-change="selectionChange"
              @page-change="onPageChange"
              @page-size-change="pageSizeChange"
            >
              <template #tag> {{ $t('standard.custom') }}</template>
              <template #fieldCode="{ record }">
                {{ getProfessionalText(record.fieldCode) }}
              </template>
              <template #level="{ record }">
                {{ getLevelText(record.level) }}
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <add-node
    v-if="addTreeVisible"
    v-model:visible="addTreeVisible"
    :type="actionType"
    :data="treeData"
    @refresh="refreshTable"
  ></add-node>
</template>

<script lang="ts" setup>
  import { onMounted, ref, computed, watch, reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import addNode from './components/addNode.vue';
  import standardTree from '@/components/standard/standardTree/index.vue';
  import commonTabs from '@/components/common-tabs/index.vue';
  import TableTitle from '@/components/table-title/index.vue';

  import useStandardManageStore from '@/store/modules/standard-manage/index';
  import { storeToRefs } from 'pinia';

  import { getStandardTree, delTree } from './api';
  import { getDictionary } from '@/api/standard';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';
  import { useUserStore } from '@/store';

  const userStore = useUserStore();
  const admin = computed(() => userStore.admin);
  const route = useRoute();

  const { t } = useI18n();

  const store = useStandardManageStore();

  const {
    tableData,
    selTreeData,
    standardListData,
    professionalList,
    treeSelectedKey,
    nowStandardId,
  } = storeToRefs(store);

  const nowStandardIdNew: any = ref('');

  const loading: any = ref(false);
  const addTreeVisible: any = ref(false); // 新增编辑弹窗开关
  const actionType: any = ref('add'); // 新增编辑类型
  const treeData: any = ref({}); // 树数据

  const scroll = {
    y: 'calc(100vh - 380px)',
  };

  const columns = computed<TableColumnData[]>(() => [
    // {
    //   title: '序号',
    //   slotName: 'index',
    //   width: 100,
    //   render: (e: any) => {
    //     const res = e.rowIndex + 1;
    //     return res;
    //   },
    //   align: 'left',
    //   fixed: 'left',
    // },

    {
      title: t('standard.name'),
      dataIndex: 'name',
      align: 'left',
      fixed: 'left',
    },
    {
      title: t('standard.major'),
      dataIndex: 'fieldCode',
      slotName: 'fieldCode',
      align: 'left',
    },
    {
      title: t('standard.code'),
      dataIndex: 'code',
      align: 'left',
    },
    {
      title: t('standard.level-category'),
      dataIndex: 'level',
      slotName: 'level',

      align: 'left',
    },
    {
      title: t('standard.ifc-type'),
      dataIndex: 'ifcType',
      slotName: 'ifcType',
      align: 'left',
    },
    {
      title: t('standard.description'),
      dataIndex: 'description',
      align: 'left',
    },
  ]);
  const searchParams: any = ref({
    pageNo: 1,
    pageSize: 20,
    name: '',
  });

  // 获取树数据
  const getTreeData = async (isRefreshKey?: any) => {
    if (nowStandardIdNew.value) {
      store.setTreeLoading(true);
      const { data }: any = await getStandardTree({
        standardId: nowStandardIdNew.value,
      });
      const setTreeSelectList: any = [
        {
          name: '根节点',
          id: data.id || '',
          childList: data.childList || [],
        },
      ];
      store.setSelTreeData({ id: data.id }); // 设置当前没有树数据时候 新增弹窗父节点所需的id

      store.setTreeLoading(false);
      await store.setTreeData(data.childList || []); // 设置树
      if (data?.childList?.length && data.childList[0]?.childList)
        store.setTableData(data.childList[0].childList); // 设置第一个树节点下的表格数据
      await store.setTreeSelectList(setTreeSelectList); // 弹窗所需的节点树

      // 只有一个节点时 默认第一个为当前选中项
      if (data?.childList?.length === 1)
        store.setTreeSelectedKey(data.childList[0]?.id);

      if (isRefreshKey) {
        if (data.childList?.length) {
          store.setSelTreeData(data); // 设置新增编辑时所需要的节点树数据
          store.setTreeSelectedKey(data.childList[0]?.id); // 设置当前点击的节点树
        }
      }

      return data.childList;
    }
    return false;
  };

  const treeRef: any = ref(null);

  // 切换标准
  const selStandardHandle = async (val: any) => {
    store.setNowStand(val); // 设置当前选择的标准
    store.setTableDataNew([]); // 清空表格数据
    store.setTreeData([]); // 清空树数据

    const data = await getTreeData(true);
    if (data?.length) {
      // 设置当前选择树节点
      store.setTreeSelectedKey(data[0]?.id);
      // 设置表格数据
      store.setTableDataNew(data[0].childList);
    }
  };

  // 新增编辑后刷新树、表格数据
  const refreshTable = async () => {
    store.setTreeSelectedKey(treeSelectedKey.value);
    await getTreeData(); // 刷新树
    await store.searchNowNode(treeSelectedKey.value); // 获取当前节点下的数据 用于表格数据的展示
    store.setTableData(selTreeData.value?.childList || [], ''); // 刷新表格
  };

  // 获取数据
  const getList = async () => {
    // 获取左侧树形数据
    getTreeData(true);
  };

  const selectedKeys: any = ref([]);

  // 新增
  const addHandle = () => {
    actionType.value = 'add';
    addTreeVisible.value = true;
  };

  // 编辑
  const editHandle = async () => {
    actionType.value = 'edit';
    addTreeVisible.value = true;
    // treeData.value = nodeData;
    treeData.value = [];
    store.setTableData(selTreeData.value.childList || [], '');
  };

  const selecHandle = (data: any) => {
    store.setSelTreeData(data);
    treeData.value.parentId = selTreeData.value.id;
    store.setTableData(data.childList || [], '');
  };

  // 删除树
  const delHandle = async (record?: any) => {
    store.setSelTreeData(record);
    const { id } = selTreeData.value;
    try {
      const delRes: any = await delTree({ idList: [id] });
      if (delRes) {
        Message.success(t('delete-successful'));
        store.setTreeSelectedKey('');
        getList();
        // 删除后 刷新表格数据
        refreshTable();
      }
    } catch (err) {
      console.log(err);
    }
  };

  const isBatchDetlete = ref(true);
  const selectionIds = ref([]);

  // 批量删除
  const batchDelete = async () => {
    const ids = selectionIds.value;
    const res = await delTree({ idList: ids });
    if (res) {
      Message.success(t('delete-successful'));
      isBatchDetlete.value = true;
      await getList();
    }
  };

  // 获取已选择表格行数据
  const selectionChange = (keys: any) => {
    selectionIds.value = [];
    const data = tableData.value.filter((item: any) => {
      if (keys.includes(item.id)) selectionIds.value.push(item.id);
      return keys.includes(item.id);
    });
    if (selectionIds.value.length > 0) isBatchDetlete.value = false;
    else isBatchDetlete.value = true;
  };

  const tableDataNew: any = ref([]);

  // 表格翻页
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    pageSizeOptions: [10, 20, 50, 100],
    showTotal: true,
    showJumper: true,
    showPageSize: true,
    total: 0,
  });

  // 名称查询
  const searchHandle = () => {
    tableDataNew.value = tableData.value.filter((item: any) => {
      return item.name.includes(searchParams.value.name);
    });
    pagination.total = tableDataNew.value?.length || 0;
  };

  // 重置
  const reset = () => {
    searchParams.value.name = '';
    tableDataNew.value = tableData.value;
    // getList();
  };

  // 获取专业数据
  const getStdFieldHandle = async () => {
    const { data } = await getDictionary({
      code: 'stdField',
      langCode: 'zh-Cn',
    });
    store.setProfessionalList(data);
  };
  getStdFieldHandle();

  // 专业
  const getProfessionalText = (code: any) => {
    const data = professionalList.value.filter(
      (item: any) => item.code === code
    );
    return data[0]?.name;
  };
  // 级类
  const getLevelText = (level: any) => {
    const arrText = [
      '',
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
      '七',
      '八',
      '九',
      '十',
    ];

    return `${arrText[level]}级类`;
  };

  onMounted(async () => {
    store.setTreeData([]); // 树数据清空
    // 1.获取标准下拉数据
    await store.setStandardList();

    // 缓存中的标准id如果在标准列表数据中 则默认展示此标准  若没有 默认展示第一个
    const hasStandardId = standardListData.value.some(
      (item: any) => item.id === nowStandardId.value
    );

    nowStandardIdNew.value =
      route.query?.id ??
      (hasStandardId ? nowStandardId.value : standardListData.value[0]?.id);

    getList();
    store.setDictionaryData('stdField'); // 获取专业数据
  });

  const onPageChange = (pageNo: number) => {
    pagination.current = pageNo;
  };
  // 修改条数
  const pageSizeChange = (pageSize: number): void => {
    pagination.pageSize = pageSize;
  };

  const isSystemStandard = ref<boolean>(false); // 是否是系统标准

  watch(
    () => tableData.value,
    () => {
      tableDataNew.value = tableData.value;
      pagination.total = tableDataNew.value?.length;
      if (isSystemStandard.value && tableData.value.length > 0) {
        // 执行递归添加 disabled: true
        tableData.value.forEach((element) => {
          element.disabled = true;
        });
      }
    }
  );

  watch(
    () => nowStandardIdNew.value,
    (val) => {
      if (val && admin.value !== 0 && standardListData.value.length > 0) {
        const ids: string[] = standardListData.value
          .filter((item) => item.groupId === '0') // 筛选 groupId 为 "0" 的元素
          .map((item) => item.id); // 提取 id 组成新数组
        isSystemStandard.value = ids.includes(val);
      }
    }
  );
</script>

<style scoped lang="less">
  .pageContainer {
    position: relative;
    padding: 16px 20px;
    .container-body {
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
    }

    .containerLeft {
      .add-btn {
        display: block;
        width: 278px;
        margin: 20px 6px 0 16px;
      }
      width: 300px;
      background-color: var(--color-bg-1);

      .content-title {
        div {
          display: flex;
          align-items: center;
          justify-content: left;

          .title-text {
            margin-left: 8px;
            font-weight: bold;
            font-size: 16px;
          }
        }
      }

      .leftList {
        div {
          padding: 4px 0 4px 20px;
          font-size: 16px;
          line-height: 30px;
          cursor: pointer;
        }

        .active {
          color: #fff;
          background-color: rgb(var(--link-6));
        }
      }
    }
    .containerRight {
      padding: 20px;
      border-left: 2px solid #efefef;
      background-color: var(--color-bg-1);
    }

    .content {
      // margin-top: 24px;

      .content-title {
        position: relative;
        margin-top: 16px;

        .btns {
          position: absolute;
          top: 0;
          right: 0;
        }

        .title-img {
          width: 20px;
          height: 20px;
        }

        .title-text {
          position: absolute;
          top: 0;
          left: 20px;
          display: inline-block;
          margin-left: 8px;
          color: #1d2129;
          // color: var(--color-text-1);
          font-weight: 600;
          font-size: 18px;
          // font-family: 'Source Han Sans CN-Medium', 'Source Han Sans CN', serif;
          line-height: 21px;
        }
      }

      .table-wrap {
        height: calc(100% - 60px);
        margin-top: 18px;
      }
    }

    .leftCont {
      // height: 100%;
      height: calc(100vh - 214px);
    }

    :deep(.arco-space) {
      flex-wrap: wrap;
    }
  }

  :deep(.arco-select) {
    border: 1px solid #c9cdd4 !important;
    background-color: #fff;
  }

  .stardard-sel {
    position: absolute;
    top: 14px;
    left: 60px;
    :deep(.arco-select) {
      border: 1px solid transparent !important;
    }
    :deep(.arco-select-view-value) {
      font-size: 16px !important;
    }
    :deep(.arco-select-view-value) {
      min-width: 200px;
    }
    :deep(.arco-select-view-value),
    :deep(.arco-select-view-icon) {
      color: #3366ff;
      font-size: 16px;
      font-weight: 500;
    }
    :deep(.arco-select-view-single) {
      max-width: 500px;
    }
    :deep(.arco-select-view-value) {
      display: inline-block;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
