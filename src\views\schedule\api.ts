import axios from 'axios';

function agendaList(params: any) {
  return axios.get('/cde-collaboration/agenda/listAgenda', {
    params,
  });
}
// 获取会议列表数据
export function getMeetingData(params: any) {
  return axios.get('/cde-work/meetings/list', {
    params,
  });
}

// 获取事项列表数据
export function getAgendaList(params: any) {
  return axios.get('/cde-work/agenda/listAgenda', {
    params,
  });
}
// 删除事项标识(是否管理会议是否有子事项)
export function deleteMatterFlag(params: { scheduleDetailId: any }) {
  return axios.get(`/cde-work/agenda/removeFlag`, { params });
}
// 删除事项
export function deleteMatter(params: { scheduleDetailId: any }) {
  return axios.get(`/cde-work/agenda/remove`, { params });
}

function agendaSave(data: any) {
  return axios.post('/cde-collaboration/agenda/save', data);
}

export function getGanttMatterList(params: any) {
  return axios.get(`/cde-collaboration/agenda/ganttDiagramPage`, { params });
}
export function getGanttLinks(params: any) {
  return axios.get(`/cde-collaboration/agenda/ganttDiagraByProjectId`, {
    params,
  });
}
export function getGanttCounts(params: any) {
  return axios.get(`/cde-collaboration/agenda/statisticsAgenda`, {
    params,
  });
}

export default {
  agendaList,
  agendaSave,
};
