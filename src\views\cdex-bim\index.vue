<template>
  <div class="to-cdex-bim">
    <div class="content">
      <div><img src="@/assets/images/group.png" alt=""></div>
      <a-button type="primary" style="margin-top: 30px;margin-left: 250px;" @click="genLoginToken">进入项目空间</a-button>
    </div>

  </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import {getLoginToken, getLoginCode} from './api';

const cdexUrl = 'https://cdex.ccccltd.cn/bim/login'

const genLoginToken = async () => {
  const { code } = await getLoginCode();
  if(code){
    const res: any = await getLoginToken(code)
    if(res.access_token){
      window.open(`${cdexUrl}?accessToken=${res.access_token}`)
    }
  }
}
onMounted(()=>{
  genLoginToken();
})
</script>

<style scoped lang="less">
.to-cdex-bim{
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>