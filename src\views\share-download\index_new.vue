<template>
  <div class="share-download">
    <div class="navbar">
      <div class="left-side" :title="$t('navbar.logo.to')">
        <a-space @click="toDashboard">
          <img alt="logo" style="width: 40px" :src="logoTop" />
          <a-typography-title
            :style="{ margin: 0, fontSize: '16px', fontWeight: 'bold' }"
            :heading="5"
          >
            {{ $t('navbar.logo.title') }}
          </a-typography-title>
        </a-space>
      </div>
      <a-divider
        direction="vertical"
        :margin="24"
        :style="{ fontSize: '32px' }"
      />
    </div>
    <a-card>
      <template #title>
        <div class="card_title_view">
          <img
            class="title-img"
            src="@/assets/images/file-manager/folder.png"
          />
          <span style="font-size: 18px">{{ titleFileName }} </span>
          <span class="hint-text">{{ hintText }}</span>
        </div>
      </template>

      <template #extra>
        <!-- 面包屑 -->
        <a-space class="breadcrumb_space">
          <!-- 返回上一级按钮 -->
          <span
            v-if="navList.length > 0"
            style="cursor: pointer"
            @click="goSkip(navList[navList.length - 2], navList.length - 2)"
          >
            {{ $t('share-download.go-back') }} &nbsp; <span>|</span>
          </span>
          <a-breadcrumb>
            <template #separator>
              <icon-right />
            </template>
            <a-breadcrumb-item
              v-for="(item, index) in navList"
              :key="item.fileId"
              class="breadcrumb_item"
              @click="goSkip(item, index)"
            >
              <span class="breadcrumb_item_text">{{ item.fileName }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </a-space>
        <!-- 面包屑 -->
        <a-button type="primary" @click="downloadFile">{{
          $t('share-download.download')
        }}</a-button>
      </template>
      <a-divider />
      <a-row style="margin-bottom: 16px">
        <span>{{ $t('share-download.all-file') }}</span>
      </a-row>

      <!-- 表格--yb -->
      <a-table
        ref="tableRef"
        v-model:selectedKeys="selectedKeys"
        v-table-height
        row-key="fileId"
        :loading="loading"
        :columns="columns"
        :row-selection="rowSelection"
        :data="tableData"
        :bordered="false"
        :scroll="scroll"
        :scrollbar="true"
        :pagination="false"
        @select="onSelect"
      >
        <template #fileName="{ record }">
          <file-image
            v-if="record.fileType"
            :file-name="record.fileName"
            :is-file="record.fileType === 1"
            style="margin-right: 8px; margin-bottom: -4px"
          />
          <file-image
            v-if="!record.fileType"
            :file-name="record.fileName"
            :is-file="record.fileType === 1"
            style="margin-right: 8px; margin-bottom: -4px"
          />
          <span style="cursor: pointer" @click="nextLeave(record)">
            {{ record.fileName }}</span
          >
        </template>
        <template #version="{ record }">
          <a-tag v-if="record.version" class="tag" bordered color="cyan"
            >V{{ record.version }}</a-tag
          >
        </template>
        <template #downloadFile="{ record }">
          <a-tooltip
            v-if="record.fileType === 1"
            :content="$t('design.download-source-file')"
          >
            <a-button type="text" @click="download(record)">
              <template #icon> <icon-download /> </template>
            </a-button>
          </a-tooltip>
        </template>
      </a-table>
      <!-- 表格--yb -->
    </a-card>
  </div>

  <!-- 全屏对话框（提取码） -->
  <div class="verify-code-dialog">
    <a-modal
      v-model:visible="isDialogVisible"
      :mask-closable="false"
      :hide-cancel="true"
      :footer="false"
      :closable="false"
      :hide-title="true"
      fullscreen
    >
      <div class="share-download">
        <!-- 标题 -->
        <div class="navbar">
          <div class="left-side" :title="$t('navbar.logo.to')">
            <a-space @click="toDashboard">
              <img alt="logo" :src="logoTop" />
              <a-typography-title
                :style="{ margin: 0, fontSize: '16px', fontWeight: 'bold' }"
                :heading="5"
              >
                {{ $t('navbar.logo.title') }}
              </a-typography-title>
            </a-space>
          </div>
          <a-divider
            direction="vertical"
            :margin="24"
            :style="{ fontSize: '32px' }"
          />
        </div>
        <!-- 标题 -->
        <!-- 提取码内容 -->
        <div class="pas_view modal-content">
          <img class="tidyingImg" :src="tidyingImg" alt="" />
          <div style="margin-top: -26px">
            <h4 style="text-align: center">{{
              $t('share-download.need-code')
            }}</h4>
            <a-form
              ref="formRef"
              :model="form"
              :rules="rules"
              style="width: 100%; max-width: 300px"
            >
              <a-form-item
                field="extractCode"
                style="width: 400px"
                :hide-label="true"
              >
                <a-input
                  v-model="form.extractCode"
                  :placeholder="$t('share-download.enter-code')"
                />
              </a-form-item>
              <a-form-item>
                <a-button
                  html-type="submit"
                  type="primary"
                  class="code_btn"
                  @click="submitCode"
                  >{{ $t('share-download.confirm') }}</a-button
                >
              </a-form-item>
            </a-form>
          </div>
        </div>
        <!-- 提取码内容 -->
      </div>
    </a-modal>
  </div>
  <!-- 全屏对话框（提取码） -->
  <div ref="dropdown" class="dropdown"></div>
  <!-- 图片预览 -->
  <ImgViewer v-if="imgViewModel.visible" :viewModal="imgViewModel"></ImgViewer>
</template>

<script lang="ts" setup>
  import { useRoute, useRouter } from 'vue-router';
  import logoTop from '@/assets/images/logo-top.png';
  import { storeToRefs } from 'pinia';

  import tidyingImg from '@/assets/images/file-tidying/kongmoban.png';
  import { ref, onMounted, reactive, computed } from 'vue';
  import useLoading from '@/hooks/loading';
  import ImgViewer from '@/components/imgView/index.vue';

  import { Message } from '@arco-design/web-vue';
  // eslint-disable-next-line import/no-cycle
  import { getShareInfo, getFileInfoById } from './api';

  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import useFileStore from '@/store/modules/file/index';

  import { useI18n } from 'vue-i18n';
  import useUser from '@/hooks/user';

  // eslint-disable-next-line import/no-cycle
  import modelViewBim from '@/utils/common/view';

  import { batchDownload } from '@/views/projectSpace/file/hooks/events';
  import { getSign } from '@/utils/request-sign';
  import { getXbaseTokenTest, getTokenWithTreed } from './api';
  import { setXBaseToken, setToken, getToken, getUserId } from '@/utils/auth';
  import useGlobalModeStore from '@/store/modules/global-mode';
  import { slashToDot } from '@/utils';

  const { t } = useI18n();

  const router = useRouter();

  const store = useFileStore();

  const { hiddenSlot } = storeToRefs(store);

  const globalModelStore = useGlobalModeStore();

  globalModelStore.changeGlobalMode('project');

  const imgViewModel = computed(() => store.imgViewModal);

  // 配置
  // type Column = TableColumnData & { checked?: true };
  const { loading, setLoading } = useLoading(true);
  const scroll = {
    y: 'calc(100vh - 250px)',
  };
  const route = useRoute();
  const { uuid }: any = route.query;
  const { projectId }: any = route.params;
  // const localeDate = computed(() => {
  //   return GetNowDate();
  // });

  const tableRef = ref<any>();

  // dropdown容器
  const dropdown = ref<any>();

  // 标题
  const titleFileName = ref<string>(t('share-download.file'));

  // 有效期提示
  const hintText = ref<string>('');

  // 提取码对话框数据
  const isDialogVisible = ref<boolean>(false);

  // 表格数据
  const tableData: any = ref([]);

  // 备份——表格数据
  const tableDataCopy: any = ref([]);

  const selectedList: any = ref([]);

  const selectedKeys: any = ref([]);

  const rowSelection: any = reactive({
    type: 'checkbox',
    // showCheckedAll: true,
    onlyCurrent: false,
  });

  // 面包屑数组
  const navList: any = ref([]);

  // 标题数据

  const columns: any = computed(() => [
    {
      title: t('share-download.file-name'),
      dataIndex: 'fileName',
      slotName: 'fileName',
      ellipsis: true,
      tooltip: true,
    },
    {
      title: t('share-download.file-version'),
      dataIndex: 'version',
      slotName: 'version',
    },
    // {
    //   title: t('share-download.file-state'),
    //   dataIndex: 'description',
    //   ellipsis: true,
    //   tooltip: true,
    // },
    {
      title: t('share-download.file-size'),
      dataIndex: 'formatFileSize',
    },
    // {
    //   title: t('share-download.file-handle'),
    //   slotName: 'downloadFile',
    //   align: 'center',
    // },
  ]);

  // 初始化勾选项
  const intSelected = () => {
    selectedList.value = [];
    tableRef.value.selectAll(false);
  };

  // 计算时差
  const remainingDays = (date: string) => {
    const extractedDateTime = new Date(date);
    const currentTime = new Date();

    // 计算时间差
    const timeDiff = extractedDateTime.getTime() - currentTime.getTime();

    // 转换为天数
    const days = Math.ceil(timeDiff / (1000 * 3600 * 24));

    return days;
  };

  // 分享文件数据
  const resData = ref();

  // 获取分享的文件
  async function getFileInfo() {
    setLoading(true);

    try {
      const formData = new FormData();
      formData.append('link', uuid);
      const res: any = await getShareInfo({ link: uuid });
      if (res.status) {
        resData.value = res.data;

        // 判断是否过了有效期
        const extractedDate = new Date(res.data.extractedDate);
        const now = new Date();

        if (extractedDate < now && res.data.extractedDate) {
          router.push('/file-tidying');
        }

        const dateHint = remainingDays(res.data.extractedDate);

        if (dateHint > 1) {
          hintText.value = `${t('share-download.indate-text')}${dateHint}${t(
            'share-download.day-text'
          )}`;
        } else if (!res.data.extractedDate) {
          hintText.value = '该链接长期有效';
        } else {
          hintText.value = t('share-download.current-view-indate');
        }

        // 这里需要判断 根据链接类型判断是否展示 弹出层 或 提示用户需要账号(校验类型0-无校验,1-提取码 ,2-登陆查看)
        if (res.data.checkType === 0) {
          isDialogVisible.value = false;
        } else if (res.data.checkType === 1) {
          isDialogVisible.value = true;
        } else if (res.data.checkType === 2) {
          if (!getUserId()) {
            const { query, path } = router.currentRoute.value;
            router.push({
              path: '/login',
              query: {
                ...query,
                redirect: slashToDot(path) as string,
              },
            });
          }
        }

        // 文件名称
        titleFileName.value = res.data.shareName;

        tableData.value = res.data.shareLinkDtoList;

        // 备份一份完整的文件数据
        tableDataCopy.value = res.data.shareLinkDtoList;
      }
    } catch (err) {
      console.error('请求的错误信息:', err);
    } finally {
      setLoading(false);
    }
  }

  // 下载方法
  function downloadFile(record: any) {
    if (!selectedList.value.length) {
      Message.warning(t('share-download.select-download'));
      return;
    }
    batchDownload(selectedList.value);
    intSelected();
  }

  // 提取码数据
  const form: any = ref({
    extractCode: '',
  });

  // 校验验证码
  const rules = {
    extractCode: [
      {
        required: true,
        validator: (value: string, cb: any) => {
          return new Promise((resolve: any) => {
            if (!value) {
              cb(t('share-download.input-code'));
            }
            if (value !== resData.value.extractedCode) {
              cb(t('share-download.code-error'));
            }
            resolve();
          });
        },
      },
    ],
  };

  // 校验提取码
  const submitCode = () => {
    if (resData.value.extractedCode === form.value.extractCode) {
      isDialogVisible.value = false;
    }
  };

  // 模型预览
  const modelView = async (record: any) => {
    if (!record.folderId) {
      // 文件夹点击事件，进入下一层
      return;
    }

    // 文件点击事件，查看文件
    let needParams: any = {
      noIssue: [1, 2, 3, 4].includes(hiddenSlot.value),
    };
    if (record.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: record.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      needParams = { ...params, ...needParams };
    }
    modelViewBim(record, route.params.projectId as string, needParams);
  };

  // 跳转下一级
  const nextLeave = async (record: any) => {
    // 判断类型 1.文件夹（如果是文件夹就跳转下一级） 2.文件（如果是文件就查看）
    if (record.fileType === 1) {
      // 这里走的是文件，点击时预览效果
      getFileInfoById(record.fileId).then((res) => {
        modelView({
          ...res.data,
          someFunIsShow: true,
        });
      });
    } else if (record.fileType === 0) {
      // 这里走的是文件夹 跳转到下一级并且计算出下一级表格的数据 还要推一条面包屑数据 还要把勾选的数据清掉
      tableData.value = record.shareLinkDtoList;
      intSelected();
      navList.value.push(record);
    }
  };

  // 面包屑跳转
  const goSkip = (record: any, index: number) => {
    // 如果面包屑的长度和 index 相等，表示点的是同级
    if (navList.value.length - 1 === index) {
      return;
    }

    // 如果 index 大于等于 0，表示点击的不是最顶层文件夹
    if (index >= 0) {
      // 截断导航路径 (index后面的都不要了)
      navList.value.splice(index + 1);
      // 更新表格数据 (同时表格数据做更新处理)
      tableData.value =
        navList.value[navList.value.length - 1].shareLinkDtoList;
      intSelected();
    } else {
      // 最顶层文件夹 (最顶层比较难做，索性只能点击上一级导航，将数据全部替换成初始备份的，并且将面包屑隐藏)
      tableData.value = tableDataCopy.value;
      intSelected();
      navList.value.length = 0;
    }
  };

  // 下载操作
  const download = (record: any) => {
    downloadFile(record);
  };

  // 行选择器
  const onSelect = (rowKeys: any, rowKey: string | number, record: any) => {
    const isSelected = rowKeys.includes(rowKey);
    if (isSelected) {
      selectedList.value.push(record);
    } else {
      selectedList.value = selectedList.value.filter((item: any) => {
        return item.fileId !== rowKey;
      });
    }
  };

  // 获取客户端token
  const getTreedToken = async () => {
    try {
      const res = await getTokenWithTreed();
      setToken(res.data as string);
      // @ts-ignore
      const resWithSign = getSign();
      console.log(resWithSign);
      const resWithXbaseToken: any = await getXbaseTokenTest(resWithSign);
      setXBaseToken(resWithXbaseToken.data);
      getFileInfo();
    } catch (error) {
      console.log('请求的错误信息', error);
    }
  };

  // 生命周期函数
  onMounted(async () => {
    try {
      const ifHasToken: string | null = getToken();
      // 判断是否有token(没有token 请求token )
      if (!ifHasToken) {
        getTreedToken();
      } else {
        getFileInfo();
      }
    } catch (error) {
      Promise.reject(error);
    }
  });

  // 回到主页
  const toDashboard = () => {
    const url = router.resolve({
      path: '/dashboard',
    });
    // 打开新窗口
    window.open(url.href);
  };
</script>

<style scoped lang="less">
  .share-download {
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 100;
    width: 100%;
    height: 60px;
  }

  .navbar {
    display: flex;
    align-items: center;
    height: 100%;
    background-color: var(--color-bg-2);
    border-bottom: 1px solid var(--color-border);
  }

  .left-side {
    display: flex;
    align-items: center;
    padding-left: 8px;
  }

  .title-img {
    width: 25px;
    position: relative;
    top: 5px;
    margin-right: 5px;
  }

  .breadcrumb_space {
    position: absolute;
    left: 30px;
    top: 70px;
    font-size: 14px;
    color: blue;
  }

  .modal-content {
    display: flex;
    flex-direction: column;
  }

  .code_btn {
    // margin-top: 4px;
    // margin-left: 40px;
    width: 149px;
    height: 32px;
    margin-left: 68px;
  }

  .pas_view {
    height: 400px;
    width: 400px;
    position: absolute;
    top: calc(50vh - 250px);
    left: calc(50vw - 200px);

    .tidyingImg {
      width: 238px;
      height: 238px;
      align-self: center;
    }
  }

  .card_title_view {
    margin-bottom: 10px;
  }

  .dropdown {
    position: absolute;
    :deep(.arco-dropdown-list-wrapper) {
      max-height: none;
    }
  }

  .hint-text {
    display: inline-block;
    color: red;
    position: fixed;
    top: 18px;
    left: 276px;
    z-index: 1000;
  }

  .breadcrumb_item,
  .breadcrumb_item_text {
    color: blue;
    cursor: pointer;
  }

  .tag {
    border-radius: 4px;
  }

  :deep(.arco-card-header) {
    border: none;
    min-height: 100px !important;
    padding: 0 30px !important;
  }

  :deep(.arco-card-body) {
    padding: 0 30px !important;
  }

  :deep(.arco-card-size-medium) {
    padding: 0;
  }
</style>
