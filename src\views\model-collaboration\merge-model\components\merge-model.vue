<template>
  <a-modal
    :visible="visible"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="cancel"
  >
    <template #title>{{ $t('model-collaboration.create') }}</template>
    <template #footer>
      <a-button @click="cancel">{{
        $t('model-collaboration.cancel')
      }}</a-button>
      <a-button type="primary" :loading="loading" @click="mergaSubmit">{{
        $t('model-collaboration.create')
      }}</a-button>
    </template>
    <div class="content">
      <a-form ref="mergaFormRef" :model="mergaForm" auto-label-width>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item
              field="name"
              :label="$t('model-collaboration.name')"
              :rules="[
                {
                  required: true,
                  message: $t('model-collaboration.fileNameRequired'),
                },
                formRule.specialCharts,
              ]"
              :validate-trigger="['change', 'input']"
            >
              <!-- <remove-spaces-input
                v-model="mergaForm.name"
                :placeholder="$t('model-collaboration.please-input')"
                :max-length="currentLocale === 'en-US' ? 255 : 50"
                show-word-limit
              /> -->
              <a-input
                v-model="mergaForm.name"
                :placeholder="$t('model-collaboration.please-input')"
                :max-length="currentLocale === 'en-US' ? 255 : 50"
                show-word-limit
              />
            </a-form-item>
            <a-form-item
              field="team"
              :label="$t('model-collaboration.generateInTeam')"
              :rules="[
                {
                  required: true,
                  message: $t('model-collaboration.selectTeam'),
                },
              ]"
            >
              <a-select
                v-model="mergaForm.team"
                :placeholder="$t('model-collaboration.please-select')"
                allow-clear
              >
                <a-option
                  v-for="team in teamOptions"
                  :key="team.id"
                  :value="team.id"
                  :label="i18TeamName(team)"
                ></a-option>
              </a-select>
            </a-form-item>
            <a-form-item
              field="description"
              :label="$t('model-collaboration.description')"
              style="margin-top: 20px"
            >
              <a-textarea
                v-model="mergaForm.description"
                :placeholder="$t('model-collaboration.please-input')"
                :max-length="currentLocale === 'en-US' ? 2000 : 500"
                show-word-limit
                :auto-size="{
                  minRows: 2,
                }"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider />
        <a-row :gutter="12">
          <a-col :span="24">
            <div class="title">
              <div class="text">
                <img
                  src="@/assets/images/check/<EMAIL>"
                  alt=""
                  style="width: 17px; height: 17px"
                />
                <span class="text-font">{{
                  $t('model-collaboration.file')
                }}</span>
              </div>
              <div class="file-count">
                <span
                  >{{ $t('model-collaboration.totalFiles') }}:
                  {{ fileCounts || 0 }}
                  {{ $t('model-collaboration.file') }}</span
                >
                <a-button type="text" @click="addFileVisible = true">
                  {{ $t('model-collaboration.addFiles') }}
                </a-button>
              </div>
            </div>
            <div class="file-list-wrap">
              <FileCollapse v-model:files="mergaForm.fileList"></FileCollapse>
            </div>
          </a-col>
        </a-row>
      </a-form>
      <TreeFolder
        v-model:visible="addFileVisible"
        :title="$t('model-collaboration.addFiles')"
        :ok-function="fileChange"
        :show-type="['modelFile']"
        :checked-data="fileIdList"
        :show-sys-folder="[2]"
        :is-clear-key="true"
      ></TreeFolder>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { computed, ref, reactive, watch } from 'vue';
  import FileCollapse from '@/views/check/components/file-collapse/index.vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import TreeFolder from '@/components/tree-folder/index.vue';
  import { model2d } from '@/utils/BIM-Engine/XBase/format';
  import isEmpty from 'lodash/isEmpty';
  import { usePrjPermissionStore } from '@/store';
  import {
    addMergaFile,
    getFileInfoById,
    queryTeamList,
    getXBaseSemanticModelList,
    createXBaseSemanticModel,
    createXBaseNoSemanticModel,
  } from '../api';
  import { useI18n } from 'vue-i18n';
  import { GetXBaseTransferStates } from '@/views/bim-view/api';
  import showErrorMessage from '@/api/msg';
  import useLocale from '@/hooks/locale';
  import { formRule } from '@/utils/index';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';

  const { currentLocale } = useLocale();

  const loading = ref(false);
  const { i18TeamName } = useI18nHandleName();

  const showErrorMessageHandle = (code: string) => {
    loading.value = false;
    const text = showErrorMessage(code);
    if (text !== 'undefined') Message.error(showErrorMessage(code));
  };
  const { t } = useI18n();
  const projectStore = usePrjPermissionStore();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  interface mergaFormInter {
    name: string; // 标题
    team: string;
    fileList: Array<object>;
    description: string; // 消息
  }
  const mergaForm = reactive<mergaFormInter>({
    name: '', // 标题
    team: '',
    fileList: [],
    description: '', // 消息
  });

  const route = useRoute();
  const projectId = ref(route.params.projectId);
  const graphicEngineInfo = ref<string>('');
  const addFileVisible = ref(false);
  const fileIds = ref<Array<object>>([]);
  const mergaFormRef = ref<FormInstance>();
  const teamOptions = ref<any>([]);
  const emits = defineEmits(['update:visible', 'refresh']);
  const cancel = () => {
    mergaFormRef.value?.resetFields();
    mergaForm.fileList = [];
    fileIds.value = [];
    emits('update:visible', false);
    loading.value = false;
  };

  const semanticModelIds: string[] = [];
  const modelIds: string[] = [];
  const semanticModelListStatus: number[] = [];
  const modelListStatus: number[] = [];
  const fileListLength = ref(0);

  // 定义一个函数来处理获取语义模型列表的状态
  async function getSemanticModelStatus(
    projeId: string,
    semanticIds: string[]
  ): Promise<void> {
    const promises = semanticIds.map(async (item) => {
      const params = {
        group_id: projeId,
        filter_model_id: item,
      };
      try {
        const res = await getXBaseSemanticModelList(params);
        return res.data?.semantic_model_list[0]?.status;
      } catch (error: any) {
        showErrorMessageHandle(error.data.code);
        return -10;
      }
    });

    const status = await Promise.all(promises);
    semanticModelListStatus.push(...status);
  }

  // 定义一个函数来处理获取模型状态
  async function getModelStatus(projeId: string, Ids: string[]): Promise<void> {
    const promises = Ids.map(async (item) => {
      const params = {
        model_id: item,
        group_id: projeId,
      };
      try {
        const res = await GetXBaseTransferStates(params);
        console.log('[ res ] >', res);
        return res.data?.status;
      } catch (error: any) {
        showErrorMessageHandle(error.data.code);
        return -10;
      }
    });

    const statuses = await Promise.all(promises);
    modelListStatus.push(...statuses);
  }

  // 定义一个函数来执行所有操作
  async function executeAllOperations(): Promise<void> {
    console.log('[ mergaForm ] >', fileListLength.value);
    await getSemanticModelStatus(projectId.value as string, semanticModelIds);
    await getModelStatus(projectId.value as string, modelIds);
  }

  // 所选文件都是语义模型使用语义合模、所选文件都是非语义模型使用非语义模型合模  不支持语义和非语义模型合模
  const validExtensions = ['rvt', 'ifc', 'dgn', 'nwd'];
  const isSemanticModel: any = ref(false);
  // 设置合模方式(1为语义模型  2为非语义模型  3语义、非语义模型)
  const checkFileExtensions = (dataArray: any) => {
    let isSemantic = true;
    let onSemantic = true;

    dataArray.forEach((item: any) => {
      const fileExtension = item.data.name.split('.').pop();
      if (validExtensions.includes(fileExtension)) {
        onSemantic = false; // 至少有一个文件语义
      } else {
        isSemantic = false; // 至少有一个文件是非语义
      }
    });

    if (isSemantic) {
      return 1;
    }
    if (onSemantic) {
      return 2;
    }
    return 0;
  };

  // 创建合模
  const addMergaFileHandle = async () => {
    const params = {
      projectId: projectId.value,
      name: mergaForm.name.trim(),
      description: mergaForm.description,
      files: fileIds.value,
      graphicEngineInfo: graphicEngineInfo.value,
      teamId: mergaForm.team,
    };

    try {
      const saveRes: any = await addMergaFile(params);
      if (saveRes.status) {
        Message.success(t('model-collaboration.success'));
        emits('refresh');
        cancel();
      }
    } catch (error: any) {
      showErrorMessageHandle(error.data.code);
    }
  };

  // 合模区分语义模型和非语义模型合模  所选文件全部是ifc和rvt类型则为语义模型  走语义模型合模  其他情况走非语义模型合模   每种合模下需要判断转换状态 状态不为0为转换不成功 则不进行合模
  const XBasemergaSubmit = async () => {
    const info: any = [];
    fileIds.value.forEach((item: any) => {
      info.push(getFileInfoById(item.id));
    });
    const infosRes = await Promise.all(info);
    console.log('[ infosRes ] >', infosRes);

    semanticModelIds.length = 0;
    modelIds.length = 0;
    semanticModelListStatus.length = 0;
    modelListStatus.length = 0;
    infosRes.forEach((item: { data: { graphicEngineInfo: string } }) => {
      semanticModelIds.push(item.data.graphicEngineInfo.split('|')[2]);
      modelIds.push(item.data.graphicEngineInfo.split('|')[0]);
    });

    executeAllOperations()
      .then(async () => {
        // 所有操作完成后执行的代码
        console.log('semanticModelListStatus:', semanticModelListStatus);
        console.log('modelListStatus:', modelListStatus);

        // 若模型转换状态为-10 表示查询状态接口报错  模型有问题 不再继续合模
        if (
          semanticModelListStatus.includes(-10) ||
          modelListStatus.includes(-10)
        )
          return;
        // 设置合模方式
        isSemanticModel.value = await checkFileExtensions(infosRes);
        // 语义模型合模
        if (isSemanticModel.value === 1) {
          let semanticModelState: any = true;
          // 判断模型语义模型状态是否是转换成功
          semanticModelListStatus.forEach((item: any, index: any) => {
            const { name } = infosRes[index].data;
            if (item !== 0) {
              Message.error(
                name + t('model-collaboration.conversion-was-not-successful')
              );
              semanticModelState = false;
              loading.value = false;
            }
          });
          if (!semanticModelState) return;
          const subModelIds = infosRes.map((infoRes) => {
            return infoRes.data.graphicEngineInfo.split('|')[2];
          });
          try {
            const param: any = {
              projectId: projectId.value,
              name: mergaForm.name.trim(),
              subModelIds: subModelIds.join(),
            };
            const mergeRes: any = await createXBaseSemanticModel(param);
            if (mergeRes.status) {
              graphicEngineInfo.value = `|${mergeRes.data.asm_render_path}|${mergeRes.data.asm_model_id}`;
            }
          } catch (error: any) {
            showErrorMessageHandle(error.data.code);
            return;
          }
          await addMergaFileHandle();
        } else if (isSemanticModel.value === 2) {
          // 非语义模型合模
          let modelState: any = true;
          // 判断模型是否转换成功
          modelListStatus.forEach((item: any, index: any) => {
            const { name } = infosRes[index].data;
            if (item !== 0) {
              Message.error(
                name + t('model-collaboration.conversion-was-not-successful')
              );
              modelState = false;
              loading.value = false;
            }
          });
          if (!modelState) return;
          const subModels: any = [];
          modelIds.forEach((item: any) => {
            subModels.push({
              model_id: item,
              lod_level: 'component',
            });
          });
          const params = {
            subModels,
            unit: 1,
            name: mergaForm.name.trim(),
            projectId: projectId.value,
          };

          try {
            const res: any = await createXBaseNoSemanticModel(params);
            if (res.status) {
              graphicEngineInfo.value = `${res.data.model_id}|${res.data.model_path}|`;
            }
          } catch (error: any) {
            showErrorMessageHandle(error.data.code);
            return;
          }
          await addMergaFileHandle();
        } else {
          Message.error(t('model-collaboration.not-support-merge-model'));
          loading.value = false;
        }
      })
      .catch((error) => {
        console.error('Error fetching semantic model list:', error);
      });
  };

  const BIMBasemergaSubmit = async () => {
    const params = {
      projectId: projectId.value,
      name: mergaForm.name.trim(),
      description: mergaForm.description,
      files: fileIds.value,
      teamId: mergaForm.team,
    };
    const saveRes: any = await addMergaFile(params);
    if (saveRes.status) {
      Message.success(t('model-collaboration.success'));
      emits('refresh');
      cancel();
    } else {
      Message.error(saveRes.message);
      loading.value = false;
    }
  };

  const mergaSubmit = async () => {
    const res = await mergaFormRef.value?.validate();
    if (!isEmpty(fileIds.value)) {
      if (!res) {
        loading.value = true;
        if (projectStore.modelEngine === 'XBase') {
          XBasemergaSubmit();
        } else {
          BIMBasemergaSubmit();
        }
      }
    } else {
      Message.info(t('model-collaboration.select-file'));
    }
  };

  const getIdByFileList = (fileList2: any[]) => {
    const ids: any[] = [];
    fileList2?.forEach((item) => {
      if ('folderId' in item) {
        ids.push(item.id);
      }
      if (item.children) {
        ids.push(...getIdByFileList(item.children));
      }
    });
    return ids;
  };

  const fileIdList = computed(() => {
    const result = getIdByFileList(mergaForm.fileList);
    console.log(fileIdList, 444);
    return result;
  });

  const getDirectoryFileInfo = (directory: any) => {
    let total = 0;
    let fileIDList: Array<object> = [];
    if (directory.children?.length) {
      directory.children.forEach((item: any) => {
        if (item.isFileOrFolder === 1) {
          total += 1;
          fileIDList.push({
            id: item.id,
            version: item.version,
            graphicEngineInfo: item.graphicEngineInfo,
            name: item.name,
          });
        } else {
          total += getDirectoryFileInfo(item).total;
          fileIDList = fileIDList.concat(getDirectoryFileInfo(item).fileIDList);
        }
      });
    }
    fileListLength.value = total;
    return { total, fileIDList };
  };
  const fileChange = async (data: () => Promise<any>) => {
    const files = await data();
    mergaForm.fileList = [];
    const { fileIDList } = getDirectoryFileInfo(files[0]);
    if (fileIDList.length + fileIds.value.length < 2) {
      Message.error(t('model-collaboration.select-multi-model'));
      return;
    }
    const isAll = fileIDList.every((item: any) => {
      if (model2d.includes(item.name.split('.').pop())) {
        Message.error(
          `"${item.name}"${t('model-collaboration.nable-select3d')}`
        );
      } else if (projectStore.modelEngine === 'XBase') {
        if (
          item.graphicEngineInfo === null ||
          item.graphicEngineInfo.split('|').length < 3
        )
          Message.error(
            `"${item.name}"${t('model-collaboration.unable-reselect')}`
          );
        else return true;
      } else if (projectStore.modelEngine === 'BimBase') {
        if (
          item.graphicEngineInfo === null ||
          item.graphicEngineInfo.split('|').length < 2
        )
          Message.error(
            `"${item.name}"${t('model-collaboration.unable-reselect')}`
          );
        else return true;
      }
      return false;
    });
    if (isAll) {
      fileIds.value = fileIDList;
      mergaForm.fileList = files;
    }
  };
  const fileCounts = computed(() => {
    let counts = 0;
    mergaForm.fileList?.forEach((file) => {
      const { total } = getDirectoryFileInfo(file);
      counts += total;
    });
    return counts;
  });

  // 扁平化数组
  const flattenTeams = function (arr: any[]): any[] {
    const result: any[] = []; // 递归函数

    function traverse(item: any) {
      console.log(item);
      if (Array.isArray(item) && item.length) {
        // 如果teams是数组，遍历它
        item.forEach((item2: any) => {
          result.push(item2);
          if (item2.teams) {
            // 确保item有teams属性
            traverse(item.teams);
          }
        });
      } // 如果到达没有teams属性的对象或teams为空数组，则不执行任何操作
    } // 对输入数组的每个元素执行递归遍历

    arr.forEach((item) => {
      // 确保item有teams属性
      result.push(item);
      traverse(item.teams);
    });

    return result;
  };

  const getTeamList = async () => {
    const params: any = {
      pageNo: 1,
      pageSize: 10000,
      projectId: projectId.value,
    };
    const res = await queryTeamList(params);
    if (res.status) {
      const arr = flattenTeams(res.data.list);
      console.log(arr, '数组--------------------------------');
      teamOptions.value = arr;
    }
  };
  getTeamList();

  watch(
    () => props.visible,
    (val) => {
      if (!val) {
        mergaForm.fileList = [];
      }
    },
    { deep: true, immediate: true }
  );
</script>

<style scoped lang="less">
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .file-list-wrap {
    margin-top: 16px;
  }
</style>
