<template>
  <div class="create-Template-box">
    <a-modal
      :visible="visible"
      :title="$t('dashboard.create-template')"
      :mask-closable="false"
      :esc-to-close="false"
      draggable
      @cancel="cancel"
      @ok="submitData"
      :render-to-body="false"
    >
      <template #title> {{ $t('dashboard.create-template') }} </template>
      <div class="content">
        <a-tabs v-model:active-key="activeTab" @change="onTabChange">
          <a-tab-pane
            :title="$t('dashboard.create-template-text1')"
            key="blank"
          >
            <a-form :model="formData" label-align="left" ref="formRef">
              <a-row :gutter="12">
                <a-col :span="24">
                  <a-form-item
                    field="name"
                    :label="$t('dashboard.template-name')"
                    label-col-flex="90px"
                    :rules="[
                      {
                        required: true,
                        message: $t('dashboard.template-name-errMsg'),
                      },
                    ]"
                    :validate-trigger="['input']"
                  >
                    <remove-spaces-input
                      v-model="formData.name"
                      :placeholder="$t('dashboard.please-enter')"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-tab-pane>
          <a-tab-pane
            :title="$t('dashboard.create-template-text2')"
            key="project"
          >
            <a-form :model="formData" label-align="left" ref="formRef">
              <a-row :gutter="12">
                <a-col :span="24">
                  <a-form-item
                    field="name"
                    :label="$t('dashboard.template-name')"
                    label-col-flex="90px"
                    :rules="[
                      {
                        required: true,
                        message: $t('dashboard.template-name-errMsg'),
                      },
                    ]"
                    :validate-trigger="['input']"
                  >
                    <remove-spaces-input
                      v-model="formData.name"
                      :placeholder="$t('dashboard.please-enter')"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="12">
                <a-col :span="24">
                  <a-form-item
                    field="protemId"
                    :rules="[{ required: true }]"
                    validate-trigger="none"
                    :label="$t('dashboard.project')"
                    label-col-flex="90px"
                  >
                    <a-select
                      v-model="selectedProject"
                      :placeholder="$t('dashboard.template-name')"
                      allow-search
                      allow-clear
                      @search="onSearchProject"
                      @dropdown-scroll="onDropdownScroll"
                      @dropdown-visible-change="onDropdownVisibleChange"
                      @clear="onClearProjectSearch"
                      @blur="onProjectSelectBlur"
                      :loading="loading"
                    >
                      <a-option
                        v-for="item in projectList"
                        :key="item.id"
                        :value="item.id"
                        :label="item.name"
                      >{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </div>
      <template #footer>
        <div class="footer">
          <a-button class="footer-btn" @click="cancel">{{
            $t('dashboard.cancellation')
          }}</a-button>
          <a-button class="footer-btn" type="primary" @click="submitData">{{
            $t('dashboard.create-template')
          }}</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { defineEmits, defineProps, ref, watch } from 'vue';
  import {
    createProject,
    createTemplate,
    getProjectList,
    ProjectItem,
    ProjectListParams,
    CreateTemplateReqParams,
  } from '@/api/project';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const activeTab = ref('blank');
  const emits = defineEmits(['update:visible', 'submit']);

  const formRef = ref<FormInstance>();

  // 项目类型声明
  interface ProjectOption {
    id: string;
    name: string;
  }

  const projectList = ref<ProjectOption[]>([]);
  const selectedProject = ref<string>('');
  const pageNo = ref(1);
  const pageSize = 20;
  const total = ref(0);
  const loading = ref(false);
  const searchProjectName = ref('');
  let debounceTimer: any = null;

  const getList = async (reset = false) => {
    if (loading.value) return;
    loading.value = true;
    const params: ProjectListParams = {
      pageNo: pageNo.value,
      pageSize,
      projectType: 0,
      name: searchProjectName.value,
    };
    const res = await getProjectList(params);
    if (res.code === 8000000) {
      if (reset) {
        projectList.value = res.data?.list || [];
      } else {
        projectList.value = [...projectList.value, ...(res.data?.list || [])];
      }
      total.value = res.data?.total || 0;
    }
    loading.value = false;
  };

  const onDropdownVisibleChange = (visible: boolean) => {
    if (visible) {
      pageNo.value = 1;
      getList(true);
    }
  };

  const onDropdownScroll = (e: Event) => {
    const target = e.target as HTMLElement;
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
      if (projectList.value.length < total.value) {
        pageNo.value += 1;
        getList();
      }
    }
  };

  const onSearchProject = (value: string) => {
    searchProjectName.value = value;
    if (debounceTimer) clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
      pageNo.value = 1;
      getList(true);
    }, 0);
  };

  const onClearProjectSearch = () => {
    searchProjectName.value = '';
    pageNo.value = 1;
    getList(true);
  };

  const onInputKeydown = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (debounceTimer) clearTimeout(debounceTimer);
      pageNo.value = 1;
      getList(true);
    }
  };

  const onProjectSelectBlur = () => {
    searchProjectName.value = '';
    pageNo.value = 1;
    getList(true);
  };

  watch(selectedProject, (val) => {
    formData.value.protemId = val;
  });

  const formData = ref({
    name: '',
    protemId: '',
  });

  const cancel = () => {
    emits('update:visible', false);
  };

  const submitData = async () => {
    const params: CreateTemplateReqParams = {
      name: formData.value.name,
    };
    if (activeTab.value === 'project') {
      params.projectId = formData.value.protemId;
    }
    const result = await formRef.value?.validate();
    if (!params.name || (activeTab.value === 'project' && !params.projectId)) {
      Message.error(t('dashboard.please-input-complete-template-information'));
    } else {
      createTemplate(params as unknown as CreateTemplateReqParams).then(
        (res: any) => {
          if (res.code === 8000000) {
            Message.success(res.message);
            cancel();
            emits('submit', true);
          }
        }
      );
    }
  };

  const init = () => {
    activeTab.value = 'blank';
    formData.value = {
      name: '',
      protemId: '',
    };
    searchProjectName.value = '';
    formRef.value?.clearValidate();
    getList();
  };

  watch(
    () => props.visible,
    (val) => {
      if (val) {
        init();
      }
    }
  );

  const onTabChange = () => {
    // 清空所有相关变量
    formData.value.name = '';
    formData.value.protemId = '';
    searchProjectName.value = '';
    selectedProject.value = '';
    // 如果有其他需要清空的变量，也在这里加
    // 重新校验表单（可选）
    formRef.value?.clearValidate();
    pageNo.value = 1;
  };
</script>

<style scoped lang="less">
  .content {
    padding: 0;
  }
  .footer-btn {
    margin-left: 8px;
  }
  :deep(.arco-tabs-content) {
    padding-top: 16px;
  }
  .create-Template-box {
    :deep(.arco-modal-body) {
      padding: 10px 20px !important;
    }
  }
</style>
