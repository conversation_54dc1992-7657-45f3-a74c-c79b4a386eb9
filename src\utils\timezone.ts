// 判断是否是特殊时区
function isSpecialTimezone(timezone = '') {
  return (
    timezone === '(UTC) 协调世界时' ||
    timezone === '(UTC+00:00) 卡萨布兰卡' ||
    timezone === '(UTC+00:00) 蒙罗维亚，雷克雅未克'
  );
}

// 获取时区标记
export function getUTCStr(UTC = '') {
  const timezone = UTC;
  if (isSpecialTimezone(timezone)) return 'UTC';
  if (String(timezone).indexOf('UTC') === -1) return '';
  const strList = timezone.match(/UTC([+|-])(\d+):(\d+)/);
  return strList?.[0];
}

// 当前时区时间戳 -> 当前时区时间格式
export function getUTCOffset(UTC = '') {
  const timezone = UTC;
  if (isSpecialTimezone(timezone) || String(timezone).indexOf('UTC') === -1)
    return 0;
  const strList = timezone.match(/UTC([+|-])(\d+):(\d+)/) || '';
  const num =
    (parseInt(strList[1], 10) +
      (parseInt(strList[2], 10) * 60 + parseInt(strList[3], 10))) *
    60000;
  return num;
}
