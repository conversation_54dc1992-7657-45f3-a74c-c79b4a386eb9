import { last } from 'lodash';
import wpsJson from '@/config/wpsType.json';
import imgJson from '@/config/imgType.json';
import { wpsViewHandle } from '@/hooks/wps';
import router from '@/router';
import getFileTypeStatus from '@/utils/BIM-Engine/XBase/utils/status-info';
import { Message } from '@arco-design/web-vue';
import { usePrjPermissionStore } from '@/store';
import api from '@/utils/common/api';
import useFileStore from '@/store/modules/file';

const projectStore = usePrjPermissionStore();

interface Params {
  [key: string]: any;
}
// 添加或修改参数
const addParams = (record: any, projectId: any, params: Params = {}) => {
  // 基本查询对象，包括idStr和projectId
  const query: Params = {
    idStr: record.fileId || record.id,
    projectId,
    // noIssue: [1, 2, 3, 4].includes(fileManageStore.hiddenSlot),
  };

  // 遍历params对象，将每个键值对添加到query对象中
  Object.keys(params).forEach((key) => {
    query[key] = params[key];
  });

  // 返回构建好的查询对象
  return query;
};

// 流程中需要查询状态
const updateStatus = async (fileId: string) => {
  const result = await api.getFileInfoById(fileId);
  return result.data.status;
};

const fileStore = useFileStore();
// 第三个参数为新增或者修改的参数
// 需要流程中需要查询状态传入isUpdateStatus为true
export default async (
  record: any,
  projectId: string,
  params?: Params | undefined
) => {
  const type = last(record.name.split('.')) as string;
  const isWpsType: boolean = wpsJson.includes(type);
  const isImgType: boolean = imgJson.includes(type);
  // 需要更新参数时传入isUpdateStatus为true
  if (params?.isUpdateStatus) {
    record.status = await updateStatus(record.fileId);
    delete params.isUpdateStatus;
  }
  // 图片预览
  if (isImgType) {
    fileStore.setImgViewModal(true, record);
  } else if (isWpsType) {
    // wps预览
    wpsViewHandle(record, 'preview', 'admin');
  } else if (projectStore.modelEngine === 'XBase') {
    // 大象云预览
    if (record.status === 0 || (record.status === 3 && type === 'dwg')) {
      const url = router.resolve({
        path: `/bim-view`,
        query: addParams(record, projectId, params),
      }).href;
      window.open(url);
    } else {
      const statusInfo = getFileTypeStatus(type, record.status?.toString());
      Message.error(statusInfo);
    }
  }
};
