<template>
  <div class="sketch-container">
    <!-- <div class="title">
      <span>{{
        $t('project-setting.schematic-diagram-of-milestone-nodes')
      }}</span>
    </div> -->

    <!-- 固定文字宽度效果
    默认文字宽度80px，横线宽度120px -->
    <div
      v-for="(nodeAry, rowIndex) in showNodeRows"
      :key="rowIndex"
      class="milestone-row"
      :style="{
        width:
          rowIndex === 0
            ? (nodeAry.length - 1) * 276 + 120 + 'px'
            : (rowSize - 1) * 276 + 120 + 'px',
      }"
    >
      <a-steps
        :class="{
          'align-right': rowIndex % 2 === 1,
        }"
      >
        <a-step
          v-for="(item, itemIndex) in rowIndex % 2 === 1
            ? nodeAry.reverse()
            : nodeAry"
          :key="item.id"
          status="process"
          :description="item.endTime"
        >
          <a-tooltip :content="item.describe || item.name">
            <span class="node-title">{{ item.name }} </span>
          </a-tooltip>
          <template #icon>
            <!-- 偶数行，图标序号倒序排列 -->
            <template v-if="rowIndex % 2 === 1">{{
              rowIndex * rowSize + nodeAry.length - itemIndex
            }}</template>
            <template v-else>{{ rowIndex * rowSize + itemIndex + 1 }}</template>
          </template>
        </a-step>
      </a-steps>

      <!-- 换行线 -->
      <div
        v-if="
          showAll && rowIndex % 2 === 0 && rowIndex != allNodeRows.length - 1
        "
        class="right-wrap-line"
        :style="{ left: (rowSize - 1) * 276 + 132 + 'px' }"
      ></div>
      <div
        v-if="
          showAll && rowIndex % 2 === 1 && rowIndex != allNodeRows.length - 1
        "
        class="left-wrap-line"
      ></div>
    </div>

    <div v-show="allNodeRows.length !== 1" class="btn">
      <img
        :src="openIcon"
        :class="{ 'overturn-img': showAll }"
        @click="handleExpand"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import openIcon from '@/assets/images/project-setting/<EMAIL>';
  import { MilestoneRecord } from '../api';

  defineProps({
    allNodeRows: {
      type: Array<MilestoneRecord[]>,
      default() {
        return [];
      },
    },
    showNodeRows: {
      type: Array<MilestoneRecord[]>,
      default() {
        return [];
      },
    },
    showAll: {
      type: Boolean,
      default: false,
    },
    rowSize: {
      type: Number,
      default: 4,
    },
  });

  const emit = defineEmits(['expand']);

  const handleExpand = () => {
    emit('expand');
  };
</script>

<style lang="less" scoped>
  .sketch-container {
    max-height: 393px;
    overflow: auto;
    position: relative;
    width: 100%;
    border-radius: 4px;
    border: 1px solid var(--color-border-2);
    padding-top: 40px;
    .title {
      margin: 24px 0;
      text-align: center;
      font-size: 20px;
      line-height: 24px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: #4e5969;
    }

    .milestone-row {
      position: relative;
      margin: 0 auto;
      margin-bottom: 16px;

      /* 偶数行右对齐 */
      .align-right {
        justify-content: flex-end;
      }

      :deep(.arco-steps-item) {
        width: 264px;
        flex: none;
      }

      :deep(.arco-steps-item:last-child) {
        // width: 120px;
        .arco-steps-item-title {
          padding-right: 0;
        }
      }

      .node-title {
        display: inline-block;
        width: 96px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .right-wrap-line {
        position: absolute;
        top: 13.5px;
        width: 66px;
        height: 69px;
        border: 1px solid var(--color-neutral-3);
        border-left: none;
      }

      .left-wrap-line {
        position: absolute;
        left: -80px;
        top: 13.5px;
        width: 66px;
        height: 69px;
        border: 1px solid var(--color-neutral-3);
        border-right: none;
      }
    }

    .btn {
      margin: -7px 16px 16px;
      text-align: right;

      img {
        width: 34px;
        height: 34px;
      }

      .overturn-img {
        transform: rotate(180deg);
      }
    }
  }
</style>
