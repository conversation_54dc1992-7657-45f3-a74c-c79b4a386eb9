import { defineStore } from 'pinia';
import { DesignState, conferenceParams } from './types';

const useConferenceStore = defineStore('conference', {
  state: (): DesignState => {
    return {
      conferenceData: [],
      searchFlag: false,
      itemList: [],
      createConferenceVisible: false,
      curConferenceData: null,
      cardEditFlag: false,
      listEditFlag: false,
      curClickFileId: '',
      conferenceEditFlag: false,
      scrollFlag: false,
    };
  },
  actions: {
    // Set information
    setInfo(partial: Partial<DesignState>) {
      this.$patch(partial);
    },
    setCreateConferenceVisible(value: boolean) {
      this.createConferenceVisible = value;
    },
    setCurConferenceData(detail: any) {
      this.curConferenceData = detail;
    },
    setCurClickFileId(id: string) {
      this.curClickFileId = id;
    },

    // Reset information
    resetInfo() {
      this.$reset();
    },
  },
});

export default useConferenceStore;
