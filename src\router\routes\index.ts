import type { RouteRecordNormalized } from 'vue-router';

// const workModules = import.meta.glob('./workModules/*.ts', { eager: true });
// const projectModules = import.meta.glob('./projectModules/*.ts', {
//   eager: true,
// });
// const externalModules = import.meta.glob('./externalModules/*.ts', {
//   eager: true,
// });
const workModules = import.meta.webpackContext('./workModules', {
  recursive: true,
  regExp: /\.ts$/,
});
const projectModules = import.meta.webpackContext('./projectModules', {
  recursive: true,
  regExp: /\.ts$/,
});
const externalModules = import.meta.webpackContext('./externalModules', {
  recursive: true,
  regExp: /\.ts$/,
});

function formatModules(_modules: any, result: RouteRecordNormalized[]) {
  // eslint-disable-next-line no-restricted-syntax
  for (const path of _modules.keys()) {
    const mod = _modules(path);
    // eslint-disable-next-line no-unused-expressions
    mod.default ? result.push( mod.default) : ''
  }

  // Object.keys(_modules).forEach((key) => {
  //   const defaultModule = _modules[key].default;
  //   if (!defaultModule) return;
  //   const moduleList = Array.isArray(defaultModule)
  //     ? [...defaultModule]
  //     : [defaultModule];
  //   result.push(...moduleList);
  // });
  return result;
}

export const workRoutes: RouteRecordNormalized[] = formatModules(
  workModules,
  []
);

let projectRoutesNew: RouteRecordNormalized[] = formatModules(
  projectModules,
  []
);

// 根据用户权限是否添加用户中心菜单
const userAdmin = localStorage.getItem('userAdmin');
console.log(userAdmin, 7778);
if (!(userAdmin === '0' || userAdmin === '3')) {
  const pathsToExclude = ['user'];
  projectRoutesNew = projectRoutesNew.filter(
    (item: any) => !pathsToExclude.includes(item.path)
  );
}

export const projectRoutes: RouteRecordNormalized[] = projectRoutesNew;
export const appExternalRoutes: RouteRecordNormalized[] = formatModules(
  externalModules,
  []
);
