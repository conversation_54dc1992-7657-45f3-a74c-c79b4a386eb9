import { defineStore } from 'pinia';
import { ModelToolsState } from './types';
// import { getViewList } from '@/views/model-view/components/viewManageGl/api';

const useModelToolsStore = defineStore('model-viewer', {
  state: (): ModelToolsState => ({
    threeDMeasurementActive: false,
    issueListToolActive: false,
    createIssueToolActive: false,
    issueId: '',
    issueReplyActive: false,
    issueReplyModel: 'view',
    issueReplyData: {},
    modelViewWidth: '100%',
    attributeListToolActive: false,
    compareInfoShow: false,
    collisionInfoShow: false,
    annotationStart: false,
    quantitiesToolActive: false,
    viewManageGLVisible: false, // 构力模型视图管理弹窗开关
    startViewData: null, // 构力模型初始视点
    markManageGLVisible: false, // 构力模型标注管理弹窗开关
    nowPointData: null, // 当前点击构件数据
    temporaryModelLoadFlag: false, // 暂存区交付标准重命名展示模型标志
    temporaryModelData: '', // 暂存区交付标准重命名展示模型数据
    navVisible: false, // 构力模型导航弹窗开关
    bidimensionalVisible: false,
    navManageVisible: false, // 构力模型导航管理弹窗开关
    twoViewToolActive: false,
    modelAssemblyActive: false, // 模型装配弹窗
    motionPath: '', // 模型装配-旋转path
    ModelAssemblyType: null, // 模型装配类型
    modalTreeIsShow: false, // 模型树显示条件
    propertiesPanelShow: false, // 属性面板显示条件
    propertiesData: [], // 属性面板数据
    alertShow: false, // 创建问题提示框
    alertSubmit: false, // 创建问题提示框提交按钮
  }),

  actions: {
    setModalTreeIsShow(value: boolean) {
      this.modalTreeIsShow = value;
    },

    setPropertiesPanelShow(value: boolean) {
      this.propertiesPanelShow = value;
    },
    set3DMeasurement(value: boolean) {
      this.threeDMeasurementActive = value;
    },
    setIssueModal(value: boolean, issueId?: string) {
      this.issueListToolActive = value;
      this.issueId = issueId || '';
      if (value) {
        this.modelViewWidth = 'calc(100% - 400px)';
      } else {
        this.modelViewWidth = '100%';
      }
    },
    setQuantitiesModel(value: boolean) {
      this.quantitiesToolActive = value;
    },

    setCreateIssueModal(value: boolean) {
      this.createIssueToolActive = value;
    },
    setIssueReplyModal(value: boolean) {
      this.issueReplyActive = value;
    },
    setIssueReplyModel(value: string) {
      this.issueReplyModel = value;
    },
    setIssueReplyData(value: any) {
      this.issueReplyData = value;
    },
    setPropertiesData(value: any) {
      this.propertiesData = value;
    },
    setAnnotation(value: any) {
      this.annotationStart = value;
    },
    // 设置构力模型视图弹窗开关
    setViewManageGLVisible(val: any) {
      this.viewManageGLVisible = val;
    },
    // 设置构力标注管理弹窗开关
    setMarkManageGLVisible(val: any) {
      this.markManageGLVisible = val;
    },
    // 设置构力模型导航弹窗开关
    setNavVisible(val: any) {
      console.log(val);
      this.navVisible = val;
    },
    // 设置构力模型导航管理弹窗开关
    setNavManageVisible(val: any) {
      this.navManageVisible = val;
    },
    // 设置当前构件点击数据
    setNowPointData(val: any) {
      this.nowPointData = val || '';
    },
    // 构力模型获取初始视点
    async setStartView(fileId: any) {
      // const params = {
      //   pageNo: 1,
      //   pageSize: 20,
      //   fileId,
      //   initial: '1',
      // };
      // const { data } = await getViewList(params);
      // this.startViewData = data?.list || [];
    },

    set2dView(value: any) {
      this.twoViewToolActive = value;
    },

    setAttributeModal(value: boolean) {
      this.attributeListToolActive = value;
      if (value) {
        this.modelViewWidth = 'calc(100% - 400px)';
      } else {
        this.modelViewWidth = '100%';
      }
    },
    setCompareInfoShow(value: boolean) {
      this.compareInfoShow = value;
      if (value) {
        this.modelViewWidth = 'calc(100% - 400px)';
      } else {
        this.modelViewWidth = '100%';
      }
    },
    setCollisionInfoShow(value: boolean) {
      this.collisionInfoShow = value;
      if (value) {
        this.modelViewWidth = 'calc(100% - 400px)';
      } else {
        this.modelViewWidth = '100%';
      }
    },
    // 设置二三维联动标签是否显示
    setBidimensionalVisible(val: any) {
      this.bidimensionalVisible = val;
    },
    // 模型装配弹窗开关
    setModelAssemblyActivel(value: boolean) {
      this.modelAssemblyActive = value;
    },
    // 设置模型装配选中模型motionPath
    setMotionPath(value: string) {
      this.motionPath = value;
    },
    // 设置模型装配类型 translation/rotate
    setModelAssemblyType(val: any) {
      this.ModelAssemblyType = val;
    },

    // 创建问题提示框是否显示
    setAlertShow(value: boolean) {
      this.alertShow = value;
    },

    // 创建问题提示框提交成功
    setAlertSubmit(value: boolean) {
      this.alertSubmit = value;
    },
  },
});

export default useModelToolsStore;
