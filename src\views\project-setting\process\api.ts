import axios from 'axios';

export const createProcess = (data: any) => {
  return axios.post('/cde-collaboration/process/save', data);
};

export const getProcessList = (data: any) => {
  return axios.get('/cde-collaboration/process/list', { params: data });
};

export const deployProcess = (data: { modelId: string; modelKey: string }) => {
  return axios.post('/cde-collaboration/process/deployModel', data);
};

export const delProcess = (ids: string) => {
  return axios.get(`/cde-collaboration/process/remove?ids=${ids}`);
};

export const updateProcess = (data: any) => {
  return axios.post('/cde-collaboration/process/update', data);
};

// 分页查询参数
export interface TeamSearchParams {
  pageNo: number;
  pageSize: number;
  projectId: string;
  name?: string;
}

// 查询团队列表
export function queryTeamList(params: TeamSearchParams) {
  return axios.get('/cde-collaboration/team/list', {
    params,
  });
}

export default null;
