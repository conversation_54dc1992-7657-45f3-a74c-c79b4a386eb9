<template>
  <div class="navbar">
    <div class="left-side">
      <a-space @click="toDashboard">
        <img alt="logo" src="@/assets/images/logo-top.png" class="logo" />
        <a-typography-title
          :style="{
            margin: 0,
            fontSize: isPc ? '16px' : '12px',
            fontWeight: 'bold',
            whiteSpace: 'nowrap',
          }"
          :heading="5"
        >
          {{ $t('navbar.logo.title') }}
        </a-typography-title>
      </a-space>
      <a-divider
        direction="vertical"
        :margin="24"
        :style="{ fontSize: '28px' }"
      />
      <span class="file-name" :style="{ width: isPc ? 'auto' : '140px' }">{{
        fileName
      }}</span>
    </div>
    <div v-if="!funIsShow" class="right-side">
      <slot name="toolBar"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  defineProps({
    fileName: {
      type: String,
      default: '',
    },
  });
  const isPc = !(window.innerWidth <= 768);
  const route = useRoute();
  const router = useRouter();
  // 预览部分功能展示条件
  const funIsShow = route.query.someFunIsShow;
  const toDashboard = () => {
    if (!isPc.value) return;
    const url = router.resolve({
      path: '/dashboard',
    });
    // 打开新窗口
    window.open(url.href);
  };
</script>

<style scoped lang="less">
  .navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    background-color: var(--color-bg-2);
    border-bottom: 1px solid var(--color-border);
  }

  .left-side {
    display: flex;
    align-items: center;
    padding-left: 8px;
    font-size: 16px;
    cursor: pointer;
    .logo {
      width: 42px;
    }
  }
  .right-side {
    padding-right: 32px;
  }
  .file-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
