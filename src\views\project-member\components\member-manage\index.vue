<template>
  <div class="member-container">
    <div class="search-area">
      <a-row :gutter="16">
        <a-col :flex="1">
          <a-form :model="formModel" label-align="left">
            <a-row :gutter="16">
              <a-col flex="312px">
                <a-form-item
                  field="name"
                  :label="$t('prjMember.search.name')"
                  label-col-flex="60px"
                >
                  <a-input
                    v-model="formModel.name"
                    :placeholder="$t('prjMember.search.name.placeholder')"
                    @keyup.enter="search"
                    @clear="reset"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-col :flex="'auto'" style="text-align: right">
          <a-space :size="8">
            <a-button 
              v-if="isPrjAdmin" 
              v-permission="`${$btn.team.addMember}`" 
              type="primary" 
              @click="addMember"
            >
              {{ $t('prjMember.table.opt.add') }}
            </a-button>
            <a-button
              v-if="!isProjectTemplate"
              type="primary"
              v-permission="$btn.team.invitateMember"
              @click="inviteRegister"
            >
              {{ $t('prjMember.table.opt.invite.register') }}
            </a-button>
            <a-button
              v-if="!isProjectTemplate"
              v-permission="$btn.team.importMember"
              type="outline"
              @click="importMembers"
            >
              {{ $t('prjMember.table.opt.import') }}
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider margin="20px 0 0 0" />
    </div>
    <div class="member-box">
      <memberList ref="memberRef" class="m-left"></memberList>
      <invitationRecord
        v-if="!isProjectTemplate"
        ref="invitationRef"
        class="m-right"
      ></invitationRecord>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, provide, computed } from 'vue';
  import memberList from './member-list/index.vue';
  import invitationRecord from './Invitation-record/index.vue';
  import { useUserStore, usePrjPermissionStore } from '@/store';

  const invitationRef: any = ref(null);
  const memberRef: any = ref(null);
  const userStore = useUserStore();
  const prjPermissionStore = usePrjPermissionStore();
  const isProjectTemplate = computed(() => userStore.projectTemplate === '1');
  const { isPrjAdmin } = prjPermissionStore;

  // 定义搜索表单模型
  const formModel = ref({
    name: undefined,
  });

  // 搜索方法 - 同时触发两个组件的搜索
  const search = () => {
    console.log('触发父组件search方法');
    memberRef.value?.search();
    console.log('触发父组件search方法',!isProjectTemplate.value);
    console.log('触发父组件search方法',!isProjectTemplate.value && invitationRef.value && typeof invitationRef.value.search === 'function');
    if (!isProjectTemplate.value && invitationRef.value && typeof invitationRef.value.search === 'function') {
      console.log('触发父组件search方法',invitationRef.value);
      invitationRef.value?.search();
    }
  };

  // 重置方法 - 同时触发两个组件的重置
  const reset = () => {
    console.log("触发父组件的clear");
    formModel.value.name = undefined;
    if (memberRef.value && typeof memberRef.value.search === 'function') {
      memberRef.value.search();
    }
    if (!isProjectTemplate.value && invitationRef.value && typeof invitationRef.value.search === 'function') {
      invitationRef.value.search();
    }
  };

  // 添加成员按钮
  const addMember = () => {
    if (memberRef.value && typeof memberRef.value.add === 'function') {
      memberRef.value.add();
    }
  };

  // 邀请注册按钮
  const inviteRegister = () => {
    if (invitationRef.value && typeof invitationRef.value.produceLinkHandle === 'function') {
      invitationRef.value.produceLinkHandle();
    }
  };

  // 导入成员按钮
  const importMembers = () => {
    if (invitationRef.value  && typeof invitationRef.value.changeImportDialogVisible === 'function') {
      // invitationRef.value.importDialogVisible = true;
      invitationRef.value.changeImportDialogVisible();
    }
  };

  // 成员列表导入后刷新邀请记录列表
  const handleDataFromUpload = (data: any) => {
    // invitationRef.value.search();
    // memberRef.value.search();
    search();
  };
  provide('handleData', handleDataFromUpload);

  // 向子组件提供 formModel
  provide('parentFormModel', formModel);
</script>

<script lang="ts">
  export default {
    name: 'MemberManage',
  };
</script>

<style scoped lang="less">
  .member-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .search-area {
    // margin-bottom: 16px;
    padding: 20px 20px 0 20px;
  }
  
  .member-box {
    display: flex;
    flex: 1;
    overflow: hidden;
    
    .m-left {
      border-right: 1px solid #efefef;
    }
  }
</style>
