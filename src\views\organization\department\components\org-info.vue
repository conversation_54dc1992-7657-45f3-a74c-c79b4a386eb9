<template>
  <a-modal
    :visible="visible"
    :title="title"
    :hide-cancel="true"
    :unmount-on-close="true"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-form
      ref="deptRef"
      :model="formData"
      :label-col-props="{ span: currentLocale === 'en-US' ? 8 : 5 }"
      :wrapper-col-props="{ span: currentLocale === 'en-US' ? 16 : 19 }"
    >
      <a-form-item
        field="name"
        :label="$t('department.columns.name')"
        validate-trigger="input"
        :rules="[
          {
            required: true,
            message: $t('department.columns.name-require'),
          },
        ]"
      >
        <remove-spaces-input
          v-model="formData.name"
          :max-length="currentLocale === 'en-US' ? 255 : 50"
          show-word-limit
        />
      </a-form-item>
      <a-form-item
        field="entName"
        :label="$t('department.columns.abbr')"
        validate-trigger="input"
        :rules="[
          {
            required: true,
            message: $t('department.columns.abbr-require'),
          },
        ]"
      >
        <remove-spaces-input
          v-model="formData.entName"
          :max-length="currentLocale === 'en-US' ? 255 : 50"
          show-word-limit
        />
      </a-form-item>
      <a-form-item
        :label="$t('department.columns.orgType')"
        validate-trigger="input"
      >
        <a-select
          v-model="formData.orgType"
          disabled
          placeholder="Please select ..."
        >
          <a-option
            v-for="item in orgTypeOption"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="parentId"
        :label="$t('department.columns.superior-department')"
        :rules="[
          {
            required: false,
            message: $t('department.columns.superior-department-require'),
          },
        ]"
      >
        <!-- 部门选择器 -->
        <OrgSelector
          v-model:deptId="formData.parentId"
          :disabled="disableOrg"
          @change="handleDeptChange"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import OrgSelector from '@/components/org-selector/index.vue';
  import { usePortalStore } from '@/store';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import {
    SelectDept,
    addOrgs,
    OrgServiceRecord,
    modifyOrg,
  } from '@/api/modules/department';
  // import { useI18n } from 'vue-i18n';
  // import useLoading from '@/hooks/loading';
  import useLocale from '@/hooks/locale';
  import removeSpacesInput from '@/components/removeSpacesInput/index.vue';


  const { currentLocale } = useLocale();

  const portalStore = usePortalStore();

  const orgTypeOption: any = [
    {
      id: '0',
      name: '内部单位',
    },
    {
      id: '1',
      name: '外部单位',
    },
  ];
  console.log('portalStore', portalStore);

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
      default() {
        return { name: '', entName: '', parentId: '', orgType: '' };
      },
    },
  });
  const emit = defineEmits(['update:visible', 'refresh']);

  const title = ref('');
  const formData = ref<SelectDept>({ ...props.form } as SelectDept);
  watch(
    () => props.form,
    (n) => {
      formData.value = { ...n } as SelectDept;
      if (formData.value.operator === 'edit') title.value = '编辑';
      else if (formData.value.operator === 'add') title.value = '新增';
      else title.value = '新增子部门';
    }
  );
  const deptRef = ref<FormInstance>();

  const disableOrg = computed(() => {
    return !!props.form.parentId;
  });

  const handleBeforeOk = async (done: any) => {
    const res = await deptRef.value?.validate();
    if (!res) {
      let flg = false;
      let msg = '添加成功';
      // 编辑组织机构
      if (formData.value.operator === 'edit') {
        flg = await editOrg();
        msg = '修改成功';
      } else if (formData.value.operator === 'add') {
        if (formData.value.pathNo) {
          // 新增子组织机构
          flg = await addSubOrg();
        } else {
          // 新增根组织机构
          flg = await addOrg();
        }
      } else if (formData.value.operator === 'addSub') {
        // 新增子组织机构
        flg = await addSubOrg();
      } else {
        // nothing to do here
      }
      if (flg) {
        Message.success(msg);
        emit('update:visible', false);
        emit('refresh');
      }
      done();
    }
  };
  const editOrg = async () => {
    const params = {
      ...formData.value.content,
      name: formData.value.name,
      entName: formData.value.entName,
      orgType: formData.value.orgType,
    };
    const res = await modifyOrg(params);
    return !!res.status;
  };
  // 新增根节点下的组织机构，即最大的组织机构
  const addOrg = async () => {
    const params = {
      name: formData.value.name,
      entName: formData.value.entName,
      parentName: '根节点',
      pathName: `.根节点.${formData.value.name}.`,
      portalId: portalStore.portalId,
      type: portalStore.currentPortal.type,
      orgType: formData.value.orgType,
    };
    const res = await addOrgs(params);
    return !!res.status;
  };
  const addSubOrg = async () => {
    const params = {
      name: formData.value.name,
      entName: formData.value.entName,
      parentName: formData.value.parentName,
      parentNo: formData.value.parentId,
      pathName: `${formData.value.pathName}${formData.value.name}.`,
      pathNo: `${formData.value.pathNo}`,
      portalId: portalStore.portalId,
      type: portalStore.currentPortal.type,
      orgType: formData.value.orgType,
    };
    const res = await addOrgs(params);
    return !!res.status;
  };
  const handleCancel = () => {
    emit('update:visible', false);
  };

  const handleDeptChange = (val: OrgServiceRecord) => {
    if (val && val.content) {
      formData.value.parentName = val.content.parentName;
      formData.value.pathName = val.content.pathName;
      formData.value.pathNo = val.content.pathNo;
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'OrgInfo',
  };
</script>
