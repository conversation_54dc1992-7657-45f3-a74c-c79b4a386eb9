<template>
  <div class="project-file-panel">
    <div class="file-tree-wrap">
      <FileTree
        ref="fileTree"
        :folder-data="allFolderData"
        @node-click="nodeClick"
        @refresh="treeRefresh"
      ></FileTree>
    </div>
    <div class="content-panel">
      <div class="content-header">
        <div class="header-buttons">
          <div class="header-title">
            <img src="@/assets/images/table-title.png" />
            <div class="text">
              {{ i18FolderName(currentFolder) }}
            </div>
            <Breadcrumb class="breadcrumb" @expend-folder="expendFolderNode" />
          </div>
        </div>
        <a-divider />
      </div>

      <div style="padding: 12px 20px; display: flex">
        <a-space>
          <a-button
            :disabled="batchController"
            type="primary"
            style="gap: 4px"
            @click="handleBindEvents(null)"
          >
            <MultiBindIcon />
            {{ $t('standard-named.batch-binding') }}
          </a-button>
          <a-popconfirm
            :content="$t('standard-named.should-verification-initiated')"
            position="right"
            @ok="batchCheckFile(null)"
          >
            <a-button
              :disabled="batchController"
              type="outline"
              style="gap: 4px"
            >
              <MultiCheckIcon class="multi-check-icon" />
              {{ $t('standard-named.batch-check') }}
            </a-button>
          </a-popconfirm>
        </a-space>
      </div>
      <div class="folder-table-wrap">
        <FolderTable
          ref="folderTable"
          @expend-folder="expendFolderNode"
          @handle-bind="handleBindEvents"
          @handle-test="batchCheckFile"
        />
      </div>
    </div>
  </div>
  <a-modal
    v-model:visible="bindModalvisible"
    @cancel="bindModalvisible = false"
  >
    <template #title> {{ $t('standard-named.binding-standard') }} </template>
    <a-space style="justify-content: center; width: 100%">
      <span>{{ $t('standard-named.standard-name') }}：</span>
      <a-select
        v-model:model-value="checkedStandard"
        :style="{ width: '320px' }"
        :placeholder="$t('standard-named.please-select')"
      >
        <a-option
          v-for="item in standardData"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </a-select>
    </a-space>
    <template #footer>
      <a-space>
        <a-button type="secondary" @click="handleCancelModal"
          >{{ $t('standard-named.cancel') }}
        </a-button>
        <a-button
          type="primary"
          :loading="btnLoading"
          @click="handleBindAndCheck"
          >{{ $t('standard-named.ok-and-check') }}
        </a-button>
        <a-button type="primary" :loading="btnLoading" @click="handleBindOk"
          >{{ $t('standard-named.ok') }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { computed, nextTick, ref } from 'vue';
  import {
    FileAndFolderMessage,
    FolderMessage,
    getChildFolderList,
  } from '@/api/tree-folder';

  import FileTree from './file-tree.vue';
  import FolderTable from './folder-table.vue';

  import Breadcrumb from '@/views/projectSpace/file/components/file-panel/breadcrumb.vue';
  import useFileStore from '@/store/modules/file/index';

  import { storeToRefs } from 'pinia';
  import { isTopFolder } from '@/views/projectSpace/file/utils';
  import { useUserStore } from '@/store';
  import {
    standardList,
    standardParams,
  } from '../../standard-list/standard/api';
  import { Message } from '@arco-design/web-vue';

  import {
    bindStandardByMultiFolder,
    bindStandardByMultiFiles,
  } from '@/views/standard-manage/attrCheck/api';
  import { batchCheck } from '../api';
  import { useI18n } from 'vue-i18n';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';
  import MultiBindIcon from '@/assets/images/standard/checkbox-multiple-line.svg';
  import MultiCheckIcon from '@/assets/images/standard/file-shield-2-line.svg';

  const fileStore = useFileStore();
  const userStore = useUserStore();

  const { t } = useI18n();
  const currentFolder = computed(() => fileStore.currentFolder);
  const { standradTreeData: allFolderData, selectedKeys } =
    storeToRefs(fileStore);

  const standardData = ref<any[]>([]);
  const route = useRoute();

  const projectId = route.params.projectId as string;
  const getFolder = (type = 'WIP', parentId = '0') => {
    return getChildFolderList(projectId, '', type, parentId);
  };

  const fileTree = ref();
  const { i18FolderName } = useI18nHandleName();

  function expendFolderNode(record: FolderMessage) {
    fileTree.value.setNodeSelected(record);
  }

  const getWIPFolder = async () => {
    const res = await getFolder('WIP');
    if (res.status) {
      allFolderData.value[0].children = res.data?.list || [];
    }

    fileTree.value.setNodeSelected(allFolderData.value[0]);
  };

  const getStandards = async (
    params: standardParams = {
      pageNo: 1,
      pageSize: 1000,
      groupId: projectId,
      standardType: 0,
    }
  ) => {
    const { admin } = userStore;

    if (admin === 0) {
      params.groupId = '0';
    } else {
      params.groupId = projectId;
    }
    try {
      const { data } = await standardList(params);
      standardData.value = data.list;
    } catch (err) {
      console.log(err);
    }
  };

  const init = () => {
    getStandards();
    // 有浏览记录，还原
    if (selectedKeys.value.length) {
      nextTick(() => {
        fileTree.value.setNodeSelected(currentFolder.value);
      });
      return;
    }
    getWIPFolder();
  };
  init();

  const nodeClick = async (data: any, callback: () => void) => {
    const { nodeInfo } = data;

    if (isTopFolder(nodeInfo.id)) {
      const res = await getFolder(nodeInfo.name);
      if (res.status) {
        nodeInfo.children = res.data?.list || [];
      }
    } else {
      const res = await getFolder(nodeInfo.type, nodeInfo.id);
      if (res.status) {
        nodeInfo.children = res?.data?.list || [];
      }
    }

    fileStore.setCurrentFolder(nodeInfo);
    fileStore.setAllTreeData();
    await nextTick();
    callback();
  };

  async function treeRefresh(current: any) {
    const res = await getFolder(current.type, current.id);
    if (res.status) {
      current.children = res?.data?.list || [];
    }
  }

  const bindModalvisible = ref(false);

  const checkedStandard = ref('');
  const btnLoading = ref(false);

  const { tableData, selectedTableRowkeys } = storeToRefs(fileStore);
  const checkedTableRows = computed(() =>
    tableData.value.filter(
      (row) =>
        selectedTableRowkeys.value.findIndex((key) => key === row.id) !== -1
    )
  );

  const batchController = computed(() => {
    return (
      !selectedTableRowkeys.value.length ||
      checkedTableRows.value.findIndex((item) => !item.folderId) !== -1
    );
  });

  const currentRecord = ref<FileAndFolderMessage | null>(null);
  const folderTable = ref<any>(null);

  async function batchCheckFile(record: any) {
    const params = {
      fileIdList: [] as string[],
      teamFolderId: null,
    };

    console.log(record, 239);
    // 组装参数
    if (record) {
      params.fileIdList.push(record.id);
    } else {
      checkedTableRows.value.forEach((row: any) => {
        params.fileIdList.push(row.id);
      });
    }
    const res = await batchCheck(params);

    if (res.status) {
      Message.success(t('standard-named.check-success-result'));
    }
    folderTable.value?.refreshTableData();
  }

  function handleParams() {
    const params = {
      standardId: checkedStandard.value,
      type: 2,
      fileIds: [] as string[],
      folderIds: [] as string[],
    };

    // 组装参数
    if (currentRecord.value) {
      if (currentRecord.value.folderId) {
        params.fileIds.push(currentRecord.value.id!);
      } else {
        params.folderIds.push(currentRecord.value.id!);
      }
    } else {
      checkedTableRows.value.forEach((row: any) => {
        if (row.folderId) {
          params.fileIds.push(row.id);
        } else {
          params.folderIds.push(row.id);
        }
      });
    }
    return params;
  }

  async function handleBindOk() {
    if (!checkedStandard.value) {
      Message.error(t('standard-named.please-select'));
      return;
    }
    const params = handleParams();
    btnLoading.value = true;
    let flag = false;
    if (params.fileIds.length && params.folderIds.length) {
      const [folderRes, fileRes]: any = await Promise.all([
        bindStandardByMultiFolder(params),
        bindStandardByMultiFiles(params),
      ]);
      flag = folderRes.status && fileRes.status;
    } else if (params.fileIds.length) {
      const res = await bindStandardByMultiFiles(params);
      flag = !!res.status;
    } else if (params.folderIds.length) {
      const res = await bindStandardByMultiFolder(params);
      flag = !!res.status;
    }

    if (flag) {
      Message.success(t('standard-named.bind-success-result'));
    }
    folderTable.value?.refreshTableData();
    btnLoading.value = false;
    bindModalvisible.value = false;
    checkedStandard.value = '';
    // currentRecord.value = null;
  }

  function handleBindEvents(record: any) {
    currentRecord.value = record;
    bindModalvisible.value = true;
  }

  function handleCancelModal() {
    bindModalvisible.value = false;
    checkedStandard.value = '';
    currentRecord.value = null;
  }

  async function handleBindAndCheck() {
    await handleBindOk();
    await batchCheckFile(currentRecord.value);
    currentRecord.value = null;
  }

  const multiCheckIconColor = computed(() => {
    return batchController.value ? '#94BFFF' : '#165DFF';
  });
</script>

<style scoped lang="less">
  .project-file-panel {
    border: 1px solid #d9d9d9;
    height: 100%;
    width: 100%;
    // margin-top: 20px;
    border-radius: 8px;
    display: flex;
    .file-tree-wrap {
      height: 100%;
      width: 20.5%;
      border-right: 1px solid #d9d9d9;
    }
    .content-panel {
      //border: 1px solid red;
      flex: 1;
      position: relative;
      overflow: hidden;
      .content-header {
        padding: 0 20px;
        .header-buttons {
          height: 64px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          overflow: hidden;

          .header-title {
            width: 100%;
            flex: 1;
            display: flex;
            overflow: hidden;
            img {
              width: 20px;
              height: 20px;
              margin-right: 8px;
              margin-top: 7px;
            }
            .text {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              font-size: 18px;
              font-weight: bold;
              line-height: 32px;
              margin-right: 16px;
              color: #1d2129;
            }
          }
        }
      }

      .breadcrumb {
        margin-left: 20px;
      }
      .folder-table-wrap {
        height: calc(100% - 47px);
        width: 100%;
        overflow: hidden;
      }
    }
  }

  :deep(.multi-check-icon path) {
    fill: v-bind(multiCheckIconColor); /* 直接强制覆盖路径颜色 */
  }
</style>
