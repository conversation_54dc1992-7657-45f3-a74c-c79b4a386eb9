<template>
  <div class="msg-list">
    <a-checkbox-group v-model="selectedMsg" @change="selectChange">
      <template v-for="item in renderList" :key="item">
        <a-checkbox :value="item.receiveLogId" class="checkbox-item">
          <template #checkbox="{ checked }">
            <a-space
              align="start"
              class="custom-checkbox-card"
              :class="{ 'custom-checkbox-card-checked': checked }"
            >
              <div class="custom-checkbox-card-mask">
                <div class="custom-checkbox-card-mask-dot" />
              </div>
              <div class="msg-info">
                <div class="info-title">
                  <a-tag
                    :color="
                      item.msgTemplateParam.formKey
                        ? msgConfig[item.msgTemplateParam.formKey].color
                        : 'gray'
                    "
                    >{{
                      item.msgTemplateParam?.formKey
                        ? $t(msgConfig[item.msgTemplateParam.formKey].name)
                        : $t('messageBox.other-notification')
                    }}</a-tag
                  >
                  <span style="float: right">{{ item.createDate }}</span>
                </div>
                <div
                  class="msg-text"
                  @click="
                    to(item.msgTemplateParam, item.receiveLogId, item.status)
                  "
                >
                  {{ item.msgBody || item.msgTemplateParam.comment || '' }}
                </div>
                <div class="footer">
                  <span
                    >{{ $t('messageBox.owning-project') }}
                    {{ item.msgTemplateParam.projectName }}</span
                  >
                </div>
                <div class="footer">
                  <span
                    >{{ $t('messageBox.sender') }}
                    {{ item.fromUserFullname }}</span
                  >
                  <icon-delete
                    style="float: right; color: red"
                    @click="delMsg(item.receiveLogId)"
                  />
                </div>
              </div>
            </a-space>
          </template>
        </a-checkbox>
      </template>
    </a-checkbox-group>
    <a-modal v-model:visible="visible" @ok="handleOk" @cancel="handleCancel">
      <template #title> {{ $t('messageBox.prompt') }} </template>
      <div>{{ $t('messageBox.modal-message') }}</div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { PropType, ref, watch } from 'vue';
  import { FormKeys } from '@/directionary/process';
  import { useRouter, useRoute } from 'vue-router';
  import { useGlobalModeStore } from '@/store';
  import { msgConfig } from './api';
  import { queryissuesList } from '@/views/model-view/components/issue-list/api';
  import * as toolsFunction from '@/views/model-view/components/toolsBar/toolsFunction';
  import useModelToolsStore from '@/store/modules/model-viewer/index';

  const toolStore = useModelToolsStore();
  const route = useRoute();
  const props = defineProps({
    renderList: {
      type: Array as PropType<any>,
      default() {
        return [];
      },
    },
    defaultSelectKeys: {
      type: Array,
      default() {
        return [];
      },
    },
  });

  const modeStore = useGlobalModeStore();
  const emit = defineEmits(['change', 'delete', 'close', 'readMessage']);
  const router = useRouter();

  const selectedMsg = ref<any>([]);
  const selectChange = (val: any) => {
    emit('change', selectedMsg.value);
  };

  const delMsg = (id: any) => {
    if (window.event) {
      window.event?.stopPropagation();
    }
    emit('delete', id);
  };

  const toEngine = async (msgTemplateParam: any) => {
    const { projectId, issueId } = msgTemplateParam;
    let issueFileList: any;
    const params = {
      pageNo: 1,
      pageSize: 99999,
      projectId,
    };
    const { data } = await queryissuesList(params);
    data.list.forEach((item) => {
      if (item.id === issueId) {
        issueFileList = item;
      }
    });
    const path = `/project/${projectId}/issues`;
    const item = {
      issueItem: JSON.stringify(issueFileList),
    };
    router.push({
      path,
      query: item,
    });
    emit('close');
    // const url = router.resolve({
    //   path: '/model-view',
    //   query: {
    //     idStr: issueFileList[0].fileId,
    //     issueId,
    //     projectId: route.params.projectId,
    //   },
    // });
    // window.open(url.href);
    // toolStore.issueListToolActive = false;
    // toolsFunction.openIssueList();
  };
  const visible = ref(false);
  const newProjectObj = ref<any>({});
  const handleOk = () => {
    const { msgTemplateParam, id, status } = newProjectObj.value;
    const { projectId, formKey, folderId } = msgTemplateParam;
    if (projectId && formKey) {
      let path = '';
      let query = {};
      switch (formKey) {
        case FormKeys.reviewNotify:
        case FormKeys.review:
          modeStore.changeGlobalMode('file');
          path = `/project/${projectId}/check`;
          break;
        case FormKeys.deliveryNotify:
        case FormKeys.delivery:
          modeStore.changeGlobalMode('design');
          localStorage.setItem('TaskDefaultTabKey', '2');
          path = `/project/${projectId}/task`;
          break;
        case FormKeys.collaborateNotify:
        case FormKeys.collaborate:
          modeStore.changeGlobalMode('design');
          localStorage.setItem('TaskDefaultTabKey', '1');
          path = `/project/${projectId}/task`;
          break;
        case FormKeys.issueNotify:
        case FormKeys.issue:
          toEngine(msgTemplateParam);
          break;
        case FormKeys.enclosure:
          modeStore.changeGlobalMode('file');
          modeStore.setAttachId(msgTemplateParam.attachId);
          path = `/project/${projectId}/enclosure`;
          break;
        case FormKeys.collaborateRemind:
          modeStore.changeGlobalMode('file');
          path = `/project/${projectId}/file`;
          query = {
            folderId: folderId || '',
          };
          break;
        case FormKeys.meetingNews:
          modeStore.changeGlobalMode('design');
          path = `/project/${projectId}/conference`;
          query = {
            folderId: folderId || '',
            meetingId: msgTemplateParam.meetId,
          };
          break;
        default:
          break;
      }
      if (path) {
        router.push({
          path,
          query,
        });
        emit('close');
      }
    }
    if (id && status === -1) {
      emit('readMessage', id);
    }
  };
  const handleCancel = () => {
    visible.value = false;
  };
  const isNewProject = (newProjectId: string) => {
    const { projectId } = route.params;
    // 项目名不一样需要弹框，一样直接进入
    if (projectId !== newProjectId) {
      visible.value = true;
    } else {
      handleOk();
    }
  };
  const to = (msgTemplateParam: any, id: string, status: number) => {
    newProjectObj.value = { msgTemplateParam, id, status };
    isNewProject(msgTemplateParam.projectId);
  };
  watch(
    () => props.defaultSelectKeys,
    (val: any) => {
      if (val) {
        selectedMsg.value = [...val];
      }
    }
  );
</script>

<style scoped lang="less">
  .msg-list {
    position: relative;
    height: calc(100vh - 195px);
    //border: 1px solid black;
    overflow-y: hidden;
    overflow-x: hidden;
    &:hover {
      overflow-y: scroll;
    }
    .footer-btns {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 60px;
      width: 400px;
      border: 1px solid red;
      background: white;
    }
  }
  .checkbox-item {
    margin-top: 24px;
    &:first-child {
      margin-top: 0;
    }
  }
  .custom-checkbox-card {
    //padding: 10px 16px;
    //border: 1px solid var(--color-border-2);
    border-radius: 4px;
    width: 380px;
    box-sizing: border-box;
    //border: 1px solid red;
  }

  .custom-checkbox-card-mask {
    height: 14px;
    width: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    border: 1px solid var(--color-border-2);
    box-sizing: border-box;
    margin-top: 2px;
  }

  .custom-checkbox-card-mask-dot {
    width: 8px;
    height: 8px;
    border-radius: 2px;
  }

  .custom-checkbox-card-title {
    color: var(--color-text-1);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .custom-checkbox-card:hover,
  .custom-checkbox-card-checked,
  .custom-checkbox-card:hover .custom-checkbox-card-mask,
  .custom-checkbox-card-checked .custom-checkbox-card-mask {
    border-color: rgb(var(--primary-6));
  }

  .custom-checkbox-card-checked {
    .custom-checkbox-card-mask-dot {
      background-color: rgb(var(--primary-6));
    }
  }
  .msg-info {
    border-bottom: 1px solid #e5e6eb;
    padding-bottom: 8px;
    width: 340px;
    margin-left: 8px;
    .info-title {
      font-size: 12px;
      //font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #4e5969;
      line-height: 22px;
    }
    .msg-text {
      //border: 1px solid red;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 这里是超出几行省略 */
      font-size: 14px;
      //font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: var(--color-text-1);
      line-height: 24px;
      margin-top: 5px;
      &:hover {
        color: rgb(var(--arcoblue-5));
      }
    }
    .footer {
      span {
        font-size: 12px;
        //font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #4e5969;
        line-height: 22px;
      }
    }
  }
</style>
