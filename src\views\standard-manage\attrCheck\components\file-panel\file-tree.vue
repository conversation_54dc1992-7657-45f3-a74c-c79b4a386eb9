<template>
  <div class="file-tree">
    <div class="tree-content">
      <a-tree
        ref="folderTree"
        block-node
        default-expand-all
        :default-expanded-keys="expandedKeys"
        v-model:selected-keys="selectedKeys"
        :data="folderData"
        :field-names="{
          key: 'id',
          title: 'name',
        }"
        size="large"
        @select="nodeClick"
        @expand="nodeExpand"
      >
        <template #switcher-icon="node">
          <IconDown
            v-if="
              !('children' in node) ||
              !node.children ||
              node.children?.length > 0
            "
          />
        </template>
        <template #icon="node">
          <a-tooltip
            :content="
              node.node.standardName && node.node.deliveryStandardName
                ? `${$t('file-manage.naming-standard')}：${
                    node.node.standardName
                  }\n${$t('file-manage.delivery-criteria')}：${
                    node.node.deliveryStandardName
                  }`
                : node.node.standardName && !node.node.deliveryStandardName
                ? `${$t('file-manage.naming-standard')}：${
                    node.node.standardName
                  }`
                : `${$t('file-manage.delivery-criteria')}：${
                    node.node.deliveryStandardName
                  }`
            "
            background-color="#165dff"
            style="white-space: pre-line"
          >
            <icon-check-circle-fill
              v-if="node.node.deliveryStandardId || node.node.standardId"
              style="
                position: absolute;
                top: 15px;
                left: 9px;
                color: #00d88c;
                font-size: 12px;
              "
            />
          </a-tooltip>

          <file-image
            :file-name="node.node.name"
            :is-sysFile="isSysFolder(node.node.sysType)"
            :is-file="!!node.node.folderId"
          />
        </template>

        <template #title="nodeData">
          <div
            :style="{
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
            }"
          >
            {{ i18FolderName(nodeData) }}
          </div>
        </template>
        <template #extra="nodeData">
          <div
            class="extra"
            v-if="
              !isTopFolder(nodeData.id) &&
              nodeData.id !== 'add' &&
              selectedKeys.includes(nodeData.id)
            "
          >
          </div>
        </template>
      </a-tree>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, defineProps, defineEmits, PropType } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { FolderMessage } from '@/api/tree-folder';
  import useFileStore from '@/store/modules/file/index';
  import { storeToRefs } from 'pinia';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import { isTopFolder, isSysFolder } from '@/views/projectSpace/file/utils';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';

  const { t } = useI18n();
  const fileStore = useFileStore();
  const props = defineProps({
    folderData: {
      type: Array as PropType<FolderMessage[]>,
      default() {
        return [];
      },
    },
  });
  const emits = defineEmits([
    'nodeClick',
    'refresh',
    'handleUpload',
    'handleShare',
    'handleDownload',
  ]);

  const folderTree = ref<any>();
  const { i18FolderName } = useI18nHandleName();

  const {
    selectedKeys,
    expandedKeys,
    standradTreeData: allFolderData,
  } = storeToRefs(fileStore);

  const expendNodes = (nodeKeys: any) => {
    if (nodeKeys.length) {
      nodeKeys.forEach((key: string) => {
        folderTree.value?.expandNode(key);
      });
    }
  };

  // 点击树节点事件
  const nodeClick = async (
    selectedNodes: (string | number)[],
    nodeData: any
  ) => {
    const nodeInfo = nodeData.node;
    fileStore.setSelectedKeys([nodeInfo.id as string]);

    if (nodeInfo.isAdd || nodeInfo.isEdit) {
      return;
    }

    emits(
      'nodeClick',
      {
        nodes: selectedNodes,
        nodeInfo,
      },
      () => expendNodes(selectedNodes)
    );
  };

  function setNodeSelected(nodeData: FolderMessage) {
    if (nodeData.type !== 'WIP') {
      nodeClick(['WIP'], { node: allFolderData.value[0] });
    } else {
      nodeClick([nodeData.id as string], { node: nodeData });
    }
  }

  // 节点展开或关闭
  const nodeExpand = (expandKeys: string[]) => {
    fileStore.setExpandedKeys(expandKeys);
  };

  defineExpose({ setNodeSelected });
</script>

<style scoped lang="less">
  :deep(.arco-tree-node-title:hover) {
    background-color: #e8f2ff;
  }
  :deep(.arco-tree-node-selected) {
    background-color: #e8f2ff;
  }
  :deep(.arco-tree-node:hover) {
    background-color: #e8f2ff;
  }
  .file-tree {
    width: 100%;
    height: 100%;
    .tree-content {
      //border: 1px solid red;
      height: calc(100% - 20px);
      margin-left: 16px;
      margin-right: 6px;
      padding-top: 20px;
      overflow: auto;
    }

    ::-webkit-scrollbar {
      width: 6px;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #d9d9d9; /* 滚动条滑块颜色 */
    }
  }
  .extra {
    width: 30px;
  }
</style>
