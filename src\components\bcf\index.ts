/* eslint-disable no-underscore-dangle */
import X2js from 'x2js';
import { usePrjPermissionStore } from '@/store';
import viewpoint from './bcf2-1/viewpoint.bcfv';
import bcfVersion from './bcf2-1/bcf.version';
import project from './bcf2-1/project.bcfp';
import markup from './bcf2-1/markup.bcf';
// import { Message } from '@arco-design/web-vue';
import { downloadImage } from './api';
import { Guid } from 'guid-typescript';
import JSZip from 'jszip';

interface IParams {
  bcfName: string;
  topicDatas: any;
  project: string;
  version: string;
}

const projectStore = usePrjPermissionStore();

const x2js = new X2js();

const viewpointJS: any = x2js.xml2js(viewpoint);
const projectJS: any = x2js.xml2js(project);
const markupJS: any = x2js.xml2js(markup);

const GenerateTopicXML = (issue: any) => {
  const viewpointId = Guid.create().value;
  const topicId = Guid.create().value;
  const dimensionString = JSON.parse(issue.issueFileItems[0].dimensionString);
  // if (!dimensionString) {
  //   Message.error('存在问题未截图，此问题将无法生成视图，但不影响数据导出。');
  //   return;
  // }
  const viewpointData = dimensionString?.viewPoint || [];
  const AssignedTo = issue.issueRecipientList
    .map((item: any) => {
      return item.userName;
    })
    .join(',');

  markupJS.Markup.Topic._Guid = topicId;
  markupJS.Markup.Topic.Title = issue.title;
  markupJS.Markup.Topic.Description = issue.message;
  markupJS.Markup.Topic.CreationDate = new Date(issue.createDate).toISOString();
  markupJS.Markup.Topic.CreationAuthor = issue.creater;
  markupJS.Markup.Topic.ModifiedDate = new Date(issue.updateDate).toISOString();
  markupJS.Markup.Topic.ModifiedAuthor = issue.updateBy;
  markupJS.Markup.Topic.AssignedTo = AssignedTo;
  markupJS.Markup.Viewpoints._Guid = viewpointId;

  viewpointJS.VisualizationInfo._Guid = viewpointId;

  if (projectStore.modelEngine === 'BimBase') {
    viewpointJS.VisualizationInfo.PerspectiveCamera.CameraViewPoint.X =
      viewpointData.position?.x || '';
    viewpointJS.VisualizationInfo.PerspectiveCamera.CameraViewPoint.Y =
      viewpointData.position?.y || '';
    viewpointJS.VisualizationInfo.PerspectiveCamera.CameraViewPoint.Z =
      viewpointData.position?.z || '';
    viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.X =
      viewpointData.target?.x || '';
    viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.Y =
      viewpointData.target?.y || '';
    viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.Z =
      viewpointData.target?.z || '';
    viewpointJS.VisualizationInfo.PerspectiveCamera.CameraUpVector.X =
      viewpointData.up?.x || '';
    viewpointJS.VisualizationInfo.PerspectiveCamera.CameraUpVector.Y =
      viewpointData.up?.y || '';
    viewpointJS.VisualizationInfo.PerspectiveCamera.CameraUpVector.Z =
      viewpointData.up?.z || '';
  } else {
    [
      viewpointJS.VisualizationInfo.PerspectiveCamera.CameraViewPoint.X,
      viewpointJS.VisualizationInfo.PerspectiveCamera.CameraViewPoint.Y,
      viewpointJS.VisualizationInfo.PerspectiveCamera.CameraViewPoint.Z,
      viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.X,
      viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.Y,
      viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.Z,
      viewpointJS.VisualizationInfo.PerspectiveCamera.CameraUpVector.X,
      viewpointJS.VisualizationInfo.PerspectiveCamera.CameraUpVector.Y,
      viewpointJS.VisualizationInfo.PerspectiveCamera.CameraUpVector.Z,
    ] = viewpointData;
  }

  viewpointJS.VisualizationInfo.PerspectiveCamera.FieldOfView = 60;

  const xA = viewpointJS.VisualizationInfo.PerspectiveCamera.CameraViewPoint.X;
  const yA = viewpointJS.VisualizationInfo.PerspectiveCamera.CameraViewPoint.Y;
  const zA = viewpointJS.VisualizationInfo.PerspectiveCamera.CameraViewPoint.Z;
  const xB = viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.X;
  const yB = viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.Y;
  const zB = viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.Z;

  const tempX = xB - xA;
  const tempY = yB - yA;
  const tempZ = zB - zA;

  const tempLength = Math.sqrt(tempX * tempX + tempY * tempY + tempZ * tempZ);

  const unitVectorX = tempX / tempLength;
  const unitVectorY = tempY / tempLength;
  const unitVectorZ = tempZ / tempLength;

  viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.X =
    unitVectorX;
  viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.Y =
    unitVectorY;
  viewpointJS.VisualizationInfo.PerspectiveCamera.CameraDirection.Z =
    unitVectorZ;

  const markupXML = x2js.js2xml(markupJS);
  const viewpointXML = x2js.js2xml(viewpointJS);

  return {
    fileName: topicId,
    markUp: `<?xml version="1.0" encoding="UTF-8"?> ${markupXML}`,
    viewPoint: `<?xml version="1.0" encoding="UTF-8"?> ${viewpointXML}`,
    token: issue.issueFileItems[0].picToken,
  };
};

async function createZipFile(bcfFile: IParams) {
  const bcfzip = new JSZip();
  bcfzip.file('project.bcfp', bcfFile.project);
  bcfzip.file('bcf.version', bcfFile.version);
  // eslint-disable-next-line no-restricted-syntax
  for (const topic of bcfFile.topicDatas) {
    const folder = bcfzip.folder(topic.fileName);
    // eslint-disable-next-line no-await-in-loop
    const image = await downloadImage(topic.token);
    folder.file('markup.bcf', topic.markUp);
    folder.file('snapshot.png', image.data);
    folder.file('viewpoint.bcfv', topic.viewPoint);
  }

  // bcfFile.topicDatas.forEach(async (topic: any) => {});

  bcfzip.generateAsync({ type: 'blob' }).then((blob) => {
    const url = URL.createObjectURL(blob);
    const downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = `${bcfFile.bcfName}.bcfzip`;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
  });
}

export const GenerateIssueXML = async (issueDatas: any) => {
  const bcfName = Guid.create().value;
  const projectXML = x2js.js2xml(projectJS);
  projectJS.ProjectExtension.Project._ProjectId = issueDatas[0].projectId;

  const topicDatas = issueDatas.map((issue: any) => {
    return GenerateTopicXML(issue);
  });

  const params = {
    bcfName,
    topicDatas,
    project: `<?xml version="1.0" encoding="UTF-8"?> ${projectXML}`,
    version: bcfVersion,
  };
  createZipFile(params);
};

export default GenerateIssueXML;
