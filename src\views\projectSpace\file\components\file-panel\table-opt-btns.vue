<template>
  <a-dropdown
    v-model:popup-visible="dropVisible"
    :hide-on-select="false"
    position="br"
    @select="handleSelect($event, rowData)"
  >
    <a-button type="text">
      <template #icon>
        <icon-more-vertical
          style="transform: rotate(90deg)"
          v-permission="
            isCommunalChildren(rowData) && userName === rowData.createBy
              ? ''
              : `${rowData.teamId}_${$btn.file.editFile}`
          "
        /> </template
    ></a-button>
    <!-- 表格分享 -->
    <template #content>
      <a-doption
        v-if="
          !rowData.abandon &&
          !isSysFolder(rowData.sysType) &&
          rowData.type === 'WIP'
        "
        v-permission="`${rowData.teamId}_${$btn.file.shared}`"
        value="share"
      >
        <template #icon>
          <icon-share-alt />
        </template>
        <template #default>{{ $t('file-manage.share') }}</template>
      </a-doption>
      <!-- wps移动 -->
      <a-doption
        v-if="rowData.type === 'WIP' && rowData.parentId !== 0 && ![2, 3, 4].includes(rowData.sysType!)"
        v-permission="`${rowData.teamId}_${$btn.file.move}`"
        value="move"
      >
        <template #icon>
          <icon-share-internal />
        </template>
        <template #default>{{ $t('file-manage.move') }}</template>
      </a-doption>
      <!-- 文件编辑 -->
      <a-doption
        v-if="
          rowData.type === 'WIP' &&
          rowData.parentId !== 0 &&
          !isConsumed(rowData) &&
          rowData.abandon !== 1 &&
          isWpsFile(rowData)
        "
        v-permission="`${rowData.teamId}_${$btn.file.editFile}`"
        value="fileEdit"
      >
        <template #icon>
          <icon-edit />
        </template>
        <template #default>{{ $t('file-manage.edit') }}</template>
      </a-doption>
      <a-popconfirm
        :content="
          rowData.abandon === 1
            ? $t('file-manage.cancel-and-invalidate-the-file')
            : $t('file-manage.confirm-nullify-file') + '？'
        "
        type="info"
        position="left"
        @ok="handleSelect('nullify', rowData)"
      >
        <a-doption
          v-if="
            !isCommunalChildren(rowData) || 
            (currentFolder?.type !== 'WIP' && 
            !!currentFolder!.parentId ) ||
            (currentFolder?.type !== 'WIP' && !!currentFolder!.parentId)
            "
          v-permission="`${rowData.teamId}_${$btn.file.abandonFile}`"
          value="nullify"
        >
          <template #icon
            ><icon-close-circle v-if="rowData.type !== 'WIP'" />
          </template>
          <template #default>
            <div v-if="rowData.type !== 'WIP'">
              {{
                rowData.abandon === 1
                  ? $t('file-manage.cancellations')
                  : $t('file-manage.nullify')
              }}
            </div>
          </template>
        </a-doption>
        <!-- 文件作废-左 -->
      </a-popconfirm>
      <a-popconfirm
        :content="
          $t('file-manage.confirm-delete-file') +
          (rowData.folderId
            ? $t('file-manage.file')
            : $t('file-manage.folder')) +
          '？'
        "
        type="info"
        @ok="handleDelete(rowData)"
      >
        <a-doption
          v-if="
            (isCommunalChildren(rowData) && userName === rowData.createBy) ||
            (rowData.type === 'WIP' &&
              rowData.folderId &&
              rowData.sysType !== 1) ||
            (!rowData.folderId && !isSysFolder(rowData.sysType))
          "
          v-permission="
            isCommunalChildren(rowData) && userName === rowData.createBy
              ? ''
              : `${rowData.teamId}_${$btn.file.delete}`
          "
          value="delete-folder"
        >
          <template #icon>
            <icon-delete />
          </template>
          <template #default>{{ $t('file-manage.delete') }}</template>
        </a-doption>
      </a-popconfirm>
    </template>
  </a-dropdown>
</template>

<script lang="ts" setup>
  import { PropType, Ref, computed, inject, ref } from 'vue';
  import { FileAndFolderMessage } from '@/api/tree-folder';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import usFileStore from '@/store/modules/file/index';

  import { storeToRefs } from 'pinia';
  import {
    filterSysTreeData,
    isCommunalChildren,
    isSysFolder,
    isWpsFile,
    transformData,
  } from '@/views/projectSpace/file/utils';
  import { fileNullify, getFileChildrenAll } from '../../api';
  import { Notification } from '@arco-design/web-vue';
  import { wpsViewHandle } from '@/hooks/wps';
  import { useUserStore } from '@/store';

  const fileStore = usFileStore();
  const { currentFolder } = storeToRefs(fileStore);
  const dropVisible = ref(false);

  const userStore = useUserStore();

  const { username: userName } = storeToRefs(userStore);

  defineProps({
    rowData: {
      type: Object as PropType<FileAndFolderMessage>,
      required: true,
    },
  });

  const emits = defineEmits(['eventHandle']);

  function handleMove(row: FileAndFolderMessage) {
    const fileIds: string[] = [];
    const folderIds: string[] = [];
    if (row.folderId) {
      fileIds.push(row.id as string);
    } else {
      folderIds.push(row.id as string);
    }
    fileStore.setMoveIds(fileIds, folderIds);
    emits('eventHandle', 'move', row);
    dropVisible.value = false;
  }

  function handleDelete(row: FileAndFolderMessage) {
    emits('eventHandle', 'delete', row);
    dropVisible.value = false;
  }

  const isConsumed = (record: any) => {
    return (
      record.path &&
      record.path.split('/').length === 4 &&
      record.name === 'Consumed'
    );
  };

  const shareModalData = inject<Ref<Record<string, any>>>('shareModalData');

  const shareWithTable = async (record: any) => {
    let shareData: any = {};

    if (!record.folderId) {
      const res: any = await getFileChildrenAll(record.id);
      const treeData = transformData(res.data).shareLinkDtoList[0];
      treeData.shareLinkDtoList = filterSysTreeData(treeData.shareLinkDtoList);
      shareData = treeData;
    } else {
      shareData = {
        fileId: record.id,
        fileName: record.name,
        fileToken: record.fileToken,
        fileType: 1,
        size: record.size,
        version: record.version,
        description: record.description || '',
      };
    }
    shareModalData!.value = {
      show: true,
      shareData,
      shareType: 'rightBtn',
    };
  };

  function nullify(row: any) {
    let contentText = ''; // 需要国际化
    if (row.abandon === 1) {
      contentText = '取消作废成功';
    } else {
      contentText = '作废成功';
    }
    fileNullify([row.id]).then((res: any) => {
      if (res.code === 8000000) {
        Notification.success({
          id: 'nullify',
          title: 'Success',
          content: contentText,
        });
        emits('eventHandle', 'nullify', row);
      }
    });
    dropVisible.value = false;
  }

  // dropdown事件委托
  const handleSelect = (event: any, row: FileAndFolderMessage) => {
    switch (event) {
      case 'move':
        handleMove(row);
        break;
      case 'delete':
        break;
      case 'share':
        shareWithTable(row);
        break;
      case 'nullify':
        nullify(row);
        break;
      case 'fileEdit':
        wpsViewHandle(row, 'edit', 'admin');
        break;
      default:
    }
  };
</script>
