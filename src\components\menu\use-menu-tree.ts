import { computed } from 'vue';
import { RouteRecordRaw, RouteRecordNormalized } from 'vue-router';
import { useAppStore, useGlobalModeStore } from '@/store';
import appClientMenus from '@/router/app-menus';
import { cloneDeep } from 'lodash';

export default function useMenuTree() {

  const globalModeStore = useGlobalModeStore();
  const globalMode = computed(() => {
    let mode = globalModeStore.getGlobalMode;
    if (!mode) {
      mode = 'work';
    }
    return mode;
  });

  const appStore = useAppStore();
  const appRoute = computed(() => {
    const menus: any = [];

    if (appStore.menuFromServer) {
      return appStore.appAsyncMenus;
    }
    appClientMenus.forEach((menu)=>{
      const modes: string[] = menu.meta?.globalMode || [];
      if (modes.length) {
        if (modes.includes(globalMode.value)) {
          menus.push(menu);
        }
      } else {
        menus.push(menu);
      }
    });
    return menus;
  });


  const menuTree = computed(() => {
    const copyRouter = cloneDeep(appRoute.value) as RouteRecordNormalized[];
    copyRouter.sort((a: RouteRecordNormalized, b: RouteRecordNormalized) => {
      return (a.meta.order || 0) - (b.meta.order || 0);
    });
    function travel(_routes: RouteRecordRaw[]) {
      const result:any = [];
      if(_routes.length){
        _routes.forEach((element)=>{
          if(!element.meta?.hideInMenu){
            if (element.meta?.hideChildrenInMenu || !element.children) {
              element.children = [];
            }
            element.children = element?.children.length ? travel(element.children) : [];
            result.push(element)
          }
        })
      }
      return result;
    }
    return travel(copyRouter);
  });

  return {
    menuTree,
  };
}
