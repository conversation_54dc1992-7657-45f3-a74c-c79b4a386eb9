import { standardManage } from './types';
import { defineStore } from 'pinia';
import cloneDeep from 'lodash/cloneDeep';
import { getDictionary } from '@/api/standard';
import { standardList } from '@/views/standard-manage/standard-list/standard/api';
import { useUserStore } from '@/store';
import { getLocalstorage, setLocalstorage } from '@/utils/localstorage';
import { getUserId } from '@/utils/auth';

const useStandardManageStore = defineStore('standard-manage', {
  state: (): standardManage => {
    return {
      treeDataList: [], // 标准树数据
      tableData: [], // 分类编码标准表格数据
      selTreeData: {}, // 当前选中节点树数据
      professionalList: [], // 专业数据
      standPrecision: [], // 标准绑定的精度
      precisionData: [
        {
          code: 1,
          name: '应包括信息',
        },
        {
          code: 2,
          name: '宜包括信息',
        },
        {
          code: 3,
          name: '可包括信息',
        },
        {
          code: 0,
          name: '无要求',
        },
      ], // 精度数据
      unit: [], // 单位数据
      dataType: [], // 数据类型
      infoCategory: [], // 信息类型
      nowStandard: getLocalstorage('nowStandard') || [], // 当前选择的标准
      nowStandardId: getLocalstorage('nowStandardId') || '', // 当前选择的标准id
      standardListData: [], // 标准下拉数据
      treeSelectList: [], // 当前选择的标准对应的新增编辑时需要的父节点数据
      treeSelectedKey: '', // 当前选择的标准对应的新增编辑时需要的父节点数据
      resultDetail: [], // 校验结果数据
      precisionResData: [], // 校验结果数据
      treeLoading: false,
    };
  },
  actions: {
    // 字典-设置专业数据
    setProfessionalList(value: any) {
      const data = value[0].children;
      data.forEach((item: any) => {
        const firstKey = Object.keys(item.content.contentMap)[0];
        item.name = item.content.contentMap[firstKey];
        item.code = item.content.code;
      });
      this.professionalList = data;
    },

    // 字典-获取精度选择项（l1,l2）
    setPrecisionResData(value: any) {
      const data = value[0].children;
      data.forEach((item: any) => {
        const firstKey = Object.keys(item.content.contentMap)[0];
        item.name = item.content.contentMap[firstKey];
        item.code = item.content.code;
      });
      this.precisionResData = data;
    },
    // 字典-获取精度选择项（l1,l2）
    setStandPrecision() {
      if (this.nowStandard[0]?.accuracy) {
        const codes = this.nowStandard[0]?.accuracy.split(',') || [];
        this.standPrecision = this.precisionResData.filter((item: any) =>
          codes.includes(item.code)
        );
      } else {
        this.standPrecision = [];
      }
    },

    // 字典-设置单位
    setUnit(value: any) {
      const data = value[0].children;
      data.forEach((item: any) => {
        const firstKey = Object.keys(item.content.contentMap)[0];
        item.name = item.content.contentMap[firstKey];
        item.code = item.content.code;
        item.children.forEach((val: any) => {
          const firstKeyV = Object.keys(val.content.contentMap)[0];
          val.name = val.content.contentMap[firstKeyV];
          val.code = item.content.code;
        });
        item.disabled = true; // 设置第一层数据不可点击
      });
      this.unit = data;
    },

    // 字典-设置数据类型
    setDataType(value: any) {
      const data = value[0].children;
      data.forEach((item: any) => {
        const firstKey = Object.keys(item.content.contentMap)[0];
        item.name = item.content.contentMap[firstKey];
        item.code = item.content.code;
      });
      this.dataType = data;
    },

    // 字典-设置信息类型数据
    setinfoCategory(value: any) {
      const data = value[0].children;
      data.forEach((item: any) => {
        const firstKey = Object.keys(item.content.contentMap)[0];
        item.name = item.content.contentMap[firstKey];
        item.code = item.content.code;
      });
      this.infoCategory = data;
    },

    // 获取字典数据统一方法
    async setDictionaryData(code: any) {
      const { data } = await getDictionary({
        code,
        langCode: 'zh-Cn',
      });
      switch (code) {
        case 'stdField':
          this.setProfessionalList(data); // 专业
          break;
        case 'stdPrecision':
          this.setPrecisionResData(data); // 精度
          break;
        case 'stdUnit':
          this.setUnit(data); // 单位
          break;
        case 'stdValueType':
          this.setDataType(data); // 类型
          break;
        case 'stdType':
          this.setinfoCategory(data); // 信息类型
          break;
        default:
          break;
      }
    },

    // 设置标准树形数据
    setTreeData(value: any) {
      this.treeDataList = value;
    },
    // 设置树加载状态
    setTreeLoading(value: any) {
      this.treeLoading = value;
    },

    // 设置分类新增编辑时父节点树数据
    setTreeSelectList(value: boolean) {
      this.treeSelectList = value;
    },

    // 当前选择树节点数据
    setSelTreeData(value: any) {
      this.selTreeData = value;
      // this.treeSelectedKey = value.id;
    },

    // 分类和编码标准树数据
    setTableData(value: any, parantName?: any) {
      if (value) {
        const newData: any = cloneDeep(value);
        this.tableData = newData.map((item: any) => {
          return {
            ...item,
            children: null,
            aliasName: parantName,
          };
        });
      }
    },

    // 分类设置表格数据
    setTableDataNew(value: any) {
      this.tableData = value;
    },

    // 标准列下拉数据
    async setStandardList() {
      const userStore = useUserStore();
      const userId = getUserId() || '';
      const projectId = getLocalstorage(`last_project_${userId}`) || '';

      const params: any = {
        pageNo: 1,
        pageSize: 20,
        standardCode: '',
        standardName: '',
        groupId: userStore.admin === 0 ? '0' : projectId,
      };
      const { data } = await standardList(params);
      if (data?.list?.length) {
        this.standardListData = data.list?.filter(
          (item: { standardType: number }) => item.standardType === 1
        ); // 标准下拉数据
        if (!this.nowStandardId) this.setNowStand(data?.list[0].id);
        this.setStandPrecision();
      }
    },

    // 设置当前选中的标准
    setTreeSelectedKey(value: string) {
      this.treeSelectedKey = value;
    },
    // 设置标准校验结果详情数据
    setResultDetail(value: any) {
      this.resultDetail = value;
    },

    // 设置当前选择的标准
    async setNowStand(id?: any) {
      if (id) {
        const nowStandard = this.standardListData.filter(
          (item: any) => item.id === id
        );
        this.nowStandard = nowStandard;
        this.nowStandardId = nowStandard[0].id;
        setLocalstorage('nowStandard', nowStandard);
        setLocalstorage('nowStandardId', JSON.stringify(nowStandard[0].id));
      } else {
        this.nowStandard = null;
        this.nowStandardId = '';
        setLocalstorage('nowStandard', JSON.stringify(null));
        setLocalstorage('nowStandardId', JSON.stringify(''));
      }
    },

    findNodeById(idNew: string) {
      let result = {};
      const findNode = (nodes: any, id: string) => {
        for (let i = 0; i < nodes.length; i++) {
          if (nodes[i].id === id) {
            result = nodes[i];
            break;
          }

          if (nodes[i].childList?.length) {
            findNode(nodes[i].childList, id);
          }
        }
        return {};
      };

      findNode(this.treeDataList, idNew);
      return result;
    },

    // 通过当前当前树节点id 查询当前节点的所有数据
    searchNowNode(id: any) {
      this.selTreeData = this.findNodeById(id);
    },
  },
});

export default useStandardManageStore;
