<template>
  <div class="nav">
    <div v-for="(item, index) in navList" :key="item.id" class="nav-name">
      <span class="nav-item-name" @click="clickNav(item.id)">{{
        item.name
      }}</span>
      <span v-if="index !== navList.length - 1"> /&nbsp;&nbsp;</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    navInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  const navList = ref([]);
  watch(
    () => props.navInfo,
    // eslint-disable-next-line no-return-assign
    (newValue, oldVlue) => {
      navList.value = [];
      navList.value.push(newValue);
    }
  );

  const emits = defineEmits(['clickNav']);
  const addNav = (file) => {
    // navList.value.name = `${navList.value.name}/${file.name}`;
    navList.value.push(file);
  };
  const clickNav = (folderId: string) => {
    console.log(folderId, navList, 'folderId, navList');
    navList.value.some((item: any, index) => {
      if (item.id === folderId) {
        navList.value.splice(index + 1, navList.value.length);
        return true;
      }
      return false;
    });
    emits('clickNav', navList.value[navList.value.length - 1]);
  };
  defineExpose({
    props,
    addNav,
  });
</script>

<script lang="ts">
  // 只需在这再加个name即可
  export default {
    name: 'FolderNav', // 给组件命名
  };
</script>

<style scoped lang="less">
  .nav {
    width: 100%;
    display: flex;
    margin-bottom: 10px;
    .nav-item-name {
      color: #3491fa;
      cursor: pointer;
    }
  }
</style>
