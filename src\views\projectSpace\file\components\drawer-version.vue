<template>
  <a-drawer width="320px" :visible="show" @cancel="handleCancel">
    <template #title>{{ $t('file-manage.version-history') }}</template>
    <template #footer>
      <a-button
        v-if="isCompareVisible"
        type="primary"
        @click="versionCompare"
        >{{ $t('file-manage.version-compare') }}</a-button
      >
      <a-button v-else type="primary" @click="handleCancel">{{
        $t('file-manage.cancel')
      }}</a-button>
    </template>
    <a-spin
      :loading="versionLoading"
      style="height: calc(100% - 8px); width: 100%"
    >
      <version-card
        v-for="(item, index) in fileVersionData"
        :key="index"
        :currentFile="file"
        style="margin-top: 4px"
        :version-detail="item"
        :style="'margin-top:' + (index === 0 ? '4px' : '20px')"
      ></version-card>
    </a-spin>
  </a-drawer>
  <compare-version
    v-if="compareDrawer"
    v-model:visible="compareDrawer"
    :file-version-data="fileVersionData"
  ></compare-version>
</template>

<script lang="ts" setup>
  import useFileStore from '@/store/modules/file/index';
  import VersionCard from './card-version.vue';
  import CompareVersion from './compare-version/index.vue';
  import { computed, PropType, ref, toRefs, watch } from 'vue';
  import { getFileVersionList } from '@/views/projectSpace/file/api';
  import { FileMessage } from '@/api/tree-folder';
  import { xbaseSemanticModel } from '@/utils/BIM-Engine/XBase/format';

  const props = defineProps({
    file: {
      type: Object as PropType<FileMessage>,
      required: true,
    },
    show: {
      type: Boolean,
      default: false,
    },
  });

  const emits = defineEmits(['update:show']);
  const { show: versionDrawer, file: currentFile } = toRefs(props);

  const fileStore = useFileStore();
  const versionLoading = ref(false);
  const fileVersionData = ref<any[]>([]);

  // 版本比对抽屉显示
  const compareDrawer = ref(false);

  const isCompareVisible = computed(() => {
    if (fileVersionData.value.length > 0) {
      const fileName = fileVersionData.value[0]?.name;
      const extension = fileName?.split('.').at(-1)?.toLowerCase();
      return xbaseSemanticModel.includes(extension);
    }
    return false;
  });

  // 打开版本对比抽屉
  const versionCompare = () => {
    compareDrawer.value = true;
  };

  async function getFileVersionData() {
    fileVersionData.value = [];
    versionLoading.value = true;
    const { data } = await getFileVersionList(currentFile.value?.id);
    const { list } = data;
    list.sort(
      (a, b) =>
        parseInt(b.version as string, 10) - parseInt(a.version as string, 10)
    );
    fileVersionData.value = list;
    versionLoading.value = false;
  }

  watch(
    () => versionDrawer.value,
    () => {
      getFileVersionData();
    }
  );

  const handleCancel = () => {
    emits('update:show', false);
  };
</script>
