import axios from 'axios';

// 获取分类数据属性数据
export function getGroupList(data: string) {
  return axios.post<any>(
    `/asset-system/bimStandard/queryAttributeByClassId`,
    data
  );
}

// 添加属性
export function saveAttribute(data: string) {
  return axios.post<any>(`/asset-system/bimStandard/createAttribute`, data);
}

// 更新属性
export function editAttribute(data: string) {
  return axios.post<any>(`/asset-system/bimStandard/updateAttribute`, data);
}

// 删除属性
export function delAttribute(data: any) {
  return axios.post<any>(
    `/asset-system/bimStandard/batchDeleteAttribute`,
    data
  );
}

// 获取字典数据
export function getDictionary(params: any) {
  return axios.get<any>('/sys-system/dictionary/list', {
    params,
  });
}
