export default {
  'standard-setting.rule-name': 'Rule Name',
  'standard-setting.rule-name-required': 'Rule Name Required',
  'standard-setting.please-enter': 'Please Enter',
  'standard-setting.regular-expression': 'Regular Expression',
  'standard-setting.expression-required': 'Expression Required',
  'standard-setting.instructions': 'Instructions',
  'standard-setting.editing-rules': 'Editing Rules',

  'standard-setting.search': 'Search',
  'standard-setting.clear': 'Clear',
  'standard-setting.attribute-list': 'Attribute List',
  'standard-setting.rule-list': 'Rule List',
  'standard-setting.whether-delete-checked-rule':
    'Confirm whether to delete the checked rule?',
  'standard-setting.delete': 'Delete',
  'standard-setting.new-rule': 'New Rule',
  'standard-setting.whether-delete-rule': 'Confirm whether to delete the rule?',
  'standard-setting.name': 'Name',
  'standard-setting.creation-time': 'Creation Time',
  'standard-setting.operation': 'Operation',
  'standard-setting.add-rule': 'Add Rule',
  'standard-setting.rules-details': 'Rules Details',
  'standard-setting.unselected-list': 'Unselected List',
  'standard-setting.description': 'Description',
  'standard-setting.ok': 'OK',
  'standard-setting.field-setting': 'Field Setting',
  'standard-setting.please-enter-field': 'Please Enter The Field',
  'standard-setting.please-enter-field-value':
    'Please Enter The Value Of The Field',
  'standard-setting.maximum-4-levels': 'Maximum 4 levels',

  'standard-setting.new': 'New',
  'standard-setting.edit': 'Edit',
  'standard-setting.view': 'View',
  'standard-setting.dimension': 'Dimension',
  'standard-setting.new-dimension': 'New Dimension',
  'standard-setting.whether-delete-dimension':
    'Confirm whether to delete the dimension?',
  'standard-setting.dimension-name': 'Dimension Name',
  'standard-setting.type': 'Type',
  'standard-setting.please-select': 'Please Select',
  'standard-setting.date': 'Date',
  'standard-setting.text-field': 'Text Field',
  'standard-setting.dropdown-list': 'DropDown List',
  'standard-setting.system-attribute': 'System Attribute',
  'standard-setting.property-value-setting': 'Property Value Setting',
  'standard-setting.rule-validation': 'Rule Validation',
  'standard-setting.add-to-naming-standard': 'Added To Naming Standard',
  'standard-setting.naming-standards-set': 'Naming Standards Set',
  'standard-setting.new-attribute': 'New Attribute',
  'standard-setting.attribute-name': 'Attribute Name',
  'standard-setting.whether-delete-attribute':
    'Confirm whether to delete the attribute?',
  'standard-setting.index': 'Index',

  'standard-setting.cancel': 'Cancel',
  'standard-setting.save': 'Save',
  'standard-setting.business-attributes': 'Business Attributes',
  'standard-setting.standard-chinese-name': 'Standard Chinese Name',
  'standard-setting.standard-english-name': 'Standard English Name',
  'standard-setting.standard-number': 'Standard Number',
  'standard-setting.select-existing-criteria': 'Select Existing Criteria',
  'standard-setting.select-delimiter': 'Select Delimiter',
  'standard-setting.add-standard': 'Add Standard',
  'standard-setting.edit-standard': 'Edit Standard',
  'standard-setting.standard-name': 'Standard Name',
  'standard-setting.standard-list': 'Standard List',
  'standard-setting.whether-delete-checked-standard':
    'Confirm whether to delete the checked standard?',
  'standard-setting.whether-delete-standard':
    'Confirm whether to delete the standard?',

  'standard-setting.update-time': 'Update Time',
  'standard-setting.select-action-folder': 'Select Action Folder',
  'standard-setting.team': 'Team',
  'standard-setting.original-standard': 'Original Standard',
  'standard-setting.will-be-overwritten-by': 'Will Be Overwritten By',
  'standard-setting.add-attribute': 'Add Attribute',
  'standard-setting.go-to': 'Go To',
  'standard-setting.properties-tab': 'Properties TAB',
  'standard-setting.to-create-properties': 'To Create Properties',
  'standard-setting.add': 'Add',
  'standard-setting.select-existing-property-to-add-naming convention.':
    'Select an existing property to add as a field to the naming convention.',
  'standard-setting.attribute': 'Attribute',
  'standard-setting.please-select-attribute': 'Please Select Attribute',
  'standard-setting.yes': 'Yes',
  'standard-setting.not': 'Not',
  'standard-setting.back': 'Back',

  'standard-setting.standard-information': 'Standard Information',
  'standard-setting.standards-set': 'Standards Set',
  'standard-setting.delimiter': 'Delimiter',
  'standard-setting.meta-attribute-setting': 'Meta Attribute Setting',
  'standard-setting.coding-rule-management': 'Coding Rule Management',

  'standard-setting.property-details': 'Property Details',
  'standard-setting.attribute-display-type': 'Attribute Display Type',
  'standard-setting.folder-enforcement': 'Folder Enforcement',
  'standard-setting.staging-area': 'Staging Area',
  'standard-setting.enable-staging-areas-for-files-that-do not-meet-requirements':
    'Enable staging areas for files that do not meet requirements',
  'standard-setting.select-folder': 'Select Folder',
  'standard-setting.bound-standard': 'Bound Standard',
  'standard-setting.success': 'Success',
  'standard-setting.code': 'Code',

  'standard-setting.standard-type': 'Standard Type',
  'standard-setting.naming-standard': 'Naming Standard',
  'standard-setting.delivery-standard': 'Delivery Standard',
  'standard-setting.upload-standard': 'Upload Standard',

  'standard-setting.standard-properties': 'Standard Properties',
  'standard-setting.generating-rules': 'Generating Rules',
  'standard-setting.working-with-folders': 'Working With Folders',
  'standard-setting.text': 'Text',
  'standard-setting.stats': 'Stats',
  'standard-setting.enumeration': 'Enumeration',
  'standard-setting.class-identifier': 'Class Identifier',
  'standard-setting.attribute-types': 'Attribute Types',
  'standard-setting.unit': 'Unit',
  'standard-setting.remark': 'Remark',
  'standard-setting.semantic-entity-mapping': 'Semantic Entity Mapping',
  'standard-setting.required': 'Required',
  'standard-setting.check-range': 'Check Range',
  'standard-setting.attribute-groups': 'Attribute Groups',
  'standard-setting.select-at-least-one-of':
    'Select at least one of the "Attribute type" and "unit"',
  'standard-setting.class-identifier1': 'Class Identifier',
  'standard-setting.IFC-semantic-entities': 'IFC Semantic Entities',

  'standard-setting.id-card': 'Id Card',
  'standard-setting.phone': 'Phone',
  'standard-setting.email': 'Email',

  'standard-setting.standard-permission': 'Standard Permission',
  'standard-setting.system-standard': 'System Standard',
  'standard-setting.project-standard': 'Project Standard',
  'standard-setting.attribute-standard': 'Attribute Standard',
  'standard-setting.precision-range': 'Precision Range',
  'standard-setting.select-accuracy': 'Please Select Precision',
  'standard-setting.export-report': 'Export Report',
  'standard-setting.model-verification-report': 'Model Verification Report',
};
