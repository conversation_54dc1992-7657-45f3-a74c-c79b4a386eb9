export default {
  'model-viewer.model-detail': '模型详情',
  'model-viewer.close': '关闭',
  'model-viewer.problemList': '问题列表',
  'model-viewer.modelAssembly': '模型装配',
  'model-viewer.model.translation': '模型平移',
  'model-viewer.model.rotate': '模型旋转',
  'model-viewer.model.AxialDirection': '轴向',
  'model-viewer.model.Angle': '角度',
  'model-viewer.model.selectModel': '请选择模型',
  'model-viewer.model.EnterAngle': '请输入正确的角度',
  'model-viewer.attributeCheck': '属性校验',
  'model-viewer.roam': '漫游',
  'model-viewer.share': '共享',
  'model-viewer.mergeInfo': '对比信息',
  'model-viewer.collisionList': '碰撞检查',
  'model-viewer.annotation': '图纸批注',

  'model-viewer.issueReply': '问题回复',
  'model-viewer.problemScreenshot': '问题截图',
  'model-viewer.modifyStatus': '修改状态',
  'model-viewer.replyIssue': '问题回复',
  'model-viewer.enterIssueContent': '请输入问题内容',
  'model-viewer.enterIssueContentStage': '请输入问题阶段',
  'model-viewer.enterIssueContentType': '请输入问题类型',
  'model-viewer.confirm': '确定',
  'model-viewer.unresolved': '未解决',
  'model-viewer.resolved': '已解决',
  'model-viewer.inProgress': '处理中',

  'model-viewer.issueTitle': '问题标题',
  'model-viewer.issueDescription': '问题描述',
  'model-viewer.creator': '创建人',
  'model-viewer.issueStatus': '问题状态',
  'model-viewer.createTime': '创建时间',
  'model-viewer.screenshot': '缩略图',
  'model-viewer.uploadImg': '上传图片',
  'model-viewer.published-status': '发布状态',
  'model-viewer.issueStage': '问题阶段',
  'model-viewer.type': '问题类型',
  'model-viewer.recipient': '收件人',
  'model-viewer.operationLogs': '问题回复',
  'model-viewer.view': '查看视图',
  'model-viewer.reply': '回复',
  'model-viewer.details': '详情',
  'model-viewer.return': '返回',
  'model-viewer.missingViews': '缺少视图',

  'model-viewer.meetingIssueListTitle': '会议问题列表',
  'model-viewer.modelIssueListTitle': '模型问题列表',
  'model-viewer.issueFilterTitle': '问题标题',
  'model-viewer.selectAll': '全部',
  'model-viewer.createUser': '创建人',
  'model-viewer.allUsers': '全部',
  'model-viewer.searchPlaceholder': '请选择',
  'model-viewer.createIssue': '创建问题',
  'model-viewer.issueXMLDownload': 'BCF下载',

  'model-viewer.issueAnnotation': '问题批注',
  'model-viewer.issueScreenshot': '问题截图',
  'model-viewer.photoTooltip': '截图时自动保存视口，问题列表点击问题可快速定位',
  'model-viewer.title': '标题',
  'model-viewer.titleRuleMessage': '请输入标题',
  'model-viewer.stage': '问题阶段',
  'model-viewer.stageRuleMessage': '请选择阶段',
  'model-viewer.shareBefore': '共享前',
  'model-viewer.deliveryBefore': '交付前',
  'model-viewer.delivery': '交付',
  'model-viewer.receivers': '收件人',
  'model-viewer.receiverRuleMessage': '请选择收件人',
  'model-viewer.typeRuleMessage': '请选择类型',
  'model-viewer.collisionDetection': '碰撞检测',
  'model-viewer.violateStandard': '违反规范',
  'model-viewer.ownerStandard': '业主标准',
  'model-viewer.description': '描述',
  'model-viewer.descriptionPlaceholder': '请输入',

  'model-viewer.compareListTitle': '版本比对',
  'model-viewer.compareInfo.add': '新增',
  'model-viewer.compareInfo.delete': '删除',
  'model-viewer.compareInfo.update': '更新',
  'model-viewer.errorResult': '错误（不符合：“公共环境数据标准”）',
  'model-viewer.greenResult': '正确',
  'model-viewer.showDiffOnly': '仅展示差异属性',
  'model-viewer.componentName': '构件名称',
  'model-viewer.componentType': '构件类型',
  'model-viewer.componentID': '构件ID',
  'model-viewer.propertyName': '属性名',
  'model-viewer.basePropertyValue': '属性值（基准版本）',
  'model-viewer.newPropertyValue': '属性值（对比版本）',

  'model-viewer.partStructure': '部分结构',
  'model-viewer.exportFile': '导出',
  'model-viewer.all': '全部',
  'model-viewer.hardCollision': '硬碰撞',
  'model-viewer.gapCollision': '间隙碰撞',
  'model-viewer.objectA': '对象A',
  'model-viewer.objectB': '对象B',
  'model-viewer.checkModel': '检查模型',
  'model-viewer.collisionTolerance': '碰撞公差',

  'model-viewer.collisionID': '碰撞ID',
  'model-viewer.model': '模型',
  'model-viewer.collisionObject1': '碰撞对象1',
  'model-viewer.collisionObject2': '碰撞对象2',
  'model-viewer.collisionType': '碰撞类型',

  'model-viewer.annotationTitle': '创建标签批注',
  'model-viewer.annotationTooltip':
    '输入内容后点击图元即可自动创建批注，点击标签图标可删除标签，双击可收起文本签',
  'model-viewer.annotationPlaceholder': '请输入批注内容',
  'model-viewer.content': '内容',
  'model-viewer.confirmPositionMessage': '请先在视图区域点击确定一个标注位置',

  'model-viewer.quantities': '工程量统计',
  'model-viewer.upload-quantities': '上传工程量统计',
  'model-viewer.enter-query': '输入任意关键字查询',
  'model-viewer.search': '查询',
  'model-viewer.clear': '清空',
  'model-viewer.quantities-name': '清单名称（细目名称）',
  'model-viewer.quantities-number': '清单编号（细目号）',
  'model-viewer.quantities-unit': '单位',
  'model-viewer.quantities-code': '系统编码（WBS编码）',
  'model-viewer.quantities-amount': '0#清单数量',
  'model-viewer.no-wbs': '未找到该WBS编码的构件',
  'model-viewer.upload-tip': '请上传正确的xls格式',
  'model-viewer.cannot-select-self': '不可以选择自己',

  // 视角
  'model-viewer.viewMange': '视图管理',
  'model-viewer.addViewMange': '添加视点',
  'model-viewer.setStartViewMange': '设置初始视点',
  'model-viewer.PleaseEnterName': '请输入视点名称',
  'model-viewer.cancel': '取消',
  'model-viewer.confirmDeletion': '确认删除该视点',
  'model-viewer.name': '名称',
  'model-viewer.action': '操作',
  'model-viewer.createdSuccess': '创建成功',
  'model-viewer.editSuccess': '编辑成功',
  'model-viewer.startSetSuccess': '初始视点设置成功',
  'model-viewer.deleteSuccess': '删除成功',

  // 标注
  'model-viewer.markerTitle': '三维标记',
  'model-viewer.addMarker': '添加标记',
  'model-viewer.markerName': '添加标记',
  'model-viewer.xOffset': 'x偏移',
  'model-viewer.yOffset': 'Y偏移',
  'model-viewer.markerType': '标记类型',
  'model-viewer.url': 'URL',
  'model-viewer.show': '是否显示',
  'model-viewer.interaction': '图标点击交互',
  'model-viewer.windowShow': '网页新窗口显示',
  'model-viewer.save': '保存',
  'model-viewer.time': '时间',
  'model-viewer.webJump': '网页跳转',

  // 导航管理
  'model-viewer.navigation-management': '导航管理',
  'model-viewer.add': '新增',
  'model-viewer.edit': '编辑',
  'model-viewer.bind': '绑定',
  'model-viewer.delete': '删除',
  'model-viewer.component-bind': '绑定构件',
  'model-viewer.bind-success': '绑定成功',
  'model-viewer.Please-select-component': '请选择构件',
  'model-viewer.confirm-delete-node': '确认删除该节点吗？',
  'model-viewer.WBS': 'wbs',
  'model-viewer.runway': '施工流水段',
  'model-viewer.profession': '专业',
  'model-viewer.space': '空间',
  'model-viewer.floor': '楼层',
  'model-viewer.custom': '自定义',
  'model-viewer.structure-tree': '结构树',
  'model-viewer.noComponentData': '当前节点下没有构件数据',
  'model-viewer.do-not-repeat': '该构件已经添加过',
  'model-viewer.2D-3D-links': '二三维联动',
  'model-viewer.2D-viewer': '二维图纸',

  'model-viewer.created-a-problem': '创建了问题',
  'model-viewer.answered-the-question': '回复了问题',
  'model-collaboration.cancel': '取消',
  'model-collaboration.click-add-pin': '单击以添加问题图钉',
  'model-collaboration.complete': '完成',
  'model-collaboration.screenshot-result': '截图结果',
};
