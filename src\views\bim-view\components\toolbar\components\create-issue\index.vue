<template>
  <a-card
    v-draggable="{ handle: 'strong', bounds: false }"
    class="issue-card"
    :style="`right:${route.name === 'bimView' ? '400px' : '0'}`"
    hoverable
  >
    <template #extra>
      <a-button type="text" @click="close()">{{
        $t('model-viewer.close')
      }}</a-button>
    </template>
    <template #title>
      <strong class="title"> {{ $t('model-viewer.issueAnnotation') }}</strong>
    </template>
    <a-form ref="issueFormRef" :model="issueForm" auto-label-width>
      <a-form-item
        field="photo"
        :label="$t('model-viewer.screenshot')"
        :tooltip="$t('model-viewer.photoTooltip')"
      >
        <!-- <a-upload
          ref="uploadRef"
          v-model:file-list="issueForm.fileList"
          action="/api/sys-storage/image/upload"
          :headers="{
            'Fusion-Auth': getToken() || '',
            'Fusion-Biz': setFusionBiz() || '',
          }"
          :limit="1"
          accept="image/png, image/jpeg,image/jpg"
          image-preview
          list-type="picture-card"
          :show-link="true"
          :on-button-click="handleButtonClick"
        /> -->
        <a-image
          v-if="screenshotData"
          :src="screenshotData"
          :alt="$t('model-collaboration.screenshot-result')"
          width="88px"
          height="88px"
          style="border: 2px dashed #ccc"
        />
      </a-form-item>
      <a-form-item
        field="title"
        :label="$t('model-viewer.issueTitle')"
        :rules="[
          {
            required: true,
            message: $t('model-viewer.titleRuleMessage'),
          },
        ]"
      >
        <a-input
          v-model="issueForm.title"
          :placeholder="$t('model-viewer.enterIssueContent')"
        />
      </a-form-item>
      <a-form-item
        field="stage"
        :label="$t('model-viewer.issueStage')"
        :rules="[
          {
            required: true,
            message: $t('model-viewer.stageRuleMessage'),
          },
        ]"
      >
        <a-select
          v-model="issueForm.stage"
          :placeholder="$t('model-viewer.enterIssueContentStage')"
        >
          <a-option :value="1">{{ $t('model-viewer.shareBefore') }}</a-option>
          <a-option :value="2">{{ $t('model-viewer.share') }}</a-option>
          <a-option :value="3">{{
            $t('model-viewer.deliveryBefore')
          }}</a-option>
          <a-option :value="4">{{ $t('model-viewer.delivery') }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="receivers"
        :label="$t('model-viewer.recipient')"
        :rules="[
          {
            required: true,
            message: $t('model-viewer.receiverRuleMessage'),
          },
        ]"
      >
        <UserSelector
          v-model="issueForm.receivers"
          :placeholder="$t('issues.please-enter-name-role-company-or-email')"
          :project-id="(route.query.projectId as string)"
          @change="receiversSelect"
        />
      </a-form-item>
      <a-form-item
        field="type"
        :label="$t('model-viewer.type')"
        :rules="[
          {
            required: true,
            message: $t('model-viewer.typeRuleMessage'),
          },
        ]"
      >
        <a-select
          v-model="issueForm.type"
          :placeholder="$t('model-viewer.enterIssueContentType')"
        >
          <a-option :value="1">{{
            $t('model-viewer.collisionDetection')
          }}</a-option>
          <a-option :value="2">{{
            $t('model-viewer.violateStandard')
          }}</a-option>
          <a-option :value="3">{{ $t('model-viewer.ownerStandard') }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="message" :label="$t('model-viewer.description')">
        <a-textarea
          v-model="issueForm.message"
          :placeholder="$t('model-viewer.descriptionPlaceholder')"
          :max-length="500"
          show-word-limit
          :auto-size="{
            minRows: 2,
          }"
        />
      </a-form-item>
      <a-button type="primary" @click="issueSubmit">{{
        $t('model-viewer.confirm')
      }}</a-button>
    </a-form>
  </a-card>
  <!--截图组件-->
  <!-- <screen-short
    v-if="screenshotStatus"
    @destroy-component="destroyComponent"
    @get-image-data="completeCallback"
  ></screen-short> -->
</template>

<script setup lang="ts">
  // import ScreenShort from 'js-web-screen-shot';
  import { setFusionBiz } from '@/api/interceptor';
  import { getToken } from '@/utils/auth';
  import { Message } from '@arco-design/web-vue';
  import { ref, reactive, nextTick, onMounted, watch } from 'vue';
  import UserSelector from '@/components/user-selector/index.vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { useRoute } from 'vue-router';
  import { addIssue, fileUpload } from './api';
  import { useI18n } from 'vue-i18n';
  import useModelToolsStore from '@/store/modules/model-viewer/index';
  import { storeToRefs } from 'pinia';
  import html2canvas from 'html2canvas';

  const store = useModelToolsStore();
  const { alertShow, alertSubmit } = storeToRefs(store);
  const createIssueShow = ref('hidden'); // 创建问题弹窗显示状态
  const screenshotData = ref<any>(null);

  const { t } = useI18n();
  const route = useRoute();
  // const userStore = useUserStore();
  // const modelToolsStore = useModelToolsStore();
  const emits = defineEmits(['issueListReload', 'init', 'close']);
  interface issueFormInter {
    title: string; // 标题
    receivers: string; // 收件人
    stage: number; // 问题阶段
    fileList: Array<any>;
    type: number; // 类型
    message: string; // 描述
  }
  const issueForm = reactive<issueFormInter>({
    title: '',
    receivers: '',
    stage: 1,
    fileList: [],
    type: 1,
    message: '',
  });
  const props = defineProps<{
    fileId: string;
    viewer: any;
  }>();

  const curViewPoint = ref<string>('');
  const receiversID = ref<string[]>([]);
  const imageLocation = ref<object>({});
  const issueFormRef = ref<FormInstance>();
  const curPicToken = ref<string>('');
  const nodeInfo = ref();
  const elementId = ref('');

  const receiversSelect = (user: any) => {
    if (user.id === localStorage.getItem('userid')) {
      nextTick(() => {
        issueForm.receivers = '';
      });
      Message.info(t('issues.cannot-select-yourself'));
    } else {
      receiversID.value = [user.id];
    }
    // receiversID.value = user.map((item: any) => {
    //   if (userStore.username === item.username) {
    //     Message.info(t('model-viewer.cannot-select-self'));
    //     let receiversArrayData: any = issueForm.receivers.split(',');
    //     receiversArrayData = receiversArrayData.filter((itemId: any) => {
    //       return itemId !== userStore.id;
    //     });
    //     receiversArrayData = receiversArrayData.join();
    //     // 在Vue 3中，nextTick是一个函数，用于延迟执行一段代码，直到浏览器完成当前的事件循环。这是Vue提供的一种机制，用于处理DOM更新，并确保在DOM更新后执行某些操作。
    //     nextTick(() => {
    //       issueForm.receivers = receiversArrayData;
    //     });
    //   }
    //   return item.id;
    // });
  };

  // const screenshotStatus = ref<boolean>(false);
  // 销毁组件函数
  // const destroyComponent = (status: boolean) => {
  //   screenshotStatus.value = status;
  // };

  const completeCallback = async (data: any) => {
    const url = data;
    // const url = data.base64;
    const bytes = window.atob(url.split(',')[1]);
    // const bytes = window.atob(base64data.split(',')[1]);
    const buffer = new ArrayBuffer(bytes.length);
    const uint = new Uint8Array(buffer);
    for (let j = 0; j < bytes.length; j++) {
      uint[j] = bytes.charCodeAt(j);
    }
    const imageFile = new Blob([buffer], { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('file', imageFile, `${Date.now()}.jpeg`);
    const res = await fileUpload(formData);
    if (res.status) {
      console.log(res.data, 'res.data');
      curPicToken.value = res.data.fileToken;
      imageLocation.value = data.cutInfo;
      issueForm.fileList = [];
      issueForm.fileList.push({
        url: `/api/sys-storage/download_image?f8s=${res.data.fileToken}`,
      });
    }
  };
  // const screenShot = () => {
  //   /* eslint-disable no-new */
  //   screenShotHandler.value = new ScreenShort({
  //     enableWebRtc: false, // 是否显示选项框
  //     level: 9999, // 层级级别
  //     completeCallback, // 截图成功完成的回调
  //     // closeCallback,
  //   });
  // };
  // const handleButtonClick = (event: Event): Promise<FileList> | void => {
  //   event.preventDefault();
  //   screenshotStatus.value = true;
  //   // screenShot();
  //   curViewPoint.value = props.viewer?.getViewMessage();
  // };

  const close = () => {
    // props.viewer?.stopListenToAddMarker();
    issueForm.fileList = [];
    issueForm.title = '';
    issueForm.receivers = '';
    issueForm.message = '';
    // props.viewer?.clearMarker();
    emits('close');
    // modelToolsStore.setCreateIssueModal(false);
    createIssueShow.value = 'hidden';
    store.setAlertShow(false);
  };

  const issueSubmit = async () => {
    const res = await issueFormRef.value?.validate();
    if (!res) {
      const params = {
        projectId: route.query.projectId,
        mettingId: route.query.conferenceId,
        title: issueForm.title,
        message: issueForm.message,
        receivers: receiversID.value,
        fileIds: [props.fileId],
        type: issueForm.type,
        stage: issueForm.stage,
        items: [
          {
            dimensionString: JSON.stringify({
              viewPoint: curViewPoint.value,
              nodeInfo: nodeInfo.value,
              imageLocation: imageLocation.value,
              elementId: elementId.value,
            }),
            fileId: props.fileId,
            picToken: curPicToken.value,
          },
        ],
      };
      const saveRes: any = await addIssue(params);
      if (saveRes.status) {
        Message.success(saveRes.message);
        emits('issueListReload');
        close();
      } else {
        Message.error(saveRes.message);
      }
    }
  };

  /**
   * 自动获取截图结果
   */
  const autoCapture = async () => {
    const element = document.getElementById('Xbase-viewer');
    if (!element) return;
    const canvas = await html2canvas(element, {
      ignoreElements: (node) => {
        // 条件1：忽略所有带有 class="ignore-screenshot" 的元素
        if (node.classList?.contains('cube-container')) {
          return true;
        }
        if (node.classList?.contains('dxy-toolbar-group')) {
          return true;
        }
        // 忽略复杂装饰性元素
        if (node.classList?.contains('heavy-shadow')) return true;
        if (node.tagName === 'IFRAME') return true; // 避免加载 iframe 内容
        return false;
      },
      scrollX: 0,
      scrollY: 0,
      useCORS: true, // 允许跨域
      logging: false, // 关闭日志（可选）
      scale: 0.5, // 默认 1，降低到 0.8 可提速 30%~50%
    });
    screenshotData.value = canvas.toDataURL('image/png');
    curViewPoint.value = props.viewer?.getViewMessage();
    completeCallback(screenshotData.value);
  };

  /**
   * 监听添加创建问题打点标记
   */
  const initAddMarker = () => {
    props.viewer?.listenToAddMarker(async (data: any) => {});

    props.viewer?.entitySelectedEvent((data: object) => {
      nodeInfo.value = data;
      elementId.value = `${data.selectionIds[0]}`;
      console.log('选中的实体', data);
      console.log('[ elementId.value ] >', elementId.value);
    });
  };

  watch(
    () => alertShow.value,
    (val) => {
      if (val) {
        initAddMarker();
      } else {
        try {
          console.log('[ alert关闭了 ] >');
          props.viewer?.stopListenToAddMarker();
        } catch (error) {}
      }
    }
  );

  watch(
    () => alertSubmit.value,
    (val) => {
      if (val) {
        console.log('[ 提交了 ] >');
        autoCapture();
        createIssueShow.value = 'visible';
        store.setAlertSubmit(false);
      }
    }
  );
</script>

<style>
  #toolPanel {
    height: auto !important;
  }
</style>

<style lang="less" scoped>
  .issue-card {
    position: absolute;
    top: 25%;
    right: 0;
    width: 400px;
    height: auto;
    z-index: 10;
    visibility: v-bind(createIssueShow);
  }
  .title {
    display: block;
    width: 100%;
    font-weight: 400;
    cursor: move;
  }
  :deep#v3d-viewer {
    background-color: white;
  }
  :deep.brush-select-panel {
    position: relative;
    top: -10px;
  }
</style>
