const permissionBytns: Permission.Btn = {
  home: {
    createProject: 'home:createProject',
    deleteProject: 'home:deleteProject',
  },
  system: {
    personalInfo: 'system:personalInfo',
    securitySettings: 'system:securitySettings',
    groupMember: 'system:groupMember',
    organization: 'system:organization',
    permissions: 'system:permissions',
    roles: 'system:roles',
    projectTemplate: 'system:projectTemplate',
  },
  project: {
    editProject: 'project:editProject',
    createMilestone: 'project:createMilestone',
    editMilestone: 'project:editMilestone',
    deleteMilestone: 'project:deleteMilestone',
    createMilestoneTask: 'project:createMilestoneTask',
    updateMilestoneTask: 'project:updateMilestoneTask',
    deleteMilestoneTask: 'project:deleteMilestoneTask',
    addProcess: 'project:addProcess',
    editProcess: 'project:editProcess',
    deleteProcess: 'project:deleteProcess',
    sharing: 'projectHome:sharing',
    delivery: 'projectHome:delivery',
  },
  file: {
    upload: 'file:upload',
    download: 'file:download',
    shared: 'file:shared',
    move: 'file:move',
    delete: 'file:delete',
    renameFolder: 'file:renameFolder',
    addFolder: 'file:addFolder',
    editFile: 'file:editFile',
    reconvert: 'file:reconvert',
    abandonFile: 'file:abandonFile',
  },
  team: {
    addMember: 'team:addMember',
    editTeam: 'team:editTeam',
    deleteTeam1: 'team:deleteTeam1',
    deleteTeam2: 'team:deleteTeam2',
    addTeam: 'team:addTeam',
    addSubTeam: 'team:addSubTeam',
    updateMember: 'team:updateMember',
    deleteMember: 'team:deleteMember',
    removeMember: 'team:removeMemberForProject',
    importMember: 'team:importMember',
    exportMember: 'team:exportMember',
    invitateMember: 'team:invitateMember',
    subordination: 'team:subordination',
  },
};
export default permissionBytns;
