import { defineStore } from 'pinia';
import { FileAndFolderMessage } from '@/api/tree-folder';

import { AttachMoveIds, AttachTableView, FileState } from './types';
import {
  defaultTreeData,
  getBreadcrumbs,
  initSessionStorageData,
  setSessionStorageData,
} from '@/store/modules/file/utils';

const useFileStore = defineStore('file', {
  state: (): FileState => {
    return {
      currentFolder: initSessionStorageData('fileCurrentFolder', {}), // session待移除
      currentIdPath: initSessionStorageData('fileTreeIdPath', ''),
      tableData: [],
      checkTableKeys: [],
      checkTableData: [],
      treeData: [],
      selectedKeys: initSessionStorageData('fileTreeSelectedKeys'),
      expandedKeys: initSessionStorageData('fileTreeExpandedKeys'),
      breadcrumbList: [],
      hiddenSlot: 0,
      imgViewModal: { visible: false, title: '', fileToken: '' }, // 图片预览数据
      tableLoading: false,
      newFolderName: '',
      allTreeData: initSessionStorageData('fileAllTreeData', defaultTreeData), // session待移除
      moveIds: { fileIds: [], folderIds: [] },
      downloadList: [],
      selectedTableRowkeys: [],
      isAbandon: false,
      // 事项会议附件
      currentAttach: {} as AttachTableView,
      attachMoveIds: {} as AttachMoveIds,
      attachPathList: [{ name: '附件列表', id: '' }],
    };
  },
  getters: {
    standradTreeData(state) {
      return state.allTreeData.filter(
        (item: FileAndFolderMessage) => item.id === 'WIP'
      );
    },
  },
  actions: {
    setCurrentIdPath(idPath: string) {
      this.currentIdPath = idPath;
      setSessionStorageData('fileTreeIdPath', idPath);
    },
    setCurrentAttach(attach: AttachTableView) {
      this.currentAttach = attach;
    },
    setAttachPathList(pathList: { name: string; id: string }[]) {
      this.attachPathList = pathList;
    },
    setAttachMoveIds(moveParams: AttachMoveIds) {
      this.attachMoveIds = moveParams;
    },
    setSelectedTableRowkeys(rowkeys: string[]) {
      this.selectedTableRowkeys = rowkeys;
    },
    toggleProject() {
      this.currentFolder = {};
      this.breadcrumbList = [];
      this.allTreeData = defaultTreeData;
      this.selectedKeys = [];
      this.expandedKeys = [];
    },
    setCurrentFolder(current: FileAndFolderMessage) {
      setSessionStorageData('fileCurrentFolder', current);
      this.currentFolder = current;
      this.breadcrumbList = getBreadcrumbs(current);
    },
    setBreadcrumbList(current: FileAndFolderMessage) {
      this.breadcrumbList = getBreadcrumbs(current);
    },
    setSelectedKeys(keys: string[]) {
      setSessionStorageData('fileTreeSelectedKeys', keys);
      this.selectedKeys = keys;
    },
    setExpandedKeys(keys: string[]) {
      setSessionStorageData('fileTreeExpandedKeys', keys);
      this.expandedKeys = keys;
    },
    setAllTreeData() {
      setSessionStorageData('fileAllTreeData', this.allTreeData);
      // this.allTreeData = treeData;
    },
    setTableData(arr: FileAndFolderMessage[]) {
      this.tableData = arr;
    },
    setMoveIds(fileIds: string[], folderIds: string[]) {
      this.moveIds.fileIds = fileIds;
      this.moveIds.folderIds = folderIds;
    },
    // 设置图片弹窗开关
    setImgViewModal(visible: boolean, record?: any) {
      this.imgViewModal.visible = visible;
      this.imgViewModal.title = record?.name || '';
      this.imgViewModal.fileToken = record?.fileToken || '';
    },
    setDownloadProgress(processInfo: any) {
      if (processInfo.percent === 100) {
        this.downloadList = this.downloadList.filter(
          (object: { id: any }) => object.id !== processInfo.id
        );
      } else if (
        this.downloadList.find(
          (item: { id: any }) => item.id === processInfo.id
        )
      ) {
        this.downloadList.forEach((item: { id: any; percent: any }) => {
          if (item.id === processInfo.id) {
            item.percent = parseFloat(processInfo.percent.toFixed());
          }
        });
      } else this.downloadList.push(processInfo);
    },
  },
});

export default useFileStore;
