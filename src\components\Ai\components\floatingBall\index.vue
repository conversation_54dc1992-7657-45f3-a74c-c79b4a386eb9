<template>
  <div class="floating-ball" :style="ballStyle" @mousedown="startDrag">
    <a-button type="primary">
      <template #icon>
        <img
          src="../../../../assets/images/ai-icon2.png"
          style="width: 60px; height: 60px"
          alt=""
        />
      </template>
    </a-button>
  </div>
</template>

<script setup>
  import { storeToRefs } from 'pinia';
  import useAiStore from '../../../../store/modules/ai';
  import { ref } from 'vue';

  const aiStore = useAiStore();
  const { aiModuleVisible } = storeToRefs(aiStore);

  const showAi = () => {
    aiModuleVisible.value = !aiModuleVisible.value;
  };

  const isDragging = ref(false); // 标记是否正在拖拽
  const isClick = ref(true); // 标记是否是点击事件

  const handleButtonClick = (e) => {
    if (isDragging.value) return; // 如果正在拖拽，则不触发 showAi
    showAi();
  };

  // 使用 bottom 和 right 来控制位置
  const ballStyle = ref({
    position: 'fixed',
    bottom: '50px', // 初始位置
    right: '50px', // 初始位置
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'move',
    zIndex: 9999,
  });

  let offsetX = 0;
  let offsetY = 0;
  let startTime = 0;

  // 鼠标按下时，记录鼠标位置和悬浮球的偏移量
  const startDrag = (e) => {
    startTime = Date.now(); // 记录按下时间
    e.preventDefault();
    offsetX =
      e.clientX - (window.innerWidth - parseFloat(ballStyle.value.right)); // 计算右偏移量
    offsetY =
      e.clientY - (window.innerHeight - parseFloat(ballStyle.value.bottom)); // 计算下偏移量

    // 设置拖拽标志
    isDragging.value = true;
    isClick.value = true; // 默认认为是点击

    // 添加鼠标移动事件监听
    // eslint-disable-next-line no-use-before-define
    window.addEventListener('mousemove', onDrag);
    // eslint-disable-next-line no-use-before-define
    window.addEventListener('mouseup', stopDrag);
  };

  // 鼠标移动时更新悬浮球的位置，并限制不超出页面边界
  const onDrag = (e) => {
    let newRight = window.innerWidth - (e.clientX - offsetX);
    let newBottom = window.innerHeight - (e.clientY - offsetY);

    // 限制拖拽球的右侧不超出页面
    if (newRight < 0) newRight = 0;
    // 限制拖拽球的底部不超出页面
    if (newBottom < 0) newBottom = 0;
    // 限制拖拽球的左侧不超出页面
    if (newRight + 30 > window.innerWidth) newRight = window.innerWidth - 30;
    // 限制拖拽球的顶部不超出页面
    if (newBottom + 30 > window.innerHeight)
      newBottom = window.innerHeight - 30;

    // 更新球的位置
    ballStyle.value.right = `${newRight}px`;
    ballStyle.value.bottom = `${newBottom}px`;

    // 如果有拖拽的动作，则标记为拖拽
    isClick.value = false; // 只要开始拖拽，就认为不是点击
  };

  // 鼠标松开时停止拖拽，并处理自动贴边逻辑
  const stopDrag = (e) => {
    window.removeEventListener('mousemove', onDrag);
    window.removeEventListener('mouseup', stopDrag);

    // 停止拖拽标志
    isDragging.value = false;

    // 自动吸附到页面边缘
    let newRight = parseFloat(ballStyle.value.right);
    let newBottom = parseFloat(ballStyle.value.bottom);

    // 吸附边缘的距离阈值（可以根据需求调整）
    const edgeThreshold = 50;

    if (newRight < edgeThreshold) {
      newRight = -20; // 吸附到右边
    } else if (newRight > window.innerWidth - 80) {
      newRight = window.innerWidth - 20; // 吸附到左边
    }

    if (newBottom < edgeThreshold) {
      newBottom = -20; // 吸附到下边
    } else if (newBottom > window.innerHeight - 80) {
      newBottom = window.innerHeight - 20; // 吸附到上边
    }

    ballStyle.value.right = `${newRight}px`;
    ballStyle.value.bottom = `${newBottom}px`;

    // 判断是否是点击事件（如果拖动时间小于 200ms，则认为是点击）
    const endTime = Date.now();
    if (endTime - startTime < 200 && isClick.value) {
      handleButtonClick(e); // 如果是点击，触发点击事件
    }
  };
</script>

<style scoped>
  .floating-ball {
    position: fixed;
    z-index: 9999;
  }

  .ball-content {
    font-size: 2rem;
    cursor: pointer;
  }
</style>
