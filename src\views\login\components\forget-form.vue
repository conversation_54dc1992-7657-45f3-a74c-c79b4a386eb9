<template>
  <div class="forget-form">
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" :title="$t('login.form.forgetPassword')">
        <a-form
          ref="forgetRef"
          :model="forgetForm"
          layout="vertical"
          @submit="handleSubmit"
          style="margin-top: 16px"
        >
          <a-form-item
            field="phone"
            :rules="[
              { required: true, message: $t('login.form.telOrEmailRequired') },
              // {
              //   match: /^(\+\d{1,3})?\d{7,13}$/, //正则替换  *匹配大陆港澳台
              //   message: $t('login.form.telInvalid'),
              // },
              {
                match:
                  /^(1\d{10}|[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/,
                message: $t('login.form.telOrEmailInvalid'),
              },
            ]"
            :validate-trigger="['change', 'blur']"
            hide-label
          >
            <a-input
              v-model="forgetForm.phone"
              :placeholder="$t('login.form.telOrEmailPlaceholder')"
              :maxlegth="11"
            >
              <template #prefix>
                <icon-mobile :size="20" />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item
            field="captcha"
            :rules="[
              { required: true, message: $t('login.form.captchaRequired') },
            ]"
            :validate-trigger="['change', 'blur']"
            hide-label
          >
            <a-input
              v-model="forgetForm.captcha"
              :placeholder="$t('login.form.captchaPlaceholder')"
              :maxlegth="50"
            >
              <template #prefix>
                <icon-safe :size="20" />
              </template>
              <template #append>
                <a-button
                  type="text"
                  :loading="smsLoading"
                  @click="getSMSCaptcha"
                >
                  <span v-if="countDown === -2" class="captcha-word">{{
                    $t('login.form.getCaptcha')
                  }}</span>
                  <span v-else-if="countDown === -1" class="captcha-word">{{
                    $t('login.form.regainCaptcha')
                  }}</span>
                  <span v-else class="captcha-word">{{ `${countDown}s` }}</span>
                </a-button>
              </template>
            </a-input>
          </a-form-item>
          <a-form-item
            field="pwd"
            :rules="[
              { required: true, message: $t('login.form.password.errMsg') },
            ]"
            :validate-trigger="['change', 'blur']"
            hide-label
          >
            <a-input-password
              v-model="forgetForm.pwd"
              :placeholder="$t('login.form.newPassword')"
              allow-clear
            >
              <template #prefix>
                <icon-lock :size="20" />
              </template>
            </a-input-password>
            <template #extra>
              <div>{{ $t('login.form.password-validation') }}</div>
            </template>
          </a-form-item>
          <a-form-item
            field="confirmpwd"
            :rules="[
              { required: true, message: $t('login.form.password.errMsg') },
            ]"
            :validate-trigger="['change', 'blur']"
            hide-label
          >
            <a-input-password
              v-model="forgetForm.confirmpwd"
              :placeholder="$t('login.form.confirmPassword')"
              allow-clear
            >
              <template #prefix>
                <icon-lock :size="20" />
              </template>
            </a-input-password>
          </a-form-item>
          <div class="option-box">
            <a-button type="primary" html-type="submit" :loading="loading">{{
              $t('login.form.enterConfirm')
            }}</a-button>
            <a-link @click="changeLogin(LoginMethods.password)">{{
              $t('login.form.backLogin')
            }}</a-link>
          </div>
        </a-form>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { reactive, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import { getSms, editPassword } from '@/api/user';
  import pwdEncrypt from '@/utils/encryption/pwd';
  import LoginMethods from '../constant';
  import checkPassWordFormat from '@/utils/password-validation';

  const { t } = useI18n();

  const forgetForm = reactive({
    phone: '',
    captcha: '',
    pwd: '',
    confirmpwd: '',
  });

  // 处理验证码
  const countDown = ref(-2);
  const smsLoading = ref(false);
  const forgetRef = ref<FormInstance>();
  // 存储验证码
  let captchaKey = '';
  const getSMSCaptcha = async () => {
    if (countDown.value >= 0) {
      Message.warning(t('login.form.captchaHoldOn'));
      return;
    }
    const res = await forgetRef.value?.validateField('phone');
    // 仅判断手机号是否校验通过
    if (res) {
      return;
    }
    smsLoading.value = true;
    try {
      const res = await getSms(forgetForm.phone);
      if (!res.status) {
        Message.error(res.message);
      }
      captchaKey = res.data;
    } catch (err) {
      // you can report use errorHandler or other
      if (typeof err === 'string') {
        Message.error(err);
      }
    } finally {
      smsLoading.value = false;
      updataCountDown();
    }
  };

  const updataCountDown = () => {
    countDown.value = 60;
    const counter = setInterval(() => {
      if (countDown.value === 0) {
        clearInterval(counter);
        countDown.value = -1;
      } else {
        countDown.value--;
      }
    }, 1000);
  };

  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (!errors) {
      if (forgetForm.confirmpwd !== forgetForm.pwd) {
        Message.error(t('login.form.confirmpwd.errMsg'));
        return;
      }
      if (!checkPassWordFormat(forgetForm.pwd)) {
        Message.error(t('login.form.password-rule-error'));
        return;
      }
      try {
        const { captcha, phone, pwd } = values;
        const data = {
          captcha,
          phone,
          key: captchaKey,
          pwd: pwdEncrypt(pwd),
        };
        const res: any = await editPassword(data);
        if(res.status){
          Message.success('密码重置成功！请重新登录');
          setTimeout(()=>{
            changeLogin(LoginMethods.password)
          }, 1000)
        }
      } catch (err) {
        // Message.error((err as Error).message);
      }
    }
  };

  const emit = defineEmits(['changeLogin']);
  const changeLogin = (method: LoginMethods) => {
    emit('changeLogin', method);
  };
</script>

<script lang="ts">
  export default {
    name: 'ForgetForm',
  };
</script>

<style lang="less" scoped>
:deep(.arco-btn-size-large){
  height: 48px;
  border-radius: 8px;
}
:deep(.arco-input-wrapper){
  height: 40px;
  border: 1px solid #C9CDD4;
  background-color: #ffffff;
  border-radius: 8px !important;
}
  .option-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
