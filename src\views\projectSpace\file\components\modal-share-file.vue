<template>
  <a-modal
    v-model:visible="show"
    :title="$t('file-manage.share-file')"
    unmount-on-close
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @before-open="onBeforeOpen"
    @before-close="onBeforeClose"
    @cancel="cancel"
  >
    <a-form :model="form" auto-label-width>
      <a-form-item
        :label="$t('file-manage.share-type')"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-radio-group
          v-model="form.checkType"
          direction="vertical"
          @change="selCheckType"
        >
          <a-radio :value="2">{{ $t('file-manage.need-login') }}</a-radio>
          <a-radio :value="1">{{
            $t('file-manage.need-to-extract-code')
          }}</a-radio>
          <a-radio :value="0">{{
            $t('file-manage.visible-to-anyone')
          }}</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item :label="$t('file-manage.period-of-validity')">
        <a-radio-group v-model="form.expiryDate">
          <a-radio value="24H">{{ $t('file-manage.24h') }}</a-radio>
          <a-radio value="7D">{{ $t('file-manage.7d') }}</a-radio>
          <a-radio value="30D">{{ $t('file-manage.30d') }}</a-radio>
          <a-radio value="permanent">{{ $t('file-manage.permanent') }}</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="cancel">{{ $t('file-manage.cancel') }}</a-button>
      <a-button
        :loading="loadingWithGenerateBtn"
        type="primary"
        @click="generateLink"
        >{{ $t('file-manage.generate-link') }}</a-button
      >
    </template>
  </a-modal>
  <ModalFileLink v-model:show="linkShow" :shareLink="shareLink" />
</template>

<script setup lang="ts">
  import moment from 'moment';
  import { ref, defineProps, PropType, toRefs, computed } from 'vue';
  import useFileStore from '@/store/modules/file/index';
  import { shareFormType } from '@/store/modules/file/types';
  import ModalFileLink from './modal-file-link.vue';

  import { cloneDeep } from 'lodash';
  import { reqCreateShareLink } from '../api';
  import { useI18n } from 'vue-i18n';
  import { useRoute } from 'vue-router';

  const route = useRoute();

  // 使用大菠萝
  const fileStore = useFileStore();
  const props = defineProps({
    show: {
      type: Boolean,
      required: true,
    },
    shareType: {
      type: String as PropType<'tree' | 'table' | 'rightBtn'>,
      required: true,
    },
    shareData: {
      type: Object as PropType<any>,
      required: true,
    },
  });
  const emits = defineEmits(['update:show']);

  const { t } = useI18n();

  // 链接按钮loading~
  const loadingWithGenerateBtn = ref<boolean>(false);

  // 解构数据
  const { show, shareType, shareData } = toRefs(props);

  // 表单字段
  const form = ref<shareFormType>({
    checkType: 2,
    expiryDate: '24H',
    encryptionMode: 'AES',
    secretkey: '',
  });

  // 关闭弹窗
  function cancel(): void {
    emits('update:show', false);
  }

  // 处理数据
  const shareLinkDtoListFn = (input: any) => {
    const transformFolder = (folder: any) => {
      const transformed: any = {
        fileId: folder.id,
        fileName: folder.name,
        fileType: 0, // 数据类型
        size: 0,
        version: 0,
        description: folder.description || '',
      };

      if (folder.children && folder.children.length > 0) {
        transformed.shareLinkDtoList = folder.children.map(transformFolder);
      }

      if (folder.files && folder.files.length > 0) {
        transformed.shareLinkDtoList = (
          transformed.shareLinkDtoList || []
        ).concat(
          folder.files.map((file: any) => ({
            fileId: file.fileId,
            fileName: file.name,
            fileType: 1, // 数据类型
            size: file.size,
            version: file.version,
            fileToken: file.fileToken,
          }))
        );
      }

      return transformed;
    };

    // 格式化模板
    const output = {
      checkType: 0,
      packageType: 1,
      shareLinkDtoList: [transformFolder(input)],
      shareName: '',
      shareType: 0,
    };

    return output;
  };

  const linkShow = ref(false);
  const shareLink = ref({});
  // 生成分享链接
  function generateLink(): void {
    loadingWithGenerateBtn.value = true;
    // 获取当前时间
    const currentTime = moment();

    // 生成链接逻辑
    // 接口参数
    const params: any = cloneDeep(form.value);

    // 过期时间格式化
    if (form.value.expiryDate === '24H') {
      const expirationTime = currentTime
        .add(24, 'hours')
        .format('YYYY-MM-DD HH:mm:ss');
      params.expiryDate = expirationTime;
    } else if (form.value.expiryDate === '7D') {
      const expirationTime = currentTime
        .add(7, 'days')
        .format('YYYY-MM-DD HH:mm:ss');
      params.expiryDate = expirationTime;
    } else if (form.value.expiryDate === '30D') {
      const expirationTime = currentTime
        .add(30, 'days')
        .format('YYYY-MM-DD HH:mm:ss');
      params.expiryDate = expirationTime;
    } else if (form.value.expiryDate === 'permanent') {
      params.expiryDate = '';
    }

    // 打包类型(0-分批打包,1-合并打包) 和 分享类型(0-链接,1-邮件) 先写死
    params.packageType = 1;
    params.shareType = 0;

    params.shareLinkDtoList = [];

    params.projectId = route.params.projectId;

    try {
      // 分享类型（左侧树结构分享、右侧表格进行勾选分享、表格右侧操作按钮）
      if (shareType.value === 'tree') {
        // 数据节点过滤系统文件
        shareData.value.children = shareData.value.children.filter(
          (item: any) => {
            return !item.sysType;
          }
        );

        // 数据结构做处理
        const paramsFormat = shareLinkDtoListFn(shareData.value)
          .shareLinkDtoList[0];

        // 分享名称
        params.shareName = shareData.value.title;
        // 数据拼接
        params.shareLinkDtoList.push(paramsFormat);
      } else if (shareType.value === 'table') {
        // 分享名称  (单文件直接显示名称----多文件加个等)

        if (shareData.value.length > 1) {
          params.shareName = `${shareData.value[0].fileName}等文件`;
        } else if (shareData.value.length === 1) {
          params.shareName = shareData.value[0].fileName;
        }
        params.shareLinkDtoList = [...shareData.value];
      } else if (shareType.value === 'rightBtn') {
        params.shareName = shareData.value.fileName;
        // 数据拼接
        params.shareLinkDtoList.push(shareData.value);
      }
    } catch (e) {
      loadingWithGenerateBtn.value = false;
    }

    reqCreateShareLink(params)
      .then((res: any) => {
        const data = {
          uuid: res.data.uuid,
          extractedCode: res.data.extractedCode || '',
        };
        if (res.status === true) {
          shareLink.value = data;
          // 打开另一个窗口（文件链接窗口）
          linkShow.value = true;
        }
      })
      .catch((error: any) => {
        return Promise.reject(error);
      })
      .finally(() => {
        loadingWithGenerateBtn.value = false;
      });
  }

  // 对话框打开前触发
  const onBeforeOpen = () => {
    const formData = {
      checkType: 2,
      expiryDate: '24H',
    };
    Object.assign(form.value, formData);
  };

  // 对话框关闭前触发
  const onBeforeClose = () => {
    form.value = {
      checkType: 2,
      expiryDate: '24H',
      encryptionMode: 'AES',
      secretkey: '',
    };
  };

  // 随机设置密钥
  const setSecretKey = (length: any) => {
    const charset =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*_+';
    let result: any = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      result += charset[randomIndex];
    }
    form.value.secretkey = result;
  };

  // 选择分享类型
  const selCheckType = (value: any) => {
    if (value === 1) setSecretKey(16);
  };
</script>

<style scoped lang="less">
  .needCode {
    margin: 8px 0;
    padding-left: 28px;
    .arco-form-item {
      margin-bottom: 4px;
    }
  }
  :deep(.needCode .arco-radio) {
    display: inline-block;
    margin-right: 10px;
    span {
      vertical-align: middle;
    }
  }
</style>
