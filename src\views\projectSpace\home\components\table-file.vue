<template>
  <div class="table-file">
    <a-row :gutter="16" style="margin-bottom: 16px">
      <a-col :flex="1">
        <table-title :title="$t('design.file-list')">
          <template #default>
            <div class="file-list-title">
              <span>{{ $t('design.file-list') }}</span>
              <a-breadcrumb>
                <template #separator>
                  <icon-right />
                </template>
                <a-breadcrumb-item
                  v-for="(item, i) in breadcrumbData"
                  :key="i"
                  @click="$emit('folderSkip', item, i)"
                  >{{ i18FolderName(item) }}</a-breadcrumb-item
                >
              </a-breadcrumb>
            </div>
          </template>
        </table-title>
      </a-col>
      <a-col :flex="'460px'" style="text-align: right">
        <a-space :size="8">
          <a-form
            :model="searchForm"
            label-align="left"
            style="margin-right: 16px"
          >
            <a-input-search
              v-model="searchName"
              :placeholder="$t('design.please-enter')"
              class="search-input"
              style="width: 220px"
              @search="handleChange"
              @keydown.enter="handleChange"
            />
          </a-form>
          <a-button
            :key="currentTeamId"
            v-permission="`${currentTeamId}_${$btn.project.sharing}`"
            type="outline"
            class="button-style"
            @click="startShare"
          >
            <template #icon>
              <shareIcon />
            </template>
            <template #default> {{ $t('design.share') }}</template>
          </a-button>
          <a-button
            :key="currentTeamId"
            v-permission="`${currentTeamId}_${$btn.project.delivery}`"
            type="outline"
            class="button-style"
            @click="startDeliver"
          >
            <template #icon>
              <deliveryIcon />
            </template>
            <template #default> {{ $t('design.initiate-delivery') }}</template>
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <div style="flex: 1; overflow: hidden; position: relative">
      <a-table
        stripe
        row-key="id"
        :loading="loading"
        :pagination="{
          showTotal: true,
          showPageSize: true,
          showJumper: true,
          defaultPageSize: 20,
          pageSizeOptions: [20, 50, 100],
        }"
        :columns="columnsView"
        :data="
          FileTableData.filter((item) => item.name.includes(searchForm.name))
        "
        :bordered="false"
        :scroll="{ x: '100%', y: tableHeight }"
        table-layout-fixed
        column-resizable
      >
        <template #empty>
          <div class="empty-wrapper">
            <img src="@/assets/images/schedule/schedule-bg.png" alt="" />
            <div>{{ $t('design.no-data-available') }}</div>
          </div>
        </template>

        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
        <template #name="{ record }">
          <div class="table-name">
            <file-image
              :file-name="record.name"
              :is-file="'folderId' in record"
              style="margin-right: 8px"
            />
            <a-tooltip
              :content="$t('file-manage.system-generation')"
              background-color="#165dff"
            >
              <icon-info-circle-fill
                v-if="record.sysType"
                style="
                  position: absolute;
                  margin-top: 8px;
                  margin-left: 10px;
                  color: grey;
                  font-size: 12px;
                "
              />
            </a-tooltip>
            <div
              v-if="record.version"
              class="text-overflow"
              @click="modelView(record)"
            >
              <a-typography-paragraph
                :ellipsis="{
                  rows: 1,
                  showTooltip: true,
                }"
                style="color: rgb(var(--primary-6))"
              >
                <span>{{ i18FolderName(record) }}</span>
              </a-typography-paragraph>
            </div>
            <div
              v-else
              class="folder-name text-overflow"
              @click="$emit('folderSkip', record)"
              >{{ i18FolderName(record) }}</div
            >
          </div>
        </template>
        <template #standardName="{ record }">
          <a-typography-paragraph
            :ellipsis="{
              rows: 1,
              showTooltip: true,
            }"
          >
            <span>{{ record.standardName }}</span>
          </a-typography-paragraph>
        </template>
        <template #description="{ record }">
          <a-typography-paragraph
            :ellipsis="{
              rows: 1,
              showTooltip: true,
            }"
          >
            <span>{{ record.description }}</span>
          </a-typography-paragraph>
        </template>
        <template #version="{ record }">
          <a-tag v-if="record.version" bordered color="cyan"
            >V{{ record.version }}</a-tag
          >
        </template>
        <template #size="{ record }">
          {{ record.size ? getFileSize(record.size) : '' }}
        </template>

        <template #operation="{ record }">
          <a-tooltip :content="$t('design.download-source-file')">
            <a-button
              v-if="![1, 3, 4].includes(record.sysType)"
              type="text"
              @click="downloadSource(record)"
            >
              <template #icon> <icon-download /> </template>
            </a-button>
          </a-tooltip>
        </template>
      </a-table>
      <div class="column-setting">
        <DynamicColumn />
      </div>
    </div>
    <AddDialog
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :tree-folder-config="treeFolderConfig"
      :initial-data="initialData"
      :team-permission="teamPermission"
      v-bind="$attrs"
      :current-team-id="currentTeamId"
    />
    <!-- 图片预览 -->
    <imgViewer
      v-if="imgViewModal.visible"
      :view-modal="imgViewModal"
    ></imgViewer>
  </div>
</template>

<script lang="ts" setup>
  import { useI18n } from 'vue-i18n';
  import { ref, reactive, PropType, defineExpose, computed, watch } from 'vue';
  import TableTitle from '@/components/table-title/index.vue';
  import { getFileSize } from '@/utils/file';
  import { Message } from '@arco-design/web-vue';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import FileImage from './image-file.vue';
  import { getFileColumns, Column } from './table-column';
  import AddDialog from './add-dialog.vue';
  import { downloadSource } from '../hooks/dropdow-events';
  import modelViewBim from '@/utils/common/view';
  import { useRoute } from 'vue-router';
  import imgViewer from '@/components/imgView/index.vue';
  import shareIcon from '@/assets/images/design/share-line.svg';
  import deliveryIcon from '@/assets/images/design/folder-unknow-line.svg';
  import { last } from 'lodash';
  import imgJson from '@/config/imgType.json';
  import useI18nHandleName from '../../file/hooks/backups';

  import DynamicColumn from '@/components/dynamic-column/index.vue';
  import { useDynamicColumns } from '@/components/dynamic-column/hooks';

  const { t } = useI18n();
  const { i18FolderName } = useI18nHandleName();

  const prjPermissionStore = usePrjPermissionStore();
  const { isPrjAdmin } = prjPermissionStore;

  const imgViewModal = reactive({
    visible: false,
    title: '',
    fileToken: ' ',
  });

  const props = defineProps({
    tableData: {
      type: Array as PropType<any[]>,
      default() {
        return [];
      },
    },
    breadcrumbData: {
      type: Array as PropType<any[]>,
      default() {
        return [];
      },
    },
    currentTeamId: {
      type: String,
      default() {
        return '';
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
    teamList: {
      type: Array as PropType<any[]>,
      default() {
        return [];
      },
    },
    tableHeight: {
      type: String,
      default() {
        return '';
      },
    },
  });

  defineEmits(['folderSkip']);

  const route = useRoute();

  const { columnsView } = useDynamicColumns({
    columns: getFileColumns(),
  });

  const searchForm = ref({
    name: '',
  });
  const searchName = ref('');
  const handleChange = () => {
    searchForm.value.name = searchName.value;
  };

  const teamPermission = computed(() => {
    return (
      props.teamList.find((item) => item.id === props.currentTeamId)?.role || 0
    );
  });

  const treeFolderConfig = reactive({
    type: 1,
    module: ['wip'],
    teamIds: [props.currentTeamId] as any[] | undefined,
    teamId: props.currentTeamId,
  });
  const dialogVisible = ref(false);
  const dialogTitle = ref(t('design.share'));
  const initialData = ref({});
  const startShare = (initData = {}) => {
    if (!('id' in initData) && teamPermission.value < 3) {
      Message.warning(t('design.no-permission'));
      return;
    }
    dialogTitle.value = t('design.share');
    treeFolderConfig.module = ['wip'];
    treeFolderConfig.teamId = props.currentTeamId;
    treeFolderConfig.teamIds = [props.currentTeamId];
    dialogVisible.value = true;
    initialData.value = initData;
  };

  const startDeliver = (initData = {}) => {
    if (!('id' in initData) && teamPermission.value < 3) {
      Message.warning(t('design.no-permission'));
      return;
    }
    dialogTitle.value = t('design.delivery');
    treeFolderConfig.module = ['shared'];
    treeFolderConfig.teamId = props.currentTeamId;
    treeFolderConfig.teamIds = undefined;
    dialogVisible.value = true;
    initialData.value = initData;
  };

  const modelView = async (record: any) => {
    const type = last(record.name.split('.')) as string;
    const isImgType: boolean = imgJson.includes(type);
    // 图片预览
    if (isImgType) {
      imgViewModal.visible = true;
      imgViewModal.title = record.name || '';
      imgViewModal.fileToken = record.fileToken || '';
    } else {
      modelViewBim(record, route.params.projectId as string);
    }
  };

  defineExpose({ startShare, startDeliver });

  const FileTableData = ref<any[]>([]);

  watch(
    () => props.tableData,
    () => {
      const data: any[] = props.tableData;
      FileTableData.value.length = 0;

      data.forEach((item) => {
        const node = {
          ...item,
          isFileOrFolder: 'folderId' in item ? 1 : 0, // 1：文件，0：文件夹
        };
        FileTableData.value.push(node);
      });
    }
  );
</script>

<style lang="less" scoped>
  .table-file {
    margin-top: 48px;
    flex-shrink: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .arco-form-item {
      margin-bottom: 0;
    }
  }

  .optional-title-icon {
    width: 46px;
    text-align: center;
  }
  .dropdown {
    position: absolute;
    :deep(.arco-dropdown-list-wrapper) {
      max-height: none;
    }
  }
  :deep(.arco-tag-size-medium) {
    line-height: 24px;
    border-radius: 4px;
  }
  .file-list-title {
    display: flex;
    align-items: flex-end;
    span {
      margin-right: 16px;
    }
  }
  .search-input {
    width: 220px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #c9cdd4;
    background-color: #fff;
  }

  .button-style {
    border-radius: 8px 8px 8px 8px !important;
  }

  :deep(.arco-breadcrumb-item) {
    cursor: pointer;
  }
  .table-name {
    display: flex;
    align-items: center;
    .table-file-name-img {
      width: 12px;
      margin-right: 8px;
    }
    .folder-name {
      cursor: pointer;
      color: rgb(var(--primary-6));
    }
  }
  .text-overflow {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }
  :deep(.arco-typography) {
    margin-bottom: 0;
  }

  :deep(.arco-table-container) {
    height: 100%;
  }

  .empty-wrapper {
    height: calc(100vh - 561px);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    img {
      display: block;
      width: 140px;
      height: 140px;
    }
    div {
      margin-top: 16px;
      color: #4e5969;
    }
  }
  .column-setting {
    position: absolute;
    top: 12px;
    right: 35px;
    z-index: 99;
  }
</style>
