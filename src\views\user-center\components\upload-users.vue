<template>
  <a-modal
    v-model:visible="uploadModal"
    :title="$t('user-center.import-users')"
    unmount-on-close
    width="420px"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
  >
    <a-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      action="/api/cde-collaboration/user/importUnit"
      :headers="{
        'Fusion-Auth': getToken() || '',
        'Fusion-Biz': setFusionBiz() || '',
      }"
      :with-credentials="true"
      :auto-upload="false"
      :data="{ secondLevel: props.unitName || '' }"
      :limit="1"
      draggable
      show-file-list
      list-type="text"
      :tip="$t('user-center.support-xlsx')"
      :show-link="true"
      @before-upload="beforeUpload"
      @success="handleSuccess"
    >
      <template #upload-item="{ fileItem }">
        <div :key="fileItem.uid" class="arco-upload-list-item">
          <div class="arco-upload-list-item-content">
            <span class="arco-upload-list-item-thumbnail">
              <IconFile />
            </span>
            <div class="arco-upload-list-item-name">
              <div class="arco-upload-list-item-name-text">{{
                fileItem.name
              }}</div>
            </div>
          </div>
          <span
            class="arco-upload-list-item-operation"
            @click="removeFile(fileItem)"
          >
            <span class="arco-icon-hover">
              <span class="arco-upload-icon arco-upload-icon-remove">
                <IconClose />
              </span>
            </span>
          </span>
        </div>
      </template>
    </a-upload>
    <template #footer>
      <div class="footer">
        <a-link href="/用户模板.xlsx" download>{{
          $t('user-center.download-membership-form')
        }}</a-link>
        <a-space>
          <a-button @click="handleCancel">{{
            $t('user-center.cancel')
          }}</a-button>
          <a-button type="primary" @click="handleSubmit">{{
            $t('user-center.import')
          }}</a-button>
        </a-space>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import type { FileItem } from '@arco-design/web-vue/es/upload/interfaces';
  import { IconFile, IconClose } from '@arco-design/web-vue/es/icon';
  import { Message, UploadInstance, Modal } from '@arco-design/web-vue';
  import { setFusionBiz } from '@/api/interceptor';
  import { getToken } from '@/utils/auth';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    unitName: {
      type: String,
      default: '',
    },
  });
  const emits = defineEmits(['update:modelValue', 'refresh']);
  const uploadModal = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emits('update:modelValue', val);
    },
  });
  const handleCancel = () => {
    emits('update:modelValue', false);
  };

  const fileList = ref<FileItem[]>([]);

  const uploadRef = ref<UploadInstance>();
  const handleSubmit = () => {
    // 不传参数默认提交fileList里第一个init状态的文件
    uploadRef.value?.submit();
  };
  const handleSuccess = (res: any) => {
    const result = res.response;
    emits('update:modelValue', false);
    if (result.status) {
      if (result.data.fail === 0) {
        Modal.success({
          title: `已成功导入${result.data.correct}条用户`,
          content: '',
        });
      } else {
        const nameArr = result.data.failNames.join('、');
        Modal.error({
          title: `已成功导入${result.data.correct}条用户`,
          content: `失败${result.data.fail}条，${nameArr}导入失败`,
          bodyStyle: { color: '#ff4d4f', textAlign: 'center' },
        });
      }
      emits('refresh');
    } else {
      Message.error(result.message);
    }
  };

  const beforeUpload = (file: File): boolean | Promise<boolean | File> => {
    // 判断file格式是否正确
    if (
      [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ].indexOf(file.type) === -1
    ) {
      Message.error(t('prjMember.please-upload-correct-xls-format'));
      return false;
    }
    return true;
  };

  const removeFile = (fileItem: FileItem) => {
    fileList.value = fileList.value.filter((item) => item.uid !== fileItem.uid);
  };
</script>

<style lang="less" scoped>
  .arco-upload-list-item-thumbnail {
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 1;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      color: var(--color-text-2);
    }

    button {
      border-radius: var(--border-radius-medium);
    }
  }
</style>
