<template>
  <template v-if="!isView">
    <a-space
      style="justify-content: space-between; width: 100%; margin-bottom: 16px"
    >
      <a-input
        style="width: 360px; flex-grow: 1"
        v-model="searchName"
        placeholder="请输入接口名称"
        @keyup.enter="filterRequests"
      />
      <a-space>
        <a-button type="outline" @click="filterRequests">
          <template #icon>
            <icon-search />
          </template>
          {{ $t('list.options.btn.search') }}</a-button
        >
        <a-button type="outline" @click="resetUsers">
          <template #icon><icon-loop /> </template
          >{{ $t('list.options.btn.reset') }}</a-button
        >
      </a-space>
    </a-space>

    <div class="table-box">
      <a-table
        stripe
        row-key="id"
        :columns="columns"
        :data="filterList"
        :scroll="scroll"
        :bordered="false"
        table-layout-fixed
        :pagination="false"
        selectedable
        :row-selection="rowSelection"
        v-model:selectedKeys="selectedKeys"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
      </a-table>
    </div>
  </template>
  <template v-else>
    <a-table
      stripe
      row-key="id"
      :columns="columns"
      :data="defaultChecked"
      :scroll="{
        y: 'calc(100vh - 358px)',
      }"
      :bordered="false"
      table-layout-fixed
      :pagination="false"
    >
      <template #index="{ rowIndex }">
        {{ rowIndex + 1 }}
      </template>
    </a-table>
  </template>
</template>

<script lang="ts" setup>
  import { ref, computed, toRefs, reactive, PropType } from 'vue';
  import { useI18n } from 'vue-i18n';

  import { TableColumnData } from '@arco-design/web-vue/es/table/interface';

  import { getAllRequest } from '../api';

  const scroll = {
    y: 'calc(100vh - 400px)',
  };

  type Column = TableColumnData & { checked?: true; userId?: string };

  const { t, locale } = useI18n();
  // 国际化类型

  const searchName = ref('');
  // 列表表格展示

  const props = defineProps({
    defaultChecked: {
      type: Array as PropType<Role.Api.RequestDto[]>,
      default: () => [],
    },
    isView: {
      type: Boolean,
      default: false,
    },
  });

  const requestList = ref<Role.Api.RequestDto[]>([]);
  const filterList = ref<Role.Api.RequestDto[]>([]);

  const { defaultChecked, isView } = toRefs(props);

  const columns = computed<Column[]>(() => [
    {
      title: t('prjMember.column.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 60,
      align: 'left',
    },
    {
      title: '接口名称',
      dataIndex: 'name',
      align: 'left',
      width: 140,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '接口路径',
      dataIndex: 'url',
      align: 'left',
      width: 350,
      ellipsis: true,
      tooltip: true,
    },

    {
      title: '接口类型',
      dataIndex: 'httpMethod',
      align: 'left',
    },
    {
      title: '所属模块',
      dataIndex: 'module',
      align: 'left',
    },
  ]);

  const selectedKeys = ref<string[]>([]);

  const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  });

  // 列表数据相关
  const fetchData = async () => {
    try {
      const { data } = await getAllRequest();
      requestList.value =
        data.map((item) => {
          return {
            ...item,
            name: item.name || item.description!,
          };
        }) || [];
      selectedKeys.value = defaultChecked.value.map((item) => item.id);
      filterRequests();
    } catch (err) {
      // you can report use errorHandler or other
    }
  };

  function filterRequests() {
    filterList.value = requestList.value.filter((item) => {
      return item.name.includes(searchName.value);
    });
  }
  function resetUsers() {
    searchName.value = '';
    filterRequests();
  }

  function getRequestIds(): string[] {
    return [...selectedKeys.value];
  }

  if (!isView.value) {
    fetchData();
  }

  defineExpose({ getRequestIds });
</script>

<script lang="ts">
  export default {
    name: 'ProjectMember',
  };
</script>

<style scoped lang="less">
  .table-box {
    height: calc(100% - 128px);
    display: flex;
    justify-content: space-between;
    gap: 20px;
  }
  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
  :deep(.arco-btn-size-small) {
    padding: 0 6px;
  }
  :deep(.arco-link:hover) {
    background-color: transparent;
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }
  .blue-link {
    color: rgb(var(--primary-6));
    cursor: pointer;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .setting {
    display: flex;
    align-items: center;
    width: 200px;
    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }
  .mini-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 8px;
    margin-bottom: 2px;
  }
  .danger-text {
    color: rgb(var(--red-6));
    &:hover {
      color: rgb(var(--red-6));
    }
  }
  :deep(.arco-badge-status-dot) {
    margin-right: 4px;
  }
  .accountState {
    text-align: left;
  }
  :deep(.arco-form-item) {
    margin-bottom: 0;
  }
  :deep(.arco-select-view) {
    background-color: #fff;
    border: 1px solid #c9cdd4 !important;
  }

  :deep(.arco-form-item-label-col > .arco-form-item-label) {
    color: #1d2129;
  }
</style>
