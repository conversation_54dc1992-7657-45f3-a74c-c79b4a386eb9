<template>
  <div class="common-tabs">
    <div class="icons">
      <icon-menu-unfold
        v-if="showMenu"
        :size="24"
        class="btn"
        @click="changeMenu(false)"
      />
      <icon-menu-fold v-else :size="24" class="btn" @click="changeMenu(true)" />
    </div>
    <div class="tabs">
      <div
        v-for="item in tabs"
        :key="item.value"
        :class="`${
          item.value === currentKey ? 'tab-item selected-tab' : 'tab-item'
        }`"
        @click="tabChange(item)"
        >{{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="commonTabs">
  import { defineProps, PropType, toRefs, computed, ref } from 'vue';
  import { Tab, TabsEmits } from '@/components/common-tabs/types';
  import { useAppStore } from '@/store';

  const app = useAppStore();

  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
    tabs: {
      type: Array as PropType<Tab[]>,
      required: true,
    },
  });
  const showMenu = ref(false);

  const emits = defineEmits<TabsEmits>();

  const { modelValue } = toRefs(props);

  const currentKey = computed({
    get: () => props.modelValue,
    set: (value) => emits('update:modelValue', value),
  });

  function tabChange(item: Tab) {
    currentKey.value = item.value;
    emits('clickTab', item.value, item);
  }

  // 菜单显隐切换点击
  const changeMenu = (value: any) => {
    showMenu.value = value;
    app.setMenuView(value);
  };
</script>

<script lang="ts">
  export default {
    name: 'CommonTabs',
  };
</script>

<style lang="less" scoped>
  .common-tabs {
    height: 36px;
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
  .icons {
    :deep(.arco-icon) {
      cursor: pointer;
    }
  }
  .tabs {
    height: 100%;
    display: flex;
    //border: 1px solid red;
    .selected-tab {
      background: #f2f3f5;
      color: #3366ff;
      font-weight: 530;
      transition: background 0.3s linear;
    }
    .tab-item {
      user-select: none;
      height: 36px;
      border-radius: 18px;
      padding-left: 16px;
      padding-right: 16px;
      font-size: 16px;
      font-family: '宋体 PingFang SC-Medium';
      line-height: 22px;
      text-align: center;
      padding-top: 7px;
      margin-left: 12px;
      cursor: pointer;
    }
  }
</style>
