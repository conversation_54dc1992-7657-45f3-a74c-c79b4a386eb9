import { AppRouteRecordRaw } from '../types';

const MEETING: AppRouteRecordRaw = {
  path: 'meeting',
  name: 'meeting',
  component: () => import('@/views/schedule/index.vue'),
  props: (route: any) => ({
    type: route.query.type,
    id: route.query.id,
  }),
  meta: {
    locale: 'menu.cdex.meeting',
    requiresAuth: true,
    icon: 'icon-calendar',
    order: 2,
    showAI: true,
    globalMode: ['project'],
  },
};

export default MEETING;
