<template>
  <div class="schedule-container">
    <div class="search-box">
      <a-input-search
        v-model="searchVal"
        :placeholder="`${t('schedule.search')}${
          props.type === 'meeting'
            ? t('schedule.meeting')
            : t('schedule.matters')
        }`"
        class="search-input"
        @change="searchHandle"
        @search="searchHandle"
      />
      <!-- 新建会议按钮 -->
      <a-button type="outline" @click="createHandle">
        <img
          :src="props.type === 'meeting' ? addMeetingImg : addTaskImg"
          :alt="props.type === 'meeting' ? '会议图标' : '事项图标'"
          style="width: 14px; height: 14px; margin-right: 8px"
        />
        {{
          props.type === 'meeting'
            ? $t('schedule.createMeeting')
            : $t('schedule.createMatter')
        }}
      </a-button>
    </div>
    <div v-if="type === 'matter'" class="sort-content">
      <span
        class="create-by"
        :style="{ color: matterType === 'all' ? '#165DFF' : '#4E5969' }"
        @click="allByMe"
        >{{ $t('schedule.all') }}</span
      >
      <a-divider direction="vertical" />
      <span
        class="create-by"
        :style="{ color: matterType === 'create' ? '#165DFF' : '#4E5969' }"
        @click="createdByMe"
        >{{ $t('schedule.createdByMe') }}</span
      >
      <a-divider direction="vertical" />
      <span
        class="responsible-by"
        :style="{ color: matterType === 'responsible' ? '#165DFF' : '#4E5969' }"
        @click="responsibleByMe"
        >{{ $t('schedule.responsibleByMe') }}</span
      >
    </div>
    <div v-if="type === 'meeting'" class="type-content">
      <div v-for="item in queryTypes" :key="item.id">
        <span
          class="create-by"
          :style="{ color: meetingType === item.id ? '#165DFF' : '#4E5969' }"
          @click="changeMeetingType(item.id)"
          >{{ $t(item.name) }}</span
        >
        <a-divider v-if="item.id !== 2" direction="vertical" />
      </div>
    </div>
    <div v-if="scheduleList?.length === 0" class="noData">
      <img :src="scheduleBgImg" alt="" />
      <div>{{ $t('schedule.noData') }}</div>
    </div>
    <div v-else class="schedule-content">
      <a-spin style="width: 100%" :loading="loading">
        <div
          v-for="item in scheduleList"
          :key="item.id"
          :class="{
            'active-list': item.id === activeId,
            'active': item.status === 2,
          }"
          class="schedule-list"
          @click="(event: MouseEvent) => selSchedule(item, event)"
        >
          <div class="top-box">
            <div class="block-left">
              <div class="block" :class="{ blockWidth: isEn }">
                <a-select
                  :model-value="item.status"
                  :data-id="item.id"
                  :class="'status' + item.status"
                  style="width: 98px"
                  @change="(val: number) => changeStatusHandle(item, val)"
                  @click.stop
                >
                  <template #prefix>
                    <img v-if="!item.status" :src="status0Img" alt="" />
                    <img v-if="item.status === 1" :src="status1Img" alt="" />
                    <img v-if="item.status === 2" :src="status2Img" alt="" />
                    <img
                      v-if="item.status === 3"
                      :src="statusCloseImg"
                      alt=""
                    />
                  </template>
                  <a-option
                    v-for="sta in statusOptions[item.type]"
                    :key="sta.id"
                    :value="sta.id"
                  >
                    {{ $t(sta.name) }}
                  </a-option>
                </a-select>

                <!-- <span v-if="item.status === null" class="status status0">
                  <img src="@/assets/images/meeting/status0.png" alt="" />
                  未开始
                </span>
                <span v-if="item.status === 0" class="status status0">
                  <img src="@/assets/images/meeting/status0.png" alt="" />
                  未开始
                </span>
                <span v-if="item.status === 1" class="status status1">
                  <img src="@/assets/images/meeting/status1.png" alt="" />进行中
                </span>
                <span v-if="item.status === 2" class="status status2">
                  <img src="@/assets/images/meeting/status2.png" alt="" />已结束
                </span> -->
              </div>
              <div v-if="item.type === 'meeting'" class="time">
                {{ dayjs(item.planStartTime).format('YYYY-MMDD') }}
                {{ dayjs(item.planStartTime).format('HH:mm') }}-{{
                  dayjs(item.planEndTime).format('HH:mm')
                }}</div
              >
              <div v-else class="time">
                {{ dayjs(item.planStartTime).format('YYYY-MMDD') }}至{{
                  dayjs(item.planEndTime).format('YYYY-MMDD')
                }}</div
              >
            </div>
            <div class="content">
              <div class="title">{{ item.title }}</div>
              <div class="described">{{ item.content }}</div>
            </div>
          </div>
          <div class="schedule-list-action"
            ><a-dropdown
              class="custom-dropdown"
              :data-id="item.id"
              @select="(event: MouseEvent) => handleSelectWithStop(event, item)"
              @click.stop
            >
              <icon-more class="icon-more" />
              <template #content>
                <a-doption @click="deleteHandle(item)"
                  ><a-button type="text">{{
                    $t('schedule.delete')
                  }}</a-button></a-doption
                >
                <a-doption @click="attentionHandle(item)"
                  ><a-button type="text">{{
                    item.isFollow === 0
                      ? $t('schedule.focus')
                      : $t('schedule.cancelFocus')
                  }}</a-button></a-doption
                >
              </template>
            </a-dropdown>
          </div>
        </div>
      </a-spin>
    </div>
    <a-modal
      :visible="delVidible"
      @ok="deleteSaveHandle"
      @cancel="handleCancel"
    >
      <template #title> {{ $t('schedule.delete') }} </template>
      <div>
        <a-radio-group
          v-if="nowRecord?.type === 'meeting'"
          v-model="repeatedVal"
          direction="vertical"
        >
          <a-radio value="0">{{ $t('schedule.delete.current') }}</a-radio>
          <a-radio value="1">{{
            $t('schedule.delete.currentAndAfter')
          }}</a-radio>
          <a-radio value="2">{{ $t('schedule.delete.all') }}</a-radio>
        </a-radio-group>
        <div v-else>{{
          `${deleteMatterMsg || ''} ${$t('schedule.delete.confirm')}`
        }}</div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, nextTick, watch, defineEmits } from 'vue';
  import {
    getAgendaList,
    getMeetingData,
    deleteMatterFlag,
    deleteMatter,
  } from '../api';
  import { agendaDetail } from '@/views/create-schedule/api';
  import {
    removeschedule,
    setScheduleFollow,
  } from '@/views/schedule/component/calendar/api';
  import { getMeetingDetail } from './meeting/api';
  import dayjs from 'dayjs';
  import { useUserStore, useGlobalModeStore } from '@/store';
  import { getLocalstorage } from '@/utils/localstorage';
  import { setscheduleStatus } from '@/views/schedule/component/calendar/api';
  import { Message } from '@arco-design/web-vue';
  import { getUserId } from '@/utils/auth';
  import addMeetingImg from '@/assets/images/home-page/<EMAIL>';
  import addTaskImg from '@/assets/images/home-page/<EMAIL>';
  import scheduleBgImg from '@/assets/images/schedule/schedule-bg.png';
  import status0Img from '@/assets/images/meeting/status0.png';
  import status1Img from '@/assets/images/meeting/status1.png';
  import status2Img from '@/assets/images/meeting/status2.png';
  import statusCloseImg from '@/assets/images/meeting/status-close.png';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';

  const { t } = useI18n();
  const { currentLocale } = useLocale();
  const isEn = computed(() => currentLocale.value === 'en-US');

  const userId = getUserId() || '';
  const delVidible = ref(false);
  const nowRecord = ref(null); // 当前选择的数据
  const repeatedVal = ref('0');
  const recordType = ref('');
  const currentProjectId = ref(getLocalstorage(`last_project_${userId}`) || '');
  const statusOptions: any = {
    meeting: [
      {
        id: 0,
        name: 'schedule.notStarted',
      },
      {
        id: 1,
        name: 'schedule.inProgress',
      },
      {
        id: 2,
        name: 'schedule.completed',
      },
    ],
    item: [
      {
        id: 1,
        name: 'schedule.inProgress',
      },
      {
        id: 2,
        name: 'schedule.completed',
      },
      {
        id: 3,
        name: 'schedule.closed',
      },
    ],
  };
  const queryTypes = ref([
    {
      id: 3,
      name: 'schedule.all',
    },
    {
      id: 0,
      name: 'schedule.notStarted',
    },
    {
      id: 1,
      name: 'schedule.inProgress',
    },
    {
      id: 2,
      name: 'schedule.completed',
    },
  ]);

  const statusOptionsMeeting = ref([
    {
      id: 0,
      name: '未开始',
    },
    {
      id: 1,
      name: '进行中',
    },
    {
      id: 2,
      name: '已完成',
    },
  ]);
  const statusOptionsMatter = ref([
    {
      id: 1,
      name: '进行中',
    },
    {
      id: 2,
      name: '已完成',
    },
  ]);

  const userStore = useUserStore();
  const companyId = computed(() => userStore.companyId);
  const globalModeStore = useGlobalModeStore();
  const globalMode = computed(() => globalModeStore.getGlobalMode);
  const userName = computed(() => userStore.username);
  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
    editType: {
      type: String,
      default: 'edit',
    },
  });

  const loading = ref(false);

  const emit = defineEmits(['selectData', 'onCreate']);
  const meetingType = ref(3); // 会议查询类型
  const changeMeetingType = (id: number) => {
    meetingType.value = id;
    getMeetingDatahandle();
  };
  const matterType = ref('all'); // 事项类型
  const allByMe = () => {
    if (matterType.value !== 'all') {
      matterType.value = 'all';
      getMatterDatahandle();
    }
  };
  // 我创建的
  const createdByMe = () => {
    if (matterType.value !== 'create') {
      matterType.value = 'create';
      getMatterDatahandle();
    }
  };

  // 我负责的
  const responsibleByMe = () => {
    if (matterType.value !== 'responsible') {
      matterType.value = 'responsible';
      getMatterDatahandle();
    }
  };
  const activeId = ref('');
  const matterData = ref(); // 事项数据
  // 数据选择
  const selSchedule = async (val: any, event?: MouseEvent) => {
    console.log('触发了这里', val);
    const target = event?.target as HTMLElement;
    // 判断是否点击的是下拉框内部
    const isClickInsideDropdown = scheduleList.value.some((item: any) => {
      const statusRef = document.querySelector(`[data-id="${item.id}"]`);
      return statusRef instanceof HTMLElement && statusRef.contains(target);
    });

    // 如果点击的是下拉框内的内容，则不触发父组件事件
    if (isClickInsideDropdown) {
      console.log('点击的是下拉框内部，阻止父组件事件');
      return;
    }
    if (val) {
      activeId.value = val?.id;
      if (val.type === 'meeting') {
        const { data } = await getMeetingDetail(val.id);
        emit('selectData', data);
      } else {
        const { data } = await agendaDetail(val.id);
        matterData.value = data;
        emit('selectData', data);
      }
    }
  };

  const allScheduleList = ref([]);

  // 获取会议数据
  const scheduleList: any = ref(null);
  const getMeetingDatahandle = async () => {
    loading.value = true;
    const param = {
      pageNo: 1,
      pageSize: 999999,
      ...(meetingType.value !== 3 ? { status: meetingType.value } : {}),
      // 如果是个人空间直接传当前用户所在的团队id
      // 如果是项目空间，则需要传项目id,不需要传团队id
      ...(globalMode.value === 'work'
        ? { teamId: userStore.teamId === 'global' ? '' : userStore.teamId }
        : { projectId: currentProjectId.value }), // 项目空间
    };
    const { list } = (await getMeetingData(param)) as any;
    list.forEach((item: any) => {
      // status为null时表示该数据状态未修改   未修改时使用 autoStatus（根据时间过期自动设置该状态）
      // status有值表示 修改过状态  状态就用status值
      item.status = item.status === null ? item.autoStatus || 0 : item.status;
    });
    scheduleList.value = list;
    allScheduleList.value = list;
    loading.value = false;
  };

  // 获取事项数据
  const getMatterDatahandle = async () => {
    loading.value = true;
    const baseParams = {
      pageNo: 1,
      pageSize: 999999,
      ...(globalMode.value === 'work' && userStore.teamId !== 'global'
        ? { teamId: userStore.teamId }
        : {}),
      ...(globalMode.value === 'project' && currentProjectId.value
        ? { projectId: currentProjectId.value }
        : {}),
    };

    let params;

    if (props.type === 'matter') {
      let switchPeople;
      if (matterType.value === 'create') {
        switchPeople = 1;
      } else if (matterType.value === 'responsible') {
        switchPeople = 2;
      }
      params = {
        ...baseParams,
        ...(switchPeople ? { switchPeople } : {}),
      };
    } else {
      params = { ...baseParams };
    }
    const res = await getAgendaList(params);
    scheduleList.value = res.data?.list || [];
    scheduleList.value.forEach((element: any) => {
      element.status = element?.agendaStatus;
      // element.planStartTime = element?.createDate;
      element.id = element?.scheduleDetailId;
    });
    allScheduleList.value = res.data?.list;
    loading.value = false;
  };

  const init = async (id?: string) => {
    if (props.type === 'meeting') {
      await getMeetingDatahandle();
    } else if (props.type === 'matter') {
      await getMatterDatahandle();
    }
    // 用于定位到日程编辑跳转的那条数据 进行选中回显
    if (id) {
      if (props.type === 'meeting') {
        const { data } = await getMeetingDetail(id);
        emit('selectData', data);
      } else {
        const { data } = await agendaDetail(id);
        // if (data.createBy === userName.value) {
        //   matterType.value = 'create';
        // } else {
        //   matterType.value = 'responsible';
        // }
        emit('selectData', data);
      }
      activeId.value = id;
      await nextTick();
      const res = document.getElementsByClassName('active-list');
      if (res.length > 0) {
        const element = res[0];
        element.scrollIntoView({
          behavior: 'auto',
          block: 'center',
          inline: 'nearest',
        });
      }
    } else if (props.editType === 'edit') {
      const firstData: any = scheduleList.value[0];
      if (firstData?.type) {
        firstData.id =
          firstData.type === 'meeting'
            ? firstData.id
            : firstData.scheduleDetailId;
        // 没有传id 默认展示第一条
        selSchedule(firstData);
      }
    } else {
      console.log('触发了这里377', props.editType);
    }
  };

  // const grandparentMethod = inject('grandparentMethod');
  // 新增 传递到会议列表
  const createHandle = () => {
    // props.type分为会议：meeting和事项:matter
    // 打印一下当前是个人身份还是集团身份，个人空间还是项目空间
    // 集团身份：有值，个人身份：无值
    // globalMode === 'project'代表项目空间
    // globalMode === 'work'代表个人空间
    console.log(companyId.value, '是个人身份还是集团身份');
    console.log(props.type, 'props.type');
    // 列表选中样式去掉
    activeId.value = '';
    const data = {
      editType: 'new',
      type: props.type,
    };
    console.log('传递的参数334:', data);
    emit('onCreate', data); // 触发事件
    // if (grandparentMethod) grandparentMethod(data);
  };

  const searchVal: any = ref();
  const searchHandle = () => {
    if (searchVal.value) {
      scheduleList.value = allScheduleList.value.filter((item: any) => {
        return item.title.includes(searchVal.value);
      });
    } else {
      scheduleList.value = allScheduleList.value;
    }
  };

  // 通用设置状态方法
  const updateScheduleStatus = async (record: any) => {
    const param = {
      scheduleDetailId: record.scheduleDetailId,
      status: record.status,
      type: record.type === 'meeting' ? 2 : 1,
    };
    const res = await setscheduleStatus(param);
    if (res.status) {
      Message.success(t('schedule.status.success'));
      console.log('设置的状态是：', record.status);
      if (props.type === 'meeting') {
        await getMeetingDatahandle();
        // 需要判断是不是全部列表，是就选中的activeId还在列表中，如果不是全部，则默认选中第一条
        if (activeId.value === record.id) {
          selSchedule(meetingType.value === 3 ? record : scheduleList.value[0]);
        } else {
          // 查找当前 activeId 是否还在列表中
          const activeItem = scheduleList.value.find(
            (item: any) => item.id === activeId.value
          );
          if (activeItem) {
            selSchedule(activeItem);
          } else {
            selSchedule(scheduleList.value[0]);
          }
        }
      } else {
        await getMatterDatahandle();
        if (activeId.value === record.id) {
          selSchedule(record);
        }
      }
      // emits('refresh');
    }
  };
  const detailData = ref();
  // 只是单独的调一下详情接口
  const getMatterDetail = async (id: string) => {
    const res = await agendaDetail(id);
    if (res.status) {
      detailData.value = res.data;
    }
  };
  // 检查子事项状态
  const checkMatterStatus = async (record: any, newStatus: number) => {
    console.log('record513', record);
    console.log('newStatus513', newStatus);
    // 调一下详情接口，拿到子事项数据
    await getMatterDetail(record.id);
    const children = detailData.value?.children || [];
    const allSubMatterCompleted = children.every(
      (item: { agendaStatus: number }) => item.agendaStatus === 2
    ); // 获取所有子事项状态是否都为已完成
    console.log('allSubMatterCompleted', allSubMatterCompleted);
    if ((allSubMatterCompleted && newStatus === 2) || newStatus !== 2) {
      record.status = newStatus;
      await updateScheduleStatus(record);
    } else if (newStatus === 2) {
      Message.info(t('schedule.status.completeSubMatters'));
      // if (matterData.value) {
      //   record.status = matterData.value.agendaStatus;
      // } else {
      //   record.status = detailData.value.agendaStatus;
      // }
    }
  };
  const hasNoMatterStatusPermission = (record: any) => {
    // 判断当前用户不是创建人，且不在参与人列表里
    const chargePersonList = (record.chargePersonId || '')
      .split(',')
      .filter(Boolean);
    return (
      userName.value !== record.createBy &&
      !(chargePersonList || []).includes(userName.value)
    );
  };
  // 修改状态
  const changeStatusHandle = async (record: any, newStatus: number) => {
    // 会议的列表状态只有创建人能修改，事项的状态是创建人和分配人能修改
    // 只有创建人可以修改状态
    if (record.type === 'meeting' && userName.value !== record.createBy) {
      Message.info(t('schedule.status.notCreator'));
      return;
    }
    if (record.type === 'item' && hasNoMatterStatusPermission(record)) {
      Message.info(t('schedule.status.notMatterStatusPermission'));
      return;
    }
    if (record.type === 'item') {
      await checkMatterStatus(record, newStatus);
    } else {
      // 只有通过校验才赋值
      record.status = newStatus;
      await updateScheduleStatus(record);
    }
  };
  const handleSelect = () => {
    console.log('handleSelect 被触发');
    // 原来的逻辑
  };
  const handleSelectWithStop = (event: Event, item: any) => {
    const target = event.target as HTMLElement;
    // 获取当前下拉框的 DOM 元素
    const dropdownRef = document.querySelector(`[data-id="${item.id}"]`);
    // 检查 dropdownRef 是否有效且是一个 HTMLElement
    if (dropdownRef instanceof HTMLElement && dropdownRef.contains(target)) {
      // 如果点击的是下拉框组件内部内容，阻止事件冒泡
      event.stopPropagation();
      console.log('点击的是删除下拉框内部，阻止父组件事件');
      return;
    }
    console.log('handleSelect 被触发');
    handleSelect(); // 调用原来的方法
  };
  const handleIconMoreClick = (event: Event, item: any) => {
    const target = event.target as HTMLElement;
    // 获取当前下拉框的 DOM 元素
    const dropdownRef = document.querySelector(`[data-id="${item.id}"]`);
    // 检查 dropdownRef 是否有效且是一个 HTMLElement
    if (dropdownRef instanceof HTMLElement && dropdownRef.contains(target)) {
      // 如果点击的是下拉框组件内部内容，阻止事件冒泡
      event.stopPropagation();
      console.log('点击的是删除下拉框内部，阻止父组件事件');
      return;
    }
    Message.info(t('schedule.status.notMeetingCreator'));
  };
  // 取消删除
  const handleCancel = () => {
    delVidible.value = false;
  };
  // 删除事项
  const deleteMatterList = async () => {
    const params = {
      scheduleDetailId: nowRecord.value.id,
    };
    try {
      const res = await deleteMatter(params);
      if (res.status) {
        Message.success(t('schedule.delete.success'));
        await getMatterDatahandle();
        // 查找当前 activeId 是否还在列表中
        const activeItem = scheduleList.value.find(
          (item: any) => item.id === activeId.value
        );
        if (activeItem) {
          selSchedule(activeItem);
        } else {
          selSchedule(scheduleList.value[0]);
        }
        // emits('refresh');
        delVidible.value = false;
      }
    } catch (error) {
      console.log(error);
    }
  };
  // 删除确认框
  const deleteSaveHandle = async () => {
    if (nowRecord.value.type === 'meeting') {
      if (!repeatedVal.value) {
        Message.info(t('schedule.delete.selectType'));
        return;
      }
      const param = {
        scheduleDetailId: nowRecord.value.scheduleDetailId,
        rmOption: repeatedVal.value,
      };
      try {
        const res = await removeschedule(param);
        if (res.status) {
          Message.success(t('schedule.delete.success'));
          await getMeetingDatahandle();
          // 查找当前 activeId 是否还在列表中
          const activeItem = scheduleList.value.find(
            (item: any) => item.id === activeId.value
          );
          if (activeItem) {
            selSchedule(activeItem);
          } else {
            selSchedule(scheduleList.value[0]);
          }
        }
      } catch (error) {
        console.log(error);
      } finally {
        delVidible.value = false;
      }
    } else {
      deleteMatterList();
    }
  };
  const deleteMatterMsg = ref('');
  // 删除
  const deleteHandle = async (item: any) => {
    if (userName.value === item.createBy) {
      // 如果是创建人，允许删除
      console.log('删除', item);
      // item代表事项
      if (item.type === 'item') {
        const params = {
          scheduleDetailId: item.scheduleDetailId,
        };
        try {
          const res = await deleteMatterFlag(params);
          if (res.status) {
            const [firstError] = res.data.err || []; // 使用数组解构，并处理 err 可能为 undefined 的情况
            deleteMatterMsg.value = firstError;
            console.log('[ firstError ] >', firstError);
          }
        } catch (error) {
          console.log(error);
        }
      }
      nowRecord.value = item;
      recordType.value = item.type;
      delVidible.value = true;
    } else {
      Message.info(
        item.type === 'item'
          ? t('schedule.status.notMatterCreator')
          : t('schedule.status.notMeetingCreator')
      );
    }
  };
  // 关注
  const attentionHandle = async (item) => {
    try {
      const param = {
        // 因为关注的日程数据分为三种，我的面板，别人分配给我的日程带着别人的面板，我的项目面板，所以只需要日程id,后端做逻辑区分
        // schedulePanelIds:
        //   globalMode.value === 'project'
        //     ? props.currentProjectScheduleId
        // : mergedScheduleIds.value,
        scheduleDetailId: item.scheduleDetailId,
      };
      const res = await setScheduleFollow(param);
      if (res.status) {
        Message.success(res.message);
        if (item.type === 'meeting') {
          await getMeetingDatahandle();
        } else {
          await getMatterDatahandle();
        }
      }
      console.log(415151);
    } catch (error) {
      console.log(error);
    }
  };
  watch(
    () => userStore.teamId,
    () => {
      init();
      // emit('selectData', {});
    },
    {
      deep: true,
      // immediate: true,
    }
  );
  // 监听 scheduleList 的变化
  watch(
    scheduleList,
    (newValue) => {
      if (Array.isArray(newValue) && newValue.length === 0) {
        createHandle(); // 调用 createHandle 方法
      }
    },
    { deep: true } // 深度监听
  );

  // onMounted(() => {
  //   init();
  // });

  defineExpose({
    init,
  });
</script>

<script lang="ts">
  export default {
    name: 'MeetingListItem',
  };
</script>

<style lang="less" scoped>
  .schedule-container {
    border: 1px solid #d9d9d9;
    border-right: none;
    height: 100%;
    width: 100%;
    overflow-y: auto;
    padding: 20px 6px 20px 20px;
    border-bottom-left-radius: 8px;
    border-top-left-radius: 8px;
    :deep(.arco-input-wrapper) {
      height: 32px;
      border-radius: 8px;
      background-color: #fff;
      border: 1px solid #c9cdd4 !important;
      border-radius: 8px;
    }
    .schedule-content {
      height: calc(100vh - 292px);
      overflow-y: auto;
      overflow-x: hidden;
      padding-right: 10px;
    }
    .noData {
      height: calc(100vh - 292px);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      img {
        display: block;
        width: 140px;
        height: 140px;
      }
      div {
        margin-top: 16px;
        color: #4e5969;
      }
    }
    :deep(.arco-spin) {
      height: calc(100vh - 300px);
    }
    .noData {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .search-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      padding-right: 14px;
      .search-input {
        margin-right: 16px;
      }
      .plus-button {
        vertical-align: middle;
        cursor: pointer;
      }
    }
    .type-content {
      height: 32px;
      line-height: 32px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 16px;

      .create-by {
        cursor: pointer;
      }
    }
    .sort-content {
      height: 32px;
      line-height: 32px;
      display: inline-block;
      font-size: 16px;

      .create-by {
        cursor: pointer;
      }
      .responsible-by {
        cursor: pointer;
      }
    }
    .active-list {
      background-color: #f9fcff;
      border-radius: 8px;
    }
    .schedule-list {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      cursor: pointer;
      // height: 73px;
      padding: 20px 20px;
      align-items: top;
      border-bottom: 1px solid #ededed;
      .top-box {
        flex: 1;
        .block-left {
          display: flex;
          align-items: center;
          justify-content: flex-start;
        }
        .time {
          margin-left: 8px;
          font-size: 16px;
          color: #1d2129;
        }
        .block {
          height: 22px;
          :deep(.arco-select-view-single) {
            height: 22px;
            border-radius: 4px;
          }
          :deep(.arco-select-view-value) {
            min-height: 0 !important;
          }
          :deep(.arco-select) {
            border-radius: 4px;
          }
          :deep(.status0) {
            background-color: #ffece8 !important;
            color: #ff4d4f;
            .arco-select-view-icon {
              color: #ff4d4f;
            }
          }
          :deep(.status1) {
            background-color: #e8f2ff !important;
            color: #3366ff;
            .arco-select-view-icon {
              color: #3366ff;
            }
          }
          :deep(.status2) {
            background-color: #e5e6eb !important;
            color: #86909c;
            .arco-select-view-icon {
              color: #86909c;
            }
          }
          :deep(.status3) {
            background-color: #e5e6eb !important;
            color: #86909c;
            .arco-select-view-icon {
              color: #86909c;
            }
          }
          img {
            width: 16px;
            height: 16px;
          }
          :deep(.arco-select-view-single) {
            padding: 0 8px;
          }
          :deep(.arco-select-view-prefix) {
            padding-right: 4px;
          }
          :deep(.arco-select-view-suffix) {
            padding-left: 4px;
          }
          // .status {
          //   display: inline-block;
          //   width: 78px;
          //   height: 24px;
          //   margin-top: 8px;

          //   padding: 0 8px;
          //   line-height: 24px;
          //   background-color: #e5e6eb;
          //   color: #86909c;
          //   margin-right: 16px;
          //   padding: 0 8px;
          //   border-radius: 4px;
          //   img {
          //     width: 14px;
          //     vertical-align: middle;
          //   }
          // }
          // .statusImg {
          //   background-image: url('@/assets/images/meeting/status0.png');
          //   // background-size: 16px ;
          // }
          // .status0 {
          //   background-color: #ffece8;
          //   color: #ff4d4f;
          // }
          // .status1 {
          //   background-color: #e8f2ff;
          //   color: #3366ff;
          // }
          // .status2 {
          //   background-color: #ffece8;
          //   color: #86909c;
          // }
        }
        .blockWidth {
          width: 130px;
          :deep(.arco-select-view-single) {
            min-width: 130px !important;
          }
        }
      }

      .content {
        overflow: hidden;
        margin-top: 6px;
        > div {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
        .title {
          color: #1d2129;
          font-weight: 500;
          margin-top: 2px;
        }

        .described {
          margin-top: 8px;
          font-size: 14px;
          color: #86909c;
        }
      }
    }
  }

  .active {
    .title {
      color: #86909c !important;
    }
    .described {
      color: #c9cdd4 !important;
    }
    .time {
      color: #c9cdd4 !important;
    }
    .block .status {
      background-color: #e5e6eb !important;
      color: #86909c !important;
    }
  }
</style>

<style></style>
