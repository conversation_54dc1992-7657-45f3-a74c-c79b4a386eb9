<template>
  <div class="org-list-modal">

  <!-- 组织机构选择模态框 -->
  <a-modal
    :visible="visible"
    :ok-text="$t('org-list.confirm')"
    :title="$t('org-list.title')"
    @cancel="handleCancel"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    width="400px"
    title-align="start"
    style="padding: 0"
    :render-to-body = "false"
  >
    <!-- 模态框内容区域 -->
    <div class="org-list-content">
      <!-- 面包屑导航 -->
      <div class="breadcrumb-box">
        <a-breadcrumb>
          <template #separator>
            <icon-right />
          </template>
          <a-breadcrumb-item
            v-for="(item, index) in breadcrumbList"
            :key="index"
            @click="setBreadcrumbHandle(index)"
          >
            <a-tooltip :content="item.name">
              <span
                :class="
                  index === breadcrumbList.length - 1
                    ? 'breadcrumb-name last-breadcrumb'
                    : 'breadcrumb-name'
                "
                >{{ item.name }}</span
              >
            </a-tooltip>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <!-- 部门列表区域 -->
      <div class="structure-list">
        <div
          v-for="(item, index) in departList"
          :key="index"
          class="structure-block"
          @click="handleDepartChange(item)"
        >
          <div
            style="
              flex: 1;
              display: flex;
              align-items: center;
              width: calc(100% - 20px);
            "
          >
            <!-- 部门图标，点击选择当前部门 -->
            <span class="departImg" @click.stop="handleSelect(item)"></span>
            <!-- 部门名称，点击选择当前部门 -->
            <span class="name" @click.stop="handleSelect(item)">{{
              item.name
            }}</span>
          </div>
          <!-- 进入下级部门的箭头 -->
          <div style="width: 20px">
            <icon-right />
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { getDepartmentList } from '@/api/selectMember';

  const { t } = useI18n();

  // 控制模态框的显示与隐藏
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  // 定义组件 emits
  const emits = defineEmits(['update:visible', 'selectOrg']);

  // 面包屑导航列表，记录当前路径
  const breadcrumbList = ref([
    {
      id: -1,
      name: computed(() => t('selectMembers.organization')),
    },
  ]);

  // 部门列表数据
  const departList = ref<any[]>([
    // 使用 any[] 临时标记，实际应定义 Department 接口
    {
      name: '',
      orgNo: 0,
    },
  ]);

  // 当前选中的部门（在进入下级时记录）
  const currentDep = ref<any>({
    name: '',
    id: 0,
  });

  // 获取部门数据
  // 根据 parentNo 获取子部门列表
  const getDepartList = async (line: { orgNo: number }) => {
    const param = {
      parentNo: line.orgNo || 0,
      // 过滤数据，只显示内部部门
      filterExternalOrg: true,
    };
    const { data } = await getDepartmentList(param);
    departList.value = data || [];
  };

  // 处理面包屑点击
  // 当点击面包屑项时，回到对应的层级并重新加载部门列表
  const setBreadcrumbHandle = (index: number) => {
    const list = breadcrumbList.value.slice(0, index + 1);
    breadcrumbList.value = list;
    const currentOrgId = list[list.length - 1]?.id || 0;
    getDepartList({ orgNo: currentOrgId });
  };

  // 添加面包屑项
  // 当进入下级部门时，向面包屑列表中添加当前部门信息
  const changeBreadcrumb = (line: {
    orgNo: number;
    name: string;
    entName?: string;
  }) => {
    const breadcrumb = {
      id: line.orgNo,
      name: line.entName || line.name,
    };
    breadcrumbList.value = [...breadcrumbList.value, breadcrumb];
  };

  // 处理部门项点击（进入下级）
  // 当点击部门项的非图标/名称区域时，进入下级部门
  const handleDepartChange = (item: {
    orgNo: number;
    name: string;
    entName?: string;
  }) => {
    currentDep.value = { ...item };
    changeBreadcrumb(item);
    getDepartList(item);
  };

  // 处理部门选择（选择当前部门并关闭弹窗）
  // 当点击部门图标或名称时，选择当前部门并触发 selectOrg 事件，然后关闭弹窗
  const handleSelect = (item: { orgNo: number; name: string }) => {
    emits('selectOrg', { orgNo: item.orgNo, name: item.name });
    emits('update:visible', false);
  };

  // 重置组织列表状态（不关闭模态框）
  const resetOrgListState = () => {
    breadcrumbList.value = [
      {
        id: -1,
        name: computed(() => t('selectMembers.organization')),
      },
    ];
    departList.value = [
      {
        name: '',
        orgNo: 0,
      },
    ];
  };

  // 处理取消按钮点击
  // 只关闭弹窗
  const handleCancel = () => {
    emits('update:visible', false);
  };

  // 监听 visible 属性变化
  // 当模态框显示时，重置状态并加载初始部门数据
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        resetOrgListState();
        getDepartList({ orgNo: 0 });
      }
    },
    {
      immediate: true,
    }
  );
</script>

<script lang="ts">
  export default {
    name: 'OrgList',
  };
</script>

<style scoped lang="less">
  /* 覆盖 ArcoDesign 的模态框 body 样式 */
  .org-list-modal {
    :deep(.arco-modal-body) {
      padding: 5px 20px !important;
    }
  }


  // /* 模态框内容样式 */
  .org-list-content {
    height: 400px;
    display: flex;
    flex-direction: column;

    // 面包屑容器样式
    .breadcrumb-box {
      display: flex;
      flex-wrap: wrap;
      margin: 12px 0;
      flex-shrink: 0;
      :deep(.arco-breadcrumb) {
        flex-wrap: wrap;
      }
      :deep(.arco-breadcrumb-item) {
        max-width: 300px;
      }
      .breadcrumb-name {
        display: inline-block;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }
      .last-breadcrumb {
        max-width: 100%;
      }
    }

    /* 部门列表容器样式 */
    .structure-list {
      cursor: pointer;
      flex: 1;
      overflow-y: auto;
      .structure-block {
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        align-items: center;
        height: 38px;
        padding: 0 12px;
        line-height: 38px;
        font-size: 14px;
        color: #1d2129;
        margin-bottom: 8px;
        width: calc(100% - 8px);
        border-radius: 8px;
        overflow: hidden;
        /* 鼠标悬停效果 */
        &:hover {
          background-color: #f2f3f5;
        }
        /* 部门图标样式 */
        .departImg {
          display: inline-block;
          width: 24px;
          height: 24px;
          background-color: #3366ff;
          background-image: url(@/assets/images/schedule/organization-chart.png);
          background-size: 12px 12px;
          background-repeat: no-repeat;
          border-radius: 50%;
          background-position: center center;
          vertical-align: middle;
          cursor: pointer;
        }
        .name {
          display: inline-block;
          margin-left: 8px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: calc(100% - 40px);
          cursor: pointer;
        }
      }
    }
  }
</style>
