import { AppRouteRecordRaw } from '../types';

const KnowledgeBase: AppRouteRecordRaw = {
  path: '/knowledgeBase',
  name: 'knowledgeBase',
  component: () => import('@/views/knowledge-base-new/index.vue'),

  meta: {
    locale: 'menu.knowledgeBase',
    requiresAuth: true,
    allowProjectTemplate: true,
    icon: 'icon-folder',
    order: 3,
    globalMode: ['work'],
    showTemplate: true, // 当是项目模版时是否展示
  },
  children: [
    {
      path: 'personal/:kbId/:rootFolderId',
      name: 'personalBase',
      component: () =>
        import('@/views/knowledge-base-new/components/base/personal-base.vue'),
      meta: {
        // locale: 'menu.knowledgeBase',
        requiresAuth: true,
        hideInMenu: true,
        showAI: false,
        globalMode: ['work'],
      },
    },
    {
      path: 'shared/:kbId/:rootFolderId',
      name: 'sharedBase',
      component: () =>
        import('@/views/knowledge-base-new/components/base/shared-base.vue'),
      meta: {
        // locale: 'menu.knowledgeBase',
        requiresAuth: true,
        hideInMenu: true,
        showAI: false,
        globalMode: ['work'],
      },
    },
  ],
};

export default KnowledgeBase;
