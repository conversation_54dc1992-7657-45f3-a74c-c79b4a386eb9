export interface PathNode {
  id: string; // 文件夹id
  name: string; // 名称
}

export interface KnowledgeBaseUsage {
  fileCnt: number;
  usedStorage: number;
  remainingStorage: number;
}

export interface KnowledgeBaseState {
  baseRootId: string; // 选中的知识库根节点id
  baseRagId: string; // 选中的知识库的agentID
  baseLeafId: string; // 选中的共享知识库子节点id
  folderId: string; // 选中的文件夹的id（知识库有一个默认的文件夹）
  isBaseEmpty: boolean; // 知识库是否为空
  hasFile: boolean; // 知识库是否存在文件
  history: Array<PathNode>; // 打开文件夹的历史路径
  currentIndex: number; // 当前路径索引
  fileCnt: number; // 文件数量
  usedStorage: number; // 已使用存储
  remainingStorage: number; // 剩余存储
  isSearch: boolean; // 是否是搜索状态
  fileId: string; // 搜索文件id
  needRefresh: boolean; // 是否需要重新查询知识库使用情况
  folderVOId: string
}
