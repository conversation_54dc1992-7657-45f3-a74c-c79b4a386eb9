
const TOKEN_KEY = 'work_access_token';
const WPS_TOKEN = 'wps_token';

const USER_ID_KEY = 'work_user_id';
const USER_NAME_KEY = 'work_user_name';
const isLogin = () => {
  return !!localStorage.getItem(TOKEN_KEY);
};

const getToken = () => {
  return localStorage.getItem(TOKEN_KEY);
};

const setToken = (token: string) => {
  localStorage.setItem(TOKEN_KEY, token);
};

const clearToken = () => {
  localStorage.removeItem(TOKEN_KEY);
};

// 获取wpstoken
const getWpsToken = () => {
  return localStorage.getItem(WPS_TOKEN);
};
// 设置wpstoken
const setWpsToken = (token: string) => {
  localStorage.setItem(WPS_TOKEN, token);
};
// 清除wpstoken
const clearWpsToken = () => {
  localStorage.removeItem(WPS_TOKEN);
};

const getBIMBaseToken = () => {
  return localStorage.getItem('BIMBase_token');
};

const setBIMBaseToken = (token: string) => {
  localStorage.setItem('BIMBase_token', token);
};
const clearBIMBaseToken = () => {
  localStorage.removeItem('BIMBase_token');
};
const getXBaseToken = () => {
  return localStorage.getItem('XBase_token');
};

const setXBaseToken = (token: string) => {
  return localStorage.setItem('XBase_token', token);
};
const clearXBaseToken = () => {
  return localStorage.removeItem('XBase_token');
};

const setUserId = (userid: string) => {
  return localStorage.setItem(USER_ID_KEY, userid)
}
const getUserId = () => {
  return localStorage.getItem(USER_ID_KEY);
}
const clearUserId = () => {
  return localStorage.removeItem(USER_ID_KEY);
}
const setUserName = (userName: string) => {
  return localStorage.setItem(USER_NAME_KEY, userName)
}
const getUserName = () => {
  return localStorage.getItem(USER_NAME_KEY);
}
const clearUserName = () => {
  return localStorage.removeItem(USER_NAME_KEY);
}
export {
  isLogin,
  getToken,
  setToken,
  clearToken,
  getWpsToken,
  setWpsToken,
  clearWpsToken,
  getBIMBaseToken,
  setBIMBaseToken,
  clearBIMBaseToken,
  getXBaseToken,
  setXBaseToken,
  clearXBaseToken,
  setUserId,
  getUserId,
  clearUserId,
  setUserName,
  getUserName,
  clearUserName
};
