import { ViewColumn } from '@/components/dynamic-column/hooks';
import { TableColumnData } from '@arco-design/web-vue/es/table/interface';

export type Column = TableColumnData & { checked?: true };

export function getFileColumns(): ViewColumn[] {
  return [
    {
      title: 'design.index',
      dataIndex: 'index',
      slotName: 'index',
      width: 40,
      align: 'center',
      defaultSelected: true,
    },
    {
      title: 'design.name',
      dataIndex: 'name',
      slotName: 'name',
      align: 'left',
      width: 150,
      defaultSelected: true,
    },
    {
      title: 'design.version',
      dataIndex: 'version',
      slotName: 'version',
      width: 50,
      align: 'center',
      defaultSelected: true,
    },
    {
      title: 'design.size',
      dataIndex: 'size',
      slotName: 'size',
      width: 50,
      align: 'center',
      defaultSelected: true,
    },
    {
      title: 'file-manage.regenerator',
      dataIndex: 'updateUserName',
      slotName: 'updateUserName',
      width: 120,
      defaultSelected: true,
    },
    {
      title: 'file-manage.update-date',
      width: 180,
      dataIndex: 'updateDate',
      slotName: 'updateDate',
      defaultSelected: true,
    },
    {
      title: 'design.standard-name',
      dataIndex: 'standardName',
      slotName: 'standardName',
      align: 'left',
      width: 150,
      defaultSelected: false,
    },
    {
      title: 'design.description',
      dataIndex: 'description',
      slotName: 'description',
      align: 'left',
      width: 150,
      defaultSelected: false,
    },
  ];
}
