<template>
  <a-modal
    :visible="props.visible"
    :title="props.title"
    :width="900"
    :unmount-on-close="true"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-tabs v-model:active-key="activeTab" style="height: calc(100vh - 300px)">
      <a-tab-pane class="tab-pane" key="basic" title="基本信息">
        <a-space style="justify-content: center" fill>
          <a-form
            ref="roleRef"
            style="width: 400px; padding-top: 20px"
            :model="formData"
            auto-label-width
            :disabled="isView"
          >
            <a-form-item
              field="name"
              label="角色名称"
              validate-trigger="input"
              :rules="[
                {
                  required: true,
                  message: '角色名称未填写',
                },
              ]"
            >
              <a-input
                v-model="formData.name"
                :disabled="formData.isGeneral"
                :placeholder="$t('user-center.please-enter-name')"
              />
            </a-form-item>
            <a-form-item
              field="code"
              label="角色编码"
              validate-trigger="input"
              :rules="[
                {
                  required: true,
                  message: '角色编码未填写',
                },
              ]"
            >
              <a-input
                v-model="formData.code"
                :disabled="formData.isGeneral"
                :placeholder="$t('user-center.please-enter-name')"
              />
            </a-form-item>
            <a-form-item field="isGeneral" label="是否通用">
              <a-switch
                type="line"
                v-model="formData.isGeneral"
                disabled
                :checked-value="true"
                :unchecked-value="false"
              >
                <template #checked> 是 </template>
                <template #unchecked> 否 </template>
              </a-switch>
            </a-form-item>
          </a-form>
        </a-space>
      </a-tab-pane>
      <a-tab-pane key="btn" class="tab-pane" title="按钮绑定">
        <BindBtns
          v-if="formReady"
          ref="bindBtnsRef"
          :isView="isView"
          :default-checked="formData.buttonIdList"
        />
      </a-tab-pane>
      <a-tab-pane key="user" class="tab-pane" title="用户绑定">
        <BindUsers :isView="isView" ref="bindUsersRef" :roleId="selectId" />
      </a-tab-pane>
      <a-tab-pane key="request" class="tab-pane" title="接口绑定">
        <BindRequests
          v-if="formReady"
          ref="bindRequestsRef"
          :isView="isView"
          :default-checked="formData.requestList"
        />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script lang="ts" setup name="addRole">
  import { ref, onMounted, computed, DefineComponent, toRefs } from 'vue';
  import { FormInstance, Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import { useUserStore } from '@/store';
  import { addRole, getRoleById } from '../api';
  import { initRoleForm } from '../utils';
  import BindBtns from './bind-btns.vue';
  import BindUsers from './bind-users.vue';
  import BindRequests from './bind-request.vue';

  const { t } = useI18n();
  const userStore = useUserStore();

  const userAdmin = computed(() => {
    return userStore.admin === 0;
  });

  const activeTab = ref('basic');
  const formReady = ref(false);

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    selectId: {
      type: String,
      default: '',
    },
    currentTitle: {
      type: String,
      default: '',
    },
  });

  const formData = ref<Role.Api.RoleDto>(initRoleForm());
  const { selectId, currentTitle } = toRefs(props);

  const isView = computed(() => currentTitle.value === 'view');

  if (selectId.value) {
    getRoleById(selectId.value).then((res) => {
      const { data } = res;
      formData.value = data;
      formReady.value = true;
    });
  } else {
    formReady.value = true;
  }

  const emit = defineEmits(['update:visible', 'refresh']);
  // 绑定form的ref
  const roleRef = ref<FormInstance>();
  const bindBtnsRef = ref<DefineComponent>();
  const bindUsersRef = ref<DefineComponent>();
  const bindRequestsRef = ref<DefineComponent>();

  const handleBeforeOk = async () => {
    if (props.currentTitle === 'view') {
      emit('update:visible', false);
    } else {
      const validateRes = await roleRef.value?.validate();
      if (!validateRes) {
        const data = {
          id: formData.value.id || '',
          code: formData.value.code,
          name: formData.value.name,
          isGeneral: formData.value.isGeneral,
          buttonIdList: [],
          requestIdList: [],
          userIdList: [],
          requestList: [],
        };
        data.buttonIdList = bindBtnsRef.value?.getBtnIds();
        data.userIdList = bindUsersRef.value?.getUserIds();
        data.requestIdList = bindRequestsRef.value?.getRequestIds();

        if (props.currentTitle === 'add') {
          const res = await addRole(data);
          if (res) {
            Message.success('角色添加成功！');
          }
        } else if (props.currentTitle === 'edit') {
          const res = await addRole(data);
          if (res) {
            Message.success('角色编辑成功！');
          }
        }
        emit('update:visible', false);
        emit('refresh');
      } else {
        activeTab.value = 'basic';
      }
    }
  };
  const handleCancel = () => {
    emit('update:visible', false);
  };
</script>
<style lang="less" scoped>
  .detailTitle {
    display: flex;
    padding-bottom: 16px;
    font-size: 16px;
    align-items: center;
    color: #1d2129;
    .titleContent {
      margin-left: 6px;
    }
  }
  .tab-pane {
    padding: 0 10px;
  }
  :deep(.arco-tabs-content) {
    padding-top: 20px;
  }
</style>
