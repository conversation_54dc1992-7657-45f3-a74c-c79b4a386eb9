import i18n from '@/locale/index';
import { getCode, getWpsAppCodeProject } from '@/api/wps';
import qs from 'query-string';
import useUserStore from '@/store/modules/user';

const { t } = i18n.global;

// wps预览/编辑（record:文件数据 linkType:类型<edit编辑 preview预览> modifier:针对分享增加的固定参数（可选））
export async function wpsViewHandle(
  record: any,
  linkType: string,
  modifier?: string
) {
  const userStore = useUserStore();
  const param = {
    redirectUri: window.origin,
  };
  try {
    const { data } = await getCode(param);
    if (!data?.code) return;
    const fileSource = (await getWpsAppCodeProject({})).data;

    const paramNew = {
      code: data?.code,
      fileSource,
      linkType,
      modifier: userStore?.username || modifier,
      fileId: record?.fileId || record?.id,
      name: record?.name,
    };

    const queryParams = qs.stringify(paramNew);
    const url = `https://cdex.ccccltd.cn:8000/wpsView?${queryParams}`;
    window.open(url);
  } catch (error) {
    console.log(error);
  }
}

// wps转换
export async function wpsConvertHandle(record: any) {
  const type = record.name.split('.')[record.name.split('.').length - 1];
  // store.setConvertModal(true, record, type);
}
