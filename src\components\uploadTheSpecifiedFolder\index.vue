<template>
  <div class="page">
    <!-- <a-button type="primary" class="up-btn" @click="showModal"
      >上传文件</a-button
    > -->
    <a-modal
      :visible="visible"
      title="上传附件"
      centered
      width="420px"
      draggable
      unmount-on-close
      @cancel="handleCancel"
    >
      <!-- :accept="acceptFileType" -->
      <chunk-upload
        ref="uploadRef"
        :value="fileGroup"
        is-multiple
        :disabled="loading"
        :accept="acceptFileType"
        :engine-percentages="enginePercentages"
        @file-change="handleFileChange"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
        @line-change="handleLineChange"
      >
        <template #default>
          <a-upload
            ref="uploadRef"
            :file-list="fileArr"
            multiple
            draggable
            :auto-upload="false"
            :custom-request="customRequest"
            :before-upload="beforeUpload"
            :show-file-list="false"
            @change="onChange"
          >
            <template #upload-button>
              <div class="arco-upload-wrapper arco-upload-wrapper-type-picture">
                <span class="arco-upload arco-upload-draggable">
                  <div class="arco-upload-drag">
                    <svg
                      viewBox="0 0 48 48"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      stroke="currentColor"
                      class="arco-icon arco-icon-plus"
                      stroke-width="4"
                      stroke-linecap="butt"
                      stroke-linejoin="miter"
                    >
                      <path d="M5 24h38M24 5v38"></path>
                    </svg>
                    <div class="arco-upload-drag-text"></div>
                  </div>
                </span>
              </div>
            </template>
          </a-upload>
        </template>
      </chunk-upload>

      <template #footer>
        <div class="footer">
          <span>共{{ fileArr.length }}个文件</span>
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button
              id="picker"
              type="primary"
              :disabled="fileArr.length === 0"
              :loading="loading"
              @click="handleSubmit"
              >确定</a-button
            >
          </a-space>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
  import ChunkUpload from './components/chunk-upload.vue';
  import { ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { storeToRefs } from 'pinia';
  import acceptFileType from '@/config/accept-file-types.json';
  import useUploadFileStore from '@/store/modules/upload-file/index';
  import { useDebounceFn } from '@vueuse/core';
  import { useI18n } from 'vue-i18n';

  const emits = defineEmits([
    'uploadComplete',
    'selectComplete',
    'uploadSingleSuccess',
  ]);

  const { t } = useI18n();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
      required: true,
    },
    config: {
      type: Object,
      required: true,
      default() {
        return {
          url: '',
        };
      },
    },
  });

  const uploadFileStore = useUploadFileStore();

  const selectFolderObj = ref({});

  const { fileArr, loading, uploadFileList } = storeToRefs(uploadFileStore);

  const uploadRef = ref();
  function handleCancel() {
    fileArr.value = [];
    emits('uploadComplete');
  }

  const uploadSuccessCb = (files) => {
    emits('uploadSingleSuccess', files);
  };
  const fileList = ref(null);
  const handleFileUploadFn = (newFileList) => {
    // 需要在这里做一下文件类型的校验
    const validFiles = [];
    const invalidExts = [];
    newFileList.forEach((file) => {
      const ext = file.name.split('.').pop().toLowerCase();
      if (acceptFileType.includes(ext)) {
        validFiles.push(file);
      }
      if (!acceptFileType.includes(ext)) {
        if (!invalidExts.includes(ext)) {
          invalidExts.push(ext);
        }
      }
    });
    if (invalidExts.length > 0) {
      Message.error(`不能上传${invalidExts.join('、')}文件`);
    }
    fileList.value = validFiles.map((files) => {
      const uploadFile = {
        file: files.file,
        name: files.name,
        percent: 0,
        status: 'init',
        uid: `${Date.now().toString()}-${files.name}`,
        model: 'knowledgeBase',
        uploadSuccessCb,
      };
      return uploadFile;
    });
    fileArr.value = fileList.value;
  };
  const debounceHandleFileUploadFn = useDebounceFn((newFileList) => {
    handleFileUploadFn(newFileList);
  }, 100);
  const onChange = (newFileList) => {
    console.log('[ newFileList ] >', newFileList);
    debounceHandleFileUploadFn(newFileList);
  };

  const handleSubmit = () => {
    uploadFileStore.handleUploadFile(fileList.value);
  };

  // 触发上传完成事件
  watch(loading, (val, oldVal) => {
    if (!val && oldVal) {
      if (uploadFileList.value.length === 0) {
        fileArr.value = [];
        emits('uploadComplete');
      }
    }
  });

  /** 每当对话框打开以后清空 hasTokenFile 文件数据 */
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        fileArr.value = [];
      }
    }
  );
</script>

<style lang="less" scoped>
  /* 覆盖文件拖拽上传默认样式 */
  :deep(.arco-upload-drag) {
    padding: 40px 0;
    background-color: var(--color-fill-2);
    border-radius: var(--border-radius-medium);
    .arco-icon-plus {
      margin-bottom: 0;
    }
  }

  /* 拖动文件到拖动框内样式 */
  :deep(.webuploader-dnd-over) {
    .arco-upload-drag {
      border: 1px dashed rgb(var(--primary-6));
      svg {
        color: rgb(var(--primary-6));
      }
    }
  }

  /* 拖动框hover样式 */
  :deep(.webuploader-pick-hover) {
    .arco-upload-drag {
      border: 1px dashed var(--color-neutral-4);
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      color: var(--color-text-2);
    }

    /* button {
      border-radius: var(--border-radius-medium);
    } */
  }
  .storage-position {
    margin-top: 8px;
    .title {
      font-size: 16px;
    }
    ul {
      list-style-type: none;
      display: flex;
      padding: 0 0 0 10px;
      justify-content: space-between;
      align-items: center;
      background-color: rgb(242, 243, 245);
      height: 40px;
      li {
        cursor: pointer;
      }
    }
  }
</style>
