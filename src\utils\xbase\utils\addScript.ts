import config from '@/config/xbase-config.json';

/**
 * 动态加载js资源
 * @param jssdk js引用路径
 * @returns Promise
 */
const addJssdkScript = (jssdk: string) => {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    const head = document.head || document.getElementsByTagName('head')[0];
    script.type = 'text/javascript';
    script.src = jssdk;
    head.insertBefore(script, head.firstChild);
    script.onload = () => {
      resolve(true);
    };
    script.onerror = () => {
      reject();
    };
  });
};

/**
 * 动态加载js资源
 * @param jssdk js引用路径
 * @returns Promise
 */
const addComponentTreeScript = (jssdk: string) => {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    const head = document.head || document.getElementsByTagName('head')[0];
    script.type = 'text/javascript';
    script.src = jssdk;
    head.insertBefore(script, head.firstChild);
    script.onload = () => {
      resolve(true);
    };
    script.onerror = () => {
      reject();
    };
  });
};

/**
 * 载入DX资源
 */
const initDXY = async () => {
  await addJssdkScript(`${window.origin}${config.dx3dJs}`);
  await addComponentTreeScript(`${window.origin}${config.treeFile}`);

  // await addJssdkScript(`${config.url}${config.dxSceneEditorJs}`);
};

/**
 * 载入DX_构件树资源
 */
export const initComponentTree = async () => {
  await addComponentTreeScript(`${window.origin}${config.treeFile}`);
};

export default initDXY;
