import axios from 'axios';

// 日程创建
export function createSchedule(data: any) {
  return axios.post('/cde-work/schedule/panel/create', data);
}

// 查询日程
export function getPanelList(params: any) {
  return axios.get('/cde-work/schedule/panel/list', {
    params,
  });
}

// 编辑日历
export function editSchedule(data: any) {
  return axios.post('/cde-work/schedule/panel/edit', data);
}

// 日历删除
export function deleteSchedule(params: any) {
  return axios.get('/cde-work/schedule/panel/remove', {
    params,
  });
}

// 日程删除
export function scheduleRemove(id: any) {
  return axios.post(`/cde-work/schedule/remove?scheduleDetailId=${id}`);
}

// // 关注取消日程
export function setScheduleFollow(data: any) {
  return axios.post(
    `/cde-work/schedule/follow?scheduleDetailId=${data.scheduleDetailId}`,
    data
  );
}

// 获取关注日程列表数据
// export function getScheduleFollowList(params: any) {
//   return axios.post('/cde-work/schedule/follow/list', {
//     params,
//   });
// }
export function getScheduleFollowList(data: any) {
  return axios.post('/cde-work/schedule/follow/list', data);
}

// 查询个人日程日历数据
export function getScheduleCalendar(data: any) {
  return axios.post('/cde-work/schedule/calendar', data);
}
// 查询项目日程日历数据
export function getProjectSchedule(data: any) {
  return axios.post('/cde-work/schedule/project/calendar', data);
}

// 获取人员数据
export function getOrgUser(params: any) {
  return axios.get('/sys-user/org/users', {
    params,
  });
}

// 获取部门树数据
export function getOrgTree(params: any) {
  return axios.get('/sys-user/orgs/tree', {
    params,
  });
}

// 获取部门数据
export function getDepartmentList(params: any) {
  return axios.get('/sys-user/orgs/child', {
    params,
  });
}

// 获取团队数据
export function getTeamList(data: any) {
  return axios.post('/sys-user/team/list', data);
}
// 通过团队获取人员数据
export function getTeamMember(data: any) {
  return axios.post('/sys-user/team/member/page', data);
}

//  删除事项、会议
export function removeschedule(data: any) {
  return axios.post(
    `/cde-work/schedule/remove?rmOption=${data.rmOption}&scheduleDetailId=${data.scheduleDetailId}`
  );
}

// 获取个人日程接口
export function getPersonalSchedule(data: any) {
  return axios.post('/cde-work/schedule/personal/list', data);
}

// 设置会议事项状态
export function setscheduleStatus(param: any) {
  return axios.put(
    `/cde-work/agenda/${param.scheduleDetailId}/status/${param.status}/type/${param.type}`
  );
}

// 删除事项标识(是否管理会议是否有子事项)
export function deleteMatterFlag(params: { scheduleDetailId: any }) {
  return axios.get(`/cde-work/agenda/removeFlag`, { params });
}
// 进入到项目日历，获取该项目的项目日历
export function getProjectPanel(params: any) {
  return axios.get('/cde-collaboration/schedule/project/panel', { params });
}
