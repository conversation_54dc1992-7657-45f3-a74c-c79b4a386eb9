import { defineConfig, loadEnv } from '@rsbuild/core';
import { pluginVue } from '@rsbuild/plugin-vue';
import { pluginVueJsx } from '@rsbuild/plugin-vue-jsx';
import { pluginLess } from '@rsbuild/plugin-less';
import { resolve } from 'path';
// @ts-ignore
import { pluginSvg } from 'rsbuild-plugin-svg';
import { pluginBabel } from '@rsbuild/plugin-babel';

const { publicVars } = loadEnv({ prefixes: ['VITE_'] });
// @ts-ignore
// @ts-ignore
export default defineConfig({
  plugins: [
    pluginBabel({
      include: /\.(?:jsx|tsx)$/,
    }),
    pluginVue(),
    pluginVueJsx(),
    pluginLess(),
    pluginSvg(),
  ],
  html: {
    template: './index.html',
  },
  source: {
    define: publicVars,
    entry: {
      index: './src/main.ts',
    },
  },
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, './src'),
      },
      {
        find: 'assets',
        replacement: resolve(__dirname, './src/assets'),
      },
      {
        find: 'vue-i18n',
        replacement: 'vue-i18n/dist/vue-i18n.cjs.js', // Resolve the i18n warning issue
      },
      {
        find: 'vue',
        replacement: 'vue/dist/vue.esm-bundler.js', // compile template
      },
    ],
    extensions: ['.vue', '.ts', '.tsx', '.js'],
  },
  server: {
    base: '/work',
    publicDir: './public',
    host: '0.0.0.0',
    port: 8080,
    open: true,
    fs: {
      strict: true,
    },
    proxy: {
      '/work/api': {
        // target: 'http://************:88',
        target: 'http://**************:11312', // 天翼云后台开发环境
        // target: 'http://**************:18200', // 天翼云后台测试环境
        // target: 'https://cdex.ccccltd.cn/api', // 天翼云后台生产环境
        // target: 'https://bim.ccccltd.cn:10001/api',
        changeOrigin: true,
        pathRewrite: { '^/work/api': '' },
      },
      '/api': {
        // target: 'http://************:88',
        target: 'http://**************:11312', // 天翼云后台开发环境
        // target: 'http://**************:18200', // 天翼云后台测试环境
        // target: 'https://cdex.ccccltd.cn/api', // 天翼云后台生产环境
        // target: 'https://bim.ccccltd.cn:10001/api',
        changeOrigin: true,
        pathRewrite: { '^/api': '' },
      },
      '/aiapi': {
        target: 'https://c4ai.ccccltd.cn',
        changeOrigin: true,
        pathRewrite: { '^/aiapi': '' },
      },
      '/bimapi': {
        // target: 'http://************:88',
        target: 'http://**************:11312', // 天翼云后台开发环境
        // target: 'http://**************:18200', // 天翼云后台测试环境
        // target: 'https://cdex.ccccltd.cn/api', // 天翼云后台生产环境
        changeOrigin: true,
        pathRewrite: { '^/bimapi': '' },
      },
      '/bim_base_api': {
        target: 'https://bim.ccccltd.cn:10011',
        changeOrigin: true,
        pathRewrite: { '^/bim_base_api': '' },
      },
      '/x_base_api/streaming': {
        // target: 'https://xbasecccc.dxbim.com:18899',
        // target: 'http://10.212.34.69:18899/',
        // target: 'http://203.176.95.134:18899/', // 大象云接口生产地址
        target: 'wss://xbasecccc.daxiangyun.cn:18866/', // 大象云接口开发、测试地址
        changeOrigin: true,
        ws: true,
        pathRewrite: { '^/x_base_api': '' },
      },
      '/x_base_api': {
        // target: 'https://xbasecccc.dxbim.com:18899',
        // target: 'http://10.212.34.69:18899/',
        // target: 'http://203.176.95.134:18899/', // 大象云接口生产地址
        target: 'https://xbasecccc.daxiangyun.cn:18866/', // 大象云接口开发、测试地址
        changeOrigin: true,
        pathRewrite: { '^/x_base_api': '' },
      },
      '/xbase-staitic': {
        // target: 'https://xbasecccc.dxbim.com:18899',
        // target: 'http://10.212.34.69:18899/',
        // target: 'http://203.176.95.134:18899/', // 大象云接口生产地址
        target: 'https://xbasecccc.daxiangyun.cn:18866/', // 大象云接口开发、测试地址
        changeOrigin: true,
        pathRewrite: { '^/xbase-staitic': '' },
      },
      '/ws': {
        target: 'http://************:88',
        ws: true,
        changeOrigin: true,
        pathRewrite: { '^/ws': '' },
      },
      '/wps': {
        target: 'http://203.176.95.182:11312',
        ws: true,
        changeOrigin: true,
        pathRewrite: { '^/wps': '' },
      },
      '/chbim': {
        target: 'https://chbim.ccshcc.cn',
        changeOrigin: true,
        pathRewrite: { '^/chbim': '' },
      },
    },
  },
});
