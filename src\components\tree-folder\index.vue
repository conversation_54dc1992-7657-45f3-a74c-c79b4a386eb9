<template>
  <a-modal
    ref="modal"
    :key="moveModalKey"
    v-model:visible="dialogShow"
    unmount-on-close
    :mask-closable="false"
    :esc-to-close="false"
    :on-before-ok="() => okFunction(getResult, getTeamId)"
    :mask="!isCalculate"
    :style="'opacity: ' + (isCalculate ? '0' : '1')"
    draggable
    @cancel="() => (dialogShow = false)"
  >
    <template #title> </template>
    <a-spin style="width: 100%" :loading="loading">
      <div class="tree-box">
        <a-tree
          ref="tree"
          v-model:checked-keys="checkedKeys"
          v-model:expanded-keys="expendedKeys"
          :checkable="true"
          :check-strictly="checkType === 'single'"
          :data="treeData"
          :loading="loading"
          @check="checkNode"
        >
          <template #icon="{ node }">
            <file-image
              :file-name="node.name"
              :is-file="node.isFileOrFolder === 0 ? false : true"
              style="margin-right: 8px"
            />
            <icon-minus-circle v-if="node.abandon === 1" />
          </template>
          <template #title="nodeData">
            <span>{{ i18FolderName(nodeData) }}</span>
          </template>
        </a-tree>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import {
    ref,
    defineProps,
    computed,
    onMounted,
    watch,
    PropType,
    inject,
  } from 'vue';
  import {
    getChildFolderList,
    FileAndFolderNodeMessage,
    FolderMessage,
    getChildrenAll,
  } from '@/api/tree-folder';
  // import usePrjPermissionStore from '@/store/modules/project-permission';
  import { cloneDeep } from 'lodash';
  import { useRoute } from 'vue-router';
  import useLoading from '@/hooks/loading';
  import treeDefault from './json/tree-default.json';
  import FileImage from '@/components/uploadTheSpecifiedFolder/components/image-file.vue';
  import { AxiosResponseCustom, Paging } from '@/types/global';
  import useUserStore from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import usePrjPermissionStore from '@/store/modules/project-permission';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';

  const props = defineProps({
    /**
     * 标题。default：''
     */
    title: {
      type: String,
      default: '',
    },
    /**
     * 回调函数
     */
    okFunction: {
      type: Function as () => any,
      required: true,
    },
    /**
     * 是否显示。
     */
    visible: {
      type: Boolean,
      required: true,
    },
    /**
     * 初始化时选中的id。
     */
    checkedData: {
      type: Array,
      default: () => [],
    },
    /**
     * 需要展示的文件类型：'normalFile', 'modelFile'。default：['normalFile', 'modelFile']。
     */
    showType: {
      type: Array as PropType<('normalFile' | 'modelFile')[]>,
      default: () => ['normalFile', 'modelFile'],
    },
    /**
     * 需要展示的系统文件：1：回收站 2：消费包 3：合模 4：碰撞检查 5：共享空间。default：[]。
     */
    showSysFolder: {
      type: Array as PropType<(1 | 2 | 3 | 4 | 5)[]>,
      default: () => [5],
    },
    /**
     * 需要展示的模块：'wip', 'shared', 'published'。default：['wip']。
     */
    showModule: {
      type: Array as PropType<('wip' | 'shared' | 'published')[]>,
      default: () => ['wip'],
    },
    /**
     * 禁止选中的层级。
     */
    disCheckHierarchy: {
      type: Array as PropType<number[]>,
      default: () => [],
    },
    /**
     * 禁止选择的id。
     */
    disCheckIds: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    /**
     * 选择模式：'single'：单选，'multiple'：多选。default: 'multiple'。
     */
    checkType: {
      type: String as PropType<'single' | 'multiple'>,
      default: 'multiple',
    },
    /**
     * 要显示的团队。
     */
    teamIds: {
      type: Array as PropType<string[]>,
      required: false,
    },
    /**
     * 是否用于计算。default：false。
     */
    isCalculate: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否每次显示都清除原来的key。default：false。
     */
    isClearKey: {
      type: Boolean,
      default: false,
    },
    /**
     * 输出类型：'node'：节点，'id'：id。default：'node'。
     */
    outputType: {
      type: String as PropType<'node' | 'id'>,
      default: 'node',
    },
  });

  /** 大象云-上传文件格式集合 */
  const modelall = [
    'rfa',
    'skp',
    'obj',
    'stl',
    'glb',
    'fbx',
    'gltf',
    'stp',
    'step',
    'x_t',
    '3ds',
    'dae',
    'nwd',
    'rvt',
    'ifc',
    'dgn',
    'dwg',
    'p3d',
    'pdf',
    'doc',
    'docx',
    'xlsx',
    'pptx',
    'wps',
    'csv',
    'png',
    'jpeg',
    'jpg',
  ];

  const { i18FolderName } = useI18nHandleName();
  const { loading, setLoading } = useLoading(false);
  // const projectStore = usePrjPermissionStore();
  const route = useRoute();
  const moveModalKey = ref(0);
  const checkedKeys = ref<(string | number)[]>([]);
  const expendedKeys = ref<(string | number)[]>(props.showModule);
  const emit = defineEmits(['update:visible']);
  const tree = ref();
  const { projectId }: any = route.params;
  const treeData = ref<FileAndFolderNodeMessage[]>(cloneDeep(treeDefault));

  const prjStore = usePrjPermissionStore();

  const { teamList: designTeamlist } = storeToRefs(prjStore);

  // treeData根据showModule过滤不需要的数据
  treeData.value = treeData.value.filter((item) =>
    props.showModule.includes(item.key as 'wip' | 'shared' | 'published')
  );

  // 弹窗开关
  const dialogShow = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val),
  });

  // 记录上一次选择结果
  let oldCheckKeys: (string | number)[] = [];

  /**
   * 点击checkbox触发事件，控制单选
   * @param checkeds
   */
  const checkNode = (checkeds: (string | number)[]) => {
    if (props.checkType === 'single' && checkeds.length > 1) {
      const key = checkeds[1];
      checkedKeys.value = [key];
    }
  };

  /**
   * 格式化getChildrenAll方法获得的数据
   * @param fileAndFolderList [data]
   * @param 当前层级
   */
  const formatFileAndFolder = (fileAndFolderList: any[], hierarchy = 3) => {
    fileAndFolderList.forEach((item) => {
      // 过滤——需要哪些系统文件夹
      item.folderAllDtoList = item.folderAllDtoList?.filter(
        (itm: any) => !itm.sysType || props.showSysFolder.includes(itm.sysType)
      );
      if (item.folderAllDtoList?.length > 0) {
        item.folderAllDtoList = formatFileAndFolder(
          item.folderAllDtoList,
          hierarchy + 1
        );
      }
      item.children = [...(item.folderAllDtoList || []), ...(item.files || [])];
      item.children.forEach((itm: any) => {
        // 添加通用属性
        if ('fileId' in itm) {
          itm.isFileOrFolder = 1;
          itm.id = itm.fileId;
          itm.key = `file-${itm.id}`;
        } else {
          itm.isFileOrFolder = 0;
          itm.key = `folder-${itm.id}`;
        }
        itm.title = itm.name;
        // 添加不可选择属性
        if (
          props.disCheckHierarchy.includes(hierarchy) ||
          props.disCheckIds.includes(item.id as string) ||
          (itm.isFileOrFolder === 0 &&
            props.showType.length === 1 &&
            props.checkType === 'single')
        ) {
          itm.disableCheckbox = true;
        }
      });
      // 过滤——需要哪些children
      const normalFile = props.showType.includes('normalFile');
      const modelFile = props.showType.includes('modelFile');
      item.children = item.children.filter((itm: any) => {
        // 过滤——需要是否需要文件夹
        if (itm.isFileOrFolder === 0) {
          return true;
        }
        // 过滤——需要是否需要普通类型/模型文件
        let result = true;
        const suffix = itm.name.split('.').pop();
        if (normalFile && !modelFile) {
          result = !modelall.includes(suffix);
        } else if (!normalFile && modelFile) {
          result = modelall.includes(suffix);
        } else if (!normalFile && !modelFile) {
          result = false;
        }
        return result;
      });
    });
    return fileAndFolderList;
  };

  /**
   * 获取team文件夹下的所有文件夹和文件
   * @param teamFolder team文件夹信息
   */
  const getTeamAllFileAndFolder = (
    teamFolder: FileAndFolderNodeMessage
  ): Promise<null> => {
    return new Promise((resolve) => {
      getChildrenAll(teamFolder.id as string).then(
        (res: AxiosResponseCustom<any>) => {
          const treeDataList = formatFileAndFolder([res.data]);
          teamFolder.children = treeDataList[0]?.children;
          resolve(null);
        }
      );
    });
  };

  /**
   * 获取wip/shared/published目录下的team文件夹
   * @param folder wip/shared/published文件夹信息
   */
  const getAllTeamFolder = (folder: any): Promise<null> => {
    console.log(designTeamlist.value, 44444);
    return new Promise((resolve) => {
      const teamList =
        (props.teamIds
          ? props.teamIds
          : designTeamlist.value.map((item) => item.id)) || [];
      // 获取所有team文件夹
      getChildFolderList(projectId, undefined, folder.type, '0').then(
        (res: AxiosResponseCustom<Paging<FolderMessage>>) => {
          let { list } = res.data;
          console.log('[ list ] >', list);
          // 过滤没有权限的team文件夹
          if (folder.key === 'wip' || folder.key === 'shared') {
            list = list.filter((item: FolderMessage) => {
              return teamList.includes(item.teamId as string);
            });
          }
          // 获取team文件夹下的所有文件/文件夹
          const teamsPromiseList: Promise<null>[] = [];
          list.forEach((item: FileAndFolderNodeMessage) => {
            item.isFileOrFolder = 0;
            item.title = item.name;
            item.key = `folder-${item.id}`;
            if (
              props.disCheckHierarchy.includes(2) ||
              props.disCheckIds.includes(item.id as string) ||
              (props.showType.length === 1 && props.checkType === 'single')
            ) {
              item.disableCheckbox = true;
            }
            teamsPromiseList.push(getTeamAllFileAndFolder(item));
          });
          Promise.all(teamsPromiseList).then(() => {
            folder.children = list;
            resolve(null);
          });
        }
      );
    });
  };

  /**
   * 过滤选中的文件和文件夹
   * @param nodeList
   * @param checkedNodeKeys
   * @param halfCheckedNodeKeys
   */
  const nodeFilter = (
    nodeList: any[],
    checkedNodeKeys: (string | number | undefined)[],
    halfCheckedNodeKeys: (string | number | undefined)[]
  ) => {
    const result: any[] = [];
    nodeList.forEach((item) => {
      if (checkedNodeKeys.includes(item.key)) {
        result.push(item);
      } else if (halfCheckedNodeKeys.includes(item.key)) {
        item.children = nodeFilter(
          item.children,
          checkedNodeKeys,
          halfCheckedNodeKeys
        );
        result.push(item);
      }
    });
    return result;
  };

  /**
   * 递归查找树中的节点
   */
  const getFileInTree: any = (
    key: string,
    treeDataList: FileAndFolderNodeMessage[]
  ) => {
    let result = null;
    for (let i = 0; i < treeDataList.length; i++) {
      if (treeDataList[i].key === key) {
        result = treeDataList[i];
        break;
      } else if (treeDataList[i].children) {
        result = getFileInTree(
          key,
          treeDataList[i].children as FileAndFolderNodeMessage[]
        );
        if (result) break;
      }
    }
    return result;
  };

  const init = async () => {
    treeData.value.forEach((item) => {
      if (
        props.disCheckHierarchy.includes(1) ||
        props.disCheckIds.includes(item.id as string) ||
        (props.showType.length === 1 && props.checkType === 'single')
      ) {
        item.disableCheckbox = true;
      }
    });
    const promiseList: Promise<null>[] = [];
    treeData.value.forEach((item) => {
      promiseList.push(getAllTeamFolder(item));
    });

    await Promise.all(promiseList);
  };

  const modal = ref();

  onMounted(async () => {
    if (props.isCalculate) {
      await init();
      await modal.value.onBeforeOk();
    }
  });

  const setCheckedData = (data: any[]) => {
    const keys: (string | number)[] = [];
    data.forEach((item: any) => {
      const idList = item.split('-');
      keys.push(`file-${idList.pop()}`);
    });
    oldCheckKeys = keys;
    checkedKeys.value = keys;
  };

  const markAbandonedFiles = (data: FileAndFolderNodeMessage[]) => {
    data.forEach((item: any) => {
      if (item.children && item.children.length > 0) {
        markAbandonedFiles(item.children);
      }
      if (item.files && item.files.length > 0) {
        item.files.forEach((file: any) => {
          if (file.abandon === 1) {
            // item.disabled = true;
            file.disableCheckbox = true;
          }
        });
      }
    });
    return data;
  };

  watch(
    () => props.visible,
    async (newValue) => {
      if (!newValue || props.isCalculate) return;
      if (props.checkedData.length > 0) {
        setCheckedData(props.checkedData);
      } else if (props.isClearKey) {
        checkedKeys.value = [];
      } else {
        checkedKeys.value = oldCheckKeys;
      }
      setLoading(true);
      await init();
      setLoading(false);

      treeData.value = markAbandonedFiles(treeData.value);
    }
  );

  // checkedData改变时回显
  watch(
    () => props.checkedData,
    (newValue) => {
      if (props.isCalculate) {
        setCheckedData(newValue);
      }
    },
    { immediate: true }
  );

  /**
   * 过滤空文件夹
   * @param treeList
   */
  const emptyFolderFilter = (treeList: FileAndFolderNodeMessage[]) => {
    const result: FileAndFolderNodeMessage[] = [];
    treeList.forEach((item) => {
      if (item.isFileOrFolder === 1) {
        result.push(item);
      } else {
        const childrenResult = emptyFolderFilter(item.children || []);
        if (childrenResult.length !== 0) {
          item.children = childrenResult;
          result.push(item);
        }
      }
    });
    return result;
  };

  const specifiedTeamId = ref<string | undefined>('');
  const getResult = async () => {
    let result = null;
    const checkedNodes: FileAndFolderNodeMessage[] =
      tree.value.getCheckedNodes();
    const halfCheckedNodes: FileAndFolderNodeMessage[] =
      tree.value.getHalfCheckedNodes();
    const checkedNodeKeys: string[] = checkedNodes.map((item) => `${item.key}`);
    const halfCheckedNodeKeys: string[] = halfCheckedNodes.map(
      (item) => `${item.key}`
    );
    if (!checkedNodeKeys.length) return null;
    if (props.outputType === 'node') {
      // 返回node
      if (props.checkType === 'single') {
        // 单选
        result = getFileInTree(checkedNodeKeys[0], treeData.value);
      } else {
        // 多选
        const treeDataClone = cloneDeep(treeData.value);
        const deepResult = nodeFilter(
          treeDataClone,
          checkedNodeKeys,
          halfCheckedNodeKeys
        );

        result = emptyFolderFilter(deepResult);
      }
    } else if (props.outputType === 'id') {
      // 返回id
      if (props.checkType === 'single') {
        // 单选
        result = checkedNodeKeys[0]?.split('-')[1];
      } else {
        // 多选
        result = 'TODO';
      }
    }
    oldCheckKeys = cloneDeep(checkedKeys.value);

    return result;
  };

  const getTeamId = () => {
    const checkedNodes: FileAndFolderNodeMessage[] =
      tree.value.getCheckedNodes();
    specifiedTeamId.value =
      checkedNodes.length > 0 ? checkedNodes[0].teamId : '';
    return specifiedTeamId.value;
  };
</script>

<style lang="less" scoped>
  .tree-box {
    max-height: 60vh;
    overflow: auto;

    ::v-deep .arco-tree-node-custom-icon {
      position: relative;

      .arco-icon {
        position: absolute;
        left: -8px;
        bottom: -6px;
        color: red;
      }
    }
  }
</style>
