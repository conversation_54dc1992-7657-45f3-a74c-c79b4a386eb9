* {
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  background-color: var(--color-bg-1);
  overflow: hidden;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 8px;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.2);
  border-radius: 5px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #d9d9d9;
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.2);
}

.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    width: 164px;
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    margin: 0;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}

.general-card {
  border-radius: 4px;
  border: none;
  & > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }
  & > .arco-card-body {
    padding: 0 20px 20px 20px;
  }
}

.split-line {
  border-color: rgb(var(--gray-2));
}

.arco-table-cell {
  .circle {
    display: inline-block;
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgb(var(--blue-6));
    &.pass {
      background-color: rgb(var(--green-6));
    }
  }
}

// 统一修改中等按钮圆角
#app {
  .arco-btn-size-medium {
    border-radius: var(--border-radius-medium);
  }

  .arco-input-wrapper {
    border-radius: var(--border-radius-medium);
  }

  .arco-select-view-single {
    border-radius: var(--border-radius-medium);
  }
}

#v3d-viewer {
  background-color: white;
  border-radius: var(--border-radius-medium);
}
.obv-viewer .obv-button .obv-button-icon {
  color: #666;
}
.obv-viewer .obv-button.active > .obv-button-icon {
  color: #57a9fb !important;
}

.obv-viewer .obv-panel {
  right: 0 !important;
  background-color: #f2f3f5 !important;
}

/** CSS全局修改浏览器自动填充 input 默认样式 */
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s; /* 延长过渡时间以防止颜色闪变 */
}
