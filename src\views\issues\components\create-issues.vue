<template>
  <a-modal
    :visible="visible"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="cancel"
  >
    <template #title> {{ $t('issues.create-issues') }} </template>
    <template #footer>
      <a-button @click="cancel">{{ $t('issues.cancel') }}</a-button>
      <a-button type="primary" @click="issueSubmit">{{
        $t('issues.create')
      }}</a-button>
    </template>
    <div class="content">
      <a-form ref="issueFormRef" :model="issueForm">
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item
              field="title"
              :label="$t('issues.title')"
              :rules="[
                { required: true, message: $t('issues.please-enter-title') },
              ]"
              :validate-trigger="['change', 'input']"
            >
              <remove-spaces-input
                v-model="issueForm.title"
                :placeholder="$t('issues.please-enter')"
                :max-length="currentLocale === 'en-US' ? 255 : 100"
                show-word-limit
              />
              <!-- <a-input
                v-model="issueForm.title"
                :placeholder="$t('issues.please-enter')"
                :max-length="20"
                show-word-limit
              /> -->
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item
              field="receivers"
              :label="$t('issues.recipient')"
              :rules="[
                {
                  required: true,
                  message: $t('issues.please-select-recipient'),
                },
              ]"
            >
              <UserSelector
                v-model="issueForm.receivers"
                :placeholder="
                  $t('issues.please-enter-name-role-company-or-email')
                "
                :project-id="projectId"
                @change="receiversSelect"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item field="stage" :label="$t('model-viewer.issueStage')">
              <a-select
                v-model="issueForm.stage"
                :placeholder="$t('model-viewer.enterIssueContentStage')"
              >
                <a-option :value="1">{{
                  $t('model-viewer.shareBefore')
                }}</a-option>
                <a-option :value="2">{{ $t('model-viewer.share') }}</a-option>
                <a-option :value="3">{{
                  $t('model-viewer.deliveryBefore')
                }}</a-option>
                <a-option :value="4">{{
                  $t('model-viewer.delivery')
                }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item field="type" :label="$t('model-viewer.type')">
              <a-select
                v-model="issueForm.type"
                :placeholder="$t('model-viewer.enterIssueContentType')"
              >
                <a-option :value="1">{{
                  $t('model-viewer.collisionDetection')
                }}</a-option>
                <a-option :value="2">{{
                  $t('model-viewer.violateStandard')
                }}</a-option>
                <a-option :value="3">{{
                  $t('model-viewer.ownerStandard')
                }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="12">
          <a-col :span="24">
            <a-form-item field="photo" :label="$t('model-viewer.uploadImg')">
              <a-upload
                ref="uploadRef"
                v-model:file-list="issueForm.imgList"
                action="/api/sys-storage/image/upload"
                :headers="{
                  'Fusion-Auth': getToken() || '',
                  'Fusion-Biz': setFusionBiz() || '',
                }"
                :limit="1"
                accept="image/png, image/jpeg,image/jpg"
                image-preview
                list-type="picture-card"
                :show-link="true"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider />
        <a-row :gutter="12">
          <a-col :span="24">
            <div class="title">
              <div class="text">
                <img
                  src="@/assets/images/check/<EMAIL>"
                  alt=""
                  style="width: 17px; height: 17px"
                />
                <span class="text-font">{{ $t('issues.issues-file') }}</span>
              </div>
              <div class="file-count">
                <span
                  >{{ $t('issues.total') }}：
                  {{
                    $t('issues.number-file', { count: fileCounts || 0 })
                  }}</span
                >
                <a-button type="text" @click="addFileVisible = true">
                  {{ $t('issues.add-file') }}
                </a-button>
              </div>
            </div>
            <div class="file-list-wrap">
              <FileCollapse v-model:files="issueForm.fileList"></FileCollapse>
            </div>
          </a-col>
        </a-row>
        <a-form-item
          field="message"
          :label="$t('issues.message')"
          style="margin-top: 20px"
        >
          <a-textarea
            v-model="issueForm.message"
            :placeholder="$t('issues.please-enter')"
            :max-length="currentLocale === 'en-US' ? 2000 : 1000"
            show-word-limit
            :auto-size="{
              minRows: 2,
            }"
          />
        </a-form-item>
      </a-form>
      <TreeFolder
        v-model:visible="addFileVisible"
        :title="$t('issues.add-file')"
        :ok-function="fileChange"
        :checked-data="fileIdList"
        :show-sys-folder="[3, 4]"
        :is-clear-key="true"
      ></TreeFolder>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { setFusionBiz } from '@/api/interceptor';
  import { getToken } from '@/utils/auth';
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { computed, ref, reactive, watch, nextTick } from 'vue';
  import FileCollapse from '@/views/check/components/file-collapse/index.vue';
  import UserSelector from '@/components/user-selector/index.vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import TreeFolder from '@/components/tree-folder/index.vue';
  import isEmpty from 'lodash/isEmpty';
  // import { useUserStore } from '@/store';
  import { getDirectoryFileInfo, addIssue } from '../api';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  // import removeSpacesInput from '@/components/removeSpacesInput/index.vue';
  import { getUserId } from '@/utils/auth';

  const { t } = useI18n();
  // 国际化类型
  const { currentLocale } = useLocale();
  // const userStore = useUserStore();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  interface issueFormInter {
    title: string; // 标题
    receivers: string; // 收件人
    fileList: Array<object>;
    imgList: Array<any>;
    message: string; // 消息
    stage?: number; // 问题阶段
    type?: number; // 类型
  }
  const issueForm = reactive<issueFormInter>({
    title: '', // 标题
    receivers: '', // 收件人
    fileList: [],
    message: '', // 消息
    imgList: [],
  });
  const route = useRoute();
  const projectId = ref(route.params.projectId);

  const getInput = (val: any) => {
    issueForm.title = val;
  };
  const receiversID = ref<Array<string>>([]);
  const addFileVisible = ref(false);
  const fileIds = ref<Array<string>>([]);
  const issueFormRef = ref<FormInstance>();
  const emits = defineEmits(['update:visible', 'refresh']);
  const cancel = () => {
    issueFormRef.value?.resetFields();
    issueForm.fileList = [];
    issueForm.imgList = [];
    fileIds.value = [];
    emits('update:visible', false);
  };
  const fileCounts = computed(() => {
    let counts = 0;
    issueForm.fileList?.forEach((file) => {
      const { total } = getDirectoryFileInfo(file);
      counts += total;
    });
    return counts;
  });
  const issueSubmit = async (done: any) => {
    const res = await issueFormRef.value?.validate();
    let imgLists = '';
    if (issueForm.imgList.length > 0) {
      console.log(issueForm.imgList[0], 'res.data');
      imgLists = issueForm.imgList[0].response.data.fileToken;
    }
    const itemsList: { fileId: string; picToken: string }[] = [];
    if (fileIds.value && fileIds.value.length > 0) {
      fileIds.value.forEach((element) => {
        itemsList.push({
          fileId: element,
          picToken: imgLists,
        });
      });
    }
    if (!isEmpty(fileIds.value)) {
      if (!res) {
        const params = {
          projectId: projectId.value,
          title: issueForm.title,
          message: issueForm.message,
          receivers: receiversID.value,
          fileIds: fileIds.value,
          stage: issueForm.stage,
          type: issueForm.type,
          items: itemsList,
        };

        const saveRes = await addIssue(params);
        if (saveRes.status) {
          Message.success(saveRes.message);
          emits('refresh');
          cancel();
        } else {
          Message.error(saveRes.message);
        }
        done();
      }
    } else {
      Message.info(t('issues.please-select-file'));
    }
  };

  const receiversSelect = (user: any) => {
    if (user.fid === getUserId()) {
      nextTick(() => {
        issueForm.receivers = '';
      });
      Message.info(t('issues.cannot-select-yourself'));
    } else {
      receiversID.value = [user.id];
    }
    // receiversID.value = user.map((item: any) => {
    //   if (userStore.username === item.username) {
    //     Message.info(t('issues.cannot-select-yourself'));
    //     console.log(issueForm.receivers);
    //     let receiversArrayData: any = issueForm.receivers.split(',');
    //     receiversArrayData = receiversArrayData.filter((itemId) => {
    //       return itemId !== userStore.id;
    //     });
    //     receiversArrayData = receiversArrayData.join();
    //     // 在Vue 3中，nextTick是一个函数，用于延迟执行一段代码，直到浏览器完成当前的事件循环。这是Vue提供的一种机制，用于处理DOM更新，并确保在DOM更新后执行某些操作。
    //     nextTick(() => {
    //       issueForm.receivers = receiversArrayData;
    //     });
    //     console.log(issueForm.receivers);
    //   }
    //   return item.id;
    // });
  };

  const fileChange = async (data: () => Promise<any>) => {
    const files = await data();
    issueForm.fileList = files;

    issueForm.fileList?.forEach((file) => {
      const { fileIDList } = getDirectoryFileInfo(file);
      fileIds.value = fileIDList;
    });
  };

  const getIdByFileList = (fileList2: any[]) => {
    const ids: any[] = [];
    fileList2?.forEach((item) => {
      if ('folderId' in item) {
        ids.push(item.id);
      }
      if (item.children) {
        ids.push(...getIdByFileList(item.children));
      }
    });
    return ids;
  };

  const fileIdList = computed(() => {
    const result = getIdByFileList(issueForm.fileList);
    return result;
  });

  watch(
    () => props.visible,
    (val) => {
      if (!val) {
        issueForm.fileList = [];
      }
    },
    { deep: true, immediate: true }
  );
</script>

<style scoped lang="less">
  .title {
    position: relative;
    .text {
      display: flex;
      align-content: center;
      align-items: center;
    }
    .text-font {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }
    .file-count {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  .file-list-wrap {
    margin-top: 16px;
  }
</style>
