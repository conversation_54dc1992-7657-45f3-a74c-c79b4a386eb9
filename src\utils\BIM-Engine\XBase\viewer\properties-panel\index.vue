<template>
  <a-card ref="cardRef" v-draggable class="Linkage">
    <template #title>
      <strong class="title">
        {{ $t('utils.property') }}
      </strong>
    </template>
    <template #extra>
      <icon-close class="close" @click="close()" />
    </template>
    <a-collapse>
      <a-empty v-if="propertiesData.length === 0" />
      <a-collapse-item
        v-for="item in propertiesData"
        v-else
        :key="item.group_name || item.category"
        :header="item.group_name || item.category"
      >
        <a-table
          size="mini"
          :pagination="false"
          :columns="columns"
          :data="item.group_list || item.properties"
        ></a-table>
      </a-collapse-item>
    </a-collapse>
  </a-card>
</template>

<script lang="ts" setup>
  import { computed, onMounted, onUnmounted, ref, toRefs, watch } from 'vue';
  import { useRoute } from 'vue-router';
  import useModelToolsStore from '@/store/modules/model-viewer/index';
  import { useI18n } from 'vue-i18n';
  import { useDraggable } from '@vueuse/core';
  import { storeToRefs } from 'pinia';

  const { t } = useI18n();
  const store = useModelToolsStore();
  const { propertiesData } = storeToRefs(store);

  const props = defineProps({
    componentData: {
      type: Object,
      default() {
        return {};
      },
    },
  });

  const columns = computed(() => [
    {
      title: t('utils.property-name'),
      dataIndex: 'name',
      slotName: 'name',
      width: 100,
    },
    {
      title: t('utils.property-value'),
      dataIndex: 'value',
      slotName: 'value',
      width: 150,
    },
    {
      title: t('utils.unit'),
      dataIndex: 'unit',
      slotName: 'unit',
      width: 50,
    },
  ]);

  const cardRef = ref<HTMLElement | null>(null);
  // 使用 vueuse 的 useDraggable
  const { x, y } = useDraggable(cardRef, {
    initialValue: { x: 24, y: 24 }, // 初始位置
  });

  const close = () => {
    store.setPropertiesPanelShow(false);
    changeClassName('cusPlugin_click', 'cusPlugin');
  };

  // 修改class类名
  // eslint-disable-next-line @typescript-eslint/ban-types
  const changeClassName = (initial: string, replac: string, fn?: Function) => {
    const ele = document.getElementsByClassName(initial)[0];
    if (!ele) return;
    let className = ele.getAttribute('class') as string;
    if (className) {
      className = className.replace(initial, replac);
      setTimeout(() => {
        ele.setAttribute('class', className);
        if (fn) fn();
      }, 1);
    }
  };

  watch(
    () => store.propertiesPanelShow,
    (val) => {
      if (val) {
        changeClassName('cusPlugin', 'cusPlugin_click');
      } else {
        changeClassName('cusPlugin_click', 'cusPlugin');
      }
    }
  );

  onUnmounted(() => {
    close();
  });
</script>

<script lang="ts">
  export default {
    name: 'PropertiesPanel',
  };
</script>

<style scoped lang="less">
  .Linkage {
    position: absolute;
    left: 24px;
    top: 24px;
    width: 350px;
    height: 402px;
    z-index: 30;

    .title {
      display: block;
      text-align: center;
      font-size: 1rem;
      font-weight: 600;
      color: #282828;
      cursor: move;
    }

    .close {
      cursor: pointer;
      color: #282828;
    }

    :deep(.arco-empty) {
      margin-top: 100px;
    }
  }

  :deep(.arco-card-header) {
    height: 42px;
  }
  :deep(.arco-card-body) {
    padding: 0 !important;
  }
  :deep(.arco-collapse) {
    height: 358px;
    overflow: auto;
  }

  :deep(.arco-collapse-item-header-title) {
    color: #282828;
    font-size: 0.875rem;
    font-family: PingfangSC;
  }
  :deep(.arco-collapse-item-content-box) {
    padding: 0;
  }
  :deep(.arco-collapse-item-content) {
    padding-right: 0;
    padding-left: 0;
  }
</style>
