<template>
  <div
    ><div class="schedule-list">
      <div v-if="!props.data?.length" class="noData"
        ><img :src="scheduleBgImg" alt="" /><div>{{
          $t('schedule.noData')
        }}</div>
      </div>
      <div v-else class="schedule-body">
        <div
          v-for="item in props.data"
          :key="item.id"
          class="schedule-block"
          :class="{ active: item.scheduleStatus === '2' }"
        >
          <div class="arco-checkbox" @click="changeStatus(item)">
            <div class="schedule-block-left">
              <div class="schedule-block-time">
                <span>{{ dayjs(item.planStartTime).format('HH:mm') }}</span
                >-<span>{{ dayjs(item.planEndTime).format('HH:mm') }}</span>
              </div>
              <div
                :class="
                  item.type === 'meeting'
                    ? 'schedule-block-type1'
                    : 'schedule-block-type2'
                "
                class="schedule-block-type"
              >
                <span>
                  <span
                    class="scheduleIcon"
                    :class="
                      item.type === 'meeting'
                        ? 'scheduleIcon1'
                        : 'scheduleIcon2'
                    "
                  ></span>
                  {{
                    item.type === 'meeting'
                      ? $t('schedule.meeting')
                      : $t('schedule.matters')
                  }}
                </span>
              </div>
            </div>
            <div class="schedule-block-cont">
              <div class="schedule-block-title">{{ item.title }}</div>
              <div class="schedule-block-described">{{ item.content }}</div>
            </div>
          </div>
          <div class="schedule-list-action"
            ><a-dropdown class="custom-dropdown" @select="handleSelect">
              <icon-more class="icon-more" @click="handleSwitchVisible(item)" />
              <template #content>
                <a-doption @click="editHandle(item)"
                  ><a-button type="text">{{
                    $t('schedule.edit')
                  }}</a-button></a-doption
                >
                <a-doption @click="deleteHandle(item)"
                  ><a-button type="text">{{
                    $t('schedule.delete')
                  }}</a-button></a-doption
                >

                <a-doption @click="attentionHandle(item)"
                  ><a-button type="text">{{
                    item.isFollow === 0
                      ? $t('schedule.focus')
                      : $t('schedule.cancelFocus')
                  }}</a-button></a-doption
                >
              </template>
            </a-dropdown>
          </div>
        </div>
      </div>
    </div></div
  >

  <a-modal :visible="delVidible" @ok="deleteSaveHandle" @cancel="handleCancel">
    <template #title> {{ $t('schedule.delete') }} </template>
    <div>
      <a-radio-group
        v-if="recordType === 'meeting'"
        v-model="repeatedVal"
        direction="vertical"
      >
        <a-radio value="0">{{ $t('schedule.delete.current') }}</a-radio>
        <a-radio value="1">{{ $t('schedule.delete.currentAndAfter') }}</a-radio>
        <a-radio value="2">{{ $t('schedule.delete.all') }}</a-radio>
      </a-radio-group>
      <div v-else>{{
        `${deleteMatterMsg || ''} ${$t('schedule.delete.confirm')}`
      }}</div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import {
    setScheduleFollow,
    removeschedule,
    setscheduleStatus,
    deleteMatterFlag,
  } from '@/views/schedule/component/calendar/api';
  // import { removeMeeting } from '@/views/schedule/component/meeting/api';
  import { Message } from '@arco-design/web-vue';
  import { array } from 'js-md5';
  import dayjs from 'dayjs';
  import { userScheduleStore, useGlobalModeStore, useUserStore } from '@/store';
  import { useI18n } from 'vue-i18n';
  import scheduleBgImg from '@/assets/images/schedule/schedule-bg.png';

  const userStore = useUserStore();
  const userName = computed(() => userStore.username);
  const globalModeStore = useGlobalModeStore();
  const globalMode = computed(() => globalModeStore.getGlobalMode);

  const { t } = useI18n();

  const scheduleStore = userScheduleStore();

  const props = defineProps({
    data: {
      type: String,
      required: false,
    },
    scheduleId: {
      type: Array,
      required: false,
    },
    projectScheduleId: {
      type: Array,
      required: false,
    },
    currentProjectScheduleId: {
      type: Array,
      required: false,
    },
  });
  const mergedScheduleIds = computed(() => {
    // 过滤掉 undefined/null，避免报错
    const arr1 = Array.isArray(props.scheduleId) ? props.scheduleId : [];
    const arr2 = Array.isArray(props.projectScheduleId)
      ? props.projectScheduleId
      : [];
    return [...arr1, ...arr2];
  });
  const emits = defineEmits(['refresh', 'jump']);

  const delVidible = ref(false);

  const dropdownVisible = ref(false); // a-dropdown内容是否显示
  const nowRecord = ref(null); // 当前选择的数据

  // 更多按钮点击（手动出发a-dropdown的展开<因为点击删除时a-dropdown隐藏会导致删除二次确认窗也被隐藏> ）
  const handleSwitchVisible = (item) => {
    nowRecord.value = item;
    dropdownVisible.value = true;
  };

  // 编辑
  const editHandle = (item) => {
    // 保持会议事项统一id
    item.scheduleSubId = item.type === 'meeting' ? item.scheduleSubId : item.id;
    item.type = item.type === 'meeting' ? 'meeting' : 'matters';
    emits('jump', item);
  };

  // 取消删除
  const handleCancel = () => {
    delVidible.value = false;
  };

  const repeatedVal = ref('0');
  const recordId = ref('');
  const recordType = ref('');
  const deleteMatterMsg = ref('');
  const deleteHandle = async (val) => {
    if (userName.value === val.createBy) {
      if (val.type === 'item') {
        const params = {
          scheduleDetailId: val.id,
        };
        try {
          const res = await deleteMatterFlag(params);
          if (res.status) {
            const [firstError] = res.data.err || []; // 使用数组解构，并处理 err 可能为 undefined 的情况
            deleteMatterMsg.value = firstError;
            console.log('[ firstError ] >', firstError);
          }
        } catch (error) {
          console.log(error);
        }
      }
      recordId.value = val.id;
      delVidible.value = true;
      recordType.value = val.type;
    } else {
      Message.info(
        val.type === 'item'
          ? t('schedule.status.notMatterCreator')
          : t('schedule.status.notMeetingCreator')
      );
    }
  };
  // 删除确认框
  const deleteSaveHandle = async () => {
    if (!repeatedVal.value) {
      Message.info(t('schedule.delete.selectType'));
      return;
    }
    dropdownVisible.value = false;
    const param = {
      scheduleDetailId: recordId.value,
      rmOption: repeatedVal.value,
    };
    try {
      const res = await removeschedule(param);
      if (res.status) {
        Message.success(t('schedule.delete.success'));
        emits('refresh');
      }
    } catch (error) {
      console.log(error);
    } finally {
      dropdownVisible.value = false;
      delVidible.value = false;
    }
  };

  // 关注

  const attentionHandle = async (item) => {
    try {
      console.log(props.currentProjectScheduleId, 'currentProjectScheduleId');
      const param = {
        // 因为关注的日程数据分为三种，我的面板，别人分配给我的日程带着别人的面板，我的项目面板，所以只需要日程id,后端做逻辑区分
        // schedulePanelIds:
        //   globalMode.value === 'project'
        //     ? props.currentProjectScheduleId
        //     : mergedScheduleIds.value,
        scheduleDetailId: item.id,
      };
      const res = await setScheduleFollow(param);
      if (res.status) {
        Message.success(res.message);
        emits('refresh');
      }
      console.log(415151);
    } catch (error) {
      console.log(error);
    } finally {
      dropdownVisible.value = false;
      console.log(dropdownVisible.value, 4444);
    }
  };

  const setStatus = (status, type) => {
    let statusData = null;
    if (type === 'meeting') {
      statusData = Number(status) + 1;
      if (statusData === 3) statusData = 0;
    } else {
      statusData = status === '1' ? '2' : '1';
    }
    return statusData;
  };

  // 设置状态
  const changeStatus = async (record) => {
    const data = {
      id: record.type === 'meeting' ? record.scheduleSubId : record.id,
      type: record.type,
    };
    scheduleStore.setSummaryVisible(true);
    await scheduleStore.setSummaryData(data);
    // // 会议修改状态时 如果状态从未修改过（record.scheduleAutoStatus === null） 初始状态设为0
    // if (record.type === 'meeting') {
    //   record.scheduleStatus =
    //     record.scheduleAutoStatus === null ? 0 : record.scheduleStatus;
    // }
    // const param = {
    //   scheduleDetailId: record.id,
    //   status: setStatus(record.scheduleStatus, record.type),
    //   type: record.type === 'meeting' ? 2 : 1,
    // };
    // const res = await setscheduleStatus(param);
    // if (res.status) {
    //   Message.success('状态设置成功');
    //   emits('refresh');
    // }
  };
</script>

<style lang="less" scoped>
  .schedule-list {
    height: calc(100vh - 240px);
    background-color: #fff;
    border-radius: 1px solid #efefef;
    position: relative;

    .noData {
      height: 700px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      img {
        display: block;
        width: 140px;
        height: 140px;
      }
      div {
        margin-top: 16px;
        color: #4e5969;
      }
    }
    .schedule-body {
      height: 100%;
      width: 100%;
      overflow: auto;
      // position: absolute;
      padding: 0 20px;
      top: 0;
      left: 0;
      :deep(.arco-checkbox-icon) {
        border-radius: 50%;
        // position: absolute;
        // left: 20px;
        // top: 50px;
      }
      .arco-checkbox {
        display: flex;
        flex: 1;
      }
      :deep(.arco-checkbox-checked) {
        color: #1d2129 !important;
        .schedule-block-title {
          color: #1d2129 !important;
        }
      }
      // 已完成数据状态
      .active {
        .schedule-block-time div,
        .schedule-block-title,
        .schedule-block-described,
        .schedule-block-time span {
          color: #86909c !important;
        }
        .schedule-block-type {
          background-color: #e5e6eb !important;
          color: #86909c !important;
          .scheduleIcon1 {
            background-image: url(@/assets/images/schedule/icon-meeting2.png) !important;
          }
          .scheduleIcon2 {
            background-image: url(@/assets/images/schedule/icon-matter2.png) !important;
          }
        }
      }
      .schedule-block {
        padding: 24px 0 18px 0;
        display: flex;
        justify-content: space-between;
        align-items: top;
        border-bottom: 1px solid #efefef;
        :deep(.arco-checkbox-label) {
          display: flex;
        }
        &:hover {
          :deep(.arco-icon-hover) {
            display: block;
          }
        }
        div {
          // overflow: hidden;
          // text-overflow: ellipsis;
          // display: -webkit-box;
          // -webkit-line-clamp: 1; /* 限制显示的行数 */
          // -webkit-box-orient: vertical;
        }
        .schedule-block-left {
          margin-left: 40px;
          .schedule-block-time {
            font-size: 20px;
            margin-bottom: 8px;
            color: #1d2129;
          }
          .schedule-block-type {
            display: inline-block;
            // width: 64px;
            height: 24px;
            padding: 0 8px;
            line-height: 24px;
            border-radius: 8px;
            text-align: center;
            .scheduleIcon {
              display: inline-block;
              background-image: url(@/assets/images/schedule/icon-meeting.png);
              width: 16px;
              height: 16px;
              vertical-align: middle;
              background-size: 100% 100%;
              font-size: 14px;
            }
            .scheduleIcon1 {
              background-image: url(@/assets/images/schedule/icon-meeting1.png);
            }
            .scheduleIcon2 {
              background-image: url(@/assets/images/schedule/icon-matter1.png);
            }
            img {
              width: 20px;
              // margin-right: 2px;
            }
          }
          .schedule-block-type1 {
            background-color: #fff3e8;
            color: #f77234;
          }
          .schedule-block-type2 {
            background-color: #e8f7ff;
            color: #3491fa;
          }
        }

        .schedule-block-cont {
          flex: 1;
          padding: 0 20px;
          > div {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1; /* 限制显示的行数 */
            -webkit-box-orient: vertical;
          }

          .schedule-block-title {
            font-weight: 500;
            font-size: 16px;
            color: #1d2129;
            margin-bottom: 8px;
          }
          .schedule-block-described {
            color: #86909c;
            font-size: 14px;
          }
        }

        .icon-more {
          cursor: pointer;
        }
        // .arco-checkbox-checked,
        .schedule-block-active {
          .schedule-block-time {
            div {
              color: #c9cdd4 !important;
            }
          }
          .schedule-block-cont {
            .schedule-block-title {
              color: #86909c;
            }

            .schedule-block-type {
              background-color: #e5e6eb;
              color: #86909c;
            }
            .schedule-block-described {
              color: #c9cdd4;
            }
            .schedule-block-type {
              .scheduleIcon1 {
                background-image: url(@/assets/images/schedule/icon-meeting2.png);
              }
              .scheduleIcon2 {
                background-image: url(@/assets/images/schedule/icon-matter2.png);
              }
            }
          }
        }
        .schedule-list-action {
          cursor: pointer;
          width: 100px;
          text-align: right;
        }
      }
    }
  }
  :deep(.arco-divider-horizontal) {
    margin: 0 !important;
  }
</style>

<style>
  .custom-dropdown .arco-dropdown .arco-btn {
    color: #000;
  }
</style>
