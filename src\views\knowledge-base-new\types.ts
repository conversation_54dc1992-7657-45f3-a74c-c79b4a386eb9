// 查看知识库列表入参
export interface QueryBaseListParams {
  pageParam: {
    pageNo: number;
    pageSize: number;
  };
}

// 查看子文件/文件夹列表入参
export interface QueryFolderListParams {
  folderId: string;
  fullTree: boolean;
}

// 搜索文件夹入参
export interface SearchFileParams {
  fileName: string;
  kbId: string;
  pageParam?: {
    pageNo: number;
    pageSize: number;
  };
}

// 新建文件夹入参
export interface CreateFolderParams {
  name: string;
  parentId: string;
}

// 重命名文件夹入参
export interface RenameFolderParams {
  folderId: string;
  name: string;
}

// 重命名文件入参
export interface RenameFileParams {
  fileId: string;
  name: string;
}

export interface DeleteFolderParams {
  folderId: string;
}

export interface DeleteFileParams {
  fileId: string;
}

// 保存文件入参
export interface SaveFileParams {
  fileSize: number;
  folderId: string;
  name: string;
  ossToken: string;
  type: string;
}

// 创建/更新知识库入参
export interface CreateOrUpdateBaseParams {
  id?: string;
  description: string;
  edit: boolean;
  name: string;
  picUrl: string;
  recommendedQuestions: Array<string>;
  share: boolean;
  upload: boolean;
  view: boolean;
  updateRAGAgent?: boolean;
}

// 导入知识库入参
export interface ImportFileParams {
  fileIds: Array<string>;
  folderId: string;
}

// 生成分享链接入参
export interface GenerateShareLinkParams {
  kbId: string;
  expireMinutes?: number;
}

// 查看知识库成员列表入参
export interface QueryBaseMemberListParams {
  kbId: string;
  pageParam: {
    pageNo: number;
    pageSize: number;
  };
}

// 文件/文件夹
export interface Node {
  id?: string;
  name: string;
  parentId: string;
  type: string; // 类型:folder/DOC/PDF
  children?: Array<Node>;
  root?: boolean; // 是否是根文件夹
  childFileCnt?: number; // 文件夹下所有层级的文件个数
  aiDocId?: string; // 暂时无用
  aiFileId?: string; // 暂时无用
  aiStatus?: string; // 文档解析状态
  fileSize?: number; // 文件大小
  ossToken?: string; // fileToken
  isNewFolder?: boolean; // 是否是新建文件夹
  isEdit?: boolean; // 是否是重命名文件/文件夹
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
}

// 知识库记录
export interface KnowledgeBaseRecord {
  id?: string;
  aiKbId?: string; // 知识库id
  aiRagId?: string; // rag id
  type?: string; // 类型，个人知识库 PERSONAL，共享知识库 SHARED
  name: string; // 名称
  description: string; // 描述
  picUrl: string; // 封面url
  recommendedQuestions: Array<string>; // 推荐问题列表
  folderVO?: Node; // 根文件夹
  view: boolean; // 是否可查看
  upload: boolean; // 是否可上传
  share: boolean; // 是否可分享
  edit: boolean; // 是否可编辑
  owner?: boolean; // 是否是当前知识库创建者
  createBy?: string;
  createDate?: string;
  creatorName?: string; // 创建人姓名
  updateBy?: string;
  updateDate?: string;
}

// 要上传的文件结构
export interface CustomFileItem {
  file: File; // File对象
  size?: any;
  name: string; // 文件名称
  percent: number; // 上传进度0-100
  status: string; // 状态：init待上传，success成功
  uid: string;
  model: string; // 所属模块字段，knowledgeBase表示知识库
  folderId: string; // 所属文件夹ID
  fileToken?: string;
}

// AI对话记录
export interface ChatHistoryRecord {
  agentId: string;
  chatSessionId: string;
  chatSessionName: string;
  chatSessionStatus: number;
  chatSessionType: number;
  createBy: string;
  createDate: string;
  deleteFlag: number;
  id: string;
  updateBy: string;
  updateDate: string;
  userId: string;
}
