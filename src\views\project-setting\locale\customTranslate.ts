// 导入 translations，并使用类型断言来指定其类型
import translations from './translationsGerman';

export default function customTranslate(
  template: string,
  replacements: { [key: string]: string | undefined } = {}
): string {
  // Translate
  const translatedTemplate = translations[template] || template;

  // Replace
  return translatedTemplate.replace(/{([^}]+)}/g, (match, key) => {
    const str = replacements[key];

    // 注意：这里我们不再检查 translations[replacements[key]]，因为那通常不是替换逻辑的一部分
    // 如果你的意图是替换占位符为翻译后的文本，那么你应该只替换占位符的文本
    // 并且不应该尝试从 translations 中获取 replacements[key] 的翻译

    return str !== undefined ? str : `{${key}}`;
  });
}
