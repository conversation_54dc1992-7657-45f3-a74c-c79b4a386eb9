import { defineStore } from 'pinia';
import { GlobalMode } from './type';
import { setLocalstorage, getLocalstorage } from '@/utils/localstorage';

const useGlobalModeStore = defineStore('global-model', {
  state: (): GlobalMode => {
    return {
      mode: '',
    };
  },
  getters: {
    getGlobalMode(state: GlobalMode) {
      return state.mode;
    },
  },
  actions: {
    changeGlobalMode(mode: string) {
      // if (mode) {
      this.mode = mode;
      setLocalstorage('global_mode', mode);
      // }
    },
    initGlobalMode() {
      this.mode = getLocalstorage('global_mode') || 'work';
    },
  },
});

export default useGlobalModeStore;
