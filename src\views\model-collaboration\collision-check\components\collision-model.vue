<template>
  <a-modal
    :visible="true"
    width="800px"
    :unmount-on-close="true"
    :mask-closable="false"
    :esc-to-close="false"
    draggable
    @cancel="cancel"
  >
    <template #title>{{ $t('model-collaboration.select-model') }}</template>
    <template #footer>
      <a-space style="padding-right: 300px" size="large">
        <a-button v-show="current === 1" type="secondary" @click="cancel">
          <template #default>{{ $t('model-collaboration.cancel') }}</template>
        </a-button>
        <a-button v-show="current >= 2" type="secondary" @click="onPrev">
          <template #default>{{ $t('model-collaboration.back') }}</template>
        </a-button>
        <a-button
          v-show="current <= 2"
          type="primary"
          :disabled="nextDisabled"
          @click="onNext"
        >
          <template #default>{{ $t('model-collaboration.next') }}</template>
        </a-button>
        <a-button
          v-show="current === 3"
          :disabled="okDisabled"
          type="primary"
          :loading="submitLoading"
          @click="submit"
        >
          <template #default>{{ $t('model-collaboration.confirm') }}</template>
        </a-button>
      </a-space>
    </template>
    <div>
      <a-steps
        :current="current"
        :changeable="false"
        @change="setCurrent(current)"
      >
        <a-step :description="$t('model-collaboration.select-model')">{{
          $t('model-collaboration.first-step')
        }}</a-step>
        <a-step :description="$t('model-collaboration.select-range')">{{
          $t('model-collaboration.second-step')
        }}</a-step>
        <a-step :description="$t('model-collaboration.set-up-parameters')">{{
          $t('model-collaboration.third-step')
        }}</a-step>
      </a-steps>
      <div
        :style="{
          width: '100%',
          height: '350px',
          background: 'var(--color-bg-2)',
          color: '#C2C7CC',
        }"
      >
        <Steps1 v-show="current === 1" @change="nextDisabledFunction1" />
        <Steps2
          v-show="current === 2"
          ref="steps2"
          :tree-data="treeData"
          :model-number="collisionModelNum"
          @change="nextDisabledFunction2"
        />
        <Steps3
          v-show="current === 3"
          ref="steps3"
          :categories="categories"
          :step-data="step3Data"
          :disabled="disabledVal"
          :model-number="collisionModelNum"
          @change="nextDisabledFunction3"
          @refresh="updateData"
          @abnormal="submitLoading = false"
        />
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, toRaw, watch } from 'vue';
  import Steps1 from './steps1.vue';
  import Steps2 from './steps2.vue';
  import Steps3 from './steps3.vue';
  import { useRoute } from 'vue-router';
  import {
    GetXBaseModelCategory,
    GetXBaseSemanticInfo,
    // GetXBaseModeltree,
  } from '../api';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const route = useRoute();

  const setCurrent = (current: any) => {
    current.value = current;
  };
  const submitLoading: any = ref(false);

  // 碰撞模型个数
  const collisionModelNum: any = ref(1);

  const modalTitle = ref(t('model-collaboration.select-model'));
  const current = ref(1);
  const nextDisabled = ref(true);
  const okDisabled = ref(true);
  const modelFile: any = ref([{}, {}]);
  const treeData = ref<any[]>([]);
  const categories: any = ref([]);
  const step3Data = ref({
    files: {},
    modelInfo: [{}, {}],
    teamId: '',
  });

  // 存储语义模型数据
  const infoResArr: any = ref([]);

  // 获取构件数据
  const getMemberData = async () => {
    let categoryRes0: any = null;
    let categoryRes1: any = null;
    const categoriesAll = [];
    categoryRes0 = await GetXBaseModelCategory({
      render_path: infoResArr.value[0].data.info.render_path,
      page_num: 1,
      page_size: 10000,
    });
    if (categoryRes0.code === 0) {
      categoriesAll.push(...categoryRes0.data.categories);
    }

    if (collisionModelNum.value === 2) {
      categoryRes1 = await GetXBaseModelCategory({
        render_path: infoResArr.value[1].data.info.render_path,
        page_num: 1,
        page_size: 10000,
      });
      if (categoryRes1.code === 0) {
        categoriesAll.push(...categoryRes1.data.categories);
      }
    }

    categories.value = categoriesAll || [];
    categories.value.forEach((item: any) => {
      item.title = `${item.key}/${item.name}`;
      item.value = `${item.key}/${item.name}`;
    });
  };

  const disabledVal: any = ref(false);

  // 设置检查规则 若有一个文件为rvt 则不允许勾选规则
  const setRuleDisabled = (data: any) => {
    disabledVal.value = data.some((model: any) => {
      return model.name && model.name.endsWith('.dgn');
    });
  };

  // 获取语义模型数据
  const getSemanticInfo = async (semanticModelId: any) => {
    const params = {
      semantic_model_id: semanticModelId,
      group_id: route.params.projectId,
    };
    const infoRes1 = await GetXBaseSemanticInfo(params);
    infoResArr.value.push(infoRes1);
  };

  // 获取选择模型数据
  const getTreeData = async () => {
    infoResArr.value = [];
    // 获取语义模型信息
    // 一个模型
    if (modelFile.value[0]) {
      const semanticModelId =
        modelFile.value[0].graphicEngineInfo?.split('|')[2];
      await getSemanticInfo(semanticModelId);
      collisionModelNum.value = 1;
    }
    // 两个模型
    if (Object.keys(modelFile.value[1]).length !== 0) {
      const semanticModelId =
        modelFile.value[1].graphicEngineInfo?.split('|')[2];
      await getSemanticInfo(semanticModelId);
      collisionModelNum.value = 2;
    }
    // 获取构件数据
    getMemberData();
    step3Data.value.files = modelFile.value;
    // 模型参数 若选两个则都添加
    if (collisionModelNum.value === 2) {
      step3Data.value.modelInfo[1] = {
        model_id: modelFile.value[1].graphicEngineInfo?.split('|')[2],
        name: modelFile.value[1]?.name,
        render_path: infoResArr.value[1].data.info.render_path,
        code: '0',
        status: infoResArr.value[1].data.info.status,
      };
    }
    step3Data.value.modelInfo[0] = {
      model_id: modelFile.value[0]?.graphicEngineInfo?.split('|')[2],
      name: modelFile.value[0]?.name,
      render_path: infoResArr.value[0].data.info.render_path,
      code: '0',
      status: infoResArr.value[0].data.info.status,
    };
    // 设置检查参数禁用
    setRuleDisabled(step3Data.value.modelInfo);
  };

  const onPrev = () => {
    current.value = Math.min(3, current.value - 1);
    nextDisabled.value = false;
  };

  const nextDisabledFunction1 = async (val: any) => {
    nextDisabled.value = val.changed;
    if (!val.changed) {
      modelFile.value[val.number] = val.files;
      step3Data.value.teamId = val.teamId;
    }
    // 删除
    if (val.delIndex) {
      modelFile.value[val.delIndex] = {};
    }
    await getTreeData();
  };

  const nextDisabledFunction2 = (val: boolean) => {
    nextDisabled.value = val;
    submitLoading.value = false;
  };

  const nextDisabledFunction3 = (val: boolean) => {
    okDisabled.value = val;
  };

  const steps2 = ref();
  const steps3 = ref();

  const onNext = async () => {
    current.value = Math.min(3, current.value + 1);
    // if (steps2.value.sumSelectedKeys !== 2) {
    //   nextDisabled.value = true;
    // }

    if (current.value === 2) {
      treeData.value = [];
      treeData.value = [
        [
          {
            title: modelFile.value[0].name,
            children: [],
          },
        ],
        [
          {
            title: modelFile.value[1].name,
            children: [],
          },
        ],
      ];
    }
  };

  const emits = defineEmits(['close', 'refresh']);

  const updateData = () => {
    submitLoading.value = false;
    emits('refresh');
  };
  const cancel = () => {
    emits('close');
    current.value = 1;
    nextDisabled.value = true;
  };

  const submit = async () => {
    submitLoading.value = true;
    steps3.value.submit();
  };

  watch(
    current,
    () => {
      if (current.value === 2) {
        modalTitle.value = t('model-collaboration.model-tree-presentation');
        nextDisabled.value = false;
      } else if (current.value === 3) {
        modalTitle.value = t('model-collaboration.createNewCollisionDetection');
      }
    },
    { deep: true }
  );
</script>

<style scoped lang="less"></style>
