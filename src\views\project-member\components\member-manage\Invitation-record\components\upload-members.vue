<template>
  <a-modal
    v-model:visible="uploadModal"
    :title="$t('prjMember.table.opt.import')"
    unmount-on-close
    width="420px"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    @cancel="handleCancel"
  >
    <a-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      action="/api/sys-user/invitation/importUser"
      :headers="{
        'Fusion-Auth': getToken() || '',
        'Fusion-Biz': setFusionBiz() || '',
      }"
      :with-credentials="true"
      :auto-upload="false"
      :data="{ projectId: $route.params?.projectId as string,registerUrl:registerUrl }"
      :limit="1"
      draggable
      show-file-list
      list-type="text"
      :tip="$t('prjMember.support-xlsx')"
      :show-link="true"
      @before-upload="beforeUpload"
      @success="handleSuccess"
    >
      <template #upload-item="{ fileItem }">
        <div :key="fileItem.uid" class="arco-upload-list-item">
          <div class="arco-upload-list-item-content">
            <span class="arco-upload-list-item-thumbnail">
              <IconFile />
            </span>
            <div class="arco-upload-list-item-name">
              <div class="arco-upload-list-item-name-text">{{
                fileItem.name
              }}</div>
            </div>
          </div>
          <span
            class="arco-upload-list-item-operation"
            @click="removeFile(fileItem)"
          >
            <span class="arco-icon-hover">
              <span class="arco-upload-icon arco-upload-icon-remove">
                <IconClose />
              </span>
            </span>
          </span>
        </div>
      </template>
    </a-upload>
    <template #footer>
      <div class="footer">
        <a-link href="/成员模板.xlsx" download>{{
          $t('prjMember.download-membership-form')
        }}</a-link>
        <!-- <a-link @click="handleDownload" download>{{
          $t('prjMember.download-membership-form')
        }}</a-link> -->
        <a-space>
          <a-button @click="handleCancel">{{
            $t('prjMember.cancel')
          }}</a-button>
          <a-button type="primary" @click="handleSubmit">{{
            $t('prjMember.import')
          }}</a-button>
        </a-space>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, computed, inject } from 'vue';
  import type { FileItem } from '@arco-design/web-vue/es/upload/interfaces';
  import { IconFile, IconClose } from '@arco-design/web-vue/es/icon';
  import { Message, UploadInstance } from '@arco-design/web-vue';
  import { setFusionBiz } from '@/api/interceptor';
  import { getToken } from '@/utils/auth';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['update:modelValue', 'refresh']);
  const registerUrl = `${window.location.origin}/work/login?isRegister=1`;
  const uploadModal = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emits('update:modelValue', val);
    },
  });
  const handleCancel = () => {
    emits('update:modelValue', false);
  };

  const fileList = ref<FileItem[]>([]);

  // 上传成功后传递到爷爷组件 刷新邀请记录
  const handleData: any = inject('handleData');
  const refreshList = () => {
    handleData();
  };

  const uploadRef = ref<UploadInstance>();
  const handleSubmit = () => {
    // 不传参数默认提交fileList里第一个init状态的文件
    uploadRef.value?.submit();
  };
  const handleSuccess = (res: any) => {
    const result = res.response;
    if (result.status) {
      emits('update:modelValue', false);
      Message.success(
        `${t('prjMember.activation-link')} ${result.data.success} ${t(
          'prjMember.sent-successfully'
        )}, ${result.data.failure} ${t('prjMember.send-failure')}`
      );
      refreshList();
    } else {
      Message.error(result.message);
    }
  };

  // const customRequest = (option: RequestOption): UploadRequest => {
  //   const { fileItem, data } = option;
  //   console.log(option, fileItem.file);
  //   const formData = new FormData();
  //   formData.append('file', fileItem.file);
  //   formData.append('projectId', data?.projectId);
  //   importMember(formData).then((res) => {
  //     console.log(res);
  //   });
  // };

  const beforeUpload = (file: File): boolean | Promise<boolean | File> => {
    // 判断file格式是否正确
    if (
      [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ].indexOf(file.type) === -1
    ) {
      Message.error(t('prjMember.please-upload-correct-xls-format'));
      return false;
    }
    return true;
  };

  const removeFile = (fileItem: FileItem) => {
    fileList.value = fileList.value.filter((item) => item.uid !== fileItem.uid);
  };

  // 下载成员列表
  const handleDownload = () => {
    const url = '/public/成员模板.xlsx'; // 这里使用相对路径，假设文件在 public 文件夹中
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', '成员模板.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link); // 移除链接元素
  };
</script>

<style lang="less" scoped>
  .arco-upload-list-item-thumbnail {
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 1;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      color: var(--color-text-2);
    }

    button {
      border-radius: var(--border-radius-medium);
    }
  }
</style>
