<template>
  <div class="member-management-container">
    <!-- 左侧团队列表 -->
    <a-col
      v-if="teamId === 'global'"
      class="team-list"
      :span="8"
      style="padding: 20px"
    >
      <a-row>
        <list-title
          :title-text="$t('member.management.group')"
          btn-type="text"
          @add="showAddTeamSidebar"
          class="team-header"
        >
          <template #button-content>
            <component :is="AddFillIcon" style="width:20px;height:20px; vertical-align:middle;"/>
          </template>
        </list-title>
      </a-row>
      
      <!-- 团队列表 -->
      <div class="team-menu-container">
        <a-menu
          v-if="selectedTeam"
          mode="pop"
          :selected-keys="[String(defaultSelectedTeamId)]"
          class="aside"
          style="background-color: #ffffff"
        >
          <a-menu-item
            v-for="team in selectedTeam"
            :key="team.id"
            class="asideItem"
            @click="handleMenuItemClick($event, team)"
          >
            <a-tooltip :content="team.teamName">
              <span class="team-name">{{ team.teamName }}</span>
            </a-tooltip>
            <a-space v-if="team.isCreator" class="team-actions">
              <a-button
                status="warning"
                class="edit-team-btn"
                @click.stop="editTeam(team)"
              >
                <template #icon><icon-edit /></template>
              </a-button>
              <a-button
                status="danger"
                class="delete-team-btn"
                @click.stop="showRemoveTeamConfirm(team)"
              >
                <template #icon><icon-delete /></template>
              </a-button>
            </a-space>
          </a-menu-item>
        </a-menu>
      </div>
    </a-col>

    <!-- 右侧成员列表 -->
    <a-col :span="teamId === 'global' ? 16 : 24">
      <a-row>
        <list-title
          :title-text="$t('member.management.members')"
          :btn-text="t('member.management.invite')"
          @add="showSelectUser"
          class="member-header"
        />
      </a-row>
      <div class="member-content">
        <a-table
          :columns="columns"
          :data="memberList"
          :pagination="false"
          class="member-table"
          row-key="userName"
          :scroll="{ y: true }"
          :bordered="false"
          :show-header="true"
        >
          <template #index="{ rowIndex }">
            {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
          </template>
          <template #user="{ record }">
            <div style="display: flex; align-items: center; justify-content: flex-start; width: 100%;">
              <a-avatar
                v-if="record.avatarToken"
                :size="44"
                style="overflow: hidden"
              >
                <img
                  :src="
                    '/work/api/sys-storage/download_image?f8s=' +
                    record.avatarToken
                  "
                />
              </a-avatar>
              <a-avatar v-else :size="44">{{
                record.userFullname?.substring(0, 1) || ''
              }}</a-avatar>
              <span class="user-fullname" style="margin-left: 12px;">{{ record.userFullname }}</span>
            </div>
          </template>

          <template v-if="companyId !== ''" #organizationType="{ record }">
            <a-tag
              v-if="record.companyUserType && record.companyUserType == 1"
              class="internal-tag"
            >
              {{ t('member.management.organization') }}
            </a-tag>
            <a-tag v-else class="external-tag">
              {{ t('member.management.external') }}
            </a-tag>
          </template>

          <template #action="{ record }">
            <a-button
              v-if="currentTeamCreator"
              type="text"
              status="danger"
              class="remove-btn"
              @click="showRemoveConfirm(record)"
            >
              <template #icon><icon-export /></template>
              {{ t('member.management.remove') }}
            </a-button>
          </template>
        </a-table>
        <!-- 添加分页器 -->
        <div class="pagination-wrapper">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            show-total
            show-page-size
            :page-size-options="[5, 10]"
            @change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
        </div>
      </div>
    </a-col>

    <!-- 底部操作栏 -->
    <!-- <a-row class="bottom-actions" justify="space-between" align="center">
      <a-button class="close-btn">关闭所有</a-button>
      <a-button type="primary" class="save-btn">保存设置</a-button>
    </a-row> -->

    <!-- 移出确认弹窗 -->
    <a-modal
      v-model:visible="removeModalVisible"
      :title="t('member.management.confirm.remove')"
      @ok="handleRemoveConfirm"
      @cancel="removeModalVisible = false"
    >
      <p>{{
        t('member.management.confirm.remove.message', {
          name: removeMemberName,
        })
      }}</p>
    </a-modal>

    <!-- 移出删除团队弹窗 -->
    <a-modal
      v-model:visible="removeTeamModalVisible"
      :title="t('member.management.confirm.delete')"
      @ok="handleDeteleTeamConfirm"
      @cancel="removeTeamModalVisible = false"
    >
      <p>{{
        t('member.management.confirm.delete.message', {
          teamName: removeTeam.teamName,
        })
      }}</p>
    </a-modal>

    <!-- 添加新团队侧边栏 -->
    <!-- 修改抽屉组件属性 -->
    <a-drawer
      v-model:visible="showSidebar"
      :title="
        addOrEditTeamStatus
          ? t('member.management.add.group')
          : t('member.management.edit.group')
      "
      placement="right"
      :closable="true"
      :mask-closable="true"
      style="z-index: 105"
      width="30vw"
      @cancel="showSidebar = false"
      @click.stop=""
    >
      <a-form :model="newTeam" layout="vertical">
        <a-form-item
          field="name"
          :label="t('member.management.group.name')"
          :rules="[
            {
              required: true,
              message: t('member.management.group.name.required'),
            },
          ]"
        >
          <a-input
            v-model="newTeam.name"
            :placeholder="t('member.management.group.name.placeholder')"
          />
        </a-form-item>
        <a-form-item
          field="members"
          :label="t('member.management.selected.members')"
        >
          <div class="members-container" @click="showSelectUser">
            <a-tag
              v-for="member in newTeam.members"
              :key="member.userName"
              closable
              @close="removeMember(member)"
            >
              {{ member.userFullname }}
            </a-tag>
          </div>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button style="margin-right: 10px" @click="closeSidebar">
          {{ t('common.cancel') }}
        </a-button>
        <a-button type="primary" @click="handleAddTeam">
          {{ t('common.confirm') }}
        </a-button>
      </template>
    </a-drawer>
    
    <!-- 选择成员组件 -->
    <select-members
      ref="selectMembersRef"
      v-model:visible="selUserVisible"
      style="z-index: 115"
      @select-member="selectMember"
    ></select-members>
  </div>
</template>

<script setup>
  import {
    onMounted,
    onBeforeUnmount,
    ref,
    reactive,
    computed,
    watch,
  } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import selectMembers from '@/components/selectMembers/index.vue';
  import { useI18n } from 'vue-i18n';
  import {
    createTeamApi,
    searchTeamListApi,
    searchTeamUserListApi,
    saveTeamUserApi,
    deleteTeamUserApi,
    deleteTeamApi,
  } from '@/api/modules/team';
  import { forEach } from 'lodash';
  import { useUserStore } from '@/store';
  import ListTitle from '@/components/list-title/index.vue';
  import AddFillIcon from '@/assets/images/setting/add-fill.svg';


  const { t } = useI18n();

  const selUserVisible = ref(false);
  // 表格列定义
  const columns = computed(() => [
    {
      title: t('member.management.index'),
      dataIndex: 'index',
      slotName: 'index',
      width: 160,
      align: 'left',
    },
    {
      title: t('member.management.user'),
      dataIndex: 'user',
      slotName: 'user',
      minWidth: 120,
      align: 'left',
    },
    {
      title: t('member.management.email'),
      dataIndex: 'email',
      align: 'left',
    },
    {
      title: t('member.management.organizationType'),
      dataIndex: 'organizationType',
      slotName: 'organizationType',
      width: 120,
      align: 'left',
    },
    {
      title: t('member.management.operation'),
      key: 'action',
      slotName: 'action',
      width: 120,
      align: 'left',
    },
  ]);

  // 成员表数据
  const memberList = ref([]);
  // 接口节流限制
  const canSendRemoveMemberRequest = ref(true);
  const canSendDeleteTeamRequest = ref(true);
  const canSendAddTeamRequest = ref(true);
  const canSendSaveTeamUserRequest = ref(true);

  // 新建团队变量
  const newTeam = reactive({
    id: '',
    name: '',
    members: [],
  });

  // 团队相关状态
  const userStore = useUserStore();
  const teamId = computed(() => userStore.teamId);
  const companyId = computed(() => userStore.companyId);
  // 团队列表变量
  const defaultSelectedTeamId = ref('');
  const currentTeamCreator = ref('');
  const selectedTeam = ref([]);
  // 控制右侧弹框的信息展示
  const addOrEditTeamStatus = ref(true);

  // 移出成员相关状态
  const removeModalVisible = ref(false);
  const removeTeamModalVisible = ref(false);
  const removeMemberName = ref('');
  const removeMemberUser = ref('');
  const removeTeam = ref('');
  // 新建团队相关状态
  const showSidebar = ref(false);
  // 顶部团队列表
  const navbarTeamList = computed(() => userStore.teamList);

  // 添加分页相关的响应式数据
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const closeSidebar = () => {
    showSidebar.value = false;
    newTeam.id = '';
    newTeam.name = '';
    newTeam.members = [];
  };

  // 搜索团队列表
  const searchTeamList = async (params) => {
    try {
      const response = await searchTeamListApi(params);
      // console.log('搜索团队列表成功:', response);
      return response;
    } catch (error) {
      console.error('搜索团队列表失败:', error);
      throw error;
    }
  };

  // 删除团队用户
  const deleteTeamUser = async (id) => {
    // if(removeMemberUser.value.length<=0)return;
    if (!canSendRemoveMemberRequest.value) {
      Message.error(t('setup.frequent.request.message'));
      return;
    }
    canSendRemoveMemberRequest.value = false;
    try {
      const params = {
        teamId: id,
        teamMembers: [removeMemberUser.value],
        // teamMembers: removeMemberUser.value.map(member => member.userName)
      };
      // console.log("掉接口来团队移除用户canshu数据", params);
      const result = await deleteTeamUserApi(params);
      // console.log('掉接口来团队移除用户canshu结果数据', result);

      if (result.status) {
        Message.success(result?.message);
      }
      // else {
      //   Message.error(result?.message);
      // }
    } catch (error) {
      console.error('删除团队用户失败:', error);
      // Message.error(t('member.management.remove.failed'));
    } finally {
      canSendRemoveMemberRequest.value = true;
    }
  };

  // 显示选人组件
  const showSelectUser = () => {
    // console.log('触发选人组件', defaultSelectedTeamId.value, showSidebar.value);
    console.log('当前团队创建者', currentTeamCreator.value);
    // 在这里增加判断
    // if (showSidebar.value) {
    //   // 在这里是新建或者编辑团队判断
    //   // Message.warning(t('member.management.team.name.required'));
    // } else {
    //   // 在这里是团队添加成员判断
    //   if (!defaultSelectedTeamId.value) {
    //     Message.warning(t('member.management.team.name.required'));
    //     return;
    //   }
    // }
    if (!showSidebar.value && !defaultSelectedTeamId.value) {
      Message.warning(t('member.management.group.save.user.failed'));
      return;
    }
    selUserVisible.value = true;
    // loadTeamUserList();
  };

  // 移除团队成员tag
  const removeMember = (member) => {
    // console.log(member, '移除团队成员tag');
    if (!member || !newTeam.members) return;
    // console.log(member.userName, '移除团队成员tag');

    // 使用 filter 方法创建一个不包含目标 member 的新数组
    newTeam.members = newTeam.members.filter((item) => {
      // 如果 item 的 userName 不等于 member 的 userName，则保留该 item
      // console.log(item.userName, member.userName)
      return item.userName !== member.userName;
    });

    // console.log('Member removed successfully');
  };

  // 显示移出确认弹窗
  const showRemoveConfirm = (member) => {
    // console.log("显示移除弹框", member);
    removeMemberName.value = member.userFullname;
    // if (!Array.isArray(removeMemberUser.value)) {
    //   removeMemberUser.value = [];
    // }
    removeMemberUser.value = member.userName;
    removeModalVisible.value = true;
    // console.log(removeMemberName.value, removeMemberUser.value, removeModalVisible.value);
  };

  // 显示删除团队确认弹窗
  const showRemoveTeamConfirm = (team) => {
    // console.log("显示移除弹框", member);
    removeTeam.value = team;
    removeTeamModalVisible.value = true;
  };

  // 编辑团队
  const editTeam = async (team) => {
    addOrEditTeamStatus.value = false;
    console.log('编辑团队', team);
    const params = {
      pageNo: 1,
      pageSize: 10000,
      teamId: team.id,
      companyId: 100000,
    };
    newTeam.id = team.id;
    console.log('编辑团队参数', params);
    const result = await searchTeamUserListApi(params);
    console.log('编辑团队结果', result);
    if (result.status) {
      newTeam.members = result?.data?.list || [];
      newTeam.name = team.teamName;
    }
    // else {
    //   Message.error(
    //     result.message || t('member.management.members.load.failed')
    //   );
    // }
    // 打开编辑团队弹窗
    showSidebar.value = true;
  };

  // 加载团队数据方法
  const loadTeamList = async () => {
    try {
      const params = { companyId: '100000' };
      const { data } = await searchTeamList(params);
      if (data) {
        selectedTeam.value = data;
        // if (data.length !== navbarTeamList.value.length) {
        userStore.setTeamList(data);
        // }
        // 只在初始化时（没有默认选中团队时）才设置第一个团队
        if (!defaultSelectedTeamId.value && data.length > 0) {
          defaultSelectedTeamId.value = String(data[0].id);
          currentTeamCreator.value = data[0].isCreator;
        }
      }
    } catch (error) {
      console.error('加载团队数据失败:', error);
      // Message.error(t('member.management.team.load.failed'));
    }
  };

  // 加载团队用户列表方法
  const loadTeamUserList = async (param) => {
    try {
      const params = {
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        teamId: param,
        companyId: 100000,
      };
      const result = await searchTeamUserListApi(params);
      console.log('加载团队数据', result);
      if (result.status) {
        const data = result?.data;
        memberList.value = data?.list || [];
        pagination.total = data?.total || 0;
      }
      // else {
      //   Message.error(
      //     result.message || t('member.management.members.load.failed')
      //   );
      // }
    } catch (error) {
      console.error('加载团队数据失败:', error);
      // Message.error(t('member.management.members.load.failed'));
    }
  };

  // 保存团队成员接口
  const saveTeamUserFunction = async (id, members) => {
    if (!canSendSaveTeamUserRequest.value) {
      Message.error(t('setup.frequent.request.message'));
      return;
    }
    canSendSaveTeamUserRequest.value = false;
    try {
      if (members.length <= 0) return;
      const params = {
        teamId: id,
        teamMembers: members,
      };
      const result = await saveTeamUserApi(params);
      console.log('掉接口来保存用户团队数据', params, result);
      if (result.status) {
        Message.success(result?.message);
      }
      // else {
      //   Message.error(result?.message || t('member.management.save.failed'));
      // }
    } catch (error) {
      console.error('掉接口来保存用户团队数据:', error);
      Message.error(t('member.management.save.failed'));
    } finally {
      canSendSaveTeamUserRequest.value = true;
    }
  };

  // 修改参数名称从 teamId 到 currentTeamId
  const loadData = async (currentTeamId) => {
    try {
      if (currentTeamId === 'global') {
        await loadTeamList();
        if (defaultSelectedTeamId.value) {
          await loadTeamUserList(defaultSelectedTeamId.value);
        }
      } else {
        // 直接加载指定团队的用户
        defaultSelectedTeamId.value = currentTeamId;
        currentTeamCreator.value = navbarTeamList.value.find(
          (team) => team.id === currentTeamId
        )?.isCreator;
        await loadTeamUserList(currentTeamId);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      Message.error(t('member.management.data.load.failed'));
    }
  };

  // 修改团队选择处理函数
  const changeTeamUser = (teamItem) => {
    defaultSelectedTeamId.value = teamItem.id;
    currentTeamCreator.value = teamItem.isCreator;
    loadData(teamItem.id);
  };

  // 修改确认添加新团队函数
  const handleAddTeam = async () => {
    const teamName = newTeam.name?.trim();
    if (!teamName) {
      Message.warning(t('member.management.group.name.required'));
      return;
    }
    if (!canSendAddTeamRequest.value) {
      console.log('添加团队接口节流限制');
      Message.error(t('setup.frequent.request.message'));
      return;
    }
    console.log('添加团队接口节流限制1111');
    canSendAddTeamRequest.value = false;
    console.log('添加团队', newTeam);
    // return;
    try {
      const baseParams = {
        companyId: '100000',
        teamName,
        teamMembers: newTeam.members.map((member) => member.userName),
      };

      if (newTeam.id) {
        baseParams.id = newTeam.id;
      }

      const addTeamResult = await createTeamApi(baseParams);
      if (addTeamResult.status) {
        Message.success(addTeamResult.message);
      } else if (newTeam.id) {
        Message.error(
          addTeamResult.message || t('member.management.group.update.failed')
        );
      } else {
        Message.error(
          addTeamResult.message || t('member.management.group.create.failed')
        );
      }
    } catch (error) {
      console.error('创建团队失败:', error);
      if (newTeam.id) {
        Message.error(t('member.management.group.update.failed'));
      } else {
        Message.error(t('member.management.group.create.failed'));
      }
    } finally {
      // 重新加载数据
      await loadData('global');
      newTeam.name = '';
      newTeam.members = [];
      closeSidebar();
      canSendAddTeamRequest.value = true;
    }
  };

  // 确认移出成员
  const handleRemoveConfirm = async () => {
    try {
      const deleteTeamUserResult = await deleteTeamUser(
        defaultSelectedTeamId.value
      );
      removeModalVisible.value = false;
    } catch (error) {
      console.error('移出成员失败:', error);
    } finally {
      // 重新加载当前团队数据
      await loadTeamUserList(defaultSelectedTeamId.value);
    }
  };

  // 确认删除团队
  const handleDeteleTeamConfirm = async () => {
    if (!canSendDeleteTeamRequest.value) {
      Message.error(t('setup.frequent.request.message'));
      return;
    }
    canSendDeleteTeamRequest.value = false;
    try {
      const deleteTeamUserResult = await deleteTeamApi(removeTeam.value.id);
      if (deleteTeamUserResult.status) {
        // 保存当前选中的团队ID
        const currentSelectedId = defaultSelectedTeamId.value;
        await loadTeamList(); // 重新加载团队列表

        // 只有当删除的是当前选中的团队时，才切换到第一个团队
        if (String(removeTeam.value.id) === String(currentSelectedId)) {
          if (selectedTeam.value && selectedTeam.value.length > 0) {
            defaultSelectedTeamId.value = String(selectedTeam.value[0].id);
            await loadTeamUserList(selectedTeam.value[0].id);
          } else {
            defaultSelectedTeamId.value = '';
          }
        }
        Modal.success(t('member.management.delete.success'));
      }
      // else {
      //   Message.error(
      //     deleteTeamUserResult.message || t('member.management.delete.failed')
      //   );
      // }
      removeTeamModalVisible.value = false;
    } catch (error) {
      console.error('删除团队失败:', error);
      // Message.error(t('member.management.delete.failed'));
    } finally {
      canSendDeleteTeamRequest.value = true;
    }
  };

  // 修改选择组件方法
  const selectMember = async (user) => {
    console.log('选择成员', user);
    if (user !== null && Array.isArray(user)) {
      if (showSidebar.value) {
        console.log('处理新建团队时的成员选择');
        // 处理新建团队时的成员选择
        user.forEach((item) => {
          console.log('选择成员', item);
          if (
            item.userName &&
            !newTeam.members.some((m) => m.userName === item.userName)
          ) {
            newTeam.members.push(item);
          }
          console.log('newTeam', newTeam.members);
        });
      } else {
        console.log('处理添加团队成员');
        // 处理添加团队成员
        // const teamUsernameList = user
        //   .filter(
        //     (item) =>
        //       item.userName &&
        //       !memberList.value.some((m) => m.userName === item.userName)
        //   )
        //   .map((item) => item.userName);
        const teamUsernameList = user.map((item) => item.userName);
        console.log('teamUsernameList', teamUsernameList);
        if (teamUsernameList.length > 0) {
          await saveTeamUserFunction(
            defaultSelectedTeamId.value,
            teamUsernameList
          );
          await loadTeamUserList(defaultSelectedTeamId.value);
        } else {
          Message.warning(t('member.management.group.save.user.failed'));
        }
      }
    }
  };

  // 修改 teamId 的监听
  watch(teamId, (newTeamId) => {
    console.log('监听到团队ID变化', newTeamId);
    // 重置分页
    pagination.current = 1;
    pagination.pageSize = 10;
    // 统一加载数据
    loadData(newTeamId);
  });

  // 修改分页处理函数
  const handlePageChange = (page) => {
    pagination.current = page;
    loadTeamUserList(defaultSelectedTeamId.value);
  };

  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.current = 1;
    loadTeamUserList(defaultSelectedTeamId.value);
  };

  const showAddTeamSidebar = () => {
    // 这里是新增逻辑
    newTeam.id = '';
    newTeam.name = '';
    newTeam.members = [];
    addOrEditTeamStatus.value = true;
    showSidebar.value = true;
  };

  // 设置点击左侧团队菜单时的处理函数
  const handleMenuItemClick = (event, team) => {
    // 检查点击事件的目标是否是按钮或按钮内的元素
    const isButtonClick = event.target.closest(
      '.edit-team-btn, .delete-team-btn'
    );
    if (!isButtonClick) {
      changeTeamUser(team);
    }
  };

  // 修改 onMounted
  onMounted(() => {
    loadData(teamId.value);
  });
</script>

<style scoped lang="less">
  .member-management-container {
    display: flex;
    height: 100%;
    overflow: hidden;
    position: relative;
  }

  .team-list {
    border-right: 1px solid #d9d9d9;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
  }

  .team-header {
    width: 100%;
    height: 24px;
    margin-bottom: 16px;
    // border-bottom: 1px solid #f0f0f0;
  }

  /* 新增滚动容器样式 */
  .team-menu-container {
    flex: 1;
    overflow-y: auto;
  }
  .aside {
    .asideItem {
      margin: 0;
      width: 100%;
      height: 48px;
      border-radius: 6px;
    }

    :deep(.arco-menu-inner) {
      padding: 0;
    }
    :deep(.arco-menu-item) {
      font-size: 16px;
      color: #4e5969;

      font-weight: 500;
      line-height: 32px;
      font-style: normal;
      text-transform: none;

      // border-radius: 8px;
      height: 48px !important;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: white;
      border-bottom: 1px solid #ededed;
      &:first-child {
        border-top: 1px solid #ededed;
      }
    }
    :deep(.arco-menu-selected) {
      // border-radius: 8px;
      height: 48px;
      color: #1d2129;
      background-color: #e8f2ff;
      border: 1px solid #ededed;
    }
  }

  .team-name {
    flex: 1;
    max-width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .team-actions {
    margin-left: 8px;
    flex-shrink: 0;
    display: flex;
    gap: 4px;
  }

  .team-actions .arco-btn {
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: none;
    color: #4e5969;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e8f2ff;
      color: #165dff;
    }

    &:hover .arco-icon {
      color: #165dff;
    }
  }

  .team-actions .arco-btn .arco-icon {
    margin: 0;
    font-size: 16px;
  }

  /* 自定义滚动条样式 */
  .team-menu-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .team-menu-container::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.2);
  }

  .team-menu-container::-webkit-scrollbar-track {
    border-radius: 3px;
    background: transparent;
  }

  .members-container-form-item {
  }

  .members-container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 5px 5px 50px 5px;
    border: 1px solid #c9cdd4;

    border-radius: 8px 8px 8px 8px;
    margin-bottom: 16px;
  }

  .new-team-btn {
    padding: 0;
    font-size: 14px;
  }

  .member-header {
    height: 60px;
    width: 100%;
    // border-bottom: 1px solid #f0f0f0;
  }

  .invite-btn {
    margin-left: 16px;
  }

  .member-content {
    height: calc(100% - 80px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .member-table {
    flex: 1;
    overflow: hidden;

    :deep(.arco-table-container) {
      border: none;
    }

    :deep(.arco-table-tr) {
      height: 60px;
      border-radius: 8px;
    }

    // 修改hover样式
    :deep(.arco-table-tr:not(.arco-table-tr-header)) {
      &:hover {
        td {
          // cursor: default;
          // 使用 !important 来确保样式优先级
          background-color: rgba(232, 242, 255, 0.4) !important;
        }
      }
    }

    :deep(.arco-table-td) {
      text-align: left !important;
      line-height: 100%;
      cursor: default;
      border: none;
      background-color: transparent;
    }
    :deep(.arco-table-td:nth-child(1)) {
      box-sizing: border-box;
      // padding-left: 20px;
      width: 80px;
    }
    :deep(.arco-table-td:nth-child(2)) {
      width: auto;
      text-align: left;
      padding-left: 0px;
      .arco-table-cell {
        padding-left: 0px;
      }
    }
    :deep(.arco-table-td:nth-child(3)) {
      text-align: left;
    }
    :deep(.arco-table-td:nth-child(4)) {
      text-align: center;
      // max-width: 300px;
      // min-width: 100px;
    }
    // :deep(.arco-table-td:nth-child(5)) {
    // }

    .user-fullname {
      font-size: 14px;
      color: #1d2129;
      margin-left: 16px;
    }
    .remove-btn:hover {
      background-color: transparent;
      background-color: none;
    }

    .internal-tag {
      width: 72px;
      height: 32px;
      background: #e8f7ff;
      border-radius: 4px;
      border: 1px solid #3491fa;
      font-weight: 500;
      font-size: 14px;
      color: #3491fa;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding: 4px 8px;
      margin: 0;

      &:deep(.arco-tag-content) {
        color: #3491fa;
      }
    }

    .external-tag {
      width: 72px;
      height: 32px;
      background: #fff7e8;
      border-radius: 4px;
      border: 1px solid #ff7d00;
      font-weight: 500;
      font-size: 14px;
      color: #ff7d00;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding: 4px 8px;
      margin: 0;

      &:deep(.arco-tag-content) {
        color: #ff7d00;
      }
    }
  }

  .pagination-wrapper {
    padding: 16px;
    display: flex;
    justify-content: flex-end;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
  }

  /* 修改右侧列表容器样式 */
  .member-management-container > .a-col {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .close-btn {
    background-color: #f5f5f5;
    border: none;
    border-radius: 4px;
    padding: 0 16px;
    height: 32px;
    font-size: 14px;
  }

  .save-btn {
    background-color: #1890ff;
    border: none;
    border-radius: 4px;
    padding: 0 16px;
    height: 32px;
    font-size: 14px;
    color: white;
  }
</style>
