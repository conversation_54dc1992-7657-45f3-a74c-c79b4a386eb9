// import service from '@/utils/request';
import axios from 'axios';
import qs from 'query-string';

/**
 * [uploadFile] - 上传切片参数
 * @param fileHash 文件hash，String
 * @param fileSize 文件大小，Number
 * @param fileName 文件名称，String
 * @param index 多文件上传中的所在index，number
 * @param chunkFile 切片文件本身，File || Blob || void
 * @param chunkHash 切片文件hash，String
 * @param chunkSize 分片大小，Number
 * @param chunkNumber 切片总数量，Number
 * @param finish 是否上传完成，可选参数，Boolean
 */

export function uploadFile(
  data: any,
  onCancel: (cancel: () => void) => void,
  onProgress: (progressEvent: ProgressEvent) => void
): Promise<{ code?: number; data: any }> {
  const controller = new AbortController();
  const { signal } = controller; // 获取 signal 对象

  const request = axios({
    url: '/sys-storage/bigFile',
    method: 'post',
    data,
    onUploadProgress: (progressEvent: ProgressEvent) => {
      if (signal.aborted) {
        return; // 如果请求已被取消，停止处理进度
      }
      onProgress(progressEvent); // 调用进度回调
    },
    signal, // 将 signal 传递给请求
  });

  if (typeof onCancel === 'function') {
    onCancel(() => controller.abort()); // 调用 onCancel 时传入取消函数
  }

  return request;
}

/**
 * [mergeChunk] - 合并切片
 * @param chunkSize 分片大小，Number
 * @param fileName 文件名称，String
 * @param fileSize 文件大小，Number
 */

// 合并所有切片
export function mergeChunk(data: any): Promise<{ code: number; data: any }> {
  return axios.post('/sys-storage/bigFile/together', data);
}

// 添加合并文件的信息
export function addMergaFile(data: any) {
  return axios.post('/cde-collaboration/file/save', data);
}

/**
 * [queryUploadId] - 获取当前上传文件唯一标识
 */

// 获取当前上传文件唯一标识
export function queryUploadId(data: any) {
  return axios({
    url: '/sys-storage/uploadId',
    method: 'post',
    data,
  });
}

/* 大文件上传begin */
// 获取当前文件的上传情况(正常上传、极速秒传、断点续传)
export function queryUploadInfo(data: any) {
  return axios.post('/sys-storage/secondPass', qs.stringify(data));
}

// 获取当前文件已上传的分片列表
export function queryFileBlockList(params: any) {
  return axios.get('/sys-storage/parts', { params });
}

// 查询文件组信息(groupToken存在则返回一组信息，fileToken存在则返回精确文件信息)
export function queryGroupFileInfo(data: any) {
  return axios.post('/sys-storage/file', { data });
}

// 上传文件结构
export function uploadFolder(data: any) {
  return axios.post('/cde-collaboration/folder/uploadFolder', data);
}

// 发出标识标识表示需要后端进行转换
export function javaFileUpload(params: any) {
  return axios.post(
    // eslint-disable-next-line no-useless-concat
    `/cde-collaboration/file/file-upload` + `?fileIds=${params.fileIds}`
  );
}

/** 可视化文件上传(非分片) */
export const VisualFileUpload = (data: any) => {
  return axios({
    url: '/api/open/v1/document/upload',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
      // 'Authorization': `Bearer ${getXBaseToken() || ''}`,
    },
    baseURL: '/x_base_api',
  });
};

// 下载zip文件
export function zipDownload(fileList: any) {
  return axios.post('/sys-storage/zip', fileList, {
    responseType: 'blob',
    timeout: 0,
  });
}
