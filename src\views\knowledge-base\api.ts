import axios from 'axios';
import {
  QueryBaseListParams,
  QueryFolderListParams,
  CreateFolderParams,
  RenameFolderParams,
  DeleteFolderParams,
  SaveFileParams,
  DeleteFileParams,
  KnowledgeBaseUsageParams,
} from './types';

// 查看知识库列表
function getBaseList(data: QueryBaseListParams) {
  return axios.post('/cde-collaboration/knowledgeBase/listBases', data);
}

// 查看文件夹的子内容列表
function getFolderList(data: QueryFolderListParams) {
  return axios.post('/cde-collaboration/knowledgeBase/listFolders', data);
}

// 新建文件夹
function createFolder(data: CreateFolderParams) {
  return axios.post('/cde-collaboration/knowledgeBase/createFolder', data);
}

// 重命名文件夹
function renameFolder(data: RenameFolderParams) {
  return axios.post('/cde-collaboration/knowledgeBase/renameFolder', data);
}

// 删除文件夹
function deleteFolder(data: DeleteFolderParams) {
  return axios.post('/cde-collaboration/knowledgeBase/deleteFolder', data);
}

// （上传文件后）保存文件
function saveFile(data: SaveFileParams) {
  return axios.post('/cde-collaboration/knowledgeBase/saveFile', data);
}

// 删除文件
function deleteFile(data: DeleteFileParams) {
  return axios.post('/cde-collaboration/knowledgeBase/deleteFile', data);
}

// 查询知识库使用情况
function getKnowledgeBaseUsage(data: KnowledgeBaseUsageParams) {
  return axios.post('/cde-collaboration/knowledgeBase/queryBaseFileCnt', data);
}

// 查看当前文件夹的完整路径
function getFolderPath(data: QueryFolderListParams) {
  return axios.post('/cde-collaboration/knowledgeBase/queryFolderPath', data);
}

function batchMoveFile(data: any) {
  return axios.post('/cde-collaboration/knowledgeBase/batchMoveFile', data);
}

// 搜索文件列表
function getSearchFile(data: QueryFolderListParams) {
  return axios.post('/cde-collaboration/knowledgeBase/searchFile', data);
}
export {
  getBaseList,
  getFolderList,
  createFolder,
  renameFolder,
  deleteFolder,
  saveFile,
  deleteFile,
  getKnowledgeBaseUsage,
  getFolderPath,
  batchMoveFile,
  getSearchFile,
};
