<template>
  <div class="file-tree">
    <div class="tree-content">
      <a-tree
        ref="folderTree"
        block-node
        default-expand-all
        :default-expanded-keys="expandedKeys"
        v-model:selected-keys="selectedKeys"
        :data="folderData"
        :field-names="{
          key: 'id',
          title: 'name',
        }"
        size="large"
        @select="nodeClick"
        @expand="nodeExpand"
      >
        <template #switcher-icon="node">
          <IconDown
            v-if="
              !('children' in node) ||
              !node.children ||
              node.children?.length > 0
            "
          />
        </template>
        <template #icon="node">
          <a-tooltip
            :content="
              node.node.standardName && node.node.deliveryStandardName
                ? `${$t('file-manage.naming-standard')}：${
                    node.node.standardName
                  }\n${$t('file-manage.delivery-criteria')}：${
                    node.node.deliveryStandardName
                  }`
                : node.node.standardName && !node.node.deliveryStandardName
                ? `${$t('file-manage.naming-standard')}：${
                    node.node.standardName
                  }`
                : `${$t('file-manage.delivery-criteria')}：${
                    node.node.deliveryStandardName
                  }`
            "
            background-color="#165dff"
            style="white-space: pre-line"
          >
            <icon-check-circle-fill
              v-if="node.node.deliveryStandardId || node.node.standardId"
              style="
                position: absolute;
                top: 15px;
                left: 9px;
                color: #00d88c;
                font-size: 12px;
              "
            />
          </a-tooltip>

          <file-image
            :file-name="node.node.name"
            :is-sysFile="isSysFolder(node.node.sysType)"
            :is-file="!!node.node.folderId"
          />
        </template>
        <template #title="nodeData">
          <a-input
            ref="addInputRef"
            v-if="nodeData.isAdd"
            v-model="newFolderName"
            style="width: 100%"
            :placeholder="$t('file-manage.need-place-input')"
            @keydown.enter="addFolderRequest(nodeData, newFolderName)"
            @blur="addFolderRequest(nodeData, newFolderName)"
          />
          <a-input
            ref="editInputRef"
            v-else-if="nodeData.isEdit"
            v-model="newFolderName"
            style="width: 100%"
            :placeholder="$t('file-manage.need-place-input')"
            @keydown.enter="renameFolder(nodeData)"
            @blur="renameFolder(nodeData)"
          />
          <a-tooltip v-else :content="nodeData.name">
            <div
              :style="{
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
                width: '100px',
              }"
            >
              {{ nodeData.name }}
            </div>
          </a-tooltip>
        </template>
        <template #extra="nodeData">
          <div
            class="extra"
            v-if="
              !isTopFolder(nodeData.id) &&
              nodeData.id !== 'add' &&
              selectedKeys.includes(nodeData.id)
            "
          >
          </div>
        </template>
      </a-tree>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    nextTick,
    ref,
    defineProps,
    defineEmits,
    PropType,
    toRefs,
  } from 'vue';
  import { useI18n } from 'vue-i18n';

  import { FolderMessage } from '@/api/tree-folder';
  import useFileStore from '@/store/modules/file/index';
  import { useThrottleFn } from '@vueuse/core';

  import { Message } from '@arco-design/web-vue';
  import { encode } from 'js-base64';
  import { storeToRefs } from 'pinia';

  import { getParentFolder } from '@/views/projectSpace/file/hooks/events';
  import { isTopFolder, isSysFolder } from '@/views/projectSpace/file/utils';
  import FileImage from './image-file.vue';

  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';

  const knowledgeStore = useKnowledgeBaseNewStore();
  const { referenceModal } = storeToRefs(knowledgeStore);
  const { selectedKeys, expandedKeys } = toRefs(referenceModal.value);

  const { t } = useI18n();
  const fileStore = useFileStore();
  const props = defineProps({
    folderData: {
      type: Array as PropType<FolderMessage[]>,
      default() {
        return [];
      },
    },
  });
  const emits = defineEmits([
    'nodeClick',
    'refresh',
    'handleUpload',
    'handleShare',
    'handleDownload',
  ]);

  const folderTree = ref<any>();

  // const { newFolderName, selectedKeys, expandedKeys } = storeToRefs(fileStore);

  const expendNodes = (nodeKeys: any) => {
    if (nodeKeys.length) {
      nodeKeys.forEach((key: string) => {
        folderTree.value?.expandNode(key);
      });
    }
  };

  // 点击树节点事件
  const nodeClick = async (
    selectedNodes: (string | number)[],
    nodeData: any
  ) => {
    const nodeInfo = nodeData.node;
    knowledgeStore.setSelectedKeys([nodeInfo.id as string]);

    if (nodeInfo.isAdd || nodeInfo.isEdit) {
      return;
    }

    emits(
      'nodeClick',
      {
        nodes: selectedNodes,
        nodeInfo,
      },
      () => expendNodes(selectedNodes)
    );
  };

  function setNodeSelected(nodeData: FolderMessage) {
    nodeClick([nodeData.id as string], { node: nodeData });
  }

  // 节点展开或关闭
  const nodeExpand = (expandKeys: string[]) => {
    fileStore.setExpandedKeys(expandKeys);
  };

  const addInputRef = ref();
  const editInputRef = ref();

  const currentData = ref();
  function eventsHandler(eventType: string, data?: any) {
    switch (eventType) {
      case 'add-folder':
        currentData.value = data;
        nextTick(() => {
          folderTree.value.expandNode(data.id);
        }).then(() => {
          addInputRef.value.focus();
        });
        break;
      case 'rename-folder':
        nextTick(() => {
          editInputRef.value.focus();
        });
        break;
      case 'upload':
        emits('handleUpload', 0);
        break;
      case 'download':
        emits('handleDownload');
        break;
      case 'delete-folder':
        setNodeSelected(
          getParentFolder(props.folderData, data.id, props.folderData)
        );
        break;
      default:
        break;
    }
  }

  defineExpose({ setNodeSelected });
</script>

<style scoped lang="less">
  :deep(.arco-tree-node-title:hover) {
    background-color: #e8f2ff;
  }
  :deep(.arco-tree-node-selected) {
    background-color: #e8f2ff;
  }
  :deep(.arco-tree-node:hover) {
    background-color: #e8f2ff;
  }
  .file-tree {
    width: 100%;
    max-height: 600px;
    .tree-content {
      //border: 1px solid red;
      height: calc(100% - 20px);
      margin-left: 16px;
      margin-right: 16px;
      padding-top: 20px;
      overflow: auto;
    }
  }
  .extra {
    width: 30px;
  }
</style>
