<template>
  <a-spin ref="spin" :loading="tableLoading" class="folder-table-panel">
    <a-table
      ref="folderTable"
      :bordered="false"
      :columns="columns"
      :data="tableData"
      :scroll="{ x: 800, y: 'calc(100% - 142px)' }"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      row-key="id"
      :pagination="{
        showTotal: true,
        showPageSize: true,
        showJumper: true,
        current: currentPage,
        pageSize: pageSize,
        total: pageTotal,
        pageSizeOptions: [10, 20, 50, 100],
      }"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @selection-change="selectionChange"
    >
      <template #title="{ record }">
        <div class="table-name">
          <file-image
            :file-name="record.title"
            :is-sysFile="false"
            :is-file="!record.isFolder"
            :attatchment-type="record.type"
            style="margin-right: 8px"
          />
          <a-tooltip :content="record.title">
            <span
              style="
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
              @click="clickNameHandle(record)"
              >{{ record.title }}</span
            >
          </a-tooltip>
        </div>
      </template>

      <template #optional="{ record }">
        <a-button
          :disabled="record.abandon === 1 || isSysFolder(record.sysType)"
          type="text"
          size="small"
          @click="handleDownload(record)"
        >
          {{ t('file-manage.table-download') }}
        </a-button>
        <TableOptBtns
          style="margin-left: 10px"
          v-if="
            (hiddenSlot !== 2 &&
              !(
                [1, 2, 3, 4].includes(record.sysType) &&
                record.path?.split('/').length === 4
              )) ||
            record.type === 'SHARED'
          "
          :row-data="record"
          @event-handle="optEventsHandle"
        />
      </template>
    </a-table>
  </a-spin>
  <!-- 图片预览 -->
  <ImgViewer v-if="imgViewModel.visible" :viewModal="imgViewModel"></ImgViewer>
  <move-dialog
    v-model:visible="moveModal"
    :title="$t('file-manage.select-target-folder')"
    :ok-function="beforeMoveOkHandler"
    :show-type="[]"
    :dis-check-hierarchy="[1]"
    check-type="single"
    output-type="id"
    is-clear-key
  ></move-dialog>
</template>

<script setup lang="ts">
  import { defineEmits, ref, computed, inject, Ref } from 'vue';
  import { useI18n } from 'vue-i18n';

  import FileImage from '../image-file.vue';

  import useFileStore from '@/store/modules/file/index';
  import { storeToRefs } from 'pinia';
  import ImgViewer from '@/components/imgView/index.vue';

  import { useRoute } from 'vue-router';
  import modelViewBim from '@/utils/common/view';
  import { handleSingleFile } from '@/views/projectSpace/file/hooks/events';

  import MoveDialog from '@/components/tree-folder/index.vue';
  import { isSysFolder } from '@/views/projectSpace/file/utils';
  import {
    attachmentDownload,
    AttachParams,
    getAttachList,
    moveAttachment,
  } from '@/views/projectSpace/file/api';
  import { Message } from '@arco-design/web-vue';
  import TableOptBtns from './table-opt-btns.vue';
  import { AttachmentDto, AttachTableView } from '@/store/modules/file/types';
  import { fileTransToTableView, folderTransToTableView } from './utils';
  import { download } from '@/utils/file';

  const { t } = useI18n();

  const fileStore = useFileStore();
  const route = useRoute();

  const { hiddenSlot, tableLoading, selectedTableRowkeys, attachPathList } =
    storeToRefs(fileStore);

  const emits = defineEmits(['handleMove', 'handleDownload']);

  const moveModal = ref(false);

  const columns = computed(() => {
    return [
      {
        title: t('file-manage.name'),
        dataIndex: 'title',
        slotName: 'title',
        sortable: {
          sortDirections: ['ascend', 'descend'],
        },
        width: 300,
        fixed: 'left',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: t('file-manage.team'),
        width: 140,
        dataIndex: 'teamName',
        slotName: 'teamId',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: t('file-manage.regenerator'),
        width: 90,
        dataIndex: 'updateName',
      },
      {
        title: t('file-manage.update-date'),
        dataIndex: 'updateTime',
        sortable: {
          sortDirections: ['ascend', 'descend'],
          defaultSortOrder: 'descend',
        },
        width: 120,
      },
      {
        title: t('file-manage.operation'),
        slotName: 'optional',
        width: 80,
        fixed: 'right',
      },
    ];
  });

  const tableData = ref<AttachTableView[]>([]);

  const folderTable = ref();
  const searchForm = inject<Ref<AttachParams>>('searchForm');

  function selectionChange(rowkeys: string[]) {
    fileStore.setSelectedTableRowkeys(rowkeys);
  }

  function handlePageChange(page: number) {
    searchForm!.value.pageNo = page;
    refreshTableData();
  }
  function handlePageSizeChange(pageSize: number) {
    searchForm!.value.pageSize = pageSize;
    refreshTableData();
  }

  const currentPage = ref(1);
  const pageSize = ref(10);
  const pageTotal = ref(0);
  // 刷新表格数据
  async function refreshTableData() {
    fileStore.setCurrentAttach({} as AttachTableView);
    fileStore.setAttachPathList([{ name: '附件列表', id: '' }]);
    tableLoading.value = true;

    const params = { ...searchForm!.value };
    currentPage.value = params.pageNo;
    pageSize.value = params.pageSize;
    if (
      searchForm!.value.switchTypeView &&
      searchForm!.value.switchTypeView.length === 2
    ) {
      params.switchType = '';
    } else {
      params.switchType = searchForm!.value.switchTypeView![0];
    }
    delete params.switchTypeView;

    const res = await getAttachList(params);
    const { data } = res;
    pageTotal.value = data.total;
    tableData.value = ((data.list as AttachmentDto[]) || []).map((item) => {
      return folderTransToTableView(item);
    });
    folderTable?.value.selectAll(false);
    fileStore.setSelectedTableRowkeys([]);
    tableLoading.value = false;
  }

  // 文件夹下钻
  function enterFolder(folder: AttachTableView) {
    tableLoading.value = true;
    fileStore.setCurrentAttach(folder);

    if (folder.child) {
      const folderList = folder.child.map((item) =>
        folderTransToTableView(item)
      );
      if (folder.attachmentList) {
        const fileList = folder.attachmentList.map((item) =>
          fileTransToTableView(item)
        );
        tableData.value = [...folderList, ...fileList];
      }
    }

    folderTable?.value.selectAll(false);
    fileStore.setSelectedTableRowkeys([]);
    tableLoading.value = false;
  }

  // 面包屑点击刷新表格数据
  async function breadcrumbChange(pathList: { name: string; id: string }[]) {
    if (pathList.length === 1) {
      refreshTableData();
    } else {
      tableLoading.value = true;
      const params = { ...searchForm!.value };
      if (
        searchForm!.value.switchTypeView &&
        searchForm!.value.switchTypeView.length === 2
      ) {
        params.switchType = '';
      } else {
        params.switchType = searchForm!.value.switchTypeView![0];
      }
      delete params.switchTypeView;
      const res = await getAttachList(params);
      const { data } = res;

      if (data.list) {
        const dataList = ((data.list as AttachmentDto[]) || []).map((item) => {
          return folderTransToTableView(item);
        });
        const new_pathList = pathList.slice(1);
        if (new_pathList.length) {
          new_pathList.reduce((tableArr, item, index) => {
            const folder = tableArr.find(
              (_: AttachTableView) => _.id === item.id
            )!;
            console.log(tableArr, folder, 274);
            if (index === new_pathList.length - 1) {
              enterFolder(folder);
            } else {
              return folder?.child.map((item) => {
                return folderTransToTableView(item);
              });
            }
          }, dataList as AttachTableView[]);
        }
      }
      tableLoading.value = false;
    }
  }

  // 单文件下载
  async function handleDownload(record: AttachTableView) {
    emits('handleDownload');
    Message.info(t('file-manage.wait-downloading'));

    if (record.isFolder) {
      // 文件夹
      let zipFileName = `${record.title}.zip`;

      const downloadRes: any = await attachmentDownload(
        record.id,
        record.type,
        zipFileName
      );
      download({ name: zipFileName }, downloadRes.data);
    } else {
      // 文件
      handleSingleFile({
        name: record.title,
        fileToken: record.fileToken,
      });
    }
  }

  const imgViewModel = computed(() => fileStore.imgViewModal);
  const projectId = route.params.projectId as string;

  async function clickNameHandle(record: AttachTableView) {
    if (record.isFolder) {
      // 文件夹点击事件，进入下一层

      fileStore.setAttachPathList([
        ...attachPathList.value,
        { name: record.title, id: record.id },
      ]);
      enterFolder(record);
      return;
    }

    // 文件点击事件，查看文件
    let needParams: any = {
      noIssue: [1, 2, 3, 4].includes(hiddenSlot.value),
    };
    const file = {
      fileId: record.id,
      name: record.title,
      fileToken: record.fileToken,
    };
    modelViewBim(file, projectId, needParams);
  }

  // 批量操作
  const checkedTableRows = computed(() =>
    tableData.value.filter(
      (row) =>
        selectedTableRowkeys.value.findIndex((key) => key === row.id) !== -1
    )
  );

  let beforeMoveOkHandler = (treeDataPromise: () => Promise<any>) =>
    Promise.resolve(true);

  function handleMoveParams(records: AttachTableView[]) {
    const fileIds: string[] = [];
    const folderIds: { directoryId: string; type: string }[] = [];
    records.forEach((row: AttachTableView) => {
      if (row.isFolder) {
        folderIds.push({ directoryId: row.id, type: row.type });
      } else {
        fileIds.push(row.id);
      }
    });

    fileStore.setAttachMoveIds({
      attachmentFileIdList: fileIds,
      moveParamList: folderIds,
    });
  }

  //移动前的设置
  function preMoveSetting(records: AttachTableView[]) {
    handleMoveParams(records);

    beforeMoveOkHandler = async function moveRequest(
      treeDataPromise: () => Promise<any>
    ): Promise<boolean> {
      const parentId: string = await treeDataPromise();
      if (!parentId) {
        Message.error(t('file-manage.select-target-folder'));
        return false;
      }
      const attachMoveIds = fileStore.attachMoveIds;

      const result: any = await moveAttachment({
        folderId: parentId,
        ...attachMoveIds,
      });

      folderTable?.value.selectAll(false);
      fileStore.setSelectedTableRowkeys([]);
      if (result.code === 8000000) {
        Message.success(t('file-manage.success'));
        return true;
      }
      return false;
    };
    moveModal.value = true;
  }

  function batchMoveHandle() {
    preMoveSetting(checkedTableRows.value);
  }

  function optEventsHandle(event: string, record: AttachTableView) {
    if (event === 'move') {
      preMoveSetting([record]);
    }
  }
  refreshTableData();
  defineExpose({ batchMoveHandle, refreshTableData, breadcrumbChange });
</script>

<style scoped lang="less">
  :deep(.arco-table-content .arco-scrollbar:nth-child(2)) {
    height: 100%;
  }
  :deep(.arco-table-header + .arco-scrollbar-track-direction-horizontal) {
    display: none;
  }
  .folder-table-panel {
    width: 100%;
    height: 100%;
    padding: 0 20px;
  }
  .table-name {
    display: flex;
    align-items: center;
    color: rgb(22, 93, 255);
    cursor: pointer;
  }
  :deep(.arco-btn-size-small) {
    padding: 0 0;
    margin-right: 8px;
    font-size: 13px;
  }
  :deep(.arco-table-container) {
    height: calc(100% - 40px);
  }
</style>
