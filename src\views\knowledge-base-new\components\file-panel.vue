<template>
  <a-layout class="project-file-panel">
    <a-layout-sider
      :width="260"
      :resize-directions="['right']"
      class="file-tree-wrap"
    >
      <FileTree
        v-if="currentProjectId"
        ref="fileTree"
        :folder-data="allTreeData"
        @node-click="nodeClick"
        @refresh="treeRefresh"
      ></FileTree>
    </a-layout-sider>
    <a-layout-content class="content-panel">
      <div class="content-header">
        <div class="filter-buttons">
          <FileFilter v-if="currentProjectId" ref="grandChildRef" />
        </div>
        <div class="header-buttons">
          <div class="header-title">
            <img src="@/assets/images/table-title.png" />
            <div class="text">
              {{ currentFolder?.name }}
            </div>
            <Breadcrumb class="breadcrumb" @expend-folder="expendFolderNode" />
          </div>
        </div>
        <a-divider />
      </div>

      <!-- <div style="padding: 12px; display: flex"> </div> -->
      <div class="folder-table-wrap">
        <FolderTable ref="folderTable" @expend-folder="expendFolderNode" />
      </div>
      <div class="folder-table-wrap"> </div>
    </a-layout-content>
  </a-layout>
</template>

<script setup lang="ts">
  import { computed, nextTick, ref, toRefs, watch } from 'vue';
  import { FolderMessage, getChildFolderList } from '@/api/tree-folder';

  import FileTree from '@/views/knowledge-base-new/components/file-tree.vue';

  import Breadcrumb from '@/views/knowledge-base-new/components/breadcrumb.vue';

  import FileFilter from '@/views/knowledge-base-new/components/filter-file.vue';
  import FolderTable from './folder-table.vue';

  import useFileStore from '@/store/modules/file/index';
  import { finishTaskParams } from '@/store/modules/upload-file/types';
  import { storeToRefs } from 'pinia';
  import { isSysFolder, isTopFolder } from '@/views/projectSpace/file/utils';
  import { usePrjPermissionStore } from '@/store';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';

  const knowledgeStore = useKnowledgeBaseNewStore();
  const { referenceModal } = storeToRefs(knowledgeStore);
  const { currentProjectId, currentProject, allTreeData } = toRefs(
    referenceModal.value
  );

  // const fileStore = useFileStore();
  const prjStore = usePrjPermissionStore();

  const currentFolder = computed(() => referenceModal.value.currentFolder);
  // const { allTreeData: allFolderData, selectedKeys } = storeToRefs(fileStore);

  const { teamList } = storeToRefs(prjStore);

  const getFolder = (type = 'WIP', parentId = '0') => {
    const projectId = (currentProjectId.value as string) || '';
    return getChildFolderList(projectId, '', type, parentId);
  };

  const fileTree = ref();

  function expendFolderNode(record: FolderMessage) {
    fileTree.value.setNodeSelected(record);
  }

  const getWIPFolder = async () => {
    const res = await getFolder('WIP');
    if (res.status) {
      const list = res.data?.list || [];
      // 从里面过滤掉系统文件夹
      const filteredList = list.filter((item: FolderMessage) => {
        return !isSysFolder(item.sysType);
      });
      allTreeData.value[0].children = filteredList;
      // allTreeData.value = filteredList;
    }
    fileTree.value.setNodeSelected(allTreeData.value[0]);
  };

  const getSharedFolder = async () => {
    const res = await getFolder('SHARED');
    if (res.status) {
      const list = res.data?.list || [];
      allTreeData.value[1].children = list;
    }
  };

  const getPublishedFolder = async () => {
    const res = await getFolder('PUBLISHED');
    if (res.status) {
      allTreeData.value[2].children = (res.data?.list as []) || [];
    }
  };

  const init = () => {
    // getWIPFolder();
    // getSharedFolder();
    // getPublishedFolder();
  };
  init();

  const nodeClick = async (data: any, callback: () => void) => {
    const { nodeInfo } = data;

    if (isTopFolder(nodeInfo.id)) {
      const res = await getFolder(nodeInfo.name);
      if (res.status) {
        const list = res.data?.list || [];
        // 从里面过滤掉系统文件夹
        const filteredList = list.filter((item: FolderMessage) => {
          return !isSysFolder(item.sysType);
        });
        nodeInfo.children = filteredList;
      }
    } else {
      const res = await getFolder(nodeInfo.type, nodeInfo.id);
      if (res.status) {
        const list = res.data?.list || [];
        // 从里面过滤掉系统文件夹
        const filteredList = list.filter((item: FolderMessage) => {
          return !isSysFolder(item.sysType);
        });
        nodeInfo.children = filteredList;
      }
    }
    knowledgeStore.setCurrentFolder(nodeInfo);
    await nextTick();
    callback();
  };
  async function treeRefresh(current: any) {
    const res = await getFolder(current.type, current.id);
    if (res.status) {
      const filteredList = res?.data?.list.filter((item: FolderMessage) => {
        return !isSysFolder(item.sysType);
      });
      current.children = filteredList;
    }
  }
  // 使用watch监听currentProjectId，有值就调用接口
  watch(currentProjectId, (newVal, oldVal) => {
    if (newVal) {
      knowledgeStore.setTableData([]);
      getWIPFolder();
    }
  });
  const grandChildRef = ref();
  defineExpose({
    clearSearch: () => {
      grandChildRef.value?.clearSearch();
    },
  });
</script>

<style scoped lang="less">
  :deep(.arco-resizebox-trigger-icon-wrapper) {
    background-color: #eee;
  }
  :deep(.arco-layout-sider) {
    min-width: 150px;
    max-width: 600px;
  }
  .project-file-panel {
    border: 1px solid #d9d9d9;
    height: 600px;
    border-radius: 8px;
    .file-tree-wrap {
      border-radius: 8px;
      height: 100%;
      border-right: 1px solid #d9d9d9;
    }
    .content-panel {
      //border: 1px solid red;
      flex: 1;
      position: relative;
      overflow: hidden;
      .content-header {
        padding: 0 12px;
        .header-buttons {
          height: 64px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          overflow: hidden;

          .header-title {
            width: 100%;
            flex: 1;
            display: flex;
            overflow: hidden;
            img {
              width: 20px;
              height: 20px;
              margin-right: 8px;
              margin-top: 7px;
            }
            .text {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              max-width: 120px;
              font-size: 18px;
              font-weight: bold;
              line-height: 32px;
              margin-right: 16px;
            }
          }
        }
      }

      .breadcrumb {
        margin-left: 20px;
      }
      .folder-table-wrap {
        height: calc(100% - 47px);
        overflow: hidden;
      }
    }
  }
  .filter-buttons {
    width: 260px;
    margin-top: 10px;
  }
</style>
