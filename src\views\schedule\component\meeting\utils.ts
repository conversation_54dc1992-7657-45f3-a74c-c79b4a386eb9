function arraysEqualWithOrder(arr1: string[], arr2: string[]) {
  if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
  if (arr1.length !== arr2.length) return false;

  return arr1.every((val, idx) => val === arr2[idx]);
}

// 基本信息是否变化
export function isBasicInfoChanged(detail: any, title: string, form: any) {
  // 判断标题是否一致
  if (detail.title !== title) return true;
  // 时间是否相同（年月日时分）
  const formatTime = (str: any) => (str || '').slice(0, 16);
  if (formatTime(form.planEndTime) !== formatTime(detail.planEndTime)) {
    return true;
  }
  if (formatTime(form.planStartTime) !== formatTime(detail.planStartTime)) {
    return true;
  }

  console.log('[ form ] >', form);
  // 判断组织人是否发生变化
  const formOrganizer = Array.isArray(form.organizer)
    ? form.organizer[0]
    : form.organizer;

  if (formOrganizer && detail.organizerUsername !== formOrganizer) {
    return true;
  }

  // 判断参与人是否相同
  let participants;
  if (detail.projectId) {
    participants = detail.participants?.map(
      (item: { username: string }) => item.username
    );
  } else {
    participants = detail.users?.map(
      (item: { userFullname: string }) => item.userFullname
    );
  }
  if (!arraysEqualWithOrder(participants, form.participants)) {
    return true;
  }

  // 项目会议才判断归属团队是否相同
  if (detail.projectId && detail.teamId !== form.teamId) {
    return true;
  }

  // 判断会议方式是否相同
  if (detail.meetingFormat !== form.meetingFormat) {
    return true;
  }

  // 判断会议地点是否相同
  if (detail.location !== form.location) {
    return true;
  }

  // 判断重复方式是否相同
  if (detail.repeatType !== form.repeatType) {
    return true;
  }

  // 个人会议才判断归属日历是否相同
  if (!detail.projectId && detail.schedulePanelId !== form.schedulePanelId) {
    return true;
  }

  // 判断描述是否相同
  if (detail.content !== form.content) {
    return true;
  }

  return false;
}

export function isChildrenChanged(detailChildren: any, agencyList: any) {
  if (detailChildren?.length !== agencyList?.length) return false;
  const formatTime = (str) => (str || '').slice(0, 16);

  for (let i = 0; i < agencyList.length; i++) {
    const node = agencyList[i];
    const detail = detailChildren[i];
    // title 不一致
    if (node.title !== detail.title) return true;

    // 时间不一致（年月日时分）
    if (formatTime(node.planEndTime) !== formatTime(detail.planEndTime))
      return true;

    // chargePersonId 不一致
    if (node.chargePersonId !== detail.chargePersonId) return true;
  }

  return false;
}

// 评论信息是否变化
export function isCommentChanged(form: any) {
  if (form.description) return true;
  return false;
}
