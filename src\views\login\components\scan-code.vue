<template>
  <div class="scan-code">
    <!-- <a-tabs class="scan-code-tab" default-active-key="1">
      <a-tab-pane key="1" :title="$t('login.form.loginByJJT')">
        <div class="qr-box">
          <div id="ercodeShow"></div>
          <div class="qr-text">{{ $t('login.form.loginByJJT.tips') }}</div>
          <a-link @click="changeLogin(LoginMethods.password)">{{
            $t('login.form.backLogin')
          }}</a-link>
        </div>
      </a-tab-pane>
    </a-tabs> -->
    <div class="qr-box">
      <div id="ercodeShow"></div>
      <!-- <a-link @click="changeLogin(LoginMethods.register)">{{ $t('login.register.link') }}</a-link> -->
    </div>
    <!-- <div class="qr-text">{{ $t('login.form.scan.tips') }}</div> -->

  </div>
</template>

<script setup lang="ts">
  import { getThird } from '@/api/user';
  import LoginMethods from '../constant';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const emit = defineEmits(['changeLogin']);
  const changeLogin = (method: LoginMethods) => {
    console.log('changeLogin called with method:', method);
    emit('changeLogin', method);
  };

  // interface WwLoginParams {
  //   id: string;
  //   appid: string;
  //   agentid: string;
  //   redirect_uri: string;
  //   state?: string;
  //   href?: string;
  //   style?: string;
  // }

  const wwLogin = (originUrl: string) => {
    const frame = document.createElement('iframe');
    // frame.src = `${originUrl}&href=${window.location.origin}/wx-css.css`;
    frame.src = originUrl;
    frame.className = 'qr-frame';
    frame.width = '350px';
    frame.height = '350px';
    const el = document.getElementById('ercodeShow') as HTMLIFrameElement;
    el.innerHTML = '';
    el.appendChild(frame);
    frame.onload = () => {
      if (frame.contentWindow?.postMessage && window.addEventListener) {
        window.addEventListener('message', (event) => {
          let host = 'https://jjt.ccccltd.cn';
          const hostArr = host.split(':');
          if (hostArr[1] === '80') [host] = [hostArr[0]];
          if (event.data && event.origin.indexOf(host) > -1) {
            window.location.href = event.data;
          }
        });
        frame.contentWindow?.postMessage('ask_usePostMessage', '*');
      }
    };
  };
  getThird('JJT').then((res: any) => {
    // console.log('get-url', res);
    wwLogin(res.data as string);
  });
  // 企业微信登录获取第三方链接url，可判断系统是否支持扫码登录
  // getThird('WECHAT_ENTERPRISE').then((res: any) => {
  //   // console.log('get-url', res);
  //   wxLogin(res as string);
  // });

  // 企业微信登录
  // const wxLogin = (originUrl: string) => {
  //   const reg =
  //     /https:\/\/open.work.weixin.qq.com\/wwopen\/sso\/qrConnect\?appid=(.*?)&agentid=(.*?)&redirect_uri=(.*?)&state=(.*?)$/;
  //   // const list = originUrl.match(reg) || [];
  //   // const appid = list[1];
  //   // const agentid = list[2];
  //   // const redirectUri = encodeURIComponent(list[3]);
  //   // const state = list[4];
  //   const frame = document.createElement('iframe');
  //   frame.src = `${originUrl}&href=${window.location.origin}/wx-css.css`;
  //   frame.className = 'qr-frame';
  //   frame.width = '240px';
  //   frame.height = '240px';
  //   const el = document.getElementById('ercodeShow') as HTMLElement;
  //   el.innerHTML = '';
  //   el.appendChild(frame);
  //   frame.onload = () => {
  //     if (frame.contentWindow?.postMessage && window.addEventListener) {
  //       window.addEventListener('message', (event) => {
  //         if (event.origin === 'https://open.work.weixin.qq.com') {
  //           window.location.href = event.data;
  //         }
  //         // "http://139.9.139.61:88/sys-auth/oauth/callback/WECHAT_ENTERPRISE?code=Mrv5Kr4NbAOpGL79GozuHCQljSolCnND8ZqsrEVYjUk&state=bfe534223668c9a39808dadefc6059c7&appid=wwcbd09cea76e76e7d"
  //         // console.log(event);
  //         // if (event.data) {
  //         //   let url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_base&state=${state}&agentid=${agentid}#wechat_redirect`
  //         //   // window.location.href = url
  //         // }
  //       });
  //       frame.contentWindow?.postMessage('ask_usePostMessage', '*');
  //     }
  //   };
  // };
</script>

<script lang="ts">
  export default {
    name: 'ScanCode',
  };
</script>

<style lang="less" scoped>
  .scan-code {
    height: 340px;
    margin-top: 16px;
    :deep(.arco-tabs-nav) {
      &::before {
        display: none;
      }
    }
    :deep(.arco-tabs-nav-tab) {
      justify-content: center;
    }
    .qr-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      // todo:先不隐藏交建通下面的提示 
      // height: 270px;
      // overflow: hidden;
    }
    .qr-text {
      font-size: 16px;
      // color: var(--color-text-3);
      margin: 8px 0;
      text-align: center;
    }
    .back-text {
      font-size: 16px;
      color: rgb(var(--primary-6));
      line-height: 24px;
      cursor: pointer;
    }
    #ercodeShow {
      width: 350px;
      height: 350px;
      margin-bottom: 6px;
    }
  }
</style>

<style>
  .qr-frame {
    border: none;
    margin-top: -41px;
  }
</style>
