<template>
  <div class="project-file-panel">
    <div class="file-tree-wrap">
      <FileTree
        ref="fileTree"
        :folder-data="allFolderData"
        @node-click="nodeClick"
        @refresh="treeRefresh"
        @handle-upload="handleUpload"
        @handle-download="handleDownload"
      ></FileTree>
    </div>
    <div class="content-panel">
      <div class="content-header">
        <div class="header-buttons">
          <div class="header-title">
            <img src="@/assets/images/table-title.png" />
            <div class="text">
              {{ i18FolderName(currentFolder) }}
            </div>
            <Breadcrumb class="breadcrumb" @expend-folder="expendFolderNode" />
          </div>
          <div class="filter-buttons">
            <FileFilter />
          </div>
        </div>
        <a-divider />
      </div>

      <div style="padding: 12px 20px; display: flex">
        <header-buttons @expend-folder="expendFolderNode" />
      </div>

      <div class="folder-table-wrap">
        <FolderTable
          ref="folderTable"
          @expend-folder="expendFolderNode"
          @handle-download="handleDownload"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, nextTick, provide, ref } from 'vue';
  import { FolderMessage, getChildFolderList } from '@/api/tree-folder';

  import FileTree from './file-tree.vue';
  import FolderTable from './folder-table.vue';

  import Breadcrumb from '@/views/projectSpace/file/components/file-panel/breadcrumb.vue';

  import FileFilter from './filter-file.vue';

  import useFileStore from '@/store/modules/file/index';
  import { storeToRefs } from 'pinia';
  import { isTopFolder } from '@/views/projectSpace/file/utils';

  import { getUserId } from '@/utils/auth';
  import HeaderButtons from './buttons-header.vue';
  import useI18nHandleName from '@/views/projectSpace/file/hooks/backups';

  const userId = getUserId() || '';
  const fileStore = useFileStore();
  const { i18FolderName } = useI18nHandleName();

  const currentFolder = computed(() => fileStore.currentFolder);
  const { standradTreeData: allFolderData, selectedKeys } =
    storeToRefs(fileStore);

  const getFolder = (type = 'WIP', parentId = '0') => {
    const projectId = localStorage.getItem(`work_last_project_${userId}`) || '';
    return getChildFolderList(projectId, '', type, parentId);
  };

  const uploadModel = ref({ visible: false, type: 0, selectedFolder: {} });
  const TransmitPanelVisible = ref(false);

  const fileTree = ref();
  const transmitType = ref('upload');

  function expendFolderNode(record: FolderMessage) {
    fileTree.value.setNodeSelected(record);
  }

  const getWIPFolder = async () => {
    const res = await getFolder('WIP');
    if (res.status) {
      allFolderData.value[0].children = res.data?.list || [];
    }

    console.log('allFolderData', allFolderData.value);
    fileTree.value.setNodeSelected(allFolderData.value[0]);
  };

  const init = () => {
    // 有浏览记录，还原
    if (selectedKeys.value.length) {
      nextTick(() => {
        fileTree.value.setNodeSelected(currentFolder.value);
      });
      return;
    }
    getWIPFolder();
  };
  init();

  const nodeClick = async (data: any, callback: () => void) => {
    const { nodeInfo } = data;

    if (isTopFolder(nodeInfo.id)) {
      const res = await getFolder(nodeInfo.name);
      if (res.status) {
        nodeInfo.children = res.data?.list || [];
      }
    } else {
      const res = await getFolder(nodeInfo.type, nodeInfo.id);
      if (res.status) {
        nodeInfo.children = res?.data?.list || [];
      }
    }

    fileStore.setCurrentFolder(nodeInfo);
    fileStore.setAllTreeData();
    await nextTick();
    callback();
  };

  function handleUpload(visibleType: number) {
    uploadModel.value = {
      type: visibleType,
      visible: true,
      selectedFolder: fileStore.currentFolder,
    };
  }
  function handleDownload() {
    transmitType.value = 'download';
    TransmitPanelVisible.value = true;
  }

  async function treeRefresh(current: any) {
    const res = await getFolder(current.type, current.id);
    if (res.status) {
      current.children = res?.data?.list || [];
    }
  }

  const shareModal = ref({ show: false, shareType: 'tree', shareData: {} });
  provide('shareModalData', shareModal);

  const folderTable = ref();
</script>

<style scoped lang="less">
  .project-file-panel {
    border: 1px solid #d9d9d9;
    height: calc(100vh - 168px);
    width: 100%;
    // margin-top: 20px;
    border-radius: 8px;
    display: flex;
    .file-tree-wrap {
      height: 100%;
      width: 20.5%;
      border-right: 1px solid #d9d9d9;
    }
    .content-panel {
      //border: 1px solid red;
      flex: 1;
      position: relative;
      overflow: hidden;
      .content-header {
        padding: 0 20px;
        .header-buttons {
          height: 64px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          overflow: hidden;

          .header-title {
            width: 100%;
            flex: 1;
            display: flex;
            overflow: hidden;
            img {
              width: 20px;
              height: 20px;
              margin-right: 8px;
              margin-top: 7px;
            }
            .text {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              font-size: 18px;
              font-weight: bold;
              line-height: 32px;
              margin-right: 16px;
              color: #1d2129;
            }
          }
        }
      }

      .breadcrumb {
        margin-left: 20px;
      }
      .folder-table-wrap {
        height: calc(100% - 105px);
        width: 100%;
        overflow: hidden;
      }
    }
  }
</style>
