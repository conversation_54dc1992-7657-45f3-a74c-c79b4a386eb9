import {
  defineComponent,
  ref,
  h,
  compile,
  computed,
  nextTick,
  watch,
  resolveComponent
} from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter, RouteRecordRaw } from 'vue-router';
import type { RouteMeta } from 'vue-router';
import { useAppStore, useUserStore } from '@/store';
import { listenerRouteChange } from '@/utils/route-listener';
import { openWindow, regexUrl } from '@/utils';
import useMenuTree from './use-menu-tree';
import { getLocalstorage } from '@/utils/localstorage';
import { getUserId } from '@/utils/auth';

export default defineComponent({
  emit: ['collapse'],
  setup() {
    const { t, locale } = useI18n();
    const appStore = useAppStore();
    const router = useRouter();
    const route = useRoute();
    const { menuTree } = useMenuTree();
    const userStore = useUserStore();
    const projectTemplate = computed(() => userStore.projectTemplate);
    const collapsed = computed({
      get() {
        if (appStore.device === 'desktop') return appStore.menuCollapse;
        return false;
      },
      set(value: boolean) {
        appStore.updateSettings({ menuCollapse: value });
      },
    });

    const topMenu = computed(() => appStore.topMenu);
    const openKeys = ref<string[]>([]);
    const selectedKey = computed(() => {
      if (route.meta.hideInMenu && route.matched.length > 1) {
        // 知识库页面，子菜单隐藏，并且有父菜单，选中父菜单（只处理了向上一级）
        return [route.matched[route.matched.length - 2]?.name];
      }
      return [route.name as string];
    });

    const isEn = computed(() => locale.value === 'en-US');

    const goto = async (item: RouteRecordRaw) => {
      if (regexUrl.test(item.path)) {
        openWindow(item.path);
        // selectedKey.value = [item.name as string];
        return;
      }
      // Eliminate external link side effects
      const { hideInMenu, activeMenu } = item.meta as RouteMeta;
      if (route.name === item.name && !hideInMenu && !activeMenu) {
        // selectedKey.value = [item.name as string];
        return;
      }
      const userId = getUserId() || '';
      const lastPrj = getLocalstorage(`last_project_${userId}`);
      let routeParams = {};
      if (
        item.meta?.globalMode?.includes('project') &&
        !route.params.projectId &&
        lastPrj
      ) {
        routeParams = {
          projectId: lastPrj,
        };
      }
      router.push({
        name: item.name,
        params: routeParams,
      });
    };
    const setCollapse = (val: boolean) => {
      if (appStore.device === 'desktop')
        appStore.updateSettings({ menuCollapse: val });
    };

    const renderSubMenu = () => {
      function travel(_route: RouteRecordRaw[], nodes = []) {
        if (_route) {
          _route.forEach((element) => {
            // 在项目模板模式下只显示 project-member 和 project-setting
            if (projectTemplate.value === '1') {
              if (!['project-member', 'project-setting'].includes(element.name as string)) {
                return;
              }
            }
            // This is demo, modify nodes as needed
            const icon = element?.meta?.icon
              ? 
                  h(resolveComponent(element?.meta?.icon), { size: isEn.value ? '18' :'22' })
              : null;
            const node =
              element?.children && element?.children.length !== 0 ? (
                <a-sub-menu
                  key={element?.name}
                  v-slots={{
                    icon,
                    title: t(element?.meta?.locale || '')
                  }}
                >
                  {travel(element?.children)}
                </a-sub-menu>
              ) : (
                <a-menu-item
                  key={element?.name}
                  v-slots={{
                    icon
                  }}
                  onClick={() => goto(element)}
                >
                  {t(element?.meta?.locale || '')}
                </a-menu-item>
              );
            nodes.push(node as never);
          });
        }
        return nodes;
      }
      return travel(menuTree.value);
    };

    return () => (
      <a-menu
        mode={topMenu.value ? 'horizontal' : 'vertical'}
        v-model={[collapsed.value, 'collapsed']}
        v-model={[openKeys.value, 'openKeys']}
        v-model:selected-keys={selectedKey.value}
        accordion={true}
        auto-open={false}
        auto-open-selected={true}
        level-indent={34}
        style="height: 100%;width:100%;"
      >
        {renderSubMenu()}
      </a-menu>
    );
  },
});
