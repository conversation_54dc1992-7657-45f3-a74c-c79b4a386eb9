export default {
  'menu.org.department': 'Department',

  'department.operation.create': 'Create',

  // columns
  'department.columns.index': 'index',
  'department.columns.name': 'Department Name',
  'department.columns.name-require': 'Department name required',
  'department.columns.abbr-require': 'Department abbreviation required',
  'department.columns.superior-department': 'Superior Department',
  'department.columns.superior-department-require': 'This field is required',
  'department.columns.abbr': 'Department Abbr',
  'department.columns.orgType': 'Department OrgType',
  'department.columns.operations': 'Operations',

  'department.columns.operations.create': 'New child',
  'department.columns.operations.edit': 'Edit',
  'department.columns.operations.delete': 'Delete',

  // actions
  'department.actions.refresh': 'refresh',
  'department.actions.density': 'density',
  'department.actions.add': 'add',
  'user-management': 'User Management',
  'selected-users': 'Selected Users',
  'failed-to-get-user-list': 'Failed to get user list',
  'failed-to-get-org-user-list': 'Failed to get organization user list',
  'department.message.add-success': 'Add successful',
  'department.message.edit-success': 'Edit successful',
  'department.message.unknown-operation': 'Unknown operation type',
  'department.message.submit-fail': 'Submit failed',
};
