import axios from 'axios';
import { Paging } from '@/types/global';

export interface NetdiskImportRequestParam {
  sourceFFolderIdList?: number[]; // 源文件夹id数组
  sourceFileIdList?: number[]; // 源文件id数组
  targeFolderId?: number; // 导入目标文件夹id
  targeProjectId?: number; // 导入目标项目id
}
export interface FolderMessage {
  id?: string;
  name?: string;
  englishName?: string;
  idPath?: string;
  path?: string;
  englishPath?: string;
  title?: string;
  projectId?: string;
  teamId?: string;
  userId?: string;
  parentId?: number;
  childId?: string;
  children?: FolderMessage[];
  isFileOrFolder?: number;
  type?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: number;
  sysType?: number;
}
// 网盘初始化
export function getPrivateInit() {
  return axios.get('/cde-collaboration/netdisk/private/init');
}
// 导入文件
export function importFile(data: any) {
  return axios.post('/cde-collaboration/netdisk/private/import', data);
}
// 删除文件夹/文件
export const deleteApi = (data: any) =>
  axios.post('/cde-collaboration/file/batch-delete/private/file', data);
// 项目文件列表
export function getProjectFileList() {
  return axios.get('/cde-collaboration/netdisk/project/listFolders');
}
// 引用项目文件或文件夹
export function relateFolder(data: any) {
  return axios.post('/cde-collaboration/netdisk/project/relateFolder', data);
}
// 取消引用项目文件或文件夹
export function unrelateFolder(data: any) {
  return axios.post('/cde-collaboration/netdisk/project/unrelateFolder', data);
}
export function getChildFolderList(
  projectId?: string,
  type?: string,
  parentId = '0'
) {
  return axios.get<Paging<FolderMessage>>(
    '/cde-collaboration/folder/children',
    {
      params: {
        projectId,
        type,
        parentId,
        pageSize: 9999,
      },
    }
  );
}
