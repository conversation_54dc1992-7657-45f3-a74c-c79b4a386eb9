import axios from 'axios';

export interface TimeNode {
  startTime?: Date;
  endTime?: Date;
  interval?: number;
}

export interface TimeListTypes {
  yearArray?: TimeNode[];
  monthArray?: TimeNode[];
  weekArray?: TimeNode[];
  dayArray?: TimeNode[];
  hourArray?: TimeNode[];
}

interface File {
  name: string;
  abandon?: number | null;
}

export interface BubbleObject {
  name?: string;
  shareName?: string;
  deliveryName?: string;
  user?: string;
  time?: string;
  status?: string;
  files?: File[];
}

export interface UploadPackage {
  bizId?: string;
  createBy?: string;
  createDate?: string;
  deleteFlag?: number;
  entity?: string;
  id?: string;
  isDownload?: boolean;
  processInstanceId?: string;
  processState?: string;
  projectId?: string;
  teamId?: string;
  updateBy?: string;
  updateDate?: string;
}

// 项目信息
export interface ProjectRecord {
  id: string;
  name: string;
  modelEngine: string;
  code: string;
  type: string;
  protemId?: string;
  planStart: string;
  planEnd: string;
  description?: string;
  rangeDate?: Array<string>; // 项目时间临时字段
  actualEnd?: string;
  actualStart?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  deleteFlag?: string;
  isTemplate?: string;
  protemName: string;
  position: string;
  taskEnd?: string;
  coordinate: string;
  elevation: string;
  environmentType: string;
  magnitude: string;
  roadLevel: string;
  safetyLevel: string;
  loadDesign: string;
  projectStatus: string;
  participaUnit: string;
  projectPhase: string;
  projectProperties: string;
  application: Array<string> | string;
}

// 查询里程碑列表
export function queryMilestoneList(projectId: string) {
  return axios.get('/cde-collaboration/milestone/list', {
    params: {
      projectId,
      pageNo: 1,
      pageSize: 9999,
    },
  });
}

export function getProjectProgress(
  projectId: string,
  currentTeamId: string,
  otherTeamId: string
) {
  return axios.get('/cde-collaboration/project/dashboard/progress/accurate', {
    params: {
      projectId,
      currentTeamId,
      otherTeamId,
    },
  });
}

export function getAllTeam(projectId?: string, teamId?: string) {
  return axios.get('/cde-collaboration/team/listAll', {
    params: {
      projectId,
      teamId,
      pageNo: 1,
      pageSize: 9999,
    },
  });
}

export function getSharedFiles(id: string) {
  return axios.get('/cde-collaboration/collaborate/file/page', {
    params: {
      id,
      pageNo: 1,
      pageSize: 9999,
    },
  });
}
export const getDetailTree = (params: { id: string }) => {
  return axios.get('/cde-collaboration/collaborate/sharedFileTree', { params });
};
export function getDeliveryFiles(id: string) {
  return axios.get('/cde-collaboration/delivery/file/page', {
    params: {
      id,
      pageNo: 1,
      pageSize: 9999,
    },
  });
}

export function consumeSharedFiles(
  collaborationId: string,
  projectId: string,
  teamId: string
) {
  return axios.post('/cde-collaboration/collaborate/consume', {
    collaborationId,
    projectId,
    teamId,
  });
}

export function getShareDetail(params: any) {
  return axios.get('/cde-collaboration/collaborate/detail', params);
}

export function getDeliveryDetail(params: any) {
  return axios.get('/cde-collaboration/delivery/detail', params);
}

export function fileDownload(file: any) {
  return axios.get('/sys-storage/download', {
    params: {
      f8s: file.fileToken,
    },
    responseType: 'blob',
    timeout: 0,
  });
}

export function fileZipDownload(filelist: any) {
  return axios.post('/sys-storage/zip', filelist, {
    responseType: 'blob',
    timeout: 0,
  });
}

// 获取批量下载文件信息
export function batchDownloadInfo(data: object) {
  return axios.post('/cde-collaboration/file/getBatchDownloadInfo', data);
}

// 查询项目详情
export function queryProjectDetail(params: object) {
  return axios.get('/cde-collaboration/project/detail', {
    params,
  });
}

// 查询团队及其成员（查询收件人）
export function queryTeamListAll(id: any) {
  return axios.post(`/cde-collaboration/team/all-team?id=${id}`);
}

// 计算文件夹内文件的数量
export const getDirectoryFileCounts = (directory: any) => {
  let total = 0;
  if (directory.children?.length) {
    directory.children.forEach((item: any) => {
      if (item.isFileOrFolder === 1) {
        total += 1;
      } else {
        total += getDirectoryFileCounts(item);
      }
    });
  }
  return total;
};

export const getDirectoryFileIds = (directory: any[]): any[] => {
  const total: any[] = [];
  if (directory.length) {
    directory.forEach((e) => {
      if (e.isFileOrFolder === 1 && e.abandon !== 1) {
        total.push(e.id);
        // console.log('id: ', e.id);
      } else if (e.isFileOrFolder === 0 && e.children.length) {
        const childIds = getDirectoryFileIds(
          e.children.filter((child: any) => child.abandon !== 1)
        );
        total.push(...childIds);
      }
    });
  }

  return total;
};

// 查询第三方系统收件人
export function queryThirdRecipient(params: any) {
  return axios.get('/cde-collaboration/treed/delivery-person', {
    params,
  });
}

// 获取用户在某项目下的teams和权限
export const getUserTeamsInPrj = (projectId: string) => {
  return axios.get('/cde-collaboration/team/getTeams', {
    params: { projectId },
  });
};
