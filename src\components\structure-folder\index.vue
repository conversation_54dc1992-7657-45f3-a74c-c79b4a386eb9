<template>
  <div class="file-folder">
    <div
      v-for="item in file.children"
      :key="`${item.id}-${file.id}`"
      class="folder"
      @click="intoFolder(item)"
    >
      <file-image
        :file-name="item.name"
        :is-file="false"
        style="width: 40px; height: 40px; border-radius: 4px; margin-top: 6px"
      />
      <div class="file-text">
        <div class="file-name">{{ item.name }}</div>
      </div>
    </div>
    <div
      v-for="(item, index) in file.files"
      :key="`${item.id}-${file.id}`"
      @click="openFile(item)"
    >
      <div
        v-if="index + file.children.length < 6 || fileListExpandStatus"
        class="file"
      >
        <file-image
          :file-name="item.name"
          :is-file="true"
          style="width: 40px; height: 40px; border-radius: 4px; margin-top: 6px"
        />
        <div class="file-text">
          <div class="file-name">{{ item.name }}</div>
          <span class="file-version">{{ 'V' + item.version }}</span>
        </div>
        <span
          v-if="!modelElse.includes(item.name.split('.')[1])"
          class="icon-download"
          @click.stop="downloadFile(item)"
          ><icon-download size="14"
        /></span>
      </div>
    </div>
    <div
      v-if="fileListExpandVisible"
      class="expend-btn"
      @click="changeListExpand"
      ><icon-double-down v-if="!fileListExpandStatus" />
      <icon-double-up v-else /><span
        v-if="!fileListExpandStatus"
        style="
          font-size: 12px;
          display: inline-block;
          margin-left: 5px;
          line-height: 20px;
        "
        >{{ $t('task.view-more') }}</span
      ><span
        v-else
        style="
          font-size: 12px;
          display: inline-block;
          margin-left: 5px;
          line-height: 20px;
        "
        >{{ $t('task.pack-up') }}</span
      ></div
    >
  </div>

  <!-- 图片预览 -->
  <imgViewer v-if="imgViewModal.visible" :view-modal="imgViewModal"></imgViewer>
</template>

<script lang="ts" setup>
  import FileImage from '@/views/projectSpace/home/<USER>/image-file.vue';
  import { modelElse } from '@/utils/BIM-Engine/XBase/format';
  import modelViewBim from '@/utils/common/view';
  import { useRoute } from 'vue-router';
  import { computed, reactive, ref } from 'vue';
  import { fileDownload } from '@/api/file';
  import { download } from '@/utils/file';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import imgViewer from '@/components/imgView/index.vue';
  import { last } from 'lodash';
  import imgJson from '@/config/imgType.json';

  const imgViewModal = reactive({
    visible: false,
    title: '',
    fileToken: ' ',
  });

  const { t } = useI18n();
  const route = useRoute();
  const props = defineProps({
    file: {
      type: Object,
      default() {
        return {};
      },
    },
    sourceFile: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  const emits = defineEmits(['fileChange']);

  // 当前选中的文件夹
  const currentFolder = ref<any>();
  const intoFolder = (file) => {
    emits('fileChange', file);
    currentFolder.value = file;
  };
  const openFile = (file) => {
    modelView(file);
  };
  const fileListExpandStatus = ref(false);
  const fileListExpandVisible = computed((): boolean => {
    let result = false;
    // eslint-disable-next-line no-unsafe-optional-chaining
    const len = props.file.children?.length + props.file.files?.length || 0;
    if (len > 6) {
      result = true;
    }
    return result;
  });
  const changeListExpand = () => {
    if (fileListExpandStatus.value) {
      fileListExpandStatus.value = false;
    } else {
      fileListExpandStatus.value = true;
    }
  };
  // 大象云文件预览
  const modelView = async (record: any) => {
    const type = last(record.name.split('.')) as string;
    const isImgType: boolean = imgJson.includes(type);
    // 图片预览
    if (isImgType) {
      imgViewModal.visible = true;
      imgViewModal.title = record.name || '';
      imgViewModal.fileToken = record.fileToken || '';
    } else {
      const needParams = {
        noIssue: [1, 2, 3, 4].includes(currentFolder.value?.sysType),
      };
      modelViewBim(record, route.params.projectId as string, needParams);
    }
  };
  const downloadFile = (file: any) => {
    file.name = file.attachFileName ? file.attachFileName : file.name;
    if (file.fileToken) {
      fileDownload(file).then((res: any) => {
        if (res && res.data?.size) {
          download(file, res.data);
        }
      });
    } else {
      Message.error(t('task.fileToken-missing'));
    }
  };

  defineExpose({
    props,
  });
</script>

<script lang="ts">
  // 只需在这再加个name即可
  export default {
    name: 'StructureFolder', // 给组件命名
  };
</script>

<style scoped lang="less">
  .expend-btn {
    position: absolute;
    height: 30px;
    //border: 1px solid red;
    display: flex;
    align-content: center;
    align-items: center;
    bottom: -28px;
    left: calc(50% - 20px);
    z-index: 99;
    font-size: 20px;
    color: rgb(var(--arcoblue-3));
    cursor: pointer;
    &:hover {
      color: rgb(var(--arcoblue-6));
    }
  }
  .file-folder {
    display: flex;
    flex-wrap: wrap;
    max-width: 650px;
    //max-height: 120px;
    .file,
    .folder {
      width: 200px;
      border-radius: 6px;
      position: relative;
      display: flex;
      margin-right: 12px;
      align-content: center;
      background-color: var(--color-fill-2);
      margin-bottom: 10px;
      overflow: hidden;
      cursor: pointer;
      .file-name {
        width: 130px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        height: 22px;
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN, serif;
        font-weight: 400;
        color: var(--color-text-1);
        line-height: 22px;
      }
      .file-version {
        //height: 20px;
        background: #e8fffb;
        border-radius: 4px;
        opacity: 1;
        border: 1px solid #0fc6c2;
        //padding: 4px;
        padding-left: 6px;
        padding-right: 6px;
        color: #0fc6c2;
        font-size: 12px;
        margin-top: 4px;
        line-height: 20px;
      }
      padding: 5px 5px;
    }
    .folder .file-text {
      display: flex;
      align-items: center;
      margin: 0 5px;
    }
    .file .file-name {
      color: rgb(var(--primary-6));
    }
    .icon-download {
      position: absolute;
      right: 5px;
      bottom: 2px;
    }
  }
</style>
