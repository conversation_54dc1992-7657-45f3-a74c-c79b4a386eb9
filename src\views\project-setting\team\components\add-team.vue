<template>
  <a-modal
    draggable
    :visible="visible"
    :title="$t('project-setting.add-team')"
    :unmount-on-close="true"
    :mask-closable="false"
    width="600px"
    class="mydialog"
    :esc-to-close="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
  >
    <a-form ref="teamRef" :model="formData" auto-label-width class="add-form">
      <a-form-item
        field="name"
        :label="$t('project-setting.team')"
        validate-trigger="input"
        :rules="[
          {
            required: true,
            message: $t('project-setting.team-name-errMsg'),
          },
        ]"
      >
        <remove-spaces-input
          v-model="formData.name"
          :placeholder="$t('please-enter')"
          :max-length="currentLocale === 'en-US' ? 255 : 100"
          show-word-limit
        />
      </a-form-item>

      <a-form-item field="userIds" :label="$t('project-setting.team-members')">
        <user-selector
          v-model="formData.userIds"
          :project-id="formData.projectId"
          multiple
        />
      </a-form-item>

      <a-form-item
        field="role"
        :label="$t('project-setting.team-permission-config')"
        content-class="permission-item"
        required
      >
        <a-radio-group v-model="formData.role" direction="vertical">
          <a-radio :value="1">
            <span>{{ $t('project-setting.view') }}</span>
            <permission-icon :active-count="1" />
          </a-radio>
          <a-radio :value="2">
            <span>{{ $t('project-setting.edit') }}</span>
            <permission-icon :active-count="2" />
          </a-radio>
          <a-radio :value="3">
            <span>{{ $t('project-setting.share') }}</span>
            <permission-icon :active-count="3" />
          </a-radio>
          <a-radio :value="4">
            <span>{{ $t('project-setting.deliver') }}</span>
            <permission-icon :active-count="4" />
          </a-radio>
          <a-radio :value="5">
            <span>{{ $t('project-setting.manage') }}</span>
            <permission-icon :active-count="5" />
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        v-if="clickEvent"
        v-permission="$btn.team.subordination"
        field="isInherit"
        :label="$t('project-setting.team-relationship')"
        content-class="permission-item"
        required
      >
        <a-radio-group v-model="formData.isInherit">
          <a-radio :value="1">
            <span>有</span>
          </a-radio>
          <a-radio :value="0">
            <span>无</span>
          </a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { usePrjPermissionStore } from '@/store';
  import UserSelector from '@/components/user-selector/index.vue';
  import PermissionIcon from './permission-icon.vue';
  import { TeamRecord, saveTeam } from '../api';
  import { useI18n } from 'vue-i18n';
  import useLocale from '@/hooks/locale';
  import removeSpacesInput from '@/components/removeSpacesInput/index.vue';

  const { t } = useI18n();
  // 国际化类型
  const { currentLocale } = useLocale();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
    },
    clickEvent: {
      type: Boolean,
    },
  });
  const emit = defineEmits(['update:visible', 'refresh']);

  const formData = ref<TeamRecord>({
    ...props.form,
  } as TeamRecord);
  watch(
    () => props.form,
    (n) => {
      formData.value = { ...n } as TeamRecord;
    }
  );

  const teamRef = ref<FormInstance>();

  // 新增团队
  const handleAdd = async () => {
    const userIdsAry = formData.value.userIds
      ? formData.value.userIds.split(',')
      : [];
    const teamUsers = userIdsAry?.map((id) => {
      return { id, role: formData.value.role };
    });

    const params = {
      projectId: formData.value.projectId,
      parentId: formData.value.parentId,
      name: formData.value.name,
      color: formData.value.color,
      role: formData.value.role,
      isInherit: formData.value.isInherit,
      teamUsers,
      count: userIdsAry?.length,
    };
    const res = await saveTeam(params);
    if (res.status) {
      const prjPermissionStore = usePrjPermissionStore();
      prjPermissionStore.setPermission(formData.value.projectId);
    }
    return !!res.status;
  };

  // 提交事件处理
  const handleBeforeOk = async (done: any) => {
    const res = await teamRef.value?.validate();
    if (!res) {
      const flg = await handleAdd();
      if (flg) {
        Message.success(t('project-add-success'));
        emit('update:visible', false);
        emit('refresh');
      }
      done();
    }
  };

  // 关闭弹窗
  const handleCancel = () => {
    emit('update:visible', false);
  };
</script>

<style lang="less" scoped>
  .mydialog {
    .add-form {
      :deep(.arco-row) {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  /* TODO 按钮border-radius无法覆盖 */

  :deep(.permission-item) {
    .arco-radio-label {
      display: flex;
    }
  }
</style>
