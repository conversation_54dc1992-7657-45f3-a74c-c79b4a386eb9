<template>
  <!-- 设置默认挂载的位置 -->
  <!-- :render-to-body = "false" -->
  <a-modal
    :visible="visible"
    :ok-text="$t('invite-user.confirm')"
    :title="isEditMode ? $t('user-center.edit.title') : $t('invite-user.title')"
    :on-before-ok="handleOk"
    @cancel="handleCancel"
    draggable
    :mask-closable="false"
    :esc-to-close="false"
    width="800px"
    title-align="start"
  >
    <div class="invite-user-content">
      <a-form ref="formRef" :model="form" auto-label-width layout="vertical">
        <a-row :gutter="20">
          <a-col :span="12">
            <a-form-item
              field="phone"
              :label="$t('invite-user.phone')"
              :rules="[
                { required: true, message: $t('invite-user.phone-required') },
              ]"
            >
              <a-input
                v-model="form.phone"
                :placeholder="$t('invite-user.please-enter-phone')"
                :disabled="isEditMode"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item
              field="email"
              :label="$t('invite-user.email')"
              :rules="[
                { required: true, message: $t('invite-user.email-required') },
              ]"
            >
              <a-input
                v-model="form.email"
                :placeholder="$t('invite-user.please-enter-email')"
                :disabled="isEditMode"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-button
              type="outline"
              @click="handleMatchUser"
              class="match-btn"
              v-if="!isEditMode"
              :disabled="isMatchButtonDisabled"
              >{{ $t('invite-user.match') }}</a-button
            >
          </a-col>
        </a-row>

        <a-divider />

        <a-row :gutter="20">
          <a-col :span="12">
            <a-form-item
              field="userFullname"
              :label="$t('invite-user.userFullname')"
            >
              <a-input
                v-model="form.userFullname"
                readonly
                disabled
                :placeholder="$t('invite-user.user-placeholder')"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item
              field="userType"
              :label="$t('invite-user.user-attribute')"
            >
              <a-radio-group v-model="form.userType">
                <a-radio value="1">{{
                  $t('invite-user.internal-user')
                }}</a-radio>
                <a-radio value="2">{{
                  $t('invite-user.external-user')
                }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item
              field="organizations"
              :label="$t('invite-user.department')"
              :rules="[
                {
                  required: true,
                  message: $t('invite-user.select-department'),
                },
              ]"
              validate-trigger="none"
            >
              <div class="arco-input-wrapper cursor-pointer multi-org-select">
                <div
                  v-if="form.organizations.length === 0"
                  class="placeholder-text"
                >
                  {{ $t('invite-user.select-department') }}
                </div>
                <a-space wrap v-else class="organizations-tags">
                  <a-tag
                    v-for="org in form.organizations"
                    :key="org.organizationNo"
                    closable
                    @close="handleRemoveOrg(org)"
                  >
                    {{ org.organizationName }}
                  </a-tag>
                </a-space>
                <div class="add-org-btn-wrapper">
                  <a-button type="text" @click="openOrgList">{{
                    $t('invite-user.add')
                  }}</a-button>
                </div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <org-list v-model:visible="orgListVisible" @selectOrg="handleSelectOrg" />
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { reactive, ref, watch, computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import orgList from './org-list.vue';
  import { useI18n } from 'vue-i18n';
  import {
    checkExistedUserApi,
    sendInvitationUrl,
    companyJoinCompanyAndOrg,
    getCompanyUserInfo,
    changeCompanyUserInfo,
  } from '../api';
  import { useUserStore } from '@/store';

  const { t } = useI18n();

  // 控制模态框的显示与隐藏
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    editingUserName: {
      // 编辑用户的 userName
      type: String,
      default: '',
    },
  });

  // 定义组件 emits
  const emits = defineEmits([
    'update:visible',
    'inviteSuccess',
    'update:editingUserName',
  ]);

  const formRef = ref();
  // 记录匹配的用户情况1：直接邀请  2，给用户手机、邮箱发送邀请链接
  const matchUserType = ref(0);

  const form = reactive({
    userName: '',
    phone: '',
    email: '',
    // 存储选择的组织列表
    organizations: [] as { organizationNo: string; organizationName: string }[],
    userType: '1',
    userFullname: '',
  });
  const userStore = useUserStore();

  // 是否是编辑模式
  const isEditMode = computed(() => !!props.editingUserName);

  // 添加计算属性控制匹配按钮禁用状态
  const isMatchButtonDisabled = computed(() => {
    return !form.phone || !form.email;
  });

  // 企业id
  const storeCompanyId = computed(() => {
    return userStore.companyId;
  });

  // 组织列表模态框的显示状态
  const orgListVisible = ref(false);

  // 获取用户信息并回显表单
  const fetchUserInfoForEdit = async (userName: string) => {
    try {
      const { data, status, message } = await getCompanyUserInfo({ userName });
      if (status) {
        form.userFullname = data.userFullname;
        form.userName = userName;
        form.phone = data.phone;
        form.email = data.email;
        form.userFullname = data.userFullname;
        form.userType = data.companyUserType;
        // 回显部门列表
        if (data.orgList && data.orgList.length > 0) {
          form.organizations = data.orgList.map((org: any) => ({
            organizationNo: String(org.orgNo),
            organizationName: org.name,
          }));
        } else {
          form.organizations = [];
        }
        matchUserType.value = 0;
      } else {
        console.log('回显失败打印报错信息', message);
        // Message.error(t('invite-user.get-user-info-failed'));
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // Message.error(t('invite-user.get-user-info-failed'));
    }
  };

  // 处理确定按钮点击
  const handleOk = async () => {
    console.log(matchUserType.value);

    // 首先处理用户类型的逻辑
    if (
      !isEditMode.value &&
      matchUserType.value !== 1 &&
      matchUserType.value !== 2
    ) {
      Message.error(t('invite-user.please-match-user'));
      return false;
    }
    // 验证组织是否已选择
    if (form.organizations.length === 0) {
      Message.error(t('invite-user.select-department'));
      return false;
    }

    try {
      let result: any;
      let isSuccess = false;

      if (isEditMode.value) {
        // 编辑模式下调用 changeCompanyUserInfo 接口
        const editData = {
          userName: form.userName,
          userFullname: form.userFullname,
          phone: form.phone,
          email: form.email,
          companyUserType: form.userType,
          orgList: form.organizations.map((org) => ({
            orgNo: org.organizationNo,
            // name: org.organizationName,
          })),
        };
        console.log('编辑模式拼装参数', editData);
        result = await changeCompanyUserInfo(editData);
        isSuccess = result.status;
      } else if (matchUserType.value === 1) {
        // 直接邀请进入
        const data = {
          companyId: storeCompanyId.value,
          companyUserType: form.userType,
          orgNoList: form.organizations.map((org) => org.organizationNo),
          companyMembers: [form.userName],
        };
        console.log('1111拼装参数', data);
        result = await companyJoinCompanyAndOrg(data);
        isSuccess = result.status;
      } else if (matchUserType.value === 2) {
        // 发送邀请链接
        const data = {
          companyUserType: form.userType,
          orgNoList: form.organizations.map((org) => org.organizationNo),
          registerUrl: `${window.location.origin}/work/login?inviteCompany=1`,
          userInfoList: [
            {
              email: form.email,
              phone: form.phone,
            },
          ],
        };
        console.log('22222拼装参数', data);

        result = await sendInvitationUrl(data);
        isSuccess = result.status;
      }

      // 统一处理成功和失败的逻辑
      if (isSuccess) {
        // Message.success(
        //   matchUserType.value === 1
        //     ? result.message
        //     : t('invite-user.invite-success')
        // );
        console.log('操作成功', result);
        // Message.success(result.message || t('global.success'));
        emits('update:visible', false);
        emits('inviteSuccess');
        return true;
      }
      // Message.error(result?.message || t('invite-user.invite-failed'));
      return false;
    } catch (error) {
      console.error('操作失败:', error);
      // Message.error(t('invite-user.invite-failed'));
      return false;
    }
  };

  // 处理取消按钮点击
  const handleCancel = () => {
    formRef.value?.resetFields();
    form.organizations = [];
    matchUserType.value = 0;
    emits('update:visible', false);
  };

  // 打开组织列表模态框
  const openOrgList = () => {
    orgListVisible.value = true;
  };

  // 处理选择组织
  const handleSelectOrg = (selectedOrg: { orgNo: number; name: string }) => {
    const newOrg = {
      organizationNo: String(selectedOrg.orgNo),
      organizationName: selectedOrg.name,
    };
    // 避免重复添加
    const exists = form.organizations.some(
      (org) => org.organizationNo === newOrg.organizationNo
    );
    if (!exists) {
      form.organizations.push(newOrg);
    }
    console.log('Selected Organization:', form.organizations);
  };

  // 处理移除组织标签
  const handleRemoveOrg = (orgToRemove: { organizationNo: string }) => {
    form.organizations = form.organizations.filter(
      (org) => org.organizationNo !== orgToRemove.organizationNo
    );
  };

  // 处理匹配用户按钮点击
  const handleMatchUser = async () => {
    if (form.phone !== '' && form.email !== '') {
      try {
        const { data, status } = await checkExistedUserApi({
          phone: form.phone,
          email: form.email,
        });

        if (status) {
          if (data) {
            form.userFullname = data.userFullname;
            form.userName = data.userName;
            matchUserType.value = data.matchType;
            console.log('匹配用户后记录匹配结果', matchUserType.value);
            // Message.success(t('invite-user.user-matched'));
          } else {
            form.userFullname = '';
            // Message.error(t('invite-user.user-not-exist'));
          }
        } else {
          form.userFullname = '';
          const errorMessage =
            data.message || t('invite-user.check-user-error');
          // Message.error(errorMessage);
        }
      } catch (error) {
        console.error('Error checking user existence:', error);
        form.userFullname = '';
        // Message.error(t('invite-user.check-user-error'));
      }
    } else {
      console.log('Name, phone, or email is empty.');
      if (form.phone === '') {
        Message.error(t('invite-user.please-enter-phone'));
      } else if (form.email === '') {
        Message.error(t('invite-user.please-enter-email'));
      }
      form.userFullname = '';
    }
  };

  // 监听 visible 和 editingUserName 属性变化，用于编辑模式
  watch(
    () => [props.visible, props.editingUserName],
    async ([newVisible, newEditingUserName]) => {
      // 对显示和编辑进行共同检查
      if (newVisible && newEditingUserName) {
        // 编辑模式
        await fetchUserInfoForEdit(newEditingUserName as string);
      } else if (!newVisible) {
        // 模态框关闭时重置表单和编辑状态
        formRef.value?.resetFields();
        form.userName = '';
        form.phone = '';
        form.email = '';
        form.organizations = [];
        form.userType = '1';
        form.userFullname = '';
        matchUserType.value = 0;
        emits('update:editingUserName', ''); // 重置 editingUserName
      }
    },
    { immediate: true }
  );
</script>

<script lang="ts">
  export default {
    name: 'InviteUser',
  };
</script>

<style scoped lang="less">
  .invite-user-content {
    padding: 5px 20px;
  }

  .section-title {
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: bold;
  }

  .user-tags .arco-tag {
    margin-right: 5px;
  }

  .match-btn {
    border-radius: 8px 8px 8px 8px;
  }

  :deep(.arco-form-item-label) {
    font-size: 16px;
  }

  :deep(.arco-form-item-wrapper-col) {
    width: 100%;
  }

  :deep(.arco-input-wrapper) {
    background-color: transparent;
    border: 1px solid #c9cdd4;
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.cursor-pointer) {
    cursor: pointer;
  }

  // 标签之间的样式
  .multi-org-select {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px; /* 标签之间的间距 */
    padding: 0 10px;
    min-height: 40px;
    justify-content: space-between;

    .placeholder-text {
      color: var(--color-text-3);
    }

    .organizations-tags {
      flex: 1;
      padding: 10px 0 0 0;
    }

    .add-org-btn-wrapper {
      flex-shrink: 0;
    }
  }

  :deep(.arco-input-disabled:hover) {
    background-color: transparent;
    border: 1px solid #c9cdd4;
    border-radius: 8px;
    overflow: hidden;
  }

  // :deep(.arco-modal-body) {
  //   padding: 5px 10px !important;
  // }
</style>
