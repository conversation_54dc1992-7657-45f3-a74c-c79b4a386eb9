<template>
  <div class="base-layout">
    <div class="navbar-wrap">
      <NavBar />
    </div>
    <div class="content-wrap">
      <router-view></router-view>
    </div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from '@/components/navbar/index.vue'
</script>
<style scoped lang="less">
@nav-size-height: 72px;
.base-layout {
  /*overflow: hidden;*/
  position: relative;
}
.navbar-wrap {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: @nav-size-height;
}
.content {
  //border: 1px solid black;
  position: relative;
  top: @nav-size-height;
  height: calc(100vh - @nav-size-height);
  width: 100vw;
  overflow: hidden;
  background-color: white;
  //border: 1px solid red;
}
</style>
