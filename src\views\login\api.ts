import axios from 'axios';
import qs from 'query-string';

// 获取客户端详情
export function getClientInfo() {
  return axios.get('/sys-system/clientInfo');
}

export function changePhone(data: any) {
  return axios.post('/cde-collaboration/user/change-phone-by-password', data);
}

// 获取wpstoken
export function getWpsToken(data: any) {
  const q: any = { ...data, scope: 'all' };
  data = qs.stringify(q);
  // const signData = getSignWps({});
  return axios({
    url: `/sys-auth/oauth/token`,
    method: 'POST',
    data,
    baseURL: '/wps',
  });
}

// 获取系统对应的应用code
export function getWpsAppCode(params: any) {
  return axios.get('/cde-collaboration/wps_system/fileSoucre', { params });
}

export function getRegisterInvitation(params: any) {
  return axios.get('/sys-user/invitation/register-invitation', { params });
}

// 获取可转换的格式
export function getCode(params: any) {
  return axios.get('/cde-work/wps_system/code', { params });
}

// 邀请加入企业页面的回显信息
export function joinCompanyInfo(params: any) {
  return axios.get('/sys-user/company/join/info', { params });
}