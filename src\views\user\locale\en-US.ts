export default {
  'menu.user.setting': 'User Setting',
  'userSetting.title.baseinfo': 'Base Info',
  'userSetting.title.editPwd': 'Change Password',
  'userSetting.title.editPhone': 'Change Phone',
  'userSetting.title.editEmail': 'Change Email',
  'userSetting.title.bind-Phone': 'Bind phone Settings',
  'userSetting.title.bind-Email': 'Bind email address',
  'userSetting.form.oldPwd': 'Current Password',
  'userSetting.form.oldPwd.required': 'Current Password is required',
  'userSetting.form.newPwd': 'New Password',
  'userSetting.form.newPwd.required': 'New Password is required',
  'userSetting.form.enterPwd': 'Confirm Password',
  'userSetting.form.enterPwd.required': 'Confirm Password is required',
  'userSetting.form.placeholder.common': 'Please enter',
  'userSetting.save': 'Save',
  'userSetting.reset': 'Reset',
  'userSetting.password.edit.error': 'The two passwords entered do not match',
  'userSetting.password.edit.success': 'Password changed successfully',
  'userSetting.form.phone': 'Phone number',
  'userSetting.form.phone-new': 'New Phone number',
  'userSetting.form.captcha': 'Verification code',
  'userSetting.phone.edit.success': 'Phone number changed successfully',
  'userSetting.form.email': 'Email',
  'userSetting.form.email.required': 'Email is required',
  'userSetting.form.email.error': 'Email format is incorrect',
  'userSetting.email.edit.success': 'Email changed successfully',
  'userSetting.email.equity': 'Product permission application',
  'userSetting.password-validation': 'The password must contain at least one lowercase letter, one uppercase letter, one number, and one special character, such as $ % ? ^ () = + ,.; :, etc.) and must be at least 8 characters in length',
  'userSetting.password-rule-error':
    'The password does not comply with the rules',
};
