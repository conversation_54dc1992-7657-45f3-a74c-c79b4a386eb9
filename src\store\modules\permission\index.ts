import { defineStore } from 'pinia';
import { initSessionStorageData, setSessionStorageData } from '../file/utils';
import {
  getBtnPermissionOfProject,
  getBtnPermissionOfSys,
} from '@/api/modules/user';

interface PermissionState {
  currentMenu: Permission.Model.MenuTree;
  selectedKeys: string[];
  permissionBtns: string[];
}
const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => {
    return {
      selectedKeys: initSessionStorageData('permission_selected_menu'),
      currentMenu: {} as Permission.Model.MenuTree,
      permissionBtns: [],
    };
  },

  actions: {
    setSelectKeys(keys: string[]) {
      this.selectedKeys = keys;
      setSessionStorageData('permission_selected_menu', keys);
    },
    setPermissionBtns(data: string[]) {
      this.permissionBtns = data;
    },
    setCurrentMenu(menu: Permission.Model.MenuTree) {
      this.currentMenu = menu;
    },
    async setProjectPremission(projectId: string) {
      const { data } = await getBtnPermissionOfProject(projectId);

      let btns: string[] = (data.buttonCodes || []).concat(
        data.sysButtonCodes || []
      );

      if (data.teamButtons.length) {
        data.teamButtons.forEach((team) => {
          team.buttonCodes &&
            (btns = btns.concat(
              team.buttonCodes.map((code) => `${team.teamId}_${code}`)
            ));
        });
      }

      this.setPermissionBtns(btns);
    },
    async setPublicPermission() {
      const { data } = await getBtnPermissionOfSys();
      const btns = data.map((btn) => btn.code) || [];
      this.setPermissionBtns(btns);
    },
  },
});

export default usePermissionStore;
