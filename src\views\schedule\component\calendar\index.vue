<template>
  <!-- 天视图 -->
  <a-row class="calendar-container">
    <!-- 左侧 -->
    <div class="calendar-left">
      <!-- 天视图日历 -->
      <div class="v-calendar-parent">
        <!-- https://vcalendar.netlify.app/ -->
        <VCalendar
          :key="currentLocale.value"
          :initial-page="currentDate"
          :locale="isEn ? 'en' : 'zh-cn'"
          class="v-calendar-body"
          reservation-time
          expanded
          borderless
          :attributes="attributes"
          title-position="left"
          @dayclick="dayclick"
          @update:pages="handleMonthChange"
        >
        </VCalendar>
      </div>

      <div class="calendar-left-bottom">
        <!-- 左侧下方关注事项 -->
        <div class="attention">
          <div class="attention-head">
            <!-- <img src="@/assets/images/schedule/matter.png" alt="" /> -->
            <icon-subscribe /><span>{{ $t('schedule.focusMatters') }}</span>
          </div>
          <div
            v-if="!attentionListData?.length"
            class="noData"
            :style="{
              height: globalMode === 'work' ? '220px' : 'calc(100vh - 566px)',
              borderBottom:
                globalMode === 'work' ? '1px solid #d9d9d9' : 'none',
            }"
          >
            <img :src="attentionImg" alt="" />
            <div>{{ $t('schedule.noFocusMatters') }}</div>
          </div>
          <div
            v-else
            class="attention-body"
            :style="{
              height: globalMode === 'work' ? '220px' : 'calc(100vh - 566px)',
            }"
          >
            <div
              v-for="item in attentionListData"
              :key="item.key"
              class="attention-list"
              :style="{
                borderBottom:
                  globalMode === 'work' ? '1px solid #d9d9d9' : 'none',
              }"
            >
              <div @click="previewSchedule(item)">
                <div class="attention-list-title">
                  <img v-if="item.type === 'item'" :src="matterImg" alt="" />
                  <img v-else :src="meetingImg" alt="" />
                  <span>{{ item.title }}</span>
                </div>
                <div class="attention-list-described">{{ item.content }}</div>
                <div class="attention-list-time">
                  <icon-schedule />

                  {{ $t('schedule.deadline') }}
                  <span>{{ item.planEndTime }}</span>
                </div>
              </div>
              <div class="schedule-list-action"
                ><a-dropdown class="custom-dropdown" @select="handleSelect">
                  <icon-more
                    class="icon-more"
                    @click="handleSwitchVisible(item)"
                  />
                  <template #content>
                    <a-doption @click="editHandle(item)"
                      ><a-button type="text">{{
                        $t('schedule.edit')
                      }}</a-button></a-doption
                    >
                    <a-doption @click="deleteHandle(item)"
                      ><a-button type="text">{{
                        $t('schedule.delete')
                      }}</a-button></a-doption
                    >

                    <a-doption @click="attentionHandle(item)"
                      ><a-button type="text">{{
                        $t('schedule.cancelFocus')
                      }}</a-button></a-doption
                    >
                  </template>
                </a-dropdown>
              </div>
            </div>
          </div>
        </div>

        <div class="calendar-user" v-if="globalMode === 'work'">
          <div class="add-user title" @click="createPanelHandle">
            <icon-plus />
            {{ $t('schedule.addCalendar') }}
          </div>
          <!-- 我的日历 -->
          <div class="user-list">
            <div class="title">
              <icon-down
                v-if="retractableStatus === 0"
                @click="changeRetractable(1)"
              />
              <icon-up v-else @click="changeRetractable(0)" />
              {{ $t('schedule.myCalendar') }}
            </div>
            <div v-if="retractableStatus === 1">
              <a-checkbox-group
                v-model="scheduleId"
                direction="vertical"
                @change="changeSchedule"
              >
                <a-checkbox
                  v-for="option in scheduleOptions"
                  :key="option.id"
                  :value="option.id"
                >
                  <a-row>
                    <a-col class="panelName" :span="12">
                      {{ option.panelName }}</a-col
                    >
                    <a-col :span="12" align="right">
                      <a-space>
                        <a-button type="text"
                          ><icon-edit @click.stop="editOption(option)"
                        /></a-button>

                        <a-popconfirm
                          :content="$t('schedule.deleteCalendar.confirm')"
                          @ok="delCalendar(option)"
                        >
                          <a-button type="text"> <icon-delete /></a-button>
                        </a-popconfirm>
                      </a-space>
                    </a-col>
                  </a-row>
                </a-checkbox>
              </a-checkbox-group>
            </div>
          </div>
          <!-- 项目日历 -->
          <div class="user-list">
            <div class="title-container">
              <div class="title">
                <icon-down
                  v-if="projectRetractableStatus === 0"
                  @click="changeProjectRetractable(1)"
                />
                <icon-up v-else @click="changeProjectRetractable(0)" />
                {{ $t('schedule.projectCalendar') }}

                <a-tooltip :content="$t('calendar.tips')">
                  <icon-info-circle :size="16" />
                </a-tooltip>
              </div>
              <div
                class="search-container"
                style="display: flex; align-items: center"
              >
                <icon-search
                  v-if="!showProjectSearch"
                  style="cursor: pointer; font-size: 18px; color: #999"
                  @click="showProjectSearch = true"
                />
                <a-input
                  v-if="showProjectSearch"
                  v-model="projectSearchKeyword"
                  allow-clear
                  autofocus
                  :placeholder="$t('calendar.searchProject')"
                  style="
                    width: 140px;
                    height: 24px;
                    margin-left: 8px;
                    font-size: 14px;
                  "
                  @blur="onProjectSearchBlur"
                />
              </div>
            </div>

            <div v-if="projectRetractableStatus === 1">
              <a-checkbox-group
                v-model="projectScheduleId"
                direction="vertical"
                @change="changeProjectSchedule"
              >
                <a-checkbox
                  v-for="option in filteredProjectScheduleOptions"
                  :key="option.id"
                  :value="option.id"
                >
                  <a-row>
                    <a-col class="panelName" :span="12">
                      {{ option.panelName }}</a-col
                    >
                  </a-row>
                </a-checkbox>
              </a-checkbox-group>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧日程详情 -->
    <div class="calendar-right">
      <a-row class="calendar-detail-head">
        <a-col class="detail-time" :span="9">
          <a-space>
            <div class="detail-date">{{ rightHeadDate }}</div>
            <a-checkbox-group
              v-if="globalMode === 'project'"
              v-model="listType"
            >
              <a-checkbox
                value="item"
                :disabled="listType.length === 1 && listType.includes('item')"
                >{{ $t('schedule.matters') }}</a-checkbox
              >
              <a-checkbox
                value="meeting"
                :disabled="
                  listType.length === 1 && listType.includes('meeting')
                "
                >{{ $t('schedule.meeting') }}</a-checkbox
              >
            </a-checkbox-group>
          </a-space>
        </a-col>
        <a-col :span="15" align="right" class="detail-btn-container">
          <!-- 日程页面日程详情 -->
          <a-space class="detail-btn">
            <a-space style="flex-wrap: nowrap">
              <div
                v-if="globalMode === 'project'"
                :class="{ 'team-title-en': isEn }"
              >
                {{ $t('schedule.calendar.team') }}
              </div>
              <!-- 项目空间下，筛选团队 -->
              <div class="team-select">
                <a-select
                  v-if="globalMode === 'project'"
                  v-model="teamId"
                  allow-search
                  :placeholder="$t('schedule.meeting.team.placeholder')"
                >
                  <a-option
                    v-for="item in teamList"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-option>
                </a-select>
              </div>
            </a-space>
            <div class="addBtn" :class="{ 'addBtn-en': isEn }">
              <span @click="createScheduleHandle('matter')">
                <matter class="schedule-icon"></matter>
                {{ $t('schedule.createMatter') }}
              </span>
              <span @click="createScheduleHandle('meeting')">
                <meeting class="schedule-icon"></meeting>
                {{ $t('schedule.createMeeting') }}
              </span>
            </div>
            <!-- 日历模式切换 -->
            <a-radio-group
              v-model="calendarMode"
              type="button"
              @change="changeCalendarMode"
            >
              <a-radio :value="1">{{ $t('schedule.month') }}</a-radio>
              <a-radio :value="2">{{ $t('schedule.week') }}</a-radio>
              <a-radio :value="3">{{ $t('schedule.day') }}</a-radio>
            </a-radio-group>
            <!-- <div class="calendarModeBtn">
              <span
                :class="{ active: calendarMode === 1 }"
                @click="changeCalendarMode(1)"
              >
                月
              </span>
              <span
                :class="{ active: calendarMode === 2 }"
                @click="changeCalendarMode(2)"
              >
                周
              </span>
              <span
                :class="{ active: calendarMode === 3 }"
                @click="changeCalendarMode(3)"
              >
                天
              </span>
            </div> -->
          </a-space>
        </a-col>
      </a-row>
      <scheduleList
        v-if="calendarMode === 3"
        class="schedule-list"
        type="schedule"
        :data="scheduleCalendarData"
        :schedule-id="scheduleId"
        :project-schedule-id="projectScheduleId"
        :current-project-schedule-id="currentProjectScheduleId"
        @refresh="refreshAttention"
        @jump="jumpHandle"
      />
      <div v-else class="fullCalendar-container">
        <!-- 日历 -->
        <FullCalendar
          ref="fullCalendarRef"
          class="calendar-main"
          :options="calendarOptions"
        />
      </div>
    </div>
  </a-row>

  <!-- 创建日程 -->
  <cratedSchedule
    v-model:visible="panelVisible"
    :data="scheduleData"
    :type="scheduleType"
    @refresh="getPanelListHandle"
  />

  <!-- 日程概要 -->
  <scheduleSummary
    :visible="summaryVisible"
    @refresh="getScheduleData"
  ></scheduleSummary>
  <a-modal :visible="delVidible" @ok="deleteSaveHandle" @cancel="handleCancel">
    <template #title> {{ $t('schedule.delete') }} </template>
    <div>
      <a-radio-group
        v-if="recordType === 'meeting'"
        v-model="repeatedVal"
        direction="vertical"
      >
        <a-radio value="0">{{ $t('schedule.delete.current') }}</a-radio>
        <a-radio value="1">{{ $t('schedule.delete.currentAndAfter') }}</a-radio>
        <a-radio value="2">{{ $t('schedule.delete.all') }}</a-radio>
      </a-radio-group>
      <div v-else>{{
        `${deleteMatterMsg || ''} ${$t('schedule.delete.confirm')}`
      }}</div>
    </div>
  </a-modal>
</template>

<script setup>
  import { nextTick, ref, computed, watch } from 'vue';
  import FullCalendar from '@fullcalendar/vue3';
  import dayGridPlugin from '@fullcalendar/daygrid';
  import timeGridPlugin from '@fullcalendar/timegrid';
  import interactionPlugin from '@fullcalendar/interaction';
  import cratedSchedule from './component/cratedSchedule.vue';
  import scheduleSummary from './component/scheduleSummary.vue';
  // import scheduleList from '@/views/create-schedule/component/scheduleList.vue';
  import scheduleList from './component/scheduleList.vue';
  import { useRouter } from 'vue-router';
  import dayjs from 'dayjs';
  import 'dayjs/locale/zh-cn';
  import 'dayjs/locale/en';
  import {
    getPanelList,
    getProjectPanel,
    getScheduleCalendar,
    getScheduleFollowList,
    deleteSchedule,
    getProjectSchedule,
  } from './api';
  import { queryTeamList } from '../meeting/api';
  import {
    setScheduleFollow,
    removeschedule,
    setscheduleStatus,
    deleteMatterFlag,
  } from '@/views/schedule/component/calendar/api';
  import matter from '@/assets/images/dashboard/matter.svg';
  import meeting from '@/assets/images/dashboard/meeting.svg';
  import { storeToRefs } from 'pinia';
  import { userScheduleStore, useGlobalModeStore, useUserStore } from '@/store';
  import { Message } from '@arco-design/web-vue';
  import { useI18n } from 'vue-i18n';
  import { getLocalstorage } from '@/utils/localstorage';
  import { getUserId } from '@/utils/auth';
  import attentionImg from '@/assets/images/schedule/attention-bg.png';
  import matterImg from '@/assets/images/schedule/icon-matter.png';
  import meetingImg from '@/assets/images/schedule/icon-meeting.png';
  import useLocale from '@/hooks/locale';

  const userStore = useUserStore();
  const userName = computed(() => userStore.username);
  const showProjectSearch = ref(false);
  const projectSearchKeyword = ref('');
  const { currentLocale } = useLocale();
  const isEn = computed(() => currentLocale.value === 'en-US');
  const { t } = useI18n();
  const userId = getUserId() || '';
  const currentProjectId = ref(getLocalstorage(`last_project_${userId}`) || '');

  const globalModeStore = useGlobalModeStore();
  const globalMode = computed(() => globalModeStore.getGlobalMode);
  const scheduleStore = userScheduleStore();
  const { summaryVisible } = storeToRefs(scheduleStore);

  const today = new Date();
  const currentData = dayjs(today).format('YYYY-MM-DD');
  const teamId = ref('');
  const listType = ref(['item', 'meeting']); // 会议事项类型

  // 我的日历操作类型
  const scheduleType = ref('create');

  const isNotAllow = ref(true);

  const currentDate = ref(null);
  const scheduleOptions = ref([]); // 个人日历
  const projectScheduleOptions = ref([]); // 项目日历
  // 过滤后的项目日历
  const filteredProjectScheduleOptions = computed(() => {
    if (!projectSearchKeyword.value) return projectScheduleOptions.value;
    return projectScheduleOptions.value.filter((option) =>
      option.panelName
        ?.toLowerCase()
        .includes(projectSearchKeyword.value.trim().toLowerCase())
    );
  });
  const onProjectSearchBlur = () => {
    console.log('失去焦点的时候', projectSearchKeyword.value.trim() === '');
    if (projectSearchKeyword.value.trim() === '') {
      showProjectSearch.value = false;
    } else {
      showProjectSearch.value = true;
    }
  };
  const teamList = ref([{ id: '', name: t('schedule.all') }]);
  const startTime = ref('');
  const endTime = ref('');
  // 月的按钮单独加了自定义 调用月的视图
  const fullCalendarData = ref([]);
  // 左侧日历初始年月
  currentDate.value = {
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
  };
  const scheduleId = ref([]); // 选择的个人日历数据
  const projectScheduleId = ref([]); // 选择的项目日历数据
  const currentProjectScheduleId = ref([]); // 项目空间下当前项目的项目日历
  const attentionListData = ref([]); // 关注事项数据
  const scheduleCalendarData = ref([]); // 右侧天视图日程列表数据
  const scheduleCalendarLeft = ref([]); // 左侧天视图日程列表数据
  // 获取项目下的团队数据
  const getTeamList = async (projectId) => {
    const params = {
      pageNo: 1,
      pageSize: 999999,
      projectId,
    };
    queryTeamList(params)
      .then((res) => {
        teamList.value = [
          { id: '', name: t('schedule.all') },
          ...(res.data.list || []),
        ];
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // 根据事项或者会议筛选条件计算传参
  const getTypeParam = () => {
    // listType.value 是数组
    // scheduleTypes 是所有可选项的数组，比如 ['item', 'meeting']
    const scheduleTypes = ['item', 'meeting'];
    if (
      listType.value.length === 0 ||
      listType.value.length === scheduleTypes.length
    ) {
      return null; // 或者返回 ''，根据后端要求
    }
    if (listType.value.length === 1) {
      return listType.value[0];
    }
    return null; // 多选但不是全选时也传空
  };
  // 获取关注事项数据
  const getScheduleFollowListHandle = async () => {
    // attentionListData.value = [];
    // 这里需要把个人日历和项目日历合并为一个数组
    const param = {
      ...(globalMode.value === 'work'
        ? {
            schedulePanelIds: scheduleId.value || [],
            projectPanelIds: projectScheduleId.value || [],
          }
        : {
            projectPanelIds: currentProjectScheduleId.value || [],
            hasOtherSchedule: 0,
            // 项目日历下的关注不需要查别人分配过来的日程
          }),
    };
    // if (globalMode.value === 'work' && !scheduleIdArr?.length) return;
    // if (
    //   globalMode.value === 'project' &&
    //   !currentProjectScheduleId.value?.length
    // )
    //   return;
    const { data } = await getScheduleFollowList(param);
    console.log(data, '关注事项');
    attentionListData.value = data;
  };

  // 数组去（根据对象id）
  const deduplication = (data) => {
    return data.filter(
      (item, index, self) => index === self.findIndex((i) => i.id === item.id)
    );
  };

  const scheduleData = ref([]); // 创建日历人员数据

  const fullCalendarRef = ref(null); // 创建ref来引用FullCalendar实例
  const calendarApi = ref(null);

  // 创建面板
  const panelVisible = ref(false);
  const createPanelHandle = async () => {
    scheduleType.value = 'create';
    panelVisible.value = true;
  };

  const retractableStatus = ref(1);
  const projectRetractableStatus = ref(0);

  const emits = defineEmits(['changeTab']);

  // 通过传入的日期获取到当前周的第一天和最后一天
  const getWeekRange = (dateString) => {
    // 解析传入的日期字符串
    const date = dayjs(dateString);
    const dayOfWeek = dayjs(date).day(); // 获取给定日期是周几（0是周日，1是周一，以此类推）
    const sundayDate = dayjs(date)
      .subtract(dayOfWeek, 'day')
      .format('YYYY-MM-DD');
    const startOfWeek = dayjs(sundayDate).add(1, 'day').format('YYYY-MM-DD'); // 计算周一的日期
    const endOfWeek = dayjs(date)
      .add(7 - dayOfWeek, 'day')
      .format('YYYY-MM-DD'); // 计算周日的日期

    return [startOfWeek, endOfWeek];
  };
  // 通过传入的日期获取到当月第一天和最后一天
  const getMonthRange = (dateString) => {
    // 解析传入的日期字符串
    const date = dayjs(dateString);
    // 获取当月的第一天
    const firstDayOfMonth = date.startOf('month').format('YYYY-MM-DD');
    // 获取当月的最后一天
    const lastDayOfMonth = date.endOf('month').format('YYYY-MM-DD');
    return [firstDayOfMonth, lastDayOfMonth];
  };

  // 获取两个日期之间的所有天数
  const getContinuTime = (startDate, endDate) => {
    const daysArray = [];

    startDate = dayjs(startDate).format('YYYY-MM-DD');
    endDate = dayjs(endDate).format('YYYY-MM-DD');

    // 获取两个日期之间的所有天数
    let currentDate1 = dayjs(startDate);

    while (
      currentDate1.isBefore(endDate) ||
      currentDate1.isSame(endDate, 'day')
    ) {
      daysArray.push(currentDate1.format('YYYY-MM-DD'));
      currentDate1 = currentDate1.add(1, 'day');
    }
    return daysArray || [];
  };

  // 新建日程（会议、事项）
  const createScheduleHandle = (val) => {
    // 个人空间
    if (globalMode.value === 'work') {
      scheduleStore.setScheduleId(scheduleId.value[0] || '');
    } else {
      // 项目空间
      scheduleStore.setCurrentProjectScheduleId(
        currentProjectScheduleId.value || ''
      );
    }

    const data = {
      editType: 'create',
      type: val,
    };
    emits('changeTab', data);
  };

  // 添加当天高亮
  const attributes = ref([]);

  const rightHeadDate = ref('');
  const calendarMode = ref(1); // 1月  2周  3天

  // fullCalendar单元格点击
  const fullCellClick = (val) => {
    console.log(val);
  };

  // 跳转到对应的数据进行编辑
  const jumpHandle = (val) => {
    emits('changeTab', val);
  };

  // fullCalendar高亮数据点击
  const handleEventClick = async (val) => {
    // const data = {
    //   scheduleSubId: val.event.id,
    //   type: val.event.groupId,
    // };
    // jumpHandle(data);
    const data = {
      id: val.event.id,
      type: val.event.groupId,
    };
    scheduleStore.setSummaryVisible(true);
    await scheduleStore.setSummaryData(data);
  };

  // 日历配置
  const calendarOptions = computed(() => ({
    plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],

    locale: isEn.value ? 'en' : 'zh-cn', // 中文
    buttonText: {
      today: t('schedule.current'),
      month: t('schedule.month'),
      week: t('schedule.week'),
    },
    aspectRatio: 1,
    height: 'auto', // 允许日历高度自适应
    contentHeight: 'auto', // 允许内容高度自适应
    // slotEventOverlap: false,
    // slotMinHeight: '50'
    slotMinHeight: 600,
    slotMinWidth: 900,
    eventDidMount(info) {
      // console.log(info.el, 7777);
    },
    // aspectRatio: 1, // 可以调整宽高比
    // selectable: false, // 禁用选择功能，这样点击周单元格时不会有高亮显示
    customButtons: {
      dayButton: {
        text: t('schedule.day'),
        click: () => {
          // 天按钮切换到模式1
          setTimeout(() => {
            calendarMode.value = 3;
          }, 100);
        },
      },
    },
    headerToolbar: {
      // 头显示
      // left: 'prev,next',
      // center: 'title',
      left: '',
      center: '',
      right: '',
      // right: 'dayGridMonth,timeGridWeek,dayButton',
      // right: 'today prev,next,dayGridMonth,timeGridWeek,timeGridDay',
    },
    firstDay: 1, // 设置一周中显示的第一天是哪天，周日是0，周一是1，类推
    // editable: true, //事件是否可编辑，可编辑是指可以移动, 改变大小等。
    // droppable: false, //是否可拖拽
    // loading: loading //控制表格加载
    // initialView: '', // 默认视图
    dayCellContent: (info) => {
      return info.dayNumberText.replace(/日/g, '');
    },
    // 监听日期变化事件
    datesSet(info) {
      // startTime.value = dayjs(info.startStr).format('YYYY-MM-DD');
      // endTime.value = dayjs(info.endStr).format('YYYY-MM-DD');
      // getScheduleData();
      // 这里可以执行你需要的操作，比如发送请求更新事件等
      // todo
      // currentDate.value.month = dayjs(info.startStr).format('MM');
      // currentDate.value.year = dayjs(info.startStr).format('YYYY');
    },
    // 监听视图变化事件
    viewDidMount(view) {
      console.log('当前视图已变为:', view.type);
      // 这里可以执行你需要的操作，比如发送请求更新事件等
    },
    // viewDidMount(info) {},
    // initialView: 'dayGridWeek',
    // initialEvents: INITIAL_EVENTS, // alternatively, use the `events` setting to fetch from a feed
    editable: false, // 允许编辑表格
    droppable: false, // 允许从外部拖拽进入日历
    selectable: false, // 允许用户通过单击和拖动来突出显示多个日期或时间段
    unselectAuto: true, // 当点击页面日历以外的位置时，是否自动取消当前的选中状态
    selectMirror: true,
    dayMaxEvents: true, // 在dayGrid视图中，给定日期内的最大事件数
    // headerToolbar: false, // 关闭默认日历头部，采取自定义的方式切换日历视图
    weekends: true, // 是否显示周末，设为false则不显示周六和周日。
    dateClick: fullCellClick,
    eventResize: true,
    events: fullCalendarData.value,
    eventContent: ({ event }) => {
      if (event.classNames[0] === 'meeting')
        return {
          html: `<div class='heightCell heightCell1'>
             <span class="line"></span>
             ${
               event.overlap ? `<span class="user">${event.overlap}</span>` : ''
             }
             <span class="eventAll">${event.title}</span>
             </div>`,
        };

      return {
        html: `<div class='heightCell heightCell2' >
           <span class="line"></span>
           ${event.overlap ? `<span class="user">${event.overlap}</span>` : ''}
           <span class="eventAll">${event.title}</span>
        </div>`,
      };

      // const name = event.type === 'item' ? '2' : '3';
      // const endTime
      // <span
      //             class="surnamed"
      //             :class="item.type === 'team' ? 'surnamed2' : 'surnamed1'"
      //             >xj</span
      //           >
      //

      // html: `

      //          <div class='heightCell heightCell${
      //            event.classNames[0]
      //          }' style="display: flex; align-items: center;">
      //          <span class='user'>${event.overlap}</span>

      //           <span>${dayjs(event.start).format('YYYY-HH:MM')}-${dayjs(
      //     event.end
      //   ).format('YYYY-HH:MM')}</span>
      //            <span class="eventAll">${event.title}</span>
      //          </div>
      //        `,
      // };
    },
    eventClick: handleEventClick, // item-click
  }));

  // 渲染左侧日历数据（翻左侧日历时调用 切换我的日历时调用）
  const setVCalendar = () => {
    const nowDay = startTime.value;
    // 设置当前天
    attributes.value = [];
    // 添加高亮
    const matterData = [];
    const meetingData = [];
    scheduleCalendarLeft.value?.forEach((item) => {
      // 根据每个会议事项的开始到结束时间中间所有的天 添加高亮
      const allDay = getContinuTime(item?.planStartTime, item?.planEndTime);
      console.log(allDay, 444);
      allDay.forEach((day) => {
        matterData.push({
          key: day,
          id: day,
          dates: dayjs(day).format('YYYY-MM-DD'),
          dot: 'blue',
        });
      });
    });
    const attributesArr = [
      ...deduplication(matterData),
      ...deduplication(meetingData),
    ];

    // 去重
    const uniqueArray = [];
    const seenDates = new Set();

    // 遍历原始数组，并利用Set去重
    attributesArr.forEach((item) => {
      if (!seenDates.has(item.dates)) {
        uniqueArray.push(item); // 如果dates值是新的，则添加到新数组
        seenDates.add(item.dates); // 并将其添加到Set中
      }
    });

    attributes.value = uniqueArray;
    attributes.value.push({
      key: 'today',
      highlight: {
        color: '#999',
        fillMode: 'solid',
      },
      dates: [currentData],
    });
    if (currentData !== nowDay) {
      attributes.value.push({
        key: 'selected',
        dates: [nowDay],
        highlight: {
          color: '#999', // 背景颜色
          fillMode: 'light', // 填充模式
        },
      });
    }
    // 添加当前选择天高亮
  };

  // 获取月周日历数据
  const getFullCalendarData = async () => {
    fullCalendarData.value = [];
    scheduleCalendarData.value?.forEach((item) => {
      fullCalendarData.value.push({
        id: item.type === 'meeting' ? item.scheduleSubId : item.id,
        title: item.title,
        start: item.planStartTime,
        // start: item.type === 'meeting' ? item.planStartTime : item.createDate,
        end: item.planEndTime,
        classNames: item.type,
        overlap: item?.panelName?.charAt(0),
        groupId: item.type,
        allDay:
          dayjs(item.planStartTime).format('YYYY-MM-DD') !==
          dayjs(item.planEndTime).format('YYYY-MM-DD'),
      });
    });
    console.log(fullCalendarData.value, '33333 - 44444');
    calendarOptions.value.events = fullCalendarData.value;
  };

  // 获取当前月到下月1号得数据 用于设置左侧小日历的圆点
  const setNowMonthData = async () => {
    const nowMonthData = dayjs(startTime.value).format('YYYY-MM-01');
    const date = dayjs(nowMonthData);
    const nextMonth = date.add(1, 'month');
    const param = {
      ...(globalMode.value === 'work'
        ? {
            schedulePanelIds: scheduleId.value || [],
            projectPanelIds: projectScheduleId.value || [],
          }
        : {
            projectPanelIds: projectScheduleId.value || [],
          }),
      // timeView: calendarMode.value,
      timeView: 2,
      startTime: nowMonthData,
      endTime: nextMonth.format('YYYY-MM-DD'),
      ...(globalMode.value === 'work'
        ? {}
        : {
            projectId: currentProjectId.value,
            teamId: teamId.value,
            type: getTypeParam(),
          }),
    };
    if (globalMode.value === 'work') {
      const { data } = await getScheduleCalendar(param);
      scheduleCalendarLeft.value = data;
    } else {
      const { data } = await getProjectSchedule(param);
      scheduleCalendarLeft.value = data;
    }
  };

  // 获取日历数据
  const getScheduleData = async () => {
    if (
      globalMode.value === 'work' &&
      !scheduleId.value?.length > 0 &&
      !projectScheduleId.value?.length > 0
    ) {
      scheduleCalendarData.value = [];
      scheduleCalendarLeft.value = [];
      if (calendarMode.value !== 3) getFullCalendarData();
      return;
    }
    if (globalMode.value === 'project') {
      if (calendarMode.value !== 3) getFullCalendarData();
    }
    const nowDay = currentData;
    if (!startTime.value) startTime.value = nowDay;
    if (!endTime.value) endTime.value = nowDay;
    const param = {
      ...(globalMode.value === 'work'
        ? {
            schedulePanelIds: scheduleId.value || [],
            projectPanelIds: projectScheduleId.value || [],
          }
        : {
            projectPanelIds: projectScheduleId.value || [],
          }),
      timeView: calendarMode.value,
      startTime: startTime.value,
      endTime: endTime.value,
      ...(globalMode.value === 'work'
        ? {}
        : {
            projectId: currentProjectId.value,
            teamId: teamId.value,
            type: getTypeParam(),
          }),
    };
    // 设置左侧小日历圆点
    await setNowMonthData();
    setVCalendar();
    if (globalMode.value === 'work') {
      console.log('获取日历数据');
      const { data } = await getScheduleCalendar(param);
      scheduleCalendarData.value = data;
    } else {
      const { data } = await getProjectSchedule(param);
      scheduleCalendarData.value = data;
    }
    scheduleCalendarData.value?.forEach((item) => {
      // item.typeNew = item.type !== 'item' ? 1 : 2;
      // 会议列表 scheduleStatus值没有修改取autoStatus
      if (item.type === 'meeting') {
        item.scheduleStatus =
          item.scheduleStatus === null
            ? item.scheduleAutoStatus
            : item.scheduleStatus;
      }
    });
    if (calendarMode.value !== 3) {
      getFullCalendarData();
    }
  };

  const nowTime = ref();
  const currentMonth = ref();
  // 天视图日历点击
  const dayclick = async (e) => {
    currentMonth.value = e.month;
    const clickedDate = e.date;
    let { year } = e;
    const month = e.month < 10 ? `0${e.month}` : e.month;
    const day = e.day < 10 ? `0${e.day}` : e.day;
    const { week } = e; // 第几周
    // const weekDays = ['日', '一', '二', '三', '四', '五', '六', '日'];
    const weekDays = [
      t('calendar.weekday.sunday'),
      t('calendar.weekday.monday'),
      t('calendar.weekday.tuesday'),
      t('calendar.weekday.wednesday'),
      t('calendar.weekday.thursday'),
      t('calendar.weekday.friday'),
      t('calendar.weekday.saturday'),
    ];
    const weekDay = weekDays[e.weekdayPosition]; // 表示周几

    if (calendarMode.value === 1) {
      // 月
      rightHeadDate.value = t('calendar.monthTitle', { year, month });

      let nextmonth = e.month + 1;
      if (nextmonth > 12) {
        nextmonth = 1;
        year = e.year + 1;
      }
      startTime.value = dayjs(`${year}-${month}-01`).format('YYYY-MM-DD');
      endTime.value = dayjs(`${year}-${nextmonth}-01`).format('YYYY-MM-DD');
      await getScheduleData();
      calendarApi.value.gotoDate(e.id);
    } else if (calendarMode.value === 2) {
      // 计算当前周的周一和周日
      const currentDayOfWeek = clickedDate.getDay(); // 星期天为0，星期一为1，以此类推
      const startOfWeek = new Date(clickedDate);
      startOfWeek.setDate(
        clickedDate.getDate() -
          currentDayOfWeek +
          (currentDayOfWeek === 0 ? -6 : 1)
      ); // 如果是周日，则向前推移6天，否则推移currentDayOfWeek - 1天
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      startTime.value = dayjs(startOfWeek).format('YYYY-MM-DD');
      endTime.value = dayjs(endOfWeek).format('YYYY-MM-DD');

      rightHeadDate.value = t('calendar.weekTitle', { year, month, week });
      // rightHeadDate.value = e.ariaLabel.replace(/星期/g, ' 周');
      await getScheduleData();
      calendarApi.value.gotoDate(e.id);
    } else {
      startTime.value = dayjs(`${year}-${month}-${day}`).format('YYYY-MM-DD');
      endTime.value = dayjs(`${year}-${month}-${day}`).format('YYYY-MM-DD');

      rightHeadDate.value = t('calendar.dayTitle', {
        year,
        month,
        day,
        weekDay,
      });
      getScheduleData();
    }

    nowTime.value = {
      year,
      month,
      week: e.week,
      day,
    };
  };

  // 改变日历视图
  const changeCalendarMode = (val) => {
    calendarMode.value = val;
    if (val !== 3) {
      // 月周视图
      nextTick(async () => {
        if (fullCalendarRef.value)
          calendarApi.value = fullCalendarRef.value.getApi();
        if (val === 2) {
          // 切换周日历
          await calendarApi.value.changeView('timeGridWeek');
          // 修改标题时间
          // rightHeadDate.value = `${nowTime.value.year}年${nowTime.value.month}月第${nowTime.value.week}周`; // 修改标题时间
          rightHeadDate.value = t('calendar.weekTitle', {
            year: nowTime.value.year,
            month: nowTime.value.month,
            week: nowTime.value.week,
          });
          // 当前时间
          const time = dayjs(
            `${nowTime.value.year}-${nowTime.value.month}-${nowTime.value.day}`
          ).format('YYYY-MM-DD');
          // 获取当前周一到周日的日期
          [startTime.value, endTime.value] = getWeekRange(time);
          // 跳转到对应的周日期
          calendarApi.value.gotoDate(time);
        } else if (val === 1) {
          // 切换到月日历
          calendarApi.value.changeView('dayGridMonth');
          // 修改标题时间
          // rightHeadDate.value = `${nowTime.value.year}年${nowTime.value.month}月`;
          rightHeadDate.value = t('calendar.monthTitle', {
            year: nowTime.value.year,
            month: nowTime.value.month,
          });
          // 当前时间
          const time = dayjs(
            `${nowTime.value.year}-${nowTime.value.month}-${nowTime.value.day}`
          ).format('YYYY-MM-DD');
          // 获取当前周一到周日的日期
          [startTime.value, endTime.value] = getMonthRange(time);
          // 跳转到对应的日期
          calendarApi.value.gotoDate(time);
        }
        getScheduleData();
      });
    } else {
      const day = dayjs(
        nowTime.value.year + nowTime.value.month + nowTime.value.day
      ).format('YYYY-MM-DD');
      startTime.value = day;
      endTime.value = day;
      // 获取当前天是周几
      const date = dayjs(
        `${nowTime.value.year}-${nowTime.value.month}-${nowTime.value.day}`
      );
      const dayOfWeekNumber = date.day();
      // const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
      const weekDays = [
        t('calendar.weekday.sunday'),
        t('calendar.weekday.monday'),
        t('calendar.weekday.tuesday'),
        t('calendar.weekday.wednesday'),
        t('calendar.weekday.thursday'),
        t('calendar.weekday.friday'),
        t('calendar.weekday.saturday'),
      ];
      const weekDay = weekDays[dayOfWeekNumber];
      // 天视图(注销FullCalendar)
      calendarApi.value.destroy();
      // rightHeadDate.value = `${nowTime.value.year}年${nowTime.value.month}月${nowTime.value.day}日 周${weekDay}`;
      rightHeadDate.value = t('calendar.dayTitle', {
        year: nowTime.value.year,
        month: nowTime.value.month,
        day: nowTime.value.day,
        weekDay,
      });
      getScheduleData();
    }
  };

  // 我的日历展开收起
  const changeRetractable = (val) => {
    currentDate.value.month = 2;
    currentDate.value.year = 2025;
    retractableStatus.value = val;
  };
  // 项目日历展开收起
  const changeProjectRetractable = (val) => {
    currentDate.value.month = 2;
    currentDate.value.year = 2025;
    projectRetractableStatus.value = val;
  };

  // 切换日历
  const changeSchedule = async (val) => {
    // 只要我的日历有勾选，项目日历就清空
    if (val && val.length > 0) {
      projectScheduleId.value = [];
      localStorage.setItem(
        'selProjectScheduleId',
        JSON.stringify(projectScheduleId.value)
      );
    }
    // 获取日程(会议事项)数据
    await getScheduleData();
    // 左侧日历数据(根据日程数据 添加左侧日历高亮)
    await setVCalendar();

    // getFullCalendarData();
    getScheduleFollowListHandle();

    // 记住当前勾选的日历
    localStorage.setItem('selScheduleId', JSON.stringify(scheduleId.value));
  };
  // 切换项目日历
  const changeProjectSchedule = async (val) => {
    // 只要项目日历有勾选，个人日历就清空
    if (val && val.length > 0) {
      scheduleId.value = [];
      localStorage.setItem('selScheduleId', JSON.stringify(scheduleId.value));
    }
    // 获取日程(会议事项)数据
    await getScheduleData();
    // 左侧日历数据(根据日程数据 添加左侧日历高亮)
    await setVCalendar();

    // getFullCalendarData();
    getScheduleFollowListHandle();

    // 记住当前勾选的日历
    localStorage.setItem(
      'selProjectScheduleId',
      JSON.stringify(projectScheduleId.value)
    );
  };

  // 左侧日历切换月份联动右侧日历
  const handleMonthChange = async (page) => {
    // 为解决点击日历天会调用翻月方法bug
    if (page[0].month === currentMonth.value) return;
    currentMonth.value = page[0].month;

    const nowMonth = `${page[0].id}`;
    const date = `${page[0].id}-01`;
    const { year } = page[0];
    const month = page[0].month < 10 ? `0${page[0].month}` : page[0].month;

    // 获取当前天是周几
    const dayOfWeekNumber = dayjs(`${year}-${month}-01`).day();
    // const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
    const weekDays = [
      t('calendar.weekday.sunday'),
      t('calendar.weekday.monday'),
      t('calendar.weekday.tuesday'),
      t('calendar.weekday.wednesday'),
      t('calendar.weekday.thursday'),
      t('calendar.weekday.friday'),
      t('calendar.weekday.saturday'),
    ];
    const week = weekDays[dayOfWeekNumber];

    nowTime.value = {
      year,
      month,
      week,
      day: '01',
    };

    if (calendarMode.value !== 3) {
      if (calendarMode.value === 1) {
        // rightHeadDate.value = `${year}年${month}月`;
        rightHeadDate.value = t('calendar.monthTitle', { year, month });
        [startTime.value, endTime.value] = getMonthRange(date);
      } else {
        // rightHeadDate.value = `${year}年${month}月第1周`;
        rightHeadDate.value = t('calendar.weekTitle', {
          year,
          month,
          week: 1, // 或实际的 week 变量
        });
        [startTime.value, endTime.value] = getWeekRange(date);
      }

      if (calendarApi.value) calendarApi.value.gotoDate(nowMonth);
    } else {
      // rightHeadDate.value = `${year}年${month}月01日`;
      rightHeadDate.value = t('calendar.dayTitleSimple', {
        year,
        month,
        day: '01',
      });
      [startTime.value, endTime.value] = [date, date];
    }
    await getScheduleData();
    setVCalendar();
  };

  // 获取个人日历数据
  const getPanelListHandle = async () => {
    const param = {
      companyId: '100000',
    };
    const { data } = await getPanelList(param);
    scheduleOptions.value = data;
    const selScheduleId = JSON.parse(localStorage.getItem('selScheduleId'));
    // 若当前已有勾选日历缓存 则设置勾选的为已有勾选日历  否则默认展示第一个
    if (!selScheduleId?.length) {
      if (scheduleOptions.value?.length)
        scheduleId.value = [scheduleOptions.value[0]?.id];
    } else scheduleId.value = selScheduleId || [];
  };
  // 获取当前项目下的项目日历
  const getProjectPanelList = async () => {
    const param = {
      projectId: currentProjectId.value,
    };
    const { data } = await getProjectPanel(param);
    currentProjectScheduleId.value = [data?.id] || [];
    console.log(currentProjectScheduleId, '当前项目日历');
  };
  // 获取当前账号下的所有项目日历
  const getAllProjectPanelList = async () => {
    const param = {
      type: 1, // 1: 获取所有项目日历
    };
    const { data } = await getPanelList(param);
    projectScheduleOptions.value = data || [];
    const selProjectScheduleId = JSON.parse(
      localStorage.getItem('selProjectScheduleId')
    );
    // 若当前已有勾选日历缓存 则设置勾选的为已有勾选日历  否则默认都不勾选，不勾选查询全部
    if (!selProjectScheduleId?.length) {
      if (projectScheduleOptions.value?.length) projectScheduleId.value = [];
    } else projectScheduleId.value = selProjectScheduleId || [];
  };
  const initTime = () => {
    // 获取当前日期
    const now = dayjs();
    // 获取年月日
    const year = now.year();
    const month = now.month() + 1; // month() 返回的月份是从 0 开始的，所以需要加 1
    const date = now.date();
    // 计算当前天是本月的第几周
    // 首先找到本月的第一天
    const firstDayOfMonth = now.startOf('month');
    // 计算本月第一天是星期几（0 是星期天，1 是星期一，依此类推）
    const firstDayOfWeek = firstDayOfMonth.day();
    // 计算当前天是本月的第几天
    const dayOfMonth = now.date();
    // 计算当前天是本月的第几周
    // 如果本月第一天是星期天，则第一周是从第一天开始
    // 否则，第一周是从第一个星期一开始
    const weekOfMonth = Math.ceil((dayOfMonth + firstDayOfWeek) / 7);

    nowTime.value = {
      year,
      month: month < 10 ? `0${month}` : month,
      week: weekOfMonth, // 当月第几周
      day: date < 10 ? `0${date}` : date,
    };
    currentMonth.value = month;
    const dayOfWeek = now.day();

    // const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
    const weekDays = [
      t('calendar.weekday.sunday'),
      t('calendar.weekday.monday'),
      t('calendar.weekday.tuesday'),
      t('calendar.weekday.wednesday'),
      t('calendar.weekday.thursday'),
      t('calendar.weekday.friday'),
      t('calendar.weekday.saturday'),
    ];
    const curDate = dayjs(currentData);
    const curYear = curDate.format('YYYY');
    const curMonth = curDate.format('MM');
    const curDay = curDate.format('DD');
    const curweekDay = weekDays[dayOfWeek];
    // rightHeadDate.value = `${dayjs(currentData).format('YYYY年MM月DD日')}周${
    //   weekDays[dayOfWeek]
    // }`;
    rightHeadDate.value = t('calendar.dayTitle', {
      year: curYear,
      month: curMonth,
      day: curDay,
      weekDay: curweekDay,
    });
  };

  // 设置当前天数据
  const setTodyList = async () => {
    startTime.value = currentData;
    endTime.value = currentData;
    attributes.value?.forEach((item) => {
      if (item.key === 'selected') {
        item.dates = [currentData];
      }
    });
    const param = {
      ...(globalMode.value === 'work'
        ? {
            schedulePanelIds: scheduleId.value || [],
            projectPanelIds: projectScheduleId.value || [],
          }
        : {
            projectPanelIds: projectScheduleId.value || [],
          }),
      timeView: calendarMode.value,
      startTime: currentData,
      endTime: currentData,
      ...(globalMode.value === 'work'
        ? {}
        : {
            projectId: currentProjectId.value,
            teamId: teamId.value,
            type: getTypeParam(),
          }),
    };
    if (globalMode.value === 'work') {
      const { data } = await getScheduleCalendar(param);
      scheduleCalendarData.value = data;
    } else {
      const { data } = await getProjectSchedule(param);
      scheduleCalendarData.value = data;
    }
    scheduleCalendarData.value?.forEach((item) => {
      if (item.type === 'meeting') {
        item.scheduleStatus =
          item.scheduleStatus === null
            ? item.scheduleAutoStatus
            : item.scheduleStatus;
      }
    });
  };

  const init = async () => {
    currentMonth.value = '04';
    [startTime.value, endTime.value] = [currentData, currentData];
    // 项目空间下获取项目日历数据，并且把之前当前项目下的事项会议绑定一下新生成的日历
    if (globalMode.value === 'project') {
      await getProjectPanelList();
    } else {
      // 个人空间下的需要展示个人日历和项目日历，可能会有多个日历，所以两种都要获取
      await getPanelListHandle();
      await getAllProjectPanelList();
    }
    // 获取日历看板数据
    // await getPanelListHandle();
    // 获取关注事项数据
    // await getScheduleFollowListHandle();
    // 切换日历(默认展示第一个)
    await changeSchedule();

    await initTime();
    if (globalMode.value === 'project') {
      // 获取项目下的团队数据
      await getTeamList(currentProjectId.value);
    }
    setTodyList();
  };
  init();

  // 关注\删除后 刷新
  const refreshAttention = () => {
    getScheduleFollowListHandle();
    changeSchedule();
    changeProjectSchedule();
  };

  // 编辑日历
  const editOption = (val) => {
    scheduleType.value = 'edit';
    panelVisible.value = true;
    scheduleData.value = val;
  };

  // 删除日历
  const delCalendar = async (val) => {
    const param = {
      id: val.id,
    };
    const res = await deleteSchedule(param);
    if (res.status) {
      Message.success(t('schedule.delete.success'));
      getPanelListHandle();
    }
  };
  const setRightHeadDate = () => {
    if (!nowTime.value) {
      rightHeadDate.value = '';
      return;
    }
    if (calendarMode.value === 1) {
      // 月视图
      rightHeadDate.value = t('calendar.monthTitle', {
        year: nowTime.value.year,
        month: nowTime.value.month,
      });
    } else if (calendarMode.value === 2) {
      // 周视图
      rightHeadDate.value = t('calendar.weekTitle', {
        year: nowTime.value.year,
        month: nowTime.value.month,
        week: nowTime.value.week,
      });
    } else if (calendarMode.value === 3) {
      // 天视图
      const weekDays = [
        t('calendar.weekday.sunday'),
        t('calendar.weekday.monday'),
        t('calendar.weekday.tuesday'),
        t('calendar.weekday.wednesday'),
        t('calendar.weekday.thursday'),
        t('calendar.weekday.friday'),
        t('calendar.weekday.saturday'),
      ];
      const date = dayjs(
        `${nowTime.value.year}-${nowTime.value.month}-${nowTime.value.day}`
      );
      const weekDay = weekDays[date.day()];
      rightHeadDate.value = t('calendar.dayTitle', {
        year: nowTime.value.year,
        month: nowTime.value.month,
        day: nowTime.value.day,
        weekDay,
      });
    }
  };
  // 预览日程
  const previewSchedule = async (record) => {
    const data = {
      id: record.type === 'meeting' ? record.scheduleSubId : record.id,
      type: record.type,
    };
    scheduleStore.setSummaryVisible(true);
    await scheduleStore.setSummaryData(data);
  };
  const dropdownVisible = ref(false); // a-dropdown内容是否显示
  const nowRecord = ref(null); // 当前选择的数据
  // 更多按钮点击（手动出发a-dropdown的展开<因为点击删除时a-dropdown隐藏会导致删除二次确认窗也被隐藏> ）
  const handleSwitchVisible = (item) => {
    nowRecord.value = item;
    dropdownVisible.value = true;
  };
  // 编辑
  const editHandle = (item) => {
    // 保持会议事项统一id
    item.scheduleSubId = item.type === 'meeting' ? item.scheduleSubId : item.id;
    item.type = item.type === 'meeting' ? 'meeting' : 'matters';
    emits('changeTab', item);
  };
  const repeatedVal = ref('0');
  const delVidible = ref(false);
  const recordId = ref('');
  const recordType = ref('');
  const deleteMatterMsg = ref('');
  // 取消删除
  const handleCancel = () => {
    delVidible.value = false;
  };
  const deleteHandle = async (val) => {
    if (userName.value === val.createBy) {
      if (val.type === 'item') {
        const params = {
          scheduleDetailId: val.id,
        };
        try {
          const res = await deleteMatterFlag(params);
          if (res.status) {
            const [firstError] = res.data.err || []; // 使用数组解构，并处理 err 可能为 undefined 的情况
            deleteMatterMsg.value = firstError;
            console.log('[ firstError ] >', firstError);
          }
        } catch (error) {
          console.log(error);
        }
      }
      recordId.value = val.id;
      delVidible.value = true;
      recordType.value = val.type;
    } else {
      Message.info(
        val.type === 'item'
          ? t('schedule.status.notMatterCreator')
          : t('schedule.status.notMeetingCreator')
      );
    }
  };
  // 删除确认框
  const deleteSaveHandle = async () => {
    if (!repeatedVal.value) {
      Message.info(t('schedule.delete.selectType'));
      return;
    }
    dropdownVisible.value = false;
    const param = {
      scheduleDetailId: recordId.value,
      rmOption: repeatedVal.value,
    };
    try {
      const res = await removeschedule(param);
      if (res.status) {
        Message.success(t('schedule.delete.success'));
        refreshAttention();
      }
    } catch (error) {
      console.log(error);
    } finally {
      dropdownVisible.value = false;
      delVidible.value = false;
    }
  };
  // 关注

  const attentionHandle = async (item) => {
    try {
      const param = {
        scheduleDetailId: item.id,
      };
      const res = await setScheduleFollow(param);
      if (res.status) {
        Message.success(res.message);
        refreshAttention();
      }
    } catch (error) {
      console.log(error);
    } finally {
      dropdownVisible.value = false;
    }
  };
  // 监听语言切换，自动切换一些需要计算的
  watch(
    () => currentLocale.value,
    () => {
      // 只更新 id 为空的那一项的 name
      const allItem = teamList.value.find((item) => item.id === '');
      if (allItem) {
        allItem.name = t('schedule.all');
      }
      setRightHeadDate();
    }
  );
  watch(
    () => currentProjectId,
    () => {
      // 重新获取日历数据
      init();
    },
    {
      deep: true,
    }
  );
  watch(
    () => teamId,
    () => {
      // 重新获取日历数据
      getScheduleData();
    },
    {
      deep: true,
    }
  );
  watch(
    () => listType,
    () => {
      // 重新获取日历数据
      getScheduleData();
    },
    {
      deep: true,
    }
  );
</script>

<style lang="less" scoped>
  // 天视图页面
  .calendar-container {
    // padding: 0px 0 20px 0;
    display: flex;
    flex-wrap: nowrap;
    // height: auto;
    overflow: auto;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: calc(100vh - 168px);
    // height: 800px;

    overflow-x: auto; // 左侧
    overflow-y: hidden;
    .calendar-left {
      // display: flex;
      // flex-direction: column;
      height: auto;
      // padding: 0 16px;
      width: 400px;
      position: relative;
      border-right: 1px solid #d9d9d9;
      height: calc(100vh - 170px);
      min-width: 400px;
      overflow-y: auto;

      // border-right: 1px solid #d9d9d9;
      // 天视图日历

      .v-calendar-body {
        // height: 200px !important;
      }
      // :deep(.vc-container) {
      //   height: 369px;
      // }
      // :deep(.vc-pane-layout) {
      //   height: 369px;
      // }
      :deep(.vc-prev) {
        margin-right: 6px;
      }
      :deep(.vc-day) {
        height: 38px;
      }
      :deep(.vc-header) {
        margin-top: 0px;
        height: 38px;
        line-height: 38px;
        padding-top: 16px;
      }

      :deep(.vc-weeks) {
        // border-top: 1px solid #d9d9d9;
        padding: 0;
      }
      :deep(.in-next-month) {
        display: none;
      }
      :deep(.vc-title) {
        background-color: transparent;
        font-size: 20px;
      }
      :deep(.vc-arrow) {
        border-radius: 0;
        width: 24px;
        height: 24px;
        color: #86909c;
        border: 1px solid #e5e6eb;
        background-color: #fff;
      }
      :deep(.vc-weekdays) {
        height: 50px;
        .vc-weekday {
          line-height: 50px;
        }
      }

      // 隐藏小日历弹出的筛选年月
      :deep(.vc-popover-content-wrapper) {
        display: none !important;
      }
      :deep(.vc-arrow) {
        width: 24px !important;
        height: 24px !important;
        border-radius: 4px !important;
        overflow: hidden;
      }
      :deep(.vc-highlight) {
        width: 26px;
        height: 26px;
        border-radius: 8px;
      }
      :deep(.vc-day-layer) {
        bottom: -2px !important;
      }
      :deep(.vc-day-content) {
        font-size: 16px !important;
      }
      :deep(.fc-daygrid-day-frame) {
        min-height: 106px;
        height: 108px;
      }

      .calendar-left-bottom {
        // flex: 1;
        padding: 0 20px;
        // height: calc(100vh - 500px);
      }

      // 关注事项
      .attention {
        margin-top: 20px;
        padding: 16px 0;
        border-top: 1px solid #d9d9d9;
        .noData {
          // height: 220px;
          display: flex;
          justify-content: center;
          align-items: center;
          // border-bottom: 1px solid #d9d9d9;
          flex-direction: column;
          img {
            width: 120px;
            height: 112px;
          }
          div {
            margin-top: 16px;
            color: #4e5969;
          }
        }

        .attention-head {
          font-weight: 500;
          color: #1d2129;
          margin-bottom: 4px;
          .arco-icon-subscribe {
            font-size: 20px;
            vertical-align: middle;
          }
          span {
            margin-left: 8px;
            vertical-align: middle;
            font-size: 20px;
          }
        }
        .attention-body {
          // height: 220px;
          overflow: auto;
          img {
            width: 24px;
            height: 24px;
            vertical-align: middle;
            margin-right: 8px;
          }
          .attention-list {
            padding: 16px 0 12px 0;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding-right: 8px;
            // border-bottom: 1px solid #ededed;
            // div {
            //   overflow: hidden;
            //   text-overflow: ellipsis;
            //   display: -webkit-box;
            //   -webkit-line-clamp: 1; /* 限制显示的行数 */
            //   -webkit-box-orient: vertical;
            // }
            .attention-list-title {
              font-size: 16px;
              color: #1d2129;
              font-weight: 500;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1; /* 限制显示的行数 */
              -webkit-box-orient: vertical;
            }
            .attention-list-described {
              color: #86909c;
              font-size: 14px;
              margin: 12px 0;
              padding-left: 32px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1; /* 限制显示的行数 */
              -webkit-box-orient: vertical;
            }
            .attention-list-time {
              padding-left: 32px;
              font-size: 14px;
              color: #86909c;
              span {
                color: #4e5969;
              }
            }
          }
        }
      }

      .calendar-user {
        cursor: pointer;
        .title {
          font-weight: 500;
          font-size: 16px;
          color: #1d2129;
          .arco-icon {
            margin-right: 8px;
            cursor: pointer;
            font-size: 16px;
          }
        }
        .add-user {
          margin-bottom: 20px;
        }
        .user-list {
          margin-bottom: 10px;
          .arco-checkbox-group {
            width: 100%;
            max-height: 100px;
            overflow-y: auto;
            overflow-x: hidden;
            margin-top: 16px;
          }
          :deep(.arco-checkbox-label) {
            width: 100%;
          }
          .arco-checkbox {
            font-size: 16px;
          }
          :deep(.arco-btn) {
            padding: 0 8px;
            color: #4e5969;
          }
          .panelName {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
    // 右侧
    .calendar-right {
      flex: 1;
      .calendar-detail-head {
        padding: 0 20px;
        box-sizing: border-box;
        height: 62px;
        line-height: 62px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        // overflow-x: auto; // 横向滚动
        min-width: 0;
        .detail-time {
          flex-shrink: 0;
          min-width: 180px;
          font-weight: bold;
          font-size: 20px;
          white-space: nowrap;
          .detail-date {
            margin-right: 8px;
          }
        }
        .detail-btn-container {
          min-width: 550px; // 根据右侧内容实际宽度调整
          flex: 1 1 0;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          overflow: hidden;
        }
        .detail-btn {
          width: 100%;
          height: 50px;
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
          justify-content: flex-end;
          .team-title-en {
            min-width: 180px;
          }
          .addBtn {
            span {
              color: #4e5969;
              cursor: pointer;
              &:first-child {
                margin-right: 16px;
              }
            }
            .schedule-icon {
              vertical-align: middle;
            }
            img {
              width: 20px;
              height: 20px;
              vertical-align: middle;
            }
            display: inline-block;
          }
          .addBtn-en {
            min-width: 256px;
          }
          .calendarModeBtn {
            // border: 1px solid #d9d9d9;
            background-color: #f2f3f5;
            width: 120px;
            height: 28px;
            line-height: 22px;
            padding: 3px;
            span {
              cursor: pointer;
              display: inline-block;
              width: 38px;
              height: 22px;
              line-height: 22px;
              font-size: 14px;
              color: #778ca2;
              text-align: center;
              border-radius: 4px;
              // &:nth-child(2) {
              //   border-right: 1px solid #d9d9d9;
              //   border-left: 1px solid #d9d9d9;
              // }
            }
            .active {
              background-color: #fff !important;
              color: #3366ff !important;
            }
          }
          // position: absolute;
          // right: 200px;
          // top: 20px;
        }
      }
      .schedule-list {
        height: calc(100vh - 236px);
      }
    }
  }

  // 月、周视图页面
  .fullCalendar-container {
    height: calc(100vh - 240px);
    overflow: auto;
    // background-color: #ffff;
    .calendar-main {
      height: 500px;
    }
    :deep(.fc) {
      height: 500px;
    }
    :deep(.fc-button-primary:not(:disabled).fc-button-active) {
      border-color: #d9d9d9;
    }
    :deep(.fc-button-primary:not(:disabled):active) {
      border-color: #d9d9d9;
    }
    :deep(.fc-col-header) {
      background-color: rgb(248, 250, 251);
      color: rgb(119, 140, 162);
      th {
        font-weight: 600;
      }
    }
    // 前后翻月份按钮位置
    :deep(.fc-toolbar-chunk > div) {
      display: flex;
    }
    :deep(.fc-toolbar.fc-header-toolbar) {
      // margin-bottom: 16px !important;
    }
    :deep(.fc-header-toolbar) {
      margin-bottom: 0;
    }

    // 修改日历组件按钮颜色
    :deep(.fc-button) {
      background-color: rgb(248, 250, 250);
      border: 1px solid rgb(232, 236, 239);
      color: rgb(119, 140, 162);
      font-size: 14px;
    }
    // 月周天按钮高亮
    :deep(.fc-button-active) {
      background-color: rgb(248, 250, 250) !important;
      color: rgba(var(--primary-6)) !important;
    }
    // 隐藏日历按钮点击时候的阴影效果
    :deep(.fc-button-primary:focus) {
      box-shadow: none !important;
    }
    // 日历标题
    :deep(.fc-toolbar-title) {
      font-weight: 700;
      color: #4e4e4e;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin: 0 16px;
      font-size: 18px;
    }
    // 头部高度
    :deep(.fc-col-header-cell) {
      line-height: 40px;
      height: 40px;
    }
    // 日历单元格高度
    :deep(.fc-daygrid-day) {
      height: 108px;
    }

    :deep(.fc-col-header) {
      height: 32px;
    }
    // 周单元格高度
    :deep(.fc-timegrid-slots tr) {
      height: 32px;
    }
    // 日历头部间距
    :deep(.fc-header-toolbar.fc-toolbar.fc-toolbar-ltr) {
      padding: 0 20px;
    }
    // 月、周视图数据条颜色
    :deep(.heightCell) {
      background-color: rgb(255, 243, 232);
      color: #000;
      border: none;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    :deep(.user) {
      display: inline-block;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      text-align: center;
      line-height: 14px;
      color: #fff;
      margin: 0 4px;
      font-size: 12px;
      border: 1px solid #fff;
      color: #fff;
      border: 1px solid #fff;
      font-weight: 500;
    }
    :deep(.fc-event-main) {
      border-bottom: 2px solid #fff;
      border-top: 2px solid #fff;
      display: flex;
      align-items: center;
      justify-content: left;
    }
    :deep(.fc-event) {
      border: none;
    }
    :deep(.fc-event-main:has(.heightCell1)) {
      background-color: #fff3e8;
    }
    :deep(.fc-event-main:has(.heightCell2)) {
      background-color: #e8f7ff;
    }
    :deep(.line) {
      display: inline-block;
      width: 4px;
      height: 100%;
      background-color: #3c7eff;
      border-radius: 4px;
      // margin: 0 4px;
    }

    :deep(.heightCell1) {
      background-color: #fff3e8;
      color: #1d2129;
      font-size: 14px;
      .user {
        background-color: #f77234;
      }
      .line {
        background-color: #f77234;
      }
    }

    :deep(.heightCell2) {
      background-color: #e8f7ff;
      white-space: normal;
      color: #1d2129;
      font-size: 14px;
      .user {
        background-color: #3c7eff;
      }
      .line {
        background-color: #3c7eff;
      }
    }
  }
  :deep(.fc-daygrid-day-frame) {
    background-color: #fff !important;
  }

  :deep(.heightCell) {
    overflow: hidden;
  }

  :deep(.eventAll) {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  :deep(.vc-focus:focus-within) {
    box-shadow: none;
  }
  .team-select {
    width: 140px !important;
    :deep(.arco-form-item) {
      margin-bottom: 0;
    }
    :deep(.arco-select-view) {
      border: 1px solid #d9d9d9;
      width: 100%;
      background-color: #fff !important;
    }
  }
  .title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
