<template>
  <div>
    <div class="project-file">
      <CommonTabs v-model="tabKey" @click-tab="clickTab" />
      <div class="content-area">
        <FilePanel v-if="tabKey === 'file'"></FilePanel>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import CommonTabs from '@/components/common-tabs/index.vue';
  import FilePanel from './components/file-panel/index.vue';
  import useFileStore from '@/store/modules/file';
  import { useRoute } from 'vue-router';

  const fileStore = useFileStore();
  const route = useRoute();

  function init() {
    const lastProjectId = sessionStorage.getItem('file_last_projectId');
    const projectId = (route.params.projectId as string) || '';
    if (lastProjectId && lastProjectId !== projectId) {
      // 切换项目，重置项目文件缓存内容
      fileStore.toggleProject();
    }
    sessionStorage.setItem('file_last_projectId', projectId);
  }

  init();

  const tabKey = ref('file');

  function clickTab() {}
</script>

<script lang="ts">
  export default {
    name: 'AttrCheck',
  };
</script>

<style scoped lang="less">
  .project-file {
    //border: 1px solid red
    padding: 16px 20px;
    height: 100%;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    overflow: hidden;
    .content-area {
      height: calc(100% - 60px);
    }
  }
</style>
