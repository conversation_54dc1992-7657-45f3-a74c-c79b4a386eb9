<template>
  <a-drawer
    v-if="visible"
    :width="520"
    :body-style="bodyStyle"
    style="padding: 0"
    :visible="visible"
    unmount-on-close
    :footer="null"
    @cancel="handleCancel"
  >
    <template #title>
      <!-- <icon-left @click="handleCancel" /> -->
      <span class="type-name">{{
        summaryData.type === 'meeting'
          ? t('schedule.meeting')
          : t('schedule.matters')
      }}</span>
      <a-button
        v-show="summaryData.parentScheduleDetailId"
        type="outline"
        size="small"
        style="
          position: absolute;
          right: 40px;
          border-radius: var(--border-radius-medium);
        "
        @click="
          jumpMatterOrMeeting('matters', summaryData.parentScheduleDetailId)
        "
        >{{ $t('calendar.head-to-main-task') }}
      </a-button>

      <a-button
        v-show="summaryData.meetingId"
        type="outline"
        size="small"
        style="
          position: absolute;
          right: 40px;
          border-radius: var(--border-radius-medium);
          color: #ff6b00;
          border: 1px solid #ff6b00;
        "
        @click="jumpMatterOrMeeting('meeting', summaryData.meetingId)"
        >{{ $t('calendar.go-to-meeting') }}
      </a-button>
    </template>
    <a-spin dot :loading="summaryLoading">
      <div>
        <div class="summary-head">
          <a-select
            :model-value="status"
            :class="'status' + status"
            style="width: 98px"
            :placeholder="$t('schedule.calendar.status.placeholder')"
            @change="(val: any) =>changeStatusHandle(val)"
          >
            <template #prefix>
              <img v-if="!status" :src="status0Img" alt="" />
              <img v-if="status === 1" :src="status1Img" alt="" />
              <img v-if="status === 2" :src="status3Img" alt="" />
            </template>
            <a-option
              v-for="item in statusOptions"
              :key="item.id"
              :value="item.id"
            >
              {{ t(item.name) }}
            </a-option>
          </a-select>
          <span class="title">{{ summaryData.title }}</span>
        </div>
        <div style="width: 516px">
          <MeetingDetail
            v-if="summaryData.type === 'meeting'"
            :title="summaryData.title"
            type="view"
            :data="summaryData"
          />
          <CategoryMatter
            v-if="summaryData.type === 'item'"
            type="view"
            :title="summaryData.title"
            :data="summaryData"
          />
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { ref, watch, computed, inject } from 'vue';
  import MeetingDetail from '@/views/schedule/component/meeting/componemt/createEditMeeting.vue';
  import { userScheduleStore, useUserStore } from '@/store';
  import { storeToRefs } from 'pinia';
  import { setscheduleStatus } from '@/views/schedule/component/calendar/api';
  import CategoryMatter from '@/views/create-schedule/component/category-matter.vue';
  import { useI18n } from 'vue-i18n';
  import status0Img from '@/assets/images/meeting/status0.png';
  import status1Img from '@/assets/images/meeting/status1.png';
  import status3Img from '@/assets/images/meeting/status3.png';

  const { t } = useI18n();

  const bodyStyle = { padding: '20px 0' };

  const scheduleStore = userScheduleStore();
  const { summaryData, summaryLoading } = storeToRefs(scheduleStore);
  const userStore = useUserStore();
  const userName = computed(() => userStore.username);

  const status = ref(0);
  // 会议的状态
  const statusData: any = {
    meeting: [
      {
        id: 0,
        name: 'schedule.notStarted',
      },
      {
        id: 1,
        name: 'schedule.inProgress',
      },
      {
        id: 2,
        name: 'schedule.completed',
      },
    ],
    item: [
      {
        id: 1,
        name: 'schedule.inProgress',
      },
      {
        id: 2,
        name: 'schedule.completed',
      },
      {
        id: 3,
        name: 'schedule.closed',
      },
    ],
  };

  const statusOptions = ref();

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      required: false,
    },
  });

  const emits = defineEmits(['refresh', 'jump']);

  const handleCancel = () => {
    scheduleStore.setSummaryVisible(false);
  };

  // 通用设置状态方法
  const updateScheduleStatus = async (val: string) => {
    const param = {
      scheduleDetailId: summaryData.value.scheduleDetailId,
      status: val,
      type: summaryData.value.type === 'meeting' ? 2 : 1,
    };
    const res = await setscheduleStatus(param);
    if (res.status) {
      Message.success(t('schedule.status.success'));
      emits('refresh');
    }
  };

  // 检查子事项状态
  const checkMatterStatus = async (val: string) => {
    const allSubMatterCompleted = summaryData.value?.children.every(
      (item: { agendaStatus: number }) => item.agendaStatus === 2
    ); // 获取所有子事项状态是否都为已完成

    // if (allSubMatterCompleted) {
    //   await updateScheduleStatus(val);
    // } else {
    //   Message.info('请先完成所有子事项');
    //   status.value = summaryData.value.agendaStatus;
    // }

    if (
      (allSubMatterCompleted && summaryData.value.status === 2) ||
      summaryData.value.status !== 2
    ) {
      await updateScheduleStatus(val);
    } else if (summaryData.value.status === 2) {
      Message.info(t('schedule.status.completeSubMatters'));
      status.value = summaryData.value.agendaStatus;
    }
  };

  // 修改状态
  const changeStatusHandle = async (val: any) => {
    // 只有创建人可以修改状态
    if (userName.value !== summaryData.value.createBy) {
      Message.info(t('schedule.status.notCreator'));
      return;
    }
    // 只有通过校验才赋值
    status.value = val;
    if (summaryData.value.type === 'item') {
      // 如果是事项修改状态
      await checkMatterStatus(val);
    } else {
      await updateScheduleStatus(val);
    }
  };

  /**
   * 跳转事项或会议
   */
  const jumpMatterOrMeeting = async (type: string, id: string) => {
    scheduleStore.setSummaryVisible(true);
    const data = {
      id,
      type,
    };
    await scheduleStore.setSummaryData(data);
  };

  watch(
    () => summaryData.value,
    () => {
      if (summaryData.value.type === 'meeting') {
        statusOptions.value = statusData.meeting;
      } else {
        statusOptions.value = statusData.item;
      }
      status.value = summaryData.value?.status || 0;
    }
  );
</script>

<style lang="less" scoped>
  :deep(.drawer-body) {
    padding: 20px 0 !important;
  }

  .summary-head {
    padding: 0 16px;
    font-size: 14px;
    :deep(.arco-select-view-single) {
      height: 24px;
    }
    :deep(.arco-select-view-value) {
      min-height: 0;
    }

    .type-name {
      margin-left: 8px;
      font-size: 20px;
      color: #1d2129;
    }

    :deep(.arco-select) {
      border-radius: 4px;
    }
    :deep(.status0) {
      background-color: #ffece8 !important;
      color: #ff4d4f;
      .arco-select-view-icon {
        color: #ff4d4f;
      }
    }
    :deep(.status1) {
      background-color: #e8f2ff !important;
      color: #3366ff;
      .arco-select-view-icon {
        color: #3366ff;
      }
    }
    :deep(.status2) {
      background-color: #e5e6eb !important;
      color: #86909c;
      .arco-select-view-icon {
        color: #86909c;
      }
    }
    img {
      width: 16px;
      height: 16px;
    }
    :deep(.arco-select-view-single) {
      padding: 0 8px;
    }
    :deep(.arco-select-view-prefix) {
      padding-right: 4px;
    }
    :deep(.arco-select-view-suffix) {
      padding-left: 4px;
    }

    .title {
      margin-left: 16px;
      color: #1d2129;
      font-size: 20px;
    }
  }
</style>
