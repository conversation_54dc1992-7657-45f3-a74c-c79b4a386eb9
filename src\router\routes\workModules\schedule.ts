import { AppRouteRecordRaw } from '../types';

const DASHBOARD: AppRouteRecordRaw = {
  path: '/schedule',
  name: 'schedule',
  component: () => import('@/views/schedule/index.vue'),
  props: (route: any) => ({
    type: route.query.type,
    id: route.query.id,
  }),
  meta: {
    locale: 'menu.schedule',
    requiresAuth: true,
    allowProjectTemplate: true,
    icon: 'icon-calendar',
    order: 3,
    hideInMenu: false,
    showAI: true,
    globalMode: ['work'],
  },
};

export default DASHBOARD;
