import axios from 'axios';

export interface ProjectListParams {
  pageNo: number;
  pageSize: number;
  projectType: number;
  name?: string;
  organization?: string;
  projectProperties?: string;
}

export interface ProjectItem {
  name: string;
  type: number | string;
  id?: string;
  description?: string;
  code?: string;
  protemId?: string;
  planStart?: string;
  planEnd?: string;
  position: string;
  coordinate: number;
  elevation: number;
  environmentType: number;
  magnitude: number;
  projectStatus: number;
  participaUnit: string;
  projectPhase: number;
}

export interface CreateTemplateReqParams {
  name: string;
  projectId?: string | number;
}
export const getProjectList = (params: ProjectListParams) => {
  return axios.get('/cde-collaboration/project/list', { params });
};

export const createProject = (params: ProjectItem) => {
  return axios.post('/cde-collaboration/project/save', params);
};

// 获取用户在某项目下的teams和权限
export const getUserTeamsInPrj = (projectId: string) => {
  return axios.get('/cde-collaboration/team/getTeams', {
    params: { projectId },
  });
};

// 创建项目模板
export const createTemplate = (params: CreateTemplateReqParams) => {
  return axios.post('/cde-collaboration/project/createTemplate', params);
};
export default null;
