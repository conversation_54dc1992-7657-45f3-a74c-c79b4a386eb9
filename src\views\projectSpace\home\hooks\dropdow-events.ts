import i18n from '@/locale/index';
import { FileAndFolderNodeMessage } from '@/api/tree-folder';
import { download } from '@/utils/file';
import { fileDownload, batchDownloadInfo, fileZipDownload } from '../api';
import { Message } from '@arco-design/web-vue';

const { t } = i18n.global;

// 下载单文件
async function handleSingleFile(file: any) {
  const res: any = await fileDownload(file);
  download(file, res.data);
}

// 下载压缩包
async function handleZipFile(fileList: any) {
  // 获取批量下载文件信息
  const res: any = await batchDownloadInfo({ fileList });
  if (res.status) {
    const { data } = res;
    let zipFileName = `${t('file-manage.batch-download')}.zip`;

    const downloadRes: any = await fileZipDownload(data);
    // 获取文件名称
    const disposition = downloadRes.headers['content-disposition'];
    if (disposition && disposition.includes('filename=')) {
      const [, encodeFilename] = disposition.split('filename=');
      zipFileName = decodeURI(encodeFilename);
    }
    download({ name: zipFileName }, downloadRes.data);
  } else {
    Message.error(res.message);
  }
}

// 下载源文件按钮点击事件
export async function downloadSource(currentFile: FileAndFolderNodeMessage) {
  Message.info(t('file-manage.wait-downloading'));
  if (currentFile.isFileOrFolder === 1) {
    // 文件
    handleSingleFile(currentFile);
  } else {
    // 文件夹
    const fileList = [
      { fileId: currentFile.id, fileType: currentFile.isFileOrFolder },
    ];
    handleZipFile(fileList);
  }
  return currentFile;
}

export default downloadSource;
