// 查看知识库列表入参
interface QueryBaseListParams {
  pageParam: {
    pageNum: number;
    pageSize: number;
  };
  userName: string;
}

// 查看子文件/文件夹列表入参
interface QueryFolderListParams {
  folderId: string;
  fullTree: boolean;
}

// 新建文件夹入参
interface CreateFolderParams {
  name: string;
  parentId: string;
}

// 重命名文件夹入参
interface RenameFolderParams {
  folderId: string;
  name: string;
}

// 删除文件夹入参
interface DeleteFolderParams {
  folderId: string;
}

// 保存文件入参
interface SaveFileParams {
  fileSize: number;
  folderId: string;
  name: string;
  ossToken: string;
  type: string;
}

// 删除文件入参
interface DeleteFileParams {
  fileId: string;
}

// 查询知识库使用情况入参
interface KnowledgeBaseUsageParams {
  kbId: string;
}

// 文件/文件夹
interface Node {
  id: string;
  name: string;
  parentId: string;
  process?: string;
  size?: string;
  type: string;
  updateDate: string;
  fileToken?: string;
  isFolder: boolean;
  isNew?: boolean; // 是否是新建
}

// 要上传的文件结构
interface CustomFileItem {
  file: File; // File对象
  size?: any;
  name: string; // 文件名称
  percent: number; // 上传进度0-100
  status: string; // 状态：init待上传，success成功
  uid: string;
  model: string; // 所属模块字段，knowledgeBase表示知识库
  fileToken?: string;
}

interface ChatHistoryRecord {
  agentId: string;
  chatSessionId: string;
  chatSessionName: string;
  chatSessionStatus: number;
  chatSessionType: number;
  createBy: string;
  createDate: string;
  deleteFlag: number;
  id: string;
  updateBy: string;
  updateDate: string;
  userId: string;
}

export {
  QueryBaseListParams,
  QueryFolderListParams,
  CreateFolderParams,
  RenameFolderParams,
  DeleteFolderParams,
  SaveFileParams,
  DeleteFileParams,
  KnowledgeBaseUsageParams,
  Node,
  CustomFileItem,
  ChatHistoryRecord,
};
