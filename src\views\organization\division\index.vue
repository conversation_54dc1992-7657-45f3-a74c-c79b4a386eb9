<template>
  <div class="container">
    <div class="general-card" style="height: 100%">
      <a-row>
        <table-title
          :title="$t('menu.org.department')"
          class="table-title"
        ></table-title>
      </a-row>
      <a-table
        ref="tableRef"
        row-key="id"
        :indent-size="20"
        :loading="loading"
        :columns="columns"
        :data="renderData"
        :size="size"
        :pagination="false"
        :scroll="{ y: 'calc(100% - 47px)' }"
        :load-more="loadMore"
        v-if="renderData && renderData.length"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
        <!-- <template #orgType="{ record }">
          {{ record.content.orgType === '0' ? '内部' : '外部' }}
        </template> -->
        <!-- <template #expand-icon="{ expanded }">
          <icon-right v-if="!expanded" />
          <icon-down v-if="expanded" />
        </template> -->
        <template #operations="{ record }">
          <!-- 用户管理按钮 -->
          <a-button
            v-if="isSystemAdmin"
            type="text"
            size="small"
            @click="handleUserManage(record)"
          >
            {{ $t('user-management') }}
          </a-button>

          <!-- 下拉菜单 -->
          <a-dropdown
            v-if="isSystemAdmin"
            position="br"
            :hide-on-select="false"
          >
            <a-button type="text">
              <template #icon><icon-more-vertical /></template>
            </a-button>
            <template #content>
              <!-- 新增 -->
              <a-doption
                v-if="permissions(record, 'create')"
                value="create"
                @click="addSubHandler(record)"
              >
                <template #icon><icon-plus /></template>
                <template #default>{{ $t('department.columns.operations.create') }}</template>
              </a-doption>
              <!-- 编辑 -->
              <a-doption
                v-if="permissions(record, 'edit')"
                value="edit"
                @click="editHandler(record)"
              >
                <template #icon><icon-edit /></template>
                <template #default>{{ $t('department.columns.operations.edit') }}</template>
              </a-doption>
              <!-- 删除 -->
              <a-popconfirm
                content="确认删除该项?"
                type="warning"
                @before-ok="(done) => deleteHandler(done, record)"
              >
                <a-doption
                  v-if="permissions(record, 'del')"
                  value="delete"
                >
                  <template #icon><icon-delete /></template>
                  <template #default>{{ $t('department.columns.operations.delete') }}</template>
                </a-doption>
              </a-popconfirm>
            </template>
          </a-dropdown>
        </template>
      </a-table>
    </div>
    <OrgInfo
      v-model:visible="dialogVisible"
      :form="selectDept"
      @refreshTable="handleRefreshTable"
    />

    <!-- 用户管理穿梭框 -->
    <user-transfer
      v-model:visible="userTransferVisible"
      :title="$t('user-management')"
      :org-no="currentOrgNo"
      @confirm="handleUserTransferConfirm"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { usePortalStore, useUserStore, usePrjPermissionStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import {
    getOrgs,
    OrgServiceRecord,
    OrgRecord,
    SelectDept,
    deleteOrg,
    getDepartmentList,
  } from '@/api/modules/department';
  import OrgInfo from './components/org-info.vue';
  import { SizeProps } from './types';
  import TableTitle from '@/components/table-title/index.vue';
  import commonTabs from '@/components/common-tabs/index.vue';
  import UserTransfer from '@/components/user-transfer/index.vue';
  import { Message } from '@arco-design/web-vue';


  const tabKey = ref('organization');

  const { loading, setLoading } = useLoading(true);
  const { t } = useI18n();
  const portalStore = usePortalStore();
  const prjPermissionStore = usePrjPermissionStore();
  const userStore = useUserStore();
  const isSystemAdmin = computed(() => userStore.admin === 0);
  const renderData: any = ref([]);

  const size = ref<SizeProps>('medium');
  const tableHeight = ref(window.innerHeight - 200);

  const dialogVisible = ref(false);
  const emptyDept = {
    id: '',
    parentId: '',
    name: '',
    entName: '',
    parentName: '',
    pathName: '',
    pathNo: '',
  };
  const selectDept = ref<SelectDept>({ ...emptyDept });

  const columns = computed<TableColumnData[]>(() => [
    {
      title: t('department.columns.index'),
      dataIndex: 'index',
      slotName: 'index',
      align: 'left',
      headerCellStyle: {
        textAlign: 'left',
      },
      width: 250,
    },
    {
      title: t('department.columns.name'),
      dataIndex: 'name',
      align: 'left',
      headerCellStyle: {
        textAlign: 'left',
      },
      // width: 260,
    },
    {
      title: t('department.columns.abbr'),
      dataIndex: 'entName',
      align: 'left',
      width: 350,
    },
    // {
    //   title: t('department.columns.orgType'),
    //   dataIndex: 'orgType',
    //   slotName: 'orgType',
    //   align: 'left',
    // },
    {
      title: t('department.columns.operations'),
      dataIndex: 'operations',
      slotName: 'operations',
      align: 'left',
      width: 200,
    },
  ]);

  const tableRef = ref<any>(null);
  const firstId: any = ref('');
  const fetchData = async () => {
    // setLoading(true);
    // try {
    //   const { data }: { data: OrgServiceRecord[] } = await getOrgs({
    //     isAll: false,
    //     portalId: portalStore.portalId,
    //   });
    //   renderData.value = tranferTreeData(data);
    //   // 筛选当前用户所属的二级单位
    //   const pathNo = Number(userStore.userInfo.pathNo?.split('.')[3]);
    //   if (userStore.admin === 3) {
    //     // 单位管理员展示当前所属二级单位数据
    //     renderData.value[0].children = renderData.value[0].children?.filter(
    //       (item: any) => item.content.orgNo === pathNo
    //     );
    //   }
    //   firstId.value = data[0]?.id;
    //   // 表格默认展开一级
    //   tableRef.value.expand([renderData.value[0].id]);
    // } catch (err) {
    //   // you can report use errorHandler or other
    // } finally {
    //   setLoading(false);
    // }
  };

  const tranferTreeData = (data: OrgServiceRecord[]) => {
    const result: OrgRecord[] = [];
    data.forEach((item) => {
      const { children, content, id, parentId } = item;
      const obj: OrgRecord = {
        id,
        parentId,
        content,
        name: content.name,
        entName: content.entName,
      };
      if (children && children.length) {
        const childrenRes = tranferTreeData(children);
        obj.children = childrenRes;
      }
      result.push(obj);
    });
    return result;
  };

  const search = () => {
    fetchData();
  };

  // fetchData();

  // 打开新增子部门弹窗
  const addSubHandler = async (record: OrgRecord) => {
    dialogVisible.value = true;
    console.log(record, 7778);
    selectDept.value = {
      id: '',
      name: '',
      entName: '',
      parentId: record?.orgNo,
      pathName: record?.pathName,
      pathNo: record?.pathNo,
      parentName: record?.name,
      // orgType: record?.orgType,
      operator: 'addSub',
      content: record.content,
    };
    console.log(selectDept.value, 7779);
  };

  // 打开编辑组织机构弹窗
  const editHandler = (record: OrgRecord) => {
    dialogVisible.value = true;
    selectDept.value = {
      id: record.id,
      name: record?.name,
      entName: record.entName,
      parentId: record.parentId,
      content: record.content,
      orgType: record.content?.orgType,
      operator: 'edit',
    };
  };

  // 删除组织机构
  const deleteHandler = async (done: any, record: OrgRecord) => {
    const id = record.id || '';
    const {message,status} = await deleteOrg(id);
    search();
    done();
    if(status){
      Message.success(message);
    }
    handleRefreshTable();
  };

  const departList = ref([]);
  // 获取部门数据
  const getDepartList = async (line: any) => {
    departList.value = [];
    const param = {
      parentNo: line.orgNo || 0,
    };
    const { data } = await getDepartmentList(param);
    loading.value = false;
    // 设置部门列表数据
    departList.value = data || [];
    renderData.value = data;
  };
  getDepartList({ orgNo: 0 });

  const loadMore = async (record: any, done: any) => {
    console.log(record.orgNo, 7777);
    const param = {
      parentNo: record.orgNo || 0,
    };
    const { data } = await getDepartmentList(param);
    done(data);
  };

  // 用户管理穿梭框相关
  const userTransferVisible = ref(false);
  const currentOrgNo = ref('');

  // 处理用户管理按钮点击
  const handleUserManage = (record: any) => {
    currentOrgNo.value = record.orgNo;
    userTransferVisible.value = true;
  };

  // 处理用户穿梭框确认
  const handleUserTransferConfirm = (users: any[]) => {
    // 这里可以添加保存逻辑，例如调用接口保存用户与组织的关系
    console.log('已选择的用户:', users);
    // TODO: 调用保存接口
  };

  // 处理刷新表格事件：收起所有展开项并清除数据
  const handleRefreshTable = () => {
    if (tableRef.value) {
      tableRef.value.expandAll(false); // 收起所有展开项
      // 清空当前渲染数据，强制表格重新加载
      renderData.value = [];
      // 重新获取顶层数据
      getDepartList({ orgNo: 0 });
    }
  };

  // 权限处理
  const permissions = (record: any, action: any) => {
    // 超级管理员一级单位不允许删除外 其余皆可操作
    if (isSystemAdmin.value) {
      if (action !== 'del' || record.parentId !== '0') return true;
    } else if (userStore.admin === 3) {
      // 单位管理员 一级单位不可操作
      if (record.parentId === '0') return false;

      // 编辑、删除 只有二级单位以下的可以操作  新增任何单位都可操作
      if (action !== 'create') {
        if (record.parentId !== firstId.value) return true;
        return false;
      }
      return true;
    }
    return false;
  };
</script>

<script lang="ts">
  export default {
    name: 'Department',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 20px 20px 0 20px;
    height: 100%;
  }
  .table-title {
    margin-bottom: 20px;
  }

  .general-card {
    background-color: transparent;
  }
  :deep(.arco-table-body) {
    height: auto !important;
  }
  :deep(.arco-table-tr .arco-table-td:first-child) {
    white-space: nowrap;
  }

  // :deep(.arco-table-tr) {
  //   // height: 50px !important;
  // }

  // :deep(.arco-table-td) {
  //   // height: 60px !important;
  //   padding: 12px 16px !important;
  // }

  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }

  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }

  .setting {
    display: flex;
    align-items: center;
    width: 200px;

    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }

  .card-title {
    font-size: 18px;
    color: #1d2129;
    display: flex;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      vertical-align: middle;
    }
  }
</style>
