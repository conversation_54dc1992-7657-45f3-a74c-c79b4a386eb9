import statusTypeMap from '@/utils/BIM-Engine/XBase/dictionary/status-type';

const getFileTypeStatus = (suffix: string, status: string): string => {
  if (!status) {
    return '暂不支持查看';
  }
  // 查找匹配的文件类型
  const foundType: any = statusTypeMap.find((type) =>
    type.extensions.includes(suffix)
  );
  if (foundType) {
    const statusDescription: string = foundType.statusMap[status];
    console.log('[ statusDescription ] >', statusDescription);
    return statusDescription !== undefined ? statusDescription : '未知状态';
  }
  return '未知类型';
};

export default getFileTypeStatus;
