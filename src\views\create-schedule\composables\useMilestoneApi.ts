// composables/useTaskDialog.ts
import { ref } from 'vue';
import { MilestoneRecord, queryMilestoneList } from '../api';

export default function useMilestoneApi() {
  const allMilestoneList = ref<MilestoneRecord[]>([]); // 所有里程碑列表
  // 查询所有里程碑列表
  const getAllMilestoneList = async (projectId: string) => {
    const params = {
      pageNo: 1,
      pageSize: 1000,
      projectId,
    };
    try {
      const res = await queryMilestoneList(params);
      allMilestoneList.value = res.data.list || [];
      return res; // 可选，返回结果
    } catch (err) {
      console.log(err);
    }
  };
  return {
    allMilestoneList,
    getAllMilestoneList,
  };
}
