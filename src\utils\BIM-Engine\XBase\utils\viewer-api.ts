export default class ViewerAPI {
  private viewer;

  constructor(viewerInstance: any) {
    this.viewer = viewerInstance;
  }

  // 根据构件id集合将对应的构件实体缩放到视口中央
  gotoByComponentIds(ids: string[]) {
    // renderPath: 当前预览的模型的路径
    // path: guid所在的模型路径
    // guid: 构件guid
    const suffix = this.viewer.path?.substring(
      this.viewer.path.lastIndexOf('.') + 1
    );
    let btoaGuid;
    if (suffix === 'asm') {
      const pathMapIndex = this.viewer?.engine?.pathMapIndex;
      const index = pathMapIndex.get(this.viewer.path);
      btoaGuid =
        this.viewer.path?.substring(this.viewer.path.lastIndexOf('.') + 1) ===
        'ifc'
          ? btoa(`${index}.'${ids}'`)
          : btoa(`${index}.${ids}`);
    } else {
      btoaGuid = suffix === 'ifc' ? btoa(`'${ids}'`) : btoa(`${ids}`);
    }
    const res = this.viewer.getEntityChildrenIds(btoaGuid);
    this.viewer.gotoByIds(res); // 根据构件id集合将对应的构件实体缩放到视口中央
    this.viewer.clearEntitiesColor(); // 清除自定义的模型实体颜色
    this.viewer.setEntitiesColor([[res[0], 0]], [[110, 255, 110]]); // 设置模型实体颜色
  }
}
