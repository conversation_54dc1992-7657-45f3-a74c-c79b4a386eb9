<template>
  <div class="explain">
    <div class="title">
      {{
        isSelf
          ? $t('design.this-diagram-example')
          : $t('design.other-diagram-example')
      }}
      <div class="annotation">{{ $t('design.logo-click') }}</div>
    </div>
    <div v-for="(item, index) in data" :key="index" class="explain-item">
      <div
        :class="'graph ' + item.class + (item.children ? ' have-children' : '')"
      >
        <div></div>
      </div>
      <div v-if="item.children" class="mark">{{ item.children }}</div>
      <div class="text">
        <div v-for="(line, i) in item.text" :key="i" class="line">
          {{ line }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { useI18n } from 'vue-i18n';

  const { t } = useI18n();
  const props = defineProps({
    color: {
      type: String,
      default() {
        return '#F76560';
      },
    },
    isSelf: {
      type: Boolean,
      default() {
        return true;
      },
    },
  });
  const color = props.isSelf ? props.color : '#999999';
  const data = computed(() =>
    props.isSelf
      ? [
          {
            class: 'type1',
            text: [
              t('design.dotted-circle'),
              t('design.dotted-circle-explain'),
            ],
          },

          {
            class: 'type3',
            text: [t('design.light-circle'), t('design.light-circle-explain')],
          },
          {
            class: 'type4',
            text: [t('design.solid-circle'), t('design.shared-package')],
          },
          {
            class: 'type5',
            text: [
              t('design.dashed-square'),
              t('design.dashed-square-explain'),
            ],
          },
          {
            class: 'type7',
            text: [t('design.light-square'), t('design.light-square-explain')],
          },
          {
            class: 'type8',
            text: [t('design.solid-square'), t('design.published-package')],
          },
          {
            class: 'type4',
            text: [
              t('design.solid-circle-with-number'),
              t('design.group-shared-package'),
            ],
            children: 16,
          },
          {
            class: 'type11',
            text: [t('design.solid-drum'), t('design.group-completed-package')],
            children: 16,
          },
        ]
      : [
          {
            class: 'type2',
            text: [
              t('design.hollow-circle'),
              t('design.hollow-circle-explain'),
              t('design.hollow-circle-explain2'),
            ],
          },
          {
            class: 'type4',
            text: [
              t('design.other-solid-circle'),
              t('design.solid-circle-explain'),
            ],
          },
          {
            class: 'type8',
            text: [
              t('design.other-solid-square'),
              t('design.solid-square-explain'),
            ],
          },
          {
            class: 'type2',
            text: [
              t('design.hollow-circle-with-num'),
              t('design.hollow-circle-with-num-explain'),
              t('design.hollow-circle-with-num-explain2'),
            ],
            children: 16,
          },
          {
            class: 'type4',
            text: [
              t('design.solid-circle-with-num'),
              t('design.solid-circle-with-num-explain'),
              t('design.solid-circle-with-num-explain2'),
            ],
            children: 16,
          },
          {
            class: 'type9',
            text: [
              t('design.two-color-circle-with-num'),
              t('design.two-color-circle-with-num-explain'),
              t('design.two-color-circle-with-num-explain2'),
            ],
            children: 16,
          },
          {
            class: 'type8',
            text: [
              t('design.solid-square-with-num'),
              t('design.solid-square-with-num-explain'),
            ],
            children: 16,
          },
          {
            class: 'type11',
            text: [
              t('design.solid-drum-with-num'),
              t('design.solid-drum-with-num-explain'),
            ],
            children: 16,
          },
          {
            class: 'type10',
            text: [
              t('design.two-color-drum-with-num'),
              t('design.two-color-drum-with-num-explain'),
            ],
            children: 16,
          },
        ]
  );
</script>

<style lang="less" scoped>
  .title {
    border-bottom: 1px solid var(--color-border);
    padding-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: flex-end;
    .annotation {
      font-size: 12px;
      margin: 0 0 2px 8px;
    }
  }
  .explain-item {
    margin-top: 8px;
    display: flex;
    align-items: flex-start;
    position: relative;
    .mark {
      position: absolute;
      top: 6px;
      left: 16px;
      line-height: 16px;
      background-color: v-bind(color);
      color: #ffffff;
      font-size: 12px;
      padding: 0px 5px;
      border-radius: 10px;
      border: 2px solid #ffffff;
    }
    .graph {
      width: 16px;
      height: 16px;
      margin: 14px 24px 0 16px;
    }
    .have-children {
      margin-left: 8px;
      margin-right: 32px;
    }
    .type1 {
      border: 2px dotted v-bind(color);
      border-radius: 50%;
    }
    .type2 {
      border: 2px solid v-bind(color);
      border-radius: 50%;
    }
    .type3 {
      border: 2px solid v-bind(color);
      border-radius: 50%;
      div {
        width: 100%;
        height: 100%;
        background-color: v-bind(color);
        opacity: 0.3;
        border-radius: 50%;
      }
    }
    .type4 {
      border: 2px solid v-bind(color);
      border-radius: 50%;
      background-color: v-bind(color);
    }
    .type5 {
      border: 2px dotted v-bind(color);
      border-radius: 4px;
    }
    .type7 {
      border: 2px solid v-bind(color);
      border-radius: 4px;
      div {
        width: 100%;
        height: 100%;
        background-color: v-bind(color);
        opacity: 0.3;
      }
    }
    .type8 {
      border: 2px dotted v-bind(color);
      border-radius: 4px;
      background-color: v-bind(color);
    }
    .type9 {
      border: 2px solid v-bind(color);
      border-radius: 50%;
      background: linear-gradient(
        to right,
        v-bind(color) 0%,
        v-bind(color) 50%,
        white 50%,
        white 100%
      );
    }
    .type10 {
      width: 18px;
      margin: 14px 32px 0 8px;
      border: 2px solid v-bind(color);
      border-radius: 40%;
      background: linear-gradient(
        to right,
        v-bind(color) 0%,
        v-bind(color) 50%,
        white 50%,
        white 100%
      );
    }
    .type11 {
      width: 18px;
      margin: 14px 32px 0 8px;
      border: 2px solid v-bind(color);
      border-radius: 40%;
      background: v-bind(color);
    }
    .text {
      font-size: 14px;
      .line {
        color: var(--color-text-3);
      }
    }
    .text .line:first-child {
      color: var(--color-text);
    }
  }
</style>
