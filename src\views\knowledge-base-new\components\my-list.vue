<template>
  <div class="list-page">
    <div class="list">
      <a-spin ref="spin" :loading="tableLoading" class="folder-table-panel">
        <a-table
          ref="folderTable"
          :columns="columns"
          :data="personalCombinedList"
          :bordered="false"
          :scroll="{ x: '100%', y: 'calc(100% - 16px)' }"
          :row-selection="{
            type: 'checkbox',
            showCheckedAll: true,
            onlyCurrent: true,
          }"
          :pagination="false"
          row-key="id"
          @selection-change="selectionChange"
        >
          <template #name="{ record }">
            <div class="table-name">
              <file-image
                :file-name="record.name ?? ''"
                :is-sysFile="false"
                :is-file="!!record.folderId"
                style="margin-right: 8px"
              />
              <a-tooltip
                v-if="!record.isAdd && !record.isEdit"
                :content="record.name"
              >
                <span
                  style="
                    display: inline-block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  "
                  class="file-name"
                  @click="debouncedSelect(record)"
                  >{{ record.name }}</span
                >
              </a-tooltip>

              <a-input
                v-else-if="record.isAdd"
                ref="addInputRef"
                v-model="record.name"
                style="width: 95%"
                @blur="() => emit('handleList', 'add', record)"
                @keydown.enter="() => emit('handleList', 'add', record)"
              />
              <a-input
                v-else-if="record.isEdit && !record.folderId"
                ref="editInputRef"
                v-model="record.name"
                style="width: 95%"
                @blur="emit('handleList', 'rename', record)"
                @keydown.enter="emit('handleList', 'rename', record)"
              />

              <a-input
                v-else-if="record.isEdit && record.folderId"
                ref="editInputRef"
                v-model="record.filename"
                style="width: 95%"
                @blur="emit('handleList', 'rename', record)"
                @keydown.enter="emit('handleList', 'rename', record)"
              />
            </div>
          </template>
          <!-- 转换状态 -->
          <template #status="{ record }">
            <span>{{
              record.fileToken &&
              !isPicFile(record) &&
              isRequiredConversion(record)
                ? formatStatus(record.status, record.isCombination, record)
                : ''
            }}</span>
          </template>
          <template #size="{ record }">
            {{ record.size ? getFileSize(record.size) : '' }}
          </template>
          <template #updateDate="{ record }">
            {{ record.updateDate || '' }}
          </template>
          <template #optional="{ record }">
            <div class="flex-row">
              <a-button
                v-if="
                  !isNeedToConvert(record.status, record.isCombination) &&
                  record.folderId &&
                  isRequiredConversion(record)
                "
                type="text"
                size="small"
                @click="reConvert(record)"
              >
                {{ t('file-manage.reconvert') }}
              </a-button>
              <a-button type="text" size="small" @click="handleMove(record)">
                {{ t('knowledgenew.move') }}
              </a-button>
              <a-button type="text" size="small" @click="handleRename(record)">
                {{ t('knowledgenew.rename') }}
              </a-button>
              <a-popconfirm
                :content="t('cloud.person-delete--one-tips')"
                type="info"
                position="left"
                @ok="() => handleDelete(record)"
              >
                <a-button type="text" size="small" class="delete-btn">{{
                  t('knowledgenew.delete')
                }}</a-button>
              </a-popconfirm>
            </div>
          </template>
        </a-table>
      </a-spin>
    </div>
  </div>
  <MoveFileModal
    v-model:visible="moveFileVisible"
    :files="moveFiles"
    @move-file-success="moveFileSuccess"
    @refresh="queryFolderContent()"
  ></MoveFileModal>
</template>

<script lang="ts" setup>
  import { storeToRefs } from 'pinia';
  import { ref, computed, toRefs, defineEmits, nextTick, toRaw } from 'vue';
  import i18n from '@/locale/index';
  import { useDebounceFn, useThrottleFn } from '@vueuse/core';
  import useKnowledgeBaseNewStore from '@/store/modules/knowledge-base-new/index';
  import {
    isPicFile,
    isRequiredConversion,
    isWpsFile,
    isNeedToConvert,
  } from '@/views/projectSpace/file/utils';
  import { getFileSize } from '@/utils/file';
  import FileImage from '@/views/projectSpace/file/components/image-file.vue';
  import { Message } from '@arco-design/web-vue';
  import modelViewBim from '@/utils/common/view';
  import { reConvertApi } from '@/views/projectSpace/file/api';
  import { encode } from 'js-base64';
  import useFileStore from '@/store/modules/file/index';
  import MoveFileModal from './move-file-modal.vue';

  const { t } = i18n.global;
  const emit = defineEmits(['refreshFolder', 'handleList']);
  const fileStore = useFileStore();
  const { hiddenSlot } = storeToRefs(fileStore);

  const knowledgeBaseNewStore = useKnowledgeBaseNewStore();
  const { personal, personalCombinedList } = storeToRefs(knowledgeBaseNewStore);
  const { currentFolder, folderList, breadcrumb, projectId, tableLoading } =
    toRefs(personal.value);
  /** 移动begin */
  const moveFileVisible = ref(false);
  const moveFiles = ref<Array<Node>>([]);
  const moveFile = (record: Node) => {
    moveFiles.value = [toRaw(record)];
    moveFileVisible.value = true;
  };
  const moveFileSuccess = async () => {
    // treeRefresh();
    // 刷新当前文件列表
    knowledgeBaseNewStore.getPersonalFolder('personal');
    knowledgeBaseNewStore.getfiles('personal');
    moveFiles.value = [];
  };
  const queryFolderContent = async () => {
    // treeRefresh();
    // 刷新当前文件列表
    knowledgeBaseNewStore.getPersonalFolder('personal');
  };
  const columns = computed(() => {
    return [
      {
        title: t('file-manage.name'),
        dataIndex: 'name',
        slotName: 'name',
        sortable: {
          sortDirections: ['ascend', 'descend'],
        },
        width: 300,
      },
      {
        title: t('file-manage.size'),
        width: 90,
        dataIndex: 'size',
        slotName: 'size',
      },
      {
        title: t('file-manage.transition-status'),
        dataIndex: 'status',
        slotName: 'status',
        width: 120,
      },
      {
        title: t('file-manage.update-date'),
        width: 180,
        dataIndex: 'updateDate',
        slotName: 'updateDate',
      },
      {
        title: t('file-manage.operation'),
        slotName: 'optional',
        titleSlotName: 'optionalTitle',
        width: 200,
      },
    ];
  });
  function selectionChange(rowkeys: string[]) {
    knowledgeBaseNewStore.setSelectedByRowKeys(rowkeys);
    //
    // fileStore.setSelectedTableRowkeys(rowkeys);
  }
  const formatStatus = (status: number, type: number, record: any) => {
    if (type === 2) {
      // 碰撞检查
      if ([-1].includes(status)) return t('file-manage.failed');
      if ([2].includes(status)) return t('file-manage.success');
      if ([0, 1].includes(status)) return t('file-manage.in-conversion');
    } else if (type === 1) {
      return t('file-manage.success');
    } else {
      if (isWpsFile(record)) return t('');
      // 普通模型文件
      if ([-7, -2, -1].includes(status)) return t('file-manage.failed');
      if ([0, 3].includes(status)) return t('file-manage.success');
      if ([-3, 1, 4].includes(status)) return t('file-manage.in-conversion');
      if (status === 2) return t('file-manage.in-queue');
      if (!status) return t('file-manage.not-starts'); // 新增模型状态--未开始
    }
    return '';
  };
  const reConvert = async (record: any) => {
    const params = {
      fileId: record.id,
    };
    const res: any = await reConvertApi(params);
    if (res.code === 8000000) {
      Message.info(t('file-manage.reconvert'));
      knowledgeBaseNewStore.getfiles('personal');
    }
  };
  const addInputRef = ref(null);
  const editInputRef = ref(null);
  const handleRename = (item: any) => {
    item.isEdit = true;
    if (item.folderId) {
      const idx = item.name.lastIndexOf('.');
      item.filename = item.name.slice(0, idx);
      item.filetype = item.name.slice(idx);
    }
    // 重命名
    // 如果是新增状态，直接返回
    if (item.isAdd) {
      return;
    }
    nextTick(() => {
      const inputEdits = editInputRef.value;
      // 在 v-for 中 ref 会生成一个数组
      if (inputEdits) {
        inputEdits.focus();
      }
    });
  };
  // 点击进入下一文件夹
  const onSelect = (item: any) => {
    // 如果点击文件夹，进入下一文件夹，如果点击文件，则进入预览文件
    if (!item.folderId) {
      // 文件夹点击事件，进入下一层
      knowledgeBaseNewStore.setPersonCurrentFolder(item);
      knowledgeBaseNewStore.pushBreadcrumb(item);
      knowledgeBaseNewStore.getPersonalFolder('personal');
      knowledgeBaseNewStore.getfiles('personal');
      return;
    }
    // 文件点击事件查看文件
    const needParams: any = {
      noIssue: false,
      source: 'knowledge',
    };
    if (item.isCombination === 2) {
      const params = {
        type: 'collision',
        engine: 0,
        modelNumber: item.files.length, // 碰撞文件个数 用于碰撞检测结果页面表头区分
      };
      Object.assign(needParams, params);
    }
    console.log('record.status', item);
    modelViewBim(item, item.projectId, needParams);
  };
  // 防抖延迟 300ms 执行 onSelect 函数
  const debouncedSelect = useDebounceFn((item) => {
    // 这里是点击时要执行的操作
    onSelect(item);
  }, 300);
  const handleDelete = (record: any) => {
    knowledgeBaseNewStore.deleteItems([record], t, 'personal');
  };
  // 添加文件夹
  const focusInput = () => {
    nextTick(() => {
      const inputs = addInputRef.value;
      if (inputs) {
        inputs.focus();
      }
    });
  };
  // 移动
  const handleMove = (record: any) => {
    // 处理移动逻辑
    // 文件/文件夹移动
    moveFile(record);
  };
  defineExpose({ focusInput });
</script>

<style scoped lang="less">
  .list-page {
    position: absolute;
    height: calc(100% - 16px);
    width: 100%;
  }

  .list {
    height: calc(100% - 64px);
    overflow: auto;
  }
  :deep(.arco-table-content .arco-scrollbar:nth-child(2)) {
    height: 100%;
  }
  :deep(.arco-table-header + .arco-scrollbar-track-direction-horizontal) {
    display: none;
  }
  .folder-table-panel {
    width: 100%;
    height: 100%;
    padding: 0 12px;
  }
  :deep(.arco-table-container) {
    height: calc(100% - 40px);
  }
  .file-name {
    cursor: pointer;
    transition: color 0.2s;
  }
  .file-name:hover {
    color: rgb(22, 93, 255);
  }
  .table-name {
    display: flex;
    align-items: center;
  }
  .flex-row {
    display: flex;
    align-items: center;
  }
  .flex-row :deep(.arco-btn) {
    padding-left: 0;
    padding-right: 8px;
  }
  .delete-btn {
    color: #ff4d4f;
  }
</style>
